import { parse } from '@swc/core';

async function debugAST() {
  const code = `
    function recursiveFunction(n) {
      if (n <= 1) {
        return 1;
      }
      return recursiveFunction(n - 1) + recursiveFunction(n - 2);
    }
  `;

  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  console.log('AST:', JSON.stringify(result, null, 2));
}

debugAST().catch(console.error);