# 复杂度规则算法提取文档

## 提取的核心算法

### 1. 嵌套复杂度惩罚算法

```typescript
// 源位置: calculator.ts:1097
private calculateNestingPenalty(baseScore: number): number {
  return baseScore + this.nestingLevel;
}

// 使用场景: 所有增加嵌套的控制流语句
// 迁移目标: BaseRule.applyNestingPenalty()
```

### 2. 逻辑运算符检测算法

```typescript
// 源位置: calculator.ts:1104  
private isLogicalOperator(node: any): boolean {
  return node.operator === '&&' || node.operator === '||';
}

// 迁移目标: LogicalOperatorRule.canHandle()
```

### 3. 默认值赋值豁免算法

#### 主检测方法
```typescript
// 源位置: calculator.ts:1112
private isDefaultValueAssignment(node: any): boolean {
  // 检查 ?? 空值合并操作符 (总是应该被排除)
  if (node.operator === '??') {
    return true;
  }
  
  // 检查 || 用于默认值赋值的情况
  if (node.operator === '||') {
    return this.isInDefaultValueAssignmentContext(node);
  }
  
  // 检查 && 在某些默认值赋值模式中的使用
  if (node.operator === '&&') {
    return this.isPartOfDefaultValuePattern(node);
  }
  
  return false;
}
```

#### 上下文检测
```typescript
// 源位置: calculator.ts:1145
private isInDefaultValueAssignmentContext(_node: any): boolean {
  // 基础检查：是否在赋值上下文中
  if (!this.isInAssignment) {
    return false;
  }
  
  // 对于 || 运算符，在赋值上下文中通常都是默认值模式
  return true;
}
```

#### 属性访问模式检测
```typescript
// 源位置: calculator.ts:1191
private isPropertyAccessPattern(node: any): boolean {
  if (!node.left || !node.right) {
    return false;
  }
  
  // 检查模式：左侧是标识符，右侧是对同一对象的属性访问
  // 例如: data && data.value
  if (node.left.type === 'Identifier' && 
      node.right.type === 'MemberExpression' &&
      node.right.object?.type === 'Identifier') {
    
    // 检查是否是同一个对象
    const leftName = node.left.value || node.left.name;
    const rightObjectName = node.right.object.value || node.right.object.name;
    
    if (leftName === rightObjectName) {
      return true;
    }
  }
  
  return false;
}
```

### 4. 逻辑运算符混用检测算法

#### 主检测逻辑
```typescript
// 源位置: calculator.ts:1365
private detectLogicalOperatorMixing(node: any): boolean {
  try {
    // 性能优化：功能开关检查
    if (!this.options.enableMixedLogicOperatorPenalty) {
      return false;
    }

    // 早期退出：节点类型检查
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 对LogicalExpression和BinaryExpression节点进行检测
    if (node.type !== 'LogicalExpression' && node.type !== 'BinaryExpression') {
      return false;
    }

    // 对于BinaryExpression，只处理逻辑运算符
    if (node.type === 'BinaryExpression' && !this.isLogicalOperator(node)) {
      return false;
    }

    // 早期退出：检查是否为默认值赋值（需要豁免）
    if (this.isDefaultValueAssignment(node)) {
      return false;
    }

    // 检查是否为顶层逻辑表达式（避免在子表达式中重复惩罚）
    if (!this.isTopLevelLogicalExpression(node)) {
      return false;
    }

    // 早期退出：快速检查是否可能存在混用
    if (!this.hasLogicalMixingPotential(node)) {
      return false;
    }

    // 检查括号豁免
    if (this.hasParenthesizedOperands(node)) {
      return false;
    }

    // 懒加载：仅在必要时收集运算符
    const operators = this.collectLogicalOperators(node);
    
    // 检查是否同时包含 && 和 ||
    const hasAND = operators.includes('&&');
    const hasOR = operators.includes('||');
    const hasMixing = hasAnd && hasOR;
    
    return hasMixing;
  } catch (error) {
    // 错误边界处理：检测失败不影响整体分析
    return false;
  }
}
```

### 5. 递归调用检测算法

#### 主检测方法
```typescript
// 源位置: calculator.ts:1309
private isRecursiveCall(node: any): boolean {
  // 获取调用的函数名
  const callee = this.getCalleeIdentifier(node);
  if (!callee) {
    return false;
  }
  
  // 查找当前函数的名称
  const currentFunctionName = this.getCurrentFunctionName();
  if (!currentFunctionName) {
    return false;
  }
  
  return callee === currentFunctionName;
}
```

#### 调用标识符提取
```typescript
// 源位置: calculator.ts:1328
private getCalleeIdentifier(callNode: any): string | null {
  const callee = callNode.callee;
  
  if (!callee) {
    return null;
  }
  
  // 直接函数调用: functionName()
  if (callee.type === 'Identifier') {
    return callee.value || callee.name;
  }
  
  // 成员表达式调用: obj.method() - 检查是否是this.method()
  if (callee.type === 'MemberExpression') {
    const object = callee.object;
    const property = callee.property;
    
    // this.functionName() 的情况
    if (object?.type === 'ThisExpression' && property?.type === 'Identifier') {
      return property.value || property.name;
    }
  }
  
  return null;
}
```

## 规则类设计指导

### 1. IfStatementRule 设计

```typescript
export class IfStatementRule extends BaseRule {
  readonly id = 'if-statement';
  readonly name = 'If Statement Complexity';
  readonly priority = 400;

  canHandle(node: Node): boolean {
    return node.type === 'IfStatement';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // 基础复杂度: 1
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const complexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      return this.createComplexityResult(
        node,
        complexity,
        `If statement increases cognitive complexity by ${complexity}`,
        true, // 增加嵌套层级
        this.generateSuggestions('if statement', complexity)
      );
    });
  }
}
```

### 2. LogicalOperatorRule 增强设计

```typescript
export class LogicalOperatorRule extends BaseRule {
  readonly id = 'logical-operators';
  readonly name = 'Logical Operator Complexity';
  readonly priority = 300;

  canHandle(node: Node): boolean {
    return (node.type === 'LogicalExpression' || node.type === 'BinaryExpression') 
           && this.isLogicalOperator(node);
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // 检查豁免情况
      if (this.isDefaultValueAssignment(node, context)) {
        return this.createExemptionResult(node, 'Default value assignment exemption');
      }
      
      // 基础逻辑运算符复杂度
      let complexity = 1;
      
      // 检查是否有混用惩罚
      if (this.hasLogicalMixingPenalty(node, context)) {
        complexity += 1; // 混用惩罚
      }
      
      return this.createComplexityResult(
        node,
        complexity,
        `Logical operator increases cognitive complexity by ${complexity}`,
        false, // 不增加嵌套层级
        this.generateLogicalOperatorSuggestions(node, complexity)
      );
    });
  }
  
  private isLogicalOperator(node: any): boolean {
    return node.operator === '&&' || node.operator === '||';
  }
  
  private isDefaultValueAssignment(node: any, context: AnalysisContext): boolean {
    // 迁移算法: 检查 ??, 默认值赋值上下文, 属性访问模式
    return this.checkNullishCoalescing(node) ||
           this.checkDefaultValueContext(node, context) ||
           this.checkPropertyAccessPattern(node);
  }
  
  private hasLogicalMixingPenalty(node: any, context: AnalysisContext): boolean {
    // 迁移混用检测算法
    return context.options.enableMixedLogicOperatorPenalty &&
           this.detectMixedOperators(node) &&
           !this.hasParenthesesExemption(node);
  }
}
```

### 3. RecursiveCallRule 设计

```typescript
export class RecursiveCallRule extends BaseRule {
  readonly id = 'recursive-call';
  readonly name = 'Recursive Call Complexity';
  readonly priority = 200;

  canHandle(node: Node): boolean {
    return node.type === 'CallExpression';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // 检查是否是递归调用
      if (!this.isRecursiveCall(node, context)) {
        return this.createExemptionResult(node, 'Not a recursive call');
      }
      
      const complexity = 1;
      
      return this.createComplexityResult(
        node,
        complexity,
        `Recursive call increases cognitive complexity by ${complexity}`,
        false, // 不增加嵌套层级
        this.generateRecursiveSuggestions(node)
      );
    });
  }
  
  private isRecursiveCall(node: any, context: AnalysisContext): boolean {
    // 迁移递归检测算法
    const callee = this.extractCalleeIdentifier(node);
    return callee === context.currentFunctionName;
  }
  
  private extractCalleeIdentifier(callNode: any): string | null {
    // 迁移调用标识符提取算法
    const callee = callNode.callee;
    
    if (callee?.type === 'Identifier') {
      return callee.value || callee.name;
    }
    
    if (callee?.type === 'MemberExpression' && 
        callee.object?.type === 'ThisExpression' &&
        callee.property?.type === 'Identifier') {
      return callee.property.value || callee.property.name;
    }
    
    return null;
  }
}
```

## 迁移验证清单

### 功能完整性验证
- [ ] IfStatement规则: 基础复杂度 + 嵌套惩罚
- [ ] ForStatement规则: 各种循环类型支持
- [ ] WhileStatement规则: while/do-while支持
- [ ] LogicalOperator规则: 混用检测 + 豁免机制
- [ ] RecursiveCall规则: 直接调用 + this.method()调用
- [ ] ConditionalExpression规则: 三元运算符
- [ ] CatchClause规则: 异常处理复杂度

### 性能验证
- [ ] 规则缓存机制正常工作
- [ ] 早期退出优化保留
- [ ] 内存使用不超过基线
- [ ] 大项目分析时间 < 30秒

### 兼容性验证  
- [ ] 所有现有测试用例通过
- [ ] 复杂度计算结果与重构前一致
- [ ] 错误处理机制保持稳定
- [ ] 静态API接口不变