import { createHook } from 'async_hooks';
import { writeFileSync } from 'fs';
import { ComplexityCalculator } from './src/core/calculator';

const activeResources = new Map<number, { type: string; stack: string }>();

const hook = createHook({
  init(asyncId: number, type: string, triggerAsyncId: number, resource: object) {
    const err = new Error();
    activeResources.set(asyncId, {
      type,
      stack: err.stack || '',
    });
  },
  destroy(asyncId: number) {
    activeResources.delete(asyncId);
  },
});

hook.enable();

async function testWithAsyncHooks() {
  console.log('🔍 使用 async_hooks 进行深度诊断...\n');
  
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  const calculator = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: false
  });
  
  try {
    console.log('1. 计算前的异步资源:', activeResources.size);
    
    const results = await calculator.calculateCode(code, 'test.js');
    console.log('2. 计算完成，复杂度:', results[0]?.complexity);
    console.log('3. 计算后的异步资源:', activeResources.size);
    
    await calculator.dispose();
    console.log('4. 清理后的异步资源:', activeResources.size);
    
    if (activeResources.size > 0) {
      console.log('\n⚠️ 发现残留的异步资源:');
      let output = '';
      for (const [asyncId, resource] of activeResources.entries()) {
        output += `ID: ${asyncId}, Type: ${resource.type}\n`;
        console.log(`  - ID: ${asyncId}, Type: ${resource.type}`);
      }
      writeFileSync('lingering-resources.log', output);
      console.log('详细堆栈信息已写入 lingering-resources.log');
    } else {
      console.log('✅ 所有异步资源已正确清理');
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 在进程退出前打印最终状态
process.on('beforeExit', () => {
  console.log('\n🎉 进程即将自然退出 - 资源清理成功！');
  if (activeResources.size > 0) {
    console.log(`⚠️ 但仍有 ${activeResources.size} 个异步资源未清理`);
  }
});

// 设置一个 5 秒超时来检测是否自然退出
const exitTimeout = setTimeout(() => {
  console.log('\n❌ 进程5秒内未自然退出');
  console.log('当前剩余异步资源:', activeResources.size);
  process.exit(1);
}, 5000);

// 使用 unref 避免这个定时器本身阻止退出
exitTimeout.unref();

testWithAsyncHooks().catch(console.error);