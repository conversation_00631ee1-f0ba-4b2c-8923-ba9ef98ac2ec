#!/usr/bin/env bun

/**
 * 简化的AsyncRuleEngine节点提取调试
 */

import { ASTParser } from './src/core/parser';

async function debugSimpleNodeExtraction() {
  console.log('=== 简化的节点提取调试 ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  const parser = new ASTParser();
  const ast = await parser.parseCode(testCode, 'debug.ts');
  
  // 找到函数节点
  function findFunctions(node: any): any[] {
    const functions: any[] = [];
    
    if (node.type === 'FunctionDeclaration' || node.type === 'FunctionExpression' || node.type === 'ArrowFunctionExpression') {
      functions.push(node);
    }
    
    for (const key in node) {
      const value = node[key];
      if (Array.isArray(value)) {
        value.forEach(child => {
          if (child && typeof child === 'object' && child.type) {
            functions.push(...findFunctions(child));
          }
        });
      } else if (value && typeof value === 'object' && value.type) {
        functions.push(...findFunctions(value));
      }
    }
    
    return functions;
  }
  
  // 提取可分析节点（模拟AsyncRuleEngine的逻辑）
  function extractAnalyzableNodes(node: any): any[] {
    const nodes: any[] = [];
    
    function collectNodes(currentNode: any): void {
      if (!currentNode || typeof currentNode !== 'object') {
        return;
      }
      
      // AsyncRuleEngine的可分析节点类型
      const analyzableTypes = [
        'IfStatement',
        'WhileStatement',
        'ForStatement',
        'DoWhileStatement',
        'SwitchStatement',
        'ConditionalExpression',
        'LogicalExpression',
        'CatchClause',
        'JSXElement',
        'JSXFragment',
      ];
      
      if (analyzableTypes.includes(currentNode.type)) {
        nodes.push(currentNode);
        console.log(`找到可分析节点: ${currentNode.type}`);
      }
      
      // 递归遍历子节点
      for (const key in currentNode) {
        const value = currentNode[key];
        
        if (Array.isArray(value)) {
          value.forEach(child => {
            if (child && typeof child === 'object' && child.type) {
              collectNodes(child);
            }
          });
        } else if (value && typeof value === 'object' && value.type) {
          collectNodes(value);
        }
      }
    }
    
    collectNodes(node);
    return nodes;
  }
  
  const functions = findFunctions(ast);
  console.log('找到的函数数量:', functions.length);
  
  if (functions.length > 0) {
    const functionNode = functions[0];
    console.log('函数名:', functionNode.identifier?.value);
    console.log('\\n开始提取可分析节点...');
    
    const analyzableNodes = extractAnalyzableNodes(functionNode);
    console.log('\\n=== 总结 ===');
    console.log('可分析节点总数:', analyzableNodes.length);
    console.log('节点类型:', analyzableNodes.map(n => n.type));
  }
}

debugSimpleNodeExtraction().catch(console.error);