#!/usr/bin/env bun

/**
 * Test complex import statements to reproduce the bug
 */

import { parse } from '@swc/core';
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

async function testComplexImports() {
  console.log('=== Test Complex Import Scenarios ===\n');

  const testCases = [
    {
      name: 'Multi-line import with destructuring',
      code: `import {
  Ellipsis, 
  enqueueSnackbar, 
  useFireExport,
} from '@imile/components'`
    },
    {
      name: 'Import with type annotations',
      code: `import { 
  Component, 
  type Props 
} from 'react'`
    },
    {
      name: 'Complex import with computed property',
      code: `import { 
  [someVar]: renamed 
} from 'module'`
    },
    {
      name: 'Import with as clause',
      code: `import { 
  something as renamed,
  other
} from 'lib'`
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n--- Testing: ${testCase.name} ---`);
    console.log('Code:', testCase.code);

    try {
      const ast = await parse(testCase.code, {
        syntax: 'typescript',
        tsx: false,
        decorators: true,
        dynamicImport: true,
      });

      // Deep traverse the AST to find any BinaryExpression nodes
      const findBinaryExpressions = (node: any, path: string = 'root'): void => {
        if (!node || typeof node !== 'object') return;

        if (node.type === 'BinaryExpression') {
          console.log(`  🔍 BinaryExpression found at ${path}:`, {
            operator: node.operator,
            left: node.left?.type || 'unknown',
            right: node.right?.type || 'unknown'
          });
          
          // This could be the bug!
          console.log(`  ⚠️  This would trigger logical-operator rule with operator: ${node.operator}`);
        }

        // Recursively check all properties
        for (const [key, value] of Object.entries(node)) {
          if (key === 'span' || key === 'parent') continue;
          
          if (Array.isArray(value)) {
            value.forEach((item, i) => findBinaryExpressions(item, `${path}.${key}[${i}]`));
          } else if (value && typeof value === 'object') {
            findBinaryExpressions(value, `${path}.${key}`);
          }
        }
      };

      findBinaryExpressions(ast);

      // Also test with complexity visitor
      const detailCollector = new DetailCollector();
      const visitor = new ComplexityVisitor(testCase.code, detailCollector);

      // Override calculateNodeComplexity to log what nodes are being processed
      const originalCalculate = (visitor as any).calculateNodeComplexity.bind(visitor);
      (visitor as any).calculateNodeComplexity = function(node: any) {
        if (node.type === 'BinaryExpression') {
          console.log(`  📊 ComplexityVisitor processing BinaryExpression:`, {
            operator: node.operator,
            type: node.type,
            willTriggerLogicalOperator: true
          });
        }
        return originalCalculate(node);
      };

      visitor.visit(ast);

    } catch (error) {
      console.error(`  Error parsing ${testCase.name}:`, error.message);
    }
  }
}

// Also test what the getRuleIdForNodeType method returns
function testRuleMapping() {
  console.log('\n=== Rule Mapping Analysis ===');
  
  // Simulate the mapping logic from ComplexityVisitor
  const ruleMap: Record<string, string> = {
    'IfStatement': 'if-statement',
    'WhileStatement': 'while-statement',
    'DoWhileStatement': 'do-while-statement',
    'ForStatement': 'for-statement',
    'ForInStatement': 'for-statement',
    'ForOfStatement': 'for-statement',
    'SwitchStatement': 'switch-statement',
    'TryStatement': 'try-statement',
    'CatchClause': 'catch-clause',
    'ConditionalExpression': 'conditional-expression',
    'BinaryExpression': 'logical-operator',  // ❌ This is the problem!
    'LogicalExpression': 'logical-operator',
    'CallExpression': 'recursive-call'
  };

  console.log('Current rule mapping:');
  Object.entries(ruleMap).forEach(([nodeType, ruleId]) => {
    if (nodeType.includes('Binary') || nodeType.includes('Logical')) {
      console.log(`  ${nodeType} -> ${ruleId} ${nodeType === 'BinaryExpression' ? '❌ TOO BROAD' : '✅'}`);
    }
  });

  console.log('\n🔍 The issue: ALL BinaryExpression nodes map to logical-operator');
  console.log('   This includes import-related binary expressions!');
}

testComplexImports().then(testRuleMapping);