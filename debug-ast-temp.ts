import { parse } from '@swc/core';

async function debugAST() {
  const code = `
    class TreeNode {
      traverse() {
        if (!this.hasChildren()) return [this.value];
        return [this.value, ...this.children.map(child => child.traverse())];
      }
      
      process() {
        this.traverse();
      }
    }
  `;

  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  // 查找调用表达式节点
  function findCallExpression(node: any, depth = 0): any {
    if (node?.type === 'CallExpression') {
      console.log(`Found CallExpression at depth ${depth}:`);
      console.log('  callee:', JSON.stringify(node.callee, null, 2));
      console.log('  arguments:', JSON.stringify(node.arguments, null, 2));
      return node;
    }
    
    if (node && typeof node === 'object') {
      for (const key in node) {
        if (key !== 'parent' && key !== 'span') {
          const child = node[key];
          if (Array.isArray(child)) {
            for (const item of child) {
              const found = findCallExpression(item, depth + 1);
              if (found) return found;
            }
          } else {
            const found = findCallExpression(child, depth + 1);
            if (found) return found;
          }
        }
      }
    }
    
    return null;
  }

  const callNode = findCallExpression(result);
  console.log('\nFirst CallExpression found:');
  console.log(JSON.stringify(callNode, null, 2));
}

debugAST().catch(console.error);