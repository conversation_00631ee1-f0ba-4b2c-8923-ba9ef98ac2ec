/**
 * 静态 API 方法验证测试
 * 测试 analyze(), analyzeFile(), quickAnalyze() 方法
 */

import { ComplexityCalculator } from './src/core/calculator';
import { writeFileSync } from 'fs';
import { join } from 'path';

async function testStaticAPIMethods() {
  console.log('🔗 测试静态 API 方法...');

  try {
    // 测试 1: ComplexityCalculator.analyze()
    console.log('\n📊 测试 1: ComplexityCalculator.analyze()');
    const code1 = `
      function complex(x) {
        if (x > 0) {
          while (x-- > 0) {
            if (x % 2 === 0) {
              console.log(x);
            }
          }
        }
        return x;
      }
    `;
    
    const analyzeResult = await ComplexityCalculator.analyze(code1);
    console.log(`✅ analyze() 测试成功:`);
    console.log(`  - 函数数量: ${analyzeResult.length}`);
    console.log(`  - 函数名: ${analyzeResult[0]?.name}`);
    console.log(`  - 复杂度: ${analyzeResult[0]?.complexity}`);

    // 测试 2: ComplexityCalculator.quickAnalyze()
    console.log('\n⚡ 测试 2: ComplexityCalculator.quickAnalyze()');
    const code2 = `
      function simple() { return 42; }
      function medium() { 
        if (condition) { 
          return true; 
        } 
        return false; 
      }
      function complex() { 
        if (x) { 
          while (y) { 
            if (z) { 
              console.log('nested'); 
            } 
          } 
        } 
      }
    `;
    
    const quickResult = await ComplexityCalculator.quickAnalyze(code2);
    console.log(`✅ quickAnalyze() 测试成功:`);
    console.log(`  - 函数数量: ${quickResult.functionCount}`);
    console.log(`  - 总复杂度: ${quickResult.totalComplexity}`);
    console.log(`  - 平均复杂度: ${quickResult.averageComplexity}`);
    console.log(`  - 最大复杂度: ${quickResult.maxComplexity}`);
    console.log(`  - 复杂函数数量: ${quickResult.complexFunctions.length}`);
    if (quickResult.complexFunctions.length > 0) {
      console.log(`  - 最复杂函数: ${quickResult.complexFunctions[0].name} (${quickResult.complexFunctions[0].complexity})`);
    }

    // 测试 3: ComplexityCalculator.analyzeFile()
    console.log('\n📁 测试 3: ComplexityCalculator.analyzeFile()');
    
    // 创建测试文件
    const testFilePath = join(process.cwd(), 'temp-test-file.ts');
    const testFileContent = `
      // 测试文件
      function fibonacci(n: number): number {
        if (n <= 1) {
          return n;
        }
        return fibonacci(n - 1) + fibonacci(n - 2);
      }

      function processArray(arr: number[]): number[] {
        const result = [];
        for (let i = 0; i < arr.length; i++) {
          if (arr[i] > 0) {
            if (arr[i] % 2 === 0) {
              result.push(arr[i] * 2);
            } else {
              result.push(arr[i] * 3);
            }
          }
        }
        return result;
      }
    `;
    
    writeFileSync(testFilePath, testFileContent, 'utf8');
    
    const fileResult = await ComplexityCalculator.analyzeFile(testFilePath);
    console.log(`✅ analyzeFile() 测试成功:`);
    console.log(`  - 文件: ${testFilePath}`);
    console.log(`  - 函数数量: ${fileResult.length}`);
    
    fileResult.forEach((func, index) => {
      console.log(`  - 函数 ${index + 1}: ${func.name} (复杂度: ${func.complexity}, 行: ${func.line})`);
    });

    // 清理测试文件
    try {
      const fs = require('fs');
      fs.unlinkSync(testFilePath);
    } catch (error) {
      console.warn('清理测试文件失败:', error.message);
    }

    // 测试 4: ComplexityCalculator.analyzeFiles() (批量分析)
    console.log('\n📂 测试 4: ComplexityCalculator.analyzeFiles()');
    
    // 创建两个测试文件
    const testFile1 = join(process.cwd(), 'temp-test-1.ts');
    const testFile2 = join(process.cwd(), 'temp-test-2.ts');
    
    writeFileSync(testFile1, 'function test1() { if (x) return y; }', 'utf8');
    writeFileSync(testFile2, 'function test2() { while (a) { if (b) break; } }', 'utf8');
    
    const batchResult = await ComplexityCalculator.analyzeFiles([testFile1, testFile2]);
    console.log(`✅ analyzeFiles() 测试成功:`);
    console.log(`  - 分析文件数量: ${batchResult.size}`);
    
    for (const [filePath, functions] of batchResult) {
      console.log(`  - ${filePath}: ${functions.length} 个函数`);
      functions.forEach(func => {
        console.log(`    - ${func.name}: 复杂度 ${func.complexity}`);
      });
    }

    // 清理批量测试文件
    try {
      const fs = require('fs');
      fs.unlinkSync(testFile1);
      fs.unlinkSync(testFile2);
    } catch (error) {
      console.warn('清理批量测试文件失败:', error.message);
    }

    console.log('\n🎉 静态 API 方法验证完成！所有测试通过。');

  } catch (error) {
    console.error('❌ 静态 API 方法测试失败:', error);
  }
}

testStaticAPIMethods().catch(console.error);