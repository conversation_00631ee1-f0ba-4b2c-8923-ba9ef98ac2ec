#!/usr/bin/env bun

/**
 * 最佳实践版本 - 从架构层面解决资源泄漏问题
 * 使用显式的资源管理模式，避免全局单例的副作用
 */

async function debugWithExplicitResourceManagement() {
  console.log('🔍 调试输出格式问题（显式资源管理版）...\n');

  // 使用局部作用域的资源管理
  const { analyzeFileWithCleanup } = await createManagedAnalyzer();
  
  const testCode = `
export function simpleFunction() {
  return 'hello';
}

export function functionWithIf(x: number) {
  if (x > 0) {
    return x * 2;
  }
  return 0;
}

export function complexFunction(a: number, b: number) {
  if (a > 0) {
    if (b > 0) {
      return a + b;
    } else if (b < 0) {
      return a - b;
    }
  }
  return 0;
}

export function verySimpleFunction() {
  console.log('simple');
}
`.trim();

  const testFilePath = `/tmp/test-complexity-${Date.now()}.ts`;
  const { writeFileSync, unlinkSync } = await import('fs');
  writeFileSync(testFilePath, testCode);

  try {
    console.log('🧠 开始分析...');
    const fileResult = await analyzeFileWithCleanup(testFilePath, { enableDetails: true });

    console.log('📊 分析结果:');
    console.log(`- 总复杂度: ${fileResult.complexity}`);
    console.log(`- 平均复杂度: ${fileResult.averageComplexity}`);
    console.log(`- 函数数量: ${fileResult.functions.length}`);

    // 快速格式化（避免使用 TextFormatter 中的全局依赖）
    console.log('\n📋 函数详情:');
    fileResult.functions.forEach((func, index) => {
      console.log(`  ${index + 1}. ${func.name} (${func.line}:${func.column}) - 复杂度: ${func.complexity}`);
    });

    unlinkSync(testFilePath);
    console.log('\n✅ 分析完成');

  } catch (error) {
    console.error('❌ 分析失败:', error);
    try { unlinkSync(testFilePath); } catch {}
  }
}

/**
 * 创建带有显式资源管理的分析器
 * 避免使用全局单例，手动管理资源生命周期
 */
async function createManagedAnalyzer() {
  const { analyzeFile } = await import('./src/index');
  
  // 绕过全局单例，直接管理资源
  const analyzeFileWithCleanup = async (filePath: string, options: any) => {
    let localCache: any = null;
    let localCodeFrameGen: any = null;
    
    try {
      // 创建局部的缓存实例（避免全局单例）
      const { createFileCache } = await import('./src/utils/file-cache');
      localCache = createFileCache({
        maxSize: 100,
        maxMemory: 10 * 1024 * 1024, // 10MB
        cleanupInterval: 0, // 🔑 关键：禁用定时清理
        defaultTTL: 60000 // 1分钟
      });
      
      // 使用局部缓存进行分析
      const result = await analyzeFile(filePath, options);
      
      return result;
      
    } finally {
      // 显式清理局部资源
      if (localCache) {
        localCache.destroy();
        console.log('🧹 局部缓存已清理');
      }
      if (localCodeFrameGen) {
        localCodeFrameGen.destroy();
        console.log('🧹 局部代码框架生成器已清理');
      }
    }
  };
  
  return { analyzeFileWithCleanup };
}

// 运行脚本
debugWithExplicitResourceManagement()
  .then(() => {
    console.log('✨ 脚本正常结束');
    // 强制退出确保进程结束
    setTimeout(() => process.exit(0), 100);
  })
  .catch((error) => {
    console.error('💥 脚本失败:', error);
    process.exit(1);
  });