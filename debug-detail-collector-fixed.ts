import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

async function debugDetailCollectorFixed() {
  const sourceCode = `
    function test() {
      if (condition) {
        console.log('test');
      }
    }
  `;
  
  console.log('=== DetailCollector 集成测试 (修复版) ===');
  console.log('源代码:', sourceCode.trim());

  const parser = new ASTParser();
  const ast = await parser.parseCode(sourceCode, 'debug.ts');
  
  const detailCollector = new DetailCollector();
  console.log('DetailCollector 已创建');
  
  // 不要手动调用 startFunction，让 ComplexityVisitor 管理
  const visitor = new ComplexityVisitor(sourceCode, detailCollector);
  console.log('ComplexityVisitor 已创建，传入了 detailCollector');
  
  visitor.visit(ast);
  console.log('AST 访问完成');
  
  // 不要手动调用 endFunction，从 visitor 的结果中获取函数详情
  const results = visitor.getResults();
  console.log('获取到结果');
  
  console.log('\n=== 结果 ===');
  console.log('总复杂度:', visitor.getTotalComplexity());
  console.log('函数数量:', results.length);
  
  if (results.length > 0) {
    const firstFunction = results[0];
    console.log('第一个函数名:', firstFunction.name);
    console.log('第一个函数复杂度:', firstFunction.complexity);
    console.log('第一个函数详情:', firstFunction.details ? firstFunction.details.length : '无详情');
    
    if (firstFunction.details && firstFunction.details.length > 0) {
      console.log('\n=== 详细步骤 ===');
      firstFunction.details.forEach((step, index) => {
        console.log(`${index + 1}. ${step.description} (+${step.increment}) [${step.line}:${step.column}]`);
      });
    } else {
      console.log('\n=== 详细步骤 ===');
      console.log('没有记录到步骤');
    }
  }
}

debugDetailCollectorFixed().catch(console.error);