/**
 * 调试 import 语句被误判为逻辑运算符的问题
 */

import { ASTParser } from './src/core/parser';
import { LogicalOperatorRule } from './src/rules/logical-operator-rule';
import { hasNodeType, hasOperator, isLogicalBinaryExpression } from './src/utils/type-guards';

// 测试用例：包含多行 import 的代码
const testCode = `
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Ellipsis, enqueueSnackbar, useFireExport,
} from '@imile/components'

function test() {
  return 1;
}
`;

async function debugImportFalsePositive() {
  console.log('🔍 调试 import 语句误判问题');
  console.log('━'.repeat(50));

  try {
    // 1. 解析代码
    console.log('1. 解析代码...');
    const parser = new ASTParser();
    const ast = await parser.parseCode(testCode, 'test.ts');
    
    // 2. 遍历所有节点，寻找被误判的节点
    console.log('\n2. 遍历 AST 节点...');
    const rule = new LogicalOperatorRule();
    
    function traverseAST(node: any, depth = 0) {
      if (!node || typeof node !== 'object') return;
      
      const indent = '  '.repeat(depth);
      const nodeInfo = `${indent}类型: ${node.type || 'unknown'}`;
      
      // 检查该节点是否会被逻辑运算符规则处理
      const canHandle = rule.canHandle(node);
      
      if (canHandle) {
        console.log(`${nodeInfo} ⚠️  被逻辑运算符规则识别!`);
        console.log(`${indent}  - hasNodeType: ${hasNodeType(node)}`);
        console.log(`${indent}  - hasOperator: ${hasOperator(node)}`);
        if (hasOperator(node)) {
          console.log(`${indent}  - operator: "${node.operator}"`);
        }
        console.log(`${indent}  - isLogicalBinaryExpression: ${isLogicalBinaryExpression(node)}`);
        
        // 打印完整节点信息以便分析
        console.log(`${indent}  - 完整节点:`, JSON.stringify(node, null, 2));
      } else if (hasNodeType(node)) {
        console.log(`${nodeInfo} ✓`);
      }
      
      // 递归遍历子节点
      for (const key in node) {
        if (node.hasOwnProperty(key) && key !== 'span') {
          const child = node[key];
          if (Array.isArray(child)) {
            child.forEach((item, index) => {
              if (typeof item === 'object' && item !== null) {
                console.log(`${indent}  [${key}[${index}]]:`);
                traverseAST(item, depth + 2);
              }
            });
          } else if (typeof child === 'object' && child !== null) {
            console.log(`${indent}  [${key}]:`);
            traverseAST(child, depth + 2);
          }
        }
      }
    }
    
    traverseAST(ast);
    
  } catch (error) {
    console.error('❌ 调试过程出错:', error);
  }
}

// 运行调试
debugImportFalsePositive();