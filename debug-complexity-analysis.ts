/**
 * 调试复杂度分析过程，查看实际的分析结果
 */

import { analyzeFile } from './src/index';

// 创建测试文件
const testContent = `/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Ellipsis, enqueueSnackbar, useFireExport,
} from '@imile/components'

function simpleFunction() {
  return 42;
}

function complexFunction() {
  if (true) {
    return 1;
  }
  return 0;
}
`;

async function debugComplexityAnalysis() {
  console.log('🔍 分析复杂度计算结果');
  console.log('━'.repeat(50));

  try {
    // 创建临时测试文件
    const fs = require('fs');
    const path = require('path');
    const tempDir = path.join(process.cwd(), 'temp-debug');
    const tempFilePath = path.join(tempDir, 'test-import.ts');
    
    // 创建临时目录
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // 写入测试文件
    fs.writeFileSync(tempFilePath, testContent);
    
    console.log('1. 分析测试文件...');
    console.log(`文件路径: ${tempFilePath}`);
    
    // 分析文件
    const results = await analyzeFile(tempFilePath, {
      includeDetails: true,
      maxComplexity: 10
    });
    
    console.log('\n2. 分析结果:');
    console.log('results:', JSON.stringify(results, null, 2));
    
    // 检查是否有意外的复杂度增加
    results.functions.forEach((func, index) => {
      console.log(`\n函数 ${index + 1}: ${func.name}`);
      console.log(`  复杂度: ${func.complexity}`);
      console.log(`  位置: 行 ${func.startLine}-${func.endLine}`);
      
      if (func.details && func.details.length > 0) {
        console.log('  详情:');
        func.details.forEach((detail, detailIndex) => {
          console.log(`    ${detailIndex + 1}. L${detail.line}: +${detail.complexity} (累计: ${detail.cumulativeComplexity}) - ${detail.reason} [${detail.rule}]`);
          console.log(`       嵌套层级: ${detail.nesting}`);
        });
      }
    });
    
    // 清理临时文件
    fs.unlinkSync(tempFilePath);
    fs.rmSync(tempDir, { recursive: true, force: true });
    
  } catch (error) {
    console.error('❌ 分析过程出错:', error);
    
    // 确保清理临时文件
    try {
      const fs = require('fs');
      const path = require('path');
      const tempDir = path.join(process.cwd(), 'temp-debug');
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
    } catch (cleanupError) {
      console.error('清理临时文件失败:', cleanupError);
    }
  }
}

// 运行分析
debugComplexityAnalysis();