{
  "compilerOptions": {
    // Environment setup & latest features
    "lib": ["ESNext", "DOM"],
    "target": "ESNext",
    "module": "Preserve",
    "moduleDetection": "force",
    "allowJs": true,

    // Bundler mode
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "verbatimModuleSyntax": true,
    "noEmit": true,
    "esModuleInterop": true,

    // Best practices
    "strict": true,
    "skipLibCheck": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,

    // Enable strict flags gradually for type safety enhancement
    "noUnusedLocals": false,  // 先禁用，等待清理代码后启用
    "noUnusedParameters": false,  // 先禁用，等待清理代码后启用
    "noPropertyAccessFromIndexSignature": false,  // 先禁用，需要重构代码
    "exactOptionalPropertyTypes": false,  // 先禁用，需要重构类型定义
    "noImplicitReturns": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false,

    // CLI specific
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "src/__test__/**/*"]
}
