#!/usr/bin/env bun

/**
 * 最小化测试脚本 - 验证FileContentCache定时器泄漏
 */

import { getGlobalFileCache } from './src/utils/file-cache';

console.log('🔍 测试FileContentCache单例...');

// 直接调用getGlobalFileCache()看是否会导致进程不退出
const cache = getGlobalFileCache();
console.log('✅ FileContentCache单例已创建');

console.log('📊 缓存统计:', cache.getCacheStats());
console.log('🎉 脚本逻辑完成，如果进程不退出说明定时器泄漏');

// 注意：这里没有调用cache.destroy()，看进程是否会退出