#!/usr/bin/env bun

/**
 * Debug script to check AST parsing of import statements
 */

import { parse } from '@swc/core';
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

async function debugImportAst() {
  console.log('=== Debug: Import Statement AST Analysis ===\n');

  // Create test code with import statements
  const testCode = `
import {
  Ellipsis, enqueueSnackbar, useFireExport,
} from '@imile/components'

function testFunction() {
  if (a && b) {
    return true;
  }
  return false;
}
`;

  try {
    // Parse the code
    const ast = await parse(testCode, {
      syntax: 'typescript',
      tsx: false,
      decorators: true,
      dynamicImport: true,
    });

    console.log('AST parsed successfully');
    console.log('Module body items:');
    
    // Check the AST structure
    if (ast.type === 'Module' && ast.body) {
      ast.body.forEach((item, index) => {
        console.log(`Item ${index}: ${item.type}`);
        if (item.type === 'ImportDeclaration') {
          console.log('  - Import Declaration found');
          console.log('  - Specifiers:', item.specifiers?.length || 0);
        }
      });
    }

    console.log('\n--- Running Complexity Analysis ---');

    // Create detail collector to capture complexity steps
    const detailCollector = new DetailCollector();
    const visitor = new ComplexityVisitor(testCode, detailCollector, {
      includeDetails: true
    });

    // Visit the AST  
    visitor.visit(ast);
    const results = visitor.getResults();

    console.log('Visitor Results:', JSON.stringify(results, null, 2));

    // Check for any details from the collector
    const allDetails = detailCollector.getAllDetails();
    console.log('\nDetail collector results:');
    console.log(JSON.stringify(allDetails, null, 2));

    // Look for any logical operator details in first few lines
    const suspiciousDetails = allDetails.filter(detail => 
      detail.line <= 5 && detail.ruleId === 'logical-operator'
    );

    if (suspiciousDetails.length > 0) {
      console.error('\n❌ BUG CONFIRMED: Import-related logical operator detected!');
      console.error('Suspicious details:', suspiciousDetails);
    } else {
      console.log('\n✅ No import-related logical operator false positive detected');
    }

  } catch (error) {
    console.error('Error during analysis:', error);
    console.error('Stack:', error.stack);
  }
}

// Run the debug
debugImportAst();