#!/usr/bin/env bun

import { ComplexityCalculator } from './src/core/calculator';

async function quickTest() {
  // try-catch
  const tryCatchCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  const ternaryCode = `
    function withTernary() {
      return condition ? "yes" : "no";
    }
  `;
  
  const tryResult = await ComplexityCalculator.analyze(tryCatchCode);
  const ternaryResult = await ComplexityCalculator.analyze(ternaryCode);
  
  console.log('try-catch实际复杂度:', tryResult[0]?.complexity);
  console.log('三元运算符实际复杂度:', ternaryResult[0]?.complexity);
}

quickTest().catch(console.error);