import { ComplexityCalculator } from './src/core/calculator';

async function testSpecificCases() {
  const tests = [
    {
      name: 'Simple if',
      code: 'function test() { if (true) {} }',
      expected: 1
    },
    {
      name: 'Nested if + for',
      code: 'function test() { if (true) { for (let i = 0; i < 10; i++) {} } }',
      expected: 3  // if(1) + for(1) + nesting(1)
    },
    {
      name: 'Switch statement',
      code: 'function test() { switch(x) { case 1: break; case 2: break; default: break; } }',
      expected: 1  // switch(1)
    },
    {
      name: 'Try-catch',
      code: 'function test() { try { doSomething(); } catch (e) { handleError(); } }',
      expected: 1  // catch(1)
    }
  ];
  
  const calculator = new ComplexityCalculator();
  
  for (const test of tests) {
    console.log(`\n测试: ${test.name}`);
    console.log(`代码: ${test.code}`);
    console.log(`期望复杂度: ${test.expected}`);
    
    const results = await calculator.calculateCode(test.code, 'test.js');
    const actual = results[0]?.complexity || 0;
    
    console.log(`实际复杂度: ${actual}`);
    console.log(`结果: ${actual === test.expected ? '✅ 通过' : '❌ 失败'}`);
  }
}

testSpecificCases().catch(console.error);