import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';

async function debugTwoLevel() {
  const sourceCode = `
    function test() {
      if (condition1) {
        if (condition2) {
          console.log('nested');
        }
      }
    }
  `;
  
  console.log('=== 两层嵌套测试 ===');
  console.log('源代码:', sourceCode.trim());

  const parser = new ASTParser();
  const ast = await parser.parseCode(sourceCode, 'debug.ts');
  
  const visitor = new ComplexityVisitor(sourceCode);
  
  console.log('\n=== 分析结果 ===');
  visitor.visit(ast);
  
  console.log('总复杂度:', visitor.getTotalComplexity());
  console.log('期望复杂度: 3');
  console.log('- if (condition1): 1 + 0 = 1');
  console.log('- if (condition2): 1 + 1 = 2');
  console.log('- 总计: 1 + 2 = 3');
}

debugTwoLevel().catch(console.error);