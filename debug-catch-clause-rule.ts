#!/usr/bin/env bun

/**
 * 直接测试CatchClauseRule
 */

import { CatchClauseRule } from './src/rules/catch-clause-rule';
import { ASTParser } from './src/core/parser';

async function testCatchClauseRule() {
  console.log('=== 直接测试CatchClauseRule ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  // 解析AST
  const parser = new ASTParser();
  const ast = await parser.parseCode(testCode, 'debug.ts');
  
  // 找到CatchClause节点
  function findCatchClause(node: any): any {
    if (node.type === 'CatchClause') {
      return node;
    }
    
    for (const key in node) {
      const value = node[key];
      if (Array.isArray(value)) {
        for (const child of value) {
          if (child && typeof child === 'object' && child.type) {
            const result = findCatchClause(child);
            if (result) return result;
          }
        }
      } else if (value && typeof value === 'object' && value.type) {
        const result = findCatchClause(value);
        if (result) return result;
      }
    }
    
    return null;
  }
  
  const catchClauseNode = findCatchClause(ast);
  
  if (catchClauseNode) {
    console.log('找到CatchClause节点');
    console.log('节点类型:', catchClauseNode.type);
    console.log('有错误参数:', !!catchClauseNode.param);
    
    // 创建规则实例
    const rule = new CatchClauseRule();
    
    // 测试canHandle
    const canHandle = rule.canHandle(catchClauseNode);
    console.log('\\nCatchClauseRule canHandle:', canHandle);
    
    if (canHandle) {
      // 创建分析上下文
      const context = {
        nestingLevel: 0,
        functionName: 'withTryCatch',
        sourceCode: testCode,
        filePath: 'debug.ts',
        nodeIndex: 0
      };
      
      console.log('\\n开始评估节点...');
      try {
        const result = await rule.evaluate(catchClauseNode, context);
        console.log('评估结果:');
        console.log('- 复杂度:', result.complexity);
        console.log('- 原因:', result.reason);
        console.log('- 增加嵌套:', result.increasesNesting);
        console.log('- 建议数量:', result.suggestions?.length || 0);
      } catch (error) {
        console.error('评估错误:', error);
      }
    }
  } else {
    console.log('未找到CatchClause节点');
  }
}

testCatchClauseRule().catch(console.error);