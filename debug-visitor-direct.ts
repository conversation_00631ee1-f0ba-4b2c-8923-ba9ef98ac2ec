import { ASTParser } from './src/core/parser';
import { FunctionFinderVisitor } from './src/core/function-finder-visitor';
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';
import { initializeRules } from './src/core/default-rules';

async function directTestComplexityVisitor() {
  // Initialize rules
  initializeRules(true);
  
  const code = `
    function nested(x: number, y: number) {
      if (x > 0) {        // +1
        if (y > 0) {      // +2 (nested)
          return x + y;
        }
      }
      return 0;
    }
  `;
  
  try {
    // Parse AST
    const parser = new ASTParser();
    const ast = await parser.parseCode(code, 'test.ts');
    
    // Find functions
    const functions = FunctionFinderVisitor.find(ast);
    console.log('找到函数数量:', functions.length);
    
    if (functions.length > 0) {
      const func = functions[0];
      console.log('函数类型:', func.type);
      
      // Create detail collector
      const detailCollector = new DetailCollector();
      detailCollector.startFunction('nested');
      
      // Create complexity visitor
      const visitor = new ComplexityVisitor(code, detailCollector);
      
      // Visit the function
      visitor.visit(func);
      
      const totalComplexity = visitor.getTotalComplexity();
      console.log('Visitor 计算的复杂度:', totalComplexity);
      
      // Get details
      const funcResult = detailCollector.endFunction();
      
      console.log('\nDetailCollector 信息:');
      console.log('函数名:', funcResult.name);
      console.log('复杂度:', funcResult.complexity);
      console.log('详细步骤数:', funcResult.details.length);
      
      funcResult.details.forEach((detail, index) => {
        console.log(`  ${index + 1}. ${detail.description}: +${detail.increment} (行${detail.line})`);
      });
      
      const totalFromDetails = funcResult.details.reduce((sum, detail) => sum + detail.increment, 0);
      console.log(`\n详细步骤总计: ${totalFromDetails}`);
    }
  } catch (error) {
    console.error('计算出错:', error);
  }
}

directTestComplexityVisitor().catch(console.error);