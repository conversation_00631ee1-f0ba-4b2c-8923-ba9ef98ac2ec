#!/usr/bin/env bun
/**
 * IoC 重构最终验收测试
 * 
 * 验证重构后的系统是否满足所有要求：
 * 1. 核心功能完整性
 * 2. 性能保持或提升
 * 3. API 向后兼容性  
 * 4. 资源管理正确性
 * 5. 错误处理健壮性
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory, createLightweightFactory } from './src/core/calculator-factory';
import type { CalculatorOptions } from './src/engine/types';

// 验收测试样本
const acceptanceTestCases = {
  simpleFunction: {
    code: 'function simple() { return 42; }',
    expectedFunctions: 1,
    expectedMinComplexity: 0,
    description: '简单函数'
  },
  
  conditionalLogic: {
    code: `
      function conditional(x) {
        if (x > 0) {
          return x * 2;
        } else if (x < 0) {
          return x * -1;
        }
        return 0;
      }
    `,
    expectedFunctions: 1,
    expectedMinComplexity: 2,
    description: '条件逻辑'
  },
  
  loopingLogic: {
    code: `
      function looping(arr) {
        let sum = 0;
        for (let i = 0; i < arr.length; i++) {
          if (arr[i] > 0) {
            sum += arr[i];
          }
        }
        return sum;
      }
    `,
    expectedFunctions: 1,
    expectedMinComplexity: 2,
    description: '循环逻辑'
  },
  
  logicalOperators: {
    code: `
      function logical(a, b, c) {
        return (a && b) || (c && !a) || (b || c);
      }
    `,
    expectedFunctions: 1,
    expectedMinComplexity: 2,
    description: '逻辑运算符'
  },
  
  nestedComplexity: {
    code: `
      function nested(data) {
        if (data) {
          for (const item of data) {
            if (item.active) {
              while (item.processing) {
                if (item.ready) {
                  return item.result;
                }
              }
            }
          }
        }
        return null;
      }
    `,
    expectedFunctions: 1,
    expectedMinComplexity: 4,
    description: '嵌套复杂度'
  }
};

interface AcceptanceResult {
  testName: string;
  description: string;
  passed: boolean;
  details: {
    functionsFound: number;
    actualComplexity: number;
    expectedMinComplexity: number;
    executionTime: number;
  };
  error?: string;
}

class AcceptanceTestSuite {
  private results: AcceptanceResult[] = [];
  private totalTests = 0;
  private passedTests = 0;
  
  async runFullAcceptanceTest(): Promise<void> {
    console.log('🎯 IoC 重构最终验收测试');
    console.log('='.repeat(60));
    
    // 1. 功能完整性测试
    console.log('\n📋 1. 功能完整性测试');
    console.log('-'.repeat(30));
    await this.testFunctionalCompleteness();
    
    // 2. API 兼容性测试
    console.log('\n🔄 2. API 向后兼容性测试');
    console.log('-'.repeat(30));
    await this.testApiCompatibility();
    
    // 3. 性能基准验证
    console.log('\n⚡ 3. 性能基准验证');
    console.log('-'.repeat(30));
    await this.testPerformanceBenchmarks();
    
    // 4. 资源管理验证
    console.log('\n🧹 4. 资源管理验证');
    console.log('-'.repeat(30));
    await this.testResourceManagement();
    
    // 5. 错误处理验证
    console.log('\n🛡️ 5. 错误处理验证');
    console.log('-'.repeat(30));
    await this.testErrorHandling();
    
    // 6. 并发安全性测试
    console.log('\n🚀 6. 并发安全性测试');
    console.log('-'.repeat(30));
    await this.testConcurrencySafety();
    
    // 生成最终报告
    this.generateFinalReport();
  }
  
  private async testFunctionalCompleteness(): Promise<void> {
    for (const [testName, testCase] of Object.entries(acceptanceTestCases)) {
      const result = await this.runSingleTest(testName, testCase, async (code) => {
        return await ComplexityCalculator.analyze(code);
      });
      
      this.results.push(result);
      this.printTestResult(result);
    }
  }
  
  private async testApiCompatibility(): Promise<void> {
    // 测试传统构造方式
    const legacyTest = await this.runSingleTest('legacy-constructor', acceptanceTestCases.simpleFunction, async (code) => {
      const calculator = new ComplexityCalculator();
      try {
        return await calculator.calculateCode(code, 'test.ts');
      } finally {
        await calculator.dispose();
      }
    });
    
    this.results.push(legacyTest);
    this.printTestResult(legacyTest);
    
    // 测试静态方法
    const staticTest = await this.runSingleTest('static-api', acceptanceTestCases.conditionalLogic, async (code) => {
      return await ComplexityCalculator.analyze(code);
    });
    
    this.results.push(staticTest);
    this.printTestResult(staticTest);
  }
  
  private async testPerformanceBenchmarks(): Promise<void> {
    const iterations = 10;
    const testCode = acceptanceTestCases.conditionalLogic.code;
    
    // 测试静态 API 性能
    const staticTimes: number[] = [];
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      await ComplexityCalculator.analyze(testCode);
      staticTimes.push(performance.now() - start);
    }
    
    // 测试轻量级工厂性能
    const lightweightTimes: number[] = [];
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      const factory = createLightweightFactory();
      const calculator = new ComplexityCalculator({}, factory);
      try {
        await calculator.calculateCode(testCode, 'perf-test.ts');
      } finally {
        await calculator.dispose();
      }
      lightweightTimes.push(performance.now() - start);
    }
    
    const avgStaticTime = staticTimes.reduce((a, b) => a + b, 0) / iterations;
    const avgLightweightTime = lightweightTimes.reduce((a, b) => a + b, 0) / iterations;
    
    console.log(`  静态 API 平均时间: ${avgStaticTime.toFixed(2)}ms`);
    console.log(`  轻量级工厂平均时间: ${avgLightweightTime.toFixed(2)}ms`);
    
    // 性能基准：应该在 1ms 以内
    const performancePassed = avgStaticTime < 1.0 && avgLightweightTime < 1.0;
    console.log(`  ✅ 性能基准: ${performancePassed ? '通过' : '未通过'} (< 1ms)`);
    
    this.totalTests++;
    if (performancePassed) this.passedTests++;
  }
  
  private async testResourceManagement(): Promise<void> {
    const initialMemory = process.memoryUsage().heapUsed;
    
    // 创建和销毁多个计算器实例
    for (let i = 0; i < 10; i++) {
      const factory = new CalculatorFactory({
        enableMonitoring: false,
        quiet: true
      });
      const calculator = new ComplexityCalculator({}, factory);
      
      try {
        await calculator.calculateCode(acceptanceTestCases.simpleFunction.code, 'resource-test.ts');
      } finally {
        await calculator.dispose();
      }
    }
    
    // 强制垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }
    
    const finalMemory = process.memoryUsage().heapUsed;
    const memoryIncrease = finalMemory - initialMemory;
    
    console.log(`  初始内存: ${(initialMemory / 1024 / 1024).toFixed(2)}MB`);
    console.log(`  最终内存: ${(finalMemory / 1024 / 1024).toFixed(2)}MB`);
    console.log(`  内存增长: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    
    // 内存泄漏检查：增长应该小于 10MB
    const resourcesPassed = memoryIncrease < 10 * 1024 * 1024;
    console.log(`  ✅ 资源管理: ${resourcesPassed ? '通过' : '未通过'} (< 10MB 增长)`);
    
    this.totalTests++;
    if (resourcesPassed) this.passedTests++;
  }
  
  private async testErrorHandling(): Promise<void> {
    const errorTests = [
      {
        name: '无效语法',
        code: 'function invalid( { invalid syntax',
        shouldThrow: true
      },
      {
        name: '空代码',
        code: '',
        shouldThrow: false,
        expectedLength: 0
      },
      {
        name: '只有注释',
        code: '// just a comment',
        shouldThrow: false,
        expectedLength: 0
      }
    ];
    
    for (const errorTest of errorTests) {
      try {
        const results = await ComplexityCalculator.analyze(errorTest.code);
        
        if (errorTest.shouldThrow) {
          console.log(`  ❌ ${errorTest.name}: 应该抛出错误但没有`);
        } else {
          const lengthCorrect = !errorTest.expectedLength || results.length === errorTest.expectedLength;
          console.log(`  ✅ ${errorTest.name}: ${lengthCorrect ? '通过' : '未通过'}`);
          this.totalTests++;
          if (lengthCorrect) this.passedTests++;
        }
      } catch (error) {
        if (errorTest.shouldThrow) {
          console.log(`  ✅ ${errorTest.name}: 正确抛出错误`);
          this.totalTests++;
          this.passedTests++;
        } else {
          console.log(`  ❌ ${errorTest.name}: 不应该抛出错误`);
          this.totalTests++;
        }
      }
    }
  }
  
  private async testConcurrencySafety(): Promise<void> {
    const concurrentTasks = Array.from({ length: 5 }, async (_, index) => {
      const factory = createLightweightFactory();
      const calculator = new ComplexityCalculator({}, factory);
      
      try {
        const results = await calculator.calculateCode(
          acceptanceTestCases.nestedComplexity.code,
          `concurrent-${index}.ts`
        );
        return results.length > 0;
      } finally {
        await calculator.dispose();
      }
    });
    
    const results = await Promise.all(concurrentTasks);
    const allPassed = results.every(result => result === true);
    
    console.log(`  并发任务完成: ${results.length}`);
    console.log(`  ✅ 并发安全性: ${allPassed ? '通过' : '未通过'}`);
    
    this.totalTests++;
    if (allPassed) this.passedTests++;
  }
  
  private async runSingleTest(
    testName: string,
    testCase: typeof acceptanceTestCases[keyof typeof acceptanceTestCases],
    executor: (code: string) => Promise<any[]>
  ): Promise<AcceptanceResult> {
    this.totalTests++;
    
    try {
      const start = performance.now();
      const results = await executor(testCase.code);
      const executionTime = performance.now() - start;
      
      const functionsFound = results.length;
      const actualComplexity = results[0]?.complexity || 0;
      
      const functionsCorrect = functionsFound === testCase.expectedFunctions;
      const complexityCorrect = actualComplexity >= testCase.expectedMinComplexity;
      const passed = functionsCorrect && complexityCorrect;
      
      if (passed) this.passedTests++;
      
      return {
        testName,
        description: testCase.description,
        passed,
        details: {
          functionsFound,
          actualComplexity,
          expectedMinComplexity: testCase.expectedMinComplexity,
          executionTime
        }
      };
    } catch (error) {
      return {
        testName,
        description: testCase.description,
        passed: false,
        details: {
          functionsFound: 0,
          actualComplexity: 0,
          expectedMinComplexity: testCase.expectedMinComplexity,
          executionTime: 0
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  private printTestResult(result: AcceptanceResult): void {
    const status = result.passed ? '✅' : '❌';
    const details = result.error ? 
      `错误: ${result.error}` : 
      `函数: ${result.details.functionsFound}, 复杂度: ${result.details.actualComplexity}, 时间: ${result.details.executionTime.toFixed(2)}ms`;
    
    console.log(`  ${status} ${result.description}: ${details}`);
  }
  
  private generateFinalReport(): void {
    console.log('\n🎯 最终验收测试报告');
    console.log('='.repeat(60));
    
    const successRate = (this.passedTests / this.totalTests * 100).toFixed(1);
    
    console.log(`测试总数: ${this.totalTests}`);
    console.log(`通过测试: ${this.passedTests}`);
    console.log(`失败测试: ${this.totalTests - this.passedTests}`);
    console.log(`成功率: ${successRate}%`);
    
    if (this.passedTests === this.totalTests) {
      console.log('\n🎉 所有验收测试通过！');
      console.log('✅ IoC 重构成功完成，系统满足所有验收标准');
      console.log('\n核心成果:');
      console.log('  🏗️  依赖注入架构 - 实现现代化架构设计');
      console.log('  ⚡ 高性能保持 - 亚毫秒级分析速度');
      console.log('  🔄 向后兼容 - 现有代码无需修改');
      console.log('  🧹 资源管理 - 自动清理，防止内存泄漏');
      console.log('  🛡️  错误处理 - 健壮的异常处理机制');
      console.log('  🚀 并发安全 - 支持安全的并发处理');
    } else {
      console.log('\n⚠️ 部分验收测试未通过');
      console.log('需要进一步调查和修复失败的测试用例');
    }
    
    // 内存使用报告
    const memUsage = process.memoryUsage();
    console.log('\n💾 最终内存使用:');
    console.log(`  堆内存: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    console.log(`  总内存: ${(memUsage.heapTotal / 1024 / 1024).toFixed(2)}MB`);
  }
}

// 运行验收测试
const acceptanceTest = new AcceptanceTestSuite();
acceptanceTest.runFullAcceptanceTest().catch(error => {
  console.error('💥 验收测试执行失败:', error);
  process.exit(1);
});