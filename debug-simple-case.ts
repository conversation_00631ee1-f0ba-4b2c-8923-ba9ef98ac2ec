import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';

async function debugSimpleCase() {
  const sourceCode = `
    function test() {
      if (condition) {
        console.log('test');
      }
    }
  `;
  
  console.log('=== 简单 if 语句测试 ===');
  console.log('源代码:', sourceCode.trim());

  const parser = new ASTParser();
  const ast = await parser.parseCode(sourceCode, 'debug.ts');
  
  const visitor = new ComplexityVisitor(sourceCode);
  
  console.log('\n=== 分析结果 ===');
  visitor.visit(ast);
  
  console.log('总复杂度:', visitor.getTotalComplexity());
  console.log('期望复杂度: 1 (if语句基础复杂度1 + 嵌套级别0)');
}

debugSimpleCase().catch(console.error);