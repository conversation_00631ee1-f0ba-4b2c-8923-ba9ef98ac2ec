import { Hono } from 'hono';
import { serve } from '@hono/node-server';
import { serveStatic } from '@hono/node-server/serve-static';
import type { Context } from 'hono';
import { promises as fs } from 'fs';
import { join } from 'path';
import { createServer } from 'net';
import open from 'open';
import type { Server } from 'http';
import type { ServerType } from '@hono/node-server';
import type { AnalysisResult } from '../core/types';
import type { CognitiveConfig } from '../config/types';
import { HtmlFormatter } from '../formatters/html';

export interface UIServerOptions {
  port?: number;
  host?: string;
  openBrowser?: boolean;
  autoShutdown?: boolean;
}

export class UIServer {
  private app: Hono;
  private server: ServerType | null = null;
  private config: CognitiveConfig;
  private options: UIServerOptions;
  private htmlFormatter: HtmlFormatter;
  private currentPort: number = 0;

  constructor(config: CognitiveConfig, options: UIServerOptions = {}) {
    this.config = config;
    this.options = {
      port: 3000,
      host: 'localhost',
      openBrowser: true,
      autoShutdown: false,
      ...options
    };
    this.app = new Hono();
    this.htmlFormatter = new HtmlFormatter(config);
    this.setupRoutes();
  }

  private setupRoutes(): void {
    // 静态资源中间件
    this.app.use('/*', serveStatic({ root: './public' }));

    // 主路由
    this.app.get('/', (c: Context) => {
      const actualPort = this.currentPort || this.options.port;
      return c.html(`
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>认知复杂度分析 - Web UI</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    line-height: 1.6;
                    margin: 0;
                    padding: 40px;
                    background: #f5f5f5;
                }
                .container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 40px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    text-align: center;
                }
                h1 {
                    color: #4a5568;
                    margin-bottom: 20px;
                }
                .status {
                    padding: 20px;
                    background: #e6fffa;
                    border-left: 4px solid #38b2ac;
                    border-radius: 4px;
                    margin: 20px 0;
                }
                .spinner {
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #38b2ac;
                    border-radius: 50%;
                    width: 40px;
                    height: 40px;
                    animation: spin 1s linear infinite;
                    margin: 20px auto;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .info {
                    color: #666;
                    margin-top: 20px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔍 认知复杂度分析 Web UI</h1>
                <div class="status">
                    <div class="spinner"></div>
                    <p><strong>正在等待分析结果...</strong></p>
                    <p>分析完成后，结果将自动显示在此页面</p>
                </div>
                <div class="info">
                    <p>服务器运行在 ${this.options.host}:${actualPort}</p>
                    <p>分析完成后点击刷新或访问 <a href="/report">/report</a> 查看详细报告</p>
                </div>
            </div>
            <script>
                // 每5秒检查一次是否有结果
                const checkForResults = () => {
                    fetch('/api/status')
                        .then(response => response.json())
                        .then(data => {
                            if (data.hasResults) {
                                window.location.href = '/report';
                            }
                        })
                        .catch(error => {
                            console.log('等待分析结果...');
                        });
                };
                
                setInterval(checkForResults, 5000);
                
                // 立即检查一次
                setTimeout(checkForResults, 1000);
            </script>
        </body>
        </html>
      `);
    });

    // API 状态检查
    this.app.get('/api/status', (c: Context) => {
      return c.json({ 
        hasResults: this.hasResults(),
        status: 'running',
        timestamp: new Date().toISOString()
      });
    });

    // 报告路由
    this.app.get('/report', (c: Context) => {
      const result = this.getStoredResult();
      if (result) {
        const htmlContent = this.htmlFormatter.format(result, true);
        return c.html(htmlContent);
      } else {
        return c.html(`
          <html>
            <body style="font-family: sans-serif; text-align: center; padding: 40px;">
              <h1>报告尚未生成</h1>
              <p>请等待分析完成后再访问此页面</p>
              <a href="/">返回首页</a>
            </body>
          </html>
        `, 404);
      }
    });

    // API 获取原始结果
    this.app.get('/api/result', (c: Context) => {
      const result = this.getStoredResult();
      if (result) {
        return c.json(result);
      } else {
        return c.json({ error: 'No analysis result available' }, 404);
      }
    });

    // 健康检查
    this.app.get('/health', (c: Context) => {
      return c.json({ status: 'ok', timestamp: new Date().toISOString() });
    });

    // 添加全局错误处理中间件
    this.app.onError((err, c) => {
      console.error('UIServer error:', err);
      return c.json({ error: 'Internal Server Error' }, 500);
    });

    // 404 处理
    this.app.notFound((c) => {
      return c.json({ error: 'Not Found' }, 404);
    });
  }

  private hasResults(): boolean {
    try {
      return require('fs').existsSync(this.getResultPath());
    } catch {
      return false;
    }
  }

  private getResultPath(): string {
    return join(process.cwd(), '.cognitive-complexity-result.json');
  }

  private getStoredResult(): AnalysisResult | null {
    try {
      const resultPath = this.getResultPath();
      if (require('fs').existsSync(resultPath)) {
        const content = require('fs').readFileSync(resultPath, 'utf-8');
        return JSON.parse(content);
      }
    } catch (error) {
      console.warn('Failed to read stored result:', error);
    }
    return null;
  }

  public async storeResult(result: AnalysisResult): Promise<void> {
    try {
      const resultPath = this.getResultPath();
      await fs.writeFile(resultPath, JSON.stringify(result, null, 2), 'utf-8');
    } catch (error) {
      console.warn('Failed to store result:', error);
    }
  }

  public async start(): Promise<{ url: string; port: number }> {
    try {
      // 查找可用端口
      const targetPort = this.options.port === 0 ? 3000 : this.options.port!;
      const port = await this.findAvailablePort(targetPort);
      this.currentPort = port;
      
      // 创建服务器
      this.server = serve({
        fetch: this.app.fetch,
        port: port,
        hostname: this.options.host
      });

      const url = `http://${this.options.host}:${port}`;
      console.log(`\n🌐 Web UI started at: ${url}`);
      console.log(`📊 Analysis report will be available at: ${url}/report`);
      
      // 自动打开浏览器
      if (this.options.openBrowser) {
        await this.openBrowser(url);
      }
      
      return { url, port };
    } catch (error) {
      throw new Error(`Failed to start UIServer: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async stop(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.server) {
        try {
          this.server.close((error) => {
            if (error) {
              console.warn('Error stopping UIServer:', error);
              reject(new Error(`Failed to stop UIServer: ${error.message}`));
            } else {
              console.log('🛑 Web UI server stopped');
              this.server = null;
              resolve();
            }
          });
        } catch (error) {
          console.warn('Error during server shutdown:', error);
          this.server = null;
          reject(new Error(`Failed to stop UIServer: ${error instanceof Error ? error.message : String(error)}`));
        }
      } else {
        console.log('🛑 Web UI server already stopped');
        resolve();
      }
    });
  }

  private async findAvailablePort(startPort: number): Promise<number> {
    const isPortAvailable = (port: number): Promise<boolean> => {
      return new Promise((resolve) => {
        const server = createServer();
        server.listen(port, this.options.host, () => {
          server.once('close', () => resolve(true));
          server.close();
        });
        server.on('error', () => resolve(false));
      });
    };

    for (let port = startPort; port < startPort + 10; port++) {
      if (await isPortAvailable(port)) {
        return port;
      }
    }
    
    throw new Error(`No available ports found starting from ${startPort}`);
  }

  private async openBrowser(url: string): Promise<void> {
    try {
      await open(url);
    } catch (error) {
      console.warn('Could not automatically open browser. Please visit:', url);
    }
  }

  public async cleanupResult(): Promise<void> {
    try {
      const resultPath = this.getResultPath();
      if (require('fs').existsSync(resultPath)) {
        await fs.unlink(resultPath);
      }
    } catch (error) {
      // 忽略清理错误
    }
  }
}