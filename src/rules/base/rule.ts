/**
 * 基础规则类 - 提供通用的规则实现功能
 */

import type { Node } from '@swc/core';
import type { Rule, RuleResult, AnalysisContext } from '../../engine/types';

/**
 * 高性能基础规则抽象类
 * 提供缓存、性能监控等通用功能
 */
export abstract class BaseRule implements Rule {
  abstract readonly id: string;
  abstract readonly name: string;
  abstract readonly priority: number;

  // 快速节点类型检查
  abstract canHandle(node: Node): boolean;

  // 异步规则评估
  abstract evaluate(node: Node, context: AnalysisContext): Promise<RuleResult>;

  // 依赖声明
  getDependencies(): string[] {
    return [];
  }

  // 生命周期钩子（可选）
  async onLoad?(): Promise<void>;
  async onUnload?(): Promise<void>;

  /**
   * 生成缓存键
   */
  protected generateCacheKey(node: Node, context: AnalysisContext): string {
    return `${this.id}:${node.type}:${this.getNodeHash(node)}:${context.nestingLevel}`;
  }

  /**
   * 获取节点哈希值
   */
  protected getNodeHash(node: Node): string {
    // 简化版本：基于节点类型和位置生成哈希
    const span = (node as any).span;
    const spanData = span ? `${span.start}-${span.end}` : '0-0';
    return `${node.type}:${spanData}`;
  }

  /**
   * 带缓存的规则评估
   */
  protected async evaluateWithCache(
    node: Node,
    context: AnalysisContext,
    evaluator: () => Promise<RuleResult>
  ): Promise<RuleResult> {
    const startTime = performance.now();
    const nodeHash = this.getNodeHash(node);
    
    try {
      // 尝试从缓存获取结果
      const cached = await context.cache.getCachedRuleResult?.(this.id, nodeHash);
      
      if (cached) {
        return {
          ...cached,
          cacheHit: true,
          executionTime: performance.now() - startTime
        };
      }

      // 执行规则评估
      const result = await evaluator();
      
      // 缓存结果
      if (context.cache.setCachedRuleResult) {
        await context.cache.setCachedRuleResult(this.id, nodeHash, {
          ...result,
          cacheHit: false
        });
      }

      return {
        ...result,
        cacheHit: false,
        executionTime: performance.now() - startTime
      };
    } catch (error) {
      // 错误处理：创建错误结果
      return this.createErrorResult(node, error, performance.now() - startTime);
    }
  }

  /**
   * 创建非豁免结果
   */
  protected createNonExemptionResult(complexity: number = 0, reason: string = 'No exemption applied'): RuleResult {
    return {
      ruleId: this.id,
      complexity,
      isExempted: false,
      shouldIncreaseNesting: false,
      reason,
      suggestions: [],
      metadata: { nodeType: 'unknown' },
      executionTime: 0,
      cacheHit: false
    };
  }

  /**
   * 创建豁免结果
   */
  protected createExemptionResult(reason: string, metadata: any = {}): RuleResult {
    return {
      ruleId: this.id,
      complexity: 0,
      isExempted: true,
      shouldIncreaseNesting: false,
      reason,
      suggestions: [],
      metadata: {
        exemptionType: 'rule-based',
        ...metadata
      },
      executionTime: 0,
      cacheHit: false
    };
  }

  /**
   * 创建复杂度结果
   */
  protected createComplexityResult(
    complexity: number,
    shouldIncreaseNesting: boolean,
    reason: string,
    metadata: any = {}
  ): RuleResult {
    return {
      ruleId: this.id,
      complexity,
      isExempted: false,
      shouldIncreaseNesting,
      reason,
      suggestions: [],
      metadata,
      executionTime: 0,
      cacheHit: false
    };
  }

  /**
   * 创建错误结果
   */
  protected createErrorResult(node: Node, error: any, executionTime: number): RuleResult {
    return {
      ruleId: this.id,
      complexity: 0,
      isExempted: true, // 错误时豁免复杂度
      shouldIncreaseNesting: false,
      reason: `Rule execution error: ${error.message || error}`,
      suggestions: [],
      metadata: {
        nodeType: node.type,
        error: true,
        errorType: error.name || 'UnknownError'
      },
      executionTime,
      cacheHit: false
    };
  }

  /**
   * 检查是否为JSX相关节点
   */
  protected isJSXNode(node: Node): boolean {
    return node.type.startsWith('JSX');
  }

  /**
   * 检查是否为JSX结构节点
   */
  protected isJSXStructuralNode(node: Node): boolean {
    const structuralTypes = [
      'JSXElement',
      'JSXFragment',
      'JSXAttribute',
      'JSXText',
      'JSXExpressionContainer',
      'JSXSpreadAttribute',
      'JSXOpeningElement',
      'JSXClosingElement',
      'JSXSpreadChild'
    ];
    
    return structuralTypes.includes(node.type);
  }

  /**
   * 检查是否为表达式节点
   */
  protected isExpressionNode(node: Node): boolean {
    const expressionTypes = [
      'ConditionalExpression',
      'LogicalExpression',
      'BinaryExpression',
      'CallExpression',
      'MemberExpression',
      'ArrowFunctionExpression',
      'FunctionExpression'
    ];
    
    return expressionTypes.includes(node.type);
  }

  /**
   * 记录调试信息
   */
  protected debug(message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[${this.id}] ${message}`, data || '');
    }
  }
}