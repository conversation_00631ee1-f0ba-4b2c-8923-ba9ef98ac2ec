/**
 * 条件表达式复杂度规则实现
 * 处理三元运算符（? :）的复杂度计算
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * 条件表达式复杂度规则
 * 统一处理三元运算符（? :）的复杂度计算
 */
export class ConditionalExpressionRule extends BaseRule {
  readonly id = 'conditional-expression';
  readonly name = 'Conditional Expression Complexity';
  readonly priority = 400; // 中等优先级

  canHandle(node: Node): boolean {
    return node.type === 'ConditionalExpression';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const conditionalNode = node as any;
      
      // 基础复杂度：每个三元运算符都增加1点复杂度
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateConditionalReason(conditionalNode, baseComplexity, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateConditionalSuggestions(conditionalNode, finalComplexity, context);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        false, // 三元运算符不增加嵌套层级，它不创建作用域
        suggestions,
        {
          nodeType: node.type,
          baseComplexity,
          nestingLevel: context.nestingLevel,
          hasComplexCondition: this.hasComplexCondition(conditionalNode),
          hasNestedConditionals: this.hasNestedConditionals(conditionalNode),
          conditionComplexity: this.calculateConditionComplexity(conditionalNode),
        }
      );
    });
  }

  /**
   * 检查是否有复杂的条件表达式
   */
  private hasComplexCondition(conditionalNode: any): boolean {
    const test = conditionalNode.test;
    if (!test) {
      return false;
    }

    // 检查是否包含逻辑运算符
    if (this.containsLogicalOperators(test)) {
      return true;
    }

    // 检查是否包含函数调用
    if (this.containsFunctionCalls(test)) {
      return true;
    }

    // 检查是否包含复杂的比较或计算
    if (this.containsComplexExpressions(test)) {
      return true;
    }

    return false;
  }

  /**
   * 检查是否包含嵌套的三元运算符
   */
  private hasNestedConditionals(conditionalNode: any): boolean {
    const { consequent, alternate } = conditionalNode;
    
    // 检查 consequent 分支是否包含三元运算符
    if (this.containsConditionalExpressions(consequent)) {
      return true;
    }
    
    // 检查 alternate 分支是否包含三元运算符
    if (this.containsConditionalExpressions(alternate)) {
      return true;
    }
    
    return false;
  }

  /**
   * 计算条件的复杂度分数
   */
  private calculateConditionComplexity(conditionalNode: any): number {
    const test = conditionalNode.test;
    if (!test) {
      return 0;
    }

    let complexity = 0;

    // 逻辑运算符增加复杂度
    complexity += this.countLogicalOperators(test);

    // 函数调用增加复杂度
    complexity += this.countFunctionCalls(test);

    // 复杂表达式增加复杂度
    if (this.containsComplexExpressions(test)) {
      complexity += 1;
    }

    return complexity;
  }

  /**
   * 检查节点是否包含逻辑运算符
   */
  private containsLogicalOperators(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查当前节点
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') &&
        ['&&', '||'].includes(node.operator)) {
      return true;
    }

    // 递归检查子节点
    return this.traverseAndCheck(node, this.containsLogicalOperators.bind(this));
  }

  /**
   * 计算逻辑运算符的数量
   */
  private countLogicalOperators(node: any): number {
    if (!node || typeof node !== 'object') {
      return 0;
    }

    let count = 0;

    // 检查当前节点
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') &&
        ['&&', '||'].includes(node.operator)) {
      count += 1;
    }

    // 递归计算子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        count += value.reduce((sum, child) => sum + this.countLogicalOperators(child), 0);
      } else if (value && typeof value === 'object') {
        count += this.countLogicalOperators(value);
      }
    }

    return count;
  }

  /**
   * 检查节点是否包含函数调用
   */
  private containsFunctionCalls(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    if (node.type === 'CallExpression') {
      return true;
    }

    return this.traverseAndCheck(node, this.containsFunctionCalls.bind(this));
  }

  /**
   * 计算函数调用的数量
   */
  private countFunctionCalls(node: any): number {
    if (!node || typeof node !== 'object') {
      return 0;
    }

    let count = 0;

    if (node.type === 'CallExpression') {
      count += 1;
    }

    // 递归计算子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        count += value.reduce((sum, child) => sum + this.countFunctionCalls(child), 0);
      } else if (value && typeof value === 'object') {
        count += this.countFunctionCalls(value);
      }
    }

    return count;
  }

  /**
   * 检查节点是否包含复杂表达式
   */
  private containsComplexExpressions(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查是否包含成员表达式链
    if (node.type === 'MemberExpression' && this.isMemberExpressionChain(node)) {
      return true;
    }

    // 检查是否包含复杂的二元表达式
    if (node.type === 'BinaryExpression' && this.isComplexBinaryExpression(node)) {
      return true;
    }

    // 检查是否包含数组或对象字面量
    if (node.type === 'ArrayExpression' || node.type === 'ObjectExpression') {
      return true;
    }

    return this.traverseAndCheck(node, this.containsComplexExpressions.bind(this));
  }

  /**
   * 检查节点是否包含三元运算符
   */
  private containsConditionalExpressions(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    if (node.type === 'ConditionalExpression') {
      return true;
    }

    return this.traverseAndCheck(node, this.containsConditionalExpressions.bind(this));
  }

  /**
   * 通用的节点遍历和检查方法
   */
  private traverseAndCheck(node: any, checkFunction: (node: any) => boolean): boolean {
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(child => checkFunction(child))) {
          return true;
        }
      } else if (value && typeof value === 'object') {
        if (checkFunction(value)) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * 检查是否为成员表达式链
   */
  private isMemberExpressionChain(node: any): boolean {
    if (node.type !== 'MemberExpression') {
      return false;
    }

    let depth = 0;
    let current = node;

    while (current && current.type === 'MemberExpression') {
      depth++;
      current = current.object;
    }

    return depth > 2; // 超过2层的成员访问认为是复杂的
  }

  /**
   * 检查是否为复杂的二元表达式
   */
  private isComplexBinaryExpression(node: any): boolean {
    if (node.type !== 'BinaryExpression') {
      return false;
    }

    // 检查是否包含复杂的操作符
    const complexOperators = ['instanceof', 'in'];
    if (complexOperators.includes(node.operator)) {
      return true;
    }

    // 检查操作数是否复杂
    return this.containsComplexExpressions(node.left) || 
           this.containsComplexExpressions(node.right);
  }

  /**
   * 生成条件表达式的复杂度说明
   */
  private generateConditionalReason(
    conditionalNode: any,
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext
  ): string {
    const nestingPenalty = finalComplexity - baseComplexity;
    
    let reason = `Ternary operator increases cognitive complexity by ${finalComplexity}`;
    
    if (nestingPenalty > 0) {
      reason += ` (nesting penalty: +${nestingPenalty})`;
    }

    // 添加额外的复杂度信息
    if (this.hasComplexCondition(conditionalNode)) {
      const conditionComplexity = this.calculateConditionComplexity(conditionalNode);
      reason += ` (complex condition detected, complexity: ${conditionComplexity})`;
    }

    // 嵌套三元运算符特殊说明
    if (this.hasNestedConditionals(conditionalNode)) {
      reason += ` (contains nested ternary operators)`;
    }
    
    return reason;
  }

  /**
   * 生成条件表达式特定的建议
   */
  private generateConditionalSuggestions(
    conditionalNode: any,
    complexity: number,
    context: AnalysisContext
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    
    // 基础复杂度建议
    const basicSuggestions = this.generateSuggestions('ternary operator', complexity);
    suggestions.push(...basicSuggestions);
    
    // 嵌套建议
    const nestingSuggestions = this.generateNestingSuggestions('ternary operator', context.nestingLevel);
    suggestions.push(...nestingSuggestions);
    
    // 复杂条件建议
    if (this.hasComplexCondition(conditionalNode)) {
      const conditionComplexity = this.calculateConditionComplexity(conditionalNode);
      
      suggestions.push({
        type: 'refactor',
        message: `Complex ternary condition detected (complexity: ${conditionComplexity}). Consider extracting condition logic into a variable or function.`,
        codeExample: `// Instead of complex condition:\nconst result = isValid(data) && data.length > 0 && !stopped ? processData(data) : defaultValue;\n\n// Extract to variable:\nconst shouldProcess = isValid(data) && data.length > 0 && !stopped;\nconst result = shouldProcess ? processData(data) : defaultValue;\n\n// Or extract to function:\nfunction shouldProcessData(data, stopped) {\n  return isValid(data) && data.length > 0 && !stopped;\n}\nconst result = shouldProcessData(data, stopped) ? processData(data) : defaultValue;`,
        priority: conditionComplexity > 2 ? 'high' : 'medium',
      });
    }

    // 嵌套三元运算符建议
    if (this.hasNestedConditionals(conditionalNode)) {
      suggestions.push({
        type: 'refactor',
        message: 'Nested ternary operators detected. Consider using if-else statements or extracting logic into functions for better readability.',
        codeExample: `// Instead of nested ternary:\nconst result = condition1 ? (condition2 ? value1 : value2) : (condition3 ? value3 : value4);\n\n// Use if-else statements:\nlet result;\nif (condition1) {\n  result = condition2 ? value1 : value2;\n} else {\n  result = condition3 ? value3 : value4;\n}\n\n// Or extract to function:\nfunction determineResult() {\n  if (condition1) {\n    return condition2 ? value1 : value2;\n  }\n  return condition3 ? value3 : value4;\n}\nconst result = determineResult();`,
        priority: 'high',
      });
    }

    // 函数调用在条件中的建议
    if (this.countFunctionCalls(conditionalNode.test) > 1) {
      suggestions.push({
        type: 'optimize',
        message: 'Multiple function calls in ternary condition. Consider caching results or extracting condition logic.',
        codeExample: `// Instead of multiple calls:\nconst result = isValid(item) && hasPermission(user) ? processItem(item) : defaultValue;\n\n// Cache results:\nconst canProcess = isValid(item) && hasPermission(user);\nconst result = canProcess ? processItem(item) : defaultValue;`,
        priority: 'medium',
      });
    }

    // 深度嵌套建议
    if (context.nestingLevel > 2) {
      suggestions.push({
        type: 'refactor',
        message: 'Consider extracting nested ternary logic into separate functions to reduce cognitive complexity.',
        codeExample: `// Instead of deeply nested ternary:\nfunction complexFunction() {\n  if (outerCondition) {\n    return innerCondition ? (deepCondition ? value1 : value2) : value3;\n  }\n  return value4;\n}\n\n// Extract to function:\nfunction getInnerValue() {\n  return deepCondition ? value1 : value2;\n}\n\nfunction complexFunction() {\n  if (outerCondition) {\n    return innerCondition ? getInnerValue() : value3;\n  }\n  return value4;\n}`,
        priority: 'medium',
      });
    }

    // 简化建议
    if (complexity === 1 && context.nestingLevel === 0) {
      suggestions.push({
        type: 'info',
        message: 'Simple ternary operator. Consider if an if-else statement would be more readable for complex logic.',
        codeExample: `// Ternary is good for simple cases:\nconst status = isActive ? 'active' : 'inactive';\n\n// For complex logic, consider if-else:\nif (hasComplexCondition && requiresValidation) {\n  return performComplexOperation();\n} else {\n  return getDefaultValue();\n}`,
        priority: 'low',
      });
    }
    
    return suggestions;
  }
}