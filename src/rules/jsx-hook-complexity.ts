/**
 * JSX Hook复杂度分析规则
 * 分析useEffect、useCallback等Hook的复杂度贡献
 */

import type { Node } from '@swc/core';
import type { Rule, RuleResult, AnalysisContext, Suggestion } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * Hook复杂度分析接口
 */
interface HookAnalysis {
  hookType: string;
  complexity: number;
  hasComplexDependencies: boolean;
  hasNestedLogic: boolean;
  dependencyCount: number;
  description: string;
  suggestions: Suggestion[];
}

/**
 * Hook调用模式
 */
interface HookCallPattern {
  name: string;
  args: any[];
  isEffect: boolean;
  isCallback: boolean;
  isMemo: boolean;
  isCustomHook: boolean;
}

/**
 * JSX Hook复杂度分析规则
 * 专门分析React Hook的复杂度贡献
 */
export class JSXHookComplexityRule extends BaseRule implements Rule {
  readonly id = 'jsx.hook.complexity';
  readonly name = 'JSX Hook Complexity Analysis';
  readonly priority = 700; // 中高优先级

  canHandle(node: Node): boolean {
    return node.type === 'CallExpression' && this.isHookCall(node as any);
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const hookCall = node as any;
      const pattern = this.analyzeHookCallPattern(hookCall);
      
      if (!pattern) {
        return this.createNonExemptionResult(node, 'Not a valid hook call');
      }

      const analysis = await this.analyzeHookComplexity(hookCall, pattern, context);
      
      if (analysis.complexity === 0) {
        return this.createExemptionResult(
          node,
          `Simple ${analysis.hookType} hook with no complexity contribution`,
          { hookType: analysis.hookType, dependencyCount: analysis.dependencyCount }
        );
      }

      return this.createComplexityResult(
        node,
        analysis.complexity,
        analysis.description,
        analysis.hasNestedLogic, // 嵌套逻辑需要增加嵌套层级
        analysis.suggestions,
        {
          hookType: analysis.hookType,
          dependencyCount: analysis.dependencyCount,
          hasComplexDependencies: analysis.hasComplexDependencies,
          hasNestedLogic: analysis.hasNestedLogic,
        }
      );
    });
  }

  /**
   * 检查是否为Hook调用
   */
  private isHookCall(node: any): boolean {
    if (node.type !== 'CallExpression' || !node.callee) {
      return false;
    }

    const callee = node.callee;
    
    // 检查直接的Hook调用: useEffect, useState等
    if (callee.type === 'Identifier') {
      const name = callee.value || callee.name;
      return typeof name === 'string' && this.isKnownHookName(name);
    }

    // 检查成员表达式Hook调用: React.useEffect等
    if (callee.type === 'MemberExpression') {
      const property = callee.property;
      if (property && property.type === 'Identifier') {
        const name = property.value || property.name;
        return typeof name === 'string' && this.isKnownHookName(name);
      }
    }

    return false;
  }

  /**
   * 检查是否为已知的Hook名称
   */
  private isKnownHookName(name: string): boolean {
    // React内置Hook
    const builtinHooks = [
      'useState', 'useEffect', 'useContext', 'useReducer',
      'useCallback', 'useMemo', 'useRef', 'useImperativeHandle',
      'useLayoutEffect', 'useDebugValue', 'useDeferredValue',
      'useTransition', 'useId', 'useSyncExternalStore',
      'useInsertionEffect'
    ];

    // 检查内置Hook
    if (builtinHooks.includes(name)) {
      return true;
    }

    // 检查自定义Hook（以use开头）
    return name.startsWith('use') && name.length > 3 && 
           !!name[3] && name[3] === name[3].toUpperCase();
  }

  /**
   * 分析Hook调用模式
   */
  private analyzeHookCallPattern(hookCall: any): HookCallPattern | null {
    const callee = hookCall.callee;
    if (!callee) {
      return null;
    }
    
    let hookName: string;

    if (callee.type === 'Identifier') {
      hookName = callee.value || callee.name;
    } else if (callee.type === 'MemberExpression' && callee.property) {
      hookName = callee.property.value || callee.property.name;
    } else {
      return null;
    }

    if (!this.isKnownHookName(hookName)) {
      return null;
    }

    const args = hookCall.arguments || [];

    return {
      name: hookName,
      args,
      isEffect: this.isEffectHook(hookName),
      isCallback: this.isCallbackHook(hookName),
      isMemo: this.isMemoHook(hookName),
      isCustomHook: this.isCustomHook(hookName),
    };
  }

  /**
   * 分析Hook复杂度
   */
  private async analyzeHookComplexity(
    hookCall: any,
    pattern: HookCallPattern,
    context: AnalysisContext
  ): Promise<HookAnalysis> {
    const baseAnalysis: HookAnalysis = {
      hookType: pattern.name,
      complexity: 0,
      hasComplexDependencies: false,
      hasNestedLogic: false,
      dependencyCount: 0,
      description: `${pattern.name} hook analysis`,
      suggestions: [],
    };

    // 分析不同类型的Hook
    if (pattern.isEffect) {
      return this.analyzeEffectHook(hookCall, pattern, context, baseAnalysis);
    } else if (pattern.isCallback) {
      return this.analyzeCallbackHook(hookCall, pattern, context, baseAnalysis);
    } else if (pattern.isMemo) {
      return this.analyzeMemoHook(hookCall, pattern, context, baseAnalysis);
    } else if (pattern.isCustomHook) {
      return this.analyzeCustomHook(hookCall, pattern, context, baseAnalysis);
    } else {
      return this.analyzeStateHook(hookCall, pattern, context, baseAnalysis);
    }
  }

  /**
   * 分析Effect Hook (useEffect, useLayoutEffect等)
   */
  private async analyzeEffectHook(
    hookCall: any,
    pattern: HookCallPattern,
    context: AnalysisContext,
    baseAnalysis: HookAnalysis
  ): Promise<HookAnalysis> {
    const args = pattern.args;
    let complexity = 0;
    const suggestions: Suggestion[] = [];

    // 分析Effect函数的复杂度
    if (args.length > 0 && args[0]) {
      const effectFunction = args[0];
      const functionComplexity = await this.analyzeFunctionComplexity(effectFunction, context);
      complexity += functionComplexity.complexity;
      
      if (functionComplexity.hasNestedLogic) {
        baseAnalysis.hasNestedLogic = true;
      }

      if (functionComplexity.complexity > 3) {
        suggestions.push({
          type: 'refactor',
          message: `Consider extracting complex logic from ${pattern.name} into separate functions`,
          priority: 'medium',
        });
      }
    }

    // 分析依赖数组
    if (args.length > 1 && args[1]) {
      const dependencyArray = args[1];
      const depAnalysis = this.analyzeDependencyArray(dependencyArray);
      
      baseAnalysis.dependencyCount = depAnalysis.count;
      baseAnalysis.hasComplexDependencies = depAnalysis.hasComplexDependencies;
      
      // 复杂依赖增加复杂度
      if (depAnalysis.hasComplexDependencies) {
        complexity += 1;
        suggestions.push({
          type: 'optimize',
          message: `Complex dependencies detected in ${pattern.name}. Consider simplifying.`,
          priority: 'low',
        });
      }

      // 过多依赖增加复杂度
      if (depAnalysis.count > 5) {
        complexity += 1;
        suggestions.push({
          type: 'refactor',
          message: `Too many dependencies (${depAnalysis.count}) in ${pattern.name}. Consider splitting the effect.`,
          priority: 'medium',
        });
      }
    } else {
      // 缺少依赖数组时增加复杂度（可能导致性能问题）
      complexity += 1;
      suggestions.push({
        type: 'warning',
        message: `${pattern.name} is missing dependency array. This may cause performance issues.`,
        priority: 'high',
      });
    }

    return {
      ...baseAnalysis,
      complexity,
      description: `${pattern.name} with ${baseAnalysis.dependencyCount} dependencies${baseAnalysis.hasComplexDependencies ? ' (complex)' : ''}`,
      suggestions,
    };
  }

  /**
   * 分析Callback Hook (useCallback)
   */
  private async analyzeCallbackHook(
    hookCall: any,
    pattern: HookCallPattern,
    context: AnalysisContext,
    baseAnalysis: HookAnalysis
  ): Promise<HookAnalysis> {
    const args = pattern.args;
    let complexity = 0;
    const suggestions: Suggestion[] = [];

    // 分析回调函数的复杂度
    if (args.length > 0 && args[0]) {
      const callbackFunction = args[0];
      const functionComplexity = await this.analyzeFunctionComplexity(callbackFunction, context);
      
      // useCallback中的复杂逻辑需要计算复杂度
      complexity += functionComplexity.complexity;
      
      if (functionComplexity.hasNestedLogic) {
        baseAnalysis.hasNestedLogic = true;
      }

      if (functionComplexity.complexity > 2) {
        suggestions.push({
          type: 'refactor',
          message: 'Consider extracting complex logic from useCallback into separate functions',
          priority: 'medium',
        });
      }
    }

    // 分析依赖数组
    if (args.length > 1 && args[1]) {
      const dependencyArray = args[1];
      const depAnalysis = this.analyzeDependencyArray(dependencyArray);
      
      baseAnalysis.dependencyCount = depAnalysis.count;
      baseAnalysis.hasComplexDependencies = depAnalysis.hasComplexDependencies;
      
      if (depAnalysis.hasComplexDependencies) {
        complexity += 1;
        suggestions.push({
          type: 'optimize',
          message: 'Complex dependencies in useCallback may cause frequent re-renders',
          priority: 'low',
        });
      }
    }

    return {
      ...baseAnalysis,
      complexity,
      description: `useCallback with ${baseAnalysis.dependencyCount} dependencies${baseAnalysis.hasComplexDependencies ? ' (complex)' : ''}`,
      suggestions,
    };
  }

  /**
   * 分析Memo Hook (useMemo)
   */
  private async analyzeMemoHook(
    hookCall: any,
    pattern: HookCallPattern,
    context: AnalysisContext,
    baseAnalysis: HookAnalysis
  ): Promise<HookAnalysis> {
    const args = pattern.args;
    let complexity = 0;
    const suggestions: Suggestion[] = [];

    // 分析计算函数的复杂度
    if (args.length > 0 && args[0]) {
      const computeFunction = args[0];
      const functionComplexity = await this.analyzeFunctionComplexity(computeFunction, context);
      
      // useMemo中的复杂计算逻辑需要计算复杂度
      complexity += functionComplexity.complexity;
      
      if (functionComplexity.hasNestedLogic) {
        baseAnalysis.hasNestedLogic = true;
      }

      if (functionComplexity.complexity > 2) {
        suggestions.push({
          type: 'optimize',
          message: 'Complex computation in useMemo detected. Ensure memoization is beneficial.',
          priority: 'low',
        });
      }
    }

    // 分析依赖数组
    if (args.length > 1 && args[1]) {
      const dependencyArray = args[1];
      const depAnalysis = this.analyzeDependencyArray(dependencyArray);
      
      baseAnalysis.dependencyCount = depAnalysis.count;
      baseAnalysis.hasComplexDependencies = depAnalysis.hasComplexDependencies;
      
      if (depAnalysis.hasComplexDependencies) {
        complexity += 1;
        suggestions.push({
          type: 'optimize',
          message: 'Complex dependencies in useMemo may reduce effectiveness',
          priority: 'low',
        });
      }
    }

    return {
      ...baseAnalysis,
      complexity,
      description: `useMemo with ${baseAnalysis.dependencyCount} dependencies${baseAnalysis.hasComplexDependencies ? ' (complex)' : ''}`,
      suggestions,
    };
  }

  /**
   * 分析自定义Hook
   */
  private async analyzeCustomHook(
    hookCall: any,
    pattern: HookCallPattern,
    context: AnalysisContext,
    baseAnalysis: HookAnalysis
  ): Promise<HookAnalysis> {
    // 自定义Hook通常有一定的抽象复杂度
    let complexity = 1;
    const suggestions: Suggestion[] = [];

    // 分析传入的参数复杂度
    const args = pattern.args;
    if (args.length > 0) {
      let paramComplexity = 0;
      for (const arg of args) {
        if (this.isComplexArgument(arg)) {
          paramComplexity += 1;
        }
      }
      
      complexity += paramComplexity;
      
      if (paramComplexity > 2) {
        suggestions.push({
          type: 'refactor',
          message: `Custom hook ${pattern.name} has complex parameters. Consider simplifying.`,
          priority: 'low',
        });
      }
    }

    return {
      ...baseAnalysis,
      complexity,
      description: `Custom hook ${pattern.name} with abstraction complexity`,
      suggestions,
    };
  }

  /**
   * 分析状态Hook (useState, useReducer等)
   */
  private async analyzeStateHook(
    hookCall: any,
    pattern: HookCallPattern,
    context: AnalysisContext,
    baseAnalysis: HookAnalysis
  ): Promise<HookAnalysis> {
    // 状态Hook通常不直接贡献复杂度，除非有复杂的初始化逻辑
    let complexity = 0;
    const suggestions: Suggestion[] = [];

    const args = pattern.args;
    if (args.length > 0 && args[0]) {
      const initializer = args[0];
      
      // 检查是否有复杂的初始化逻辑
      if (this.hasComplexInitialization(initializer)) {
        complexity += 1;
        suggestions.push({
          type: 'optimize',
          message: `Complex initialization in ${pattern.name}. Consider lazy initialization.`,
          priority: 'low',
        });
      }
    }

    return {
      ...baseAnalysis,
      complexity,
      description: complexity > 0 
        ? `${pattern.name} with complex initialization`
        : `Simple ${pattern.name} hook`,
      suggestions,
    };
  }

  /**
   * 分析函数复杂度
   */
  private async analyzeFunctionComplexity(
    func: any,
    context: AnalysisContext
  ): Promise<{ complexity: number; hasNestedLogic: boolean }> {
    if (!func || !func.body) {
      return { complexity: 0, hasNestedLogic: false };
    }

    let complexity = 0;
    let hasNestedLogic = false;

    // 简化版本：基于函数体中的控制结构计算复杂度
    const controlStructures = this.findControlStructures(func.body);
    
    for (const structure of controlStructures) {
      switch (structure.type) {
        case 'IfStatement':
        case 'ConditionalExpression':
          complexity += 1;
          hasNestedLogic = true;
          break;
        case 'ForStatement':
        case 'WhileStatement':
        case 'DoWhileStatement':
          complexity += 1;
          hasNestedLogic = true;
          break;
        case 'SwitchStatement':
          complexity += 1;
          hasNestedLogic = true;
          break;
        case 'LogicalExpression':
          complexity += 1;
          break;
        case 'TryStatement':
          complexity += 1;
          hasNestedLogic = true;
          break;
      }
    }

    return { complexity, hasNestedLogic };
  }

  /**
   * 查找控制结构
   */
  private findControlStructures(body: any): Array<{ type: string; node: any }> {
    const structures: Array<{ type: string; node: any }> = [];
    
    if (!body || typeof body !== 'object') {
      return structures;
    }

    const traverse = (node: any) => {
      if (!node || typeof node !== 'object') {
        return;
      }

      const controlTypes = [
        'IfStatement', 'ConditionalExpression', 'ForStatement',
        'WhileStatement', 'DoWhileStatement', 'SwitchStatement',
        'LogicalExpression', 'TryStatement'
      ];

      if (controlTypes.includes(node.type)) {
        structures.push({ type: node.type, node });
      }

      // 递归遍历子节点
      for (const key in node) {
        if (key === 'span' || key === 'type') continue;
        const value = node[key];
        
        if (Array.isArray(value)) {
          value.forEach(traverse);
        } else if (value && typeof value === 'object') {
          traverse(value);
        }
      }
    };

    traverse(body);
    return structures;
  }

  /**
   * 分析依赖数组
   */
  private analyzeDependencyArray(depArray: any): {
    count: number;
    hasComplexDependencies: boolean;
  } {
    if (!depArray || depArray.type !== 'ArrayExpression') {
      return { count: 0, hasComplexDependencies: false };
    }

    const elements = depArray.elements || [];
    let hasComplexDependencies = false;

    for (const element of elements) {
      if (this.isComplexDependency(element)) {
        hasComplexDependencies = true;
        break;
      }
    }

    return {
      count: elements.length,
      hasComplexDependencies,
    };
  }

  /**
   * 检查是否为复杂依赖
   */
  private isComplexDependency(dependency: any): boolean {
    if (!dependency) return false;

    // 复杂依赖的判断标准
    return (
      dependency.type === 'CallExpression' || // 函数调用
      dependency.type === 'ConditionalExpression' || // 三元运算符
      dependency.type === 'LogicalExpression' || // 逻辑表达式
      dependency.type === 'BinaryExpression' || // 二元表达式
      (dependency.type === 'MemberExpression' && 
       this.getMemberExpressionDepth(dependency) > 2) // 深层属性访问
    );
  }

  /**
   * 获取成员表达式深度
   */
  private getMemberExpressionDepth(memberExpr: any): number {
    let depth = 0;
    let current = memberExpr;

    while (current && current.type === 'MemberExpression') {
      depth++;
      current = current.object;
    }

    return depth;
  }

  /**
   * 检查是否为复杂参数
   */
  private isComplexArgument(arg: any): boolean {
    if (!arg) return false;

    return (
      arg.type === 'ObjectExpression' || // 对象字面量
      arg.type === 'ArrayExpression' || // 数组字面量
      arg.type === 'ArrowFunctionExpression' || // 箭头函数
      arg.type === 'FunctionExpression' || // 函数表达式
      arg.type === 'CallExpression' || // 函数调用
      arg.type === 'ConditionalExpression' // 三元运算符
    );
  }

  /**
   * 检查是否有复杂的初始化逻辑
   */
  private hasComplexInitialization(initializer: any): boolean {
    if (!initializer) return false;

    return (
      initializer.type === 'CallExpression' || // 函数调用初始化
      initializer.type === 'ConditionalExpression' || // 条件初始化
      initializer.type === 'LogicalExpression' || // 逻辑表达式初始化
      (initializer.type === 'ObjectExpression' && 
       initializer.properties && initializer.properties.length > 3) || // 复杂对象
      (initializer.type === 'ArrayExpression' && 
       initializer.elements && initializer.elements.length > 5) // 复杂数组
    );
  }

  /**
   * Hook类型检查辅助方法
   */
  private isEffectHook(name: string): boolean {
    return ['useEffect', 'useLayoutEffect', 'useInsertionEffect'].includes(name);
  }

  private isCallbackHook(name: string): boolean {
    return name === 'useCallback';
  }

  private isMemoHook(name: string): boolean {
    return name === 'useMemo';
  }

  private isCustomHook(name: string): boolean {
    return name.startsWith('use') && name.length > 3 && 
           !!name[3] && name[3] === name[3].toUpperCase() && 
           !this.isBuiltinHook(name);
  }

  private isBuiltinHook(name: string): boolean {
    const builtinHooks = [
      'useState', 'useEffect', 'useContext', 'useReducer',
      'useCallback', 'useMemo', 'useRef', 'useImperativeHandle',
      'useLayoutEffect', 'useDebugValue', 'useDeferredValue',
      'useTransition', 'useId', 'useSyncExternalStore',
      'useInsertionEffect'
    ];
    return builtinHooks.includes(name);
  }
}