/**
 * For语句复杂度规则实现
 * 处理for、for-in、for-of循环语句的复杂度计算
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * For语句复杂度规则
 * 统一处理for、for-in、for-of循环语句的复杂度计算
 */
export class ForStatementRule extends BaseRule {
  readonly id = 'for-statement';
  readonly name = 'For Statement Complexity';
  readonly priority = 450; // 高优先级

  canHandle(node: Node): boolean {
    return node.type === 'ForStatement' ||
           node.type === 'ForInStatement' ||
           node.type === 'ForOfStatement';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const forNode = node as any;
      
      // 基础复杂度：每个循环都增加1点复杂度
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateForStatementReason(forNode, baseComplexity, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateForStatementSuggestions(forNode, finalComplexity, context);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        true, // for循环增加嵌套层级
        suggestions,
        {
          nodeType: node.type,
          baseComplexity,
          nestingLevel: context.nestingLevel,
          loopType: this.getLoopType(node.type),
          hasComplexCondition: this.hasComplexCondition(forNode),
          hasComplexUpdate: this.hasComplexUpdate(forNode),
        }
      );
    });
  }

  /**
   * 获取循环类型的描述
   */
  private getLoopType(nodeType: string): string {
    switch (nodeType) {
      case 'ForStatement':
        return 'for-loop';
      case 'ForInStatement':
        return 'for-in-loop';
      case 'ForOfStatement':
        return 'for-of-loop';
      default:
        return 'unknown-loop';
    }
  }

  /**
   * 检查是否有复杂的循环条件
   * 针对传统for循环，检查条件表达式的复杂度
   */
  private hasComplexCondition(forNode: any): boolean {
    if (forNode.type !== 'ForStatement') {
      return false; // for-in和for-of没有传统的条件表达式
    }

    const test = forNode.test;
    if (!test) {
      return false;
    }

    // 检查是否包含逻辑运算符
    if (this.containsLogicalOperators(test)) {
      return true;
    }

    // 检查是否包含复杂的比较或计算
    if (this.containsComplexExpressions(test)) {
      return true;
    }

    return false;
  }

  /**
   * 检查是否有复杂的更新表达式
   * 针对传统for循环，检查更新表达式的复杂度
   */
  private hasComplexUpdate(forNode: any): boolean {
    if (forNode.type !== 'ForStatement') {
      return false; // for-in和for-of没有更新表达式
    }

    const update = forNode.update;
    if (!update) {
      return false;
    }

    // 检查是否包含函数调用
    if (this.containsFunctionCalls(update)) {
      return true;
    }

    // 检查是否包含复杂的赋值操作
    if (this.containsComplexAssignments(update)) {
      return true;
    }

    return false;
  }

  /**
   * 检查节点是否包含逻辑运算符
   */
  private containsLogicalOperators(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查当前节点
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') &&
        ['&&', '||'].includes(node.operator)) {
      return true;
    }

    // 递归检查子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(child => this.containsLogicalOperators(child))) {
          return true;
        }
      } else if (value && typeof value === 'object') {
        if (this.containsLogicalOperators(value)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 检查节点是否包含复杂表达式
   */
  private containsComplexExpressions(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查是否包含函数调用
    if (node.type === 'CallExpression') {
      return true;
    }

    // 检查是否包含成员表达式链
    if (node.type === 'MemberExpression' && this.isMemberExpressionChain(node)) {
      return true;
    }

    // 检查是否包含复杂的二元表达式
    if (node.type === 'BinaryExpression' && this.isComplexBinaryExpression(node)) {
      return true;
    }

    // 递归检查子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(child => this.containsComplexExpressions(child))) {
          return true;
        }
      } else if (value && typeof value === 'object') {
        if (this.containsComplexExpressions(value)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 检查节点是否包含函数调用
   */
  private containsFunctionCalls(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    if (node.type === 'CallExpression') {
      return true;
    }

    // 递归检查子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(child => this.containsFunctionCalls(child))) {
          return true;
        }
      } else if (value && typeof value === 'object') {
        if (this.containsFunctionCalls(value)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 检查节点是否包含复杂的赋值操作
   */
  private containsComplexAssignments(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查复合赋值操作符
    if (node.type === 'AssignmentExpression' && 
        node.operator && node.operator !== '=') {
      return true;
    }

    // 检查多重赋值
    if (node.type === 'SequenceExpression') {
      return true;
    }

    // 递归检查子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(child => this.containsComplexAssignments(child))) {
          return true;
        }
      } else if (value && typeof value === 'object') {
        if (this.containsComplexAssignments(value)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 检查是否为成员表达式链
   */
  private isMemberExpressionChain(node: any): boolean {
    if (node.type !== 'MemberExpression') {
      return false;
    }

    let depth = 0;
    let current = node;

    while (current && current.type === 'MemberExpression') {
      depth++;
      current = current.object;
    }

    return depth > 2; // 超过2层的成员访问认为是复杂的
  }

  /**
   * 检查是否为复杂的二元表达式
   */
  private isComplexBinaryExpression(node: any): boolean {
    if (node.type !== 'BinaryExpression') {
      return false;
    }

    // 检查是否包含复杂的操作符
    const complexOperators = ['instanceof', 'in'];
    if (complexOperators.includes(node.operator)) {
      return true;
    }

    // 检查操作数是否复杂
    return this.containsComplexExpressions(node.left) || 
           this.containsComplexExpressions(node.right);
  }

  /**
   * 生成for语句的复杂度说明
   */
  private generateForStatementReason(
    forNode: any,
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext
  ): string {
    const loopType = this.getLoopTypeName(forNode.type);
    const nestingPenalty = finalComplexity - baseComplexity;
    
    let reason = `${loopType} increases cognitive complexity by ${finalComplexity}`;
    
    if (nestingPenalty > 0) {
      reason += ` (nesting penalty: +${nestingPenalty})`;
    }

    // 添加额外的复杂度信息
    if (this.hasComplexCondition(forNode)) {
      reason += ` (complex condition detected)`;
    }

    if (this.hasComplexUpdate(forNode)) {
      reason += ` (complex update expression)`;
    }
    
    return reason;
  }

  /**
   * 获取循环类型的友好名称
   */
  private getLoopTypeName(nodeType: string): string {
    switch (nodeType) {
      case 'ForStatement':
        return 'For loop';
      case 'ForInStatement':
        return 'For-in loop';
      case 'ForOfStatement':
        return 'For-of loop';
      default:
        return 'Loop';
    }
  }

  /**
   * 生成for语句特定的建议
   */
  private generateForStatementSuggestions(
    forNode: any,
    complexity: number,
    context: AnalysisContext
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    const loopType = this.getLoopTypeName(forNode.type);
    
    // 基础复杂度建议
    const basicSuggestions = this.generateSuggestions(loopType.toLowerCase(), complexity);
    suggestions.push(...basicSuggestions);
    
    // 嵌套建议
    const nestingSuggestions = this.generateNestingSuggestions(loopType.toLowerCase(), context.nestingLevel);
    suggestions.push(...nestingSuggestions);
    
    // 循环特定建议
    if (this.hasComplexCondition(forNode)) {
      suggestions.push({
        type: 'refactor',
        message: 'Complex loop condition detected. Consider extracting condition logic into a separate function.',
        codeExample: `// Instead of complex condition:\nfor (let i = 0; i < arr.length && isValid(arr[i]) && !stopped; i++) {\n  // ...\n}\n\n// Extract to function:\nfunction shouldContinueLoop(i, arr, stopped) {\n  return i < arr.length && isValid(arr[i]) && !stopped;\n}\n\nfor (let i = 0; shouldContinueLoop(i, arr, stopped); i++) {\n  // ...\n}`,
        priority: 'medium',
      });
    }

    if (this.hasComplexUpdate(forNode)) {
      suggestions.push({
        type: 'refactor',
        message: 'Complex update expression detected. Consider simplifying the loop increment logic.',
        codeExample: `// Instead of complex update:\nfor (let i = 0; i < length; i = calculateNextIndex(i, step)) {\n  // ...\n}\n\n// Simplify:\nfor (let i = 0; i < length; i += step) {\n  // ...\n}`,
        priority: 'medium',
      });
    }

    // 深度嵌套建议
    if (context.nestingLevel > 2) {
      suggestions.push({
        type: 'refactor',
        message: 'Consider extracting nested loop logic into separate functions to reduce cognitive complexity.',
        codeExample: `// Instead of deeply nested loops:\nfor (const item of items) {\n  if (condition) {\n    for (const subItem of item.children) {\n      // complex logic\n    }\n  }\n}\n\n// Extract to function:\nfunction processSubItems(item) {\n  for (const subItem of item.children) {\n    // complex logic\n  }\n}\n\nfor (const item of items) {\n  if (condition) {\n    processSubItems(item);\n  }\n}`,
        priority: 'medium',
      });
    }

    // 现代语法建议
    if (forNode.type === 'ForStatement' && this.couldUseModernSyntax(forNode)) {
      suggestions.push({
        type: 'refactor',
        message: 'Consider using modern array methods (forEach, map, filter) instead of traditional for loops when appropriate.',
        codeExample: `// Instead of traditional for loop:\nfor (let i = 0; i < array.length; i++) {\n  if (array[i].isActive) {\n    results.push(process(array[i]));\n  }\n}\n\n// Use array methods:\nconst results = array\n  .filter(item => item.isActive)\n  .map(item => process(item));`,
        priority: 'low',
      });
    }
    
    return suggestions;
  }

  /**
   * 检查是否可以使用现代语法替代
   */
  private couldUseModernSyntax(forNode: any): boolean {
    // 简单的启发式检查：如果是简单的数组遍历，建议使用现代语法
    if (forNode.type !== 'ForStatement') {
      return false;
    }

    // 检查是否是标准的数组遍历模式 (i = 0; i < array.length; i++)
    const init = forNode.init;
    const test = forNode.test;
    const update = forNode.update;

    if (!init || !test || !update) {
      return false;
    }

    // 简化的检查：如果包含length属性访问，可能适合现代语法
    return this.containsLengthProperty(test);
  }

  /**
   * 检查是否包含length属性访问
   */
  private containsLengthProperty(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    if (node.type === 'MemberExpression' && 
        node.property && 
        (node.property.value === 'length' || node.property.name === 'length')) {
      return true;
    }

    // 递归检查子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(child => this.containsLengthProperty(child))) {
          return true;
        }
      } else if (value && typeof value === 'object') {
        if (this.containsLengthProperty(value)) {
          return true;
        }
      }
    }

    return false;
  }
}