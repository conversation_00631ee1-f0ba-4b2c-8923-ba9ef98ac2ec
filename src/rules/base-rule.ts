/**
 * 基础规则抽象类
 * 提供规则实现的通用功能和性能优化
 */

import type { Node } from '@swc/core';
import type { Rule, RuleResult, AnalysisContext } from '../engine/types';
import { createHash } from 'crypto';

/**
 * 高性能基础规则类
 */
export abstract class BaseRule implements Rule {
  abstract readonly id: string;
  abstract readonly name: string;
  abstract readonly priority: number;

  // 快速节点类型检查
  abstract canHandle(node: Node): boolean;

  // 异步规则评估
  abstract evaluate(node: Node, context: AnalysisContext): Promise<RuleResult>;

  // 依赖声明
  getDependencies(): string[] {
    return [];
  }

  // 生命周期钩子（可选实现）
  async onLoad?(): Promise<void>;
  async onUnload?(): Promise<void>;

  // 缓存键生成
  protected generateCacheKey(node: Node, context: AnalysisContext): string {
    return `${this.id}:${node.type}:${this.getNodeHash(node)}:${context.nestingLevel}`;
  }

  // 性能优化辅助 - 带缓存的评估
  protected async evaluateWithCache(
    node: Node,
    context: AnalysisContext,
    evaluator: () => Promise<RuleResult>
  ): Promise<RuleResult> {
    const cacheKey = this.generateCacheKey(node, context);
    const cached = await context.cache.getCachedRuleResult(this.id, cacheKey);

    if (cached) {
      return { ...cached, cacheHit: true };
    }

    const startTime = performance.now();
    const result = await evaluator();
    const executionTime = performance.now() - startTime;

    const resultWithTiming = {
      ...result,
      executionTime,
      cacheHit: false,
    };

    await context.cache.setCachedRuleResult(this.id, cacheKey, resultWithTiming);
    return resultWithTiming;
  }

  // 应用嵌套惩罚 - 新增核心方法
  protected applyNestingPenalty(baseComplexity: number, nestingLevel: number): number {
    return baseComplexity + nestingLevel;
  }

  // 检查节点是否应该增加嵌套层级 - 新增方法
  protected shouldIncreaseNesting(node: Node): boolean {
    return this.isConditionalNode(node) || this.isLoopNode(node);
  }

  // 创建豁免结果
  protected createExemptionResult(
    node: Node,
    reason: string,
    metadata: Record<string, any> = {}
  ): RuleResult {
    return {
      ruleId: this.id,
      complexity: 0,
      isExempted: true,
      shouldIncreaseNesting: false,
      reason,
      suggestions: [],
      metadata: { nodeType: node.type, ...metadata, exempted: true },
      executionTime: 0,
      cacheHit: false,
    };
  }

  // 创建标准复杂度结果
  protected createComplexityResult(
    node: Node,
    complexity: number,
    reason: string,
    shouldIncreaseNesting: boolean = false,
    suggestions: import('../engine/types').Suggestion[] = [],
    metadata: Record<string, any> = {}
  ): RuleResult {
    return {
      ruleId: this.id,
      complexity,
      isExempted: false,
      shouldIncreaseNesting,
      reason,
      suggestions,
      metadata: { nodeType: node.type, ...metadata },
      executionTime: 0,
      cacheHit: false,
    };
  }

  // 创建复杂度结果并自动应用嵌套惩罚 - 新增便捷方法
  protected createComplexityResultWithNesting(
    node: Node,
    baseComplexity: number,
    context: AnalysisContext,
    reason: string,
    shouldIncreaseNesting: boolean = true,
    suggestions: import('../engine/types').Suggestion[] = [],
    metadata: Record<string, any> = {}
  ): RuleResult {
    const actualComplexity = shouldIncreaseNesting 
      ? this.applyNestingPenalty(baseComplexity, context.nestingLevel)
      : baseComplexity;

    return this.createComplexityResult(
      node,
      actualComplexity,
      reason,
      shouldIncreaseNesting,
      suggestions,
      {
        ...metadata,
        baseComplexity,
        nestingLevel: context.nestingLevel,
        appliedNestingPenalty: shouldIncreaseNesting
      }
    );
  }

  // 创建非豁免结果（复杂度为0但不豁免）
  protected createNonExemptionResult(
    node: Node,
    reason: string = 'Rule does not apply',
    metadata: Record<string, any> = {}
  ): RuleResult {
    return {
      ruleId: this.id,
      complexity: 0,
      isExempted: false,
      shouldIncreaseNesting: false,
      reason,
      suggestions: [],
      metadata: { nodeType: node.type, ...metadata },
      executionTime: 0,
      cacheHit: false,
    };
  }

  // 节点哈希生成
  protected getNodeHash(node: Node): string {
    const nodeData = {
      type: node.type,
      span: (node as any).span,
    };
    return createHash('sha256')
      .update(JSON.stringify(nodeData))
      .digest('hex')
      .substring(0, 12);
  }

  // 检查节点是否为JSX相关
  protected isJSXNode(node: Node): boolean {
    return node.type.startsWith('JSX');
  }

  // 检查节点是否为条件相关
  protected isConditionalNode(node: Node): boolean {
    return [
      'IfStatement',
      'ConditionalExpression',
      'SwitchStatement',
      'SwitchCase',
    ].includes(node.type);
  }

  // 检查节点是否为循环相关
  protected isLoopNode(node: Node): boolean {
    return [
      'WhileStatement',
      'DoWhileStatement',
      'ForStatement',
      'ForInStatement',
      'ForOfStatement',
    ].includes(node.type);
  }

  // 检查节点是否为逻辑表达式
  protected isLogicalNode(node: Node): boolean {
    return node.type === 'LogicalExpression';
  }

  // 获取逻辑操作符
  protected getLogicalOperator(node: any): string | null {
    if (node.type === 'LogicalExpression') {
      return node.operator || null;
    }
    return null;
  }

  // 检查是否为空值合并运算符
  protected isNullishCoalescing(node: any): boolean {
    return node.operator === '??';
  }

  // 检查是否为简单的null/undefined检查
  protected isNullCheck(node: any): boolean {
    if (node.type === 'LogicalExpression' && node.operator === '&&') {
      const left = node.left;
      // 检查左侧是否为简单的变量或属性访问
      return left && (
        left.type === 'Identifier' ||
        left.type === 'MemberExpression'
      );
    }
    return false;
  }

  // 检查是否为权限检查
  protected isPermissionCheck(node: any): boolean {
    if (node.type === 'LogicalExpression' && node.operator === '&&') {
      const left = node.left;
      if (left && left.type === 'MemberExpression') {
        const property = left.property;
        if (property && property.type === 'Identifier') {
          const propertyName = property.value || property.name;
          return typeof propertyName === 'string' && 
                 /^(can|has|is|allow|permit)/i.test(propertyName);
        }
      }
    }
    return false;
  }

  // 检查是否为嵌套条件
  protected isNestedConditional(node: any): boolean {
    // 简化版本：检查是否包含嵌套的条件表达式
    if (node.type === 'ConditionalExpression') {
      return (
        (node.consequent && this.isConditionalNode(node.consequent)) ||
        (node.alternate && this.isConditionalNode(node.alternate))
      );
    }
    return false;
  }

  // 计算嵌套深度
  protected async calculateNestingDepth(
    node: Node,
    context: AnalysisContext,
    currentDepth: number = 0
  ): Promise<number> {
    let maxDepth = currentDepth;

    // 简化版本：基于节点类型增加深度
    if (this.isConditionalNode(node) || this.isLoopNode(node)) {
      maxDepth = currentDepth + 1;
    }

    return maxDepth;
  }

  // 生成重构建议
  protected generateSuggestions(nodeType: string, complexity: number): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];

    if (complexity > 3) {
      suggestions.push({
        type: 'refactor',
        message: `Consider breaking down this ${nodeType} into smaller functions`,
        priority: 'medium',
      });
    }

    if (complexity > 5) {
      suggestions.push({
        type: 'warning',
        message: `High complexity detected in ${nodeType}. Consider refactoring.`,
        priority: 'high',
      });
    }

    return suggestions;
  }

  // 生成针对嵌套结构的建议 - 新增方法
  protected generateNestingSuggestions(nodeType: string, nestingLevel: number): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];

    if (nestingLevel > 2) {
      suggestions.push({
        type: 'refactor',
        message: `Deep nesting detected in ${nodeType}. Consider extracting logic into separate functions.`,
        codeExample: `// Instead of deep nesting:\nif (condition1) {\n  if (condition2) {\n    // logic\n  }\n}\n\n// Use early returns:\nif (!condition1) return;\nif (!condition2) return;\n// logic`,
        priority: 'medium',
      });
    }

    if (nestingLevel > 4) {
      suggestions.push({
        type: 'warning',
        message: `Very deep nesting in ${nodeType}. This significantly increases cognitive complexity.`,
        priority: 'high',
      });
    }

    return suggestions;
  }

  // 合并建议列表 - 新增工具方法
  protected mergeSuggestions(...suggestionLists: import('../engine/types').Suggestion[][]): import('../engine/types').Suggestion[] {
    const merged: import('../engine/types').Suggestion[] = [];
    const seen = new Set<string>();

    for (const list of suggestionLists) {
      for (const suggestion of list) {
        const key = `${suggestion.type}:${suggestion.message}`;
        if (!seen.has(key)) {
          seen.add(key);
          merged.push(suggestion);
        }
      }
    }

    return merged;
  }

  // 日志辅助方法
  protected debug(message: string, data?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[${this.id}] ${message}`, data);
    }
  }

  protected warn(message: string, data?: any): void {
    console.warn(`[${this.id}] ${message}`, data);
  }

  protected error(message: string, data?: any): void {
    console.error(`[${this.id}] ${message}`, data);
  }
}