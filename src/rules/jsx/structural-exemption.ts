/**
 * JSX结构智能豁免规则
 * 实现任务3.1：对纯UI结构节点进行智能豁免
 */

import type { Node } from '@swc/core';
import type { RuleResult, AnalysisContext } from '../../engine/types';
import { BaseRule } from '../base/rule';

/**
 * JSX结构节点类型定义
 */
const JSX_STRUCTURAL_TYPES = [
  'JSXElement',          // <div>...</div>
  'JSXFragment',         // <>...</>
  'JSXAttribute',        // className="..."
  'JSXText',             // 文本内容
  'JSXExpressionContainer', // {expression}
  'JSXSpreadAttribute',  // {...props}
  'JSXOpeningElement',   // <div>
  'JSXClosingElement',   // </div>
  'JSXSpreadChild'       // {...children}
] as const;

type JSXStructuralType = typeof JSX_STRUCTURAL_TYPES[number];

/**
 * JSX结构智能豁免规则实现
 * 
 * 功能：
 * - 完全豁免纯UI结构节点的复杂度
 * - 智能识别JSX模式（组件、Fragment、属性）
 * - 支持配置化的豁免策略
 * - 提供详细的豁免原因说明
 */
export class JSXStructuralExemptionRule extends BaseRule {
  readonly id = 'jsx.structural.exemption';
  readonly name = 'JSX Structural Exemption';
  readonly priority = 1000; // 高优先级，优先执行豁免逻辑

  /**
   * 检查是否能处理该节点
   */
  canHandle(node: Node): boolean {
    return JSX_STRUCTURAL_TYPES.includes(node.type as JSXStructuralType);
  }

  /**
   * 评估JSX结构节点
   */
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      this.debug('Evaluating JSX structural node', { 
        nodeType: node.type,
        enabled: context.config.rules.jsx.enabled,
        exemptionEnabled: context.config.rules.jsx.exemptions.structuralNodes
      });

      // 检查JSX规则是否启用
      if (!context.config.rules.jsx.enabled) {
        return this.createNonExemptionResult(
          0,
          'JSX rules disabled'
        );
      }

      // 检查结构节点豁免是否启用
      if (!context.config.rules.jsx.exemptions.structuralNodes) {
        return this.createNonExemptionResult(
          0,
          'JSX structural exemption disabled'
        );
      }

      // 分析豁免原因和类型
      const exemptionAnalysis = this.analyzeExemptionReason(node);
      
      // 检查是否包含复杂逻辑（需要特殊处理）
      if (await this.hasComplexLogic(node, context)) {
        return this.createComplexityResult(
          1, // 包含复杂逻辑时给予基础复杂度
          false,
          'JSX node contains complex logic',
          {
            nodeType: node.type,
            exemptionType: 'partial',
            hasComplexLogic: true
          }
        );
      }

      // 完全豁免纯结构节点
      return this.createExemptionResult(
        exemptionAnalysis.reason,
        {
          nodeType: node.type,
          exemptionCategory: exemptionAnalysis.category,
          pattern: exemptionAnalysis.pattern,
          isStructural: true
        }
      );
    });
  }

  /**
   * 分析豁免原因
   */
  private analyzeExemptionReason(node: Node): {
    reason: string;
    category: string;
    pattern: string;
  } {
    switch (node.type) {
      case 'JSXElement':
        return {
          reason: 'Pure UI element structure - contributes no cognitive complexity',
          category: 'ui-structure',
          pattern: 'jsx-element'
        };

      case 'JSXFragment':
        return {
          reason: 'UI fragment grouping - purely structural',
          category: 'ui-grouping',
          pattern: 'jsx-fragment'
        };

      case 'JSXAttribute':
        return {
          reason: 'Element attribute definition - declarative UI property',
          category: 'ui-property',
          pattern: 'jsx-attribute'
        };

      case 'JSXText':
        return {
          reason: 'Static text content - no logic complexity',
          category: 'ui-content',
          pattern: 'jsx-text'
        };

      case 'JSXExpressionContainer':
        return {
          reason: 'JSX expression container - wrapper for embedded expressions',
          category: 'ui-expression',
          pattern: 'jsx-expression-container'
        };

      case 'JSXSpreadAttribute':
        return {
          reason: 'Props spread syntax - declarative property distribution',
          category: 'ui-props',
          pattern: 'jsx-spread-attribute'
        };

      case 'JSXOpeningElement':
      case 'JSXClosingElement':
        return {
          reason: 'JSX element boundary - structural markup',
          category: 'ui-boundary',
          pattern: 'jsx-element-boundary'
        };

      case 'JSXSpreadChild':
        return {
          reason: 'Children spread syntax - declarative child distribution',
          category: 'ui-children',
          pattern: 'jsx-spread-child'
        };

      default:
        return {
          reason: 'JSX structural node - UI declaration',
          category: 'ui-generic',
          pattern: 'jsx-generic'
        };
    }
  }

  /**
   * 检查JSX节点是否包含复杂逻辑
   * 这些情况下可能需要部分计算复杂度
   */
  private async hasComplexLogic(node: Node, context: AnalysisContext): Promise<boolean> {
    // 对于JSXExpressionContainer，需要检查内部表达式
    if (node.type === 'JSXExpressionContainer') {
      return this.checkExpressionComplexity(node, context);
    }

    // 对于JSXAttribute，检查属性值是否包含复杂表达式
    if (node.type === 'JSXAttribute') {
      return this.checkAttributeComplexity(node, context);
    }

    // 其他结构节点通常不包含复杂逻辑
    return false;
  }

  /**
   * 检查表达式容器的复杂度
   */
  private checkExpressionComplexity(node: any, context: AnalysisContext): boolean {
    const expression = node.expression;
    
    if (!expression) {
      return false;
    }

    // 简单值表达式不算复杂逻辑
    if (this.isSimpleValueExpression(expression)) {
      return false;
    }

    // 简单的条件渲染可能需要豁免
    if (this.isSimpleConditionalRendering(expression) && 
        context.config.rules.jsx.exemptions.simpleConditionals) {
      return false;
    }

    // 空值合并操作符豁免
    if (this.isNullishCoalescing(expression) && 
        context.config.rules.jsx.exemptions.nullishCoalescing) {
      return false;
    }

    // 其他复杂表达式需要计算复杂度
    return this.isComplexExpression(expression);
  }

  /**
   * 检查属性值的复杂度
   */
  private checkAttributeComplexity(node: any, context: AnalysisContext): boolean {
    const value = node.value;
    
    if (!value || value.type !== 'JSXExpressionContainer') {
      return false; // 字符串字面量等简单值
    }

    // 递归检查表达式容器
    return this.checkExpressionComplexity(value, context);
  }

  /**
   * 判断是否为简单值表达式
   */
  private isSimpleValueExpression(expression: any): boolean {
    const simpleTypes = [
      'Identifier',        // 变量引用
      'StringLiteral',     // 字符串
      'NumericLiteral',    // 数字
      'BooleanLiteral',    // 布尔值
      'NullLiteral',       // null
      'MemberExpression'   // 简单的属性访问
    ];

    return simpleTypes.includes(expression.type);
  }

  /**
   * 判断是否为简单条件渲染
   * 例如：{condition && <Component />} 或 {condition ? <A /> : <B />}
   */
  private isSimpleConditionalRendering(expression: any): boolean {
    // 逻辑与操作符用于条件渲染：condition && element
    if (expression.type === 'LogicalExpression' && expression.operator === '&&') {
      return this.isSimpleCondition(expression.left) && this.isJSXResult(expression.right);
    }

    // 三元操作符用于条件渲染：condition ? elementA : elementB
    if (expression.type === 'ConditionalExpression') {
      return this.isSimpleCondition(expression.test) && 
             this.isJSXResult(expression.consequent) && 
             this.isJSXResult(expression.alternate);
    }

    return false;
  }

  /**
   * 判断是否为简单条件
   */
  private isSimpleCondition(condition: any): boolean {
    // 简单的标识符、布尔值、比较表达式等
    const simpleConditionTypes = [
      'Identifier',
      'BooleanLiteral',
      'MemberExpression',
      'CallExpression' // 简单的函数调用
    ];

    if (simpleConditionTypes.includes(condition.type)) {
      return true;
    }

    // 简单的比较操作
    if (condition.type === 'BinaryExpression') {
      const comparisonOps = ['===', '!==', '==', '!=', '>', '<', '>=', '<='];
      return comparisonOps.includes(condition.operator);
    }

    return false;
  }

  /**
   * 判断是否为JSX结果（元素或null）
   */
  private isJSXResult(result: any): boolean {
    return result.type.startsWith('JSX') || 
           result.type === 'NullLiteral' ||
           result.type === 'Identifier';
  }

  /**
   * 判断是否为空值合并操作
   */
  private isNullishCoalescing(expression: any): boolean {
    return expression.type === 'BinaryExpression' && expression.operator === '??';
  }

  /**
   * 判断是否为复杂表达式
   */
  private isComplexExpression(expression: any): boolean {
    const complexTypes = [
      'ArrowFunctionExpression',
      'FunctionExpression',
      'CallExpression',      // 复杂函数调用
      'NewExpression',
      'UpdateExpression',
      'AssignmentExpression',
      'SequenceExpression',
      'ForStatement',
      'WhileStatement',
      'DoWhileStatement',
      'IfStatement',
      'SwitchStatement'
    ];

    if (complexTypes.includes(expression.type)) {
      return true;
    }

    // 嵌套的逻辑表达式可能比较复杂
    if (expression.type === 'LogicalExpression') {
      return this.hasNestedLogicalOperators(expression);
    }

    // 复杂的三元表达式
    if (expression.type === 'ConditionalExpression') {
      return this.hasNestedConditionals(expression);
    }

    return false;
  }

  /**
   * 检查是否有嵌套的逻辑操作符
   */
  private hasNestedLogicalOperators(expression: any): boolean {
    const checkNested = (node: any): boolean => {
      if (!node) return false;
      
      if (node.type === 'LogicalExpression') {
        return true;
      }
      
      // 检查左右子节点
      return checkNested(node.left) || checkNested(node.right);
    };

    return checkNested(expression.left) || checkNested(expression.right);
  }

  /**
   * 检查是否有嵌套的条件表达式
   */
  private hasNestedConditionals(expression: any): boolean {
    const checkNested = (node: any): boolean => {
      if (!node) return false;
      
      if (node.type === 'ConditionalExpression') {
        return true;
      }
      
      return false; // 简化检查
    };

    return checkNested(expression.test) || 
           checkNested(expression.consequent) || 
           checkNested(expression.alternate);
  }
}