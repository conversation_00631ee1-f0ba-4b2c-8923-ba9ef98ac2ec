/**
 * 核心复杂度规则实现
 * 实现基础的认知复杂度计算逻辑
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * 核心复杂度规则
 * 处理基础的控制流结构：if、for、while、switch等
 */
export class CoreComplexityRule extends BaseRule {
  readonly id = 'core.complexity';
  readonly name = 'Core Complexity Rule';
  readonly priority = 500; // 中等优先级

  canHandle(node: Node): boolean {
    const coreTypes = [
      'IfStatement',
      'ForStatement', 
      'ForInStatement',
      'ForOfStatement',
      'WhileStatement',
      'DoWhileStatement',
      'SwitchStatement',
      'ConditionalExpression',
      'CatchClause',
    ];
    return coreTypes.includes(node.type);
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const complexity = this.calculateNodeComplexity(node, context);
      const shouldIncreaseNesting = this.shouldIncreaseNesting(node);
      
      const reason = this.generateReason(node, complexity, context.nestingLevel);
      const suggestions = this.generateSuggestions(node.type, complexity + context.nestingLevel);
      
      return this.createComplexityResult(
        node,
        complexity,
        reason,
        shouldIncreaseNesting,
        suggestions,
        {
          nodeType: node.type,
          baseComplexity: complexity,
          nestingLevel: context.nestingLevel,
          totalComplexity: complexity + context.nestingLevel,
        }
      );
    });
  }

  private calculateNodeComplexity(node: Node, context: AnalysisContext): number {
    const baseComplexity = this.getBaseComplexity(node.type);
    const nestingPenalty = this.calculateNestingPenalty(context.nestingLevel);
    
    return baseComplexity + nestingPenalty;
  }

  private getBaseComplexity(nodeType: string): number {
    const complexityMap: Record<string, number> = {
      'IfStatement': 1,
      'ForStatement': 1,
      'ForInStatement': 1,
      'ForOfStatement': 1,
      'WhileStatement': 1,
      'DoWhileStatement': 1,
      'SwitchStatement': 1,
      'ConditionalExpression': 1,
      'CatchClause': 1,
    };
    
    return complexityMap[nodeType] || 0;
  }

  private calculateNestingPenalty(nestingLevel: number): number {
    // 嵌套惩罚：每层嵌套增加1点复杂度
    return nestingLevel;
  }

  protected override shouldIncreaseNesting(node: Node): boolean {
    const nestingTypes = [
      'IfStatement',
      'ForStatement',
      'ForInStatement',
      'ForOfStatement',
      'WhileStatement',
      'DoWhileStatement',
      'SwitchStatement',
      'CatchClause',
    ];
    
    return nestingTypes.includes(node.type);
  }

  private generateReason(node: Node, complexity: number, nestingLevel: number): string {
    const reasonMap: Record<string, string> = {
      'IfStatement': 'Conditional branch increases cognitive complexity',
      'ForStatement': 'Loop structure increases cognitive complexity',
      'ForInStatement': 'For-in loop increases cognitive complexity',
      'ForOfStatement': 'For-of loop increases cognitive complexity', 
      'WhileStatement': 'While loop increases cognitive complexity',
      'DoWhileStatement': 'Do-while loop increases cognitive complexity',
      'SwitchStatement': 'Switch statement increases cognitive complexity',
      'ConditionalExpression': 'Ternary operator increases cognitive complexity',
      'CatchClause': 'Exception handling increases cognitive complexity',
    };

    const baseReason = reasonMap[node.type] || 'Control structure increases cognitive complexity';
    
    if (nestingLevel > 0) {
      return `${baseReason} (nesting level: ${nestingLevel}, penalty: +${nestingLevel})`;
    }
    
    return baseReason;
  }
}