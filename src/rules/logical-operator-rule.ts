/**
 * 逻辑运算符复杂度规则实现
 * 处理逻辑运算符（&&、||）和递归调用的复杂度计算
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';
import { isLogicalBinaryExpression, hasOperator, hasNodeType, hasBinaryProperties } from '../utils/type-guards';

/**
 * 逻辑运算符复杂度规则
 * 处理 && 和 || 运算符，包括混用检测和默认值赋值豁免
 */
export class LogicalOperatorRule extends BaseRule {
  readonly id = 'logical-operators';
  readonly name = 'Logical Operator Complexity';
  readonly priority = 600; // 较高优先级

  canHandle(node: Node): boolean {
    return (hasNodeType(node) && node.type === 'LogicalExpression') || 
           (hasNodeType(node) && node.type === 'BinaryExpression' && isLogicalBinaryExpression(node));
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // 类型安全地处理节点
      if (!hasOperator(node)) {
        return this.createNonExemptionResult(node, 'Node has no operator property');
      }
      
      // 检查是否为默认值赋值（应该豁免）
      if (this.isDefaultValueAssignment(node, context)) {
        return this.createExemptionResult(
          node,
          `Logical operator used for default value assignment (${node.operator})`,
          {
            nodeType: hasNodeType(node) ? node.type : 'unknown',
            operator: node.operator,
            exemptionType: 'default-value',
            isInAssignmentContext: this.isInAssignmentContext(context),
          }
        );
      }

      // 计算基础复杂度
      let complexity = 1; // 逻辑运算符基础复杂度为1

      // 检查逻辑运算符混用
      if (context.config.rules.logical.enableMixedLogicOperatorPenalty) {
        const hasMixing = await this.detectLogicalOperatorMixing(node, context);
        if (hasMixing) {
          complexity += 1; // 混用惩罚
        }
      }

      const reason = this.generateLogicalReason(node, complexity, context);
      const suggestions = this.generateLogicalSuggestions(node, complexity);

      return this.createComplexityResult(
        node,
        complexity,
        reason,
        false, // 逻辑运算符不增加嵌套层级
        suggestions,
        {
          nodeType: hasNodeType(node) ? node.type : 'unknown',
          operator: hasOperator(node) ? node.operator : 'unknown',
          hasMixing: complexity > 1,
          baseComplexity: 1,
          mixingPenalty: complexity - 1,
        }
      );
    });
  }

  // 这个方法现在可以被移除，因为我们使用了type-guards中的isLogicalBinaryExpression
  // private isLogicalBinaryExpression(node: any): boolean {
  //   return node.operator === '&&' || node.operator === '||';
  // }

  private isDefaultValueAssignment(node: unknown, context: AnalysisContext): boolean {
    if (!hasOperator(node)) {
      return false;
    }
    
    // 检查空值合并操作符 (??)
    if (node.operator === '??') {
      return true;
    }

    // 检查在赋值上下文中的 || 运算符
    if (node.operator === '||' && this.isInAssignmentContext(context)) {
      return true;
    }

    // 检查属性访问模式：obj && obj.prop
    if (node.operator === '&&' && this.isPropertyAccessPattern(node)) {
      return true;
    }

    return false;
  }

  private isInAssignmentContext(context: AnalysisContext): boolean {
    // 简化版本：检查当前函数是否包含赋值操作
    // 完整实现需要更复杂的AST分析
    return context.customData.has('isInAssignment') || false;
  }

  private isPropertyAccessPattern(node: unknown): boolean {
    if (!hasBinaryProperties(node)) {
      return false;
    }

    const binaryNode = node as { left: any; right: any };

    // 检查模式：左侧是标识符，右侧是对同一对象的属性访问
    // 例如: data && data.value
    if (this.isIdentifierNode(binaryNode.left) && 
        this.isMemberExpressionNode(binaryNode.right)) {
      
      const leftName = this.getIdentifierName(binaryNode.left);
      const rightObjectName = this.getMemberExpressionObjectName(binaryNode.right);
      
      return leftName === rightObjectName;
    }

    return false;
  }

  private isIdentifierNode(node: any): boolean {
    return hasNodeType(node) && node.type === 'Identifier';
  }

  private isMemberExpressionNode(node: any): boolean {
    return hasNodeType(node) && node.type === 'MemberExpression';
  }

  private getIdentifierName(node: any): string | null {
    if (!this.isIdentifierNode(node)) return null;
    return node.value || node.name || null;
  }

  private getMemberExpressionObjectName(node: any): string | null {
    if (!this.isMemberExpressionNode(node)) return null;
    const objectNode = node.object;
    if (!this.isIdentifierNode(objectNode)) return null;
    return this.getIdentifierName(objectNode);
  }

  private async detectLogicalOperatorMixing(node: unknown, context: AnalysisContext): Promise<boolean> {
    try {
      // 收集表达式中的所有逻辑运算符
      const operators = this.collectLogicalOperators(node);
      
      // 检查是否同时包含 && 和 ||
      const hasAND = operators.includes('&&');
      const hasOR = operators.includes('||');
      
      return hasAND && hasOR;
    } catch (error) {
      this.error('Error detecting logical operator mixing', error);
      return false;
    }
  }

  private collectLogicalOperators(node: any): string[] {
    const operators: string[] = [];
    const visited = new Set<any>();

    const collect = (currentNode: any) => {
      if (!currentNode || typeof currentNode !== 'object' || visited.has(currentNode)) {
        return;
      }
      
      visited.add(currentNode);

      // 处理当前节点的逻辑运算符
      if ((currentNode.type === 'LogicalExpression' || currentNode.type === 'BinaryExpression') && 
          currentNode.operator && ['&&', '||'].includes(currentNode.operator)) {
        operators.push(currentNode.operator);
      }

      // 递归处理子节点
      if (currentNode.left) collect(currentNode.left);
      if (currentNode.right) collect(currentNode.right);
    };

    collect(node);
    return operators;
  }

  private generateLogicalReason(node: any, complexity: number, context: AnalysisContext): string {
    const operatorNames: Record<string, string> = {
      '&&': 'logical AND',
      '||': 'logical OR',
    };

    const operatorName = operatorNames[node.operator] || node.operator;
    let reason = `${operatorName} operator increases cognitive complexity`;

    if (complexity > 1) {
      reason += ` (includes mixing penalty: +${complexity - 1})`;
    }

    return reason;
  }

  private generateLogicalSuggestions(node: any, complexity: number): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];

    if (complexity > 1) {
      suggestions.push({
        type: 'refactor',
        message: 'Consider using parentheses to clarify logical operator precedence',
        codeExample: `// Instead of: a && b || c\n// Use: (a && b) || c`,
        priority: 'medium',
      });
    }

    if (complexity > 2) {
      suggestions.push({
        type: 'warning',
        message: 'Complex logical expression detected. Consider breaking into multiple statements.',
        priority: 'high',
      });
    }

    return suggestions;
  }
}

/**
 * 递归调用复杂度规则
 * 检测和处理递归函数调用的复杂度
 */
export class RecursionComplexityRule extends BaseRule {
  readonly id = 'logical-recursion';
  readonly name = 'Recursion Complexity';
  readonly priority = 700; // 高优先级

  canHandle(node: Node): boolean {
    return node.type === 'CallExpression';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const callNode = node as any;
      
      if (!this.isRecursiveCall(callNode, context)) {
        return this.createNonExemptionResult(node, 'Not a recursive call');
      }

      const complexity = 1; // 递归调用基础复杂度为1
      const reason = `Recursive call to '${context.functionName}' increases cognitive complexity`;
      
      const suggestions = [{
        type: 'warning' as const,
        message: 'Recursive function detected. Ensure proper base case handling.',
        priority: 'medium' as const,
      }];

      return this.createComplexityResult(
        node,
        complexity,
        reason,
        false, // 递归调用不增加嵌套层级
        suggestions,
        {
          nodeType: node.type,
          isRecursive: true,
          functionName: context.functionName,
          calleeName: this.getCalleeIdentifier(callNode),
        }
      );
    });
  }

  private isRecursiveCall(callNode: any, context: AnalysisContext): boolean {
    const calleeName = this.getCalleeIdentifier(callNode);
    if (!calleeName || !context.functionName) {
      return false;
    }

    return calleeName === context.functionName;
  }

  private getCalleeIdentifier(callNode: any): string | null {
    const callee = callNode.callee;
    
    if (!callee) {
      return null;
    }
    
    // 直接函数调用: functionName()
    if (callee.type === 'Identifier') {
      return callee.value || callee.name;
    }
    
    // 成员表达式调用: obj.method() - 检查是否是this.method()
    if (callee.type === 'MemberExpression') {
      const object = callee.object;
      const property = callee.property;
      
      // this.functionName() 的情况
      if (object?.type === 'ThisExpression' && property?.type === 'Identifier') {
        return property.value || property.name;
      }
    }
    
    return null;
  }
}