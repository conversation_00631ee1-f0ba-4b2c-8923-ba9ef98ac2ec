/**
 * 智能条件渲染规则
 * 区分简单显示逻辑和复杂业务逻辑，提供精确的复杂度分析
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult, Suggestion } from '../engine/types';
import { BaseRule } from './base-rule';

// 条件分析结果
interface ConditionalAnalysis {
  pattern: 'null-check' | 'permission' | 'nested' | 'complex-logic' | 'standard';
  isSimpleDisplay: boolean;
  hasNesting: boolean;
  depth: number;
  complexity: number;
  description: string;
  conditions: ConditionInfo[];
}

// 条件信息
interface ConditionInfo {
  type: 'logical' | 'comparison' | 'function' | 'complex';
  operator?: string;
  complexity: number;
  isJSXContext: boolean;
}

/**
 * 智能条件渲染分析规则
 * 
 * 功能特性：
 * - 智能识别JSX条件渲染模式
 * - 区分简单显示逻辑和复杂业务逻辑
 * - 支持嵌套条件渲染的准确计分
 * - 提供条件渲染复杂度优化建议
 */
export class SmartConditionalRenderingRule extends BaseRule {
  readonly id = 'jsx.conditional.rendering';
  readonly name = 'Smart Conditional Rendering Analysis';
  readonly priority = 800;

  canHandle(node: Node): boolean {
    return (
      node.type === 'ConditionalExpression' ||
      (node.type === 'LogicalExpression' && 
       ['&&', '||', '??'].includes((node as any).operator))
    );
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // 检查是否启用JSX条件渲染规则
      if (!context.config.rules.jsx.scoring.conditionalRendering) {
        return this.createNonExemptionResult(node, 'JSX conditional rendering scoring disabled');
      }

      const analysis = await this.analyzeConditionalPattern(node, context);
      
      // 如果是简单显示逻辑且启用了简单条件豁免
      if (analysis.isSimpleDisplay && 
          context.config.rules.jsx.exemptions.simpleConditionals) {
        return this.createExemptionResult(
          node,
          analysis.description,
          { 
            pattern: analysis.pattern, 
            depth: analysis.depth,
            exemptionType: 'simple-display'
          }
        );
      }

      const suggestions = this.generateConditionalSuggestions(analysis);
      
      return this.createComplexityResult(
        node,
        analysis.complexity,
        analysis.description,
        analysis.hasNesting,
        suggestions,
        { 
          pattern: analysis.pattern, 
          depth: analysis.depth,
          conditions: analysis.conditions
        }
      );
    });
  }

  /**
   * 分析条件渲染模式
   */
  private async analyzeConditionalPattern(
    node: Node, 
    context: AnalysisContext
  ): Promise<ConditionalAnalysis> {
    const nodeAny = node as any;

    // 检查是否在JSX上下文中
    const isJSXContext = this.isInJSXContext(node, context);

    // 权限检查应该优先于null检查
    if (this.isPermissionCheck(nodeAny)) {
      return {
        pattern: 'permission',
        isSimpleDisplay: false,
        hasNesting: true,
        depth: 1,
        complexity: 1,
        description: 'Permission or capability check',
        conditions: [{ 
          type: 'logical', 
          operator: '&&', 
          complexity: 1, 
          isJSXContext 
        }]
      };
    }

    // 基础复杂度分析
    if (this.isNullCheck(nodeAny)) {
      return {
        pattern: 'null-check',
        isSimpleDisplay: true,
        hasNesting: false,
        depth: 1,
        complexity: isJSXContext ? 0 : 1, // JSX中的null检查通常不增加复杂度
        description: 'Simple null/undefined check for display logic',
        conditions: [{ 
          type: 'logical', 
          operator: '&&', 
          complexity: 0, 
          isJSXContext 
        }]
      };
    }

    if (this.isNestedConditional(nodeAny)) {
      const depth = await this.calculateComplexNestingDepth(nodeAny, context);
      return {
        pattern: 'nested',
        isSimpleDisplay: false,
        hasNesting: true,
        depth,
        complexity: Math.max(2, depth), // 嵌套条件至少复杂度为2
        description: `Nested conditional rendering (depth: ${depth})`,
        conditions: await this.extractConditionInfo(nodeAny, context)
      };
    }

    // 分析复杂逻辑表达式
    if (this.hasComplexLogic(nodeAny)) {
      const conditions = await this.extractConditionInfo(nodeAny, context);
      const complexity = this.calculateLogicComplexity(conditions, isJSXContext);
      
      return {
        pattern: 'complex-logic',
        isSimpleDisplay: false,
        hasNesting: false,
        depth: 1,
        complexity,
        description: 'Complex conditional logic with multiple conditions',
        conditions
      };
    }

    // 标准条件渲染
    const conditions = await this.extractConditionInfo(nodeAny, context);
    return {
      pattern: 'standard',
      isSimpleDisplay: false,
      hasNesting: false,
      depth: 1,
      complexity: isJSXContext ? 1 : 2,
      description: 'Standard conditional rendering',
      conditions
    };
  }

  /**
   * 检查是否在JSX上下文中
   */
  private isInJSXContext(node: Node, context: AnalysisContext): boolean {
    // 简化版本：检查当前文件是否包含JSX
    return context.filePath.endsWith('.jsx') || 
           context.filePath.endsWith('.tsx') ||
           context.fileContent.includes('jsx') ||
           context.fileContent.includes('<');
  }

  /**
   * 检查是否包含复杂逻辑
   */
  private hasComplexLogic(node: any): boolean {
    if (node.type === 'LogicalExpression') {
      // 检查是否有多层逻辑运算符混合
      const hasMultipleOperators = this.hasMultipleLogicalOperators(node);
      const hasComplexOperands = this.hasComplexOperands(node);
      
      return hasMultipleOperators || hasComplexOperands;
    }
    
    return false;
  }

  /**
   * 检查是否有多种逻辑运算符混合
   */
  private hasMultipleLogicalOperators(node: any): boolean {
    const operators = new Set<string>();
    this.collectLogicalOperators(node, operators);
    return operators.size > 1;
  }

  /**
   * 收集逻辑运算符
   */
  private collectLogicalOperators(node: any, operators: Set<string>): void {
    if (node.type === 'LogicalExpression') {
      operators.add(node.operator);
      if (node.left) this.collectLogicalOperators(node.left, operators);
      if (node.right) this.collectLogicalOperators(node.right, operators);
    }
  }

  /**
   * 检查是否有复杂操作数
   */
  private hasComplexOperands(node: any): boolean {
    return this.checkOperandComplexity(node.left) || 
           this.checkOperandComplexity(node.right);
  }

  /**
   * 检查操作数复杂度
   */
  private checkOperandComplexity(operand: any): boolean {
    if (!operand) return false;
    
    // 函数调用被认为是复杂的
    if (operand.type === 'CallExpression') return true;
    
    // 嵌套的条件表达式被认为是复杂的
    if (operand.type === 'ConditionalExpression') return true;
    
    // 多层属性访问被认为是复杂的
    if (operand.type === 'MemberExpression') {
      let depth = 0;
      let current = operand;
      while (current.type === 'MemberExpression') {
        depth++;
        current = current.object;
        if (depth > 2) return true;
      }
    }
    
    return false;
  }

  /**
   * 计算复杂嵌套深度
   */
  private async calculateComplexNestingDepth(
    node: any,
    context: AnalysisContext,
    currentDepth: number = 0
  ): Promise<number> {
    let maxDepth = currentDepth;

    if (node.type === 'ConditionalExpression') {
      // 三元运算符嵌套
      if (node.consequent && this.isConditionalNode(node.consequent)) {
        const consequentDepth = await this.calculateComplexNestingDepth(
          node.consequent, context, currentDepth + 1
        );
        maxDepth = Math.max(maxDepth, consequentDepth);
      }
      
      if (node.alternate && this.isConditionalNode(node.alternate)) {
        const alternateDepth = await this.calculateComplexNestingDepth(
          node.alternate, context, currentDepth + 1
        );
        maxDepth = Math.max(maxDepth, alternateDepth);
      }
    }

    if (node.type === 'LogicalExpression') {
      // 逻辑表达式嵌套
      if (node.right && this.isConditionalNode(node.right)) {
        const rightDepth = await this.calculateComplexNestingDepth(
          node.right, context, currentDepth + 1
        );
        maxDepth = Math.max(maxDepth, rightDepth);
      }
    }

    return Math.max(maxDepth, currentDepth + 1);
  }

  /**
   * 提取条件信息
   */
  private async extractConditionInfo(
    node: any, 
    context: AnalysisContext
  ): Promise<ConditionInfo[]> {
    const conditions: ConditionInfo[] = [];
    const isJSXContext = this.isInJSXContext(node, context);

    if (node.type === 'LogicalExpression') {
      conditions.push({
        type: 'logical',
        operator: node.operator,
        complexity: this.getOperatorComplexity(node.operator),
        isJSXContext
      });

      // 递归提取左右操作数的条件
      if (node.left) {
        const leftConditions = await this.extractConditionInfo(node.left, context);
        conditions.push(...leftConditions);
      }
      
      if (node.right) {
        const rightConditions = await this.extractConditionInfo(node.right, context);
        conditions.push(...rightConditions);
      }
    } else if (node.type === 'ConditionalExpression') {
      conditions.push({
        type: 'comparison',
        complexity: 1,
        isJSXContext
      });

      // 递归提取条件和结果的条件
      if (node.test) {
        const testConditions = await this.extractConditionInfo(node.test, context);
        conditions.push(...testConditions);
      }
    } else if (node.type === 'CallExpression') {
      conditions.push({
        type: 'function',
        complexity: 2,
        isJSXContext
      });
    } else {
      conditions.push({
        type: 'complex',
        complexity: 1,
        isJSXContext
      });
    }

    return conditions;
  }

  /**
   * 获取运算符复杂度
   */
  private getOperatorComplexity(operator: string): number {
    const complexityMap: Record<string, number> = {
      '&&': 1,
      '||': 1,
      '??': 0.5, // 空值合并运算符复杂度较低
    };
    
    return complexityMap[operator] || 1;
  }

  /**
   * 计算逻辑复杂度
   */
  private calculateLogicComplexity(
    conditions: ConditionInfo[], 
    isJSXContext: boolean
  ): number {
    let totalComplexity = 0;

    // 基础复杂度
    const baseComplexity = conditions.reduce((sum, condition) => {
      return sum + condition.complexity;
    }, 0);

    // JSX上下文中的复杂度调整
    if (isJSXContext) {
      // JSX中的显示逻辑复杂度适当降低
      totalComplexity = Math.max(1, Math.floor(baseComplexity * 0.8));
    } else {
      totalComplexity = baseComplexity;
    }

    // 混合操作符惩罚
    const operatorTypes = new Set(
      conditions
        .filter(c => c.type === 'logical' && c.operator)
        .map(c => c.operator)
    );
    
    if (operatorTypes.size > 1) {
      totalComplexity += 1; // 混合逻辑运算符增加复杂度
    }

    return Math.max(1, totalComplexity);
  }

  /**
   * 生成条件渲染优化建议
   */
  private generateConditionalSuggestions(analysis: ConditionalAnalysis): Suggestion[] {
    const suggestions: Suggestion[] = [];

    if (analysis.complexity > 3) {
      suggestions.push({
        type: 'refactor',
        message: 'Consider extracting complex conditional logic into a separate function',
        codeExample: `
// Instead of:
{condition1 && condition2 && condition3 && <Component />}

// Consider:
{shouldRenderComponent() && <Component />}

const shouldRenderComponent = () => {
  return condition1 && condition2 && condition3;
};`,
        priority: 'medium',
      });
    }

    if (analysis.pattern === 'nested' && analysis.depth > 2) {
      suggestions.push({
        type: 'refactor',
        message: 'Deep nested conditional rendering can be hard to read. Consider using early returns or guard clauses',
        codeExample: `
// Instead of nested ternary:
{condition1 ? (condition2 ? <A /> : <B />) : <C />}

// Consider early returns in a function:
const renderContent = () => {
  if (!condition1) return <C />;
  if (!condition2) return <B />;
  return <A />;
};`,
        priority: 'high',
      });
    }

    if (analysis.pattern === 'complex-logic') {
      suggestions.push({
        type: 'optimize',
        message: 'Complex conditional logic may benefit from useMemo for performance',
        codeExample: `
const shouldShow = useMemo(() => {
  return complexCondition1 && complexCondition2;
}, [dependency1, dependency2]);

return shouldShow && <Component />;`,
        priority: 'low',
      });
    }

    if (analysis.conditions.some(c => c.type === 'function')) {
      suggestions.push({
        type: 'info',
        message: 'Function calls in conditional rendering may cause unnecessary re-renders',
        priority: 'low',
      });
    }

    return suggestions;
  }
}