/**
 * 异常处理复杂度规则实现
 * 处理 try-catch-finally 语句的复杂度计算
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * 异常处理复杂度规则
 * 统一处理 CatchClause 节点的复杂度计算
 * 
 * 规则说明：
 * - TryStatement 本身不增加复杂度，只是一个容器
 * - CatchClause 增加基础复杂度 1 + 嵌套惩罚
 * - 嵌套的 try-catch 需要额外的嵌套惩罚
 */
export class CatchClauseRule extends BaseRule {
  readonly id = 'catch-clause';
  readonly name = 'Exception Handling Complexity';
  readonly priority = 500; // 高优先级，异常处理是重要的控制流

  canHandle(node: Node): boolean {
    return node.type === 'CatchClause';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const catchNode = node as any;
      
      // 基础复杂度：每个 catch 子句都增加1点复杂度
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateCatchReason(catchNode, baseComplexity, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateCatchSuggestions(catchNode, finalComplexity, context);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        true, // catch 子句创建新的嵌套作用域
        suggestions,
        {
          nodeType: node.type,
          baseComplexity,
          nestingLevel: context.nestingLevel,
          hasErrorParameter: this.hasErrorParameter(catchNode),
          hasComplexErrorHandling: this.hasComplexErrorHandling(catchNode),
          hasNestedTryCatch: this.hasNestedTryCatch(catchNode),
        }
      );
    });
  }

  /**
   * 检查是否有错误参数
   */
  private hasErrorParameter(catchNode: any): boolean {
    return !!(catchNode.param);
  }

  /**
   * 检查是否有复杂的错误处理
   */
  private hasComplexErrorHandling(catchNode: any): boolean {
    const body = catchNode.body;
    if (!body) {
      return false;
    }

    // 检查是否包含复杂的处理逻辑
    return this.containsComplexLogic(body);
  }

  /**
   * 检查是否包含嵌套的 try-catch
   */
  private hasNestedTryCatch(catchNode: any): boolean {
    const body = catchNode.body;
    if (!body) {
      return false;
    }

    return this.containsTryStatements(body);
  }

  /**
   * 检查节点是否包含复杂逻辑
   */
  private containsComplexLogic(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查当前节点是否为复杂控制流
    const complexNodeTypes = [
      'IfStatement',
      'ForStatement', 'ForInStatement', 'ForOfStatement',
      'WhileStatement', 'DoWhileStatement',
      'SwitchStatement',
      'ConditionalExpression'
    ];

    if (complexNodeTypes.includes(node.type)) {
      return true;
    }

    // 检查是否包含多个函数调用
    if (this.countFunctionCalls(node) > 2) {
      return true;
    }

    // 检查是否包含复杂表达式
    if (this.containsComplexExpressions(node)) {
      return true;
    }

    // 递归检查子节点
    return this.traverseAndCheck(node, this.containsComplexLogic.bind(this));
  }

  /**
   * 检查节点是否包含 try 语句
   */
  private containsTryStatements(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    if (node.type === 'TryStatement') {
      return true;
    }

    return this.traverseAndCheck(node, this.containsTryStatements.bind(this));
  }

  /**
   * 计算函数调用的数量
   */
  private countFunctionCalls(node: any): number {
    if (!node || typeof node !== 'object') {
      return 0;
    }

    let count = 0;

    if (node.type === 'CallExpression') {
      count += 1;
    }

    // 递归计算子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        count += value.reduce((sum, child) => sum + this.countFunctionCalls(child), 0);
      } else if (value && typeof value === 'object') {
        count += this.countFunctionCalls(value);
      }
    }

    return count;
  }

  /**
   * 检查节点是否包含复杂表达式
   */
  private containsComplexExpressions(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查是否包含复杂的二元表达式
    if (node.type === 'BinaryExpression') {
      const complexOperators = ['instanceof', 'in'];
      if (complexOperators.includes(node.operator)) {
        return true;
      }
    }

    // 检查是否包含逻辑运算符
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') &&
        ['&&', '||'].includes(node.operator)) {
      return true;
    }

    // 检查是否包含数组或对象字面量
    if (node.type === 'ArrayExpression' || node.type === 'ObjectExpression') {
      return true;
    }

    return this.traverseAndCheck(node, this.containsComplexExpressions.bind(this));
  }

  /**
   * 通用的节点遍历和检查方法
   */
  private traverseAndCheck(node: any, checkFunction: (node: any) => boolean): boolean {
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(child => checkFunction(child))) {
          return true;
        }
      } else if (value && typeof value === 'object') {
        if (checkFunction(value)) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * 生成 catch 子句的复杂度说明
   */
  private generateCatchReason(
    catchNode: any,
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext
  ): string {
    const nestingPenalty = finalComplexity - baseComplexity;
    
    let reason = `Catch clause increases cognitive complexity by ${finalComplexity}`;
    
    if (nestingPenalty > 0) {
      reason += ` (nesting penalty: +${nestingPenalty})`;
    }

    // 添加额外的复杂度信息
    if (this.hasComplexErrorHandling(catchNode)) {
      reason += ` (complex error handling detected)`;
    }

    // 嵌套 try-catch 特殊说明
    if (this.hasNestedTryCatch(catchNode)) {
      reason += ` (contains nested try-catch statements)`;
    }
    
    return reason;
  }

  /**
   * 生成 catch 子句特定的建议
   */
  private generateCatchSuggestions(
    catchNode: any,
    complexity: number,
    context: AnalysisContext
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    
    // 基础复杂度建议
    const basicSuggestions = this.generateSuggestions('catch clause', complexity);
    suggestions.push(...basicSuggestions);
    
    // 嵌套建议
    const nestingSuggestions = this.generateNestingSuggestions('catch clause', context.nestingLevel);
    suggestions.push(...nestingSuggestions);
    
    // 复杂错误处理建议
    if (this.hasComplexErrorHandling(catchNode)) {
      suggestions.push({
        type: 'refactor',
        message: 'Complex error handling logic detected. Consider extracting error handling into separate functions.',
        codeExample: `// Instead of complex error handling:\ntry {\n  riskyOperation();\n} catch (error) {\n  if (error.type === 'NetworkError') {\n    handleNetworkError(error);\n  } else if (error.type === 'ValidationError') {\n    handleValidationError(error);\n  } else {\n    handleGenericError(error);\n  }\n}\n\n// Extract to functions:\nfunction handleError(error) {\n  const handlers = {\n    NetworkError: handleNetworkError,\n    ValidationError: handleValidationError,\n    default: handleGenericError\n  };\n  \n  const handler = handlers[error.type] || handlers.default;\n  handler(error);\n}\n\ntry {\n  riskyOperation();\n} catch (error) {\n  handleError(error);\n}`,
        priority: 'high',
      });
    }

    // 嵌套 try-catch 建议
    if (this.hasNestedTryCatch(catchNode)) {
      suggestions.push({
        type: 'refactor',
        message: 'Nested try-catch statements detected. Consider flattening the error handling structure.',
        codeExample: `// Instead of nested try-catch:\ntry {\n  outerOperation();\n} catch (outerError) {\n  try {\n    fallbackOperation();\n  } catch (innerError) {\n    handleInnerError(innerError);\n  }\n}\n\n// Flatten structure:\nasync function safeOperation() {\n  try {\n    return await outerOperation();\n  } catch (outerError) {\n    return await safeFallbackOperation();\n  }\n}\n\nasync function safeFallbackOperation() {\n  try {\n    return await fallbackOperation();\n  } catch (innerError) {\n    handleInnerError(innerError);\n    throw innerError;\n  }\n}`,
        priority: 'high',
      });
    }

    // 无错误参数建议
    if (!this.hasErrorParameter(catchNode)) {
      suggestions.push({
        type: 'warning',
        message: 'Catch clause without error parameter. Consider adding error parameter for better debugging.',
        codeExample: `// Instead of:\ntry {\n  riskyOperation();\n} catch {\n  handleError();\n}\n\n// Use error parameter:\ntry {\n  riskyOperation();\n} catch (error) {\n  console.error('Operation failed:', error);\n  handleError(error);\n}`,
        priority: 'medium',
      });
    }

    // 过度复杂的错误处理建议
    const functionCallCount = this.countFunctionCalls(catchNode);
    if (functionCallCount > 3) {
      suggestions.push({
        type: 'optimize',
        message: `Multiple function calls (${functionCallCount}) in catch block. Consider consolidating error handling logic.`,
        codeExample: `// Instead of multiple calls:\ncatch (error) {\n  logError(error);\n  notifyUser(error);\n  sendToMonitoring(error);\n  updateState(error);\n  cleanupResources(error);\n}\n\n// Consolidate:\ncatch (error) {\n  handleErrorComprehensively(error);\n}\n\nfunction handleErrorComprehensively(error) {\n  const errorContext = { error, timestamp: Date.now() };\n  \n  // Batch operations\n  Promise.allSettled([\n    logError(errorContext),\n    notifyUser(errorContext),\n    sendToMonitoring(errorContext)\n  ]);\n  \n  // Synchronous operations\n  updateState(error);\n  cleanupResources(error);\n}`,
        priority: 'medium',
      });
    }

    // 深度嵌套建议
    if (context.nestingLevel > 2) {
      suggestions.push({
        type: 'refactor',
        message: 'Consider extracting nested catch logic into separate error handling functions to reduce cognitive complexity.',
        codeExample: `// Instead of deeply nested catch:\nfunction complexFunction() {\n  if (condition) {\n    try {\n      riskyOperation();\n    } catch (error) {\n      // deeply nested error handling\n    }\n  }\n}\n\n// Extract error handling:\nfunction handleRiskyOperation() {\n  try {\n    return riskyOperation();\n  } catch (error) {\n    handleOperationError(error);\n    throw error;\n  }\n}\n\nfunction complexFunction() {\n  if (condition) {\n    handleRiskyOperation();\n  }\n}`,
        priority: 'medium',
      });
    }

    // 最佳实践建议
    if (complexity === 1 && context.nestingLevel === 0) {
      suggestions.push({
        type: 'info',
        message: 'Simple catch clause. Consider specific error types and meaningful error messages.',
        codeExample: `// Good practices for catch clauses:\ntry {\n  await apiCall();\n} catch (error) {\n  if (error instanceof NetworkError) {\n    handleNetworkError(error);\n  } else if (error instanceof ValidationError) {\n    handleValidationError(error);\n  } else {\n    console.error('Unexpected error:', error);\n    throw error; // Re-throw if can't handle\n  }\n}`,
        priority: 'low',
      });
    }
    
    return suggestions;
  }
}