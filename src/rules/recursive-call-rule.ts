/**
 * 递归调用复杂度规则实现
 * 处理直接和间接递归函数调用的复杂度计算
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * 递归调用复杂度规则
 * 统一处理递归函数调用的复杂度计算
 * 
 * 规则说明：
 * - 直接递归调用：函数调用自身
 * - 间接递归调用：通过 this.method() 形式的递归调用
 * - 递归调用增加基础复杂度 1（不受嵌套层级影响）
 */
export class RecursiveCallRule extends BaseRule {
  readonly id = 'recursive-call';
  readonly name = 'Recursive Call Complexity';
  readonly priority = 600; // 高优先级，递归是重要的复杂度因素

  canHandle(node: Node): boolean {
    return node.type === 'CallExpression';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const callNode = node as any;
      
      // 检查是否为递归调用
      const recursionInfo = this.analyzeRecursiveCall(callNode, context);
      
      if (!recursionInfo.isRecursive) {
        return this.createNonExemptionResult(node, 'Not a recursive call');
      }

      // 递归调用的基础复杂度为1，不受嵌套层级影响
      // 这是因为递归本身就是一种复杂的控制流
      const baseComplexity = 1;
      
      // 生成原因说明
      const reason = this.generateRecursiveReason(recursionInfo, context);
      
      // 生成建议
      const suggestions = this.generateRecursiveSuggestions(recursionInfo, callNode, context);
      
      return this.createComplexityResult(
        node,
        baseComplexity,
        reason,
        false, // 递归调用不增加嵌套层级，它本身就是复杂度
        suggestions,
        {
          nodeType: node.type,
          recursionType: recursionInfo.type,
          functionName: context.functionName,
          calleeName: recursionInfo.calleeName,
          isDirectRecursion: recursionInfo.type === 'direct',
          isMethodRecursion: recursionInfo.type === 'method',
          callPattern: recursionInfo.callPattern,
        }
      );
    });
  }

  /**
   * 分析递归调用信息
   */
  private analyzeRecursiveCall(callNode: any, context: AnalysisContext): RecursionInfo {
    const functionName = context.functionName;
    
    if (!functionName) {
      return { isRecursive: false };
    }

    // 首先检查方法递归调用：this.methodName()
    const methodRecursion = this.checkMethodRecursion(callNode, functionName);
    if (methodRecursion.isRecursive) {
      return methodRecursion;
    }

    // 然后检查直接递归调用：functionName()
    const calleeName = this.getCalleeIdentifier(callNode);
    if (calleeName && calleeName === functionName) {
      return {
        isRecursive: true,
        type: 'direct',
        calleeName,
        callPattern: `${calleeName}()`,
      };
    }

    return { isRecursive: false };
  }

  /**
   * 检查方法递归调用
   */
  private checkMethodRecursion(callNode: any, functionName: string): RecursionInfo {
    const callee = callNode.callee;
    
    if (callee?.type !== 'MemberExpression') {
      return { isRecursive: false };
    }

    const object = callee.object;
    const property = callee.property;
    
    // this.methodName() 的情况
    if (object?.type === 'ThisExpression' && property?.type === 'Identifier') {
      const methodName = property.value || property.name;
      
      if (methodName === functionName) {
        return {
          isRecursive: true,
          type: 'method',
          calleeName: methodName,
          callPattern: `this.${methodName}()`,
        };
      }
    }

    return { isRecursive: false };
  }

  /**
   * 获取被调用函数的标识符
   * 只返回直接函数调用的标识符，不处理成员表达式调用
   */
  private getCalleeIdentifier(callNode: any): string | null {
    const callee = callNode.callee;
    
    if (!callee) {
      return null;
    }
    
    // 直接函数调用: functionName()
    if (callee.type === 'Identifier') {
      return callee.value || callee.name;
    }
    
    // 对于成员表达式调用，返回 null（由 checkMethodRecursion 处理）
    return null;
  }

  /**
   * 生成递归调用的复杂度说明
   */
  private generateRecursiveReason(recursionInfo: RecursionInfo, context: AnalysisContext): string {
    const pattern = recursionInfo.callPattern || recursionInfo.calleeName;
    const type = recursionInfo.type === 'method' ? 'method' : 'function';
    
    return `Recursive ${type} call '${pattern}' increases cognitive complexity by 1`;
  }

  /**
   * 生成递归调用特定的建议
   */
  private generateRecursiveSuggestions(
    recursionInfo: RecursionInfo,
    callNode: any,
    context: AnalysisContext
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    
    // 基础递归建议
    if (recursionInfo.type === 'direct') {
      suggestions.push({
        type: 'warning',
        message: 'Direct recursive function detected. Ensure proper base case and termination conditions.',
        codeExample: `// Ensure proper base case:\nfunction factorial(n) {\n  // Base case - prevents infinite recursion\n  if (n <= 1) {\n    return 1;\n  }\n  // Recursive case\n  return n * factorial(n - 1);\n}\n\n// Consider iterative alternative:\nfunction factorialIterative(n) {\n  let result = 1;\n  for (let i = 2; i <= n; i++) {\n    result *= i;\n  }\n  return result;\n}`,
        priority: 'high',
      });
    }

    if (recursionInfo.type === 'method') {
      suggestions.push({
        type: 'warning',
        message: 'Recursive method call detected. Verify base case and consider stack overflow prevention.',
        codeExample: `// Ensure proper base case in methods:\nclass TreeNode {\n  traverse() {\n    // Base case - prevents infinite recursion\n    if (!this.hasChildren()) {\n      return [this.value];\n    }\n    \n    // Recursive case\n    return [this.value, ...this.children.flatMap(child => child.traverse())];\n  }\n  \n  // Alternative: use iterative approach with stack\n  traverseIterative() {\n    const result = [];\n    const stack = [this];\n    \n    while (stack.length > 0) {\n      const node = stack.pop();\n      result.push(node.value);\n      stack.push(...node.children);\n    }\n    \n    return result;\n  }\n}`,
        priority: 'high',
      });
    }

    // 性能建议
    suggestions.push({
      type: 'optimize',
      message: 'Recursive calls can cause stack overflow for large inputs. Consider iterative solutions or tail-call optimization.',
      codeExample: `// Tail-call optimized recursion:\nfunction fibonacciTailCall(n, a = 0, b = 1) {\n  if (n === 0) return a;\n  if (n === 1) return b;\n  return fibonacciTailCall(n - 1, b, a + b);\n}\n\n// Iterative alternative:\nfunction fibonacciIterative(n) {\n  if (n <= 1) return n;\n  let a = 0, b = 1;\n  for (let i = 2; i <= n; i++) {\n    [a, b] = [b, a + b];\n  }\n  return b;\n}\n\n// Memoization for expensive recursive calls:\nconst memoize = (fn) => {\n  const cache = new Map();\n  return (...args) => {\n    const key = JSON.stringify(args);\n    if (cache.has(key)) return cache.get(key);\n    const result = fn(...args);\n    cache.set(key, result);\n    return result;\n  };\n};`,
      priority: 'medium',
    });

    // 深度递归检测建议
    if (context.nestingLevel > 0) {
      suggestions.push({
        type: 'refactor',
        message: 'Recursive call within nested structure. Consider extracting to separate function to reduce cognitive complexity.',
        codeExample: `// Instead of nested recursive call:\nfunction processData(data) {\n  if (data.hasChildren) {\n    for (const child of data.children) {\n      if (child.needsProcessing) {\n        processData(child); // Recursive call in nested structure\n      }\n    }\n  }\n}\n\n// Extract recursive logic:\nfunction processDataRecursively(data) {\n  if (!data.needsProcessing) return;\n  \n  // Process current data\n  handleData(data);\n  \n  // Recursively process children\n  data.children?.forEach(processDataRecursively);\n}\n\nfunction processData(data) {\n  if (data.hasChildren) {\n    data.children.forEach(processDataRecursively);\n  }\n}`,
        priority: 'medium',
      });
    }

    // 最佳实践建议
    suggestions.push({
      type: 'info',
      message: 'Best practices for recursive functions: Always define base cases, limit recursion depth, and consider iterative alternatives.',
      codeExample: `// Recursive function best practices:\nfunction safeRecursiveFunction(data, depth = 0) {\n  // 1. Check recursion depth limit\n  if (depth > 1000) {\n    throw new Error('Maximum recursion depth exceeded');\n  }\n  \n  // 2. Define clear base cases\n  if (!data || data.length === 0) {\n    return [];\n  }\n  \n  // 3. Make progress toward base case\n  return [data[0], ...safeRecursiveFunction(data.slice(1), depth + 1)];\n}\n\n// 4. Consider trampoline pattern for deep recursion:\nfunction trampoline(fn) {\n  return function trampolineWrapper(...args) {\n    let result = fn(...args);\n    while (typeof result === 'function') {\n      result = result();\n    }\n    return result;\n  };\n}`,
      priority: 'low',
    });
    
    return suggestions;
  }
}

/**
 * 递归信息接口
 */
interface RecursionInfo {
  isRecursive: boolean;
  type?: 'direct' | 'method';
  calleeName?: string;
  callPattern?: string;
}