/**
 * JSX结构豁免规则实现
 * 豁免纯UI结构节点的复杂度计算
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

// JSX结构节点类型
const JSX_STRUCTURAL_TYPES = [
  'JSXElement',
  'JSXFragment',
  'JSXAttribute',
  'JSXText',
  'JSXExpressionContainer',
  'JSXSpreadAttribute',
  'JSXOpeningElement',
  'JSXClosingElement',
  'JSXEmptyExpression',
];

/**
 * JSX结构豁免规则
 * 优先级设置为1000，确保在其他规则之前执行
 */
export class JSXStructuralExemptionRule extends BaseRule {
  readonly id = 'jsx.structural.exemption';
  readonly name = 'JSX Structural Exemption';
  readonly priority = 1000; // 高优先级，先执行豁免

  canHandle(node: Node): boolean {
    return JSX_STRUCTURAL_TYPES.includes(node.type);
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // 检查JSX豁免配置是否启用
      if (!context.config.rules.jsx.exemptions.structuralNodes) {
        return this.createNonExemptionResult(
          node,
          'JSX structural exemption is disabled in configuration'
        );
      }

      const exemptionReason = this.analyzeExemptionReason(node);
      const metadata = this.createExemptionMetadata(node);

      this.debug(`Exempting JSX structural node: ${node.type}`, metadata);

      return this.createExemptionResult(node, exemptionReason, metadata);
    });
  }

  private analyzeExemptionReason(node: Node): string {
    const reasonMap: Record<string, string> = {
      'JSXElement': 'Pure UI element structure - no cognitive load',
      'JSXFragment': 'UI fragment grouping - structural only',
      'JSXAttribute': 'Element attribute definition - declarative',
      'JSXText': 'Static text content - no logic',
      'JSXExpressionContainer': 'Expression container - wrapper only',
      'JSXSpreadAttribute': 'Spread attribute - syntax sugar',
      'JSXOpeningElement': 'Opening tag - structural markup',
      'JSXClosingElement': 'Closing tag - structural markup',
      'JSXEmptyExpression': 'Empty expression - no complexity',
    };

    return reasonMap[node.type] || 'JSX structural node - UI presentation only';
  }

  private createExemptionMetadata(node: Node): Record<string, any> {
    const metadata: Record<string, any> = {
      nodeType: node.type,
      exemptionType: 'structural',
      category: 'jsx',
      isUIOnly: true,
    };

    // 添加特定节点类型的额外信息
    if (node.type === 'JSXElement') {
      const element = node as any;
      metadata.tagName = this.extractTagName(element);
      metadata.hasChildren = this.hasJSXChildren(element);
      metadata.attributeCount = this.countAttributes(element);
    }

    return metadata;
  }

  private extractTagName(element: any): string | null {
    try {
      if (element.openingElement?.name?.type === 'Identifier') {
        return element.openingElement.name.value || element.openingElement.name.name;
      }
      if (element.openingElement?.name?.type === 'JSXMemberExpression') {
        return this.extractMemberExpressionName(element.openingElement.name);
      }
    } catch (error) {
      this.debug('Failed to extract tag name', error);
    }
    return null;
  }

  private extractMemberExpressionName(memberExpr: any): string {
    try {
      if (memberExpr.object?.type === 'Identifier' && memberExpr.property?.type === 'Identifier') {
        const object = memberExpr.object.value || memberExpr.object.name;
        const property = memberExpr.property.value || memberExpr.property.name;
        return `${object}.${property}`;
      }
    } catch (error) {
      this.debug('Failed to extract member expression name', error);
    }
    return 'unknown';
  }

  private hasJSXChildren(element: any): boolean {
    return element.children && Array.isArray(element.children) && element.children.length > 0;
  }

  private countAttributes(element: any): number {
    if (element.openingElement?.attributes) {
      return Array.isArray(element.openingElement.attributes) 
        ? element.openingElement.attributes.length 
        : 0;
    }
    return 0;
  }

  // 提供额外的辅助方法给其他JSX规则使用
  static isJSXStructuralNode(node: Node): boolean {
    return JSX_STRUCTURAL_TYPES.includes(node.type);
  }

  static shouldExemptJSXNode(node: Node, context: AnalysisContext): boolean {
    return JSX_STRUCTURAL_TYPES.includes(node.type) && 
           context.config.rules.jsx.exemptions.structuralNodes;
  }
}