/**
 * 规则集管理器
 * 管理和组织不同类型的规则集合
 */

import type { Rule } from '../engine/types';
import type { RuleRegistry } from '../engine/registry';
import { JSXStructuralExemptionRule } from './jsx/structural-exemption';
import { SmartConditionalRenderingRule } from './smart-conditional-rendering';
import { JSXEventHandlerRule } from './jsx-event-handler-rule';
import { JSXHookComplexityRule } from './jsx-hook-complexity';

/**
 * 核心规则集
 */
export class CoreRuleSet {
  static getRules(): Rule[] {
    // 暂时返回空数组，核心规则将在后续任务中实现
    return [];
  }
  
  static getDescription(): string {
    return 'Core complexity analysis rules';
  }
}

/**
 * JSX规则集
 */
export class JSXRuleSet {
  static getRules(): Rule[] {
    return [
      new JSXStructuralExemptionRule(),
      new SmartConditionalRenderingRule(),
      new JSXEventHandlerRule(),
      new JSXHookComplexityRule(),
    ];
  }
  
  static getDescription(): string {
    return 'React/JSX specialized analysis rules for intelligent complexity analysis';
  }
}

/**
 * 规则集统计信息
 */
interface RuleSetStats {
  totalRuleSets: number;
  totalRules: number;
  rulesBySet: Map<string, number>;
  enabledRules: number;
  disabledRules: number;
}

/**
 * 规则集验证结果
 */
interface ValidationResult {
  isValid: boolean;
  issues: string[];
  warnings: string[];
}

/**
 * 规则集管理器
 */
export class RuleSetManager {
  private static registeredSets = new Map<string, typeof CoreRuleSet | typeof JSXRuleSet>();
  
  static {
    // 注册默认规则集
    this.registeredSets.set('core', CoreRuleSet);
    this.registeredSets.set('jsx', JSXRuleSet);
  }
  
  /**
   * 注册规则集
   */
  static registerRuleSet(name: string, ruleSet: typeof CoreRuleSet | typeof JSXRuleSet): void {
    this.registeredSets.set(name, ruleSet);
    console.log(`Rule set '${name}' registered: ${ruleSet.getDescription()}`);
  }
  
  /**
   * 获取所有规则
   */
  static getAllRules(): Rule[] {
    const allRules: Rule[] = [];
    
    for (const [name, ruleSet] of this.registeredSets) {
      try {
        const rules = ruleSet.getRules();
        allRules.push(...rules);
        console.log(`Loaded ${rules.length} rules from '${name}' rule set`);
      } catch (error) {
        console.error(`Failed to load rules from '${name}' rule set:`, error);
      }
    }
    
    return allRules;
  }
  
  /**
   * 获取指定规则集的规则
   */
  static getRulesBySet(setName: string): Rule[] {
    const ruleSet = this.registeredSets.get(setName);
    if (!ruleSet) {
      throw new Error(`Rule set '${setName}' not found`);
    }
    
    return ruleSet.getRules();
  }
  
  /**
   * 将所有规则注册到引擎
   */
  static registerAllToEngine(registry: RuleRegistry | { registerRule: (rule: Rule, quiet?: boolean) => void }): void {
    const allRules = this.getAllRules();
    
    for (const rule of allRules) {
      try {
        registry.registerRule(rule);
      } catch (error) {
        console.error(`Failed to register rule '${rule.id}':`, error);
      }
    }
    
    console.log(`Registered ${allRules.length} rules to engine`);
  }
  
  /**
   * 将指定规则集注册到引擎
   */
  static registerSetToEngine(registry: RuleRegistry, setName: string): void {
    const rules = this.getRulesBySet(setName);
    
    for (const rule of rules) {
      try {
        registry.registerRule(rule);
      } catch (error) {
        console.error(`Failed to register rule '${rule.id}' from set '${setName}':`, error);
      }
    }
    
    console.log(`Registered ${rules.length} rules from '${setName}' set to engine`);
  }
  
  /**
   * 获取规则集统计信息
   */
  static getStatistics(): RuleSetStats {
    const rulesBySet = new Map<string, number>();
    let totalRules = 0;
    let enabledRules = 0;
    
    for (const [name, ruleSet] of this.registeredSets) {
      try {
        const rules = ruleSet.getRules();
        rulesBySet.set(name, rules.length);
        totalRules += rules.length;
        enabledRules += rules.length; // 简化版本：假设所有规则都启用
      } catch (error) {
        rulesBySet.set(name, 0);
        console.error(`Failed to get statistics for rule set '${name}':`, error);
      }
    }
    
    return {
      totalRuleSets: this.registeredSets.size,
      totalRules,
      rulesBySet,
      enabledRules,
      disabledRules: totalRules - enabledRules,
    };
  }
  
  /**
   * 验证规则集
   */
  static validateRuleSet(): ValidationResult {
    const issues: string[] = [];
    const warnings: string[] = [];
    
    // 检查是否有注册的规则集
    if (this.registeredSets.size === 0) {
      issues.push('No rule sets registered');
      return { isValid: false, issues, warnings };
    }
    
    // 验证每个规则集
    for (const [name, ruleSet] of this.registeredSets) {
      try {
        const rules = ruleSet.getRules();
        
        if (rules.length === 0) {
          warnings.push(`Rule set '${name}' has no rules`);
          continue;
        }
        
        // 检查规则ID唯一性
        const ruleIds = new Set<string>();
        for (const rule of rules) {
          if (ruleIds.has(rule.id)) {
            issues.push(`Duplicate rule ID '${rule.id}' in rule set '${name}'`);
          } else {
            ruleIds.add(rule.id);
          }
          
          // 检查规则基本属性
          if (!rule.name || rule.name.trim().length === 0) {
            issues.push(`Rule '${rule.id}' has empty name`);
          }
          
          if (rule.priority < 0 || rule.priority > 2000) {
            warnings.push(`Rule '${rule.id}' has unusual priority: ${rule.priority}`);
          }
        }
        
      } catch (error) {
        issues.push(`Failed to validate rule set '${name}': ${error}`);
      }
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      warnings,
    };
  }
  
  /**
   * 列出所有已注册的规则集
   */
  static listRuleSets(): Array<{ name: string; description: string; ruleCount: number }> {
    const result: Array<{ name: string; description: string; ruleCount: number }> = [];
    
    for (const [name, ruleSet] of this.registeredSets) {
      try {
        const description = ruleSet.getDescription();
        const ruleCount = ruleSet.getRules().length;
        result.push({ name, description, ruleCount });
      } catch (error) {
        result.push({ 
          name, 
          description: `Error: ${error}`, 
          ruleCount: 0 
        });
      }
    }
    
    return result;
  }
  
  /**
   * 清除所有规则集（主要用于测试）
   */
  static clearAll(): void {
    this.registeredSets.clear();
    console.log('All rule sets cleared');
  }
  
  /**
   * 重置为默认规则集
   */
  static resetToDefaults(): void {
    this.clearAll();
    this.registeredSets.set('core', CoreRuleSet);
    this.registeredSets.set('jsx', JSXRuleSet);
    console.log('Rule sets reset to defaults');
  }
}