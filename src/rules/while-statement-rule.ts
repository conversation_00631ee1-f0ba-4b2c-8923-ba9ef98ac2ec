/**
 * While语句复杂度规则实现
 * 处理while和do-while循环语句的复杂度计算
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * While语句复杂度规则
 * 统一处理while和do-while循环语句的复杂度计算
 */
export class WhileStatementRule extends BaseRule {
  readonly id = 'while-statement';
  readonly name = 'While Statement Complexity';
  readonly priority = 500; // 高优先级

  canHandle(node: Node): boolean {
    return node.type === 'WhileStatement' ||
           node.type === 'DoWhileStatement';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const whileNode = node as any;
      
      // 基础复杂度：每个循环都增加1点复杂度
      const baseComplexity = 1;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(baseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateWhileStatementReason(whileNode, baseComplexity, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateWhileStatementSuggestions(whileNode, finalComplexity, context);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        true, // while循环增加嵌套层级
        suggestions,
        {
          nodeType: node.type,
          baseComplexity,
          nestingLevel: context.nestingLevel,
          loopType: this.getLoopType(node.type),
          hasComplexCondition: this.hasComplexCondition(whileNode),
          isDoWhile: node.type === 'DoWhileStatement',
          conditionComplexity: this.calculateConditionComplexity(whileNode),
        }
      );
    });
  }

  /**
   * 获取循环类型的描述
   */
  private getLoopType(nodeType: string): string {
    switch (nodeType) {
      case 'WhileStatement':
        return 'while-loop';
      case 'DoWhileStatement':
        return 'do-while-loop';
      default:
        return 'unknown-loop';
    }
  }

  /**
   * 检查是否有复杂的循环条件
   */
  private hasComplexCondition(whileNode: any): boolean {
    const test = whileNode.test;
    if (!test) {
      return false;
    }

    // 检查是否包含逻辑运算符
    if (this.containsLogicalOperators(test)) {
      return true;
    }

    // 检查是否包含函数调用
    if (this.containsFunctionCalls(test)) {
      return true;
    }

    // 检查是否包含复杂的比较或计算
    if (this.containsComplexExpressions(test)) {
      return true;
    }

    // 检查是否包含赋值操作（在条件中赋值是复杂的）
    if (this.containsAssignmentInCondition(test)) {
      return true;
    }

    return false;
  }

  /**
   * 计算条件的复杂度分数
   * 返回一个表示条件复杂度的数值
   */
  private calculateConditionComplexity(whileNode: any): number {
    const test = whileNode.test;
    if (!test) {
      return 0;
    }

    let complexity = 0;

    // 逻辑运算符增加复杂度
    complexity += this.countLogicalOperators(test);

    // 函数调用增加复杂度
    complexity += this.countFunctionCalls(test);

    // 复杂表达式增加复杂度
    if (this.containsComplexExpressions(test)) {
      complexity += 1;
    }

    // 条件中的赋值大幅增加复杂度
    if (this.containsAssignmentInCondition(test)) {
      complexity += 2;
    }

    return complexity;
  }

  /**
   * 检查节点是否包含逻辑运算符
   */
  private containsLogicalOperators(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查当前节点
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') &&
        ['&&', '||'].includes(node.operator)) {
      return true;
    }

    // 递归检查子节点
    return this.traverseAndCheck(node, this.containsLogicalOperators.bind(this));
  }

  /**
   * 计算逻辑运算符的数量
   */
  private countLogicalOperators(node: any): number {
    if (!node || typeof node !== 'object') {
      return 0;
    }

    let count = 0;

    // 检查当前节点
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') &&
        ['&&', '||'].includes(node.operator)) {
      count += 1;
    }

    // 递归计算子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        count += value.reduce((sum, child) => sum + this.countLogicalOperators(child), 0);
      } else if (value && typeof value === 'object') {
        count += this.countLogicalOperators(value);
      }
    }

    return count;
  }

  /**
   * 检查节点是否包含函数调用
   */
  private containsFunctionCalls(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    if (node.type === 'CallExpression') {
      return true;
    }

    return this.traverseAndCheck(node, this.containsFunctionCalls.bind(this));
  }

  /**
   * 计算函数调用的数量
   */
  private countFunctionCalls(node: any): number {
    if (!node || typeof node !== 'object') {
      return 0;
    }

    let count = 0;

    if (node.type === 'CallExpression') {
      count += 1;
    }

    // 递归计算子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        count += value.reduce((sum, child) => sum + this.countFunctionCalls(child), 0);
      } else if (value && typeof value === 'object') {
        count += this.countFunctionCalls(value);
      }
    }

    return count;
  }

  /**
   * 检查节点是否包含复杂表达式
   */
  private containsComplexExpressions(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查是否包含成员表达式链
    if (node.type === 'MemberExpression' && this.isMemberExpressionChain(node)) {
      return true;
    }

    // 检查是否包含复杂的二元表达式
    if (node.type === 'BinaryExpression' && this.isComplexBinaryExpression(node)) {
      return true;
    }

    // 检查是否包含条件表达式（三元运算符）
    if (node.type === 'ConditionalExpression') {
      return true;
    }

    // 检查是否包含数组或对象字面量
    if (node.type === 'ArrayExpression' || node.type === 'ObjectExpression') {
      return true;
    }

    return this.traverseAndCheck(node, this.containsComplexExpressions.bind(this));
  }

  /**
   * 检查条件中是否包含赋值操作
   */
  private containsAssignmentInCondition(node: any): boolean {
    if (!node || typeof node !== 'object') {
      return false;
    }

    // 检查赋值表达式
    if (node.type === 'AssignmentExpression') {
      return true;
    }

    // 检查更新表达式（++, --）
    if (node.type === 'UpdateExpression') {
      return true;
    }

    return this.traverseAndCheck(node, this.containsAssignmentInCondition.bind(this));
  }

  /**
   * 通用的节点遍历和检查方法
   */
  private traverseAndCheck(node: any, checkFunction: (node: any) => boolean): boolean {
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(child => checkFunction(child))) {
          return true;
        }
      } else if (value && typeof value === 'object') {
        if (checkFunction(value)) {
          return true;
        }
      }
    }
    
    return false;
  }

  /**
   * 检查是否为成员表达式链
   */
  private isMemberExpressionChain(node: any): boolean {
    if (node.type !== 'MemberExpression') {
      return false;
    }

    let depth = 0;
    let current = node;

    while (current && current.type === 'MemberExpression') {
      depth++;
      current = current.object;
    }

    return depth > 2; // 超过2层的成员访问认为是复杂的
  }

  /**
   * 检查是否为复杂的二元表达式
   */
  private isComplexBinaryExpression(node: any): boolean {
    if (node.type !== 'BinaryExpression') {
      return false;
    }

    // 检查是否包含复杂的操作符
    const complexOperators = ['instanceof', 'in'];
    if (complexOperators.includes(node.operator)) {
      return true;
    }

    // 检查操作数是否复杂
    return this.containsComplexExpressions(node.left) || 
           this.containsComplexExpressions(node.right);
  }

  /**
   * 生成while语句的复杂度说明
   */
  private generateWhileStatementReason(
    whileNode: any,
    baseComplexity: number,
    finalComplexity: number,
    context: AnalysisContext
  ): string {
    const loopType = this.getLoopTypeName(whileNode.type);
    const nestingPenalty = finalComplexity - baseComplexity;
    
    let reason = `${loopType} increases cognitive complexity by ${finalComplexity}`;
    
    if (nestingPenalty > 0) {
      reason += ` (nesting penalty: +${nestingPenalty})`;
    }

    // 添加额外的复杂度信息
    if (this.hasComplexCondition(whileNode)) {
      const conditionComplexity = this.calculateConditionComplexity(whileNode);
      reason += ` (complex condition detected, complexity: ${conditionComplexity})`;
    }

    // do-while特殊说明
    if (whileNode.type === 'DoWhileStatement') {
      reason += ` (do-while executes body at least once)`;
    }
    
    return reason;
  }

  /**
   * 获取循环类型的友好名称
   */
  private getLoopTypeName(nodeType: string): string {
    switch (nodeType) {
      case 'WhileStatement':
        return 'While loop';
      case 'DoWhileStatement':
        return 'Do-while loop';
      default:
        return 'Loop';
    }
  }

  /**
   * 生成while语句特定的建议
   */
  private generateWhileStatementSuggestions(
    whileNode: any,
    complexity: number,
    context: AnalysisContext
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    const loopType = this.getLoopTypeName(whileNode.type);
    
    // 基础复杂度建议
    const basicSuggestions = this.generateSuggestions(loopType.toLowerCase(), complexity);
    suggestions.push(...basicSuggestions);
    
    // 嵌套建议
    const nestingSuggestions = this.generateNestingSuggestions(loopType.toLowerCase(), context.nestingLevel);
    suggestions.push(...nestingSuggestions);
    
    // 复杂条件建议
    if (this.hasComplexCondition(whileNode)) {
      const conditionComplexity = this.calculateConditionComplexity(whileNode);
      
      suggestions.push({
        type: 'refactor',
        message: `Complex loop condition detected (complexity: ${conditionComplexity}). Consider extracting condition logic into a separate function.`,
        codeExample: `// Instead of complex condition:\nwhile (isValid(data) && data.length > 0 && !stopped && checkStatus()) {\n  // ...\n}\n\n// Extract to function:\nfunction shouldContinueLoop(data, stopped) {\n  return isValid(data) && data.length > 0 && !stopped && checkStatus();\n}\n\nwhile (shouldContinueLoop(data, stopped)) {\n  // ...\n}`,
        priority: conditionComplexity > 2 ? 'high' : 'medium',
      });
    }

    // 条件中赋值的警告
    if (this.containsAssignmentInCondition(whileNode.test)) {
      suggestions.push({
        type: 'warning',
        message: 'Assignment in loop condition detected. This significantly increases cognitive complexity and can be error-prone.',
        codeExample: `// Avoid assignment in condition:\nwhile ((line = reader.readLine()) != null) {\n  // ...\n}\n\n// Prefer explicit assignment:\nline = reader.readLine();\nwhile (line != null) {\n  // ...\n  line = reader.readLine();\n}`,
        priority: 'high',
      });
    }

    // 函数调用在条件中的建议
    if (this.countFunctionCalls(whileNode.test) > 1) {
      suggestions.push({
        type: 'optimize',
        message: 'Multiple function calls in loop condition. Consider caching results or extracting condition logic.',
        codeExample: `// Instead of multiple calls:\nwhile (isValid(item) && hasPermission(user) && isActive(session)) {\n  // ...\n}\n\n// Cache results:\nconst canProcess = isValid(item) && hasPermission(user) && isActive(session);\nwhile (canProcess) {\n  // ... update canProcess as needed\n}`,
        priority: 'medium',
      });
    }

    // do-while特定建议
    if (whileNode.type === 'DoWhileStatement') {
      suggestions.push({
        type: 'info',
        message: 'Do-while loops execute the body at least once. Ensure this behavior is intentional.',
        codeExample: `// Do-while guarantees at least one execution:\ndo {\n  // This code runs at least once\n  process();\n} while (condition);\n\n// Regular while might not execute at all:\nwhile (condition) {\n  // This might never run\n  process();\n}`,
        priority: 'low',
      });
    }

    // 深度嵌套建议
    if (context.nestingLevel > 2) {
      suggestions.push({
        type: 'refactor',
        message: 'Consider extracting nested loop logic into separate functions to reduce cognitive complexity.',
        codeExample: `// Instead of deeply nested loops:\nwhile (hasData()) {\n  if (condition) {\n    while (hasSubData()) {\n      // complex logic\n    }\n  }\n}\n\n// Extract to function:\nfunction processSubData() {\n  while (hasSubData()) {\n    // complex logic\n  }\n}\n\nwhile (hasData()) {\n  if (condition) {\n    processSubData();\n  }\n}`,
        priority: 'medium',
      });
    }

    // 无限循环警告
    if (this.isPotentialInfiniteLoop(whileNode)) {
      suggestions.push({
        type: 'warning',
        message: 'Potential infinite loop detected. Ensure loop condition can become false and consider adding safety mechanisms.',
        codeExample: `// Add safety counter for complex loops:\nlet iterations = 0;\nconst MAX_ITERATIONS = 1000;\n\nwhile (condition && iterations < MAX_ITERATIONS) {\n  // loop body\n  iterations++;\n  \n  // Ensure condition can change\n  updateConditionVariables();\n}\n\nif (iterations >= MAX_ITERATIONS) {\n  console.warn('Loop exceeded maximum iterations');\n}`,
        priority: 'high',
      });
    }
    
    return suggestions;
  }

  /**
   * 检查是否可能是无限循环
   * 简单的启发式检查
   */
  private isPotentialInfiniteLoop(whileNode: any): boolean {
    const test = whileNode.test;
    if (!test) {
      return false;
    }

    // 如果条件是字面量true，可能是无限循环
    if (test.type === 'BooleanLiteral' && test.value === true) {
      return true;
    }

    // 如果条件是数字字面量且为真值，可能是无限循环
    if (test.type === 'NumericLiteral' && test.value !== 0) {
      return true;
    }

    // 如果条件只是一个标识符且没有明显的修改，可能有风险
    if (test.type === 'Identifier' && !this.containsAssignmentInCondition(whileNode.test)) {
      return true;
    }

    return false;
  }
}