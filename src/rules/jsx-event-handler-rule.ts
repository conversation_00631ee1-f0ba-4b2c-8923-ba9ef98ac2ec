/**
 * JSX事件处理器复杂度分析规则
 * 分析内联事件处理器vs引用处理器的复杂度
 * 检测事件委托和性能反模式
 * 生成事件处理优化建议
 */

import type { Node } from '@swc/core';
import type { Rule, RuleResult, AnalysisContext, Suggestion } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * 事件处理器类型
 */
export type EventHandlerType = 'inline' | 'reference' | 'arrow' | 'bound' | 'async';

/**
 * 事件处理器模式
 */
export type EventHandlerPattern = 
  | 'simple-inline'      // 简单内联: onClick={() => setCount(c => c + 1)}
  | 'complex-inline'     // 复杂内联: onClick={() => { /* 多行逻辑 */ }}
  | 'function-reference' // 函数引用: onClick={handleClick}
  | 'bound-method'       // 绑定方法: onClick={this.handleClick.bind(this)}
  | 'async-handler'      // 异步处理: onClick={async () => { await api.call() }}
  | 'delegated'          // 事件委托: onClick={e => handleGenericClick(e, 'specific')}
  | 'performance-anti';  // 性能反模式: onClick={() => expensiveCalculation()}

/**
 * 事件处理器分析结果
 */
export interface EventHandlerAnalysis {
  handlerType: EventHandlerType;
  pattern: EventHandlerPattern;
  complexity: number;
  isOptimal: boolean;
  hasPerformanceIssues: boolean;
  suggestions: Suggestion[];
  eventType: string;
  nodeInfo: {
    isInline: boolean;
    hasMultipleStatements: boolean;
    hasAsyncOperations: boolean;
    hasNestedFunctions: boolean;
    stateUpdates: number;
    apiCalls: number;
  };
}

/**
 * JSX事件处理器分析规则
 */
export class JSXEventHandlerRule extends BaseRule {
  readonly id = 'jsx.event.handler';
  readonly name = 'JSX Event Handler Analysis';
  readonly priority = 750; // 中高优先级

  // JSX属性中的事件处理器模式
  private readonly EVENT_ATTRIBUTES = new Set([
    'onClick', 'onDoubleClick', 'onMouseDown', 'onMouseUp', 'onMouseMove',
    'onMouseEnter', 'onMouseLeave', 'onMouseOver', 'onMouseOut',
    'onKeyDown', 'onKeyUp', 'onKeyPress',
    'onFocus', 'onBlur', 'onChange', 'onInput', 'onSubmit',
    'onTouchStart', 'onTouchMove', 'onTouchEnd', 'onTouchCancel',
    'onScroll', 'onWheel', 'onResize', 'onLoad', 'onError'
  ]);

  canHandle(node: Node): boolean {
    // 处理JSX属性中的事件处理器
    return node.type === 'JSXExpressionContainer' || 
           node.type === 'ArrowFunctionExpression' ||
           node.type === 'FunctionExpression' ||
           (node.type === 'JSXAttribute' && this.isEventAttribute(node));
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // 检查JSX配置是否启用事件处理器分析
      if (!context.config.rules.jsx.enabled || 
          !context.config.rules.jsx.scoring.eventHandlers) {
        return this.createNonExemptionResult(node, 'Event handler analysis disabled');
      }

      const analysis = await this.analyzeEventHandler(node, context);
      
      if (!analysis) {
        return this.createNonExemptionResult(node, 'Not an event handler');
      }

      // 根据分析结果计算复杂度
      const complexity = this.calculateEventHandlerComplexity(analysis, context);
      
      // 生成建议
      const suggestions = this.generateEventHandlerSuggestions(analysis);

      return this.createComplexityResult(
        node,
        complexity,
        `Event handler: ${analysis.pattern} (${analysis.handlerType})`,
        false, // 事件处理器通常不增加嵌套层级
        suggestions,
        {
          handlerType: analysis.handlerType,
          pattern: analysis.pattern,
          eventType: analysis.eventType,
          isOptimal: analysis.isOptimal,
          hasPerformanceIssues: analysis.hasPerformanceIssues,
          nodeInfo: analysis.nodeInfo
        }
      );
    });
  }

  /**
   * 检查JSX属性是否为事件属性
   */
  private isEventAttribute(node: any): boolean {
    if (node.type === 'JSXAttribute' && node.name && node.name.type === 'Identifier') {
      const attrName = node.name.value || node.name.name;
      return this.EVENT_ATTRIBUTES.has(attrName);
    }
    return false;
  }

  /**
   * 分析事件处理器
   */
  private async analyzeEventHandler(
    node: Node, 
    context: AnalysisContext
  ): Promise<EventHandlerAnalysis | null> {
    let handlerNode: any = null;
    let eventType = 'unknown';

    // 根据节点类型找到实际的处理器节点
    if (node.type === 'JSXAttribute' && this.isEventAttribute(node)) {
      eventType = this.extractEventType(node);
      handlerNode = (node as any).value?.expression;
    } else if (node.type === 'JSXExpressionContainer') {
      handlerNode = (node as any).expression;
    } else if (this.isEventHandlerFunction(node)) {
      handlerNode = node;
    }

    if (!handlerNode) {
      return null;
    }

    // 分析处理器类型和模式
    const handlerType = this.determineHandlerType(handlerNode);
    const pattern = this.determineHandlerPattern(handlerNode, context);
    const nodeInfo = this.analyzeHandlerNode(handlerNode, context);
    
    // 计算基础复杂度
    const baseComplexity = await this.calculateBaseComplexity(handlerNode, context);
    
    // 检查性能问题
    const hasPerformanceIssues = this.detectPerformanceIssues(handlerNode, nodeInfo);
    const isOptimal = this.evaluateOptimality(pattern, nodeInfo, hasPerformanceIssues);

    return {
      handlerType,
      pattern,
      complexity: baseComplexity,
      isOptimal,
      hasPerformanceIssues,
      suggestions: [],
      eventType,
      nodeInfo
    };
  }

  /**
   * 提取事件类型
   */
  private extractEventType(node: any): string {
    if (node.name && node.name.type === 'Identifier') {
      const attrName = node.name.value || node.name.name;
      return attrName.replace(/^on/, '').toLowerCase();
    }
    return 'unknown';
  }

  /**
   * 检查节点是否为事件处理器函数
   */
  private isEventHandlerFunction(node: Node): boolean {
    return node.type === 'ArrowFunctionExpression' || 
           node.type === 'FunctionExpression' ||
           (node.type === 'Identifier' && this.looksLikeEventHandler(node));
  }

  /**
   * 检查标识符是否看起来像事件处理器
   */
  private looksLikeEventHandler(node: any): boolean {
    const name = node.value || node.name;
    return typeof name === 'string' && /^(handle|on)[A-Z]/.test(name);
  }

  /**
   * 确定处理器类型
   */
  private determineHandlerType(node: any): EventHandlerType {
    switch (node.type) {
      case 'ArrowFunctionExpression':
        return node.async ? 'async' : 'arrow';
      case 'FunctionExpression':
        return node.async ? 'async' : 'inline';
      case 'Identifier':
        return 'reference';
      case 'MemberExpression':
        return 'bound';
      case 'CallExpression':
        // 检查是否为bind调用
        if (node.callee && node.callee.type === 'MemberExpression' && 
            node.callee.property && node.callee.property.name === 'bind') {
          return 'bound';
        }
        return 'reference';
      default:
        return 'reference';
    }
  }

  /**
   * 确定处理器模式
   */
  private determineHandlerPattern(node: any, context: AnalysisContext): EventHandlerPattern {
    const handlerType = this.determineHandlerType(node);
    
    switch (handlerType) {
      case 'reference':
        return 'function-reference';
        
      case 'bound':
        return 'bound-method';
        
      case 'async':
        return 'async-handler';
        
      case 'arrow':
      case 'inline':
        // 分析内联函数的复杂度
        const nodeInfo = this.analyzeHandlerNode(node, context);
        
        if (nodeInfo.hasMultipleStatements || nodeInfo.hasNestedFunctions) {
          return 'complex-inline';
        }
        
        if (this.isDelegatedPattern(node)) {
          return 'delegated';
        }
        
        if (this.hasPerformanceAntiPatterns(node)) {
          return 'performance-anti';
        }
        
        return 'simple-inline';
        
      default:
        return 'simple-inline';
    }
  }

  /**
   * 分析处理器节点信息
   */
  private analyzeHandlerNode(node: any, context: AnalysisContext): EventHandlerAnalysis['nodeInfo'] {
    const info = {
      isInline: false,
      hasMultipleStatements: false,
      hasAsyncOperations: false,
      hasNestedFunctions: false,
      stateUpdates: 0,
      apiCalls: 0
    };

    if (node.type === 'ArrowFunctionExpression' || node.type === 'FunctionExpression') {
      info.isInline = true;
      
      // 分析函数体
      const body = node.body;
      if (body) {
        if (body.type === 'BlockStatement') {
          info.hasMultipleStatements = (body.stmts || body.body || []).length > 1;
        }
        
        // 递归分析内容（简化版本）
        this.analyzeNodeContent(body, info);
      }
    }

    return info;
  }

  /**
   * 分析节点内容（递归）
   */
  private analyzeNodeContent(node: any, info: EventHandlerAnalysis['nodeInfo']): void {
    if (!node) return;

    // 检查异步操作
    if (node.type === 'AwaitExpression' || 
        (node.type === 'CallExpression' && this.isAsyncCall(node))) {
      info.hasAsyncOperations = true;
    }

    // 检查嵌套函数
    if (node.type === 'ArrowFunctionExpression' || 
        node.type === 'FunctionExpression' ||
        node.type === 'FunctionDeclaration') {
      info.hasNestedFunctions = true;
    }

    // 检查状态更新
    if (this.isStateUpdate(node)) {
      info.stateUpdates++;
    }

    // 检查API调用
    if (this.isApiCall(node)) {
      info.apiCalls++;
    }

    // 递归处理子节点（简化版本）
    if (node.body) {
      if (Array.isArray(node.body)) {
        node.body.forEach((child: any) => this.analyzeNodeContent(child, info));
      } else {
        this.analyzeNodeContent(node.body, info);
      }
    }

    if (node.stmts) {
      node.stmts.forEach((stmt: any) => this.analyzeNodeContent(stmt, info));
    }
  }

  /**
   * 检查是否为异步调用
   */
  private isAsyncCall(node: any): boolean {
    if (node.type === 'CallExpression' && node.callee) {
      const callee = node.callee;
      
      // 检查常见的异步API模式
      if (callee.type === 'MemberExpression') {
        const methodName = callee.property?.name || callee.property?.value;
        return ['fetch', 'then', 'catch', 'finally', 'post', 'get', 'put', 'delete'].includes(methodName);
      }
    }
    return false;
  }

  /**
   * 检查是否为状态更新
   */
  private isStateUpdate(node: any): boolean {
    if (node.type === 'CallExpression' && node.callee) {
      const callee = node.callee;
      
      // 检查React setState模式
      if (callee.type === 'Identifier') {
        const name = callee.name || callee.value;
        return typeof name === 'string' && /^set[A-Z]/.test(name);
      }
    }
    return false;
  }

  /**
   * 检查是否为API调用
   */
  private isApiCall(node: any): boolean {
    return this.isAsyncCall(node); // 简化版本，可以扩展
  }

  /**
   * 检查是否为委托模式
   */
  private isDelegatedPattern(node: any): boolean {
    // 检查是否有通用处理器模式
    if (node.type === 'ArrowFunctionExpression' && node.body) {
      const body = node.body;
      
      // 检查是否调用了通用处理器并传递参数
      if (body.type === 'CallExpression' && body.arguments && body.arguments.length > 1) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查性能反模式
   */
  private hasPerformanceAntiPatterns(node: any): boolean {
    // 检查常见的性能问题模式
    if (node.type === 'ArrowFunctionExpression' && node.body) {
      return this.hasExpensiveOperations(node.body);
    }
    return false;
  }

  /**
   * 检查是否有昂贵操作
   */
  private hasExpensiveOperations(node: any): boolean {
    if (!node) return false;

    // 检查循环操作
    if (['ForStatement', 'WhileStatement', 'DoWhileStatement', 'ForInStatement', 'ForOfStatement'].includes(node.type)) {
      return true;
    }

    // 检查数组方法链
    if (node.type === 'CallExpression' && node.callee && node.callee.type === 'MemberExpression') {
      const methodName = node.callee.property?.name || node.callee.property?.value;
      if (['map', 'filter', 'reduce', 'sort', 'find'].includes(methodName)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 计算基础复杂度
   */
  private async calculateBaseComplexity(node: any, context: AnalysisContext): Promise<number> {
    let complexity = 0;

    // 内联函数基础复杂度
    if (node.type === 'ArrowFunctionExpression' || node.type === 'FunctionExpression') {
      complexity += 1;

      // 异步处理器额外复杂度
      if (node.async) {
        complexity += 1;
      }

      // 多语句额外复杂度
      const body = node.body;
      if (body && body.type === 'BlockStatement') {
        const statements = body.stmts || body.body || [];
        if (statements.length > 1) {
          complexity += Math.min(statements.length - 1, 3); // 最多增加3点
        }
      }
    }

    return complexity;
  }

  /**
   * 检测性能问题
   */
  private detectPerformanceIssues(node: any, nodeInfo: EventHandlerAnalysis['nodeInfo']): boolean {
    return nodeInfo.hasNestedFunctions || 
           this.hasExpensiveOperations(node) ||
           (nodeInfo.isInline && nodeInfo.hasMultipleStatements);
  }

  /**
   * 评估优化程度
   */
  private evaluateOptimality(
    pattern: EventHandlerPattern, 
    nodeInfo: EventHandlerAnalysis['nodeInfo'],
    hasPerformanceIssues: boolean
  ): boolean {
    // 函数引用通常是最优的
    if (pattern === 'function-reference') {
      return true;
    }

    // 简单内联可以接受
    if (pattern === 'simple-inline' && !hasPerformanceIssues) {
      return true;
    }

    // 其他模式通常不是最优的
    return false;
  }

  /**
   * 计算事件处理器复杂度
   */
  private calculateEventHandlerComplexity(
    analysis: EventHandlerAnalysis, 
    context: AnalysisContext
  ): number {
    let complexity = analysis.complexity;

    // 根据模式调整复杂度
    switch (analysis.pattern) {
      case 'simple-inline':
        // 简单内联处理器相对简单
        break;
        
      case 'complex-inline':
        // 复杂内联处理器增加复杂度
        complexity += 2;
        break;
        
      case 'performance-anti':
        // 性能反模式严重增加复杂度
        complexity += 3;
        break;
        
      case 'async-handler':
        // 异步处理器增加复杂度
        complexity += 1;
        break;
        
      case 'delegated':
        // 委托模式略微增加复杂度
        complexity += 1;
        break;
    }

    // 根据节点信息调整
    if (analysis.nodeInfo.hasNestedFunctions) {
      complexity += 2;
    }

    if (analysis.nodeInfo.stateUpdates > 1) {
      complexity += analysis.nodeInfo.stateUpdates - 1;
    }

    if (analysis.nodeInfo.apiCalls > 0) {
      complexity += analysis.nodeInfo.apiCalls;
    }

    return Math.max(0, complexity);
  }

  /**
   * 生成事件处理器建议
   */
  private generateEventHandlerSuggestions(analysis: EventHandlerAnalysis): Suggestion[] {
    const suggestions: Suggestion[] = [];

    switch (analysis.pattern) {
      case 'complex-inline':
        suggestions.push({
          type: 'refactor',
          message: 'Consider extracting this complex inline event handler to a separate function',
          codeExample: `// Instead of:\n<button onClick={() => {\n  // complex logic\n}}>\n\n// Use:\nconst handleClick = () => {\n  // complex logic\n};\n<button onClick={handleClick}>`,
          priority: 'medium'
        });
        break;

      case 'performance-anti':
        suggestions.push({
          type: 'optimize',
          message: 'This inline handler contains expensive operations that will run on every render',
          codeExample: `// Use useCallback to memoize the handler:\nconst handleClick = useCallback(() => {\n  // expensive operations\n}, [dependencies]);`,
          priority: 'high'
        });
        break;

      case 'bound-method':
        suggestions.push({
          type: 'optimize',
          message: 'Consider using arrow functions or useCallback instead of .bind()',
          codeExample: `// Instead of: onClick={this.handleClick.bind(this)}\n// Use: onClick={() => this.handleClick()} or useCallback`,
          priority: 'medium'
        });
        break;

      case 'async-handler':
        if (analysis.nodeInfo.hasMultipleStatements) {
          suggestions.push({
            type: 'refactor',
            message: 'Complex async event handlers should be extracted to separate functions',
            priority: 'medium'
          });
        }
        break;
    }

    // 性能问题建议
    if (analysis.hasPerformanceIssues) {
      suggestions.push({
        type: 'warning',
        message: 'This event handler may cause performance issues due to inline definition',
        codeExample: `// Consider using useCallback:\nconst optimizedHandler = useCallback(() => {\n  // handler logic\n}, [dependencies]);`,
        priority: 'high'
      });
    }

    // 状态更新建议
    if (analysis.nodeInfo.stateUpdates > 1) {
      suggestions.push({
        type: 'optimize',
        message: 'Multiple state updates can be batched for better performance',
        priority: 'medium'
      });
    }

    return suggestions;
  }
}