/**
 * If语句复杂度规则实现
 * 处理if语句及其else-if链的复杂度计算
 */

import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../engine/types';
import { BaseRule } from './base-rule';

/**
 * If语句复杂度规则
 * 处理if语句、else-if链和嵌套if语句的复杂度计算
 */
export class IfStatementRule extends BaseRule {
  readonly id = 'if-statement';
  readonly name = 'If Statement Complexity';
  readonly priority = 400; // 最高优先级

  canHandle(node: Node): boolean {
    return node.type === 'IfStatement';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const ifNode = node as any;
      
      // 基础复杂度：每个if语句都增加1点复杂度
      const baseComplexity = 1;
      
      // 计算else-if链的额外复杂度
      const elseIfComplexity = this.calculateElseIfChainComplexity(ifNode);
      
      // 总基础复杂度
      const totalBaseComplexity = baseComplexity + elseIfComplexity;
      
      // 应用嵌套惩罚
      const finalComplexity = this.applyNestingPenalty(totalBaseComplexity, context.nestingLevel);
      
      // 生成原因说明
      const reason = this.generateIfStatementReason(ifNode, totalBaseComplexity, finalComplexity, context);
      
      // 生成建议
      const suggestions = this.generateIfStatementSuggestions(ifNode, finalComplexity, context);
      
      return this.createComplexityResult(
        node,
        finalComplexity,
        reason,
        true, // if语句增加嵌套层级
        suggestions,
        {
          nodeType: node.type,
          baseComplexity: totalBaseComplexity,
          nestingLevel: context.nestingLevel,
          hasElseIf: elseIfComplexity > 0,
          elseIfCount: this.countElseIfStatements(ifNode),
          hasElseBlock: this.hasElseBlock(ifNode),
        }
      );
    });
  }

  /**
   * 计算else-if链的复杂度
   * 每个else-if都会增加额外的复杂度
   */
  private calculateElseIfChainComplexity(ifNode: any): number {
    let complexity = 0;
    let currentNode = ifNode;
    
    // 遍历else-if链
    while (currentNode.alternate) {
      const alternate = currentNode.alternate;
      
      // 如果else块是另一个if语句，则是else-if
      if (alternate.type === 'IfStatement') {
        complexity += 1; // 每个else-if增加1点复杂度
        currentNode = alternate;
      } else {
        // 普通的else块，不增加复杂度
        break;
      }
    }
    
    return complexity;
  }

  /**
   * 计算else-if语句的数量
   */
  private countElseIfStatements(ifNode: any): number {
    let count = 0;
    let currentNode = ifNode;
    
    while (currentNode.alternate && currentNode.alternate.type === 'IfStatement') {
      count++;
      currentNode = currentNode.alternate;
    }
    
    return count;
  }

  /**
   * 检查是否有else块
   */
  private hasElseBlock(ifNode: any): boolean {
    if (!ifNode || !ifNode.alternate) {
      return false;
    }
    
    let currentNode = ifNode;
    
    // 遍历到else-if链的末尾
    while (currentNode.alternate && currentNode.alternate.type === 'IfStatement') {
      currentNode = currentNode.alternate;
    }
    
    // 检查最后是否有非if的else块
    return !!(currentNode.alternate && currentNode.alternate.type !== 'IfStatement');
  }

  /**
   * 生成if语句的复杂度说明
   */
  private generateIfStatementReason(
    ifNode: any, 
    baseComplexity: number, 
    finalComplexity: number, 
    context: AnalysisContext
  ): string {
    const elseIfCount = this.countElseIfStatements(ifNode);
    const hasElse = this.hasElseBlock(ifNode);
    const nestingPenalty = finalComplexity - baseComplexity;
    
    let reason = `If statement increases cognitive complexity by ${finalComplexity}`;
    
    if (elseIfCount > 0) {
      reason += ` (includes ${elseIfCount} else-if ${elseIfCount === 1 ? 'clause' : 'clauses'})`;
    }
    
    if (hasElse) {
      reason += ` (includes else clause)`;
    }
    
    if (nestingPenalty > 0) {
      reason += ` (nesting penalty: +${nestingPenalty})`;
    }
    
    return reason;
  }

  /**
   * 生成if语句特定的建议
   */
  private generateIfStatementSuggestions(
    ifNode: any, 
    complexity: number, 
    context: AnalysisContext
  ): import('../engine/types').Suggestion[] {
    const suggestions: import('../engine/types').Suggestion[] = [];
    const elseIfCount = this.countElseIfStatements(ifNode);
    
    // 基础复杂度建议
    const basicSuggestions = this.generateSuggestions('if statement', complexity);
    suggestions.push(...basicSuggestions);
    
    // 嵌套建议
    const nestingSuggestions = this.generateNestingSuggestions('if statement', context.nestingLevel);
    suggestions.push(...nestingSuggestions);
    
    // else-if链特定建议
    if (elseIfCount > 2) {
      suggestions.push({
        type: 'refactor',
        message: `Long else-if chain detected (${elseIfCount} clauses). Consider using switch statement or lookup table.`,
        codeExample: `// Instead of long else-if chain:\nif (type === 'A') {\n  // ...\n} else if (type === 'B') {\n  // ...\n} else if (type === 'C') {\n  // ...\n}\n\n// Use switch or lookup:\nswitch (type) {\n  case 'A': /* ... */ break;\n  case 'B': /* ... */ break;\n  case 'C': /* ... */ break;\n}`,
        priority: 'medium',
      });
    }
    
    if (elseIfCount > 4) {
      suggestions.push({
        type: 'warning',
        message: `Very long else-if chain (${elseIfCount} clauses). This significantly increases cognitive complexity.`,
        priority: 'high',
      });
    }
    
    // 深度嵌套建议
    if (context.nestingLevel > 2) {
      suggestions.push({
        type: 'refactor',
        message: 'Consider using early returns to reduce nesting depth in if statements.',
        codeExample: `// Instead of nested if:\nif (condition1) {\n  if (condition2) {\n    // logic\n  }\n}\n\n// Use early return:\nif (!condition1) return;\nif (!condition2) return;\n// logic`,
        priority: 'medium',
      });
    }
    
    return suggestions;
  }
}