import type { DetailStep } from './types';
import type {
  ReadonlyDetailStep,
  InternalDetailStep,
  ReadonlyFunctionContext,
  InternalFunctionContext,
  DetailStepFactory,
  FunctionContextFactory,
  ObjectPool,
  FactoryObjectPool,
  ObjectPoolStats,
  ObjectPoolOptions
} from './object-pool-types';
import {
  DefaultDetailStepFactory,
  ObjectPoolError
} from './object-pool-types';

// 重新导出类型以保持兼容性
export type { 
  ReadonlyDetailStep, 
  InternalDetailStep,
  ReadonlyFunctionContext,
  InternalFunctionContext,
  DetailStepFactory,
  FunctionContextFactory 
} from './object-pool-types';

/**
 * 类型安全的 DetailStep 对象池实现
 * 使用工厂模式重用 DetailStep 对象以减少内存分配开销
 * 外部接口提供只读访问，内部实现支持可变操作
 */
export class TypeSafeDetailStepPool implements FactoryObjectPool<ReadonlyDetailStep, InternalDetailStep> {
  private pool: InternalDetailStep[] = [];
  private readonly maxSize: number;
  private readonly factory: DetailStepFactory;
  private readonly enableValidation: boolean;
  private created: number = 0;
  private reused: number = 0;

  constructor(options: ObjectPoolOptions = {}) {
    this.maxSize = options.maxSize ?? 1000;
    this.factory = options.factory ?? new DefaultDetailStepFactory();
    this.enableValidation = options.enableValidation ?? false;
    
    // 预热对象池
    if (options.warmUpSize && options.warmUpSize > 0) {
      this.warmUp(options.warmUpSize);
    }
  }

  /**
   * 获取一个 DetailStep 对象
   * 优先从池中获取，如果池为空则创建新对象
   * @returns 只读的 DetailStep 对象
   */
  public acquire(): ReadonlyDetailStep {
    let step: InternalDetailStep;
    
    if (this.pool.length > 0) {
      step = this.pool.pop()!;
      this.reused++;
      
      // 可选验证
      if (this.enableValidation && !this.factory.validate(step as ReadonlyDetailStep)) {
        throw new ObjectPoolError(
          'Invalid object retrieved from pool',
          'DetailStepPool',
          'acquire',
          { step }
        );
      }
    } else {
      step = this.createNewStep();
      this.created++;
    }
    
    // 返回只读视图
    return step as ReadonlyDetailStep;
  }

  /**
   * 释放一个 DetailStep 对象回池中
   * 使用工厂重置对象属性并放回池中以便重用
   * @param step 要释放的 DetailStep 对象
   */
  public release(step: ReadonlyDetailStep): void {
    if (this.pool.length >= this.maxSize) {
      // 如果池已满，让对象被垃圾回收
      return;
    }

    try {
      // 类型转换为内部可变接口
      const internalStep = step as InternalDetailStep;
      
      // 使用工厂重置对象
      this.factory.reset(internalStep);
      
      // 可选验证重置后的对象
      if (this.enableValidation && !this.factory.validate(internalStep as ReadonlyDetailStep)) {
        throw new ObjectPoolError(
          'Object validation failed after reset',
          'DetailStepPool',
          'release',
          { step: internalStep }
        );
      }
      
      this.pool.push(internalStep);
    } catch (error: unknown) {
      // 重置失败时抛出详细错误
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new ObjectPoolError(
        `Failed to release object: ${errorMessage}`,
        'DetailStepPool',
        'release',
        { originalError: error, step }
      );
    }
  }

  /**
   * 获取池的当前大小
   */
  public size(): number {
    return this.pool.length;
  }

  /**
   * 清空对象池
   */
  public clear(): void {
    this.pool.length = 0;
  }

  /**
   * 获取对象池统计信息
   */
  public getStats(): ObjectPoolStats {
    const totalOperations = this.created + this.reused;
    return {
      poolSize: this.pool.length,
      maxSize: this.maxSize,
      totalCreated: this.created,
      totalReused: this.reused,
      reuseRate: totalOperations > 0 
        ? (this.reused / totalOperations * 100).toFixed(1) + '%' 
        : '0%'
    };
  }

  /**
   * 获取关联的工厂实例
   */
  public getFactory(): DetailStepFactory {
    return this.factory;
  }

  /**
   * 预热对象池
   * 预先创建一定数量的对象以减少运行时分配
   */
  public warmUp(count: number): void {
    const warmUpCount = Math.min(count, this.maxSize - this.pool.length);
    
    for (let i = 0; i < warmUpCount; i++) {
      try {
        const step = this.createNewStep();
        this.pool.push(step);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new ObjectPoolError(
          `Failed to warm up pool at item ${i}: ${errorMessage}`,
          'DetailStepPool',
          'warmUp',
          { count, currentIndex: i, originalError: error }
        );
      }
    }
  }

  /**
   * 验证池中对象的完整性
   */
  public validatePool(): boolean {
    if (!this.enableValidation) {
      return true;
    }

    return this.pool.every(step => {
      try {
        return this.factory.validate(step as ReadonlyDetailStep);
      } catch {
        return false;
      }
    });
  }

  /**
   * 创建新的 DetailStep 对象
   * 使用工厂创建类型安全的对象
   */
  private createNewStep(): InternalDetailStep {
    try {
      const step = this.factory.create();
      return step as InternalDetailStep;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new ObjectPoolError(
        `Factory failed to create new step: ${errorMessage}`,
        'DetailStepPool',
        'createNewStep',
        { originalError: error }
      );
    }
  }
}

/**
 * 兼容性包装器 - 保持现有 API 兼容
 * @deprecated 使用 TypeSafeDetailStepPool 替代
 */
export class DetailStepPool implements ObjectPool<DetailStep> {
  private typeSafePool: TypeSafeDetailStepPool;

  constructor(maxSize: number = 1000) {
    this.typeSafePool = new TypeSafeDetailStepPool({ maxSize });
  }

  public acquire(): DetailStep {
    return this.typeSafePool.acquire() as DetailStep;
  }

  public release(step: DetailStep): void {
    this.typeSafePool.release(step as ReadonlyDetailStep);
  }

  public size(): number {
    return this.typeSafePool.size();
  }

  public clear(): void {
    this.typeSafePool.clear();
  }

  public getStats() {
    return this.typeSafePool.getStats();
  }

  public warmUp(count: number): void {
    this.typeSafePool.warmUp(count);
  }
}

/**
 * 默认函数上下文工厂实现
 */
export class DefaultFunctionContextFactory implements FunctionContextFactory {
  create(): ReadonlyFunctionContext {
    const context: InternalFunctionContext = {
      name: '',
      line: 0,
      column: 0,
      complexity: 0,
      steps: [],
      nestingLevel: 0
    };
    return context as ReadonlyFunctionContext;
  }

  reset(context: InternalFunctionContext): void {
    context.name = '';
    context.line = 0;
    context.column = 0;
    context.complexity = 0;
    context.steps.length = 0; // 清空数组但保留引用
    context.nestingLevel = 0;
  }

  validate(context: ReadonlyFunctionContext): boolean {
    return (
      typeof context.name === 'string' &&
      typeof context.line === 'number' &&
      context.line >= 0 &&
      typeof context.column === 'number' &&
      context.column >= 0 &&
      typeof context.complexity === 'number' &&
      context.complexity >= 0 &&
      Array.isArray(context.steps) &&
      typeof context.nestingLevel === 'number' &&
      context.nestingLevel >= 0
    );
  }
}

/**
 * 类型安全的函数上下文对象池实现
 * 使用工厂模式重用函数上下文对象以优化嵌套函数处理
 */
export class TypeSafeFunctionContextPool implements FactoryObjectPool<ReadonlyFunctionContext, InternalFunctionContext> {
  private pool: InternalFunctionContext[] = [];
  private readonly maxSize: number;
  private readonly factory: FunctionContextFactory;
  private readonly enableValidation: boolean;
  private created: number = 0;
  private reused: number = 0;

  constructor(options: {
    maxSize?: number;
    factory?: FunctionContextFactory;
    enableValidation?: boolean;
    warmUpSize?: number;
  } = {}) {
    this.maxSize = options.maxSize ?? 100;
    this.factory = options.factory ?? new DefaultFunctionContextFactory();
    this.enableValidation = options.enableValidation ?? false;
    
    if (options.warmUpSize && options.warmUpSize > 0) {
      this.warmUp(options.warmUpSize);
    }
  }

  public acquire(): ReadonlyFunctionContext {
    let context: InternalFunctionContext;
    
    if (this.pool.length > 0) {
      context = this.pool.pop()!;
      this.reused++;
      
      if (this.enableValidation && !this.factory.validate(context as ReadonlyFunctionContext)) {
        throw new ObjectPoolError(
          'Invalid context retrieved from pool',
          'FunctionContextPool',
          'acquire',
          { context }
        );
      }
    } else {
      context = this.createNewContext();
      this.created++;
    }
    
    return context as ReadonlyFunctionContext;
  }

  public release(context: ReadonlyFunctionContext): void {
    if (this.pool.length >= this.maxSize) {
      return;
    }

    try {
      const internalContext = context as InternalFunctionContext;
      this.factory.reset(internalContext);
      
      if (this.enableValidation && !this.factory.validate(internalContext as ReadonlyFunctionContext)) {
        throw new ObjectPoolError(
          'Context validation failed after reset',
          'FunctionContextPool',
          'release',
          { context: internalContext }
        );
      }
      
      this.pool.push(internalContext);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new ObjectPoolError(
        `Failed to release context: ${errorMessage}`,
        'FunctionContextPool',
        'release',
        { originalError: error, context }
      );
    }
  }

  public size(): number {
    return this.pool.length;
  }

  public clear(): void {
    this.pool.length = 0;
  }

  public getStats(): ObjectPoolStats {
    const totalOperations = this.created + this.reused;
    return {
      poolSize: this.pool.length,
      maxSize: this.maxSize,
      totalCreated: this.created,
      totalReused: this.reused,
      reuseRate: totalOperations > 0 
        ? (this.reused / totalOperations * 100).toFixed(1) + '%' 
        : '0%'
    };
  }

  public getFactory(): DetailStepFactory {
    // 类型转换以符合接口要求
    return this.factory as unknown as DetailStepFactory;
  }

  public warmUp(count: number): void {
    const warmUpCount = Math.min(count, this.maxSize - this.pool.length);
    
    for (let i = 0; i < warmUpCount; i++) {
      try {
        const context = this.createNewContext();
        this.pool.push(context);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new ObjectPoolError(
          `Failed to warm up context pool at item ${i}: ${errorMessage}`,
          'FunctionContextPool',
          'warmUp',
          { count, currentIndex: i, originalError: error }
        );
      }
    }
  }

  public validatePool(): boolean {
    if (!this.enableValidation) {
      return true;
    }

    return this.pool.every(context => {
      try {
        return this.factory.validate(context as ReadonlyFunctionContext);
      } catch {
        return false;
      }
    });
  }

  private createNewContext(): InternalFunctionContext {
    try {
      const context = this.factory.create();
      return context as InternalFunctionContext;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new ObjectPoolError(
        `Factory failed to create new context: ${errorMessage}`,
        'FunctionContextPool',
        'createNewContext',
        { originalError: error }
      );
    }
  }
}

/**
 * 兼容性包装器 - 保持现有 API 兼容
 * @deprecated 使用 TypeSafeFunctionContextPool 替代
 */
export interface FunctionContext {
  name: string;
  line: number;
  column: number;
  complexity: number;
  steps: DetailStep[];
  nestingLevel: number;
}

export class FunctionContextPool implements ObjectPool<FunctionContext> {
  private typeSafePool: TypeSafeFunctionContextPool;

  constructor(maxSize: number = 100) {
    this.typeSafePool = new TypeSafeFunctionContextPool({ maxSize });
  }

  public acquire(): FunctionContext {
    return this.typeSafePool.acquire() as FunctionContext;
  }

  public release(context: FunctionContext): void {
    this.typeSafePool.release(context as ReadonlyFunctionContext);
  }

  public size(): number {
    return this.typeSafePool.size();
  }

  public clear(): void {
    this.typeSafePool.clear();
  }

  public getStats() {
    return this.typeSafePool.getStats();
  }

  public warmUp(count: number): void {
    this.typeSafePool.warmUp(count);
  }
}

/**
 * 全局对象池管理器
 * 统一管理所有对象池实例，使用类型安全的实现
 */
export class ObjectPoolManager {
  private static instance: ObjectPoolManager;
  private detailStepPool: TypeSafeDetailStepPool;
  private functionContextPool: TypeSafeFunctionContextPool;

  private constructor() {
    // 使用类型安全的对象池实现
    this.detailStepPool = new TypeSafeDetailStepPool({
      maxSize: 1000,
      enableValidation: false,
      warmUpSize: 50
    });
    
    this.functionContextPool = new TypeSafeFunctionContextPool({
      maxSize: 100,
      enableValidation: false,
      warmUpSize: 10
    });
  }

  public static getInstance(): ObjectPoolManager {
    if (!ObjectPoolManager.instance) {
      ObjectPoolManager.instance = new ObjectPoolManager();
    }
    return ObjectPoolManager.instance;
  }

  /**
   * 获取类型安全的 DetailStep 对象池
   */
  public getTypeSafeDetailStepPool(): TypeSafeDetailStepPool {
    return this.detailStepPool;
  }

  /**
   * 获取类型安全的 FunctionContext 对象池
   */
  public getTypeSafeFunctionContextPool(): TypeSafeFunctionContextPool {
    return this.functionContextPool;
  }

  /**
   * 获取兼容的 DetailStep 对象池（向后兼容）
   * @deprecated 使用 getTypeSafeDetailStepPool() 替代
   */
  public getDetailStepPool(): DetailStepPool {
    // 返回兼容包装器
    return new DetailStepPool(this.detailStepPool.getStats().maxSize);
  }

  /**
   * 获取兼容的 FunctionContext 对象池（向后兼容）
   * @deprecated 使用 getTypeSafeFunctionContextPool() 替代
   */
  public getFunctionContextPool(): FunctionContextPool {
    // 返回兼容包装器
    return new FunctionContextPool(this.functionContextPool.getStats().maxSize);
  }

  /**
   * 获取所有对象池的统计信息
   */
  public getAllStats() {
    return {
      detailStepPool: this.detailStepPool.getStats(),
      functionContextPool: this.functionContextPool.getStats(),
      poolValidation: {
        detailStepPoolValid: this.detailStepPool.validatePool(),
        functionContextPoolValid: this.functionContextPool.validatePool()
      }
    };
  }

  /**
   * 清空所有对象池
   */
  public clearAllPools(): void {
    this.detailStepPool.clear();
    this.functionContextPool.clear();
  }

  /**
   * 预热所有对象池
   */
  public warmUpAllPools(): void {
    this.detailStepPool.warmUp(50);
    this.functionContextPool.warmUp(10);
  }

  /**
   * 验证所有对象池的完整性
   */
  public validateAllPools(): boolean {
    return this.detailStepPool.validatePool() && 
           this.functionContextPool.validatePool();
  }

  /**
   * 重置管理器实例（主要用于测试）
   */
  public static resetInstance(): void {
    ObjectPoolManager.instance = undefined as any;
  }
}