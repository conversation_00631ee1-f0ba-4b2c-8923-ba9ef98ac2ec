import type { <PERSON><PERSON><PERSON>, Node } from '@swc/core';
import type { CalculationOptions, FunctionResult, IgnoreExemption } from './types';
import type { AsyncRuleEngine, AnalysisContext, CalculatorOptions } from '../engine/types';
import { ASTParser } from './parser';
import { DetailCollector } from './detail-collector';
import { RuleRegistry } from './rule-registry';
import { initializeRules, getNodeTypeRuleId, getLogicalOperatorRuleId } from './default-rules';
import { isObject, hasProperty, hasOperator, hasBinaryProperties, hasNodeType, isArray } from '../utils/type-guards';
import { PositionConverter } from '../utils/position-converter';
import { FunctionFinderVisitor } from './function-finder-visitor';
import { ComplexityVisitor } from './complexity-visitor';
import { CalculatorFactory, type ComponentFactory } from './calculator-factory';

export class ComplexityCalculator {
  private options: CalculationOptions;
  private nestingLevel: number = 0;
  private parser: ASTParser;
  private currentFilePath: string = '';
  private currentFunctionName: string = '';
  private currentSourceCode: string = ''; // 添加当前源代码存储
  private currentIgnoreExemptions: IgnoreExemption[] = [];
  private appliedExemptions: IgnoreExemption[] = [];
  private isInAssignment: boolean = false; // 简单的赋值上下文标志
  private debugMode: boolean = false; // 调试模式标志
  
  // 详细模式相关属性
  private detailCollector: DetailCollector | null = null;
  private detailsMode: boolean = false;
  private rulesInitialized: boolean = false;
  
  // 异步规则引擎相关属性
  private asyncRuleEngine: AsyncRuleEngine | null = null;
  private ruleEngineInitialized: boolean = false;
  
  // 性能监控相关属性
  private performanceStartTime: number = 0;
  private performanceStartMemory: number = 0;
  private detailStepCount: number = 0;

  // IoC依赖注入属性
  private factory: ComponentFactory;
  private executionPool: any;
  private performanceMonitor: any;
  private cacheManager: any;
  private pluginManager: any;
  private ruleManager: any;
  
  constructor(options: CalculationOptions = {}, factory?: ComponentFactory) {
    this.options = options;
    this.parser = new ASTParser();
    this.debugMode = options.enableDebugLog || false;
    
    // 初始化工厂和依赖
    this.factory = factory || this.createDefaultFactory(options);
    this.initializeDependencies();
    
    // 初始化详细模式
    this.detailsMode = options.enableDetails || false;
    if (this.detailsMode) {
      this.initializeDetailsMode();
    }
    
    // 异步初始化规则引擎
    this.initializeAsyncRuleEngine();
  }

  /**
   * 创建默认工厂
   * 将CalculationOptions转换为CalculatorOptions
   */
  private createDefaultFactory(options: CalculationOptions): ComponentFactory {
    const calculatorOptions: CalculatorOptions = {
      enableMonitoring: options.enableDebugLog || false,
      enableCaching: true,
      maxConcurrency: 4,
      debugMode: options.enableDebugLog || false,
      quiet: options.quiet || false,
      enableDetails: options.enableDetails || false,
      detailLevel: 'standard',
      includeMetrics: false,
    };
    
    return new CalculatorFactory(calculatorOptions);
  }

  /**
   * 初始化依赖组件
   * 使用工厂创建所需的组件实例
   */
  private initializeDependencies(): void {
    try {
      // 创建执行池（如果需要并行处理）
      this.executionPool = this.factory.createExecutionPool({
        maxConcurrency: 4,
        timeout: 5000,
        enableProfiling: this.options.enableDebugLog || false,
        isolateErrors: true,
      });

      // 创建性能监控器
      this.performanceMonitor = this.factory.createPerformanceMonitor();

      // 创建缓存管理器
      this.cacheManager = this.factory.createCacheManager(10000);

      // 创建插件管理器
      this.pluginManager = this.factory.createPluginManager([]);

      // 创建规则管理器
      this.ruleManager = this.factory.createRuleManager();

      this.logDebug('Dependencies initialized successfully', {
        hasExecutionPool: !!this.executionPool,
        hasPerformanceMonitor: !!this.performanceMonitor,
        hasCacheManager: !!this.cacheManager,
        hasPluginManager: !!this.pluginManager,
        hasRuleManager: !!this.ruleManager,
      });
    } catch (error) {
      this.logError('Failed to initialize dependencies', error);
      // 使用空对象作为回退，确保应用仍能运行
      this.initializeFallbackDependencies();
    }
  }

  /**
   * 初始化回退依赖
   * 当主要依赖初始化失败时使用
   */
  private initializeFallbackDependencies(): void {
    this.executionPool = {
      executeRules: async (rules: any[], node: any, context: any) => {
        const results = [];
        for (const rule of rules) {
          if (rule.canHandle && rule.canHandle(node)) {
            try {
              const result = await rule.evaluate(node, context);
              results.push(result);
            } catch (error) {
              console.warn(`Rule ${rule.id} failed:`, error);
            }
          }
        }
        return results;
      },
      shutdown: () => {},
    };

    this.performanceMonitor = {
      recordRuleStart: () => {},
      recordRuleEnd: () => {},
      recordFunctionAnalysis: () => {},
      recordFileAnalysis: () => {},
      generateReport: () => ({}),
      stop: () => {},
    };

    this.cacheManager = {
      get: () => undefined,
      set: () => {},
      clear: () => {},
      size: () => 0,
    };

    this.pluginManager = {
      loadPlugin: () => {},
      getLoadedPlugins: () => [],
    };

    // 创建健壮的回退规则管理器
    this.ruleManager = {
      initializeAsyncRuleEngine: async (config?: any) => {
        console.warn('Using fallback rule manager, AsyncRuleEngine initialization skipped');
        // 返回 null 而不是抛出错误，让调用者处理
        return null;
      },
      getAsyncRuleEngine: () => null,
      dispose: async () => {},
    };

    this.logDebug('Fallback dependencies initialized');
  }

  /**
   * 启动性能监控记录
   */
  private startPerformanceMonitoring(operation: string): void {
    if (this.performanceMonitor && typeof this.performanceMonitor.recordRuleStart === 'function') {
      this.performanceMonitor.recordRuleStart(operation, null, {
        filePath: this.currentFilePath,
        functionName: this.currentFunctionName,
      });
    }
  }

  /**
   * 结束性能监控记录
   */
  private endPerformanceMonitoring(operation: string, executionTime: number): void {
    if (this.performanceMonitor && typeof this.performanceMonitor.recordRuleEnd === 'function') {
      this.performanceMonitor.recordRuleEnd(operation, {
        ruleId: operation,
        complexity: 0,
        isExempted: false,
        shouldIncreaseNesting: false,
        reason: 'Performance monitoring',
        suggestions: [],
        metadata: {},
        executionTime,
        cacheHit: false,
      }, executionTime);
    }
  }

  /**
   * 获取工厂配置摘要
   */
  public getFactoryFeatures(): any {
    if (this.factory && typeof (this.factory as any).getFeatureSummary === 'function') {
      return (this.factory as any).getFeatureSummary();
    }
    return {
      monitoring: false,
      caching: false,
      debugging: false,
      plugins: false,
      parallelExecution: false,
    };
  }

  /**
   * 更新计算器配置
   * 使用新选项重新创建工厂和依赖
   */
  public updateConfiguration(newOptions: CalculationOptions): void {
    // 清理现有依赖
    this.cleanupDependencies();
    
    // 合并选项
    this.options = { ...this.options, ...newOptions };
    
    // 重新创建工厂和依赖
    this.factory = this.createDefaultFactory(this.options);
    this.initializeDependencies();
    
    // 更新相关状态
    this.debugMode = this.options.enableDebugLog || false;
    this.detailsMode = this.options.enableDetails || false;
    
    if (this.detailsMode && !this.detailCollector) {
      this.initializeDetailsMode();
    } else if (!this.detailsMode && this.detailCollector) {
      this.detailCollector = null;
    }
    
    this.logDebug('Configuration updated successfully', {
      newOptions,
      features: this.getFactoryFeatures(),
    });
  }
  
  /**
   * 异步初始化规则引擎
   * 在后台初始化AsyncRuleEngine，不阻塞构造函数
   */
  private initializeAsyncRuleEngine(): void {
    // 异步初始化，避免阻塞构造函数
    if (this.ruleManager && typeof this.ruleManager.initializeAsyncRuleEngine === 'function') {
      // 获取工厂配置中的规则引擎配置
      const ruleEngineConfig = (this.factory as CalculatorFactory).getOptions().ruleEngineConfig;
      
      this.ruleManager.initializeAsyncRuleEngine(ruleEngineConfig)
        .then((engine: any) => {
          this.asyncRuleEngine = engine;
          this.ruleEngineInitialized = true;
          this.logDebug('AsyncRuleEngine initialized successfully', {
            engineConfig: ruleEngineConfig
          });
        })
        .catch((error: any) => {
          console.warn('Failed to initialize AsyncRuleEngine:', error);
          this.ruleEngineInitialized = false;
          this.asyncRuleEngine = null;
        });
    } else {
      console.warn('RuleManager not available, skipping async rule engine initialization');
      this.ruleEngineInitialized = false;
      this.asyncRuleEngine = null;
    }
  }
  
  /**
   * 确保异步规则引擎已初始化
   * 如果未初始化，会同步等待初始化完成
   */
  private async ensureAsyncRuleEngine(): Promise<any> {
    if (this.asyncRuleEngine && this.ruleEngineInitialized) {
      return this.asyncRuleEngine;
    }
    
    try {
      if (this.ruleManager && typeof this.ruleManager.initializeAsyncRuleEngine === 'function') {
        // 获取工厂配置中的规则引擎配置
        const ruleEngineConfig = (this.factory as CalculatorFactory).getOptions().ruleEngineConfig;
        
        this.asyncRuleEngine = await this.ruleManager.initializeAsyncRuleEngine(ruleEngineConfig);
        this.ruleEngineInitialized = true;
        this.logDebug('AsyncRuleEngine initialized on demand', {
          engineConfig: ruleEngineConfig
        });
        return this.asyncRuleEngine;
      } else {
        this.logError('RuleManager not available for on-demand initialization', new Error('RuleManager not available'));
        this.ruleEngineInitialized = false;
        this.asyncRuleEngine = null;
        return null;
      }
    } catch (error) {
      this.logError('Failed to initialize AsyncRuleEngine on demand', error);
      this.ruleEngineInitialized = false;
      this.asyncRuleEngine = null;
      return null;
    }
  }
  
  /**
   * 初始化详细模式
   * 创建DetailCollector实例并初始化规则
   */
  private initializeDetailsMode(): void {
    try {
      // 确保规则已初始化
      if (!this.rulesInitialized) {
        initializeRules(this.options.quiet);
        this.rulesInitialized = true;
        this.logDebug('Rules initialized for details mode');
      }
      
      // 创建DetailCollector实例
      this.detailCollector = new DetailCollector();
      
      // 将规则注册表的规则注册到DetailCollector中
      this.registerRulesToDetailCollector();
      
      this.logDebug('Details mode initialized successfully');
    } catch (error) {
      console.warn('Failed to initialize details mode:', error);
      this.detailsMode = false;
      this.detailCollector = null;
    }
  }
  
  /**
   * 将RuleRegistry中的规则注册到DetailCollector
   */
  private registerRulesToDetailCollector(): void {
    if (!this.detailCollector) {
      return;
    }
    
    try {
      const allRules = RuleRegistry.getAllRules();
      this.detailCollector.registerRules(allRules);
      
      this.logDebug('Registered rules to DetailCollector', {
        ruleCount: allRules.length
      });
    } catch (error) {
      this.logError('Failed to register rules to DetailCollector', error);
    }
  }
  
  /**
   * 启用或禁用详细模式
   * @param enable 是否启用详细模式
   */
  public setDetailsMode(enable: boolean): void {
    if (enable && !this.detailsMode) {
      this.detailsMode = true;
      this.initializeDetailsMode();
    } else if (!enable && this.detailsMode) {
      this.detailsMode = false;
      this.detailCollector = null;
    }
  }
  
  /**
   * 检查是否启用了详细模式
   * @returns 是否启用详细模式
   */
  public isDetailsMode(): boolean {
    return this.detailsMode && this.detailCollector !== null;
  }
  
  /**
   * 计算文件的认知复杂度
   */
  public async calculateFile(filePath: string): Promise<FunctionResult[]> {
    this.currentFilePath = filePath;
    const ast = await this.parser.parseFile(filePath);
    return await this.calculate(ast);
  }

  /**
   * 计算代码的认知复杂度
   */
  public async calculateCode(code: string, filePath: string = 'inline'): Promise<FunctionResult[]> {
    this.currentFilePath = filePath;
    this.currentSourceCode = code; // 存储源代码
    const ast = await this.parser.parseCode(code, filePath);
    return await this.calculate(ast);
  }
  
  /**
   * 计算AST的认知复杂度
   * 基于访问者模式的完全重构版本
   */
  public async calculate(ast: Module): Promise<FunctionResult[]> {
    const startTime = performance.now();
    const startMemory = this.getMemoryUsage();
    
    // 初始化性能监控变量
    this.performanceStartTime = startTime;
    this.performanceStartMemory = startMemory;
    this.detailStepCount = 0;
    
    try {
      this.logDebug('Starting complexity calculation with visitor pattern', { 
        detailsMode: this.detailsMode,
        memoryStart: `${startMemory.toFixed(2)}MB`
      });
      
      // 使用 FunctionFinderVisitor 查找所有函数
      const functions = FunctionFinderVisitor.find(ast);
      const results: FunctionResult[] = [];
      
      this.logDebug('Found functions for analysis', { functionCount: functions.length });
      
      // 获取源代码用于访问者
      const sourceCode = this.getSourceCode();
      if (!sourceCode) {
        this.logError('Unable to get source code for ComplexityVisitor', new Error('Source code not available'));
        return [];
      }
      
      // 获取所有豁免注释
      this.currentIgnoreExemptions = this.parser.findIgnoreExemptions();
      this.logDebug('Found ignore exemptions', { exemptionCount: this.currentIgnoreExemptions.length });
      
      // 为每个函数创建独立的 ComplexityVisitor 实例并计算复杂度
      for (let i = 0; i < functions.length; i++) {
        const func = functions[i];
        
        // 跳过无效的函数节点
        if (!func) {
          this.logDebug('Skipping invalid function node', { functionIndex: i + 1 });
          continue;
        }
        
        try {
          // 获取函数名
          this.currentFunctionName = this.parser.getFunctionName(func);
          
          this.logDebug('Calculating function complexity', { 
            functionName: this.currentFunctionName,
            functionIndex: i + 1,
            totalFunctions: functions.length
          });
          
          // 计算单个函数的复杂度
          const result = await this.calculateFunctionComplexityAsync(func, sourceCode);
          results.push(result);
          
          this.logDebug('Function complexity calculated', { 
            functionName: this.currentFunctionName,
            complexity: result.complexity
          });
          
        } catch (functionError) {
          // 函数级错误处理：单个函数失败不影响整体分析
          const errorResult = this.createFunctionErrorResult(func, functionError, i + 1);
          results.push(errorResult);
        }
      }
      
      const endTime = performance.now();
      const executionTime = endTime - startTime;
      
      this.logPerformance('Total complexity calculation', executionTime, {
        functionsAnalyzed: functions.length,
        architecture: 'visitor-pattern',
        totalComplexity: results.reduce((sum, r) => sum + r.complexity, 0)
      });
      
      return results;
      
    } catch (error) {
      // 总体错误处理
      const endTime = performance.now();
      this.logError('Critical error in complexity calculation', error, {
        totalDuration: endTime - startTime
      });
      
      return [];
    }
  }
  
  /**
   * 计算单个函数的复杂度
   * 使用 ComplexityVisitor 的新方法
   */
  /**
   * 异步计算单个函数的复杂度
   * 使用 ComplexityVisitor 访问者模式进行复杂度计算
   * 包含异步规则引擎回退机制
   * @param functionNode 函数节点
   * @param sourceCode 源代码
   * @returns 函数复杂度结果
   */
  private async calculateFunctionComplexityAsync(functionNode: Node, sourceCode: string): Promise<FunctionResult> {
    try {
      // 重置应用的豁免状态
      this.appliedExemptions = [];
      
      // 获取函数位置信息
      const location = this.parser.getLocation(functionNode);
      
      // 启用详细模式跟踪
      let functionDetail = null;
      if (this.detailCollector) {
        this.detailCollector.startFunction(this.currentFunctionName, location.line, location.column);
      }
      
      let totalComplexity = 0;
      
      // 尝试使用异步规则引擎计算复杂度
      try {
        const asyncEngine = await this.ensureAsyncRuleEngine();
        if (asyncEngine && typeof asyncEngine.analyzeFunction === 'function') {
          const analysis = await asyncEngine.analyzeFunction(functionNode);
          totalComplexity = analysis.totalComplexity;
          this.logDebug('Function complexity calculated using AsyncRuleEngine', {
            functionName: this.currentFunctionName,
            totalComplexity,
            architecture: 'async-rule-engine'
          });
        } else {
          throw new Error('AsyncRuleEngine not available or missing analyzeFunction method');
        }
      } catch (asyncError) {
        // 回退到 ComplexityVisitor
        this.logDebug('AsyncRuleEngine failed, falling back to ComplexityVisitor', { 
          error: asyncError instanceof Error ? asyncError.message : String(asyncError) 
        });
        
        const visitor = new ComplexityVisitor(sourceCode, this.detailCollector ?? undefined);
        visitor.visit(functionNode);
        totalComplexity = visitor.getTotalComplexity();
        
        this.logDebug('Function complexity calculated using ComplexityVisitor fallback', {
          functionName: this.currentFunctionName,
          totalComplexity,
          architecture: 'visitor-pattern-fallback'
        });
      }
      
      // 结束详细模式跟踪
      if (this.detailCollector) {
        functionDetail = this.detailCollector.endFunction();
      }
      
      this.logDebug('Function complexity calculation completed', {
        functionName: this.currentFunctionName,
        totalComplexity
      });

      // 构建结果对象
      return {
        name: this.currentFunctionName,
        complexity: totalComplexity,
        line: location.line,
        column: location.column,
        filePath: this.currentFilePath,
        ignoreExemptions: this.appliedExemptions.length > 0 ? [...this.appliedExemptions] : undefined,
        details: functionDetail?.details
      };

    } catch (error) {
      return this.handleFunctionAnalysisError(functionNode, error);
    }
  }

  /**
   * 处理函数分析错误的统一方法
   */
  private handleFunctionAnalysisError(functionNode: Node, error: any): FunctionResult {
    const location = this.parser.getLocation(functionNode);
    
    this.logError('Error calculating function complexity', error, {
      functionName: this.currentFunctionName,
      line: location.line,
      column: location.column
    });

    // 清理详细模式跟踪
    if (this.detailCollector) {
      try {
        this.detailCollector.endFunction();
      } catch (cleanupError) {
        this.logError('Error cleaning up function detail collection after failure', cleanupError);
      }
    }

    // 返回错误结果而不是抛出异常，保证批量处理的连续性
    return {
      name: this.currentFunctionName,
      complexity: 0,
      line: location.line,
      column: location.column,
      filePath: this.currentFilePath,
      severity: 'Critical' as const,
      ignoreExemptions: [{
        line: location.line,
        type: 'ignore-next-line',
        complexityReduced: 0
      }]
    };
  }

  /**
   * 创建函数分析错误结果
   * 用于批量分析时的错误处理
   */
  private createFunctionErrorResult(functionNode: Node, error: any, functionIndex: number): FunctionResult {
    const functionName = this.parser.getFunctionName(functionNode);
    const location = this.parser.getLocation(functionNode);
    
    this.logError('Error calculating complexity for function', error, { 
      functionName,
      functionIndex
    });
    
    return {
      name: functionName || `<unknown-function-${functionIndex}>`,
      complexity: 0,
      line: location.line,
      column: location.column,
      filePath: this.currentFilePath
    };
  }

  /**
   * 使用 AsyncRuleEngine 计算复杂度
   * 纯委托实现，不包含回退逻辑
   */
  private async calculateComplexityWithRuleEngine(functionNode: Node, sourceCode: string, ruleEngine: AsyncRuleEngine): Promise<number> {
    // 获取实际的函数节点
    const actualFunction = this.getActualFunctionNode(functionNode);
    
    // 直接使用规则引擎的 analyzeFunction 方法
    // 这个方法会内部创建合适的 AnalysisContext
    const analysis = await ruleEngine.analyzeFunction(actualFunction);
    
    return analysis.totalComplexity;
  }



  /**
   * 获取源代码用于访问者
   * 从已存储的当前源代码中获取
   */
  private getSourceCode(): string | null {
    try {
      // 优先使用存储的当前源代码
      if (this.currentSourceCode) {
        return this.currentSourceCode;
      }
      
      // 尝试多种方式获取源代码（回退方案）
      if (this.parser && typeof (this.parser as any).getSourceCode === 'function') {
        return (this.parser as any).getSourceCode();
      }
      
      if (this.parser && typeof (this.parser as any).sourceCode === 'string') {
        return (this.parser as any).sourceCode;
      }
      
      // 尝试从源行重建源代码
      const sourceLines = this.getParserSourceLines();
      if (sourceLines) {
        return sourceLines.join('\n');
      }
      
      return null;
    } catch (error) {
      this.logError('Error getting source code', error);
      return null;
    }
  }

  /**
   * 获取实际的函数节点（处理包装的情况）
   */
  private getActualFunctionNode(node: any): Node {
    // 处理ExportDeclaration包装的函数
    if (node.type === 'ExportDeclaration' && node.declaration) {
      return node.declaration;
    }
    
    // 处理VariableDeclarator中的函数表达式
    if (node.type === 'VariableDeclarator' && node.init) {
      return node.init;
    }
    
    // 处理类方法（需要提取内部的function.body属性）
    if (node.type === 'ClassMethod' || node.type === 'MethodDefinition') {
      // ClassMethod节点有一个function属性，function.body包含实际的函数体
      const classMethodNode = this.getClassMethodNode(node);
      if (classMethodNode && classMethodNode.function && classMethodNode.function.body) {
        return classMethodNode.function.body;
      }
      return node;
    }
    
    return node;
  }

  /**
   * 尝试使用基于类的规则进行复杂度评估
   */
  private tryEvaluateWithClassBasedRules(
    node: Node, 
    nodeLineNumber: number, 
    nodeColumnNumber: number
  ): { 
    complexity: number, 
    ruleId: string, 
    description: string, 
    hasMixingPenalty: boolean,
    wasHandled: boolean 
  } | null {
    // 如果异步规则引擎未初始化，返回null使用传统方法
    if (!this.asyncRuleEngine || !this.ruleEngineInitialized) {
      this.logDebug('AsyncRuleEngine not available, using traditional rules', {
        engineAvailable: !!this.asyncRuleEngine,
        engineInitialized: this.ruleEngineInitialized
      });
      return null;
    }
    
    try {
      // 获取适用于该节点的规则（简化版本）
      // 使用类型守卫访问完整接口的方法
      const completeEngine = this.getCompleteRuleEngine();
      const allRules = completeEngine.getAllRules?.() || [];
      
      this.logDebug('Checking class-based rules', {
        nodeType: node.type,
        totalRules: allRules.length,
        ruleIds: allRules.map((r: any) => r.id)
      });
      
      const applicableRules = allRules.filter((rule: any) => 
        rule && rule.canHandle && rule.canHandle(node)
      );
      
      this.logDebug('Found applicable class-based rules', {
        nodeType: node.type,
        applicableRules: applicableRules.length,
        ruleIds: applicableRules.map((r: any) => r.id)
      });
      
      if (applicableRules.length === 0) {
        return null; // 没有适用规则，使用传统方法
      }
      
      // 同步评估第一个适用规则（简化版本）
      const rule = this.getRuleFromArray(applicableRules);
      if (!rule) {
        return null;
      }
      
      this.logDebug('Using class-based rule', {
        ruleId: rule.id,
        ruleName: rule.name,
        nodeType: node.type
      });
      
      // 对于LogicalOperatorRule，我们需要特殊处理以获得同步结果
      if (rule.id === 'logical-operators' && (node.type === 'LogicalExpression' || 
          (node.type === 'BinaryExpression' && this.isLogicalOperator(node)))) {
        
        const logicalNode = this.getLogicalNode(node);
        if (!logicalNode) {
          return null;
        }
        
        // 检查是否为默认值赋值（豁免）
        if (this.isDefaultValueAssignment(logicalNode)) {
          return {
            complexity: 0,
            ruleId: rule.id,
            description: '默认值赋值豁免',
            hasMixingPenalty: false,
            wasHandled: true
          };
        }
        
        // 计算基础复杂度
        let complexity = 1;
        let hasMixingPenalty = false;
        
        // 检查逻辑运算符混用
        if (this.detectLogicalOperatorMixing(logicalNode)) {
          complexity += 1;
          hasMixingPenalty = true;
        }
        
        const description = this.getLogicalOperatorDescription(logicalNode.operator);
        
        // 记录详细步骤
        if (hasMixingPenalty) {
          // 分别记录基础逻辑运算符和混用惩罚
          this.addDetailStepSafely(nodeLineNumber, nodeColumnNumber, 1, rule.id, description, node.type);
          this.addDetailStepSafely(nodeLineNumber, nodeColumnNumber, 1, 'logical-operator-mixing', '逻辑运算符混用惩罚', node.type);
        } else {
          this.addDetailStepSafely(nodeLineNumber, nodeColumnNumber, complexity, rule.id, description, node.type);
        }
        
        return {
          complexity,
          ruleId: rule.id,
          description,
          hasMixingPenalty,
          wasHandled: true
        };
      }
      
      // 对于递归调用规则
      if (rule.id === 'logical-recursion' && node.type === 'CallExpression') {
        const callNode = this.getCallNode(node);
        if (!callNode) {
          return null;
        }
        
        if (this.isRecursiveCall(callNode)) {
          const complexity = 1;
          const description = this.getRecursiveCallDescription();
          
          this.addDetailStepSafely(nodeLineNumber, nodeColumnNumber, complexity, rule.id, description, node.type);
          
          return {
            complexity,
            ruleId: rule.id,
            description,
            hasMixingPenalty: false,
            wasHandled: true
          };
        } else {
          // 不是递归调用，不处理
          return {
            complexity: 0,
            ruleId: rule.id,
            description: '非递归调用',
            hasMixingPenalty: false,
            wasHandled: false
          };
        }
      }
      
      return null; // 其他规则暂不处理，使用传统方法
      
    } catch (error) {
      this.logError('Error evaluating with class-based rules', error, {
        nodeType: node.type,
        line: nodeLineNumber,
        column: nodeColumnNumber
      });
      return null; // 出错时回退到传统方法
    }
  }
  
  /**
   * 安全地添加详细步骤记录
   * 统一的详细步骤记录入口，包含错误处理、规则ID验证和性能监控
   */
  private addDetailStepSafely(
    line: number, 
    column: number, 
    increment: number, 
    ruleId: string, 
    description: string, 
    nodeType: string
  ): void {
    if (!this.detailCollector || increment === 0) {
      return;
    }
    
    // 惰性性能检查：每100步检查一次，避免影响性能
    this.detailStepCount++;
    if (this.detailStepCount % 100 === 0) {
      if (!this.shouldContinueDetailCollection(this.performanceStartTime, this.performanceStartMemory)) {
        console.warn('⚠️ 详细日志收集因性能限制被禁用');
        this.detailsMode = false;
        this.detailCollector = null;
        return;
      }
    }
    
    try {
      // 验证和规范化规则ID
      const validatedRuleId = this.validateAndNormalizeRuleId(ruleId, nodeType);
      const finalDescription = this.getFinalRuleDescription(validatedRuleId, description);
      
      this.detailCollector.addStep({
        line,
        column,
        increment,
        ruleId: validatedRuleId,
        description: finalDescription,
        context: `${nodeType} at ${line}:${column}`
      });
      
      this.logDebug('Added detail step', {
        line,
        column,
        increment,
        ruleId: validatedRuleId,
        description: finalDescription,
        nodeType,
        totalSteps: this.detailStepCount
      });
    } catch (error) {
      this.logError('Error adding detail step', error, {
        nodeType,
        line,
        column,
        ruleId,
        increment
      });
    }
  }
  
  /**
   * 验证和规范化规则ID
   * 确保每个规则ID都有对应的注册规则，否则使用回退机制
   */
  private validateAndNormalizeRuleId(ruleId: string, nodeType: string): string {
    // 如果规则ID为空或无效，使用节点类型推导
    if (!ruleId || ruleId.trim() === '') {
      this.logDebug('Empty rule ID, inferring from node type', { nodeType });
      return this.inferRuleIdFromNodeType(nodeType);
    }
    
    // 检查规则是否已注册
    if (RuleRegistry.hasRule(ruleId)) {
      return ruleId;
    }
    
    // 尝试自动注册常见但未注册的规则
    if (this.tryAutoRegisterRule(ruleId, nodeType)) {
      this.logDebug('Auto-registered rule', { ruleId, nodeType });
      return ruleId;
    }
    
    // 如果规则未注册且无法自动注册，使用回退机制
    this.logDebug('Rule not registered, using fallback', { 
      originalRuleId: ruleId, 
      nodeType 
    });
    
    return this.getFallbackRuleId(ruleId, nodeType);
  }
  
  /**
   * 从节点类型推导规则ID
   */
  private inferRuleIdFromNodeType(nodeType: string): string {
    const ruleId = getNodeTypeRuleId(nodeType);
    
    // 如果推导的规则ID也未注册，使用unknown-rule
    if (!RuleRegistry.hasRule(ruleId)) {
      return 'unknown-rule';
    }
    
    return ruleId;
  }
  
  /**
   * 尝试自动注册常见规则
   */
  private tryAutoRegisterRule(ruleId: string, nodeType: string): boolean {
    try {
      // 定义一些常见的自动注册规则模式
      const autoRegisterPatterns: Record<string, () => void> = {
        'unknown-rule': () => {
          // unknown-rule应该已经注册，这里是保险措施
          if (!RuleRegistry.hasRule('unknown-rule')) {
            RuleRegistry.register('unknown-rule', '未知规则或无法识别的复杂度增量');
          }
        },
        'nesting-penalty': () => {
          if (!RuleRegistry.hasRule('nesting-penalty')) {
            RuleRegistry.register('nesting-penalty', '嵌套层级增加的额外复杂度');
          }
        },
        'ignore-exemption': () => {
          if (!RuleRegistry.hasRule('ignore-exemption')) {
            RuleRegistry.register('ignore-exemption', '认知复杂度忽略注释豁免');
          }
        },
        'recursive-call': () => {
          if (!RuleRegistry.hasRule('recursive-call')) {
            RuleRegistry.register('recursive-call', '递归函数调用增加复杂度');
          }
        },
        // 新的基于类的规则自动注册
        'logical-operators': () => {
          if (!RuleRegistry.hasRule('logical-operators')) {
            RuleRegistry.register('logical-operators', '逻辑运算符复杂度（基于类的规则）');
          }
        },
        'logical-recursion': () => {
          if (!RuleRegistry.hasRule('logical-recursion')) {
            RuleRegistry.register('logical-recursion', '递归调用复杂度（基于类的规则）');
          }
        }
      };
      
      const autoRegistrar = autoRegisterPatterns[ruleId];
      if (autoRegistrar) {
        autoRegistrar();
        return RuleRegistry.hasRule(ruleId);
      }
      
      return false;
    } catch (error) {
      this.logError('Failed to auto-register rule', error, { ruleId, nodeType });
      return false;
    }
  }
  
  /**
   * 获取回退规则ID
   */
  private getFallbackRuleId(_originalRuleId: string, nodeType: string): string {
    // 首先尝试从节点类型推导
    const inferredRuleId = getNodeTypeRuleId(nodeType);
    if (RuleRegistry.hasRule(inferredRuleId)) {
      return inferredRuleId;
    }
    
    // 最后回退到unknown-rule
    return 'unknown-rule';
  }
  
  /**
   * 获取最终的规则描述
   * 优先使用注册表中的描述，回退到提供的描述
   */
  private getFinalRuleDescription(ruleId: string, providedDescription: string): string {
    // 尝试从规则注册表获取官方描述
    const registeredDescription = RuleRegistry.getDescription(ruleId);
    
    if (registeredDescription) {
      return registeredDescription;
    }
    
    // 回退到提供的描述
    if (providedDescription && providedDescription.trim() !== '') {
      return providedDescription.trim();
    }
    
    // 最后回退到默认描述
    return `未知规则: ${ruleId}`;
  }
  
  /**
   * 获取节点的列号
   */
  private getNodeColumnNumber(node: any): number {
    if (node.span && node.span.start !== undefined) {
      try {
        // 使用位置转换器获取更准确的列位置
        const sourceLines = this.getParserSourceLines();
        if (Array.isArray(sourceLines)) {
          const sourceCode = sourceLines.join('\n');
          const position = PositionConverter.spanToPosition(sourceCode, node.span.start);
          return position.column;
        }
        
        // 备用方案：直接使用PositionConverter
        if (this.parser && (this.parser as any).sourceCode) {
          const position = PositionConverter.spanToPosition((this.parser as any).sourceCode, node.span.start);
          return Math.max(0, position.column);
        }
      } catch (error) {
        console.warn(`获取节点列号失败: ${error}`);
        return 0;
      }
    }
    return 0;
  }
  
  /**
   * 获取递归调用的描述
   */
  private getRecursiveCallDescription(): string {
    const registeredDescription = RuleRegistry.getDescription('recursive-call');
    return registeredDescription || '递归调用';
  }
  
  /**
   * 获取节点类型对应的循环描述
   */
  private getLoopDescription(nodeType: string): string {
    const descriptions: Record<string, string> = {
      'ForStatement': 'for 循环',
      'ForInStatement': 'for-in 循环',
      'ForOfStatement': 'for-of 循环',
      'WhileStatement': 'while 循环',
      'DoWhileStatement': 'do-while 循环'
    };
    return descriptions[nodeType] || '循环';
  }
  
  /**
   * 获取逻辑运算符的描述
   */
  private getLogicalOperatorDescription(operator: string): string {
    switch (operator) {
      case '&&':
        return '逻辑与运算符 (&&)';
      case '||':
        return '逻辑或运算符 (||)';
      default:
        return `逻辑运算符 (${operator})`;
    }
  }
  
  /**
   * 检测逻辑运算符
   */
  private isLogicalOperator(node: any): boolean {
    return node.operator === '&&' || node.operator === '||';
  }
  
  /**
   * 检测是否为默认值赋值的逻辑运算符(需要排除)
   * 完善版本：支持更多默认值赋值模式和AST父节点分析
   */
  private isDefaultValueAssignment(node: any): boolean {
    // 检查 ?? 空值合并操作符 (总是应该被排除)
    if (node.operator === '??') {
      return true;
    }
    
    // 检查 || 用于默认值赋值的情况
    if (node.operator === '||') {
      return this.isInDefaultValueAssignmentContext(node);
    }
    
    // 检查 && 在某些默认值赋值模式中的使用
    // 例如: const value = data && data.value || fallback
    if (node.operator === '&&') {
      return this.isPartOfDefaultValuePattern(node);
    }
    
    return false;
  }
  
  /**
   * 检测节点是否在赋值上下文中（旧版本方法，待移除）
   * 改进版本：使用上下文标志
   */
  private isInAssignmentContext(_node: any): boolean {
    // 这个方法在新的访问者模式架构中已不再需要
    return this.isInAssignment;
  }
  
  /**
   * 检测节点是否在默认值赋值上下文中（旧版本方法，待移除）
   * 扩展版本：更精确的赋值上下文检测
   */
  private isInDefaultValueAssignmentContext(_node: any): boolean {
    // 基础检查：是否在赋值上下文中
    if (!this.isInAssignment) {
      return false;
    }
    
    // 进一步检查：这个 || 运算符是否真的用于默认值赋值
    // 对于 || 运算符，在赋值上下文中通常都是默认值模式
    return true;
  }
  
  /**
   * 检测 && 运算符是否是默认值赋值模式的一部分
   * 例如: const value = data && data.value || fallback
   * 或: const name = user && user.profile && user.profile.name || 'Unknown'
   */
  private isPartOfDefaultValuePattern(node: any): boolean {
    // 在赋值上下文中的 && 运算符可能是复杂默认值模式的一部分
    if (!this.isInAssignment) {
      return false;
    }
    
    // 检查是否存在典型的默认值模式：obj && obj.prop || default
    // 这种模式中的 && 应该被视为默认值检查的一部分
    if (this.isPropertyAccessPattern(node)) {
      return true;
    }
    
    // 检查是否是多层属性访问链的一部分
    // 例如: user && user.profile && user.profile.name
    if (this.isChainedPropertyAccessPattern(node)) {
      return true;
    }
    
    // 检查左操作数是否也是属性访问链的一部分
    // 处理 (user && user.profile) && user.profile.name 这种嵌套结构
    if (this.leftOperandIsPropertyChain(node)) {
      return true;
    }
    
    return false;
  }
  
  /**
   * 检测是否为属性访问模式 (obj && obj.prop)
   */
  private isPropertyAccessPattern(node: any): boolean {
    if (!node.left || !node.right) {
      return false;
    }
    
    // 检查模式：左侧是标识符，右侧是对同一对象的属性访问
    // 例如: data && data.value
    if (node.left.type === 'Identifier' && 
        node.right.type === 'MemberExpression' &&
        node.right.object?.type === 'Identifier') {
      
      // 检查是否是同一个对象
      const leftName = node.left.value || node.left.name;
      const rightObjectName = node.right.object.value || node.right.object.name;
      
      if (leftName === rightObjectName) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 检测是否为链式属性访问模式
   * 例如: user && user.profile 或 user.profile && user.profile.name
   */
  private isChainedPropertyAccessPattern(node: any): boolean {
    if (!node.left || !node.right) {
      return false;
    }
    
    // 检查左侧是否为MemberExpression或Identifier
    // 右侧是否为更深层的MemberExpression
    const leftId = this.extractBaseIdentifier(node.left);
    const rightId = this.extractBaseIdentifier(node.right);
    
    // 如果两个基础标识符相同，说明是同一对象的属性访问链
    if (leftId && rightId && leftId === rightId) {
      // 检查右侧是否比左侧更深层的属性访问
      const leftDepth = this.getPropertyAccessDepth(node.left);
      const rightDepth = this.getPropertyAccessDepth(node.right);
      
      if (rightDepth > leftDepth) {
        return true;
      }
    }
    
    return false;
  }
  
  /**
   * 提取基础标识符名称
   * 例如: user.profile.name -> "user"
   */
  private extractBaseIdentifier(node: any): string | null {
    if (!node) return null;
    
    if (node.type === 'Identifier') {
      return node.value || node.name;
    }
    
    if (node.type === 'MemberExpression') {
      return this.extractBaseIdentifier(node.object);
    }
    
    return null;
  }
  
  /**
   * 计算属性访问的深度
   * 例如: user -> 0, user.profile -> 1, user.profile.name -> 2
   */
  private getPropertyAccessDepth(node: any): number {
    if (!node) return 0;
    
    if (node.type === 'Identifier') {
      return 0;
    }
    
    if (node.type === 'MemberExpression') {
      return 1 + this.getPropertyAccessDepth(node.object);
    }
    
    return 0;
  }
  
  /**
   * 检查左操作数是否是属性访问链的一部分
   * 处理复杂的嵌套结构如 (user && user.profile) && user.profile.name
   */
  private leftOperandIsPropertyChain(node: any): boolean {
    if (!node.left || !node.right) {
      return false;
    }
    
    // 如果左操作数是一个逻辑表达式 (&&), 检查它是否也是属性访问模式
    if ((node.left.type === 'LogicalExpression' || node.left.type === 'BinaryExpression') && 
        node.left.operator === '&&') {
      
      // 递归检查左操作数是否也是属性访问模式
      if (this.isPropertyAccessPattern(node.left) || this.isChainedPropertyAccessPattern(node.left)) {
        // 检查右操作数是否继续这个属性链
        const leftBase = this.extractBaseIdentifier(node.left.left); // 最深的基础标识符
        const rightBase = this.extractBaseIdentifier(node.right);
        
        if (leftBase && rightBase && leftBase === rightBase) {
          return true;
        }
      }
    }
    
    return false;
  }
  
  /**
   * 检测递归调用
   */
  private isRecursiveCall(node: any): boolean {
    // 获取调用的函数名
    const callee = this.getCalleeIdentifier(node);
    if (!callee) {
      return false;
    }
    
    // 查找当前函数的名称
    const currentFunctionName = this.getCurrentFunctionName();
    if (!currentFunctionName) {
      return false;
    }
    
    return callee === currentFunctionName;
  }
  
  /**
   * 获取调用表达式的函数标识符
   */
  private getCalleeIdentifier(callNode: any): string | null {
    const callee = callNode.callee;
    
    if (!callee) {
      return null;
    }
    
    // 直接函数调用: functionName()
    if (callee.type === 'Identifier') {
      return callee.value || callee.name;
    }
    
    // 成员表达式调用: obj.method() - 检查是否是this.method()
    if (callee.type === 'MemberExpression') {
      const object = callee.object;
      const property = callee.property;
      
      // this.functionName() 的情况
      if (object?.type === 'ThisExpression' && property?.type === 'Identifier') {
        return property.value || property.name;
      }
    }
    
    return null;
  }
  
  /**
   * 获取当前正在分析的函数名称
   */
  private getCurrentFunctionName(): string | null {
    return this.currentFunctionName || null;
  }
  
  /**
   * 检测逻辑运算符混用
   * 性能优化版本：懒加载检测、早期退出、避免重复惩罚、强化错误处理
   */
  private detectLogicalOperatorMixing(node: any): boolean {
    try {
      // 性能优化：功能开关检查
      if (!this.options.enableMixedLogicOperatorPenalty) {
        this.logDebug('Mixed logic operator penalty disabled, skipping detection');
        return false;
      }

      // 早期退出：节点类型检查
      if (!node || typeof node !== 'object') {
        this.logDebug('Invalid node provided to detectLogicalOperatorMixing', { nodeType: typeof node });
        return false;
      }

      // 对LogicalExpression和BinaryExpression节点进行检测
      if (node.type !== 'LogicalExpression' && node.type !== 'BinaryExpression') {
        this.logDebug('Node type not applicable for mixing detection', { nodeType: node.type });
        return false;
      }

      // 对于BinaryExpression，只处理逻辑运算符
      if (node.type === 'BinaryExpression' && !this.isLogicalOperator(node)) {
        this.logDebug('BinaryExpression does not contain logical operator', { operator: node.operator });
        return false;
      }

      // 早期退出：检查是否为默认值赋值（需要豁免）
      if (this.isDefaultValueAssignment(node)) {
        this.logDebug('Node is default value assignment, exempt from mixing penalty');
        return false;
      }

      // 检查是否为顶层逻辑表达式（避免在子表达式中重复惩罚）
      if (!this.isTopLevelLogicalExpression(node)) {
        this.logDebug('Node is not top-level logical expression, skipping to avoid duplicate penalty');
        return false;
      }

      // 早期退出：快速检查是否可能存在混用
      if (!this.hasLogicalMixingPotential(node)) {
        this.logDebug('Node has no logical mixing potential');
        return false;
      }

      // 检查括号豁免 - 优先检查，避免昂贵的运算符收集
      if (this.hasParenthesizedOperands(node)) {
        this.logDebug('Node has parenthesized operands, exempt from mixing penalty');
        return false;
      }

      // 懒加载：仅在必要时收集运算符
      const operators = this.collectLogicalOperators(node);
      this.logDebug('Collected logical operators', { operators, nodeType: node.type });
      
      // 检查是否同时包含 && 和 ||
      const hasAND = operators.includes('&&');
      const hasOR = operators.includes('||');
      const hasMixing = hasAND && hasOR;
      
      if (hasMixing) {
        this.logDebug('Logical operator mixing detected, applying penalty', { 
          hasAND, 
          hasOR, 
          operatorCount: operators.length 
        });
      }
      
      return hasMixing;
    } catch (error) {
      // 错误边界处理：检测失败不影响整体分析
      this.logError('Error in detectLogicalOperatorMixing', error, { 
        nodeType: node?.type, 
        filePath: this.currentFilePath,
        functionName: this.currentFunctionName
      });
      
      // 优雅降级：检测失败时不应用惩罚，但继续分析
      return false;
    }
  }

  /**
   * 检查是否为顶层逻辑表达式（旧版本方法，待移除）
   * 避免在子表达式中重复应用混用惩罚
   */
  private isTopLevelLogicalExpression(_node: any): boolean {
    // 简化实现：检查是否包含逻辑混用
    // 完整实现需要父节点分析来确定是否为顶层
    return true; // 暂时返回true，依赖其他机制避免重复
  }

  /**
   * 快速检查表达式是否有逻辑混用潜力
   * 避免深度递归，提前过滤明显不需要检测的情况
   */
  private hasLogicalMixingPotential(node: any): boolean {
    // 单一运算符表达式不可能混用
    if (!node.left || !node.right) {
      return false;
    }

    // 快速扫描：检查当前层是否存在不同的逻辑运算符
    const currentOp = node.operator;
    
    // 如果当前不是逻辑运算符，检查子节点
    if (!['&&', '||'].includes(currentOp)) {
      return this.hasNestedLogicalOperators(node);
    }

    // 检查直接子节点是否包含不同的逻辑运算符
    return this.hasConflictingLogicalOperators(node, currentOp);
  }

  /**
   * 检查是否存在嵌套的逻辑运算符
   */
  private hasNestedLogicalOperators(node: any): boolean {
    const checkForLogical = (childNode: any): boolean => {
      if (!childNode || typeof childNode !== 'object') return false;
      
      if ((childNode.type === 'LogicalExpression' || childNode.type === 'BinaryExpression') && 
          ['&&', '||'].includes(childNode.operator)) {
        return true;
      }
      
      // 只检查一层深度，避免深度递归
      return this.hasDirectLogicalChildren(childNode);
    };

    return checkForLogical(node.left) || checkForLogical(node.right);
  }

  /**
   * 检查节点的直接子节点是否包含逻辑运算符
   */
  private hasDirectLogicalChildren(node: any): boolean {
    if (!node || typeof node !== 'object') return false;
    
    // 只检查直接的 left 和 right 属性
    const checkDirectChild = (child: any): boolean => {
      return child && 
             (child.type === 'LogicalExpression' || child.type === 'BinaryExpression') &&
             ['&&', '||'].includes(child.operator);
    };

    return checkDirectChild(node.left) || checkDirectChild(node.right);
  }

  /**
   * 检查是否存在与当前运算符冲突的逻辑运算符
   * 只有运算符不同时才认为是潜在混用
   */
  private hasConflictingLogicalOperators(node: any, currentOp: string): boolean {
    // 快速检查直接子节点
    const leftOp = this.getNodeOperator(node.left);
    const rightOp = this.getNodeOperator(node.right);
    
    // 只有当子节点运算符与当前运算符不同时，才认为是潜在混用
    const hasLeftConflict = Boolean(leftOp && leftOp !== currentOp && ['&&', '||'].includes(leftOp));
    const hasRightConflict = Boolean(rightOp && rightOp !== currentOp && ['&&', '||'].includes(rightOp));
    
    return hasLeftConflict || hasRightConflict;
  }

  /**
   * 获取节点的运算符（如果是逻辑/二元表达式）
   */
  private getNodeOperator(node: any): string | null {
    if (!node || typeof node !== 'object') return null;
    
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') && node.operator) {
      return node.operator;
    }
    
    return null;
  }

  /**
   * 收集逻辑表达式中的所有逻辑运算符
   * 强化错误处理版本
   */
  private collectLogicalOperators(node: any): string[] {
    try {
      const operators: string[] = [];
      const visited = new Set<any>(); // 防止重复访问同一节点
      
      // 深度优先递归收集运算符
      this.visitLogicalOperatorsDepthFirst(node, (operator: string) => {
        if (['&&', '||'].includes(operator)) {
          operators.push(operator);
        }
      }, visited);
      
      this.logDebug('Successfully collected logical operators', { 
        operatorCount: operators.length, 
        operators: operators.join(', ') 
      });
      
      return operators;
    } catch (error) {
      // 错误边界处理：收集失败时返回空数组，避免混用检测
      this.logError('Error in collectLogicalOperators', error, { 
        nodeType: node?.type,
        filePath: this.currentFilePath,
        functionName: this.currentFunctionName
      });
      
      // 优雅降级：返回空数组，不会触发混用检测
      return [];
    }
  }

  /**
   * 深度优先递归访问逻辑运算符
   * 实现高级表达式树遍历，支持复杂嵌套结构，强化错误处理
   */
  private visitLogicalOperatorsDepthFirst(
    node: any, 
    callback: (operator: string) => void, 
    visited: Set<any> = new Set(),
    depth: number = 0
  ): void {
    try {
      // 防止无限递归和重复访问
      if (!node || typeof node !== 'object' || visited.has(node) || depth > 50) {
        if (depth > 50) {
          this.logDebug('Reached maximum recursion depth in visitLogicalOperatorsDepthFirst', { depth });
        }
        return;
      }
      
      visited.add(node);

      // 处理当前节点的逻辑运算符
      if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') && node.operator) {
        try {
          callback(node.operator);
        } catch (callbackError) {
          this.logError('Error in callback during operator visit', callbackError, { 
            operator: node.operator,
            nodeType: node.type 
          });
          // 回调错误不中断遍历
        }
      }

      // 深度遍历所有子节点 - 不仅限于left/right
      this.traverseAllChildren(node, (childNode: any) => {
        try {
          if (childNode && typeof childNode === 'object' && childNode.type) {
            // 对于逻辑相关节点进行递归
            if (this.isLogicalRelevantNode(childNode)) {
              this.visitLogicalOperatorsDepthFirst(childNode, callback, visited, depth + 1);
            }
          }
        } catch (childError) {
          this.logError('Error processing child node in visitLogicalOperatorsDepthFirst', childError, { 
            childNodeType: childNode?.type,
            depth: depth + 1
          });
          // 子节点错误不中断其他子节点的处理
        }
      });
    } catch (error) {
      // 错误边界处理：遍历失败不影响整体分析
      this.logError('Error in visitLogicalOperatorsDepthFirst', error, { 
        nodeType: node?.type,
        depth,
        visitedCount: visited.size
      });
      // 不重新抛出错误，允许其他部分继续执行
    }
  }

  /**
   * 遍历节点的所有子属性
   */
  private traverseAllChildren(node: any, visitor: (child: any) => void): void {
    for (const key in node) {
      if (key === 'span' || key === 'type') continue; // 跳过元数据
      
      const value = node[key];
      
      if (Array.isArray(value)) {
        value.forEach(child => visitor(child));
      } else if (value && typeof value === 'object') {
        visitor(value);
      }
    }
  }

  /**
   * 判断节点是否与逻辑运算相关
   */
  private isLogicalRelevantNode(node: any): boolean {
    const logicalTypes = [
      'LogicalExpression',
      'BinaryExpression', 
      'ConditionalExpression',
      'ParenthesizedExpression',
      'CallExpression',
      'MemberExpression',
      'Identifier'
    ];
    
    return logicalTypes.includes(node.type);
  }

  /**
   * 检查逻辑表达式的操作数是否被括号包裹
   * 高级括号检测，支持深层嵌套和复杂表达式结构
   */
  private hasParenthesizedOperands(node: any): boolean {
    // 直接检查当前节点的左右操作数是否被括号包裹
    if (this.isDirectlyParenthesized(node.left) || this.isDirectlyParenthesized(node.right)) {
      return true;
    }
    
    // 检查是否存在逻辑分组的括号（消除歧义的括号）
    if (this.hasLogicalGroupingParentheses(node)) {
      return true;
    }
    
    return false;
  }

  /**
   * 检查节点是否直接被括号包裹
   */
  private isDirectlyParenthesized(node: any): boolean {
    return node && node.type === 'ParenthesizedExpression';
  }

  /**
   * 检查是否存在逻辑分组的括号
   * 例如: (a && b) || c 或 a && (b || c)
   */
  private hasLogicalGroupingParentheses(node: any): boolean {
    if (!node) return false;
    
    // 检查左子表达式
    if (node.left && this.containsLogicalGrouping(node.left)) {
      return true;
    }
    
    // 检查右子表达式  
    if (node.right && this.containsLogicalGrouping(node.right)) {
      return true;
    }
    
    return false;
  }

  /**
   * 递归检查表达式是否包含逻辑分组括号
   */
  private containsLogicalGrouping(node: any): boolean {
    if (!node || typeof node !== 'object') return false;
    
    // 如果是括号表达式且包含逻辑运算符，则认为是分组括号
    if (node.type === 'ParenthesizedExpression') {
      return this.containsLogicalOperators(node.expression);
    }
    
    // 递归检查子节点
    if (node.left && this.containsLogicalGrouping(node.left)) return true;
    if (node.right && this.containsLogicalGrouping(node.right)) return true;
    
    return false;
  }

  /**
   * 检查表达式是否包含逻辑运算符
   */
  private containsLogicalOperators(node: any): boolean {
    if (!node || typeof node !== 'object') return false;
    
    // 检查当前节点是否是逻辑运算符
    if ((node.type === 'LogicalExpression' || node.type === 'BinaryExpression') && 
        ['&&', '||'].includes(node.operator)) {
      return true;
    }
    
    // 递归检查子节点
    const checkNode = (childNode: any): boolean => {
      if (childNode && typeof childNode === 'object') {
        return this.containsLogicalOperators(childNode);
      }
      return false;
    };
    
    // 检查所有子属性
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        if (value.some(checkNode)) return true;
      } else if (checkNode(value)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 获取节点的行号
   */
  private getNodeLineNumber(node: any): number {
    if (node.span && node.span.start !== undefined) {
      try {
        // 直接使用PositionConverter
        const sourceLines = this.getParserSourceLines();
        if (Array.isArray(sourceLines)) {
          const sourceCode = sourceLines.join('\n');
          const position = PositionConverter.spanToPosition(sourceCode, node.span.start);
          const line = position.line;
        
          // 验证行号的合理性
          if (line > 0 && line <= this.getTotalLines()) {
            return line;
          } else {
            console.warn(`节点行号超出范围: ${line}, 总行数: ${this.getTotalLines()}`);
            return Math.max(1, Math.min(line, this.getTotalLines()));
          }
        }
      } catch (error) {
        console.warn(`获取节点行号失败: ${error}`);
        return 1;
      }
    }
    return 1;
  }

  /**
   * 获取总行数（用于验证）
   */
  private getTotalLines(): number {
    // 尝试从parser获取源码行数
    try {
      const sourceLines = this.getParserSourceLines();
      if (Array.isArray(sourceLines)) {
        return sourceLines.length;
      }
    } catch {
      // 忽略错误，使用默认值
    }
    return 1000; // 默认最大行数
  }

  /**
   * 检查节点是否被豁免
   */
  private isNodeIgnored(lineNumber: number): boolean {
    return this.parser.isLineIgnored(lineNumber);
  }

  /**
   * 记录豁免使用情况
   */
  private recordExemption(lineNumber: number, complexityReduced: number): void {
    // 查找匹配的豁免注释
    const exemption = this.currentIgnoreExemptions.find(ex => ex.line === lineNumber);
    if (exemption) {
      // 记录实际减少的复杂度
      const appliedExemption: IgnoreExemption = {
        ...exemption,
        complexityReduced
      };
      this.appliedExemptions.push(appliedExemption);
    }
  }

  /**
   * 调试日志记录方法
   * 提供详细的调试信息以支持问题排查
   */
  private logDebug(message: string, context?: any): void {
    if (this.debugMode) {
      const timestamp = new Date().toISOString();
      const logContext = {
        timestamp,
        file: this.currentFilePath,
        function: this.currentFunctionName,
        nestingLevel: this.nestingLevel,
        ...context
      };
      
      console.debug(`[CognitiveComplexity:DEBUG] ${message}`, logContext);
    }
  }

  /**
   * 错误日志记录方法
   * 记录错误信息以支持问题排查，不管调试模式是否开启都会记录
   */
  private logError(message: string, error: any, context?: any): void {
    const timestamp = new Date().toISOString();
    const logContext = {
      timestamp,
      file: this.currentFilePath,
      function: this.currentFunctionName,
      nestingLevel: this.nestingLevel,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error,
      ...context
    };
    
    console.error(`[CognitiveComplexity:ERROR] ${message}`, logContext);
  }

  /**
   * 性能日志记录方法
   * 记录性能相关信息以支持优化
   */
  private logPerformance(operation: string, duration: number, context?: any): void {
    if (this.debugMode) {
      const timestamp = new Date().toISOString();
      const logContext = {
        timestamp,
        operation,
        duration: `${duration}ms`,
        file: this.currentFilePath,
        function: this.currentFunctionName,
        ...context
      };
      
      console.debug(`[CognitiveComplexity:PERF] ${operation}`, logContext);
    }
  }
  
  /**
   * 获取当前内存使用量（MB）
   * 使用 Node.js 兼容的方式获取内存信息
   */
  private getMemoryUsage(): number {
    try {
      // 检查是否在 Node.js 环境中
      if (typeof process !== 'undefined' && process.memoryUsage) {
        const memoryUsage = process.memoryUsage();
        return memoryUsage.heapUsed / 1024 / 1024; // 转换为MB
      }
      
      // 回退：如果 performance.memory 可用（浏览器环境）
      if (typeof performance !== 'undefined' && 'memory' in performance) {
        const memory = this.getPerformanceMemory();
        if (memory && typeof memory.usedJSHeapSize === 'number') {
          return memory.usedJSHeapSize / 1024 / 1024; // 转换为MB
        }
      }
      
      // 如果都不可用，返回0
      return 0;
    } catch (_error) {
      // 静默失败，返回0
      return 0;
    }
  }
  
  /**
   * 检查是否应该继续详细日志收集
   * 基于性能指标决定是否需要降级
   */
  private shouldContinueDetailCollection(startTime: number, startMemory: number): boolean {
    if (!this.detailsMode) {
      return false;
    }
    
    const currentTime = performance.now();
    const elapsedTime = currentTime - startTime;
    const currentMemory = this.getMemoryUsage();
    const memoryIncrease = currentMemory - startMemory;
    
    // 检查时间开销是否超过阈值
    const timeOverhead = elapsedTime > 5000; // 5秒阈值
    
    // 检查内存开销是否超过阈值
    const memoryOverhead = memoryIncrease > 50; // 50MB阈值
    
    // 检查详细步骤收集是否过多
    const stepsOverhead = this.detailStepCount > 10000; // 10000步阈值
    
    if (timeOverhead || memoryOverhead || stepsOverhead) {
      this.logDebug('详细模式性能检查', {
        timeOverhead,
        memoryOverhead,
        stepsOverhead,
        elapsedTime: `${elapsedTime.toFixed(0)}ms`,
        memoryIncrease: `${memoryIncrease.toFixed(2)}MB`,
        detailStepCount: this.detailStepCount
      });
      
      // 如果有DetailCollector，获取性能统计
      if (this.detailCollector) {
        const stats = this.detailCollector.getPerformanceStats();
        this.logDebug('DetailCollector 性能统计', stats);
        
        // 自动降级：禁用内存优化但保持收集
        if (memoryOverhead) {
          console.warn('⚠️ 内存使用过高，禁用内存优化');
          this.detailCollector.setMemoryOptimization(false);
        }
      }
      
      return false;
    }
    
    return true;
  }

  /**
   * 优化内存使用（旧版本方法，待移除）
   * 在检测到内存压力时执行清理操作
   */
  private optimizeMemoryUsage(): void {
    if (!this.detailsMode || !this.detailCollector) {
      return;
    }
    
    try {
      // 获取当前内存使用情况
      const currentMemory = this.getMemoryUsage();
      const memoryIncrease = currentMemory - this.performanceStartMemory;
      
      if (memoryIncrease > 100) { // 100MB阈值
        console.warn(`⚠️ 内存使用过高 (+${memoryIncrease.toFixed(2)}MB)，执行内存优化`);
        
        // 禁用内存优化，清空对象池
        this.detailCollector.setMemoryOptimization(false);
        
        // 建议垃圾回收（如果可用）
        if (typeof global !== 'undefined' && global.gc) {
          global.gc();
          this.logDebug('手动触发垃圾回收');
        }
        
        // 记录优化后的内存使用
        const optimizedMemory = this.getMemoryUsage();
        const memoryFreed = currentMemory - optimizedMemory;
        if (memoryFreed > 0) {
          console.log(`✅ 内存优化完成，释放了 ${memoryFreed.toFixed(2)}MB`);
        }
      }
    } catch (error) {
      this.logError('内存优化失败', error);
    }
  }

  /**
   * 获取完整的规则引擎接口
   * 使用类型守卫确保安全访问
   */
  private getCompleteRuleEngine(): any {
    if (!this.asyncRuleEngine || !isObject(this.asyncRuleEngine)) {
      return {};
    }
    
    // 检查是否有getAllRules方法
    if (hasProperty(this.asyncRuleEngine, 'getAllRules') && 
        typeof this.asyncRuleEngine.getAllRules === 'function') {
      return this.asyncRuleEngine;
    }
    
    return {};
  }

  /**
   * 安全地获取逻辑节点
   * 使用类型守卫验证节点属性
   */
  private getLogicalNode(node: Node): any {
    if (!isObject(node)) {
      return null;
    }
    
    if (hasOperator(node) && hasBinaryProperties(node)) {
      return node;
    }
    
    return null;
  }

  /**
   * 安全地获取调用节点
   * 使用类型守卫验证节点属性
   */
  private getCallNode(node: Node): any {
    if (!isObject(node) || !hasNodeType(node) || node.type !== 'CallExpression') {
      return null;
    }
    
    if (hasProperty(node, 'callee')) {
      return node;
    }
    
    return null;
  }

  /**
   * 安全地获取类方法节点
   * 使用类型守卫验证节点属性
   */
  private getClassMethodNode(node: Node): any {
    if (!isObject(node) || !hasNodeType(node)) {
      return null;
    }
    
    if ((node.type === 'ClassMethod' || node.type === 'MethodDefinition') && 
        hasProperty(node, 'function')) {
      return node;
    }
    
    return null;
  }

  /**
   * 安全地获取Parser的源码行
   * 使用类型守卫验证parser属性
   */
  private getParserSourceLines(): string[] | null {
    try {
      // 优先使用公共方法获取源码
      if (this.parser && typeof (this.parser as any).getSourceLines === 'function') {
        return (this.parser as any).getSourceLines();
      }
      
      // 如果没有公共方法，尝试从sourceCode属性获取
      if (this.parser && typeof (this.parser as any).sourceCode === 'string') {
        return (this.parser as any).sourceCode.split('\n');
      }
      
      // 最后的回退方案
      if (isObject(this.parser) && hasProperty(this.parser, 'sourceLines')) {
        const sourceLines = (this.parser as any).sourceLines;
        if (isArray(sourceLines)) {
          return sourceLines as string[];
        }
      }
    } catch (_error) {
      // 静默处理访问错误
    }
    
    return null;
  }

  /**
   * 安全地获取Performance Memory信息
   * 使用类型守卫验证memory属性
   */
  private getPerformanceMemory(): any {
    if (typeof performance === 'undefined') {
      return null;
    }
    
    if (hasProperty(performance, 'memory') && isObject(performance.memory)) {
      return performance.memory;
    }
    
    return null;
  }

  /**
   * 安全地从规则数组中获取第一个规则
   * 使用类型守卫验证规则属性
   */
  private getRuleFromArray(rules: any[]): any {
    if (!isArray(rules) || rules.length === 0) {
      return null;
    }
    
    const rule = rules[0];
    if (isObject(rule) && hasProperty(rule, 'id') && hasProperty(rule, 'name')) {
      return rule;
    }
    
    return null;
  }
  
  /**
   * 清理依赖资源
   * 确保所有组件正确关闭，避免资源泄漏
   */
  private cleanupDependencies(): void {
    try {
      // 清理执行池
      if (this.executionPool && typeof this.executionPool.shutdown === 'function') {
        this.executionPool.shutdown();
      }

      // 清理性能监控器
      if (this.performanceMonitor && typeof this.performanceMonitor.stop === 'function') {
        this.performanceMonitor.stop();
      }

      // 清理缓存管理器
      if (this.cacheManager && typeof this.cacheManager.clear === 'function') {
        this.cacheManager.clear();
      }

      // 清理规则管理器
      if (this.ruleManager && typeof this.ruleManager.dispose === 'function') {
        this.ruleManager.dispose();
      }

      // 清理插件管理器
      // 插件管理器通常不需要特殊清理

      this.logDebug('Dependencies cleaned up successfully');
    } catch (error) {
      this.logError('Error during dependency cleanup', error);
    }
  }
  
  /**
   * 清理资源，确保进程能够正常退出
   * 主要用于清理 AsyncRuleEngine 相关资源
   */
  public async dispose(): Promise<void> {
    try {
      // 清理依赖组件
      this.cleanupDependencies();
      
      // 清理异步规则引擎
      if (this.asyncRuleEngine) {
        await this.asyncRuleEngine.dispose?.();
        this.asyncRuleEngine = null;
        this.ruleEngineInitialized = false;
      }
      
      // 清理详细模式相关资源
      if (this.detailCollector) {
        // 如果 DetailCollector 有 dispose 方法，调用它
        if (typeof (this.detailCollector as any).dispose === 'function') {
          await (this.detailCollector as any).dispose();
        }
        this.detailCollector = null;
      }
      
      this.logDebug('ComplexityCalculator disposed successfully');
    } catch (error) {
      this.logError('Error during ComplexityCalculator disposal', error);
      throw error;
    }
  }

  /**
   * 静态分析方法 - 提供一次性分析的优雅API
   * 自动管理资源生命周期，适用于简单的一次性分析场景
   * 
   * @param code 要分析的代码字符串
   * @param options 可选的分析配置
   * @returns 分析结果
   * 
   * @example
   * ```typescript
   * const results = await ComplexityCalculator.analyze(`
   *   function complex(x) {
   *     if (x > 0) {
   *       while (x-- > 0) {
   *         if (x % 2 === 0) {
   *           console.log(x);
   *         }
   *       }
   *     }
   *   }
   * `);
   * console.log(results[0].complexity); // 输出复杂度
   * ```
   */
  public static async analyze(
    code: string, 
    options: Partial<CalculationOptions> = {}
  ): Promise<FunctionResult[]> {
    // 创建轻量级配置以优化一次性分析的性能
    const lightweightOptions: CalculationOptions = {
      enableDebugLog: false,
      enableDetails: false,
      quiet: true,
      enableMixedLogicOperatorPenalty: false,
      ...options
    };

    // 创建轻量级工厂，禁用重量级功能
    const { createLightweightFactory } = await import('./calculator-factory');
    const factory = createLightweightFactory();
    
    let calculator: ComplexityCalculator | null = null;
    
    try {
      // 创建计算器实例
      calculator = new ComplexityCalculator(lightweightOptions, factory);
      
      // 执行分析
      const results = await calculator.calculateCode(code, 'inline-analysis');
      
      return results;
      
    } catch (error) {
      // 分析失败时记录错误但不抛出，返回空结果
      console.warn('Static analysis failed:', error);
      return [];
      
    } finally {
      // 确保资源被正确清理
      if (calculator) {
        try {
          await calculator.dispose();
        } catch (disposeError) {
          console.warn('Failed to dispose calculator:', disposeError);
        }
      }
    }
  }

  /**
   * 静态文件分析方法 - 分析指定文件
   * 自动管理资源生命周期，适用于单文件分析场景
   * 
   * @param filePath 要分析的文件路径
   * @param options 可选的分析配置
   * @returns 分析结果
   * 
   * @example
   * ```typescript
   * const results = await ComplexityCalculator.analyzeFile('./src/complex-function.ts');
   * console.log(`Found ${results.length} functions with complexity analysis`);
   * ```
   */
  public static async analyzeFile(
    filePath: string,
    options: Partial<CalculationOptions> = {}
  ): Promise<FunctionResult[]> {
    // 创建轻量级配置
    const lightweightOptions: CalculationOptions = {
      enableDebugLog: false,
      enableDetails: false,
      quiet: true,
      enableMixedLogicOperatorPenalty: false,
      ...options
    };

    // 创建轻量级工厂
    const { createLightweightFactory } = await import('./calculator-factory');
    const factory = createLightweightFactory();
    
    let calculator: ComplexityCalculator | null = null;
    
    try {
      // 创建计算器实例
      calculator = new ComplexityCalculator(lightweightOptions, factory);
      
      // 执行文件分析
      const results = await calculator.calculateFile(filePath);
      
      return results;
      
    } catch (error) {
      // 分析失败时记录错误但不抛出，返回空结果
      console.warn(`File analysis failed for ${filePath}:`, error);
      return [];
      
    } finally {
      // 确保资源被正确清理
      if (calculator) {
        try {
          await calculator.dispose();
        } catch (disposeError) {
          console.warn('Failed to dispose calculator:', disposeError);
        }
      }
    }
  }

  /**
   * 静态批量文件分析方法 - 分析多个文件
   * 使用单个计算器实例分析多个文件，提升性能
   * 
   * @param filePaths 要分析的文件路径数组
   * @param options 可选的分析配置
   * @returns 文件路径到分析结果的映射
   * 
   * @example
   * ```typescript
   * const results = await ComplexityCalculator.analyzeFiles([
   *   './src/module1.ts',
   *   './src/module2.ts',
   *   './src/module3.ts'
   * ]);
   * 
   * for (const [filePath, functions] of results) {
   *   console.log(`${filePath}: ${functions.length} functions analyzed`);
   * }
   * ```
   */
  public static async analyzeFiles(
    filePaths: string[],
    options: Partial<CalculationOptions> = {}
  ): Promise<Map<string, FunctionResult[]>> {
    const results = new Map<string, FunctionResult[]>();
    
    if (filePaths.length === 0) {
      return results;
    }

    // 对于批量分析，使用优化配置但不完全轻量级
    const batchOptions: CalculationOptions = {
      enableDebugLog: false,
      enableDetails: options.enableDetails ?? false, // 允许用户启用详细模式
      quiet: true,
      enableMixedLogicOperatorPenalty: options.enableMixedLogicOperatorPenalty ?? false,
      ...options
    };

    // 根据文件数量选择工厂类型
    const factoryModule = await import('./calculator-factory');
    const factory = filePaths.length > 10 
      ? factoryModule.createCalculatorFactory(batchOptions) // 大批量使用标准工厂
      : factoryModule.createLightweightFactory(); // 小批量使用轻量级工厂
    
    let calculator: ComplexityCalculator | null = null;
    
    try {
      // 创建计算器实例
      calculator = new ComplexityCalculator(batchOptions, factory);
      
      // 批量分析文件
      for (const filePath of filePaths) {
        try {
          const fileResults = await calculator.calculateFile(filePath);
          results.set(filePath, fileResults);
        } catch (fileError) {
          console.warn(`Failed to analyze file ${filePath}:`, fileError);
          results.set(filePath, []); // 失败的文件设置为空结果
        }
      }
      
      return results;
      
    } catch (error) {
      console.warn('Batch file analysis failed:', error);
      return results; // 返回已完成的部分结果
      
    } finally {
      // 确保资源被正确清理
      if (calculator) {
        try {
          await calculator.dispose();
        } catch (disposeError) {
          console.warn('Failed to dispose calculator:', disposeError);
        }
      }
    }
  }

  /**
   * 静态快速分析方法 - 仅返回复杂度概览
   * 最轻量级的分析，只返回基本的复杂度数据，不包含详细信息
   * 
   * @param code 要分析的代码字符串
   * @returns 简化的复杂度结果
   * 
   * @example
   * ```typescript
   * const overview = await ComplexityCalculator.quickAnalyze(`
   *   function simple() { return 42; }
   *   function complex() { if (x) { while (y) { if (z) { } } } }
   * `);
   * console.log(`Total functions: ${overview.functionCount}`);
   * console.log(`Average complexity: ${overview.averageComplexity}`);
   * ```
   */
  public static async quickAnalyze(code: string): Promise<{
    functionCount: number;
    totalComplexity: number;
    averageComplexity: number;
    maxComplexity: number;
    complexFunctions: Array<{ name: string; complexity: number }>;
  }> {
    try {
      // 使用最小化的配置
      const results = await ComplexityCalculator.analyze(code, {
        enableDebugLog: false,
        enableDetails: false,
        quiet: true,
        enableMixedLogicOperatorPenalty: false
      });
      
      const functionCount = results.length;
      const totalComplexity = results.reduce((sum, r) => sum + r.complexity, 0);
      const averageComplexity = functionCount > 0 ? totalComplexity / functionCount : 0;
      const maxComplexity = functionCount > 0 ? Math.max(...results.map(r => r.complexity)) : 0;
      
      // 找出复杂度较高的函数（>= 5）
      const complexFunctions = results
        .filter(r => r.complexity >= 5)
        .map(r => ({ name: r.name, complexity: r.complexity }))
        .sort((a, b) => b.complexity - a.complexity);
      
      return {
        functionCount,
        totalComplexity,
        averageComplexity: Math.round(averageComplexity * 100) / 100, // 保留2位小数
        maxComplexity,
        complexFunctions
      };
      
    } catch (error) {
      console.warn('Quick analysis failed:', error);
      return {
        functionCount: 0,
        totalComplexity: 0,
        averageComplexity: 0,
        maxComplexity: 0,
        complexFunctions: []
      };
    }
  }
}