/**
 * Legacy error classes for backward compatibility
 * 
 * This module maintains existing error classes while integrating with 
 * the new type-safe error system.
 */

import { 
  TypeSafeError, 
  SystemError, 
  ConfigurationError, 
  TypeValidationError
} from './type-safe-errors';
import type { ErrorContext } from './type-safe-errors';

export class CognitiveComplexityError extends Error {
  constructor(message: string, public readonly code: string, public readonly details?: Record<string, any>) {
    super(message);
    this.name = 'CognitiveComplexityError';
  }
}

export class ParseError extends CognitiveComplexityError {
  constructor(filePath: string, originalError: Error) {
    super(`Failed to parse file: ${filePath}. ${originalError.message}`, 'PARSE_ERROR', {
      filePath,
      originalError: originalError.message,
      errorType: originalError.constructor.name
    });
  }
}

export class ConfigError extends CognitiveComplexityError {
  constructor(message: string) {
    super(`Configuration error: ${message}`, 'CONFIG_ERROR');
  }
}

export class FileReadError extends CognitiveComplexityError {
  constructor(filePath: string, originalError: Error) {
    super(`Failed to read file: ${filePath}. ${originalError.message}`, 'FILE_READ_ERROR', {
      filePath,
      originalError: originalError.message,
      errorCode: (originalError as any).code,
      errno: (originalError as any).errno
    });
  }
}

export class CodeFrameError extends CognitiveComplexityError {
  constructor(filePath: string, position: { line: number; column: number }, originalError: Error) {
    super(`Failed to generate code frame for ${filePath} at line ${position.line}, column ${position.column}. ${originalError.message}`, 'CODE_FRAME_ERROR', {
      filePath,
      position,
      originalError: originalError.message
    });
  }
}

export class DetailCollectionError extends CognitiveComplexityError {
  constructor(context: string, originalError: Error) {
    super(`Failed to collect details for ${context}. ${originalError.message}`, 'DETAIL_COLLECTION_ERROR', {
      context,
      originalError: originalError.message
    });
  }
}

export class ValidationError extends CognitiveComplexityError {
  constructor(field: string, value: any, reason: string) {
    super(`Validation failed for ${field}: ${reason}`, 'VALIDATION_ERROR', {
      field,
      value,
      reason
    });
  }
}

// Re-export the new type-safe error system
export * from './type-safe-errors';

/**
 * Factory functions to create type-safe errors from legacy errors
 */
export class ErrorFactory {
  /**
   * Create a type-safe ParseError equivalent
   */
  static createParseError(filePath: string, originalError: Error): SystemError {
    return new SystemError('file', `Failed to parse file: ${filePath}`, false, {
      filePath,
      originalError: originalError.message,
      errorType: originalError.constructor.name,
      component: 'parser',
    });
  }

  /**
   * Create a type-safe ConfigError equivalent
   */
  static createConfigError(configPath: string, message: string, configKey?: string): ConfigurationError {
    return new ConfigurationError(configPath, configKey, message, {
      component: 'config',
    });
  }

  /**
   * Create a type-safe FileReadError equivalent
   */
  static createFileReadError(filePath: string, originalError: Error): SystemError {
    return new SystemError('file', `Failed to read file: ${filePath}`, true, {
      filePath,
      originalError: originalError.message,
      errorCode: (originalError as any).code,
      errno: (originalError as any).errno,
      component: 'fileSystem',
    });
  }

  /**
   * Create a type-safe CodeFrameError equivalent
   */
  static createCodeFrameError(
    filePath: string, 
    position: { line: number; column: number }, 
    originalError: Error
  ): SystemError {
    return new SystemError('process', `Failed to generate code frame for ${filePath}`, true, {
      filePath,
      position,
      originalError: originalError.message,
      component: 'codeFrame',
    });
  }

  /**
   * Create a type-safe DetailCollectionError equivalent
   */
  static createDetailCollectionError(context: string, originalError: Error): SystemError {
    return new SystemError('process', `Failed to collect details for ${context}`, true, {
      context,
      originalError: originalError.message,
      component: 'detailCollector',
    });
  }

  /**
   * Create a type-safe ValidationError equivalent
   */
  static createValidationError(field: string, value: any, reason: string): TypeValidationError {
    return new TypeValidationError(
      'valid value',
      typeof value,
      field,
      {
        field,
        value: typeof value === 'object' ? JSON.stringify(value) : String(value),
        reason,
        component: 'validator',
      }
    );
  }
}

/**
 * Migration utilities for gradually adopting type-safe errors
 */
export class ErrorMigration {
  /**
   * Check if an error is a legacy error
   */
  static isLegacyError(error: unknown): error is CognitiveComplexityError {
    return error instanceof CognitiveComplexityError;
  }

  /**
   * Check if an error is a type-safe error
   */
  static isTypeSafeError(error: unknown): error is TypeSafeError {
    return error instanceof TypeSafeError;
  }

  /**
   * Convert legacy error to type-safe error
   */
  static convertLegacyError(error: CognitiveComplexityError): TypeSafeError {
    const context: ErrorContext = {
      legacyCode: error.code,
      legacyDetails: error.details,
      component: 'legacy',
    };

    if (error instanceof ParseError) {
      return ErrorFactory.createParseError(error.details?.filePath || '', error);
    }
    
    if (error instanceof ConfigError) {
      return ErrorFactory.createConfigError('unknown', error.message);
    }
    
    if (error instanceof FileReadError) {
      return ErrorFactory.createFileReadError(error.details?.filePath || '', error);
    }
    
    if (error instanceof CodeFrameError) {
      return ErrorFactory.createCodeFrameError(
        error.details?.filePath || '',
        error.details?.position || { line: 0, column: 0 },
        error
      );
    }
    
    if (error instanceof DetailCollectionError) {
      return ErrorFactory.createDetailCollectionError(error.details?.context || '', error);
    }
    
    if (error instanceof ValidationError) {
      return ErrorFactory.createValidationError(
        error.details?.field || '',
        error.details?.value,
        error.details?.reason || ''
      );
    }

    // Generic conversion for unknown legacy errors
    return new SystemError('process', error.message, true, context);
  }

  /**
   * Normalize any error to type-safe error
   */
  static normalizeError(error: unknown): TypeSafeError {
    if (this.isTypeSafeError(error)) {
      return error;
    }
    
    if (this.isLegacyError(error)) {
      return this.convertLegacyError(error);
    }

    // Handle generic Error instances
    if (error instanceof Error) {
      return new SystemError('process', error.message, true, {
        originalName: error.name,
        originalStack: error.stack,
        component: 'unknown',
      });
    }

    // Handle non-Error values
    return new SystemError('process', String(error), true, {
      originalType: typeof error,
      component: 'unknown',
    });
  }
}