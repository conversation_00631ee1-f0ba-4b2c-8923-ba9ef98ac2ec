import type { 
  Module, 
  Node, 
  FunctionDeclaration, 
  VariableDeclarator,
  ClassDeclaration,
  ExportDeclaration,
  ExportDefaultDeclaration,
  VariableDeclaration,
  ClassMethod,
  TsMethodSignature,
  FunctionExpression,
  ArrowFunctionExpression
} from '@swc/core';
import { BaseVisitor } from './base-visitor';

/**
 * FunctionFinderVisitor 类
 * 
 * 专门负责在 AST 中查找所有函数类型节点的访问者。
 * 继承自 BaseVisitor，利用父节点栈提供的上下文信息进行精确识别。
 * 
 * 支持的函数类型：
 * - 函数声明 (FunctionDeclaration)
 * - 类方法 (ClassMethod, TsMethodSignature) 
 * - 函数表达式和箭头函数 (通过 VariableDeclarator 识别)
 * - 导出的函数声明 (ExportDeclaration)
 * 
 * 设计原则：
 * - 使用访问者模式，避免手动递归遍历
 * - 利用 BaseVisitor 的父节点栈，提供结构化的上下文信息
 * - 防重复收集，确保每个函数只被识别一次
 * - 提供静态入口方法，便于外部调用
 */
export class FunctionFinderVisitor extends BaseVisitor {
  /**
   * 收集到的函数节点列表
   */
  private functions: Node[] = [];
  
  /**
   * 防重复收集的节点集合
   */
  private foundNodes = new Set<Node>();

  /**
   * 构造函数
   * 
   * 初始化访问者状态，准备开始函数查找
   */
  constructor() {
    super();
    this.functions = [];
    this.foundNodes = new Set();
  }

  /**
   * 静态工厂方法 - 查找 AST 中的所有函数
   * 
   * 这是该类的主要入口点，提供简洁的 API 供外部调用。
   * 
   * @param ast 要搜索的 AST 模块
   * @returns 找到的所有函数节点数组
   */
  public static find(ast: Module): Node[] {
    const visitor = new FunctionFinderVisitor();
    visitor.visit(ast);
    return visitor.getFunctions();
  }

  /**
   * 获取收集到的函数列表
   * 
   * @returns 所有找到的函数节点数组
   */
  public getFunctions(): Node[] {
    return [...this.functions];
  }

  /**
   * 访问节点的核心实现
   * 
   * 根据节点类型调用相应的处理方法，避免在每个方法中重复判断。
   * 这是 BaseVisitor 要求实现的抽象方法。
   * 
   * @param node 当前访问的节点
   * @returns 原始节点（不进行转换）
   */
  protected visitNode(node: Node): Node {
    // 防止重复处理同一个节点
    if (this.foundNodes.has(node)) {
      return node;
    }

    // 根据节点类型分发到具体的处理方法
    switch (node.type) {
      case 'FunctionDeclaration':
        this.visitFunctionDeclaration(node as FunctionDeclaration);
        break;
      case 'ClassMethod':
        this.visitClassMethod(node as ClassMethod);
        break;
      case 'TsMethodSignature':
        this.visitTsMethodSignature(node as TsMethodSignature);
        break;
      case 'VariableDeclarator':
        this.visitVariableDeclarator(node as VariableDeclarator);
        break;
      case 'ClassDeclaration':
        this.visitClassDeclaration(node as ClassDeclaration);
        break;
      case 'ExportDeclaration':
        this.visitExportDeclaration(node as ExportDeclaration);
        break;
      case 'ExportDefaultDeclaration':
        this.visitExportDefaultDeclaration(node as ExportDefaultDeclaration);
        break;
      case 'VariableDeclaration':
        this.visitVariableDeclaration(node as VariableDeclaration);
        break;
      case 'FunctionExpression':
        this.visitFunctionExpression(node as FunctionExpression);
        break;
      case 'ArrowFunctionExpression':
        this.visitArrowFunctionExpression(node as ArrowFunctionExpression);
        break;
    }

    return node;
  }

  /**
   * 访问函数声明节点
   * 
   * 处理直接的函数声明，如：
   * function foo() { }
   * 
   * @param node 函数声明节点
   * @returns 处理后的节点
   */
  public visitFunctionDeclaration(node: FunctionDeclaration): FunctionDeclaration {
    this.addFunction(node);
    return node;
  }

  /**
   * 访问类方法节点
   * 
   * 处理使用 ClassMethod 类型的方法定义（SWC 的标准表示方式）
   * 
   * @param node 类方法节点
   * @returns 处理后的节点
   */
  public visitClassMethod(node: ClassMethod): ClassMethod {
    if (this.isConstructor(node) || this.isAbstractMethod(node)) {
      return node;
    }
    
    this.addFunction(node);
    return node;
  }

  /**
   * 访问 TypeScript 方法签名节点
   * 
   * 处理 TypeScript 接口或类型中的方法签名
   * 注意：这些通常是声明而非实现，是否包含取决于具体需求
   * 
   * @param node TypeScript 方法签名节点
   * @returns 处理后的节点
   */
  public visitTsMethodSignature(node: TsMethodSignature): TsMethodSignature {
    // 方法签名通常不包含实现，可以选择是否收集
    // 根据需求，这里选择收集，因为它们也代表了接口的复杂度
    this.addFunction(node);
    return node;
  }

  /**
   * 访问变量声明器节点
   * 
   * 处理通过变量声明的函数表达式和箭头函数，如：
   * const foo = function() { }
   * const bar = () => { }
   * 
   * @param node 变量声明器节点
   * @returns 处理后的节点
   */
  public visitVariableDeclarator(node: VariableDeclarator): VariableDeclarator {
    // 检查初始化表达式是否为函数
    if (node.init && this.isFunctionExpression(node.init)) {
      this.addFunction(node);
      // 将内部的函数表达式标记为已找到，避免重复收集
      this.foundNodes.add(node.init);
    }
    
    return node;
  }

  /**
   * 访问类声明节点
   * 
   * 类声明节点本身不是函数，但这个方法确保类的内部方法被正确访问。
   * BaseVisitor 的 visitChildren 方法会自动遍历类的成员，
   * 所以这里主要用于记录或特殊处理（如果需要的话）。
   * 
   * @param node 类声明节点
   * @returns 处理后的节点
   */
  public visitClassDeclaration(node: ClassDeclaration): ClassDeclaration {
    // 类声明本身不是函数，但其成员方法会通过 visitChildren 自动处理
    // 这里可以添加额外的类级别处理逻辑（如果需要的话）
    return node;
  }

  /**
   * 访问导出声明节点
   * 
   * 处理导出的函数声明，如：
   * export function foo() { }
   * export default function() { }
   * 
   * 注意：这里只收集导出声明本身，避免与内部的函数声明重复
   * 
   * @param node 导出声明节点
   * @returns 处理后的节点
   */
  public visitExportDeclaration(node: ExportDeclaration): ExportDeclaration {
    // 检查导出的声明是否为函数
    if (node.declaration && this.isFunctionDeclaration(node.declaration)) {
      this.addFunction(node);
      // 将内部的函数声明也标记为已找到，避免重复收集
      if (node.declaration) {
        this.foundNodes.add(node.declaration);
      }
    }
    
    return node;
  }

  /**
   * 访问默认导出声明节点
   * 
   * 处理默认导出的函数，如：
   * export default function() { }
   * export default function namedFunc() { }
   * 
   * @param node 默认导出声明节点
   * @returns 处理后的节点
   */
  public visitExportDefaultDeclaration(node: ExportDefaultDeclaration): ExportDefaultDeclaration {
    // 检查默认导出的声明是否为函数
    if (node.decl && this.isFunctionLike(node.decl)) {
      this.addFunction(node);
      // 将内部的函数声明也标记为已找到，避免重复收集
      this.foundNodes.add(node.decl);
    }
    
    return node;
  }

  /**
   * 访问函数表达式节点
   * 
   * 处理直接的函数表达式，如：
   * function() { }
   * function namedFunc() { }
   * 
   * @param node 函数表达式节点
   * @returns 处理后的节点
   */
  public visitFunctionExpression(node: FunctionExpression): FunctionExpression {
    this.addFunction(node);
    return node;
  }

  /**
   * 访问箭头函数表达式节点
   * 
   * 处理箭头函数，如：
   * () => { }
   * (x) => x * 2
   * 
   * @param node 箭头函数表达式节点
   * @returns 处理后的节点
   */
  public visitArrowFunctionExpression(node: ArrowFunctionExpression): ArrowFunctionExpression {
    this.addFunction(node);
    return node;
  }

  /**
   * 访问变量声明节点
   * 
   * @param node 变量声明节点
   * @returns 处理后的节点
   */
  public visitVariableDeclaration(node: VariableDeclaration): VariableDeclaration {
    // 变量声明的具体声明器会通过 visitChildren 自动处理
    // 这里可以添加额外的声明级别处理逻辑（如果需要的话）
    return node;
  }

  /**
   * 添加函数到收集列表
   * 
   * 防重复添加，确保每个函数只被收集一次
   * 
   * @param node 要添加的函数节点
   */
  private addFunction(node: Node): void {
    if (!this.foundNodes.has(node)) {
      this.foundNodes.add(node);
      this.functions.push(node);
    }
  }

  /**
   * 检查节点是否为函数声明
   * 
   * @param node 要检查的节点
   * @returns 如果是函数声明则返回 true
   */
  private isFunctionDeclaration(node: any): boolean {
    return ['FunctionDeclaration', 'ClassMethod', 'TsMethodSignature'].includes(node.type);
  }

  /**
   * 检查节点是否为函数表达式
   * 
   * @param node 要检查的节点
   * @returns 如果是函数表达式则返回 true
   */
  private isFunctionExpression(node: any): boolean {
    return ['FunctionExpression', 'ArrowFunctionExpression'].includes(node.type);
  }

  /**
   * 检查节点是否为函数类型（包括声明和表达式）
   * 
   * @param node 要检查的节点
   * @returns 如果是函数类型则返回 true
   */
  private isFunctionLike(node: any): boolean {
    return this.isFunctionDeclaration(node) || this.isFunctionExpression(node);
  }

  /**
   * 检查方法是否为构造函数
   * 
   * 构造函数通常有特殊的复杂度计算规则，因此被单独识别
   * 
   * @param node 方法节点
   * @returns 如果是构造函数则返回 true
   */
  private isConstructor(node: any): boolean {
    // 检查方法名是否为 'constructor'
    if (node.key?.value === 'constructor') {
      return true;
    }
    
    // 检查 kind 属性（某些 AST 表示中使用）
    if (node.kind === 'constructor') {
      return true;
    }
    
    // 检查 key 的类型和名称
    if (node.key?.type === 'Identifier' && 
        (node.key.value === 'constructor' || node.key.name === 'constructor')) {
      return true;
    }
    
    return false;
  }

  /**
   * 检查方法是否为抽象方法
   * 
   * 抽象方法没有实现体，不需要计算复杂度
   * 
   * @param node 方法节点
   * @returns 如果是抽象方法则返回 true
   */
  private isAbstractMethod(node: any): boolean {
    // 检查方法是否没有函数体
    if (!node.function?.body) {
      return true;
    }

    // 检查 abstract 修饰符
    if (node.function?.declare) {
      return true;
    }

    return false;
  }

  /**
   * 重置访问者状态
   * 
   * 清空收集的函数列表和防重复集合，用于访问者复用
   */
  public override reset(): void {
    super.reset();
    this.functions = [];
    this.foundNodes.clear();
  }

  /**
   * 获取当前收集的函数数量
   * 
   * @returns 已收集的函数数量
   */
  public getFunctionCount(): number {
    return this.functions.length;
  }
}