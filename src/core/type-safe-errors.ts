/**
 * Type-safe error system with hierarchical error categories
 * 
 * This module provides a comprehensive error handling system with proper
 * categorization, severity levels, and recovery capabilities.
 * 
 * Requirements: 1.4, 2.4
 */

import { isObject, isString, isNotNullish } from '../utils/type-guards';

/**
 * Error categories for systematic error classification
 */
export type ErrorCategory = 'type' | 'runtime' | 'validation' | 'system' | 'plugin' | 'config' | 'performance';

/**
 * Severity levels for error impact assessment
 */
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Error context interface for structured error information
 */
export interface ErrorContext extends Record<string, unknown> {
  timestamp?: number;
  component?: string;
  operation?: string;
  userId?: string;
  requestId?: string;
}

/**
 * Abstract base class for all type-safe errors in the system
 * Provides consistent error structure and metadata
 */
export abstract class TypeSafeError extends Error {
  abstract readonly category: ErrorCategory;
  abstract readonly severity: ErrorSeverity;
  abstract readonly isRecoverable: boolean;

  public readonly timestamp: number;
  public readonly errorId: string;

  constructor(
    message: string,
    public readonly context: ErrorContext = {}
  ) {
    super(message);
    this.name = this.constructor.name;
    this.timestamp = Date.now();
    this.errorId = this.generateErrorId();
    
    // Maintain proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, new.target.prototype);
    
    // Capture stack trace if available
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Generate a unique error ID for tracking
   */
  private generateErrorId(): string {
    const timestamp = this.timestamp.toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `${this.category}_${timestamp}_${random}`;
  }

  /**
   * Convert error to structured JSON representation
   */
  toJSON(): Record<string, unknown> {
    return {
      name: this.name,
      message: this.message,
      category: this.category,
      severity: this.severity,
      isRecoverable: this.isRecoverable,
      timestamp: this.timestamp,
      errorId: this.errorId,
      context: this.context,
      stack: this.stack,
    };
  }

  /**
   * Create a user-friendly error message
   */
  getUserMessage(): string {
    switch (this.severity) {
      case 'critical':
        return `Critical error occurred. Please contact support with error ID: ${this.errorId}`;
      case 'high':
        return `An error occurred that may affect functionality. Error ID: ${this.errorId}`;
      case 'medium':
        return `Minor issue detected. Operation may continue with reduced functionality.`;
      case 'low':
        return `Information: ${this.message}`;
      default:
        return this.message;
    }
  }

  /**
   * Check if this error should be reported to monitoring systems
   */
  shouldReport(): boolean {
    return this.severity === 'critical' || this.severity === 'high';
  }

  /**
   * Get suggested recovery actions
   */
  abstract getRecoveryActions(): string[];
}

/**
 * Type validation errors for TypeScript type safety violations
 */
export class TypeValidationError extends TypeSafeError {
  readonly category = 'type' as const;
  readonly severity = 'high' as const;
  readonly isRecoverable = false;

  constructor(
    public readonly expectedType: string,
    public readonly actualType: string,
    public readonly propertyPath: string,
    context: ErrorContext = {}
  ) {
    super(
      `Type validation failed: expected ${expectedType}, got ${actualType} at ${propertyPath}`,
      {
        ...context,
        expectedType,
        actualType,
        propertyPath,
      }
    );
  }

  getRecoveryActions(): string[] {
    return [
      'Check the type definition and ensure correct type is being passed',
      'Verify the data source is providing expected type',
      'Add type guards or validation before the operation',
      'Review the type conversion logic',
    ];
  }
}

/**
 * Object pool related errors
 */
export class ObjectPoolError extends TypeSafeError {
  readonly category = 'runtime' as const;
  readonly severity = 'medium' as const;
  readonly isRecoverable = true;

  constructor(
    public readonly poolType: string,
    message: string,
    context: ErrorContext = {}
  ) {
    super(`Object pool error (${poolType}): ${message}`, {
      ...context,
      poolType,
    });
  }

  getRecoveryActions(): string[] {
    return [
      'Retry the operation after a brief delay',
      'Check if pool size limits need adjustment',
      'Verify object lifecycle management',
      'Clear and reinitialize the pool if necessary',
    ];
  }
}

/**
 * Plugin system errors
 */
export class PluginError extends TypeSafeError {
  readonly category = 'plugin' as const;
  readonly severity: ErrorSeverity;
  readonly isRecoverable: boolean;

  constructor(
    message: string,
    public readonly pluginId: string,
    public readonly pluginOperation: 'loading' | 'execution' | 'unloading' | 'validation',
    isRecoverable: boolean = true,
    public readonly originalError?: Error,
    context: ErrorContext = {}
  ) {
    super(message, {
      ...context,
      pluginId,
      pluginOperation,
      originalError: originalError?.message,
      originalStack: originalError?.stack,
    });

    this.isRecoverable = isRecoverable;
    this.severity = this.determineSeverity(pluginOperation, isRecoverable);
  }

  private determineSeverity(operation: string, recoverable: boolean): ErrorSeverity {
    if (!recoverable) return 'high';
    if (operation === 'loading') return 'medium';
    if (operation === 'execution') return 'medium';
    return 'low';
  }

  getRecoveryActions(): string[] {
    const baseActions = [
      `Disable plugin '${this.pluginId}' temporarily`,
      'Check plugin compatibility and version requirements',
      'Review plugin configuration and permissions',
    ];

    switch (this.pluginOperation) {
      case 'loading':
        return [
          ...baseActions,
          'Verify plugin file integrity',
          'Check if plugin dependencies are installed',
        ];
      case 'execution':
        return [
          ...baseActions,
          'Check plugin input parameters',
          'Review plugin sandbox settings',
        ];
      case 'validation':
        return [
          ...baseActions,
          'Update plugin to compatible version',
          'Fix plugin configuration schema',
        ];
      default:
        return baseActions;
    }
  }
}

/**
 * Configuration related errors
 */
export class ConfigurationError extends TypeSafeError {
  readonly category = 'config' as const;
  readonly severity = 'high' as const;
  readonly isRecoverable = true;

  constructor(
    public readonly configPath: string,
    public readonly configKey?: string,
    message?: string,
    context: ErrorContext = {}
  ) {
    super(
      message || `Configuration error in ${configPath}${configKey ? ` at key '${configKey}'` : ''}`,
      {
        ...context,
        configPath,
        configKey,
      }
    );
  }

  getRecoveryActions(): string[] {
    return [
      'Check configuration file syntax and format',
      'Verify all required configuration keys are present',
      'Validate configuration values against schema',
      'Use default configuration as fallback',
      'Regenerate configuration file if corrupted',
    ];
  }
}

/**
 * Performance related errors and warnings
 */
export class PerformanceError extends TypeSafeError {
  readonly category = 'performance' as const;
  readonly severity: ErrorSeverity;
  readonly isRecoverable = true;

  constructor(
    public readonly operation: string,
    public readonly threshold: number,
    public readonly actualValue: number,
    public readonly unit: string = 'ms',
    context: ErrorContext = {}
  ) {
    super(
      `Performance threshold exceeded for ${operation}: ${actualValue}${unit} > ${threshold}${unit}`,
      {
        ...context,
        operation,
        threshold,
        actualValue,
        unit,
      }
    );

    this.severity = this.determineSeverity(actualValue, threshold);
  }

  private determineSeverity(actual: number, threshold: number): ErrorSeverity {
    const ratio = actual / threshold;
    if (ratio > 5) return 'critical';
    if (ratio > 3) return 'high';
    if (ratio > 2) return 'medium';
    return 'low';
  }

  getRecoveryActions(): string[] {
    return [
      'Optimize the slow operation',
      'Increase performance thresholds if appropriate',
      'Add caching to reduce computation time',
      'Consider breaking operation into smaller chunks',
      'Profile the operation to identify bottlenecks',
    ];
  }
}

/**
 * System resource errors
 */
export class SystemError extends TypeSafeError {
  readonly category = 'system' as const;
  readonly severity: ErrorSeverity;
  readonly isRecoverable: boolean;

  constructor(
    public readonly resource: 'memory' | 'disk' | 'network' | 'cpu' | 'file' | 'process',
    message: string,
    isRecoverable: boolean = true,
    context: ErrorContext = {}
  ) {
    super(`System ${resource} error: ${message}`, {
      ...context,
      resource,
    });

    this.isRecoverable = isRecoverable;
    this.severity = this.determineSeverity(resource, isRecoverable);
  }

  private determineSeverity(resource: string, recoverable: boolean): ErrorSeverity {
    if (!recoverable) return 'critical';
    if (resource === 'memory' || resource === 'disk') return 'high';
    if (resource === 'network' || resource === 'file') return 'medium';
    return 'low';
  }

  getRecoveryActions(): string[] {
    const baseActions = ['Monitor system resources', 'Check system logs'];

    switch (this.resource) {
      case 'memory':
        return [...baseActions, 'Free up memory', 'Restart the application', 'Increase memory limits'];
      case 'disk':
        return [...baseActions, 'Free up disk space', 'Check disk health', 'Clean temporary files'];
      case 'network':
        return [...baseActions, 'Check network connectivity', 'Verify firewall settings', 'Retry with backoff'];
      case 'file':
        return [...baseActions, 'Check file permissions', 'Verify file exists', 'Create missing directories'];
      default:
        return baseActions;
    }
  }
}

/**
 * Utility functions for error handling
 */
export class ErrorUtils {
  /**
   * Safely extract error message from unknown error
   */
  static getErrorMessage(error: unknown): string {
    if (error instanceof Error) {
      return error.message;
    }
    if (isString(error)) {
      return error;
    }
    if (isObject(error) && isString(error.message)) {
      return error.message;
    }
    return 'Unknown error occurred';
  }

  /**
   * Safely extract error stack from unknown error
   */
  static getErrorStack(error: unknown): string | undefined {
    if (error instanceof Error) {
      return error.stack;
    }
    if (isObject(error) && isString(error.stack)) {
      return error.stack;
    }
    return undefined;
  }

  /**
   * Create appropriate TypeSafeError from unknown error
   */
  static wrapError(
    error: unknown,
    category: ErrorCategory = 'runtime',
    context: ErrorContext = {}
  ): TypeSafeError {
    const message = this.getErrorMessage(error);
    const stack = this.getErrorStack(error);

    const wrappedContext: ErrorContext = {
      ...context,
      originalError: error instanceof Error ? error.name : typeof error,
      originalMessage: message,
    };

    if (stack) {
      (wrappedContext as any).originalStack = stack;
    }

    // Create appropriate error type based on category
    switch (category) {
      case 'type':
        return new TypeValidationError('unknown', typeof error, 'wrapped', wrappedContext);
      case 'plugin':
        return new PluginError(message, 'unknown', 'execution', true, error instanceof Error ? error : undefined, wrappedContext);
      case 'config':
        return new ConfigurationError('unknown', undefined, message, wrappedContext);
      case 'performance':
        return new PerformanceError('unknown', 0, 0, 'ms', wrappedContext);
      case 'system':
        return new SystemError('process', message, true, wrappedContext);
      default:
        // For runtime and validation, create a generic runtime error
        return new (class RuntimeError extends TypeSafeError {
          readonly category = 'runtime' as const;
          readonly severity = 'medium' as const;
          readonly isRecoverable = true;

          getRecoveryActions(): string[] {
            return ['Retry the operation', 'Check input parameters', 'Review operation logic'];
          }
        })(message, wrappedContext);
    }
  }

  /**
   * Check if error is of specific type
   */
  static isErrorOfCategory(error: unknown, category: ErrorCategory): boolean {
    return error instanceof TypeSafeError && error.category === category;
  }

  /**
   * Check if error is recoverable
   */
  static isRecoverable(error: unknown): boolean {
    return error instanceof TypeSafeError ? error.isRecoverable : false;
  }

  /**
   * Get all errors of specific category from error array
   */
  static filterErrorsByCategory(errors: unknown[], category: ErrorCategory): TypeSafeError[] {
    return errors.filter((error): error is TypeSafeError => 
      this.isErrorOfCategory(error, category)
    );
  }

  /**
   * Get the most severe error from error array
   */
  static getMostSevereError(errors: TypeSafeError[]): TypeSafeError | null {
    if (errors.length === 0) return null;

    const severityOrder: ErrorSeverity[] = ['critical', 'high', 'medium', 'low'];
    
    return errors.reduce((mostSevere, current) => {
      const currentIndex = severityOrder.indexOf(current.severity);
      const mostSevereIndex = severityOrder.indexOf(mostSevere.severity);
      
      return currentIndex < mostSevereIndex ? current : mostSevere;
    });
  }
}

/**
 * Error aggregator for collecting and managing multiple errors
 */
export class ErrorAggregator {
  private errors: TypeSafeError[] = [];

  /**
   * Add an error to the aggregator
   */
  add(error: TypeSafeError): void {
    this.errors.push(error);
  }

  /**
   * Add multiple errors to the aggregator
   */
  addAll(errors: TypeSafeError[]): void {
    this.errors.push(...errors);
  }

  /**
   * Get all errors
   */
  getAll(): readonly TypeSafeError[] {
    return [...this.errors];
  }

  /**
   * Get errors by category
   */
  getByCategory(category: ErrorCategory): TypeSafeError[] {
    return this.errors.filter(error => error.category === category);
  }

  /**
   * Get errors by severity
   */
  getBySeverity(severity: ErrorSeverity): TypeSafeError[] {
    return this.errors.filter(error => error.severity === severity);
  }

  /**
   * Check if aggregator has any errors
   */
  hasErrors(): boolean {
    return this.errors.length > 0;
  }

  /**
   * Check if aggregator has critical errors
   */
  hasCriticalErrors(): boolean {
    return this.errors.some(error => error.severity === 'critical');
  }

  /**
   * Clear all errors
   */
  clear(): void {
    this.errors = [];
  }

  /**
   * Get error summary
   */
  getSummary(): {
    total: number;
    byCategory: Record<ErrorCategory, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recoverable: number;
    nonRecoverable: number;
  } {
    const byCategory: Record<ErrorCategory, number> = {
      type: 0,
      runtime: 0,
      validation: 0,
      system: 0,
      plugin: 0,
      config: 0,
      performance: 0,
    };

    const bySeverity: Record<ErrorSeverity, number> = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0,
    };

    let recoverable = 0;
    let nonRecoverable = 0;

    for (const error of this.errors) {
      byCategory[error.category]++;
      bySeverity[error.severity]++;
      
      if (error.isRecoverable) {
        recoverable++;
      } else {
        nonRecoverable++;
      }
    }

    return {
      total: this.errors.length,
      byCategory,
      bySeverity,
      recoverable,
      nonRecoverable,
    };
  }
}