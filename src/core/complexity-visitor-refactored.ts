/**
 * ComplexityVisitor 重构计划
 * 将现有的 1244 行复杂度计算逻辑重构为委托模式
 * 
 * 重构策略：
 * 1. 保留核心访问者结构和嵌套管理逻辑
 * 2. 移除所有具体的复杂度计算方法（visit*Statement）
 * 3. 集成 AsyncRuleEngine，通过它处理所有节点
 * 4. 保留 span 修正和详细信息收集功能
 * 5. 简化为纯委托模式，减少代码行数至 < 400 行
 * 
 * 核心变更：
 * - 移除: visitIfStatement, visitWhileStatement, visitForStatement 等具体计算方法
 * - 添加: AsyncRuleEngine 集成和通用节点处理逻辑
 * - 保留: 嵌套管理、span 修正、DetailCollector 集成
 * - 简化: 错误处理和状态管理
 */

import type { Node, Module } from '@swc/core';
import type { CalculationOptions, DiagnosticMarker } from './types';
import { DiagnosticMarker as DiagnosticMarkerEnum } from './types';
import type { AsyncRuleEngine, AnalysisContext } from '../engine/types';
import { BaseVisitor } from './base-visitor';
import { DetailCollector } from './detail-collector';
import { PositionConverter } from '../utils/position-converter';

/**
 * SWC 节点的 span 类型定义
 */
interface SwcSpan {
  start: number;
  end: number;
  ctxt: number;
}

/**
 * 具有 span 属性的节点类型
 */
interface NodeWithSpan extends Node {
  span: SwcSpan;
}

/**
 * ComplexityVisitor - 重构版认知复杂度计算访问者
 * 
 * 基于委托模式的轻量级访问者实现，通过 AsyncRuleEngine 处理所有复杂度计算。
 * 
 * 核心职责：
 * - AST 节点遍历和嵌套层级管理
 * - 委托给 AsyncRuleEngine 进行复杂度计算
 * - span 修正和位置转换
 * - 与 DetailCollector 的集成
 * 
 * 设计原则：
 * - 单一职责：只负责访问者模式的核心功能
 * - 委托模式：将复杂度计算委托给规则引擎
 * - 错误恢复：部分节点失败不影响整体计算
 */
export class ComplexityVisitor extends BaseVisitor {
  /**
   * 源代码内容，用于位置转换和 span 修正
   */
  private readonly sourceCode: string;

  /**
   * 详细信息收集器，用于记录复杂度计算步骤
   */
  private readonly detailCollector?: DetailCollector;

  /**
   * 计算选项，用于配置复杂度计算行为
   */
  private readonly options: CalculationOptions;

  /**
   * 异步规则引擎，负责所有复杂度计算
   */
  private readonly ruleEngine: AsyncRuleEngine;

  /**
   * 分析上下文，用于规则引擎调用
   */
  private readonly analysisContext: AnalysisContext;

  /**
   * 当前累计复杂度
   */
  private totalComplexity: number = 0;

  /**
   * 当前嵌套层级
   */
  private nestingLevel: number = 0;

  /**
   * 构造函数
   * @param sourceCode 源代码内容
   * @param ruleEngine 异步规则引擎
   * @param analysisContext 分析上下文
   * @param detailCollector 可选的详细信息收集器
   * @param options 可选的计算选项
   */
  constructor(
    sourceCode: string,
    ruleEngine: AsyncRuleEngine,
    analysisContext: AnalysisContext,
    detailCollector?: DetailCollector,
    options: CalculationOptions = {}
  ) {
    super();
    this.sourceCode = sourceCode;
    this.ruleEngine = ruleEngine;
    this.analysisContext = analysisContext;
    this.detailCollector = detailCollector;
    this.options = options;
  }

  /**
   * 获取当前累计的总复杂度
   */
  public getTotalComplexity(): number {
    return this.totalComplexity;
  }

  /**
   * 获取当前嵌套层级
   */
  public getCurrentNestingLevel(): number {
    return this.nestingLevel;
  }

  /**
   * 重置访问者状态
   */
  public resetComplexity(): void {
    this.totalComplexity = 0;
    this.nestingLevel = 0;
    this.reset(); // 调用父类的重置方法
  }

  /**
   * 重写 visit 方法以支持委托模式和嵌套层级管理
   */
  public override visit<T extends Node>(node: T): T {
    // 检查这个节点是否会影响嵌套层级
    const shouldManageNesting = this.shouldEnterNesting(node);
    
    if (shouldManageNesting) {
      this.enterNesting();
      const result = super.visit(node);
      this.exitNesting();
      return result;
    } else {
      return super.visit(node);
    }
  }

  /**
   * 核心节点处理方法 - 委托给规则引擎
   */
  protected override visitNode(node: Node): Node {
    // 异步处理节点，但在同步上下文中需要特殊处理
    this.processNodeAsync(node).catch(error => {
      this.handleNodeProcessingError(node, error);
    });

    // 继续遍历子节点
    return node;
  }

  /**
   * 异步处理单个节点
   */
  private async processNodeAsync(node: Node): Promise<void> {
    try {
      // 创建当前节点的分析上下文
      const nodeContext = this.createNodeContext(node);
      
      // 委托给规则引擎进行分析
      const analysis = await this.ruleEngine.analyzeNode(node, nodeContext);
      
      // 处理分析结果
      this.processAnalysisResult(node, analysis);
      
    } catch (error) {
      this.handleNodeProcessingError(node, error as Error);
    }
  }

  /**
   * 创建节点分析上下文
   */
  private createNodeContext(node: Node): AnalysisContext {
    return {
      ...this.analysisContext,
      nestingLevel: this.nestingLevel,
      currentNode: node,
    } as AnalysisContext;
  }

  /**
   * 处理分析结果
   */
  private processAnalysisResult(node: Node, analysis: any): void {
    if (analysis.complexity > 0) {
      this.totalComplexity += analysis.complexity;
      
      // 记录详细信息
      if (this.detailCollector && analysis.appliedRules) {
        this.recordAnalysisDetails(node, analysis);
      }
    }
  }

  /**
   * 记录分析详细信息
   */
  private recordAnalysisDetails(node: Node, analysis: any): void {
    try {
      const position = this.getNodePosition(node);
      
      for (const ruleApplication of analysis.appliedRules) {
        this.detailCollector?.addStepWithDiagnostic(
          {
            line: position.line,
            column: position.column,
            increment: ruleApplication.complexity,
            ruleId: ruleApplication.ruleId,
            description: ruleApplication.description || `${node.type} complexity`,
            context: `Nesting: ${this.nestingLevel}, Rule: ${ruleApplication.ruleId}`
          },
          DiagnosticMarkerEnum.NONE,
          ruleApplication.reason || `Applied rule ${ruleApplication.ruleId} to ${node.type}`
        );
      }
    } catch (error) {
      console.warn(`Failed to record analysis details for ${node.type}:`, error);
    }
  }

  /**
   * 获取节点位置信息
   */
  private getNodePosition(node: Node): { line: number; column: number } {
    try {
      const span = this.validateSpan(node);
      return PositionConverter.spanToPosition(this.sourceCode, span);
    } catch (error) {
      return { line: 1, column: 1 }; // 默认位置
    }
  }

  /**
   * 验证并修正节点的 span 信息
   */
  private validateSpan(node: Node): number {
    try {
      if (this.isValidSpan(node)) {
        return (node as NodeWithSpan).span.start;
      }
      
      // 尝试父节点回退
      const correctedSpan = this.attemptParentSpanFallback(node);
      if (correctedSpan !== null) {
        return correctedSpan;
      }
      
      // 默认回退
      return 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 检查 span 是否有效
   */
  private isValidSpan(node: Node): boolean {
    try {
      const nodeWithSpan = node as NodeWithSpan;
      return nodeWithSpan.span && 
             typeof nodeWithSpan.span.start === 'number' && 
             nodeWithSpan.span.start >= 0 &&
             nodeWithSpan.span.start <= this.sourceCode.length;
    } catch {
      return false;
    }
  }

  /**
   * 尝试父节点 span 回退
   */
  private attemptParentSpanFallback(node: Node): number | null {
    try {
      const parent = this.getParent();
      if (parent && this.isValidSpan(parent)) {
        return (parent as NodeWithSpan).span.start;
      }
      return null;
    } catch {
      return null;
    }
  }

  /**
   * 判断节点是否应该影响嵌套层级
   */
  private shouldEnterNesting(node: Node): boolean {
    switch (node.type) {
      case 'IfStatement':
      case 'WhileStatement':
      case 'DoWhileStatement':
      case 'ForStatement':
      case 'ForInStatement':
      case 'ForOfStatement':
      case 'SwitchStatement':
      case 'TryStatement':
      case 'CatchClause':
        return true;
      default:
        return false;
    }
  }

  /**
   * 进入嵌套层级
   */
  private enterNesting(): void {
    this.nestingLevel++;
  }

  /**
   * 退出嵌套层级
   */
  private exitNesting(): void {
    if (this.nestingLevel > 0) {
      this.nestingLevel--;
    }
  }

  /**
   * 处理节点处理错误
   */
  private handleNodeProcessingError(node: Node, error: Error): void {
    console.warn(`Failed to process node ${node.type}:`, error);
    
    if (this.detailCollector) {
      try {
        const position = this.getNodePosition(node);
        this.detailCollector.addStepWithDiagnostic(
          {
            line: position.line,
            column: position.column,
            increment: 0,
            ruleId: 'error-recovery',
            description: `Error processing ${node.type}`,
            context: `Error: ${error.message}`
          },
          DiagnosticMarkerEnum.ERROR,
          `Failed to process ${node.type}: ${error.message}`
        );
      } catch (detailError) {
        console.warn('Failed to record error details:', detailError);
      }
    }
  }
}