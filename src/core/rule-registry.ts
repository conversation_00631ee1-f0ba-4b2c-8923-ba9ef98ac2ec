import { type RuleMetadata, RuleCategory, type RuleRegistrationConfig } from './types';
import type { Rule } from '../engine/types';

/**
 * 从Rule实例创建RuleMetadata
 * 用于将基于类的规则与RuleRegistry集成
 * @param rule Rule实例
 * @param category 规则分类（可选，默认推断）
 * @returns RuleMetadata对象
 */
function createMetadataFromRule(rule: Rule, category?: RuleCategory): RuleMetadata {
  // 根据规则ID推断分类
  const inferredCategory = category || inferCategoryFromRuleId(rule.id);
  
  return {
    ruleId: rule.id,
    description: rule.name,
    category: inferredCategory,
    defaultIncrement: 1, // 基于类的规则复杂度通过evaluate方法动态计算
    enabled: true,
    priority: rule.priority,
  };
}

/**
 * 根据规则ID推断分类
 * @param ruleId 规则ID
 * @returns 推断的规则分类
 */
function inferCategoryFromRuleId(ruleId: string): RuleCategory {
  if (ruleId.includes('if') || ruleId.includes('for') || ruleId.includes('while') || ruleId.includes('switch')) {
    return RuleCategory.CONTROL_FLOW;
  }
  if (ruleId.includes('logical')) {
    return RuleCategory.LOGICAL_OPERATOR;
  }
  if (ruleId.includes('recursive') || ruleId.includes('recursion')) {
    return RuleCategory.RECURSION;
  }
  if (ruleId.includes('catch') || ruleId.includes('exception')) {
    return RuleCategory.EXCEPTION_HANDLING;
  }
  if (ruleId.includes('jsx')) {
    return RuleCategory.JSX;
  }
  if (ruleId.includes('nesting')) {
    return RuleCategory.NESTING;
  }
  return RuleCategory.OTHER;
}

/**
 * RuleRegistry - 规则注册表
 *
 * 维护所有复杂度规则的标识符和描述信息的全局注册表。
 * 使用静态方法提供规则注册、查询和验证功能。
 * 确保规则ID遵循kebab-case命名约定。
 */
export class RuleRegistry {
  // 静态存储所有已注册的规则
  private static rules: Map<string, RuleMetadata> = new Map();

  // 规则ID格式验证正则表达式 (kebab-case)
  private static readonly RULE_ID_PATTERN = /^[a-z][a-z0-9]*(-[a-z0-9]+)*$/;

  /**
   * 注册单个规则
   * @param ruleId 规则标识符 (kebab-case 格式)
   * @param description 规则描述
   * @param category 规则分类 (可选，默认为OTHER)
   * @param defaultIncrement 默认复杂度增量 (可选，默认为1)
   */
  public static register(
    ruleId: string,
    description: string,
    category: RuleCategory = RuleCategory.OTHER,
    defaultIncrement: number = 1
  ): void {
    // 验证规则ID格式
    if (!this.validateRuleId(ruleId)) {
      throw new Error(
        `Invalid rule ID format: "${ruleId}". Rule IDs must follow kebab-case convention (e.g., "if-statement", "logical-and").`
      );
    }

    // 检查是否已存在
    if (this.rules.has(ruleId)) {
      throw new Error(`Rule ID "${ruleId}" is already registered. Use updateRule() to modify existing rules.`);
    }

    // 验证描述
    if (!description || description.trim().length === 0) {
      throw new Error(`Rule description cannot be empty for rule ID: "${ruleId}"`);
    }

    // 验证默认增量
    if (typeof defaultIncrement !== 'number' || defaultIncrement < 0) {
      throw new Error(`Default increment must be a non-negative number for rule ID: "${ruleId}"`);
    }

    const ruleMetadata: RuleMetadata = {
      ruleId,
      description: description.trim(),
      category,
      defaultIncrement,
      enabled: true,
      priority: 0,
    };

    this.rules.set(ruleId, ruleMetadata);
  }

  /**
   * 从Rule实例注册规则
   * 支持基于类的规则与RuleRegistry的集成
   * @param rule Rule实例 
   * @param category 可选的规则分类，如果不提供会自动推断
   * @param quiet 是否静默注册（不输出日志）
   */
  public static registerFromRule(rule: Rule, category?: RuleCategory, quiet: boolean = false): void {
    const metadata = createMetadataFromRule(rule, category);
    
    try {
      this.registerRule(metadata);
      if (!quiet) {
        console.log(`Rule '${rule.id}' registered successfully`);
      }
    } catch (error) {
      if (!quiet) {
        console.error(`Failed to register rule '${rule.id}':`, error);
      }
      throw error;
    }
  }

  /**
   * 批量从Rule实例注册规则
   * @param rules Rule实例数组
   * @param quiet 是否静默注册
   */
  public static registerFromRules(rules: Rule[], quiet: boolean = false): void {
    for (const rule of rules) {
      try {
        this.registerFromRule(rule, undefined, true); // 单个注册时静默
      } catch (error) {
        if (!quiet) {
          console.error(`Failed to register rule '${rule.id}':`, error);
        }
      }
    }
    
    if (!quiet) {
      console.log(`Successfully registered ${rules.length} class-based rules`);
    }
  }

  /**
   * 注册规则的重载方法 - 接受完整的RuleMetadata对象
   * @param ruleMetadata 规则元数据对象
   */
  public static registerRule(ruleMetadata: RuleMetadata): void {
    this.register(ruleMetadata.ruleId, ruleMetadata.description, ruleMetadata.category, ruleMetadata.defaultIncrement);

    // 设置可选属性
    if (ruleMetadata.enabled !== undefined) {
      this.rules.get(ruleMetadata.ruleId)!.enabled = ruleMetadata.enabled;
    }
    if (ruleMetadata.priority !== undefined) {
      this.rules.get(ruleMetadata.ruleId)!.priority = ruleMetadata.priority;
    }
  }

  /**
   * 批量注册规则
   * @param config 规则注册配置
   */
  public static registerBatch(config: RuleRegistrationConfig): void {
    const { rules, overwrite = false } = config;

    for (const ruleMetadata of rules) {
      if (this.rules.has(ruleMetadata.ruleId) && !overwrite) {
        console.warn(`Skipping already registered rule: ${ruleMetadata.ruleId}`);
        continue;
      }

      if (overwrite && this.rules.has(ruleMetadata.ruleId)) {
        this.rules.delete(ruleMetadata.ruleId);
      }

      this.registerRule(ruleMetadata);
    }
  }

  /**
   * 获取规则描述
   * @param ruleId 规则标识符
   * @returns 规则描述，如果不存在返回null
   */
  public static getDescription(ruleId: string): string | null {
    const rule = this.rules.get(ruleId);
    return rule ? rule.description : null;
  }

  /**
   * 获取规则元数据
   * @param ruleId 规则标识符
   * @returns 规则元数据，如果不存在返回null
   */
  public static getRule(ruleId: string): RuleMetadata | null {
    return this.rules.get(ruleId) || null;
  }

  /**
   * 获取所有已注册的规则
   * @returns 所有规则元数据的数组
   */
  public static getAllRules(): RuleMetadata[] {
    return Array.from(this.rules.values());
  }

  /**
   * 获取指定分类的规则
   * @param category 规则分类
   * @returns 指定分类的规则数组
   */
  public static getRulesByCategory(category: RuleCategory): RuleMetadata[] {
    return Array.from(this.rules.values()).filter((rule) => rule.category === category);
  }

  /**
   * 检查规则是否已注册
   * @param ruleId 规则标识符
   * @returns 是否已注册
   */
  public static hasRule(ruleId: string): boolean {
    return this.rules.has(ruleId);
  }

  /**
   * 获取所有已注册的规则ID
   * @returns 规则ID数组
   */
  public static getAllRuleIds(): string[] {
    return Array.from(this.rules.keys());
  }

  /**
   * 更新已存在的规则
   * @param ruleId 规则标识符
   * @param updates 要更新的字段
   */
  public static updateRule(ruleId: string, updates: Partial<Omit<RuleMetadata, 'ruleId'>>): void {
    const existingRule = this.rules.get(ruleId);
    if (!existingRule) {
      throw new Error(`Cannot update non-existent rule: "${ruleId}"`);
    }

    const updatedRule: RuleMetadata = {
      ...existingRule,
      ...updates,
    };

    this.rules.set(ruleId, updatedRule);
  }

  /**
   * 取消注册规则
   * @param ruleId 规则标识符
   * @returns 是否成功取消注册
   */
  public static unregister(ruleId: string): boolean {
    return this.rules.delete(ruleId);
  }

  /**
   * 清空所有已注册的规则
   */
  public static clear(): void {
    this.rules.clear();
  }

  /**
   * 验证规则ID格式是否符合kebab-case约定
   * @param ruleId 规则标识符
   * @returns 是否符合格式
   */
  public static validateRuleId(ruleId: string): boolean {
    return typeof ruleId === 'string' && this.RULE_ID_PATTERN.test(ruleId);
  }

  /**
   * 获取规则统计信息
   * @returns 规则统计信息
   */
  public static getStatistics(): {
    total: number;
    byCategory: Record<RuleCategory, number>;
    enabled: number;
    disabled: number;
  } {
    const rules = Array.from(this.rules.values());
    const byCategory = {} as Record<RuleCategory, number>;

    // 初始化分类计数器
    Object.values(RuleCategory).forEach((category) => {
      byCategory[category] = 0;
    });

    let enabled = 0;
    let disabled = 0;

    rules.forEach((rule) => {
      byCategory[rule.category]++;
      if (rule.enabled !== false) {
        enabled++;
      } else {
        disabled++;
      }
    });

    return {
      total: rules.length,
      byCategory,
      enabled,
      disabled,
    };
  }

  /**
   * 导出所有规则配置
   * @returns 规则注册配置对象
   */
  public static exportConfig(): RuleRegistrationConfig {
    return {
      rules: this.getAllRules(),
      version: '1.0.0',
      description: 'Exported rule configuration',
      overwrite: false,
    };
  }

  /**
   * 验证规则注册配置的完整性
   * @param config 规则注册配置
   * @returns 验证结果
   */
  public static validateConfig(config: RuleRegistrationConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.rules || !Array.isArray(config.rules)) {
      errors.push('Config must contain a rules array');
      return { isValid: false, errors };
    }

    if (!config.version || typeof config.version !== 'string') {
      errors.push('Config must contain a version string');
    }

    for (let i = 0; i < config.rules.length; i++) {
      const rule = config.rules[i]!;

      if (!this.validateRuleId(rule.ruleId)) {
        errors.push(`Rule at index ${i} has invalid ID format: ${rule.ruleId}`);
      }

      if (!rule.description || typeof rule.description !== 'string') {
        errors.push(`Rule at index ${i} (${rule.ruleId}) has invalid description`);
      }

      if (typeof rule.defaultIncrement !== 'number' || rule.defaultIncrement < 0) {
        errors.push(`Rule at index ${i} (${rule.ruleId}) has invalid defaultIncrement`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
