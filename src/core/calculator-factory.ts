/**
 * 计算器工厂实现
 * 基于IoC模式的组件工厂，支持配置驱动的组件创建和依赖注入
 */

import type { 
  CalculatorOptions,
  MonitorOptions,
  PluginConfig 
} from '../engine/types';
import { LightweightExecutionPool, ParallelExecutionPool } from '../engine/execution-pool';
import { NullPerformanceMonitor, AdvancedPerformanceMonitor } from '../engine/performance-monitor';
import { RuleInitializationManager } from './rule-initialization';
import type { ExecutionOptions, PerformanceMonitorConfig } from '../engine/types';

/**
 * 组件工厂接口
 * 定义了所有可创建组件的工厂方法
 */
export interface ComponentFactory {
  /**
   * 创建执行池实例
   * @param options 执行选项
   * @returns 执行池实例（可能是完整版或轻量版）
   */
  createExecutionPool(options?: ExecutionOptions): any;

  /**
   * 创建性能监控器实例
   * @param config 监控器配置
   * @returns 性能监控器实例（可能是完整版或空对象版）
   */
  createPerformanceMonitor(config?: Partial<PerformanceMonitorConfig>): any;

  /**
   * 创建缓存管理器实例
   * @param size 缓存大小
   * @returns 缓存管理器实例
   */
  createCacheManager(size?: number): any;

  /**
   * 创建插件管理器实例
   * @param plugins 插件配置列表
   * @returns 插件管理器实例
   */
  createPluginManager(plugins?: PluginConfig[]): any;

  /**
   * 创建规则初始化管理器实例
   * @returns 规则初始化管理器实例
   */
  createRuleManager(): any;
}

/**
 * 计算器组件工厂实现
 * 基于配置选项创建相应的组件实例，支持Null Object模式
 */
export class CalculatorFactory implements ComponentFactory {
  private readonly options: Required<CalculatorOptions>;

  constructor(options: CalculatorOptions = {}) {
    // 设置默认配置选项
    this.options = {
      enableMonitoring: options.enableMonitoring ?? false,
      monitorConfig: {
        collectMetrics: options.monitorConfig?.collectMetrics ?? false,
        performanceThreshold: options.monitorConfig?.performanceThreshold ?? 100,
        metricsInterval: options.monitorConfig?.metricsInterval ?? 1000,
        enablePerformanceLogging: options.monitorConfig?.enablePerformanceLogging ?? false,
        ...options.monitorConfig
      },
      plugins: options.plugins ?? [],
      enabledRuleIds: options.enabledRuleIds ?? [],
      maxConcurrency: options.maxConcurrency ?? 4,
      enableCaching: options.enableCaching ?? true,
      debugMode: options.debugMode ?? false,
      quiet: options.quiet ?? false,
      enableDetails: options.enableDetails ?? false,
      streamingThreshold: options.streamingThreshold ?? 1000,
      cacheSize: options.cacheSize ?? 10000,
      detailLevel: options.detailLevel ?? 'standard',
      includeMetrics: options.includeMetrics ?? false,
      ruleEngineConfig: {
        maxRuleConcurrency: options.ruleEngineConfig?.maxRuleConcurrency ?? 10,
        enableRuleCaching: options.ruleEngineConfig?.enableRuleCaching ?? true,
        ruleDebugMode: options.ruleEngineConfig?.ruleDebugMode ?? false,
        ...options.ruleEngineConfig
      },
    };
  }

  /**
   * 创建执行池实例
   * 根据配置决定创建完整的并行执行池还是轻量级执行池
   */
  createExecutionPool(options?: ExecutionOptions): ParallelExecutionPool | LightweightExecutionPool {
    const executionOptions: ExecutionOptions = {
      maxConcurrency: options?.maxConcurrency ?? this.options.maxConcurrency,
      timeout: options?.timeout ?? 5000,
      enableProfiling: options?.enableProfiling ?? this.options.enableMonitoring,
      isolateErrors: options?.isolateErrors ?? true,
    };

    // 如果禁用监控或者要求轻量级模式，返回轻量级执行池
    if (!this.options.enableMonitoring || this.options.maxConcurrency === 1) {
      return new LightweightExecutionPool(executionOptions);
    }

    // 否则返回完整的并行执行池
    return new ParallelExecutionPool(executionOptions);
  }

  /**
   * 创建性能监控器实例
   * 根据配置决定创建完整的监控器还是空对象监控器
   */
  createPerformanceMonitor(config?: Partial<PerformanceMonitorConfig>): AdvancedPerformanceMonitor | NullPerformanceMonitor {
    // 如果禁用监控，返回空对象监控器
    if (!this.options.enableMonitoring) {
      return new NullPerformanceMonitor(config);
    }

    // 构建完整的监控器配置
    const monitorConfig: Partial<PerformanceMonitorConfig> = {
      enabled: true,
      enableRealTimeMonitoring: this.options.monitorConfig.collectMetrics,
      enableTrendAnalysis: this.options.monitorConfig.collectMetrics,
      enableHotspotDetection: this.options.monitorConfig.collectMetrics,
      enableMemoryTracking: this.options.monitorConfig.collectMetrics,
      
      thresholds: {
        ruleExecutionTime: this.options.monitorConfig?.performanceThreshold ?? 100,
        functionAnalysisTime: (this.options.monitorConfig?.performanceThreshold ?? 100) * 2,
        fileAnalysisTime: (this.options.monitorConfig?.performanceThreshold ?? 100) * 10,
        memoryUsage: 100, // 100MB
        cacheHitRate: 70, // 70%
        cpuUsage: 80, // 80%
      },
      
      dataRetention: {
        trendDataPoints: 100,
        maxHotspots: 20,
        maxBottlenecks: 10,
      },
      
      reporting: {
        includeRecommendations: this.options.includeMetrics,
        includeTrends: this.options.includeMetrics,
        verbosityLevel: this.options.detailLevel === 'verbose' ? 'detailed' : 
                       this.options.detailLevel === 'minimal' ? 'minimal' : 'standard',
      },
      
      ...config
    };

    return new AdvancedPerformanceMonitor(monitorConfig);
  }

  /**
   * 创建缓存管理器实例
   * 根据配置创建合适的缓存管理器
   */
  createCacheManager(size?: number): any {
    // 如果禁用缓存，可以返回空缓存管理器
    if (!this.options.enableCaching) {
      return new NullCacheManager();
    }

    // 动态导入实际的缓存管理器并创建实例
    // 这里先返回一个占位符，实际实现需要根据具体的缓存管理器类
    return {
      size: size ?? this.options.cacheSize,
      enabled: true,
      // 实际的缓存管理器方法会在这里实现
    };
  }

  /**
   * 创建插件管理器实例
   * 根据配置创建插件管理器并加载指定插件
   */
  createPluginManager(plugins?: PluginConfig[]): any {
    const pluginList = plugins ?? this.options.plugins;
    
    // 如果没有插件配置，返回空插件管理器
    if (pluginList.length === 0) {
      return new NullPluginManager();
    }

    // 创建实际的插件管理器
    // 这里先返回一个占位符，实际实现需要根据具体的插件管理器类
    return {
      plugins: pluginList,
      // 实际的插件管理器方法会在这里实现
    };
  }

  /**
   * 创建规则初始化管理器实例
   * 根据配置创建合适的规则管理器
   */
  createRuleManager(): RuleInitializationManager {
    // 创建规则初始化管理器实例
    // 传递quiet选项以控制日志输出
    const ruleManager = new RuleInitializationManager(this.options.quiet);
    
    // 确保在这里直接返回管理器，不要重写方法
    // 在使用时再传递配置参数
    return ruleManager;
  }

  /**
   * 获取当前工厂配置
   */
  getOptions(): Required<CalculatorOptions> {
    return { ...this.options };
  }

  /**
   * 创建配置副本，用于创建新的工厂实例
   */
  withOptions(newOptions: CalculatorOptions): CalculatorFactory {
    const mergedOptions = {
      ...this.options,
      ...newOptions,
      monitorConfig: {
        ...this.options.monitorConfig,
        ...newOptions.monitorConfig
      }
    };
    return new CalculatorFactory(mergedOptions);
  }

  /**
   * 检查是否启用了特定功能
   */
  isFeatureEnabled(feature: keyof CalculatorOptions): boolean {
    return Boolean(this.options[feature]);
  }

  /**
   * 获取功能级别的配置汇总
   */
  getFeatureSummary(): {
    monitoring: boolean;
    caching: boolean;
    debugging: boolean;
    plugins: boolean;
    parallelExecution: boolean;
  } {
    return {
      monitoring: this.options.enableMonitoring,
      caching: this.options.enableCaching,
      debugging: this.options.debugMode,
      plugins: this.options.plugins.length > 0,
      parallelExecution: this.options.maxConcurrency > 1,
    };
  }
}

/**
 * 空缓存管理器（Null Object模式）
 */
class NullCacheManager {
  get(_key: string): any {
    return undefined;
  }

  set(_key: string, _value: any): void {
    // 空操作
  }

  clear(): void {
    // 空操作
  }

  size(): number {
    return 0;
  }

  getStats(): any {
    return {
      hits: 0,
      misses: 0,
      size: 0,
      hitRate: 0
    };
  }
}

/**
 * 空插件管理器（Null Object模式）
 */
class NullPluginManager {
  loadPlugin(_config: PluginConfig): void {
    // 空操作
  }

  unloadPlugin(_pluginId: string): void {
    // 空操作
  }

  getLoadedPlugins(): any[] {
    return [];
  }

  isPluginLoaded(_pluginId: string): boolean {
    return false;
  }
}

/**
 * 创建默认工厂实例的便捷函数
 */
export function createCalculatorFactory(options?: CalculatorOptions): CalculatorFactory {
  return new CalculatorFactory(options);
}

/**
 * 创建轻量级工厂实例的便捷函数
 * 禁用所有重量级功能，适用于简单的一次性分析
 */
export function createLightweightFactory(): CalculatorFactory {
  return new CalculatorFactory({
    enableMonitoring: false,
    enableCaching: false,
    maxConcurrency: 1,
    debugMode: false,
    quiet: true,
    detailLevel: 'minimal',
    includeMetrics: false,
  });
}

/**
 * 创建完整功能工厂实例的便捷函数
 * 启用所有高级功能，适用于持续监控和性能分析
 */
export function createFullFeaturedFactory(options?: Partial<CalculatorOptions>): CalculatorFactory {
  return new CalculatorFactory({
    enableMonitoring: true,
    enableCaching: true,
    maxConcurrency: 8,
    debugMode: false,
    quiet: false,
    detailLevel: 'verbose',
    includeMetrics: true,
    monitorConfig: {
      collectMetrics: true,
      performanceThreshold: 50,
      metricsInterval: 1000,
      enablePerformanceLogging: true,
    },
    ...options
  });
}