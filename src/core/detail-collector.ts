import type { DetailStep, FunctionDetail, RuleMetadata } from './types';
import { DiagnosticMarker } from './types';
import { 
  TypeSafeDetailStepPool, 
  TypeSafeFunctionContextPool
} from './object-pool';
import {
  type ReadonlyDetailStep,
  type ReadonlyFunctionContext,
  type InternalDetailStep,
  type InternalFunctionContext
} from './object-pool-types';
import { DetailCollectionError } from './errors';
import { getErrorOutputFormatter } from '../utils/error-output-formatter';
import { isNotNullish } from '../utils/type-guards';
import { safeGet } from '../utils/safe-operations';

/**
 * DetailCollector - 详细计算步骤收集器
 * 
 * 在复杂度计算过程中收集详细步骤信息，包括每个复杂度增量的详细数据。
 * 支持嵌套函数的独立上下文跟踪和累计复杂度计算。
 * 使用对象池优化内存分配性能。
 * 集成错误处理和恢复机制。
 */
export class DetailCollector {
  // 使用栈来管理嵌套函数上下文
  private functionStack: ReadonlyFunctionContext[] = [];
  
  private ruleRegistry: Map<string, RuleMetadata> = new Map();
  
  // 类型安全的对象池
  private detailStepPool: TypeSafeDetailStepPool;
  private functionContextPool: TypeSafeFunctionContextPool;
  
  // 性能监控
  private stepsCollected: number = 0;
  private memoryOptimized: boolean = true;

  // 错误处理相关
  private errorFormatter = getErrorOutputFormatter();
  private errors: DetailCollectionError[] = [];
  private warnings: string[] = [];
  private recoveryAttempts = 0;

  constructor() {
    this.detailStepPool = new TypeSafeDetailStepPool();
    this.functionContextPool = new TypeSafeFunctionContextPool();
  }

  /**
   * 开始跟踪一个函数的复杂度计算
   * 支持嵌套函数，会自动管理嵌套层级
   * 使用对象池优化内存分配
   * @param name 函数名称
   * @param line 函数起始行号
   * @param column 函数起始列号
   */
  public startFunction(name: string, line: number, column: number): void {
    const nestingLevel = this.functionStack.length;
    
    // 从对象池获取函数上下文
    const functionContext = this.functionContextPool.acquire();
    
    // 类型安全地转换为内部可变接口进行初始化
    const internalContext = functionContext as InternalFunctionContext;
    internalContext.name = name;
    internalContext.line = line;
    internalContext.column = column;
    internalContext.complexity = 0;
    internalContext.steps = [];
    internalContext.nestingLevel = nestingLevel;

    this.functionStack.push(functionContext);
  }

  /**
   * 添加一个复杂度计算步骤
   * 只会影响当前最内层函数的复杂度计算
   * 集成错误处理和恢复机制
   * @param step 复杂度步骤（不包含累计值和嵌套层级，将自动计算）
   */
  public addStep(step: Omit<DetailStep, 'cumulative' | 'nestingLevel'>): void {
    try {
      if (this.functionStack.length === 0) {
        throw new Error('No function is currently being tracked. Please call startFunction() first.');
      }

      // 验证步骤数据
      this.validateStepInternal(step);

      const currentFunction = this.functionStack[this.functionStack.length - 1];
      
      if (!isNotNullish(currentFunction)) {
        throw new DetailCollectionError(
          '没有活动的函数上下文',
          new Error('NO_ACTIVE_FUNCTION')
        );
      }
      
      // 类型安全地转换为内部可变接口来修改复杂度
      const internalContext = currentFunction as InternalFunctionContext;
      internalContext.complexity += step.increment;
      this.stepsCollected++;

      // 处理规则标识符，如果未知则添加诊断标记
      let finalStep = { ...step };
      if (!this.ruleRegistry.has(step.ruleId)) {
        finalStep = this.handleUnknownRule(step);
      }

      // 从对象池获取 DetailStep 对象
      const detailStep = this.detailStepPool.acquire();
      
      // 类型安全地转换为内部可变接口来填充数据
      const internalStep = detailStep as InternalDetailStep;
      Object.assign(internalStep, {
        ...finalStep,
        cumulative: internalContext.complexity || 0,
        nestingLevel: internalContext.nestingLevel || 0
      });

      // 添加到当前函数的步骤列表
      internalContext.steps.push(detailStep);

    } catch (error) {
      // 特定错误直接重新抛出，不进入错误处理机制
      if (error instanceof Error && error.message.includes('No function is currently being tracked')) {
        throw error;
      }
      
      const detailError = new DetailCollectionError(
        `addStep for rule ${step.ruleId}`,
        error as Error
      );
      this.handleError(detailError);
    }
  }

  /**
   * 添加带有诊断标记的步骤
   * @param step 基础步骤信息
   * @param marker 诊断标记
   * @param message 诊断消息
   */
  public addStepWithDiagnostic(
    step: Omit<DetailStep, 'cumulative' | 'nestingLevel' | 'diagnosticMarker' | 'diagnosticMessage'>,
    marker: DiagnosticMarker,
    message: string
  ): void {
    const diagnosticStep = {
      ...step,
      diagnosticMarker: marker,
      diagnosticMessage: message
    };
    this.addStep(diagnosticStep);
  }

  /**
   * 处理未知规则的情况
   * @param step 原始步骤
   * @returns 带有诊断标记的步骤
   */
  private handleUnknownRule(step: Omit<DetailStep, 'cumulative' | 'nestingLevel'>): typeof step {
    return {
      ...step,
      ruleId: step.ruleId || 'unknown-rule',
      description: step.description || '未知规则',
      diagnosticMarker: DiagnosticMarker.UNKNOWN,
      diagnosticMessage: `未知规则标识符: ${step.ruleId}`
    };
  }

  /**
   * 添加警告步骤
   * @param step 基础步骤信息
   * @param warningMessage 警告消息
   */
  public addWarningStep(
    step: Omit<DetailStep, 'cumulative' | 'nestingLevel' | 'diagnosticMarker' | 'diagnosticMessage'>,
    warningMessage: string
  ): void {
    this.addStepWithDiagnostic(step, DiagnosticMarker.WARNING, warningMessage);
  }

  /**
   * 添加错误步骤
   * @param step 基础步骤信息
   * @param errorMessage 错误消息
   */
  public addErrorStep(
    step: Omit<DetailStep, 'cumulative' | 'nestingLevel' | 'diagnosticMarker' | 'diagnosticMessage'>,
    errorMessage: string
  ): void {
    this.addStepWithDiagnostic(step, DiagnosticMarker.ERROR, errorMessage);
  }

  /**
   * 检测并处理嵌套上下文异常
   * @returns 是否检测到异常
   */
  public detectNestingAnomalies(): boolean {
    if (this.functionStack.length > 10) { // 嵌套层级过深
      this.addWarningStep({
        line: 0,
        column: 0,
        increment: 0,
        ruleId: 'nesting-depth-warning',
        description: '嵌套层级过深'
      }, `嵌套层级达到 ${this.functionStack.length}，可能存在异常`);
      return true;
    }
    return false;
  }

  /**
   * 恢复错误状态
   * 清理异常状态并尝试继续执行
   */
  public recoverFromError(): void {
    // 如果函数栈过深，清理到合理层级
    if (this.functionStack.length > 10) {
      const excessFunctions = this.functionStack.splice(10);
      console.warn(`Recovered from deep nesting, removed ${excessFunctions.length} functions from stack`);
    }

    // 检查是否有无效的函数上下文
    this.functionStack = this.functionStack.filter(context => 
      context.name && context.line >= 0 && context.column >= 0
    );
  }

  /**
   * 结束当前最内层函数的跟踪并返回函数详细信息
   * @returns 函数详细信息
   */
  public endFunction(): FunctionDetail {
    if (this.functionStack.length === 0) {
      throw new Error('No function is currently being tracked. Please call startFunction() first.');
    }

    const completedFunction = this.functionStack.pop()!;

    // 创建函数详情（深拷贝步骤以避免对象池回收影响）
    const internalFunction = completedFunction as InternalFunctionContext;
    const functionDetail: FunctionDetail = {
      name: internalFunction.name,
      line: internalFunction.line,
      column: internalFunction.column,
      complexity: internalFunction.complexity,
      details: this.copySteps(internalFunction.steps)
    };
    
    // 释放详细步骤对象回池中
    this.releaseSteps(internalFunction.steps);
    
    // 释放函数上下文对象回池中
    this.functionContextPool.release(completedFunction);

    return functionDetail;
  }

  /**
   * 重置收集器状态
   * 清除所有跟踪的函数和步骤信息
   */
  public reset(): void {
    // 释放所有未完成的函数上下文和步骤
    for (const functionContext of this.functionStack) {
      const internalContext = functionContext as InternalFunctionContext;
      this.releaseSteps(internalContext.steps);
      this.functionContextPool.release(functionContext);
    }
    
    this.functionStack = [];
    this.stepsCollected = 0;
  }

  /**
   * 检查是否正在跟踪函数
   * @returns 是否正在跟踪函数
   */
  public isTracking(): boolean {
    return this.functionStack.length > 0;
  }

  /**
   * 获取当前最内层函数名称（如果正在跟踪）
   * @returns 当前函数名称或null
   */
  public getCurrentFunctionName(): string | null {
    if (this.functionStack.length === 0) {
      return null;
    }
    const currentFunction = this.functionStack[this.functionStack.length - 1] as InternalFunctionContext;
    return currentFunction?.name || null;
  }

  /**
   * 获取当前最内层函数累计复杂度（如果正在跟踪）
   * @returns 当前累计复杂度或0
   */
  public getCurrentComplexity(): number {
    if (this.functionStack.length === 0) {
      return 0;
    }
    const currentFunction = this.functionStack[this.functionStack.length - 1] as InternalFunctionContext;
    return currentFunction?.complexity || 0;
  }

  /**
   * 获取当前嵌套层级
   * @returns 当前嵌套层级，顶层为0
   */
  public getCurrentNestingLevel(): number {
    return this.functionStack.length > 0 ? this.functionStack.length - 1 : 0;
  }

  /**
   * 获取函数调用栈信息（用于调试）
   * @returns 函数调用栈的名称数组
   */
  public getFunctionStack(): string[] {
    return this.functionStack.map(context => {
      const internalContext = context as InternalFunctionContext;
      return internalContext.name;
    });
  }

  /**
   * 获取指定层级的函数上下文
   * @param level 嵌套层级，0为最外层
   * @returns 函数上下文或null
   */
  public getFunctionAtLevel(level: number): { name: string; complexity: number } | null {
    if (level < 0 || level >= this.functionStack.length) {
      return null;
    }
    const context = this.functionStack[level];
    if (!isNotNullish(context)) {
      return null;
    }
    const internalContext = context as InternalFunctionContext;
    return {
      name: internalContext?.name || 'unknown',
      complexity: internalContext?.complexity || 0
    };
  }

  /**
   * 验证步骤数据的公共方法（供测试使用）
   * @param step 要验证的步骤数据
   * @returns 验证结果
   */
  public validateStep(step: Partial<DetailStep>): { isValid: boolean; errors: string[] } {
    return this.validateStepData(step);
  }

  /**
   * 注册规则元数据
   * @param ruleMetadata 规则元数据
   */
  public registerRule(ruleMetadata: RuleMetadata): void {
    this.ruleRegistry.set(ruleMetadata.ruleId, ruleMetadata);
  }

  /**
   * 获取规则描述
   * @param ruleId 规则标识符
   * @returns 规则描述，如果不存在则返回默认描述
   */
  public getRuleDescription(ruleId: string): string {
    const rule = this.ruleRegistry.get(ruleId);
    if (!rule) {
      // 记录未知规则的使用
      console.warn(`Unknown rule encountered: ${ruleId}`);
      return `未知规则: ${ruleId}`;
    }
    return rule.description;
  }

  /**
   * 检查规则是否已注册
   * @param ruleId 规则标识符
   * @returns 是否已注册
   */
  public hasRule(ruleId: string): boolean {
    return this.ruleRegistry.has(ruleId);
  }

  /**
   * 获取所有已注册的规则ID
   * @returns 规则ID数组
   */
  public getRegisteredRuleIds(): string[] {
    return Array.from(this.ruleRegistry.keys());
  }

  /**
   * 批量注册规则
   * @param rules 规则元数据数组
   */
  public registerRules(rules: RuleMetadata[]): void {
    for (const rule of rules) {
      this.registerRule(rule);
    }
  }

  /**
   * 深拷贝步骤数组，避免对象池回收影响
   */
  private copySteps(steps: DetailStep[]): DetailStep[] {
    return steps.map(step => ({
      line: step.line,
      column: step.column,
      increment: step.increment,
      cumulative: step.cumulative,
      ruleId: step.ruleId,
      description: step.description,
      nestingLevel: step.nestingLevel,
      context: step.context,
      diagnosticMarker: step.diagnosticMarker,
      diagnosticMessage: step.diagnosticMessage
    }));
  }

  /**
   * 释放步骤数组中的所有对象回池中
   */
  private releaseSteps(steps: ReadonlyDetailStep[]): void {
    for (const step of steps) {
      this.detailStepPool.release(step);
    }
    // 类型安全地清空数组
    const mutableSteps = steps as InternalDetailStep[];
    mutableSteps.length = 0;
  }

  /**
   * 获取性能统计信息
   */
  public getPerformanceStats() {
    return {
      stepsCollected: this.stepsCollected,
      memoryOptimized: this.memoryOptimized,
      activeFunctions: this.functionStack.length,
      objectPoolStats: {
        detailStepPool: this.detailStepPool.getStats(),
        functionContextPool: this.functionContextPool.getStats()
      }
    };
  }

  /**
   * 启用或禁用内存优化
   */
  public setMemoryOptimization(enabled: boolean): void {
    this.memoryOptimized = enabled;
    if (!enabled) {
      // 禁用内存优化时清空对象池，让对象被垃圾回收
      this.detailStepPool.clear();
      this.functionContextPool.clear();
    }
  }

  // 错误处理相关方法

  /**
   * 验证步骤数据的增强版本，抛出错误而不是返回结果
   */
  private validateStepInternal(step: Omit<DetailStep, 'cumulative' | 'nestingLevel'>): void {
    const validation = this.validateStepData(step);
    if (!validation.isValid) {
      throw new Error(`Step validation failed: ${validation.errors.join(', ')}`);
    }
  }

  /**
   * 验证步骤数据（保留原有的返回结果版本）
   */
  private validateStepData(step: Partial<DetailStep>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (typeof step.line !== 'number' || step.line < 0) {
      errors.push('Invalid line number');
    }

    if (typeof step.column !== 'number' || step.column < 0) {
      errors.push('Invalid column number');
    }

    if (typeof step.increment !== 'number') {
      errors.push('Invalid increment value');
    }

    if (!step.ruleId || typeof step.ruleId !== 'string') {
      errors.push('Invalid rule ID');
    }

    if (!step.description || typeof step.description !== 'string') {
      errors.push('Invalid description');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 处理错误
   */
  private handleError(error: DetailCollectionError): void {
    this.errors.push(error);
    this.recoveryAttempts++;
    
    // 输出错误信息
    this.errorFormatter.outputError(error, 'DetailCollector');
    
    // 尝试恢复（如果可能）
    this.attemptRecovery(error);
  }

  /**
   * 尝试错误恢复
   */
  private attemptRecovery(error: DetailCollectionError): void {
    try {
      // 根据错误类型尝试不同的恢复策略
      if (error.message.includes('No function is currently being tracked')) {
        this.warnings.push('尝试在未跟踪函数时添加步骤，已跳过');
        return;
      }

      if (error.message.includes('validation failed')) {
        this.warnings.push('步骤验证失败，已跳过该步骤');
        return;
      }

      // 其他类型的错误，记录警告但继续执行
      this.warnings.push(`未知错误类型，已跳过: ${error.message}`);
      
    } catch (recoveryError) {
      // 恢复失败，记录额外的错误
      this.errors.push(new DetailCollectionError(
        'error recovery',
        recoveryError as Error
      ));
    }
  }

  /**
   * 获取错误信息
   */
  public getErrors(): DetailCollectionError[] {
    return [...this.errors];
  }

  /**
   * 获取警告信息
   */
  public getWarnings(): string[] {
    return [...this.warnings];
  }

  /**
   * 清除错误和警告
   */
  public clearErrorsAndWarnings(): void {
    this.errors = [];
    this.warnings = [];
    this.recoveryAttempts = 0;
  }

  /**
   * 获取错误统计信息
   */
  public getErrorStats(): {
    errorCount: number;
    warningCount: number;
    recoveryAttempts: number;
    errorsByType: Record<string, number>;
  } {
    const errorsByType: Record<string, number> = {};
    
    for (const error of this.errors) {
      const code = error.code || 'UNKNOWN';
      errorsByType[code] = (errorsByType[code] || 0) + 1;
    }

    return {
      errorCount: this.errors.length,
      warningCount: this.warnings.length,
      recoveryAttempts: this.recoveryAttempts,
      errorsByType
    };
  }

  /**
   * 输出所有错误和警告的摘要
   */
  public outputErrorSummary(): void {
    if (this.errors.length === 0 && this.warnings.length === 0) {
      return;
    }

    const stats = this.getErrorStats();
    
    this.errorFormatter.outputErrorStats({
      totalErrors: stats.errorCount,
      errorsByType: stats.errorsByType,
      recoveryAttempts: stats.recoveryAttempts
    });

    // 输出警告
    if (this.warnings.length > 0) {
      console.warn(`\n发现 ${this.warnings.length} 个警告:`);
      this.warnings.forEach((warning, index) => {
        console.warn(`${index + 1}. ${warning}`);
      });
      console.warn();
    }
  }
}