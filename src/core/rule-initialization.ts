/**
 * 规则初始化系统
 * 统一管理基于类的规则注册和发现
 */

import type { AsyncRuleEngine, ResolvedEngineConfig } from '../engine/types';
import { CompleteAsyncRuleEngineImpl } from '../engine/complete-async-engine';
import { LogicalOperatorRule, RecursionComplexityRule } from '../rules/logical-operator-rule';

// 预导入未来的核心规则类（当它们被创建时取消注释）
import { IfStatementRule } from '../rules/if-statement-rule';
import { ForStatementRule } from '../rules/for-statement-rule';
import { WhileStatementRule } from '../rules/while-statement-rule';
import { ConditionalExpressionRule } from '../rules/conditional-expression-rule';
import { CatchClauseRule } from '../rules/catch-clause-rule';
import { RecursiveCallRule } from '../rules/recursive-call-rule';

/**
 * 规则初始化管理器
 * 负责创建和配置异步规则引擎，注册所有基于类的规则
 * 已重构为实例化模式，去除单例依赖
 */
export class RuleInitializationManager {
  private asyncRuleEngine: AsyncRuleEngine | null = null;
  private quiet: boolean = false;

  constructor(quiet: boolean = false) {
    this.quiet = quiet;
  }

  /**
   * 设置静默模式
   */
  setQuiet(quiet: boolean): void {
    this.quiet = quiet;
  }

  /**
   * 初始化并配置异步规则引擎
   * 注册所有基于类的规则
   * @param ruleEngineConfig 可选的规则引擎配置
   */
  async initializeAsyncRuleEngine(ruleEngineConfig?: {
    maxRuleConcurrency?: number;
    enableRuleCaching?: boolean;
    ruleDebugMode?: boolean;
  }): Promise<AsyncRuleEngine> {
    if (this.asyncRuleEngine) {
      return this.asyncRuleEngine;
    }

    // 合并默认配置和传入的配置
    const config: Partial<ResolvedEngineConfig> = {
      performance: {
        maxConcurrency: ruleEngineConfig?.maxRuleConcurrency ?? 10,
        enableParallelExecution: true,
        cacheSize: 1000,
        streamingThreshold: 100
      },
      output: {
        includeMetrics: true,
        detailLevel: ruleEngineConfig?.ruleDebugMode ? 'verbose' : 'standard',
        enableDebugInfo: ruleEngineConfig?.ruleDebugMode ?? false
      }
    };

    // 创建异步规则引擎实例
    this.asyncRuleEngine = new CompleteAsyncRuleEngineImpl(config);

    // 注册基于类的规则
    await this.registerAllRules();

    return this.asyncRuleEngine;
  }

  /**
   * 注册所有基于类的规则
   */
  private async registerAllRules(): Promise<void> {
    if (!this.asyncRuleEngine) {
      throw new Error('AsyncRuleEngine not initialized');
    }

    try {
      // 注册逻辑运算符规则
      const logicalOperatorRule = new LogicalOperatorRule();
      await this.asyncRuleEngine.registerRule(logicalOperatorRule, this.quiet);

      // 注册递归复杂度规则
      const recursionRule = new RecursionComplexityRule();
      await this.asyncRuleEngine.registerRule(recursionRule, this.quiet);

      // 注册核心控制流规则
      await this.registerCoreRules();

      if (!this.quiet) {
        console.log('All class-based rules registered successfully');
      }
    } catch (error) {
      console.error('Error registering class-based rules:', error);
      throw error;
    }
  }

  /**
   * 注册核心控制流规则类
   * 为未来的规则实现做准备
   * @private
   */
  private async registerCoreRules(): Promise<void> {
    if (!this.asyncRuleEngine) {
      throw new Error('AsyncRuleEngine not initialized');
    }

    // Task 4.1: IfStatementRule (已实现)
    const ifStatementRule = new IfStatementRule();
    await this.asyncRuleEngine.registerRule(ifStatementRule, this.quiet);
    
    // Task 4.2: ForStatementRule (已实现)
    const forStatementRule = new ForStatementRule();
    await this.asyncRuleEngine.registerRule(forStatementRule, this.quiet);
    
    // Task 4.3: WhileStatementRule (已实现)
    const whileStatementRule = new WhileStatementRule();
    await this.asyncRuleEngine.registerRule(whileStatementRule, this.quiet);
    
    // Task 4.5: ConditionalExpressionRule (已实现)
    const conditionalExpressionRule = new ConditionalExpressionRule();
    await this.asyncRuleEngine.registerRule(conditionalExpressionRule, this.quiet);
    
    // Task 4.6: CatchClauseRule (已实现)
    const catchClauseRule = new CatchClauseRule();
    await this.asyncRuleEngine.registerRule(catchClauseRule, this.quiet);
    
    // Task 4.7: RecursiveCallRule (已实现)
    const recursiveCallRule = new RecursiveCallRule();
    await this.asyncRuleEngine.registerRule(recursiveCallRule, this.quiet);

    if (!this.quiet) {
      console.log('Core complexity rules registered successfully');
    }
  }

  /**
   * 获取已初始化的异步规则引擎
   */
  getAsyncRuleEngine(): AsyncRuleEngine | null {
    return this.asyncRuleEngine;
  }

  /**
   * 重置规则引擎（主要用于测试）
   */
  async reset(): Promise<void> {
    if (this.asyncRuleEngine) {
      // 清理资源
      await this.asyncRuleEngine.dispose?.();
      this.asyncRuleEngine = null;
    }
  }

  /**
   * 清理资源
   */
  async dispose(): Promise<void> {
    await this.reset();
  }
}

// 默认管理器实例，用于向后兼容
let defaultManager: RuleInitializationManager | null = null;

/**
 * 创建规则初始化管理器
 * 推荐使用此函数而不是全局单例
 */
export function createRuleManager(quiet: boolean = false): RuleInitializationManager {
  return new RuleInitializationManager(quiet);
}

/**
 * 全局函数：初始化规则系统
 * 提供便捷的初始化接口（向后兼容）
 * @deprecated 推荐使用 createRuleManager 创建独立实例
 */
export async function initializeRuleSystem(quiet: boolean = false, ruleEngineConfig?: {
  maxRuleConcurrency?: number;
  enableRuleCaching?: boolean;
  ruleDebugMode?: boolean;
}): Promise<AsyncRuleEngine> {
  if (!defaultManager) {
    defaultManager = new RuleInitializationManager(quiet);
  }
  return await defaultManager.initializeAsyncRuleEngine(ruleEngineConfig);
}

/**
 * 全局函数：获取规则引擎
 * 如果未初始化，会自动初始化（向后兼容）
 * @deprecated 推荐使用 createRuleManager 创建独立实例
 */
export async function getRuleEngine(quiet: boolean = false, ruleEngineConfig?: {
  maxRuleConcurrency?: number;
  enableRuleCaching?: boolean;
  ruleDebugMode?: boolean;
}): Promise<AsyncRuleEngine> {
  if (!defaultManager) {
    defaultManager = new RuleInitializationManager(quiet);
  }
  
  let engine = defaultManager.getAsyncRuleEngine();
  if (!engine) {
    engine = await defaultManager.initializeAsyncRuleEngine(ruleEngineConfig);
  }
  
  return engine;
}

/**
 * 清理默认管理器实例
 * 用于测试和资源清理
 */
export async function resetDefaultManager(): Promise<void> {
  if (defaultManager) {
    await defaultManager.dispose();
    defaultManager = null;
  }
}