import type { Node } from '@swc/core';

/**
 * BaseVisitor 基础类
 * 
 * 提供访问者模式的基础实现，管理父节点栈。
 * 基于访问者模式重构架构，所有 AST 遍历都通过此基类或其子类实现。
 * 
 * 核心功能：
 * - 维护父节点栈，支持获取当前节点的父节点
 * - 提供标准的 visit 方法，自动管理节点访问过程
 * - 支持子类重写特定节点类型的访问方法
 * 
 * 设计原则：
 * - 遵循访问者模式，分离 AST 结构和操作逻辑
 * - 通过父节点栈提供上下文信息，支持结构化的 span 修正
 * - 抽象类设计，强制子类实现具体的访问逻辑
 */
export abstract class BaseVisitor {
  /**
   * 父节点栈
   * 
   * 在遍历 AST 时维护当前访问路径上的所有父节点。
   * 栈顶元素是当前节点的直接父节点。
   */
  protected parentStack: Node[] = [];

  /**
   * 获取当前节点的父节点
   * 
   * @returns 父节点，如果当前在根节点则返回 undefined
   */
  protected getParent(): Node | undefined {
    return this.parentStack.length > 1 
      ? this.parentStack[this.parentStack.length - 2] 
      : undefined;
  }

  /**
   * 获取当前节点的祖先节点（父节点的父节点）
   * 
   * @returns 祖先节点，如果层级不足则返回 undefined
   */
  protected getGrandParent(): Node | undefined {
    return this.parentStack.length >= 3
      ? this.parentStack[this.parentStack.length - 3]
      : undefined;
  }

  /**
   * 获取完整的父节点路径（从根到当前父节点）
   * 
   * @returns 父节点路径数组的副本（不包含当前节点）
   */
  protected getParentPath(): readonly Node[] {
    return this.parentStack.length > 0 
      ? [...this.parentStack.slice(0, -1)]
      : [];
  }

  /**
   * 检查当前节点是否具有指定类型的祖先
   * 
   * @param nodeType 要检查的节点类型
   * @returns 如果找到指定类型的祖先节点则返回 true
   */
  protected hasAncestorOfType(nodeType: string): boolean {
    // 排除当前节点，只检查祖先
    return this.parentStack.slice(0, -1).some(parent => parent.type === nodeType);
  }

  /**
   * 查找最近的指定类型的祖先节点
   * 
   * @param nodeType 要查找的节点类型
   * @returns 最近的指定类型祖先节点，如果没有找到则返回 undefined
   */
  protected findNearestAncestorOfType(nodeType: string): Node | undefined {
    // 从栈顶向下查找，但排除当前节点
    for (let i = this.parentStack.length - 2; i >= 0; i--) {
      const node = this.parentStack[i];
      if (node && node.type === nodeType) {
        return node;
      }
    }
    return undefined;
  }

  /**
   * 访问节点的核心方法
   * 
   * 自动管理父节点栈的推入和弹出，确保在访问任何节点时都能正确维护上下文。
   * 子类可以重写此方法来添加自定义的访问逻辑，但应该调用 super.visit() 来维护栈。
   * 
   * @param node 要访问的节点
   * @returns 访问后的节点（通常是原始节点，除非子类进行了转换）
   */
  public visit<T extends Node>(node: T): T {
    // 推入当前节点作为子节点的父节点
    this.parentStack.push(node);

    try {
      // 调用特定节点类型的访问方法
      const result = this.visitNode(node);
      
      // 访问子节点
      this.visitChildren(node);
      
      return (result || node) as T;
    } finally {
      // 确保在任何情况下都会弹出栈
      this.parentStack.pop();
    }
  }

  /**
   * 访问特定节点的抽象方法
   * 
   * 子类必须实现此方法来定义对每个节点的具体处理逻辑。
   * 此方法在 visit() 中被调用，此时父节点栈已经正确维护。
   * 
   * @param node 要访问的节点
   * @returns 处理后的节点
   */
  protected abstract visitNode(node: Node): Node;

  /**
   * 访问节点的所有子节点
   * 
   * 使用递归方式遍历节点的所有属性，对发现的子节点调用 visit() 方法。
   * 此方法处理 SWC AST 的通用结构，自动识别各种节点属性中的子节点。
   * 
   * @param node 父节点
   */
  protected visitChildren(node: any): void {
    if (!node || typeof node !== 'object') {
      return;
    }

    // 遍历节点的所有属性
    for (const key in node) {
      if (!node.hasOwnProperty(key)) {
        continue;
      }

      const value = node[key];

      // 处理单个子节点
      if (this.isNode(value)) {
        this.visit(value);
      }
      // 处理子节点数组
      else if (Array.isArray(value)) {
        for (const item of value) {
          if (this.isNode(item)) {
            this.visit(item);
          }
        }
      }
      // 递归处理嵌套对象（但避免处理已知的非节点属性）
      else if (this.shouldVisitProperty(key, value)) {
        this.visitChildren(value);
      }
    }
  }

  /**
   * 检查对象是否为 SWC AST 节点
   * 
   * SWC 节点必须具有 'type' 属性和 'span' 属性。
   * 
   * @param obj 要检查的对象
   * @returns 如果是节点则返回 true
   */
  private isNode(obj: any): obj is Node {
    return obj && 
           typeof obj === 'object' && 
           typeof obj.type === 'string' &&
           obj.span &&
           typeof obj.span === 'object';
  }

  /**
   * 检查是否应该访问指定属性
   * 
   * 排除一些已知的非节点属性，避免无谓的递归。
   * 
   * @param key 属性名
   * @param value 属性值
   * @returns 如果应该访问则返回 true
   */
  private shouldVisitProperty(key: string, value: any): boolean {
    // 排除已知的非节点属性
    const excludedProperties = new Set([
      'span', 'type', 'ctxt', 'value', 'raw', 'optional',
      'start', 'end', 'loc', 'range'
    ]);

    return !excludedProperties.has(key) && 
           value && 
           typeof value === 'object';
  }

  /**
   * 重置访问器状态
   * 
   * 清空父节点栈，用于访问器复用或错误恢复。
   */
  protected reset(): void {
    this.parentStack = [];
  }

  /**
   * 获取当前访问深度
   * 
   * @returns 当前在 AST 中的嵌套深度（不计入当前节点）
   */
  protected getDepth(): number {
    return this.parentStack.length > 0 ? this.parentStack.length - 1 : 0;
  }
}