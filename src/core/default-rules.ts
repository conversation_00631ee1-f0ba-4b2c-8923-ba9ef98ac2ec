import { RuleRegistry } from './rule-registry';
import { RuleCategory, type RuleRegistrationConfig } from './types';

/**
 * 默认规则注册配置
 * 包含所有现有复杂度规则的标准化定义
 */
const DEFAULT_RULES_CONFIG: RuleRegistrationConfig = {
  version: '1.0.0',
  description: 'Default cognitive complexity rules for TypeScript/JavaScript analysis',
  overwrite: false,
  rules: [
    // 控制流规则 - 按照Task 4中规划的优先级顺序
    {
      ruleId: 'if-statement',
      description: 'If statements increase complexity by 1 + nesting penalty',
      category: RuleCategory.CONTROL_FLOW,
      defaultIncrement: 1,
      enabled: true,
      priority: 400, // 最高优先级（Task 4.1）
    },
    {
      ruleId: 'for-statement',
      description: 'For loops increase complexity by 1 + nesting penalty',
      category: RuleCategory.CONTROL_FLOW,
      defaultIncrement: 1,
      enabled: true,
      priority: 450, // 高优先级（Task 4.2）
    },
    {
      ruleId: 'for-in-statement',
      description: 'For-in loops increase complexity by 1 + nesting penalty',
      category: RuleCategory.CONTROL_FLOW,
      defaultIncrement: 1,
      enabled: true,
      priority: 450, // 高优先级（Task 4.2）
    },
    {
      ruleId: 'for-of-statement',
      description: 'For-of loops increase complexity by 1 + nesting penalty',
      category: RuleCategory.CONTROL_FLOW,
      defaultIncrement: 1,
      enabled: true,
      priority: 450, // 高优先级（Task 4.2）
    },
    {
      ruleId: 'while-statement',
      description: 'While loops increase complexity by 1 + nesting penalty',
      category: RuleCategory.CONTROL_FLOW,
      defaultIncrement: 1,
      enabled: true,
      priority: 500, // 高优先级（Task 4.3）
    },
    {
      ruleId: 'do-while-statement',
      description: 'Do-while loops increase complexity by 1 + nesting penalty',
      category: RuleCategory.CONTROL_FLOW,
      defaultIncrement: 1,
      enabled: true,
      priority: 500, // 高优先级（Task 4.3）
    },
    {
      ruleId: 'switch-statement',
      description: 'Switch statements increase complexity by 1 + nesting penalty',
      category: RuleCategory.CONTROL_FLOW,
      defaultIncrement: 1,
      enabled: true,
      priority: 550, // 中等优先级
    },
    {
      ruleId: 'conditional-expression',
      description: 'Ternary operators (condition ? a : b) increase complexity by 1 + nesting penalty',
      category: RuleCategory.CONTROL_FLOW,
      defaultIncrement: 1,
      enabled: true,
      priority: 600, // 中等优先级（Task 4.5）
    },

    // 异常处理规则
    {
      ruleId: 'catch-clause',
      description: 'Catch clauses increase complexity by 1 + nesting penalty',
      category: RuleCategory.EXCEPTION_HANDLING,
      defaultIncrement: 1,
      enabled: true,
      priority: 650, // 中等优先级（Task 4.6）
    },

    // 逻辑操作符规则 - 与现有LogicalOperatorRule优先级对齐
    {
      ruleId: 'logical-and',
      description: 'Logical AND (&&) operators increase complexity by 1',
      category: RuleCategory.LOGICAL_OPERATOR,
      defaultIncrement: 1,
      enabled: true,
      priority: 600, // 与LogicalOperatorRule.priority对齐
    },
    {
      ruleId: 'logical-or',
      description: 'Logical OR (||) operators increase complexity by 1',
      category: RuleCategory.LOGICAL_OPERATOR,
      defaultIncrement: 1,
      enabled: true,
      priority: 600, // 与LogicalOperatorRule.priority对齐
    },
    {
      ruleId: 'logical-operator-mixing',
      description: 'Mixed logical operators (&&, ||) in same expression add penalty',
      category: RuleCategory.LOGICAL_OPERATOR,
      defaultIncrement: 1,
      enabled: true,
      priority: 650, // 比基础逻辑运算符稍低
    },

    // 递归规则 - 与现有RecursionComplexityRule优先级对齐
    {
      ruleId: 'recursive-call',
      description: 'Recursive function calls increase complexity',
      category: RuleCategory.RECURSION,
      defaultIncrement: 1,
      enabled: true,
      priority: 700, // 与RecursionComplexityRule.priority对齐（Task 4.7）
    },

    // JSX特定规则
    {
      ruleId: 'jsx-conditional-rendering',
      description: 'JSX conditional rendering patterns increase complexity',
      category: RuleCategory.JSX,
      defaultIncrement: 1,
      enabled: true,
      priority: 500,
    },
    {
      ruleId: 'jsx-event-handler',
      description: 'Complex JSX event handlers increase complexity',
      category: RuleCategory.JSX,
      defaultIncrement: 1,
      enabled: true,
      priority: 500,
    },
    {
      ruleId: 'jsx-hook-complexity',
      description: 'React hooks with complex logic increase complexity',
      category: RuleCategory.JSX,
      defaultIncrement: 1,
      enabled: true,
      priority: 500,
    },
    {
      ruleId: 'jsx-smart-conditional',
      description: 'Smart conditional rendering in JSX components',
      category: RuleCategory.JSX,
      defaultIncrement: 1,
      enabled: true,
      priority: 500,
    },

    // 嵌套规则
    {
      ruleId: 'nesting-penalty',
      description: 'Additional complexity for deeply nested structures',
      category: RuleCategory.NESTING,
      defaultIncrement: 1,
      enabled: true,
      priority: 600,
    },

    // 特殊情况和豁免相关
    {
      ruleId: 'default-value-assignment',
      description: 'Default value assignments using || operator (exempt from complexity)',
      category: RuleCategory.OTHER,
      defaultIncrement: 0,
      enabled: true,
      priority: 900,
    },
    {
      ruleId: 'ignore-exemption',
      description: 'Lines with cognitive-complexity ignore comments',
      category: RuleCategory.OTHER,
      defaultIncrement: 0,
      enabled: true,
      priority: 1000,
    },

    // 错误和未知情况
    {
      ruleId: 'unknown-rule',
      description: '未知规则或无法识别的复杂度增量',
      category: RuleCategory.OTHER,
      defaultIncrement: 1,
      enabled: true,
      priority: 0,
    },
    {
      ruleId: 'nesting-depth-warning',
      description: '嵌套层级过深警告',
      category: RuleCategory.NESTING,
      defaultIncrement: 0,
      enabled: true,
      priority: 0,
    },
  ],
};

/**
 * 初始化规则注册表
 * 注册所有默认的复杂度规则
 */
export function initializeRules(quiet: boolean = false): void {
  try {
    RuleRegistry.registerBatch(DEFAULT_RULES_CONFIG);
    if (!quiet) {
      console.log(`Successfully registered ${DEFAULT_RULES_CONFIG.rules.length} complexity rules`);
    }
  } catch (error) {
    console.error('Failed to initialize rules:', error);
    throw error;
  }
}

/**
 * 获取默认规则配置
 * @returns 默认规则配置对象
 */
export function getDefaultRulesConfig(): RuleRegistrationConfig {
  return { ...DEFAULT_RULES_CONFIG };
}

/**
 * 获取特定分类的规则配置
 * @param category 规则分类
 * @returns 指定分类的规则配置
 */
export function getRulesByCategory(category: RuleCategory): RuleRegistrationConfig {
  const filteredRules = DEFAULT_RULES_CONFIG.rules.filter((rule) => rule.category === category);

  return {
    ...DEFAULT_RULES_CONFIG,
    description: `${category} rules configuration`,
    rules: filteredRules,
  };
}

/**
 * 验证规则ID是否为已知规则
 * @param ruleId 规则标识符
 * @returns 是否为已知规则
 */
export function isKnownRule(ruleId: string): boolean {
  return DEFAULT_RULES_CONFIG.rules.some((rule) => rule.ruleId === ruleId);
}

/**
 * 获取规则的默认增量
 * @param ruleId 规则标识符
 * @returns 默认增量，如果规则不存在返回1
 */
export function getDefaultIncrement(ruleId: string): number {
  const rule = DEFAULT_RULES_CONFIG.rules.find((rule) => rule.ruleId === ruleId);
  return rule ? rule.defaultIncrement : 1;
}

/**
 * 根据AST节点类型获取对应的规则ID
 * @param nodeType AST节点类型
 * @returns 对应的规则ID
 */
export function getNodeTypeRuleId(nodeType: string): string {
  const nodeTypeMap: Record<string, string> = {
    IfStatement: 'if-statement',
    ForStatement: 'for-statement',
    ForInStatement: 'for-in-statement',
    ForOfStatement: 'for-of-statement',
    WhileStatement: 'while-statement',
    DoWhileStatement: 'do-while-statement',
    SwitchStatement: 'switch-statement',
    ConditionalExpression: 'conditional-expression',
    CatchClause: 'catch-clause',
    CallExpression: 'recursive-call', // 递归调用情况
    LogicalExpression: 'logical-and', // 默认，需要根据具体操作符调整
    BinaryExpression: 'logical-and', // 默认，需要根据具体操作符调整
  };

  return nodeTypeMap[nodeType] || 'unknown-rule';
}

/**
 * 根据逻辑操作符获取规则ID
 * @param operator 逻辑操作符 ('&&' | '||')
 * @returns 对应的规则ID
 */
export function getLogicalOperatorRuleId(operator: string): string {
  switch (operator) {
    case '&&':
      return 'logical-and';
    case '||':
      return 'logical-or';
    default:
      return 'unknown-rule';
  }
}

// 自动初始化规则（模块加载时）
// 注释掉，让使用者手动调用，避免副作用
// initializeRules();
