// 核心类型定义
export interface CalculationOptions {
  enableMixedLogicOperatorPenalty?: boolean;
  recursionChainThreshold?: number;
  enableDebugLog?: boolean; // 调试日志开关
  enableDetails?: boolean; // 详细日志模式开关
  quiet?: boolean; // 静默模式开关
}

export interface FunctionResult {
  name: string;
  complexity: number;
  line: number;
  column: number;
  filePath: string;
  severity?: 'Critical' | 'Warning' | 'Info';
  ignoreExemptions?: IgnoreExemption[];
  details?: DetailStep[]; // 详细计算步骤（仅在详细模式下填充）
}

export interface IgnoreExemption {
  line: number;
  type: 'ignore-next-line';
  complexityReduced: number;
}

export interface FileResult {
  filePath: string;
  complexity: number;
  functions: FunctionResult[];
  averageComplexity: number;
}

export interface AnalysisResult {
  summary: {
    totalComplexity: number;
    averageComplexity: number;
    filesAnalyzed: number;
    functionsAnalyzed: number;
    highComplexityFunctions: number;
  };
  results: FileResult[];
  baseline?: import('../baseline/types').BaselineData;
}

// 详细日志输出相关类型定义

/**
 * 规则分类枚举
 * 定义复杂度规则的不同类别
 */
export enum RuleCategory {
  /** 控制流规则 (if, switch, loops) */
  CONTROL_FLOW = 'control-flow',
  /** 逻辑操作符规则 (&&, ||) */
  LOGICAL_OPERATOR = 'logical-operator',
  /** 函数调用规则 */
  FUNCTION_CALL = 'function-call',
  /** 异常处理规则 (try-catch) */
  EXCEPTION_HANDLING = 'exception-handling',
  /** 递归规则 */
  RECURSION = 'recursion',
  /** 嵌套规则 */
  NESTING = 'nesting',
  /** JSX特定规则 */
  JSX = 'jsx',
  /** 其他规则 */
  OTHER = 'other'
}

/**
 * 规则注册配置类型
 * 用于批量注册规则时的配置
 */
export interface RuleRegistrationConfig {
  /** 规则列表 */
  rules: RuleMetadata[];
  /** 配置版本 */
  version: string;
  /** 配置描述 */
  description?: string;
  /** 是否覆盖已存在的规则 */
  overwrite?: boolean;
}

/**
 * 诊断标记类型
 * 用于标识计算过程中的异常情况
 */
export enum DiagnosticMarker {
  /** 正常情况，无标记 */
  NONE = '',
  /** 警告标记 - 异常计算行为 */
  WARNING = 'warning',
  /** 问号标记 - 未知规则或增量 */
  UNKNOWN = 'unknown',
  /** 错误标记 - 规则计算错误 */
  ERROR = 'error'
}

/**
 * 详细计算步骤数据结构
 * 表示单个复杂度增量的详细信息
 */
export interface DetailStep {
  /** 代码行号 */
  line: number;
  /** 代码列号 */
  column: number;
  /** 本步骤复杂度增量 */
  increment: number;
  /** 累计复杂度 */
  cumulative: number;
  /** 规则标识符 (kebab-case 格式) */
  ruleId: string;
  /** 人类可读的规则描述 */
  description: string;
  /** 嵌套层级 */
  nestingLevel: number;
  /** 可选的上下文信息 */
  context?: string;
  /** 诊断标记 */
  diagnosticMarker?: DiagnosticMarker;
  /** 诊断消息 */
  diagnosticMessage?: string;
  /** SWC span信息 (用于代码框架生成) */
  span?: { start: number; end: number };
  /** 是否应显示代码上下文 */
  shouldShowContext?: boolean;
  /** 上下文优先级排序 */
  contextRank?: number;
}

/**
 * 位置信息
 * 用于表示代码中的行列位置
 */
export interface Position {
  /** 行号 (从1开始) */
  line: number;
  /** 列号 (从0开始) */
  column: number;
}

/**
 * 代码框架生成选项
 * 用于配置@babel/code-frame的显示行为
 */
export interface CodeFrameOptions {
  /** 是否启用语法高亮 */
  highlightCode?: boolean;
  /** 显示目标行上方的行数 */
  linesAbove?: number;
  /** 显示目标行下方的行数 */
  linesBelow?: number;
  /** 是否强制使用颜色输出 */
  forceColor?: boolean;
}

/**
 * 代码框架生成结果
 * 包含生成的代码框架和相关元信息
 */
export interface CodeFrameResult {
  /** 生成的代码框架字符串 */
  frame: string;
  /** 是否生成成功 */
  success: boolean;
  /** 错误信息 (如果生成失败) */
  error?: string;
  /** 是否使用了缓存结果 */
  cached: boolean;
}

/**
 * 智能过滤选项
 * 用于控制上下文显示的过滤策略
 */
export interface FilterOptions {
  /** 最小复杂度增量阈值 */
  minComplexityIncrement: number;
  /** 最大上下文项目数量 */
  maxContextItems: number;
  /** 是否强制显示所有上下文 */
  forceShowAll: boolean;
}

/**
 * 详细格式化选项
 * 用于控制详细输出的格式化行为
 */
export interface DetailFormattingOptions {
  /** 是否显示代码上下文 */
  showContext: boolean;
  /** 代码框架生成选项 */
  contextOptions: CodeFrameOptions;
  /** 智能过滤选项 */
  filterOptions: FilterOptions;
}

/**
 * 函数详细信息
 * 包含函数级别的计算详情
 */
export interface FunctionDetail {
  /** 函数名称 */
  name: string;
  /** 函数起始行号 */
  line: number;
  /** 函数起始列号 */
  column: number;
  /** 最终复杂度 */
  complexity: number;
  /** 详细计算步骤 */
  details: DetailStep[];
}

/**
 * 规则元数据
 * 用于规则注册表的数据结构
 */
export interface RuleMetadata {
  /** 唯一规则标识符 (kebab-case) */
  ruleId: string;
  /** 规则描述 */
  description: string;
  /** 规则分类 */
  category: RuleCategory;
  /** 默认复杂度增量 */
  defaultIncrement: number;
  /** 是否启用 (可选，默认为true) */
  enabled?: boolean;
  /** 规则优先级 (可选，用于排序) */
  priority?: number;
}