/**
 * 插件间通信系统
 * 支持安全的插件间数据交换和事件传递
 */

import { EventEmitter } from 'events';
import type {
  PluginMessage,
  PluginMessageHandler,
  Plugin,
  LoadedPlugin,
} from './types';

// 消息类型定义
export interface MessageBus {
  // 消息发送
  sendMessage(message: PluginMessage): Promise<PluginMessage | void>;
  broadcastMessage(message: Omit<PluginMessage, 'target'>): Promise<void>;
  
  // 消息订阅
  subscribe(pluginId: string, handler: PluginMessageHandler): void;
  unsubscribe(pluginId: string, handler: PluginMessageHandler): void;
  
  // 请求-响应模式
  request(message: PluginMessage, timeout?: number): Promise<PluginMessage>;
  
  // 事件系统
  on(event: string, handler: PluginMessageHandler): void;
  off(event: string, handler: PluginMessageHandler): void;
  emit(event: string, data: any): void;
  
  // 通信统计
  getStatistics(): CommunicationStatistics;
  
  // 安全控制
  setPermissions(pluginId: string, permissions: CommunicationPermissions): void;
  getPermissions(pluginId: string): CommunicationPermissions;
}

// 通信权限
export interface CommunicationPermissions {
  canSendTo: string[]; // 允许发送消息给这些插件
  canReceiveFrom: string[]; // 允许接收这些插件的消息
  canBroadcast: boolean; // 是否允许广播消息
  canSubscribeToEvents: string[]; // 允许订阅的事件类型
  maxMessagesPerSecond: number; // 每秒最大消息数
  maxMessageSize: number; // 消息最大字节数
}

// 通信统计
export interface CommunicationStatistics {
  totalMessages: number;
  messagesByPlugin: Record<string, number>;
  messagesByType: Record<string, number>;
  errorCount: number;
  averageLatency: number;
  throughput: number;
}

// 消息队列项
interface QueuedMessage {
  message: PluginMessage;
  timestamp: number;
  retries: number;
  resolve: (value: PluginMessage | void) => void;
  reject: (reason: any) => void;
}

// 速率限制器
class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  
  isAllowed(pluginId: string, maxRequests: number, windowMs: number = 1000): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!this.requests.has(pluginId)) {
      this.requests.set(pluginId, []);
    }
    
    const pluginRequests = this.requests.get(pluginId)!;
    
    // 清理过期的请求记录
    const validRequests = pluginRequests.filter(time => time > windowStart);
    this.requests.set(pluginId, validRequests);
    
    // 检查是否超过限制
    if (validRequests.length >= maxRequests) {
      return false;
    }
    
    // 记录新请求
    validRequests.push(now);
    return true;
  }
  
  reset(pluginId?: string): void {
    if (pluginId) {
      this.requests.delete(pluginId);
    } else {
      this.requests.clear();
    }
  }
}

/**
 * 插件消息总线实现
 */
export class PluginMessageBus extends EventEmitter implements MessageBus {
  private subscribers = new Map<string, Set<PluginMessageHandler>>();
  private permissions = new Map<string, CommunicationPermissions>();
  private pendingRequests = new Map<string, QueuedMessage>();
  private messageQueue: QueuedMessage[] = [];
  private rateLimiter = new RateLimiter();
  private statistics: CommunicationStatistics = {
    totalMessages: 0,
    messagesByPlugin: {},
    messagesByType: {},
    errorCount: 0,
    averageLatency: 0,
    throughput: 0,
  };
  
  private processingQueue = false;
  private loadedPlugins: Map<string, LoadedPlugin>;
  
  constructor(loadedPlugins: Map<string, LoadedPlugin>) {
    super();
    this.loadedPlugins = loadedPlugins;
    this.startQueueProcessor();
    this.startStatisticsCollector();
  }

  async sendMessage(message: PluginMessage): Promise<PluginMessage | void> {
    // 验证消息格式
    this.validateMessage(message);
    
    // 检查权限
    if (!this.checkSendPermission(message.source, message.target)) {
      throw new Error(`Plugin '${message.source}' is not allowed to send messages to '${message.target}'`);
    }
    
    // 检查速率限制
    const permissions = this.getPermissions(message.source);
    if (!this.rateLimiter.isAllowed(message.source, permissions.maxMessagesPerSecond)) {
      throw new Error(`Plugin '${message.source}' has exceeded rate limit`);
    }
    
    // 检查消息大小
    const messageSize = JSON.stringify(message.payload).length;
    if (messageSize > permissions.maxMessageSize) {
      throw new Error(`Message size (${messageSize}) exceeds limit (${permissions.maxMessageSize})`);
    }
    
    return new Promise((resolve, reject) => {
      const queuedMessage: QueuedMessage = {
        message,
        timestamp: Date.now(),
        retries: 0,
        resolve,
        reject,
      };
      
      this.messageQueue.push(queuedMessage);
      this.processQueue();
    });
  }

  async broadcastMessage(message: Omit<PluginMessage, 'target'>): Promise<void> {
    const sourcePermissions = this.getPermissions(message.source);
    if (!sourcePermissions.canBroadcast) {
      throw new Error(`Plugin '${message.source}' is not allowed to broadcast messages`);
    }
    
    const broadcastMessage: PluginMessage = {
      ...message,
      target: '*', // 广播标记
    };
    
    // 发送给所有有权限接收的插件
    const promises: Promise<void>[] = [];
    
    for (const [pluginId] of this.loadedPlugins) {
      if (pluginId !== message.source && this.checkReceivePermission(pluginId, message.source)) {
        const targetMessage: PluginMessage = {
          ...broadcastMessage,
          target: pluginId,
        };
        
        promises.push(
          this.sendMessage(targetMessage).then(() => {}).catch(error => {
            console.warn(`Failed to broadcast message to '${pluginId}':`, error);
          })
        );
      }
    }
    
    await Promise.allSettled(promises);
  }

  subscribe(pluginId: string, handler: PluginMessageHandler): void {
    if (!this.subscribers.has(pluginId)) {
      this.subscribers.set(pluginId, new Set());
    }
    
    this.subscribers.get(pluginId)!.add(handler);
  }

  unsubscribe(pluginId: string, handler: PluginMessageHandler): void {
    const handlers = this.subscribers.get(pluginId);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.subscribers.delete(pluginId);
      }
    }
  }

  async request(message: PluginMessage, timeout: number = 5000): Promise<PluginMessage> {
    // 生成请求ID
    const requestId = `req-${Date.now()}-${Math.random().toString(36)}`;
    const requestMessage: PluginMessage = {
      ...message,
      id: requestId,
      type: 'request',
    };
    
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeoutId = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error(`Request timeout after ${timeout}ms`));
      }, timeout);
      
      // 存储待处理请求
      this.pendingRequests.set(requestId, {
        message: requestMessage,
        timestamp: Date.now(),
        retries: 0,
        resolve: (response: PluginMessage | void) => {
          clearTimeout(timeoutId);
          if (response) {
            resolve(response);
          } else {
            reject(new Error('No response received'));
          }
        },
        reject: (error: any) => {
          clearTimeout(timeoutId);
          reject(error);
        },
      });
      
      // 发送请求
      this.sendMessage(requestMessage).catch(error => {
        clearTimeout(timeoutId);
        this.pendingRequests.delete(requestId);
        reject(error);
      });
    });
  }

  setPermissions(pluginId: string, permissions: CommunicationPermissions): void {
    this.permissions.set(pluginId, { ...permissions });
  }

  getPermissions(pluginId: string): CommunicationPermissions {
    return this.permissions.get(pluginId) || {
      canSendTo: [],
      canReceiveFrom: [],
      canBroadcast: false,
      canSubscribeToEvents: [],
      maxMessagesPerSecond: 10,
      maxMessageSize: 1024 * 1024, // 1MB
    };
  }

  getStatistics(): CommunicationStatistics {
    return { ...this.statistics };
  }

  // 私有方法

  private validateMessage(message: PluginMessage): void {
    if (!message.id || typeof message.id !== 'string') {
      throw new Error('Message must have a valid id');
    }
    
    if (!message.source || typeof message.source !== 'string') {
      throw new Error('Message must have a valid source');
    }
    
    if (!message.target || typeof message.target !== 'string') {
      throw new Error('Message must have a valid target');
    }
    
    if (!message.type || !['request', 'response', 'event'].includes(message.type)) {
      throw new Error('Message must have a valid type');
    }
    
    if (typeof message.timestamp !== 'number') {
      throw new Error('Message must have a valid timestamp');
    }
  }

  private checkSendPermission(source: string, target: string): boolean {
    if (target === '*') {
      // 广播权限检查
      return this.getPermissions(source).canBroadcast;
    }
    
    const permissions = this.getPermissions(source);
    return permissions.canSendTo.includes(target) || permissions.canSendTo.includes('*');
  }

  private checkReceivePermission(target: string, source: string): boolean {
    const permissions = this.getPermissions(target);
    return permissions.canReceiveFrom.includes(source) || permissions.canReceiveFrom.includes('*');
  }

  private async processQueue(): Promise<void> {
    if (this.processingQueue || this.messageQueue.length === 0) {
      return;
    }
    
    this.processingQueue = true;
    
    while (this.messageQueue.length > 0) {
      const queuedMessage = this.messageQueue.shift()!;
      
      try {
        await this.deliverMessage(queuedMessage);
        this.updateStatistics(queuedMessage.message, true);
      } catch (error) {
        this.handleDeliveryError(queuedMessage, error as Error);
      }
    }
    
    this.processingQueue = false;
  }

  private async deliverMessage(queuedMessage: QueuedMessage): Promise<void> {
    const { message } = queuedMessage;
    const handlers = this.subscribers.get(message.target);
    
    if (!handlers || handlers.size === 0) {
      if (message.type === 'request') {
        throw new Error(`No handlers found for target '${message.target}'`);
      }
      return; // 对于事件，没有处理器不是错误
    }
    
    const startTime = Date.now();
    const responses: (PluginMessage | void)[] = [];
    
    // 并行执行所有处理器
    const promises = Array.from(handlers).map(async handler => {
      try {
        return await handler(message);
      } catch (error) {
        console.warn(`Message handler error for plugin '${message.target}':`, error);
        throw error;
      }
    });
    
    const results = await Promise.allSettled(promises);
    
    // 收集成功的响应
    for (const result of results) {
      if (result.status === 'fulfilled' && result.value) {
        responses.push(result.value);
      }
    }
    
    // 处理请求-响应
    if (message.type === 'request') {
      const response = responses.find(r => r !== undefined);
      if (response) {
        // 发送响应回原请求者
        const responseMessage: PluginMessage = {
          id: `resp-${message.id}`,
          type: 'response',
          source: message.target,
          target: message.source,
          payload: response,
          timestamp: Date.now(),
        };
        
        queuedMessage.resolve(responseMessage);
      } else {
        throw new Error(`No valid response for request '${message.id}'`);
      }
    } else {
      queuedMessage.resolve();
    }
    
    // 更新延迟统计
    const latency = Date.now() - startTime;
    this.updateLatencyStatistics(latency);
  }

  private handleDeliveryError(queuedMessage: QueuedMessage, error: Error): void {
    queuedMessage.retries++;
    
    // 重试逻辑
    if (queuedMessage.retries < 3) {
      // 指数退避重试
      const delay = Math.pow(2, queuedMessage.retries) * 1000;
      setTimeout(() => {
        this.messageQueue.unshift(queuedMessage);
        this.processQueue();
      }, delay);
    } else {
      // 重试次数用完，拒绝Promise
      queuedMessage.reject(error);
      this.updateStatistics(queuedMessage.message, false);
    }
  }

  private updateStatistics(message: PluginMessage, success: boolean): void {
    this.statistics.totalMessages++;
    
    // 按插件统计
    if (!this.statistics.messagesByPlugin[message.source]) {
      this.statistics.messagesByPlugin[message.source] = 0;
    }
    this.statistics.messagesByPlugin[message.source]++;
    
    // 按类型统计
    if (!this.statistics.messagesByType[message.type]) {
      this.statistics.messagesByType[message.type] = 0;
    }
    this.statistics.messagesByType[message.type]++;
    
    // 错误统计
    if (!success) {
      this.statistics.errorCount++;
    }
  }

  private updateLatencyStatistics(latency: number): void {
    // 简化的移动平均
    if (this.statistics.averageLatency === 0) {
      this.statistics.averageLatency = latency;
    } else {
      this.statistics.averageLatency = (this.statistics.averageLatency * 0.9) + (latency * 0.1);
    }
  }

  private startQueueProcessor(): void {
    // 定期处理队列（防止队列阻塞）
    setInterval(() => {
      if (!this.processingQueue && this.messageQueue.length > 0) {
        this.processQueue().catch(error => {
          console.error('Queue processing error:', error);
        });
      }
    }, 100);
  }

  private startStatisticsCollector(): void {
    let lastMessageCount = 0;
    
    setInterval(() => {
      // 计算吞吐量（每秒消息数）
      const currentMessageCount = this.statistics.totalMessages;
      this.statistics.throughput = currentMessageCount - lastMessageCount;
      lastMessageCount = currentMessageCount;
      
      // 清理旧的速率限制记录
      this.rateLimiter.reset();
    }, 1000);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 清理所有订阅
    this.subscribers.clear();
    
    // 拒绝所有待处理的请求
    for (const [requestId, queuedMessage] of this.pendingRequests) {
      queuedMessage.reject(new Error('Message bus is being cleaned up'));
    }
    this.pendingRequests.clear();
    
    // 清理消息队列
    for (const queuedMessage of this.messageQueue) {
      queuedMessage.reject(new Error('Message bus is being cleaned up'));
    }
    this.messageQueue.length = 0;
    
    // 移除所有事件监听器
    this.removeAllListeners();
  }
}

/**
 * 创建默认的通信权限
 */
export function createDefaultCommunicationPermissions(): CommunicationPermissions {
  return {
    canSendTo: [], // 默认不允许向任何插件发送消息
    canReceiveFrom: [], // 默认不允许接收任何插件的消息
    canBroadcast: false, // 默认不允许广播
    canSubscribeToEvents: [], // 默认不允许订阅事件
    maxMessagesPerSecond: 10, // 每秒最多10条消息
    maxMessageSize: 1024 * 1024, // 最大1MB
  };
}

/**
 * 创建宽松的通信权限（用于开发环境）
 */
export function createPermissiveCommunicationPermissions(): CommunicationPermissions {
  return {
    canSendTo: ['*'], // 允许向所有插件发送消息
    canReceiveFrom: ['*'], // 允许接收所有插件的消息
    canBroadcast: true, // 允许广播
    canSubscribeToEvents: ['*'], // 允许订阅所有事件
    maxMessagesPerSecond: 100, // 每秒最多100条消息
    maxMessageSize: 10 * 1024 * 1024, // 最大10MB
  };
}