/**
 * 插件系统主入口
 * 导出所有插件系统相关的类型、组件和工具
 */

// 核心类型
export * from './types';

// 插件管理器
export { PluginManagerImpl } from './manager';

// 插件沙箱
export { PluginSandboxImpl } from './sandbox';

// 安全插件沙箱（类型安全增强版本）
export { 
  SecurePluginSandbox,
  TypeSafeSecurityProxy,
  createSecurePluginSandbox,
  type TypeSafeSandboxOptions,
  type SecurityProxy,
  type SecurityAuditEntry,
} from './secure-sandbox';

// 插件验证器
export { PluginValidator } from './validator';

// 依赖解析器
export { DependencyResolver } from './dependency-resolver';

// 插件通信系统
export { 
  PluginMessageBus,
  createDefaultCommunicationPermissions,
  createPermissiveCommunicationPermissions,
} from './communication';

// 热更新系统
export { 
  HotReloadManager,
  createHotReloadManager,
} from './hot-reload';

// 错误恢复和降级机制
export {
  ErrorRecoveryManager,
  createErrorRecoveryManager,
} from './error-recovery';

// 版本管理器
export {
  VersionManager,
  createVersionManager,
} from './version-manager';

// 开发工具套件
export { 
  PluginDevToolsImpl,
  createPluginDevTools,
} from './dev-tools';

// 便利创建函数
import type { AsyncRuleEngine } from '../engine/types';
import type { 
  PluginManager, 
  HotReloadManager, 
  HotReloadConfig, 
  PluginDevTools,
  ErrorRecoveryManager,
  ErrorPattern,
  CircuitBreakerConfig,
} from './types';
import { PluginManagerImpl } from './manager';
import { createHotReloadManager } from './hot-reload';
import { createPluginDevTools } from './dev-tools';
import { createErrorRecoveryManager } from './error-recovery';

/**
 * 创建完整的插件系统
 */
export function createPluginSystem(
  engine: AsyncRuleEngine,
  options: {
    hotReloadConfig?: Partial<HotReloadConfig>;
    errorPatterns?: ErrorPattern[];
    circuitBreakerConfig?: CircuitBreakerConfig;
  } = {}
): {
  pluginManager: PluginManager;
  hotReloadManager: HotReloadManager;
  errorRecoveryManager: ErrorRecoveryManager;
  devTools: PluginDevTools;
} {
  const pluginManager = new PluginManagerImpl(engine);
  const hotReloadManager = createHotReloadManager(pluginManager, options.hotReloadConfig);
  const errorRecoveryManager = createErrorRecoveryManager(
    pluginManager, 
    options.errorPatterns,
    options.circuitBreakerConfig
  );
  const devTools = createPluginDevTools(pluginManager, hotReloadManager);

  return {
    pluginManager,
    hotReloadManager,
    errorRecoveryManager,
    devTools,
  };
}

/**
 * 默认插件系统配置
 */
export const DEFAULT_PLUGIN_CONFIG = {
  hotReload: {
    enabled: true,
    watchFiles: true,
    watchDependencies: true,
    debounceMs: 100,
    maxRetries: 3,
    retryDelayMs: 1000,
    preserveState: true,
    gracefulShutdown: true,
    batchReloads: true,
    batchTimeoutMs: 500,
  },
} as const;

/**
 * 开发环境插件系统配置
 */
export const DEVELOPMENT_PLUGIN_CONFIG = {
  hotReload: {
    enabled: true,
    watchFiles: true,
    watchDependencies: true,
    debounceMs: 50, // 更快的响应
    maxRetries: 5,
    retryDelayMs: 500,
    preserveState: true,
    gracefulShutdown: false, // 开发时快速失败
    batchReloads: false, // 开发时立即重载
    batchTimeoutMs: 100,
  },
} as const;

/**
 * 生产环境插件系统配置
 */
export const PRODUCTION_PLUGIN_CONFIG = {
  hotReload: {
    enabled: false, // 生产环境通常禁用热更新
    watchFiles: false,
    watchDependencies: false,
    debounceMs: 1000,
    maxRetries: 1,
    retryDelayMs: 5000,
    preserveState: false,
    gracefulShutdown: true,
    batchReloads: true,
    batchTimeoutMs: 2000,
  },
} as const;