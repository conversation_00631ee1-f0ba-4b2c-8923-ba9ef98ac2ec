/**
 * 类型安全版本管理器
 * 基于现有的 PluginVersionManager，增强类型安全性和错误处理
 */

import type {
  Plugin,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  CompatibilityInfo,
} from './types';

/**
 * 验证后的版本组件接口
 */
export interface ValidatedVersionParts {
  readonly major: number;
  readonly minor: number;
  readonly patch: number;
  readonly prerelease: string | null;
  readonly build: string | null;
  readonly raw: string;
}

/**
 * 版本比较结果类型
 */
export type VersionComparisonResult = -1 | 0 | 1;

/**
 * 版本范围类型
 */
export type VersionRange = string; // 如: "^1.0.0", "~2.1.0", ">=1.2.0"

/**
 * 版本比较详细结果
 */
export interface DetailedVersionComparison {
  result: VersionComparisonResult;
  compatible: boolean;
  reason: string;
  recommendation?: string;
}

/**
 * 类型安全的插件升级信息
 */
export interface TypeSafePluginUpgradeInfo {
  readonly pluginId: string;
  readonly currentVersion: ValidatedVersionParts;
  readonly availableVersion: ValidatedVersionParts;
  readonly upgradeType: 'major' | 'minor' | 'patch';
  readonly breakingChanges: boolean;
  readonly changelog: string;
  readonly dependencies: {
    readonly added: readonly string[];
    readonly removed: readonly string[];
    readonly updated: readonly string[];
  };
}

/**
 * 兼容性检查结果
 */
export interface TypeSafeCompatibilityCheckResult {
  readonly isCompatible: boolean;
  readonly issues: readonly TypeSafeCompatibilityIssue[];
  readonly recommendations: readonly string[];
  readonly migrationRequired: boolean;
}

/**
 * 类型安全的兼容性问题
 */
export interface TypeSafeCompatibilityIssue {
  readonly type: 'version' | 'api' | 'dependency' | 'configuration' | 'engine';
  readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly description: string;
  readonly affectedComponents: readonly string[];
  readonly solution?: string;
}

/**
 * 版本解析错误
 */
export class VersionParsingError extends Error {
  constructor(
    public readonly version: string,
    public readonly reason: string,
    public readonly context?: Record<string, unknown>
  ) {
    super(`Failed to parse version '${version}': ${reason}`);
    this.name = 'VersionParsingError';
  }
}

/**
 * 版本比较错误
 */
export class VersionComparisonError extends Error {
  constructor(
    public readonly version1: string,
    public readonly version2: string,
    public readonly reason: string
  ) {
    super(`Failed to compare versions '${version1}' and '${version2}': ${reason}`);
    this.name = 'VersionComparisonError';
  }
}

/**
 * 版本范围验证错误
 */
export class VersionRangeError extends Error {
  constructor(
    public readonly range: string,
    public readonly version: string,
    public readonly reason: string
  ) {
    super(`Version '${version}' does not satisfy range '${range}': ${reason}`);
    this.name = 'VersionRangeError';
  }
}

/**
 * 类型安全版本管理器
 */
export class TypeSafeVersionManager {
  private static readonly VERSION_REGEX = 
    /^(\d+)\.(\d+)\.(\d+)(?:-([^+]+))?(?:\+(.+))?$/;

  private readonly ENGINE_VERSION = '1.0.0';
  private readonly SUPPORTED_NODE_VERSIONS = ['18.0.0', '20.0.0', '21.0.0'] as const;
  private readonly MAX_VERSION_NUMBER = 100000;

  /**
   * 解析版本字符串并验证格式
   */
  parseVersion(version: string): ValidatedVersionParts {
    if (typeof version !== 'string' || version.trim() === '') {
      throw new VersionParsingError(version, 'Version must be a non-empty string');
    }

    const trimmedVersion = version.trim();
    const match = trimmedVersion.match(TypeSafeVersionManager.VERSION_REGEX);
    
    if (!match) {
      throw new VersionParsingError(
        version, 
        'Invalid version format. Expected format: major.minor.patch[-prerelease][+build]'
      );
    }

    const [, majorStr, minorStr, patchStr, prerelease, build] = match;
    
    const major = this.parseVersionNumber(majorStr, 'major', version);
    const minor = this.parseVersionNumber(minorStr, 'minor', version);
    const patch = this.parseVersionNumber(patchStr, 'patch', version);

    return {
      major,
      minor,
      patch,
      prerelease: prerelease || null,
      build: build || null,
      raw: trimmedVersion,
    };
  }

  /**
   * 比较两个版本
   */
  compareVersions(v1: string, v2: string): VersionComparisonResult {
    try {
      const version1 = this.parseVersion(v1);
      const version2 = this.parseVersion(v2);

      // 比较主版本号
      const majorDiff = version1.major - version2.major;
      if (majorDiff !== 0) {
        return majorDiff > 0 ? 1 : -1;
      }

      // 比较次版本号
      const minorDiff = version1.minor - version2.minor;
      if (minorDiff !== 0) {
        return minorDiff > 0 ? 1 : -1;
      }

      // 比较修订版本号
      const patchDiff = version1.patch - version2.patch;
      if (patchDiff !== 0) {
        return patchDiff > 0 ? 1 : -1;
      }

      // 比较预发布版本
      return this.comparePrereleaseVersions(
        version1.prerelease, 
        version2.prerelease
      );
    } catch (error: unknown) {
      throw new VersionComparisonError(
        v1, 
        v2, 
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  /**
   * 详细版本比较，包含兼容性信息
   */
  compareVersionsDetailed(v1: string, v2: string): DetailedVersionComparison {
    try {
      const result = this.compareVersions(v1, v2);
      const version1 = this.parseVersion(v1);
      const version2 = this.parseVersion(v2);

      let compatible = true;
      let reason = 'Versions are compatible';
      let recommendation: string | undefined;

      if (result !== 0) {
        if (version1.major !== version2.major) {
          compatible = false;
          reason = 'Major version difference may include breaking changes';
          recommendation = 'Consider upgrading incrementally through minor versions';
        } else if (version1.minor !== version2.minor) {
          reason = 'Minor version difference, should be backward compatible';
        } else {
          reason = 'Patch version difference, fully compatible';
        }
      }

      return {
        result,
        compatible,
        reason,
        recommendation,
      };
    } catch (error: unknown) {
      throw new VersionComparisonError(
        v1,
        v2,
        error instanceof Error ? error.message : 'Unknown error during detailed comparison'
      );
    }
  }

  /**
   * 检查版本是否满足范围要求
   */
  satisfiesRange(version: string, range: VersionRange): boolean {
    if (typeof range !== 'string' || range.trim() === '') {
      throw new VersionRangeError(range, version, 'Version range must be a non-empty string');
    }

    const trimmedRange = range.trim();
    
    try {
      return this.evaluateVersionRange(version, trimmedRange);
    } catch (error: unknown) {
      throw new VersionRangeError(
        range,
        version,
        error instanceof Error ? error.message : 'Unknown error'
      );
    }
  }

  /**
   * 验证版本字符串格式
   */
  validateVersionFormat(version: string): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    if (typeof version !== 'string') {
      errors.push({
        message: 'Version must be a string',
        severity: 'error',
        code: 'INVALID_VERSION_TYPE',
      });
      return { isValid: false, errors, warnings };
    }

    if (version.trim() === '') {
      errors.push({
        message: 'Version cannot be empty',
        severity: 'error',
        code: 'EMPTY_VERSION',
      });
      return { isValid: false, errors, warnings };
    }

    try {
      const parsed = this.parseVersion(version);
      
      // 检查版本号的合理性
      if (parsed.major < 0 || parsed.minor < 0 || parsed.patch < 0) {
        errors.push({
          message: 'Version numbers must be non-negative',
          severity: 'error',
          code: 'NEGATIVE_VERSION_NUMBER',
        });
      }

      if (parsed.major > this.MAX_VERSION_NUMBER || 
          parsed.minor > this.MAX_VERSION_NUMBER || 
          parsed.patch > this.MAX_VERSION_NUMBER) {
        errors.push({
          message: `Version numbers exceed maximum allowed value (${this.MAX_VERSION_NUMBER})`,
          severity: 'error',
          code: 'VERSION_TOO_LARGE',
        });
      }

      // 检查预发布版本格式
      if (parsed.prerelease !== null) {
        if (!/^[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/.test(parsed.prerelease)) {
          errors.push({
            message: 'Prerelease version contains invalid characters. Only alphanumeric characters, hyphens, and dots are allowed',
            severity: 'error',
            code: 'INVALID_PRERELEASE_FORMAT',
          });
        }

        if (parsed.prerelease.length === 0) {
          errors.push({
            message: 'Prerelease version cannot be empty',
            severity: 'error',
            code: 'EMPTY_PRERELEASE',
          });
        }
      }

      // 检查构建元数据格式
      if (parsed.build !== null) {
        if (!/^[a-zA-Z0-9.-]+$/.test(parsed.build)) {
          errors.push({
            message: 'Build metadata contains invalid characters',
            severity: 'error',
            code: 'INVALID_BUILD_FORMAT',
          });
        }
      }

    } catch (error: unknown) {
      errors.push({
        message: error instanceof VersionParsingError 
          ? error.reason 
          : 'Unknown parsing error',
        severity: 'error',
        code: 'PARSING_ERROR',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 安全地获取升级路径
   */
  getSafeUpgradePath(
    currentVersion: string, 
    targetVersion: string, 
    availableVersions: readonly string[]
  ): TypeSafeVersionUpgradePath {
    const validatedCurrent = this.parseVersion(currentVersion);
    const validatedTarget = this.parseVersion(targetVersion);
    
    if (this.compareVersions(currentVersion, targetVersion) >= 0) {
      return {
        currentVersion: validatedCurrent,
        targetVersion: validatedTarget,
        steps: [],
        totalSteps: 0,
        estimatedDuration: 0,
        isDowngrade: this.compareVersions(currentVersion, targetVersion) > 0,
      };
    }

    const validVersions = availableVersions
      .filter((v): v is string => {
        try {
          return this.compareVersions(v, currentVersion) > 0 && 
                 this.compareVersions(v, targetVersion) <= 0;
        } catch {
          return false;
        }
      })
      .sort((a, b) => this.compareVersions(a, b));

    const path: TypeSafeVersionStep[] = [];
    let current = currentVersion;

    for (const version of validVersions) {
      try {
        const currentParsed = this.parseVersion(current);
        const versionParsed = this.parseVersion(version);

        const stepType = this.determineUpgradeType(currentParsed, versionParsed);
        const risks = this.assessUpgradeRisks(current, version);

        path.push({
          from: currentParsed,
          to: versionParsed,
          type: stepType,
          recommended: stepType !== 'major',
          risks,
        });

        current = version;
      } catch (error: unknown) {
        // Skip invalid versions
        continue;
      }
    }

    return {
      currentVersion: validatedCurrent,
      targetVersion: validatedTarget,
      steps: path,
      totalSteps: path.length,
      estimatedDuration: this.estimateUpgradeDuration(path),
      isDowngrade: false,
    };
  }

  // 私有方法

  private parseVersionNumber(
    versionStr: string | undefined, 
    component: string,
    originalVersion: string
  ): number {
    if (!versionStr) {
      throw new VersionParsingError(
        originalVersion,
        `Missing ${component} version component`
      );
    }

    const parsed = parseInt(versionStr, 10);
    
    if (isNaN(parsed) || parsed < 0) {
      throw new VersionParsingError(
        originalVersion,
        `Invalid ${component} version: '${versionStr}'. Must be a non-negative integer`
      );
    }

    if (parsed > this.MAX_VERSION_NUMBER) {
      throw new VersionParsingError(
        originalVersion,
        `${component} version number too large: ${parsed}. Maximum allowed: ${this.MAX_VERSION_NUMBER}`
      );
    }

    return parsed;
  }

  private comparePrereleaseVersions(
    pre1: string | null, 
    pre2: string | null
  ): VersionComparisonResult {
    if (pre1 === null && pre2 === null) return 0;
    if (pre1 === null && pre2 !== null) return 1;  // 正式版本 > 预发布版本
    if (pre1 !== null && pre2 === null) return -1; // 预发布版本 < 正式版本
    
    // 两个都是预发布版本
    const comparison = pre1!.localeCompare(pre2!);
    return comparison > 0 ? 1 : comparison < 0 ? -1 : 0;
  }

  private evaluateVersionRange(version: string, range: string): boolean {
    // 实现各种版本范围解析逻辑
    if (range === '*') return true;
    
    if (range.startsWith('^')) {
      return this.satisfiesCaretRange(version, range.substring(1));
    }
    
    if (range.startsWith('~')) {
      return this.satisfiesTildeRange(version, range.substring(1));
    }

    if (range.startsWith('>=')) {
      return this.compareVersions(version, range.substring(2).trim()) >= 0;
    }

    if (range.startsWith('<=')) {
      return this.compareVersions(version, range.substring(2).trim()) <= 0;
    }

    if (range.startsWith('>')) {
      return this.compareVersions(version, range.substring(1).trim()) > 0;
    }

    if (range.startsWith('<')) {
      return this.compareVersions(version, range.substring(1).trim()) < 0;
    }

    if (range.includes(' - ')) {
      const parts = range.split(' - ');
      if (parts.length !== 2) {
        throw new Error('Invalid range format for hyphen range');
      }
      const minTrimmed = parts[0]?.trim();
      const maxTrimmed = parts[1]?.trim();
      if (!minTrimmed || !maxTrimmed) {
        throw new Error('Invalid range bounds');
      }
      return this.compareVersions(version, minTrimmed) >= 0 && 
             this.compareVersions(version, maxTrimmed) <= 0;
    }

    if (range.includes(' || ')) {
      return range.split(' || ')
        .some(r => {
          const trimmed = r.trim();
          return trimmed !== '' && this.satisfiesRange(version, trimmed);
        });
    }
    
    // 精确匹配
    return this.compareVersions(version, range) === 0;
  }

  private satisfiesCaretRange(version: string, baseVersion: string): boolean {
    const v = this.parseVersion(version);
    const base = this.parseVersion(baseVersion);

    return v.major === base.major && 
           this.compareVersions(version, baseVersion) >= 0;
  }

  private satisfiesTildeRange(version: string, baseVersion: string): boolean {
    const v = this.parseVersion(version);
    const base = this.parseVersion(baseVersion);

    return v.major === base.major && 
           v.minor === base.minor && 
           this.compareVersions(version, baseVersion) >= 0;
  }

  private determineUpgradeType(
    current: ValidatedVersionParts, 
    target: ValidatedVersionParts
  ): 'major' | 'minor' | 'patch' {
    if (target.major > current.major) {
      return 'major';
    } else if (target.minor > current.minor) {
      return 'minor';
    } else {
      return 'patch';
    }
  }

  private assessUpgradeRisks(fromVersion: string, toVersion: string): TypeSafeUpgradeRisk[] {
    const risks: TypeSafeUpgradeRisk[] = [];
    
    try {
      const from = this.parseVersion(fromVersion);
      const to = this.parseVersion(toVersion);
      
      if (to.major > from.major) {
        risks.push({
          type: 'breaking-changes',
          severity: 'high',
          description: 'Major version upgrade may include breaking changes',
          mitigation: 'Review changelog and test thoroughly before upgrading',
        });
      }

      if (to.prerelease !== null && from.prerelease === null) {
        risks.push({
          type: 'stability',
          severity: 'medium',
          description: 'Upgrading to prerelease version',
          mitigation: 'Consider using stable release for production',
        });
      }
    } catch {
      risks.push({
        type: 'validation',
        severity: 'critical',
        description: 'Unable to parse version for risk assessment',
        mitigation: 'Validate version format before proceeding',
      });
    }
    
    return risks;
  }

  private estimateUpgradeDuration(steps: readonly TypeSafeVersionStep[]): number {
    // 估算升级所需时间（分钟）
    let duration = 0;
    
    for (const step of steps) {
      switch (step.type) {
        case 'patch':
          duration += 5;
          break;
        case 'minor':
          duration += 15;
          break;
        case 'major':
          duration += 60;
          break;
      }

      // 高风险升级需要额外时间
      const highRiskCount = step.risks.filter(r => r.severity === 'high' || r.severity === 'critical').length;
      duration += highRiskCount * 30;
    }
    
    return duration;
  }
}

// 辅助接口和类型

interface TypeSafeVersionStep {
  readonly from: ValidatedVersionParts;
  readonly to: ValidatedVersionParts;
  readonly type: 'major' | 'minor' | 'patch';
  readonly recommended: boolean;
  readonly risks: readonly TypeSafeUpgradeRisk[];
}

interface TypeSafeVersionUpgradePath {
  readonly currentVersion: ValidatedVersionParts;
  readonly targetVersion: ValidatedVersionParts;
  readonly steps: readonly TypeSafeVersionStep[];
  readonly totalSteps: number;
  readonly estimatedDuration: number; // 分钟
  readonly isDowngrade: boolean;
}

interface TypeSafeUpgradeRisk {
  readonly type: 'breaking-changes' | 'dependency-conflicts' | 'configuration-changes' | 'stability' | 'validation';
  readonly severity: 'low' | 'medium' | 'high' | 'critical';
  readonly description: string;
  readonly mitigation: string;
}