/**
 * 插件版本管理和兼容性检查系统
 * 处理插件版本控制、升级、降级和兼容性验证
 * 
 * 增强版本：集成了类型安全的版本管理器
 */

import type {
  Plugin,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  CompatibilityInfo,
} from './types';

import { 
  TypeSafeVersionManager,
  type ValidatedVersionParts,
  type VersionComparisonResult as SafeVersionComparisonResult,
  VersionParsingError,
  VersionComparisonError,
  VersionRangeError,
  type TypeSafePluginUpgradeInfo,
  type TypeSafeCompatibilityCheckResult,
  type TypeSafeCompatibilityIssue,
} from './type-safe-version-manager';

/**
 * 版本范围类型
 */
export type VersionRange = string; // 如: "^1.0.0", "~2.1.0", ">=1.2.0"

/**
 * 版本比较结果
 */
export interface VersionComparisonResult {
  compatible: boolean;
  reason: string;
  recommendation?: string;
}

/**
 * 插件升级信息
 */
export interface PluginUpgradeInfo {
  pluginId: string;
  currentVersion: string;
  availableVersion: string;
  upgradeType: 'major' | 'minor' | 'patch';
  breakingChanges: boolean;
  changelog?: string;
  dependencies: {
    added: string[];
    removed: string[];
    updated: string[];
  };
}

/**
 * 兼容性检查结果
 */
export interface CompatibilityCheckResult {
  isCompatible: boolean;
  issues: CompatibilityIssue[];
  recommendations: string[];
  migrationRequired: boolean;
}

/**
 * 兼容性问题
 */
export interface CompatibilityIssue {
  type: 'version' | 'api' | 'dependency' | 'configuration' | 'engine';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  affectedComponents: string[];
  solution?: string;
}

/**
 * 增强的插件版本管理器
 * 使用组合模式集成类型安全的版本解析和比较
 */
export class PluginVersionManager {
  private readonly typeSafeVersionManager: TypeSafeVersionManager;
  private readonly PLUGIN_ENGINE_VERSION = '1.0.0';
  private readonly PLUGIN_SUPPORTED_NODE_VERSIONS = ['18.0.0', '20.0.0', '21.0.0'] as const;

  constructor() {
    this.typeSafeVersionManager = new TypeSafeVersionManager();
  }

  /**
   * 解析版本字符串 - 使用类型安全实现
   */
  parseVersion(version: string): ValidatedVersionParts {
    return this.typeSafeVersionManager.parseVersion(version);
  }

  /**
   * 解析版本字符串 - 使用类型安全实现（向后兼容）
   * @deprecated 使用 parseVersion 替代，返回 ValidatedVersionParts
   */
  parseVersionLegacy(version: string): VersionParts {
    try {
      const validated = this.typeSafeVersionManager.parseVersion(version);
      return {
        major: validated.major,
        minor: validated.minor,
        patch: validated.patch,
        prerelease: validated.prerelease,
        build: validated.build,
        raw: validated.raw,
      };
    } catch (error: unknown) {
      if (error instanceof VersionParsingError) {
        throw new Error(`Invalid version format: ${version}`);
      }
      throw error;
    }
  }

  /**
   * 比较两个版本 - 使用类型安全实现
   */
  compareVersions(v1: string, v2: string): SafeVersionComparisonResult {
    return this.typeSafeVersionManager.compareVersions(v1, v2);
  }

  /**
   * 比较两个版本 - 使用类型安全实现（向后兼容）
   * @deprecated 使用 compareVersions 替代，返回 VersionComparisonResult
   */
  compareVersionsLegacy(v1: string, v2: string): number {
    try {
      return this.typeSafeVersionManager.compareVersions(v1, v2);
    } catch (error: unknown) {
      if (error instanceof VersionComparisonError) {
        console.warn(`Version comparison error: ${error.message}`);
        return 0; // 默认认为相等
      }
      throw error;
    }
  }

  /**
   * 检查版本是否满足范围要求 - 使用类型安全实现
   */
  satisfiesRange(version: string, range: VersionRange): boolean {
    return this.typeSafeVersionManager.satisfiesRange(version, range);
  }

  /**
   * 检查版本是否满足范围要求 - 使用类型安全实现（安全包装）
   */
  satisfiesRangeSafe(version: string, range: VersionRange): boolean {
    try {
      return this.typeSafeVersionManager.satisfiesRange(version, range);
    } catch (error: unknown) {
      if (error instanceof VersionRangeError) {
        console.warn(`Version range check failed: ${error.message}`);
        return false;
      }
      throw error;
    }
  }

  /**
   * 验证版本字符串格式
   */
  validateVersionFormat(version: string): ValidationResult {
    return this.typeSafeVersionManager.validateVersionFormat(version);
  }

  /**
   * 检查插件兼容性 - 使用类型安全实现
   */
  async checkPluginCompatibility(plugin: Plugin, context: CompatibilityContext): Promise<TypeSafeCompatibilityCheckResult> {
    const issues: TypeSafeCompatibilityIssue[] = [];
    const recommendations: string[] = [];
    let migrationRequired = false;

    try {
      // 1. 检查引擎版本兼容性
      await this.checkEngineCompatibilitySafe(plugin, issues, recommendations);

      // 2. 检查Node.js版本兼容性
      await this.checkNodeCompatibilitySafe(plugin, issues, recommendations);

      // 3. 检查插件API兼容性
      await this.checkApiCompatibilitySafe(plugin, context, issues, recommendations);

      // 4. 检查依赖兼容性
      await this.checkDependencyCompatibilitySafe(plugin, context, issues, recommendations);

      // 5. 检查配置兼容性
      await this.checkConfigurationCompatibilitySafe(plugin, context, issues, recommendations);

      // 判断是否需要迁移
      migrationRequired = issues.some(issue => 
        issue.severity === 'critical' || 
        (issue.severity === 'high' && issue.type === 'api')
      );

      return {
        isCompatible: issues.filter(i => i.severity === 'critical' || i.severity === 'high').length === 0,
        issues,
        recommendations,
        migrationRequired,
      };
    } catch (error: unknown) {
      const criticalIssue: TypeSafeCompatibilityIssue = {
        type: 'engine',
        severity: 'critical',
        description: `Failed to check plugin compatibility: ${error instanceof Error ? error.message : 'Unknown error'}`,
        affectedComponents: ['compatibility-check'],
        solution: 'Review plugin configuration and retry compatibility check',
      };

      return {
        isCompatible: false,
        issues: [criticalIssue],
        recommendations: ['Fix compatibility check errors before proceeding'],
        migrationRequired: true,
      };
    }
  }

  /**
   * 生成插件升级信息 - 使用类型安全实现
   */
  async generateUpgradeInfo(
    currentPlugin: Plugin,
    targetPlugin: Plugin
  ): Promise<TypeSafePluginUpgradeInfo> {
    try {
      const currentVersion = this.typeSafeVersionManager.parseVersion(currentPlugin.version);
      const targetVersion = this.typeSafeVersionManager.parseVersion(targetPlugin.version);

      // 确定升级类型
      let upgradeType: 'major' | 'minor' | 'patch';
      let breakingChanges = false;

      if (targetVersion.major > currentVersion.major) {
        upgradeType = 'major';
        breakingChanges = true;
      } else if (targetVersion.minor > currentVersion.minor) {
        upgradeType = 'minor';
        breakingChanges = this.hasMinorBreakingChangesSafe(currentPlugin, targetPlugin);
      } else {
        upgradeType = 'patch';
        breakingChanges = false;
      }

      // 分析依赖变化
      const dependencies = this.analyzeDependencyChangesSafe(currentPlugin, targetPlugin);

      return {
        pluginId: currentPlugin.id,
        currentVersion,
        availableVersion: targetVersion,
        upgradeType,
        breakingChanges,
        changelog: await this.generateChangelogSafe(currentPlugin, targetPlugin),
        dependencies,
      };
    } catch (error: unknown) {
      // 提供默认值以防解析失败
      const fallbackCurrent = { 
        major: 0, minor: 0, patch: 0, prerelease: null, build: null, raw: currentPlugin.version 
      } as ValidatedVersionParts;
      const fallbackTarget = { 
        major: 0, minor: 0, patch: 0, prerelease: null, build: null, raw: targetPlugin.version 
      } as ValidatedVersionParts;

      return {
        pluginId: currentPlugin.id,
        currentVersion: fallbackCurrent,
        availableVersion: fallbackTarget,
        upgradeType: 'major',
        breakingChanges: true,
        changelog: `Error generating changelog: ${error instanceof Error ? error.message : 'Unknown error'}`,
        dependencies: {
          added: [],
          removed: [],
          updated: [],
        },
      };
    }
  }

  // 私有方法 - 类型安全版本

  private async checkEngineCompatibilitySafe(
    plugin: Plugin,
    issues: TypeSafeCompatibilityIssue[],
    recommendations: string[]
  ): Promise<void> {
    if (!plugin.engineVersion) {
      recommendations.push('Consider specifying engineVersion for better compatibility checking');
      return;
    }

    try {
      if (!this.typeSafeVersionManager.satisfiesRange(this.PLUGIN_ENGINE_VERSION, plugin.engineVersion)) {
        issues.push({
          type: 'engine',
          severity: 'critical',
          description: `Plugin requires engine version ${plugin.engineVersion}, but current engine is ${this.PLUGIN_ENGINE_VERSION}`,
          affectedComponents: ['engine'],
          solution: 'Update the engine or use a compatible plugin version',
        });
      }
    } catch (error: unknown) {
      issues.push({
        type: 'engine',
        severity: 'high',
        description: `Failed to check engine compatibility: ${error instanceof Error ? error.message : 'Unknown error'}`,
        affectedComponents: ['engine'],
        solution: 'Verify engine version format and retry',
      });
    }
  }

  private async checkNodeCompatibilitySafe(
    plugin: Plugin,
    issues: TypeSafeCompatibilityIssue[],
    recommendations: string[]
  ): Promise<void> {
    try {
      const currentNodeVersion = process.version.substring(1); // 移除 'v' 前缀
      
      // 简化的Node.js版本检查
      const minNodeVersion = '18.0.0';
      if (this.typeSafeVersionManager.compareVersions(currentNodeVersion, minNodeVersion) < 0) {
        issues.push({
          type: 'engine',
          severity: 'high',
          description: `Plugin may require Node.js >= ${minNodeVersion}, but current version is ${currentNodeVersion}`,
          affectedComponents: ['runtime'],
          solution: `Upgrade Node.js to version ${minNodeVersion} or higher`,
        });
      }
    } catch (error: unknown) {
      issues.push({
        type: 'engine',
        severity: 'medium',
        description: `Failed to check Node.js compatibility: ${error instanceof Error ? error.message : 'Unknown error'}`,
        affectedComponents: ['runtime'],
        solution: 'Verify Node.js version and retry',
      });
    }
  }

  private async checkApiCompatibilitySafe(
    plugin: Plugin,
    context: CompatibilityContext,
    issues: TypeSafeCompatibilityIssue[],
    recommendations: string[]
  ): Promise<void> {
    try {
      // 检查规则API兼容性
      for (const rule of plugin.rules) {
        if (!this.isRuleApiCompatible(rule, context.apiVersion)) {
          issues.push({
            type: 'api',
            severity: 'high',
            description: `Rule '${rule.id}' uses incompatible API`,
            affectedComponents: [rule.id],
            solution: 'Update the rule to use the current API version',
          });
        }
      }
    } catch (error: unknown) {
      issues.push({
        type: 'api',
        severity: 'medium',
        description: `Failed to check API compatibility: ${error instanceof Error ? error.message : 'Unknown error'}`,
        affectedComponents: ['api'],
        solution: 'Review plugin API usage and retry',
      });
    }
  }

  private async checkDependencyCompatibilitySafe(
    plugin: Plugin,
    context: CompatibilityContext,
    issues: TypeSafeCompatibilityIssue[],
    recommendations: string[]
  ): Promise<void> {
    if (!plugin.dependencies) return;

    try {
      for (const dep of plugin.dependencies) {
        const depId = this.extractPluginIdSafe(dep);
        const depVersion = this.extractVersionRangeSafe(dep);
        
        const availablePlugin = context.availablePlugins.find(p => p.id === depId);
        
        if (!availablePlugin) {
          issues.push({
            type: 'dependency',
            severity: 'critical',
            description: `Missing dependency: ${depId}`,
            affectedComponents: [depId],
            solution: `Install plugin '${depId}'`,
          });
          continue;
        }

        if (depVersion) {
          try {
            if (!this.typeSafeVersionManager.satisfiesRange(availablePlugin.version, depVersion)) {
              issues.push({
                type: 'dependency',
                severity: 'high',
                description: `Dependency version conflict: ${depId} requires ${depVersion}, but ${availablePlugin.version} is available`,
                affectedComponents: [depId],
                solution: `Update ${depId} to a compatible version`,
              });
            }
          } catch (error: unknown) {
            issues.push({
              type: 'dependency',
              severity: 'medium',
              description: `Failed to check dependency version for ${depId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
              affectedComponents: [depId],
              solution: 'Verify dependency version format',
            });
          }
        }
      }
    } catch (error: unknown) {
      issues.push({
        type: 'dependency',
        severity: 'medium',
        description: `Failed to check dependency compatibility: ${error instanceof Error ? error.message : 'Unknown error'}`,
        affectedComponents: ['dependencies'],
        solution: 'Review plugin dependencies and retry',
      });
    }
  }

  private async checkConfigurationCompatibilitySafe(
    plugin: Plugin,
    context: CompatibilityContext,
    issues: TypeSafeCompatibilityIssue[],
    recommendations: string[]
  ): Promise<void> {
    if (!plugin.configSchema) return;

    try {
      // 检查配置schema兼容性
      const schemaIssues = this.validateConfigSchemaCompatibilitySafe(plugin.configSchema, context.currentConfig);
      
      issues.push(...schemaIssues.map(issue => ({
        type: 'configuration' as const,
        severity: issue.severity as 'low' | 'medium' | 'high' | 'critical',
        description: issue.description,
        affectedComponents: ['configuration'],
        solution: issue.solution || 'Review configuration schema compatibility',
      })));
    } catch (error: unknown) {
      issues.push({
        type: 'configuration',
        severity: 'low',
        description: `Failed to check configuration compatibility: ${error instanceof Error ? error.message : 'Unknown error'}`,
        affectedComponents: ['configuration'],
        solution: 'Review plugin configuration schema',
      });
    }
  }

  private hasMinorBreakingChangesSafe(oldPlugin: Plugin, newPlugin: Plugin): boolean {
    try {
      // 检查是否有次版本中的破坏性变更
      // 比如移除了规则、改变了规则接口等
      
      const oldRuleIds = new Set(oldPlugin.rules.map(r => r.id));
      const newRuleIds = new Set(newPlugin.rules.map(r => r.id));
      
      // 检查是否有规则被移除
      for (const oldRuleId of Array.from(oldRuleIds)) {
        if (!newRuleIds.has(oldRuleId)) {
          return true; // 移除规则是破坏性变更
        }
      }

      return false;
    } catch (error: unknown) {
      console.warn(`Error checking minor breaking changes: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return true; // 保守估计
    }
  }

  private analyzeDependencyChangesSafe(oldPlugin: Plugin, newPlugin: Plugin): {
    readonly added: readonly string[];
    readonly removed: readonly string[];
    readonly updated: readonly string[];
  } {
    try {
      const oldDeps = new Set(oldPlugin.dependencies || []);
      const newDeps = new Set(newPlugin.dependencies || []);
      
      const added: string[] = [];
      const removed: string[] = [];
      const updated: string[] = [];

      // 查找新增的依赖
      for (const dep of Array.from(newDeps)) {
        if (!oldDeps.has(dep)) {
          added.push(dep);
        }
      }

      // 查找移除的依赖
      for (const dep of Array.from(oldDeps)) {
        if (!newDeps.has(dep)) {
          removed.push(dep);
        }
      }

      // TODO: 检查版本更新的依赖
      // 这需要更复杂的逻辑来比较版本范围

      return { added, removed, updated };
    } catch (error: unknown) {
      console.warn(`Error analyzing dependency changes: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { added: [], removed: [], updated: [] };
    }
  }

  private async generateChangelogSafe(oldPlugin: Plugin, newPlugin: Plugin): Promise<string> {
    try {
      // 简化的变更日志生成
      const changes: string[] = [];
      
      // 版本变更
      changes.push(`Version: ${oldPlugin.version} → ${newPlugin.version}`);
      
      // 规则变更
      const oldRuleIds = new Set(oldPlugin.rules.map(r => r.id));
      const newRuleIds = new Set(newPlugin.rules.map(r => r.id));
      
      for (const ruleId of Array.from(newRuleIds)) {
        if (!oldRuleIds.has(ruleId)) {
          changes.push(`+ Added rule: ${ruleId}`);
        }
      }
      
      for (const ruleId of Array.from(oldRuleIds)) {
        if (!newRuleIds.has(ruleId)) {
          changes.push(`- Removed rule: ${ruleId}`);
        }
      }

      return changes.join('\n');
    } catch (error: unknown) {
      return `Error generating changelog: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  private extractPluginIdSafe(dependency: string): string {
    try {
      const parts = dependency.split('@');
      return parts[0] || dependency;
    } catch (error: unknown) {
      return dependency; // 回退到原始字符串
    }
  }

  private extractVersionRangeSafe(dependency: string): string | null {
    try {
      const parts = dependency.split('@');
      return parts.length > 1 ? parts[1] || null : null;
    } catch (error: unknown) {
      return null;
    }
  }

  private validateConfigSchemaCompatibilitySafe(schema: any, currentConfig: any): any[] {
    try {
      // 简化的配置schema兼容性检查
      return [];
    } catch (error: unknown) {
      return [{
        severity: 'low',
        description: `Configuration schema validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        solution: 'Review configuration schema format',
      }];
    }
  }

  private isRuleApiCompatible(rule: any, apiVersion: string): boolean {
    // 简化的API兼容性检查
    // 实际实现应该检查规则使用的API方法和接口
    return true; // 暂时返回true
  }
}

// 辅助接口和类型 - 保持向后兼容

interface VersionParts {
  major: number;
  minor: number;
  patch: number;
  prerelease: string | null;
  build: string | null;
  raw: string;
}

interface CompatibilityContext {
  apiVersion: string;
  availablePlugins: Plugin[];
  currentConfig: any;
}