/**
 * 插件沙箱实现
 * 提供安全隔离环境，限制插件对系统资源的访问
 */

import * as vm from 'vm';
import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import type {
  PluginSandbox,
  SandboxPermissions,
  SandboxContext,
  SandboxRestrictions,
} from './types';

/**
 * 资源监控器
 */
class ResourceMonitor {
  private memoryUsage = 0;
  private startTime = 0;
  private executionCount = 0;
  private maxMemory: number;
  private maxExecutionTime: number;

  constructor(maxMemory: number, maxExecutionTime: number) {
    this.maxMemory = maxMemory;
    this.maxExecutionTime = maxExecutionTime;
  }

  startExecution(): void {
    this.startTime = performance.now();
    this.executionCount++;
    this.updateMemoryUsage();
  }

  endExecution(): void {
    this.updateMemoryUsage();
  }

  isWithinLimits(): boolean {
    const executionTime = performance.now() - this.startTime;
    return (
      this.memoryUsage <= this.maxMemory &&
      executionTime <= this.maxExecutionTime
    );
  }

  getMemoryUsage(): number {
    this.updateMemoryUsage();
    return this.memoryUsage;
  }

  getExecutionTime(): number {
    return this.startTime > 0 ? performance.now() - this.startTime : 0;
  }

  reset(): void {
    this.memoryUsage = 0;
    this.startTime = 0;
    this.executionCount = 0;
  }

  private updateMemoryUsage(): void {
    // 简化版本：使用进程内存使用量
    const usage = process.memoryUsage();
    this.memoryUsage = usage.heapUsed;
  }
}

/**
 * 安全代理
 */
class SecurityProxy {
  private permissions: SandboxPermissions;
  private allowedModules: Set<string>;
  private blockedModules: Set<string>;

  constructor(permissions: SandboxPermissions, context: SandboxContext) {
    this.permissions = permissions;
    this.allowedModules = new Set(context.restrictions.allowedModules);
    this.blockedModules = new Set(context.restrictions.blockedModules);
  }

  createSecureRequire(pluginId: string): NodeRequire {
    const originalRequire = require;
    
    return ((moduleId: string) => {
      // 检查模块访问权限
      if (!this.canRequireModule(moduleId)) {
        throw new Error(`Plugin '${pluginId}' is not allowed to require module '${moduleId}'`);
      }

      // 限制文件系统访问
      if (moduleId.startsWith('./') || moduleId.startsWith('../') || path.isAbsolute(moduleId)) {
        if (!this.canAccessFile(moduleId, 'read')) {
          throw new Error(`Plugin '${pluginId}' is not allowed to access file '${moduleId}'`);
        }
      }

      return originalRequire(moduleId);
    }) as NodeRequire;
  }

  createSecureFileSystem(): Partial<typeof fs> {
    const originalFs = fs;
    const secureFs: Partial<typeof fs> = {};

    // 包装文件系统方法
    const wrapFsMethod = <T extends keyof typeof fs>(methodName: T) => {
      return (...args: any[]) => {
        const filePath = args[0];
        const isWrite = ['writeFile', 'writeFileSync', 'appendFile', 'appendFileSync', 'mkdir', 'mkdirSync'].includes(methodName);
        const operation = isWrite ? 'write' : 'read';

        if (!this.canAccessFile(filePath, operation)) {
          throw new Error(`File system access denied: ${operation} ${filePath}`);
        }

        return (originalFs[methodName] as Function)(...args);
      };
    };

    // 包装常用的文件系统方法
    const fsMethodsToWrap = [
      'readFile', 'readFileSync', 'writeFile', 'writeFileSync',
      'appendFile', 'appendFileSync', 'exists', 'existsSync',
      'stat', 'statSync', 'readdir', 'readdirSync',
      'mkdir', 'mkdirSync', 'rmdir', 'rmdirSync',
    ] as const;

    for (const method of fsMethodsToWrap) {
      if (method in originalFs) {
        secureFs[method] = wrapFsMethod(method);
      }
    }

    return secureFs;
  }

  createSecureProcess(): Partial<NodeJS.Process> {
    return {
      // 只允许访问基本的进程信息
      env: { ...process.env },
      cwd: process.cwd,
      version: process.version,
      versions: { ...process.versions },
      platform: process.platform,
      arch: process.arch,
      
      // 禁止退出进程
      exit: () => {
        throw new Error('Process exit is not allowed in plugin sandbox');
      },
      
      // 禁止发送信号
      kill: () => {
        throw new Error('Process kill is not allowed in plugin sandbox');
      },
    };
  }

  private canRequireModule(moduleId: string): boolean {
    // 检查是否在阻止列表中
    if (this.blockedModules.has(moduleId)) {
      return false;
    }

    // 检查是否在允许列表中（如果有允许列表）
    if (this.allowedModules.size > 0) {
      return this.allowedModules.has(moduleId) || this.isBuiltinModule(moduleId);
    }

    // 默认允许内置模块
    return this.isBuiltinModule(moduleId) || this.isSafeModule(moduleId);
  }

  private canAccessFile(filePath: string, operation: 'read' | 'write'): boolean {
    const resolvedPath = path.resolve(filePath);
    const patterns = operation === 'read' 
      ? this.permissions.fileSystem.read 
      : this.permissions.fileSystem.write;

    return patterns.some(pattern => this.matchesPattern(resolvedPath, pattern));
  }

  private matchesPattern(filePath: string, pattern: string): boolean {
    // 简化版本的通配符匹配
    if (pattern.includes('**')) {
      const basePattern = pattern.replace('**/*', '');
      return filePath.startsWith(basePattern);
    }
    
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(filePath);
    }
    
    return filePath === pattern || filePath.startsWith(pattern);
  }

  private isBuiltinModule(moduleId: string): boolean {
    try {
      return require('module').builtinModules?.includes(moduleId) || false;
    } catch {
      return false;
    }
  }

  private isSafeModule(moduleId: string): boolean {
    // 定义安全的第三方模块
    const safeModules = [
      'lodash', 'ramda', 'moment', 'dayjs', 'uuid',
      'axios', 'node-fetch', 'chalk', 'debug',
      '@swc/core', 'typescript',
    ];
    
    return safeModules.some(safe => moduleId === safe || moduleId.startsWith(`${safe}/`));
  }
}

/**
 * 插件沙箱实现
 */
export class PluginSandboxImpl extends EventEmitter implements PluginSandbox {
  readonly pluginId: string;
  readonly permissions: SandboxPermissions;
  readonly context: SandboxContext;

  private vmContext: vm.Context | null = null;
  private resourceMonitor: ResourceMonitor;
  private securityProxy: SecurityProxy;
  private isDestroyed = false;
  private timeout: number;
  private executionPromises = new Set<Promise<any>>();

  constructor(pluginId: string, permissions: SandboxPermissions) {
    super();
    
    this.pluginId = pluginId;
    this.permissions = permissions;
    this.timeout = permissions.memory.timeout;
    
    // 创建沙箱上下文
    this.context = this.createSandboxContext();
    
    // 初始化监控器和代理
    this.resourceMonitor = new ResourceMonitor(
      permissions.memory.limit,
      permissions.memory.timeout
    );
    this.securityProxy = new SecurityProxy(permissions, this.context);
    
    // 创建VM上下文
    this.createVMContext();
  }

  allocateMemory(size: number): boolean {
    if (this.isDestroyed) {
      return false;
    }

    const currentUsage = this.resourceMonitor.getMemoryUsage();
    return currentUsage + size <= this.permissions.memory.limit;
  }

  releaseMemory(): void {
    // 触发垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }
    
    this.resourceMonitor.reset();
  }

  getMemoryUsage(): number {
    return this.resourceMonitor.getMemoryUsage();
  }

  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }

  getTimeout(): number {
    return this.timeout;
  }

  isWithinLimits(): boolean {
    return !this.isDestroyed && this.resourceMonitor.isWithinLimits();
  }

  canAccess(resource: string): boolean {
    if (this.isDestroyed) {
      return false;
    }

    // 检查文件系统访问
    if (resource.startsWith('fs:')) {
      const filePath = resource.substring(3);
      return this.securityProxy['canAccessFile'](filePath, 'read');
    }

    // 检查网络访问
    if (resource.startsWith('network:')) {
      const url = resource.substring(8);
      return this.canAccessNetwork(url);
    }

    // 检查模块访问
    if (resource.startsWith('module:')) {
      const moduleId = resource.substring(7);
      return this.securityProxy['canRequireModule'](moduleId);
    }

    return false;
  }

  async requestPermission(permission: string): Promise<boolean> {
    // 简化版本：直接检查权限配置
    this.emit('permission-requested', { permission, pluginId: this.pluginId });
    
    // 实际实现中可能需要用户确认
    return this.canAccess(permission);
  }

  /**
   * 在沙箱中执行代码
   */
  async execute<T>(code: string | (() => Promise<T>)): Promise<T> {
    if (this.isDestroyed) {
      throw new Error(`Sandbox for plugin '${this.pluginId}' is destroyed`);
    }

    const promise = this.doExecute(code);
    this.executionPromises.add(promise);
    
    try {
      const result = await promise;
      return result;
    } finally {
      this.executionPromises.delete(promise);
    }
  }

  async cleanup(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    // 等待所有执行完成
    if (this.executionPromises.size > 0) {
      await Promise.allSettled(Array.from(this.executionPromises));
    }

    // 清理资源
    this.releaseMemory();
    
    // 清理VM上下文
    if (this.vmContext) {
      // VM上下文会被垃圾收集器清理
      this.vmContext = null;
    }

    this.emit('cleanup', { pluginId: this.pluginId });
  }

  async destroy(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    this.isDestroyed = true;
    
    // 中断所有正在执行的代码
    for (const promise of this.executionPromises) {
      // 注意：实际实现中需要更复杂的中断机制
      promise.catch(() => {}); // 忽略错误
    }
    this.executionPromises.clear();

    // 清理资源
    await this.cleanup();
    
    // 移除所有监听器
    this.removeAllListeners();

    this.emit('destroyed', { pluginId: this.pluginId });
  }

  private async doExecute<T>(code: string | (() => Promise<T>)): Promise<T> {
    this.resourceMonitor.startExecution();
    
    try {
      let result: T;

      if (typeof code === 'function') {
        // 执行函数
        result = await this.executeFunction(code);
      } else {
        // 执行代码字符串
        result = await this.executeCode(code);
      }

      this.resourceMonitor.endExecution();
      return result;

    } catch (error) {
      this.resourceMonitor.endExecution();
      throw error;
    }
  }

  private async executeFunction<T>(fn: () => Promise<T>): Promise<T> {
    // 检查执行限制
    if (!this.isWithinLimits()) {
      throw new Error(`Execution limits exceeded for plugin '${this.pluginId}'`);
    }

    // 创建超时处理
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Execution timeout (${this.timeout}ms) exceeded for plugin '${this.pluginId}'`));
      }, this.timeout);
    });

    // 执行函数
    return Promise.race([fn(), timeoutPromise]);
  }

  private async executeCode<T>(code: string): Promise<T> {
    if (!this.vmContext) {
      throw new Error(`VM context not available for plugin '${this.pluginId}'`);
    }

    // 检查执行限制
    if (!this.isWithinLimits()) {
      throw new Error(`Execution limits exceeded for plugin '${this.pluginId}'`);
    }

    try {
      // 在VM上下文中执行代码
      const script = new vm.Script(code, {
        filename: `plugin-${this.pluginId}.js`,
        timeout: this.timeout,
      });

      const result = script.runInContext(this.vmContext, {
        timeout: this.timeout,
        breakOnSigint: true,
      });

      return result;

    } catch (error) {
      throw new Error(`Code execution failed in plugin '${this.pluginId}': ${error}`);
    }
  }

  private createSandboxContext(): SandboxContext {
    const pluginTempDir = path.join(process.cwd(), '.temp', 'plugins', this.pluginId);
    const pluginWorkDir = path.join(process.cwd(), 'plugins', this.pluginId);

    return {
      workingDirectory: pluginWorkDir,
      tempDirectory: pluginTempDir,
      environment: {
        NODE_ENV: process.env.NODE_ENV || 'development',
        PLUGIN_ID: this.pluginId,
        PLUGIN_TEMP_DIR: pluginTempDir,
        PLUGIN_WORK_DIR: pluginWorkDir,
      },
      restrictions: {
        maxExecutionTime: this.permissions.memory.timeout,
        maxMemoryUsage: this.permissions.memory.limit,
        allowedModules: ['fs', 'path', 'crypto', 'util', '@swc/core'],
        blockedModules: ['child_process', 'cluster', 'dgram', 'dns', 'http', 'https', 'net', 'tls', 'worker_threads'],
        allowNetworkAccess: this.permissions.network.outbound.length > 0,
        allowFileSystemAccess: this.permissions.fileSystem.read.length > 0 || this.permissions.fileSystem.write.length > 0,
      },
    };
  }

  private createVMContext(): void {
    if (this.vmContext) {
      return;
    }

    // 创建安全的全局对象
    const sandbox = {
      // 基本全局对象
      console: {
        log: (...args: any[]) => console.log(`[Plugin:${this.pluginId}]`, ...args),
        error: (...args: any[]) => console.error(`[Plugin:${this.pluginId}]`, ...args),
        warn: (...args: any[]) => console.warn(`[Plugin:${this.pluginId}]`, ...args),
        info: (...args: any[]) => console.info(`[Plugin:${this.pluginId}]`, ...args),
        debug: (...args: any[]) => console.debug(`[Plugin:${this.pluginId}]`, ...args),
      },
      
      // 安全的require函数
      require: this.securityProxy.createSecureRequire(this.pluginId),
      
      // 限制的文件系统访问
      fs: this.securityProxy.createSecureFileSystem(),
      
      // 安全的进程对象
      process: this.securityProxy.createSecureProcess(),
      
      // 全局对象
      global: undefined, // 禁止访问全局对象
      globalThis: undefined,
      
      // 基本JavaScript对象
      Object,
      Array,
      String,
      Number,
      Boolean,
      Date,
      RegExp,
      Error,
      Promise,
      Map,
      Set,
      WeakMap,
      WeakSet,
      JSON,
      Math,
      
      // 计时器（受限）
      setTimeout: (callback: Function, delay: number) => {
        if (delay > this.timeout) {
          delay = this.timeout;
        }
        return setTimeout(callback, delay);
      },
      setInterval: (callback: Function, delay: number) => {
        if (delay < 100) {
          delay = 100; // 最小间隔100ms
        }
        return setInterval(callback, delay);
      },
      clearTimeout,
      clearInterval,
      
      // 插件上下文
      __pluginContext: {
        id: this.pluginId,
        permissions: this.permissions,
        workingDirectory: this.context.workingDirectory,
        tempDirectory: this.context.tempDirectory,
      },
    };

    this.vmContext = vm.createContext(sandbox, {
      name: `plugin-${this.pluginId}`,
      codeGeneration: {
        strings: false, // 禁止动态代码生成
        wasm: false,    // 禁止WebAssembly
      },
    });
  }

  private canAccessNetwork(url: string): boolean {
    if (!this.permissions.network.outbound.length) {
      return false;
    }

    return this.permissions.network.outbound.some(pattern => {
      if (pattern === '*') return true;
      return url.startsWith(pattern) || url.match(new RegExp(pattern));
    });
  }
}