/**
 * 插件系统核心类型定义
 * 支持动态加载、热更新和安全隔离的现代化插件架构
 */

import type { Node } from '@swc/core';
import type { Rule, AsyncRuleEngine, AnalysisContext, RuleResult } from '../engine/types';

// 插件接口
export interface Plugin {
  readonly id: string;
  readonly name: string;
  readonly version: string;
  readonly description?: string;
  readonly author?: string;
  readonly homepage?: string;
  readonly keywords?: string[];
  
  // 插件规则
  readonly rules: PluginRule[];
  
  // 配置Schema
  readonly configSchema?: ConfigSchema;
  
  // 插件依赖
  readonly dependencies?: string[];
  readonly peerDependencies?: string[];
  readonly engineVersion?: string; // 支持的引擎版本范围
  
  // 生命周期钩子
  onLoad?(engine: AsyncRuleEngine, config?: any): Promise<void>;
  onUnload?(engine: AsyncRuleEngine): Promise<void>;
  onConfigChange?(newConfig: any, oldConfig?: any): Promise<void>;
  
  // 插件特定功能
  getConfigDefaults?(): any;
  validateConfig?(config: any): ValidationResult;
  getMetadata?(): PluginMetadata;
  
  // Phase 6.4: 错误恢复相关方法
  reset?(): Promise<void>;
  execute?(): Promise<void>;
}

// 插件规则接口（扩展基础规则）
export interface PluginRule extends Rule {
  readonly pluginId: string;
  readonly category?: string; // 规则分类
  readonly tags?: string[]; // 规则标签
  
  // 插件规则特有属性
  readonly isExperimental?: boolean;
  readonly requiresConfig?: boolean;
  readonly configKey?: string; // 配置键路径
  
  // 高级功能
  getDocumentation?(): RuleDocumentation;
  getExamples?(): RuleExample[];
  canDisable?(): boolean;
  getCompatibilityInfo?(): CompatibilityInfo;
}

// 插件管理器接口
export interface PluginManager {
  // 插件加载和卸载
  loadPlugin(source: PluginSource): Promise<LoadedPlugin>;
  unloadPlugin(pluginId: string): Promise<void>;
  reloadPlugin(pluginId: string): Promise<void>;
  
  // 插件查询
  getPlugin(pluginId: string): LoadedPlugin | null;
  getAllPlugins(): LoadedPlugin[];
  getActivePlugins(): LoadedPlugin[];
  getInactivePlugins(): LoadedPlugin[];
  
  // 插件控制
  enablePlugin(pluginId: string): Promise<void>;
  disablePlugin(pluginId: string): Promise<void>;
  
  // 依赖管理
  resolveDependencies(pluginId: string): string[];
  validateDependencies(pluginId: string): DependencyValidationResult;
  
  // 配置管理
  updatePluginConfig(pluginId: string, config: any): Promise<void>;
  getPluginConfig(pluginId: string): any;
  
  // 监控和诊断
  getPluginStatistics(): PluginStatistics;
  getPluginHealth(pluginId: string): PluginHealth;
  
  // 热更新支持
  watchPlugins(enabled: boolean): void;
  isWatching(): boolean;
  
  // 安全和沙箱
  validatePlugin(plugin: Plugin): ValidationResult;
  createSandbox(pluginId: string): PluginSandbox;
  
  // 事件系统
  on(event: PluginEvent, handler: PluginEventHandler): void;
  off(event: PluginEvent, handler: PluginEventHandler): void;
  emit(event: PluginEvent, data: any): void;
}

// 已加载的插件
export interface LoadedPlugin {
  readonly plugin: Plugin;
  readonly metadata: PluginMetadata;
  readonly status: PluginStatus;
  readonly loadTime: number;
  readonly rules: Map<string, PluginRule>;
  readonly config: any;
  readonly sandbox?: PluginSandbox;
  readonly dependencies: string[];
  readonly stats: PluginStatistics;
  readonly health: PluginHealth;
}

// 插件来源
export type PluginSource = 
  | { type: 'file'; path: string }
  | { type: 'package'; name: string; version?: string }
  | { type: 'url'; url: string }
  | { type: 'inline'; plugin: Plugin };

// 插件状态
export type PluginStatus = 
  | 'loading'
  | 'loaded'
  | 'active'
  | 'inactive'
  | 'error'
  | 'unloading';

// 插件元数据
export interface PluginMetadata {
  readonly source: PluginSource;
  readonly loadPath: string;
  readonly checksum: string;
  readonly size: number;
  readonly lastModified: number;
  readonly loadDuration: number;
  readonly memoryUsage: number;
  readonly apiVersion: string;
}

// 配置Schema
export interface ConfigSchema {
  type: 'object';
  properties: Record<string, ConfigPropertySchema>;
  required?: string[];
  additionalProperties?: boolean;
}

export interface ConfigPropertySchema {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  description?: string;
  default?: any;
  enum?: any[];
  minimum?: number;
  maximum?: number;
  items?: ConfigPropertySchema;
  properties?: Record<string, ConfigPropertySchema>;
}

// 验证结果
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  message: string;
  path?: string;
  code?: string;
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  message: string;
  path?: string;
  code?: string;
  suggestion?: string;
}

// 依赖验证结果
export interface DependencyValidationResult extends ValidationResult {
  missingDependencies: string[];
  versionConflicts: VersionConflict[];
  circularDependencies: string[];
}

export interface VersionConflict {
  dependency: string;
  required: string;
  actual: string;
  conflictType: 'missing' | 'incompatible' | 'circular';
}

// 规则文档
export interface RuleDocumentation {
  description: string;
  rationale?: string;
  examples: RuleExample[];
  configuration?: ConfigurationDoc[];
  relatedRules?: string[];
  references?: string[];
}

export interface RuleExample {
  title: string;
  description: string;
  code: {
    good?: string;
    bad?: string;
  };
  options?: any;
}

export interface ConfigurationDoc {
  option: string;
  type: string;
  default: any;
  description: string;
  examples?: string[];
}

// 兼容性信息
export interface CompatibilityInfo {
  engineVersions: string[];
  nodeVersions: string[];
  typescript?: {
    minVersion: string;
    maxVersion?: string;
  };
  frameworks?: {
    react?: string;
    vue?: string;
    angular?: string;
  };
}

// 插件统计
export interface PluginStatistics {
  totalPlugins: number;
  activePlugins: number;
  loadedRules: number;
  memoryUsage: number;
  executionTime: number;
  errorCount: number;
  ruleExecutions: number;
  byStatus: Record<PluginStatus, number>;
  byCategory: Record<string, number>;
}

// 插件健康状态
export interface PluginHealth {
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  lastCheck: number;
  issues: HealthIssue[];
  recommendations: string[];
  metrics: {
    memoryLeaks: boolean;
    errorRate: number;
    responseTime: number;
    cacheHitRate: number;
  };
}

export interface HealthIssue {
  type: 'memory' | 'performance' | 'error' | 'security' | 'compatibility';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: number;
  suggestions?: string[];
}

// 插件沙箱
export interface PluginSandbox {
  readonly pluginId: string;
  readonly permissions: SandboxPermissions;
  readonly context: SandboxContext;
  
  // 资源管理
  allocateMemory(size: number): boolean;
  releaseMemory(): void;
  getMemoryUsage(): number;
  
  // 执行控制
  setTimeout(timeout: number): void;
  getTimeout(): number;
  isWithinLimits(): boolean;
  
  // 访问控制
  canAccess(resource: string): boolean;
  requestPermission(permission: string): Promise<boolean>;
  
  // 清理和销毁
  cleanup(): Promise<void>;
  destroy(): Promise<void>;
}

export interface SandboxPermissions {
  fileSystem: {
    read: string[]; // 允许读取的路径模式
    write: string[]; // 允许写入的路径模式
  };
  network: {
    outbound: string[]; // 允许的出站URL模式
    inbound: boolean; // 是否允许入站连接
  };
  process: {
    spawn: boolean; // 是否允许启动子进程
    signals: boolean; // 是否允许发送信号
  };
  memory: {
    limit: number; // 内存限制（字节）
    timeout: number; // 执行超时（毫秒）
  };
}

export interface SandboxContext {
  readonly workingDirectory: string;
  readonly tempDirectory: string;
  readonly environment: Record<string, string>;
  readonly restrictions: SandboxRestrictions;
}

export interface SandboxRestrictions {
  readonly maxExecutionTime: number;
  readonly maxMemoryUsage: number;
  readonly allowedModules: string[];
  readonly blockedModules: string[];
  readonly allowNetworkAccess: boolean;
  readonly allowFileSystemAccess: boolean;
}

// 插件事件系统
export type PluginEvent = 
  | 'plugin:loading'
  | 'plugin:loaded'
  | 'plugin:activating'
  | 'plugin:activated'
  | 'plugin:deactivating'
  | 'plugin:deactivated'
  | 'plugin:unloading'
  | 'plugin:unloaded'
  | 'plugin:error'
  | 'plugin:config-changed'
  | 'rule:registering'
  | 'rule:registered'
  | 'rule:unregistering'
  | 'rule:unregistered'
  | 'rule:executing'
  | 'rule:executed'
  | 'rule:error';

export interface PluginEventData {
  pluginId: string;
  plugin?: Plugin;
  error?: Error;
  config?: any;
  ruleId?: string;
  rule?: PluginRule;
  node?: Node;
  context?: AnalysisContext;
  result?: RuleResult;
  timestamp: number;
}

export type PluginEventHandler = (data: PluginEventData) => void | Promise<void>;

// 插件错误类型
export class PluginError extends Error {
  constructor(
    message: string,
    public readonly pluginId: string,
    public readonly errorType: PluginErrorType,
    public readonly isRecoverable: boolean = false,
    public readonly context?: any
  ) {
    super(message);
    this.name = 'PluginError';
  }
}

export type PluginErrorType = 
  | 'loading'
  | 'validation'
  | 'dependency'
  | 'configuration'
  | 'execution'
  | 'security'
  | 'compatibility'
  | 'sandbox'
  | 'timeout'
  | 'memory';

// 插件配置更新选项
export interface PluginConfigUpdateOptions {
  validateSchema?: boolean;
  restartPlugin?: boolean;
  notifyPlugin?: boolean;
  backup?: boolean;
}

// 插件搜索和发现
export interface PluginDiscovery {
  searchPlugins(query: PluginSearchQuery): Promise<PluginSearchResult[]>;
  getPluginInfo(source: PluginSource): Promise<PluginInfo>;
  validatePluginSource(source: PluginSource): Promise<ValidationResult>;
  downloadPlugin(source: PluginSource, destination: string): Promise<string>;
}

export interface PluginSearchQuery {
  name?: string;
  description?: string;
  author?: string;
  keywords?: string[];
  category?: string;
  engineVersion?: string;
  limit?: number;
  offset?: number;
}

export interface PluginSearchResult {
  id: string;
  name: string;
  description: string;
  version: string;
  author: string;
  keywords: string[];
  source: PluginSource;
  popularity: number;
  rating: number;
  lastUpdated: number;
}

export interface PluginInfo {
  plugin: Plugin;
  metadata: {
    size: number;
    checksum: string;
    signature?: string;
    certificate?: string;
  };
  compatibility: CompatibilityInfo;
  security: {
    permissions: SandboxPermissions;
    trustLevel: 'trusted' | 'verified' | 'unknown' | 'suspicious';
    vulnerabilities: SecurityVulnerability[];
  };
}

export interface SecurityVulnerability {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  cve?: string;
  fixedIn?: string;
  workaround?: string;
}

// 插件仓库接口
export interface PluginRepository {
  readonly name: string;
  readonly url: string;
  readonly type: 'official' | 'community' | 'private';
  readonly trustLevel: 'trusted' | 'verified' | 'unknown';
  
  search(query: PluginSearchQuery): Promise<PluginSearchResult[]>;
  getPluginInfo(pluginId: string): Promise<PluginInfo>;
  downloadPlugin(pluginId: string, version?: string): Promise<Buffer>;
  verifyPlugin(pluginData: Buffer): Promise<ValidationResult>;
}

// 插件缓存
export interface PluginCache {
  // 缓存管理
  get(key: string): Promise<any>;
  set(key: string, value: any, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  
  // 插件特定缓存
  cachePlugin(pluginId: string, plugin: Plugin): Promise<void>;
  getCachedPlugin(pluginId: string): Promise<Plugin | null>;
  invalidatePlugin(pluginId: string): Promise<void>;
  
  // 规则缓存
  cacheRuleResult(ruleId: string, nodeHash: string, result: RuleResult): Promise<void>;
  getCachedRuleResult(ruleId: string, nodeHash: string): Promise<RuleResult | null>;
  
  // 统计信息
  getHitRate(): number;
  getSize(): number;
  optimize(): Promise<void>;
}

// 热更新系统类型
export interface HotReloadManager {
  readonly config: HotReloadConfig;
  
  // 生命周期管理
  start(): void;
  stop(): void;
  getStatus(): HotReloadStatus;
  
  // 插件重载
  reloadPlugin(pluginId: string, options?: { preserveState?: boolean; force?: boolean }): Promise<ReloadResult>;
  batchReload(pluginIds: string[], options?: { preserveState?: boolean }): Promise<ReloadResult[]>;
  
  // 文件监听
  watchPlugin(pluginId: string): void;
  unwatchPlugin(pluginId: string): void;
  
  // 统计信息
  getReloadStatistics(): ReloadStatistics;
  
  // 事件系统
  on(event: HotReloadEvent, handler: (...args: any[]) => void): void;
  off(event: HotReloadEvent, handler: (...args: any[]) => void): void;
  emit(event: HotReloadEvent, ...args: any[]): boolean;
  
  // 清理
  cleanup(): void;
}

export interface HotReloadConfig {
  enabled: boolean;
  watchFiles: boolean;
  watchDependencies: boolean;
  debounceMs: number;
  maxRetries: number;
  retryDelayMs: number;
  preserveState: boolean;
  gracefulShutdown: boolean;
  batchReloads: boolean;
  batchTimeoutMs: number;
}

export type HotReloadStatus = 'stopped' | 'starting' | 'running' | 'stopping' | 'error';

export type HotReloadEvent = 
  | 'hot-reload:started'
  | 'hot-reload:stopped'
  | 'plugin:reload-start'
  | 'plugin:reload-complete'
  | 'plugin:reload-error'
  | 'batch:reload-start'
  | 'batch:reload-complete' 
  | 'batch:reload-error'
  | 'file:changed'
  | 'dependency:changed';

export interface ReloadRequest {
  pluginId: string;
  timestamp: number;
  options: {
    preserveState: boolean;
    force: boolean;
  };
  retries: number;
}

export interface ReloadResult {
  success: boolean;
  pluginId: string;
  timestamp: number;
  duration: number;
  error?: Error;
  rollback: boolean;
}

export interface ReloadStatistics {
  totalReloads: number;
  successfulReloads: number;
  failedReloads: number;
  averageReloadTime: number;
}

export interface FileWatchConfig {
  pluginId?: string;
  debounceMs?: number;
}

export interface DependencyChangeEvent {
  pluginId: string;
  dependencyId: string;
  changeType: 'added' | 'removed' | 'modified';
  timestamp: number;
}

// 扩展插件接口以支持状态管理
export interface StatefulPlugin extends Plugin {
  // 状态管理
  getState?(): Promise<any>;
  setState?(state: any): Promise<void>;
  
  // 热更新回调
  onHotReload?(oldVersion: string, newVersion: string): Promise<void>;
  onStateRestore?(state: any): Promise<void>;
}

// 开发工具接口
export interface PluginDevTools {
  // 插件生成器
  generatePlugin(template: PluginTemplate): Promise<GeneratedPlugin>;
  generateRule(template: RuleTemplate): Promise<GeneratedRule>;
  
  // 调试工具
  debug(pluginId: string, options?: DebugOptions): PluginDebugger;
  inspect(pluginId: string): PluginInspector;
  
  // 测试工具
  createTestSuite(pluginId: string): PluginTestSuite;
  runTests(pluginId: string, options?: TestOptions): Promise<TestResults>;
  
  // 性能分析
  profile(pluginId: string, options?: ProfileOptions): Promise<PluginProfile>;
  benchmark(pluginIds: string[], options?: BenchmarkOptions): Promise<BenchmarkResults>;
  
  // 文档生成
  generateDocs(pluginId: string, options?: DocOptions): Promise<PluginDocumentation>;
  
  // 开发服务器
  startDevServer(options?: DevServerOptions): Promise<DevServer>;
  
  // 实用工具
  validatePluginStructure(pluginPath: string): Promise<ValidationResult>;
  lintPlugin(pluginPath: string): Promise<LintResults>;
  formatPlugin(pluginPath: string): Promise<void>;
}

// 插件模板
export interface PluginTemplate {
  name: string;
  id: string;
  description: string;
  author: string;
  version: string;
  ruleTemplates: RuleTemplate[];
  configSchema?: any;
  dependencies?: string[];
  features?: PluginFeature[];
}

export interface RuleTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  priority: number;
  nodeTypes: string[];
  parameters?: RuleParameter[];
}

export interface RuleParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  defaultValue?: any;
  required?: boolean;
}

export type PluginFeature = 
  | 'hot-reload'
  | 'state-management' 
  | 'inter-plugin-communication'
  | 'configuration'
  | 'dependency-injection'
  | 'async-processing';

// 生成结果
export interface GeneratedPlugin {
  plugin: Plugin;
  files: GeneratedFile[];
  metadata: GenerationMetadata;
}

export interface GeneratedRule {
  rule: PluginRule;
  code: string;
  tests: string;
  docs: string;
}

export interface GeneratedFile {
  path: string;
  content: string;
  type: 'typescript' | 'javascript' | 'json' | 'markdown' | 'test';
}

export interface GenerationMetadata {
  template: string;
  timestamp: number;
  version: string;
  features: PluginFeature[];
}

// 调试工具
export interface PluginDebugger {
  setBreakpoint(ruleId: string, condition?: string): void;
  removeBreakpoint(ruleId: string): void;
  listBreakpoints(): Breakpoint[];
  step(): Promise<void>;
  continue(): Promise<void>;
  pause(): void;
  getVariables(): Promise<DebugVariable[]>;
  evaluate(expression: string): Promise<any>;
  getCallStack(): Promise<CallStackFrame[]>;
  enableLogging(level: LogLevel): void;
  getLogs(): DebugLog[];
  clearLogs(): void;
  on(event: DebugEvent, handler: (data: any) => void): void;
  off(event: DebugEvent, handler: (data: any) => void): void;
}

export interface Breakpoint {
  id: string;
  ruleId: string;
  condition?: string;
  enabled: boolean;
  hitCount: number;
}

export interface DebugVariable {
  name: string;
  value: any;
  type: string;
  scope: 'global' | 'local' | 'parameter';
}

export interface CallStackFrame {
  ruleId: string;
  methodName: string;
  fileName: string;
  lineNumber: number;
  variables: DebugVariable[];
}

export interface DebugLog {
  timestamp: number;
  level: LogLevel;
  message: string;
  data?: any;
  ruleId?: string;
}

export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error';
export type DebugEvent = 'breakpoint' | 'step' | 'pause' | 'continue' | 'error';

// 检查工具
export interface PluginInspector {
  getBasicInfo(): PluginBasicInfo;
  getMetadata(): PluginInspectorMetadata;
  analyzeRules(): RuleAnalysis[];
  validateRules(): RuleValidationResult[];
  analyzeDependencies(): DependencyAnalysis;
  getPerformanceMetrics(): PerformanceMetrics;
  getMemoryUsage(): MemoryUsage;
  analyzeConfiguration(): ConfigurationAnalysis;
}

export interface PluginBasicInfo {
  id: string;
  name: string;
  version: string;
  status: string;
  loadTime: number;
  ruleCount: number;
  memoryUsage: number;
}

export interface PluginInspectorMetadata {
  source: any;
  checksum: string;
  size: number;
  lastModified: number;
  apiVersion: string;
}

export interface RuleAnalysis {
  ruleId: string;
  complexity: number;
  dependencies: string[];
  performance: {
    averageExecutionTime: number;
    totalExecutions: number;
    errorRate: number;
  };
  coverage: {
    nodeTypes: string[];
    testCoverage: number;
  };
}

export interface RuleValidationResult {
  ruleId: string;
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface DependencyAnalysis {
  directDependencies: string[];
  transitiveDependencies: string[];
  circularDependencies: string[];
  missingDependencies: string[];
  unusedDependencies: string[];
  dependencyGraph: DependencyNode[];
}

export interface DependencyNode {
  id: string;
  dependencies: string[];
  dependents: string[];
  depth: number;
}

export interface PerformanceMetrics {
  executionTime: {
    total: number;
    average: number;
    min: number;
    max: number;
    p95: number;
    p99: number;
  };
  throughput: {
    rulesPerSecond: number;
    nodesPerSecond: number;
  };
  errors: {
    total: number;
    rate: number;
    byType: Record<string, number>;
  };
}

export interface MemoryUsage {
  current: number;
  peak: number;
  average: number;
  allocated: number;
  freed: number;
  gcCount: number;
  leaks: MemoryLeak[];
}

export interface MemoryLeak {
  object: string;
  size: number;
  age: number;
  location: string;
}

export interface ConfigurationAnalysis {
  schema: any;
  currentConfig: any;
  defaultConfig: any;
  validation: ValidationResult;
  recommendations: ConfigRecommendation[];
}

export interface ConfigRecommendation {
  path: string;
  current: any;
  recommended: any;
  reason: string;
  impact: 'low' | 'medium' | 'high';
}

// 测试工具
export interface PluginTestSuite {
  addTest(test: PluginTest): void;
  removeTest(testId: string): void;
  listTests(): PluginTest[];
  runAll(): Promise<TestResults>;
  runTest(testId: string): Promise<TestResult>;
  generateCoverage(): Promise<CoverageReport>;
}

export interface PluginTest {
  id: string;
  name: string;
  description: string;
  type: 'unit' | 'integration' | 'performance' | 'regression';
  ruleId?: string;
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
  test: () => Promise<void>;
  timeout?: number;
  skip?: boolean;
}

export interface TestResults {
  total: number;
  passed: number;
  failed: number;
  skipped: number;
  duration: number;
  results: TestResult[];
  coverage?: CoverageReport;
}

export interface TestResult {
  testId: string;
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: Error;
  logs: string[];
}

export interface CoverageReport {
  lines: CoverageMetrics;
  statements: CoverageMetrics;
  functions: CoverageMetrics;
  branches: CoverageMetrics;
  files: FileCoverage[];
}

export interface CoverageMetrics {
  total: number;
  covered: number;
  percentage: number;
}

export interface FileCoverage {
  path: string;
  lines: CoverageMetrics;
  statements: CoverageMetrics;
  functions: CoverageMetrics;
  branches: CoverageMetrics;
  uncoveredLines: number[];
}

// 性能分析
export interface PluginProfile {
  pluginId: string;
  duration: number;
  samples: ProfileSample[];
  hotspots: ProfileHotspot[];
  memoryProfile: MemoryProfile;
  recommendations: PerformanceRecommendation[];
}

export interface ProfileSample {
  timestamp: number;
  ruleId: string;
  method: string;
  duration: number;
  memoryDelta: number;
  cpuUsage: number;
}

export interface ProfileHotspot {
  location: string;
  totalTime: number;
  selfTime: number;
  callCount: number;
  averageTime: number;
  percentage: number;
}

export interface MemoryProfile {
  allocations: AllocationSite[];
  heapSnapshot: HeapSnapshot;
  gcEvents: GCEvent[];
}

export interface AllocationSite {
  location: string;
  count: number;
  size: number;
  retained: number;
}

export interface HeapSnapshot {
  timestamp: number;
  totalSize: number;
  objects: ObjectSummary[];
}

export interface ObjectSummary {
  type: string;
  count: number;
  size: number;
  examples: any[];
}

export interface GCEvent {
  timestamp: number;
  type: string;
  duration: number;
  beforeSize: number;
  afterSize: number;
  freed: number;
}

export interface PerformanceRecommendation {
  type: 'optimization' | 'refactoring' | 'caching' | 'memory';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location: string;
  impact: string;
  fix: string;
}

// 基准测试
export interface BenchmarkResults {
  timestamp: number;
  duration: number;
  results: PluginBenchmark[];
  comparison: BenchmarkComparison[];
  summary: BenchmarkSummary;
}

export interface PluginBenchmark {
  pluginId: string;
  metrics: BenchmarkMetrics;
  ranking: number;
  score: number;
}

export interface BenchmarkMetrics {
  executionTime: number;
  memoryUsage: number;
  throughput: number;
  errorRate: number;
  cpuUsage: number;
}

export interface BenchmarkComparison {
  pluginA: string;
  pluginB: string;
  comparison: {
    executionTime: number;
    memoryUsage: number;
    throughput: number;
    overall: number;
  };
}

export interface BenchmarkSummary {
  fastest: string;
  slowest: string;
  mostMemoryEfficient: string;
  leastMemoryEfficient: string;
  mostReliable: string;
  recommendations: string[];
}

// 文档生成
export interface PluginDocumentation {
  pluginId: string;
  readme: string;
  api: ApiDocumentation;
  rules: DetailedRuleDocumentation[];
  examples: CodeExample[];
  changelog: ChangelogEntry[];
}

export interface ApiDocumentation {
  interfaces: InterfaceDoc[];
  methods: MethodDoc[];
  events: EventDoc[];
  configuration: ConfigDoc[];
}

export interface InterfaceDoc {
  name: string;
  description: string;
  properties: PropertyDoc[];
  extends?: string[];
}

export interface PropertyDoc {
  name: string;
  type: string;
  description: string;
  optional: boolean;
  defaultValue?: any;
}

export interface MethodDoc {
  name: string;
  description: string;
  parameters: ParameterDoc[];
  returns: ReturnDoc;
  examples: string[];
  throws?: string[];
}

export interface ParameterDoc {
  name: string;
  type: string;
  description: string;
  optional: boolean;
  defaultValue?: any;
}

export interface ReturnDoc {
  type: string;
  description: string;
}

export interface EventDoc {
  name: string;
  description: string;
  payload: PropertyDoc[];
  examples: string[];
}

export interface ConfigDoc {
  path: string;
  type: string;
  description: string;
  defaultValue: any;
  examples: any[];
  validation?: string;
}

export interface DetailedRuleDocumentation {
  ruleId: string;
  name: string;
  description: string;
  category: string;
  examples: RuleExample[];
  configuration: ConfigurationDoc[];
  notes: string[];
}

export interface CodeExample {
  title: string;
  description: string;
  code: string;
  language: string;
  tags: string[];
}

export interface ChangelogEntry {
  version: string;
  date: string;
  changes: ChangelogChange[];
}

export interface ChangelogChange {
  type: 'added' | 'changed' | 'deprecated' | 'removed' | 'fixed' | 'security';
  description: string;
  breaking?: boolean;
}

// 开发服务器
export interface DevServer {
  start(): Promise<void>;
  stop(): Promise<void>;
  restart(): Promise<void>;
  getStatus(): DevServerStatus;
  enableHotReload(): void;
  disableHotReload(): void;
  addMiddleware(middleware: DevMiddleware): void;
  removeMiddleware(name: string): void;
  watchFiles(patterns: string[]): void;
  unwatchFiles(patterns: string[]): void;
  on(event: DevServerEvent, handler: (data: any) => void): void;
  off(event: DevServerEvent, handler: (data: any) => void): void;
}

export interface DevServerStatus {
  running: boolean;
  port: number;
  hotReload: boolean;
  connectedClients: number;
  uptime: number;
}

export interface DevMiddleware {
  name: string;
  handler: (req: any, res: any, next: () => void) => void;
  order?: number;
}

export type DevServerEvent = 'started' | 'stopped' | 'error' | 'file-changed' | 'hot-reload';

// 选项接口
export interface DebugOptions {
  breakOnStart?: boolean;
  logLevel?: LogLevel;
  enableProfiling?: boolean;
}

export interface TestOptions {
  pattern?: string;
  coverage?: boolean;
  parallel?: boolean;
  timeout?: number;
}

export interface ProfileOptions {
  duration?: number;
  sampleRate?: number;
  memoryProfiling?: boolean;
  cpuProfiling?: boolean;
}

export interface BenchmarkOptions {
  iterations?: number;
  warmup?: number;
  timeout?: number;
  metrics?: ('time' | 'memory' | 'cpu' | 'throughput')[];
}

export interface DocOptions {
  format?: 'markdown' | 'html' | 'json';
  includeExamples?: boolean;
  includeInternals?: boolean;
  template?: string;
}

export interface DevServerOptions {
  port?: number;
  host?: string;
  hotReload?: boolean;
  proxy?: Record<string, string>;
  middleware?: DevMiddleware[];
}

export interface LintResults {
  errors: LintIssue[];
  warnings: LintIssue[];
  suggestions: LintIssue[];
  summary: LintSummary;
}

export interface LintIssue {
  file: string;
  line: number;
  column: number;
  severity: 'error' | 'warning' | 'suggestion';
  rule: string;
  message: string;
  fix?: string;
}

export interface LintSummary {
  totalFiles: number;
  totalIssues: number;
  errorCount: number;
  warningCount: number;
  suggestionCount: number;
}

// 插件消息和通信相关类型
export interface PluginMessage {
  id: string;
  type: 'request' | 'response' | 'event' | 'broadcast';
  source: string;
  target?: string;
  timestamp: number;
  payload: any;
  correlationId?: string;
}

export interface PluginMessageHandler {
  handle(message: PluginMessage): Promise<PluginMessage | void>;
}

// 错误恢复相关类型
export interface RecoveryStrategy {
  id: string;
  name: string;
  priority: number;
  canRecover(error: Error): boolean;
  recover(error: Error, context: any): Promise<RecoveryResult>;
}

export type RecoveryStrategyType = 
  | 'restart'
  | 'reload'
  | 'reset'
  | 'fallback'
  | 'disable'
  | 'isolate'
  | 'retry';

export type RecoveryAction = 
  | 'retry' 
  | 'fallback' 
  | 'ignore' 
  | 'restart' 
  | 'circuit-break' 
  | 'degrade' 
  | 'isolate'
  | 'plugin_restarted'
  | 'plugin_reloaded'
  | 'plugin_reset'
  | 'plugin_degraded'
  | 'plugin_disabled'
  | 'plugin_isolated';

export interface RecoveryResult {
  success: boolean;
  action: RecoveryAction;
  message?: string;
  data?: any;
  pluginId?: string;
  strategy?: RecoveryStrategyType;
  duration?: number;
  timestamp?: number;
}

export interface HealthCheck {
  id: string;
  name: string;
  check(): Promise<HealthStatus>;
}

export type HealthStatus = 'healthy' | 'warning' | 'unhealthy' | 'unknown';

// Phase 6.1: 缺失的错误恢复相关类型定义
export interface ErrorRecoveryManager {
  readonly strategies: Map<string, RecoveryStrategy>;
  readonly policies: Map<string, RecoveryPolicy>;
  
  registerStrategy(strategy: RecoveryStrategy): void;
  unregisterStrategy(strategyId: string): void;
  
  addPolicy(policy: RecoveryPolicy): void;
  removePolicy(policyId: string): void;
  
  handleError(error: Error, context: any): Promise<RecoveryResult>;
  canRecover(error: Error): boolean;
  
  getStatistics(): RecoveryStatistics;
  reset(): void;
}

export interface ErrorPattern {
  id: string;
  name: string;
  pattern: RegExp | string;
  messagePattern?: RegExp | string;
  matcher?: (error: Error) => boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'runtime' | 'syntax' | 'type' | 'configuration' | 'network' | 'system';
  errorType: string;
  description: string;
  recoverable: boolean;
  suggestedActions: string[];
  recommendedStrategy?: RecoveryStrategyType;
}

export interface CircuitBreakerConfig {
  enabled: boolean;
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringWindow: number;
  halfOpenMaxCalls: number;
  resetTimeout: number;
  timeout: number;
  successThreshold: number;
  onStateChange?: (oldState: CircuitBreakerState, newState: CircuitBreakerState) => void;
}

export interface RecoveryPolicy {
  id: string;
  name: string;
  priority: number;
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
  maxRetryDelay: number;
  
  errorPatterns: string[];
  recoveryStrategies: string[];
  defaultStrategy?: RecoveryStrategyType;
  healthCheck?: HealthCheck;
  
  shouldApply(error: Error): boolean;
  getDegradationLevel(): DegradationLevel;
}

export type DegradationLevel = 
  | 'none'         // 正常运行
  | 'graceful'     // 优雅降级
  | 'partial'      // 部分功能降级
  | 'minimal'      // 最小功能模式
  | 'emergency'    // 紧急模式
  | 'isolated'     // 隔离模式
  | 'critical';    // 严重错误级别

export interface FallbackConfig {
  enabled: boolean;
  strategy: 'static' | 'dynamic' | 'cached' | 'default';
  staticValue?: any;
  dynamicHandler?: (error: Error, context: any) => Promise<any>;
  cacheKey?: string;
  cacheTtl?: number;
  defaultValue?: any;
  timeout?: number;
  degradationLevel?: DegradationLevel;
  onRestore?: () => Promise<void>;
}

export type CircuitBreakerState = 
  | 'closed'      // 正常状态，允许请求通过
  | 'open'        // 断路器打开，拒绝请求
  | 'half-open';  // 半开状态，允许限量请求测试

export interface RecoveryStatistics {
  totalErrors: number;
  recoveredErrors: number;
  failedRecoveries: number;
  recoveryRate: number;
  averageRecoveryTime: number;
  
  byStrategy: Record<string, {
    attempts: number;
    successes: number;
    failures: number;
    averageTime: number;
  }>;
  
  byErrorType: Record<string, {
    count: number;
    recovered: number;
    rate: number;
  }>;
  
  circuitBreakerStats: {
    state: CircuitBreakerState;
    failureCount: number;
    lastFailureTime: number;
    nextAttemptTime: number;
  };
}