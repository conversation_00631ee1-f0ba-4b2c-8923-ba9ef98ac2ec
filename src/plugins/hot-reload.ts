/**
 * 插件热更新管理器
 * 支持文件监听、状态保持和批量重载的智能热更新系统
 */

import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';
import type {
  PluginManager,
  HotReloadManager,
  HotReloadConfig,
  HotReloadStatus,
  HotReloadEvent,
  ReloadRequest,
  ReloadResult,
  ReloadStatistics,
  DependencyChangeEvent,
  StatefulPlugin,
} from './types';

/**
 * 热更新管理器实现
 */
export class HotReloadManagerImpl extends EventEmitter implements HotReloadManager {
  readonly config: HotReloadConfig;
  private pluginManager: PluginManager;
  private status: HotReloadStatus = 'stopped';
  private watchers = new Map<string, fs.FSWatcher>();
  private reloadQueue = new Map<string, ReloadRequest>();
  private batchReloadTimer?: NodeJS.Timeout;
  private statistics: ReloadStatistics = {
    totalReloads: 0,
    successfulReloads: 0,
    failedReloads: 0,
    averageReloadTime: 0,
  };

  constructor(pluginManager: PluginManager, config: Partial<HotReloadConfig> = {}) {
    super();
    this.pluginManager = pluginManager;
    this.config = {
      enabled: true,
      watchFiles: true,
      watchDependencies: true,
      debounceMs: 100,
      maxRetries: 3,
      retryDelayMs: 1000,
      preserveState: true,
      gracefulShutdown: true,
      batchReloads: true,
      batchTimeoutMs: 500,
      ...config,
    };
  }

  start(): void {
    if (this.status === 'running' || !this.config.enabled) {
      return;
    }

    this.status = 'starting';
    this.emit('hot-reload:started');

    try {
      if (this.config.watchFiles) {
        this.startFileWatching();
      }

      if (this.config.watchDependencies) {
        this.startDependencyWatching();
      }

      this.status = 'running';
      console.log('Hot reload manager started');
    } catch (error) {
      this.status = 'error';
      console.error('Failed to start hot reload manager:', error);
      throw error;
    }
  }

  stop(): void {
    if (this.status === 'stopped') {
      return;
    }

    this.status = 'stopping';
    this.emit('hot-reload:stopped');

    // 停止所有文件监听
    for (const [pluginId, watcher] of this.watchers.entries()) {
      try {
        watcher.close();
      } catch (error) {
        console.warn(`Failed to close watcher for plugin '${pluginId}':`, error);
      }
    }
    this.watchers.clear();

    // 清理批量重载定时器
    if (this.batchReloadTimer) {
      clearTimeout(this.batchReloadTimer);
      this.batchReloadTimer = undefined;
    }

    // 清空重载队列
    this.reloadQueue.clear();

    this.status = 'stopped';
    console.log('Hot reload manager stopped');
  }

  getStatus(): HotReloadStatus {
    return this.status;
  }

  async reloadPlugin(
    pluginId: string, 
    options: { preserveState?: boolean; force?: boolean } = {}
  ): Promise<ReloadResult> {
    const startTime = performance.now();
    const request: ReloadRequest = {
      pluginId,
      timestamp: Date.now(),
      options: {
        preserveState: options.preserveState ?? this.config.preserveState,
        force: options.force ?? false,
      },
      retries: 0,
    };

    try {
      this.emit('plugin:reload-start', { pluginId, timestamp: request.timestamp });

      // 获取插件状态（如果需要保持）
      let pluginState: any = undefined;
      if (request.options.preserveState) {
        pluginState = await this.preservePluginState(pluginId);
      }

      // 执行重载
      await this.pluginManager.reloadPlugin(pluginId);

      // 恢复插件状态（如果需要）
      if (pluginState && request.options.preserveState) {
        await this.restorePluginState(pluginId, pluginState);
      }

      const result: ReloadResult = {
        success: true,
        pluginId,
        timestamp: request.timestamp,
        duration: performance.now() - startTime,
        rollback: false,
      };

      this.statistics.totalReloads++;
      this.statistics.successfulReloads++;
      this.updateAverageReloadTime(result.duration);

      this.emit('plugin:reload-complete', result);
      console.log(`Plugin '${pluginId}' reloaded successfully in ${result.duration.toFixed(2)}ms`);

      return result;

    } catch (error) {
      const result: ReloadResult = {
        success: false,
        pluginId,
        timestamp: request.timestamp,
        duration: performance.now() - startTime,
        error: error as Error,
        rollback: false,
      };

      // 尝试重试
      if (request.retries < this.config.maxRetries) {
        console.warn(`Plugin '${pluginId}' reload failed, retrying... (${request.retries + 1}/${this.config.maxRetries})`);
        
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelayMs));
        request.retries++;
        
        return this.reloadPlugin(pluginId, options);
      }

      this.statistics.totalReloads++;
      this.statistics.failedReloads++;

      this.emit('plugin:reload-error', result);
      console.error(`Plugin '${pluginId}' reload failed after ${this.config.maxRetries} retries:`, error);

      return result;
    }
  }

  async batchReload(
    pluginIds: string[], 
    options: { preserveState?: boolean } = {}
  ): Promise<ReloadResult[]> {
    if (pluginIds.length === 0) {
      return [];
    }

    try {
      this.emit('batch:reload-start', { pluginIds, timestamp: Date.now() });

      const reloadPromises = pluginIds.map(pluginId => 
        this.reloadPlugin(pluginId, options)
      );

      const results = await Promise.allSettled(reloadPromises);
      const reloadResults: ReloadResult[] = results.map(result => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          return {
            success: false,
            pluginId: 'unknown',
            timestamp: Date.now(),
            duration: 0,
            error: result.reason,
            rollback: false,
          };
        }
      });

      const successCount = reloadResults.filter(r => r.success).length;
      console.log(`Batch reload completed: ${successCount}/${pluginIds.length} plugins reloaded successfully`);

      this.emit('batch:reload-complete', { results: reloadResults, timestamp: Date.now() });
      return reloadResults;

    } catch (error) {
      this.emit('batch:reload-error', { error, timestamp: Date.now() });
      throw error;
    }
  }

  watchPlugin(pluginId: string): void {
    if (this.watchers.has(pluginId)) {
      return; // 已经在监听
    }

    const plugin = this.pluginManager.getPlugin(pluginId);
    if (!plugin) {
      console.warn(`Cannot watch plugin '${pluginId}': plugin not found`);
      return;
    }

    // 只监听文件类型的插件
    if (plugin.metadata.source.type !== 'file') {
      return;
    }

    const filePath = plugin.metadata.source.path;
    
    try {
      const watcher = fs.watch(filePath, { persistent: false }, (eventType) => {
        if (eventType === 'change') {
          this.emit('file:changed', { pluginId, filePath, timestamp: Date.now() });
          this.scheduleReload(pluginId);
        }
      });

      this.watchers.set(pluginId, watcher);
      console.log(`Started watching plugin file: ${filePath}`);

    } catch (error) {
      console.warn(`Failed to watch plugin file '${filePath}':`, error);
    }
  }

  unwatchPlugin(pluginId: string): void {
    const watcher = this.watchers.get(pluginId);
    if (watcher) {
      try {
        watcher.close();
        this.watchers.delete(pluginId);
        console.log(`Stopped watching plugin '${pluginId}'`);
      } catch (error) {
        console.warn(`Failed to stop watching plugin '${pluginId}':`, error);
      }
    }
  }

  getReloadStatistics(): ReloadStatistics {
    return { ...this.statistics };
  }

  cleanup(): void {
    this.stop();
    this.removeAllListeners();
  }

  // 私有方法

  private startFileWatching(): void {
    const activePlugins = this.pluginManager.getActivePlugins();
    
    for (const loadedPlugin of activePlugins) {
      this.watchPlugin(loadedPlugin.plugin.id);
    }

    // 监听新插件加载
    this.pluginManager.on('plugin:loaded', (data) => {
      if (this.config.watchFiles) {
        this.watchPlugin(data.pluginId);
      }
    });

    // 监听插件卸载
    this.pluginManager.on('plugin:unloaded', (data) => {
      this.unwatchPlugin(data.pluginId);
    });
  }

  private startDependencyWatching(): void {
    // 监听依赖变化
    this.pluginManager.on('plugin:loaded', (data) => {
      this.checkDependencyChanges(data.pluginId);
    });

    this.pluginManager.on('plugin:unloaded', (data) => {
      this.notifyDependents(data.pluginId, 'removed');
    });
  }

  private scheduleReload(pluginId: string): void {
    if (!this.config.batchReloads) {
      // 立即重载
      this.reloadPlugin(pluginId).catch(error => {
        console.error(`Failed to reload plugin '${pluginId}':`, error);
      });
      return;
    }

    // 添加到批量重载队列
    this.reloadQueue.set(pluginId, {
      pluginId,
      timestamp: Date.now(),
      options: {
        preserveState: this.config.preserveState,
        force: false,
      },
      retries: 0,
    });

    // 重置批量重载定时器
    if (this.batchReloadTimer) {
      clearTimeout(this.batchReloadTimer);
    }

    this.batchReloadTimer = setTimeout(() => {
      this.processBatchReload();
    }, this.config.batchTimeoutMs);
  }

  private async processBatchReload(): Promise<void> {
    if (this.reloadQueue.size === 0) {
      return;
    }

    const pluginIds = Array.from(this.reloadQueue.keys());
    this.reloadQueue.clear();

    console.log(`Processing batch reload for ${pluginIds.length} plugins...`);

    try {
      await this.batchReload(pluginIds, {
        preserveState: this.config.preserveState,
      });
    } catch (error) {
      console.error('Batch reload failed:', error);
    }
  }

  private async preservePluginState(pluginId: string): Promise<any> {
    const plugin = this.pluginManager.getPlugin(pluginId);
    if (!plugin) {
      return undefined;
    }

    const pluginInstance = plugin.plugin as StatefulPlugin;
    
    if (typeof pluginInstance.getState === 'function') {
      try {
        return await pluginInstance.getState();
      } catch (error) {
        console.warn(`Failed to preserve state for plugin '${pluginId}':`, error);
        return undefined;
      }
    }

    return undefined;
  }

  private async restorePluginState(pluginId: string, state: any): Promise<void> {
    const plugin = this.pluginManager.getPlugin(pluginId);
    if (!plugin || !state) {
      return;
    }

    const pluginInstance = plugin.plugin as StatefulPlugin;
    
    if (typeof pluginInstance.setState === 'function') {
      try {
        await pluginInstance.setState(state);
      } catch (error) {
        console.warn(`Failed to restore state for plugin '${pluginId}':`, error);
      }
    }

    if (typeof pluginInstance.onStateRestore === 'function') {
      try {
        await pluginInstance.onStateRestore(state);
      } catch (error) {
        console.warn(`Failed to call onStateRestore for plugin '${pluginId}':`, error);
      }
    }
  }

  private checkDependencyChanges(pluginId: string): void {
    const plugin = this.pluginManager.getPlugin(pluginId);
    if (!plugin || !plugin.plugin.dependencies) {
      return;
    }

    for (const dependencyId of plugin.plugin.dependencies) {
      const dependency = this.pluginManager.getPlugin(dependencyId);
      if (dependency) {
        this.emit('dependency:changed', {
          pluginId,
          dependencyId,
          changeType: 'added',
          timestamp: Date.now(),
        } as DependencyChangeEvent);
      }
    }
  }

  private notifyDependents(pluginId: string, changeType: 'added' | 'removed' | 'modified'): void {
    const allPlugins = this.pluginManager.getAllPlugins();
    
    for (const loadedPlugin of allPlugins) {
      if (loadedPlugin.plugin.dependencies?.includes(pluginId)) {
        this.emit('dependency:changed', {
          pluginId: loadedPlugin.plugin.id,
          dependencyId: pluginId,
          changeType,
          timestamp: Date.now(),
        } as DependencyChangeEvent);

        // 如果依赖被移除，可能需要重载依赖它的插件
        if (changeType === 'removed') {
          console.warn(`Plugin '${loadedPlugin.plugin.id}' depends on removed plugin '${pluginId}'`);
        }
      }
    }
  }

  private updateAverageReloadTime(duration: number): void {
    const totalTime = this.statistics.averageReloadTime * this.statistics.successfulReloads;
    this.statistics.averageReloadTime = (totalTime + duration) / (this.statistics.successfulReloads + 1);
  }
}

/**
 * 创建热更新管理器
 */
export function createHotReloadManager(
  pluginManager: PluginManager,
  config?: Partial<HotReloadConfig>
): HotReloadManager {
  return new HotReloadManagerImpl(pluginManager, config);
}