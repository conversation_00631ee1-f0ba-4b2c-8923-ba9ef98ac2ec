/**
 * 安全插件沙箱实现
 * 提供类型安全的插件隔离环境，增强权限管理和API访问控制
 * 
 * 基于现有沙箱集成和权限管理框架，提供以下增强功能：
 * - 类型安全的配置验证
 * - 代理模式的安全API访问
 * - 细粒度的权限控制机制
 * - 详细的安全审计日志
 */

import * as vm from 'vm';
import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';
import type { PluginSandbox } from './types';

/**
 * 类型安全的沙箱配置选项
 * 
 * 相比原有 SandboxPermissions，提供更精确的类型约束和配置验证
 */
export interface TypeSafeSandboxOptions {
  readonly memory: {
    readonly limit: number;        // 内存限制（字节）
    readonly timeout: number;      // 执行超时（毫秒）
    readonly enableGC: boolean;    // 是否启用垃圾收集
  };
  readonly fileSystem: {
    readonly readPaths: readonly string[];      // 允许读取的路径模式
    readonly writePaths: readonly string[];     // 允许写入的路径模式
    readonly enableWatching: boolean;           // 是否允许文件监听
  };
  readonly network: {
    readonly allowedHosts: readonly string[];   // 允许访问的主机列表
    readonly enableOutbound: boolean;           // 是否允许出站连接
  };
  readonly permissions: {
    readonly allowDynamicImport: boolean;       // 是否允许动态导入
    readonly allowEval: boolean;                // 是否允许 eval
    readonly allowModuleAccess: readonly string[]; // 允许访问的模块列表
  };
}

/**
 * 安全代理接口
 * 提供代理模式的安全API访问
 */
export interface SecurityProxy {
  canAccessFileSystem(path: string): boolean;
  canAccessNetwork(url: string): boolean;
  canAccessModule(moduleName: string): boolean;
  createSecureContext(): Record<string, unknown>;
}

/**
 * 类型安全的安全代理实现
 */
export class TypeSafeSecurityProxy implements SecurityProxy {
  private readonly options: TypeSafeSandboxOptions;
  private readonly auditLog: SecurityAuditEntry[] = [];

  constructor(options: TypeSafeSandboxOptions) {
    this.options = options;
  }

  canAccessFileSystem(filePath: string): boolean {
    const resolvedPath = path.resolve(filePath);
    
    // 检查读取权限
    const canRead = this.options.fileSystem.readPaths.some(allowedPath => {
      const resolvedAllowed = path.resolve(allowedPath);
      return resolvedPath.startsWith(resolvedAllowed);
    });
    
    // 检查写入权限
    const canWrite = this.options.fileSystem.writePaths.some(allowedPath => {
      const resolvedAllowed = path.resolve(allowedPath);
      return resolvedPath.startsWith(resolvedAllowed);
    });

    const hasAccess = canRead || canWrite;
    
    // 记录访问尝试
    this.auditLog.push({
      type: 'filesystem',
      resource: filePath,
      allowed: hasAccess,
      timestamp: Date.now(),
      operation: canWrite ? 'write' : 'read'
    });

    return hasAccess;
  }

  canAccessNetwork(url: string): boolean {
    if (!this.options.network.enableOutbound) {
      this.logSecurityEvent('network', url, false, 'outbound_disabled');
      return false;
    }

    try {
      const parsedUrl = new URL(url);
      const hasAccess = this.options.network.allowedHosts.some(host => {
        if (host === '*') return true;
        return parsedUrl.hostname === host || parsedUrl.hostname.endsWith(`.${host}`);
      });

      this.logSecurityEvent('network', url, hasAccess, hasAccess ? 'allowed_host' : 'blocked_host');
      return hasAccess;
    } catch (error) {
      this.logSecurityEvent('network', url, false, 'invalid_url');
      return false;
    }
  }

  canAccessModule(moduleName: string): boolean {
    // 检查是否在允许列表中
    const isAllowed = this.options.permissions.allowModuleAccess.includes(moduleName);
    
    // 检查是否为内置模块
    const isBuiltin = this.isBuiltinModule(moduleName);
    
    // 检查是否为安全的第三方模块
    const isSafe = this.isSafeModule(moduleName);

    const hasAccess = isAllowed || isBuiltin || isSafe;
    
    this.logSecurityEvent('module', moduleName, hasAccess, 
      isAllowed ? 'explicitly_allowed' : 
      isBuiltin ? 'builtin_module' : 
      isSafe ? 'safe_module' : 'blocked_module'
    );

    return hasAccess;
  }

  createSecureContext(): Record<string, unknown> {
    return {
      // 基本全局对象（只读）
      Object: Object,
      Array: Array,
      String: String,
      Number: Number,
      Boolean: Boolean,
      Date: Date,
      RegExp: RegExp,
      Error: Error,
      Promise: Promise,
      Map: Map,
      Set: Set,
      WeakMap: WeakMap,
      WeakSet: WeakSet,
      JSON: JSON,
      Math: Math,

      // 禁用危险的全局对象
      global: undefined,
      globalThis: undefined,
      eval: this.options.permissions.allowEval ? eval : undefined,
      Function: this.options.permissions.allowEval ? Function : undefined,
    };
  }

  /**
   * 获取安全审计日志
   */
  getAuditLog(): readonly SecurityAuditEntry[] {
    return [...this.auditLog];
  }

  /**
   * 清空审计日志
   */
  clearAuditLog(): void {
    this.auditLog.length = 0;
  }

  private isBuiltinModule(moduleId: string): boolean {
    try {
      const builtinModules = require('module').builtinModules;
      return Array.isArray(builtinModules) && builtinModules.includes(moduleId);
    } catch {
      // 如果无法获取内置模块列表，使用预定义列表
      const commonBuiltins = [
        'fs', 'path', 'crypto', 'util', 'stream', 'events', 
        'buffer', 'url', 'querystring', 'os', 'zlib'
      ];
      return commonBuiltins.includes(moduleId);
    }
  }

  private isSafeModule(moduleId: string): boolean {
    // 预定义的安全第三方模块列表
    const safeModules = [
      'lodash', 'ramda', 'moment', 'dayjs', 'uuid',
      'chalk', 'debug', '@swc/core', 'typescript'
    ];
    
    return safeModules.some(safe => 
      moduleId === safe || moduleId.startsWith(`${safe}/`)
    );
  }

  private logSecurityEvent(
    type: string, 
    resource: string, 
    allowed: boolean, 
    reason: string
  ): void {
    this.auditLog.push({
      type,
      resource,
      allowed,
      timestamp: Date.now(),
      reason
    });
  }
}

/**
 * 安全审计日志条目
 */
export interface SecurityAuditEntry {
  readonly type: string;
  readonly resource: string;
  readonly allowed: boolean;
  readonly timestamp: number;
  readonly operation?: string;
  readonly reason?: string;
}

/**
 * 安全插件沙箱实现
 * 
 * 基于现有 PluginSandboxImpl 的增强版本，提供：
 * - 类型安全的配置验证
 * - 增强的权限检查
 * - 详细的安全审计
 */
export class SecurePluginSandbox extends EventEmitter implements PluginSandbox {
  readonly pluginId: string;
  readonly permissions: any; // 保持兼容性
  readonly context: any;     // 保持兼容性

  private readonly options: TypeSafeSandboxOptions;
  private readonly securityProxy: TypeSafeSecurityProxy;
  private vmContext: vm.Context | null = null;
  private isDestroyed = false;
  private executionCount = 0;
  private memoryUsage = 0;
  private startTime = 0;

  constructor(pluginId: string, options: TypeSafeSandboxOptions) {
    super();
    
    this.pluginId = pluginId;
    this.options = this.validateAndNormalizeOptions(options);
    this.securityProxy = new TypeSafeSecurityProxy(this.options);
    
    // 兼容性属性
    this.permissions = this.convertToLegacyPermissions(this.options);
    this.context = this.createLegacyContext();
    
    this.createSecureVMContext();
  }

  allocateMemory(size: number): boolean {
    if (this.isDestroyed) {
      return false;
    }

    const currentUsage = this.getMemoryUsage();
    const canAllocate = currentUsage + size <= this.options.memory.limit;
    
    if (canAllocate) {
      this.memoryUsage += size;
    }

    return canAllocate;
  }

  releaseMemory(): void {
    if (this.options.memory.enableGC && global.gc) {
      global.gc();
    }
    
    this.memoryUsage = Math.max(0, this.memoryUsage * 0.8); // 简化的内存释放模拟
  }

  getMemoryUsage(): number {
    // 简化版本：返回模拟的内存使用量
    return this.memoryUsage + (process.memoryUsage().heapUsed * 0.1);
  }

  setTimeout(timeout: number): void {
    if (timeout <= 0) {
      throw new TypeError('Timeout must be positive');
    }
    // 注意：这里应该更新选项，但由于选项是只读的，我们只能记录这次调用
    // 在实际实现中，可能需要重新设计以支持运行时超时调整
  }

  getTimeout(): number {
    return this.options.memory.timeout;
  }

  isWithinLimits(): boolean {
    if (this.isDestroyed) {
      return false;
    }

    const memoryOk = this.getMemoryUsage() <= this.options.memory.limit;
    const timeOk = this.startTime === 0 || 
                   (performance.now() - this.startTime) <= this.options.memory.timeout;

    return memoryOk && timeOk;
  }

  canAccess(resource: string): boolean {
    if (this.isDestroyed) {
      return false;
    }

    const [type, ...pathParts] = resource.split(':', 2);
    const resourcePath = pathParts.join(':');

    switch (type) {
      case 'fs':
      case 'filesystem':
        return this.securityProxy.canAccessFileSystem(resourcePath);
      
      case 'network':
      case 'http':
      case 'https':
        return this.securityProxy.canAccessNetwork(resourcePath);
      
      case 'module':
      case 'require':
        return this.securityProxy.canAccessModule(resourcePath);
      
      default:
        return false;
    }
  }

  async requestPermission(permission: string): Promise<boolean> {
    this.emit('permission-requested', { 
      permission, 
      pluginId: this.pluginId,
      timestamp: Date.now()
    });
    
    // 简化版本：直接检查权限
    // 实际实现中可能需要用户交互或权限升级机制
    return this.canAccess(permission);
  }

  async execute<T>(code: string | (() => Promise<T>)): Promise<T> {
    if (this.isDestroyed) {
      throw new Error(`Sandbox for plugin '${this.pluginId}' is destroyed`);
    }

    this.startTime = performance.now();
    this.executionCount++;

    try {
      if (typeof code === 'function') {
        return await this.executeFunction(code);
      } else {
        return await this.executeCode(code);
      }
    } finally {
      this.startTime = 0;
    }
  }

  async cleanup(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    this.releaseMemory();
    
    if (this.vmContext) {
      this.vmContext = null;
    }

    this.emit('cleanup', { 
      pluginId: this.pluginId,
      executionCount: this.executionCount,
      auditLog: this.securityProxy.getAuditLog()
    });
  }

  async destroy(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    this.isDestroyed = true;
    await this.cleanup();
    this.removeAllListeners();

    this.emit('destroyed', { 
      pluginId: this.pluginId,
      finalAuditLog: this.securityProxy.getAuditLog()
    });
  }

  /**
   * 获取安全审计日志
   */
  getAuditLog(): readonly SecurityAuditEntry[] {
    return this.securityProxy.getAuditLog();
  }

  private async executeFunction<T>(fn: () => Promise<T>): Promise<T> {
    if (!this.isWithinLimits()) {
      throw new Error(`Execution limits exceeded for plugin '${this.pluginId}'`);
    }

    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Execution timeout (${this.options.memory.timeout}ms) exceeded for plugin '${this.pluginId}'`));
      }, this.options.memory.timeout);
    });

    return Promise.race([fn(), timeoutPromise]);
  }

  private async executeCode<T>(code: string): Promise<T> {
    if (!this.vmContext) {
      throw new Error(`VM context not available for plugin '${this.pluginId}'`);
    }

    if (!this.isWithinLimits()) {
      throw new Error(`Execution limits exceeded for plugin '${this.pluginId}'`);
    }

    try {
      const script = new vm.Script(code, {
        filename: `plugin-${this.pluginId}.js`,
      });

      const result = script.runInContext(this.vmContext, {
        timeout: this.options.memory.timeout,
        breakOnSigint: true,
      });

      return result;
    } catch (error) {
      throw new Error(`Code execution failed in plugin '${this.pluginId}': ${error}`);
    }
  }

  private validateAndNormalizeOptions(
    options: TypeSafeSandboxOptions
  ): TypeSafeSandboxOptions {
    // 验证内存配置
    if (options.memory.limit <= 0) {
      throw new TypeError('Memory limit must be positive');
    }

    if (options.memory.timeout <= 0) {
      throw new TypeError('Timeout must be positive');
    }

    // 验证路径配置
    const normalizedReadPaths = options.fileSystem.readPaths.map(p => 
      path.resolve(p)
    );
    const normalizedWritePaths = options.fileSystem.writePaths.map(p => 
      path.resolve(p)
    );

    // 验证网络配置
    if (options.network.enableOutbound && options.network.allowedHosts.length === 0) {
      throw new TypeError('Outbound network access enabled but no allowed hosts specified');
    }

    return {
      ...options,
      fileSystem: {
        ...options.fileSystem,
        readPaths: normalizedReadPaths,
        writePaths: normalizedWritePaths,
      }
    };
  }

  private createSecureVMContext(): void {
    if (this.vmContext) {
      return;
    }

    const secureContext = this.securityProxy.createSecureContext();
    
    const sandbox = {
      ...secureContext,
      
      // 安全的控制台
      console: {
        log: (...args: any[]) => console.log(`[Plugin:${this.pluginId}]`, ...args),
        error: (...args: any[]) => console.error(`[Plugin:${this.pluginId}]`, ...args),
        warn: (...args: any[]) => console.warn(`[Plugin:${this.pluginId}]`, ...args),
        info: (...args: any[]) => console.info(`[Plugin:${this.pluginId}]`, ...args),
        debug: (...args: any[]) => console.debug(`[Plugin:${this.pluginId}]`, ...args),
      },

      // 安全的require函数
      require: this.createSecureRequire(),

      // 安全的进程对象
      process: this.createSecureProcess(),

      // 受限的计时器
      setTimeout: (callback: Function, delay: number) => {
        const actualDelay = Math.min(delay, this.options.memory.timeout);
        return setTimeout(callback, actualDelay);
      },
      setInterval: (callback: Function, delay: number) => {
        const actualDelay = Math.max(delay, 100); // 最小间隔100ms
        return setInterval(callback, actualDelay);
      },
      clearTimeout,
      clearInterval,

      // 插件上下文
      __pluginContext: {
        id: this.pluginId,
        canAccess: (resource: string) => this.canAccess(resource),
        requestPermission: (permission: string) => this.requestPermission(permission),
        getMemoryUsage: () => this.getMemoryUsage(),
        isWithinLimits: () => this.isWithinLimits(),
      },
    };

    this.vmContext = vm.createContext(sandbox, {
      name: `secure-plugin-${this.pluginId}`,
      codeGeneration: {
        strings: this.options.permissions.allowEval,
        wasm: false, // 禁用WebAssembly
      },
    });
  }

  private createSecureRequire(): NodeRequire {
    return ((moduleId: string) => {
      if (!this.securityProxy.canAccessModule(moduleId)) {
        throw new Error(`Plugin '${this.pluginId}' is not allowed to require module '${moduleId}'`);
      }

      // 对于文件系统路径，检查文件访问权限
      if (moduleId.startsWith('./') || moduleId.startsWith('../') || path.isAbsolute(moduleId)) {
        if (!this.securityProxy.canAccessFileSystem(moduleId)) {
          throw new Error(`Plugin '${this.pluginId}' is not allowed to access file '${moduleId}'`);
        }
      }

      return require(moduleId);
    }) as NodeRequire;
  }

  private createSecureProcess(): Partial<NodeJS.Process> {
    return {
      env: { ...process.env },
      cwd: process.cwd,
      version: process.version,
      versions: { ...process.versions },
      platform: process.platform,
      arch: process.arch,
      
      // 禁用危险操作
      exit: () => {
        throw new Error('Process exit is not allowed in plugin sandbox');
      },
      kill: () => {
        throw new Error('Process kill is not allowed in plugin sandbox');
      },
    };
  }

  private convertToLegacyPermissions(options: TypeSafeSandboxOptions): any {
    // 转换为现有接口格式以保持兼容性
    return {
      fileSystem: {
        read: options.fileSystem.readPaths,
        write: options.fileSystem.writePaths,
      },
      network: {
        outbound: options.network.allowedHosts,
        inbound: false,
      },
      process: {
        spawn: false,
        signals: false,
      },
      memory: {
        limit: options.memory.limit,
        timeout: options.memory.timeout,
      },
    };
  }

  private createLegacyContext(): any {
    // 创建兼容的上下文对象
    return {
      workingDirectory: process.cwd(),
      tempDirectory: path.join(process.cwd(), '.temp', 'plugins', this.pluginId),
      environment: {
        NODE_ENV: process.env.NODE_ENV || 'development',
        PLUGIN_ID: this.pluginId,
      },
      restrictions: {
        maxExecutionTime: this.options.memory.timeout,
        maxMemoryUsage: this.options.memory.limit,
        allowedModules: [...this.options.permissions.allowModuleAccess],
        blockedModules: [],
        allowNetworkAccess: this.options.network.enableOutbound,
        allowFileSystemAccess: this.options.fileSystem.readPaths.length > 0 || 
                               this.options.fileSystem.writePaths.length > 0,
      },
    };
  }
}

/**
 * 创建类型安全沙箱的工厂函数
 */
export function createSecurePluginSandbox(
  pluginId: string,
  options: Partial<TypeSafeSandboxOptions> = {}
): SecurePluginSandbox {
  const defaultOptions: TypeSafeSandboxOptions = {
    memory: {
      limit: 128 * 1024 * 1024, // 128MB
      timeout: 30000,            // 30秒
      enableGC: true,
    },
    fileSystem: {
      readPaths: [path.join(process.cwd(), 'plugins', pluginId)],
      writePaths: [path.join(process.cwd(), '.temp', 'plugins', pluginId)],
      enableWatching: false,
    },
    network: {
      allowedHosts: [],
      enableOutbound: false,
    },
    permissions: {
      allowDynamicImport: false,
      allowEval: false,
      allowModuleAccess: ['fs', 'path', 'crypto', 'util'],
    },
  };

  const mergedOptions: TypeSafeSandboxOptions = {
    memory: { ...defaultOptions.memory, ...options.memory },
    fileSystem: { ...defaultOptions.fileSystem, ...options.fileSystem },
    network: { ...defaultOptions.network, ...options.network },
    permissions: { ...defaultOptions.permissions, ...options.permissions },
  };

  return new SecurePluginSandbox(pluginId, mergedOptions);
}