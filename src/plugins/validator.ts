/**
 * 插件验证器
 * 负责验证插件结构、配置和兼容性
 */

import type {
  Plugin,
  PluginRule,
  ValidationResult,
  ValidationError,
  ValidationWarning,
  ConfigSchema,
  CompatibilityInfo,
} from './types';

export class PluginValidator {
  private readonly ENGINE_VERSION = '1.0.0';
  private readonly MIN_NODE_VERSION = '18.0.0';

  /**
   * 验证插件对象
   */
  async validatePlugin(plugin: Plugin): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      // 1. 基本结构验证
      this.validateBasicStructure(plugin, errors);

      // 2. 规则验证
      this.validateRules(plugin.rules, errors, warnings);

      // 3. 配置Schema验证
      if (plugin.configSchema) {
        this.validateConfigSchema(plugin.configSchema, errors, warnings);
      }

      // 4. 依赖验证
      if (plugin.dependencies) {
        this.validateDependencies(plugin.dependencies, errors, warnings);
      }

      // 5. 版本兼容性验证
      if (plugin.engineVersion) {
        this.validateEngineCompatibility(plugin.engineVersion, errors, warnings);
      }

      // 6. 安全性检查
      await this.validateSecurity(plugin, errors, warnings);

    } catch (error) {
      errors.push({
        message: `Plugin validation failed: ${error}`,
        severity: 'error',
        code: 'VALIDATION_ERROR',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证插件配置
   */
  validateConfig(config: any, schema: ConfigSchema): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    try {
      this.validateConfigAgainstSchema(config, schema, '', errors, warnings);
    } catch (error) {
      errors.push({
        message: `Config validation failed: ${error}`,
        severity: 'error',
        code: 'CONFIG_VALIDATION_ERROR',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 验证插件兼容性
   */
  validateCompatibility(plugin: Plugin): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 检查引擎版本兼容性
    if (plugin.engineVersion) {
      const compatible = this.isVersionCompatible(plugin.engineVersion, this.ENGINE_VERSION);
      if (!compatible) {
        errors.push({
          message: `Plugin requires engine version ${plugin.engineVersion}, but current version is ${this.ENGINE_VERSION}`,
          severity: 'error',
          code: 'ENGINE_VERSION_MISMATCH',
        });
      }
    }

    // 检查Node.js版本
    const nodeVersion = process.version.substring(1); // 移除'v'前缀
    if (!this.isVersionCompatible(`>=${this.MIN_NODE_VERSION}`, nodeVersion)) {
      errors.push({
        message: `Plugin requires Node.js >= ${this.MIN_NODE_VERSION}, but current version is ${nodeVersion}`,
        severity: 'error',
        code: 'NODE_VERSION_MISMATCH',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private validateBasicStructure(plugin: Plugin, errors: ValidationError[]): void {
    // 必需字段验证
    if (!plugin.id || typeof plugin.id !== 'string') {
      errors.push({
        message: 'Plugin must have a valid string id',
        path: 'id',
        severity: 'error',
        code: 'MISSING_ID',
      });
    } else if (!/^[a-zA-Z0-9-_]+$/.test(plugin.id)) {
      errors.push({
        message: 'Plugin id must contain only alphanumeric characters, hyphens, and underscores',
        path: 'id',
        severity: 'error',
        code: 'INVALID_ID_FORMAT',
      });
    }

    if (!plugin.name || typeof plugin.name !== 'string') {
      errors.push({
        message: 'Plugin must have a valid string name',
        path: 'name',
        severity: 'error',
        code: 'MISSING_NAME',
      });
    }

    if (!plugin.version || typeof plugin.version !== 'string') {
      errors.push({
        message: 'Plugin must have a valid string version',
        path: 'version',
        severity: 'error',
        code: 'MISSING_VERSION',
      });
    } else if (!this.isValidVersion(plugin.version)) {
      errors.push({
        message: 'Plugin version must follow semantic versioning (e.g., 1.0.0)',
        path: 'version',
        severity: 'error',
        code: 'INVALID_VERSION_FORMAT',
      });
    }

    if (!Array.isArray(plugin.rules)) {
      errors.push({
        message: 'Plugin must have a rules array',
        path: 'rules',
        severity: 'error',
        code: 'MISSING_RULES',
      });
    }

    // 可选字段类型验证
    if (plugin.description !== undefined && typeof plugin.description !== 'string') {
      errors.push({
        message: 'Plugin description must be a string',
        path: 'description',
        severity: 'error',
        code: 'INVALID_DESCRIPTION_TYPE',
      });
    }

    if (plugin.author !== undefined && typeof plugin.author !== 'string') {
      errors.push({
        message: 'Plugin author must be a string',
        path: 'author',
        severity: 'error',
        code: 'INVALID_AUTHOR_TYPE',
      });
    }

    if (plugin.keywords !== undefined && (!Array.isArray(plugin.keywords) || !plugin.keywords.every(k => typeof k === 'string'))) {
      errors.push({
        message: 'Plugin keywords must be an array of strings',
        path: 'keywords',
        severity: 'error',
        code: 'INVALID_KEYWORDS_TYPE',
      });
    }
  }

  private validateRules(rules: PluginRule[], errors: ValidationError[], warnings: ValidationWarning[]): void {
    if (!Array.isArray(rules)) {
      errors.push({
        message: 'Rules must be an array',
        path: 'rules',
        severity: 'error',
        code: 'INVALID_RULES_TYPE',
      });
      return;
    }

    if (rules.length === 0) {
      warnings.push({
        message: 'Plugin contains no rules',
        path: 'rules',
        code: 'NO_RULES',
        suggestion: 'Consider adding at least one rule to make the plugin useful',
      });
    }

    const ruleIds = new Set<string>();

    rules.forEach((rule, index) => {
      const rulePath = `rules[${index}]`;
      
      // 基本结构验证
      if (!rule || typeof rule !== 'object') {
        errors.push({
          message: `Rule at index ${index} must be an object`,
          path: rulePath,
          severity: 'error',
          code: 'INVALID_RULE_TYPE',
        });
        return;
      }

      // ID验证
      if (!rule.id || typeof rule.id !== 'string') {
        errors.push({
          message: `Rule at index ${index} must have a valid string id`,
          path: `${rulePath}.id`,
          severity: 'error',
          code: 'MISSING_RULE_ID',
        });
      } else {
        if (ruleIds.has(rule.id)) {
          errors.push({
            message: `Duplicate rule id: ${rule.id}`,
            path: `${rulePath}.id`,
            severity: 'error',
            code: 'DUPLICATE_RULE_ID',
          });
        } else {
          ruleIds.add(rule.id);
        }
      }

      // 名称验证
      if (!rule.name || typeof rule.name !== 'string') {
        errors.push({
          message: `Rule '${rule.id}' must have a valid string name`,
          path: `${rulePath}.name`,
          severity: 'error',
          code: 'MISSING_RULE_NAME',
        });
      }

      // 优先级验证
      if (typeof rule.priority !== 'number') {
        errors.push({
          message: `Rule '${rule.id}' must have a numeric priority`,
          path: `${rulePath}.priority`,
          severity: 'error',
          code: 'MISSING_RULE_PRIORITY',
        });
      } else if (rule.priority < 0 || rule.priority > 1000) {
        warnings.push({
          message: `Rule '${rule.id}' priority should be between 0 and 1000`,
          path: `${rulePath}.priority`,
          code: 'UNUSUAL_PRIORITY',
          suggestion: 'Consider using priority values between 0-1000 for better compatibility',
        });
      }

      // 方法验证
      if (typeof rule.evaluate !== 'function') {
        errors.push({
          message: `Rule '${rule.id}' must have an evaluate function`,
          path: `${rulePath}.evaluate`,
          severity: 'error',
          code: 'MISSING_EVALUATE_FUNCTION',
        });
      }

      if (typeof rule.canHandle !== 'function') {
        errors.push({
          message: `Rule '${rule.id}' must have a canHandle function`,
          path: `${rulePath}.canHandle`,
          severity: 'error',
          code: 'MISSING_CANHANDLE_FUNCTION',
        });
      }

      if (typeof rule.getDependencies !== 'function') {
        errors.push({
          message: `Rule '${rule.id}' must have a getDependencies function`,
          path: `${rulePath}.getDependencies`,
          severity: 'error',
          code: 'MISSING_GETDEPENDENCIES_FUNCTION',
        });
      }

      // 可选字段验证
      if (rule.category !== undefined && typeof rule.category !== 'string') {
        errors.push({
          message: `Rule '${rule.id}' category must be a string`,
          path: `${rulePath}.category`,
          severity: 'error',
          code: 'INVALID_RULE_CATEGORY_TYPE',
        });
      }

      if (rule.tags !== undefined && (!Array.isArray(rule.tags) || !rule.tags.every(t => typeof t === 'string'))) {
        errors.push({
          message: `Rule '${rule.id}' tags must be an array of strings`,
          path: `${rulePath}.tags`,
          severity: 'error',
          code: 'INVALID_RULE_TAGS_TYPE',
        });
      }
    });
  }

  private validateConfigSchema(schema: ConfigSchema, errors: ValidationError[], warnings: ValidationWarning[]): void {
    if (!schema || typeof schema !== 'object') {
      errors.push({
        message: 'Config schema must be an object',
        path: 'configSchema',
        severity: 'error',
        code: 'INVALID_CONFIG_SCHEMA_TYPE',
      });
      return;
    }

    if (schema.type !== 'object') {
      errors.push({
        message: 'Config schema root type must be "object"',
        path: 'configSchema.type',
        severity: 'error',
        code: 'INVALID_CONFIG_SCHEMA_ROOT_TYPE',
      });
    }

    if (!schema.properties || typeof schema.properties !== 'object') {
      warnings.push({
        message: 'Config schema has no properties defined',
        path: 'configSchema.properties',
        code: 'NO_CONFIG_PROPERTIES',
        suggestion: 'Define properties to provide configuration options',
      });
    } else {
      this.validateConfigProperties(schema.properties, 'configSchema.properties', errors, warnings);
    }
  }

  private validateConfigProperties(
    properties: Record<string, any>,
    basePath: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    for (const [key, property] of Object.entries(properties)) {
      const propertyPath = `${basePath}.${key}`;
      
      if (!property || typeof property !== 'object') {
        errors.push({
          message: `Config property '${key}' must be an object`,
          path: propertyPath,
          severity: 'error',
          code: 'INVALID_CONFIG_PROPERTY_TYPE',
        });
        continue;
      }

      const validTypes = ['string', 'number', 'boolean', 'array', 'object'];
      if (!property.type || !validTypes.includes(property.type)) {
        errors.push({
          message: `Config property '${key}' must have a valid type (${validTypes.join(', ')})`,
          path: `${propertyPath}.type`,
          severity: 'error',
          code: 'INVALID_CONFIG_PROPERTY_TYPE_VALUE',
        });
      }

      if (property.description !== undefined && typeof property.description !== 'string') {
        warnings.push({
          message: `Config property '${key}' description should be a string`,
          path: `${propertyPath}.description`,
          code: 'INVALID_CONFIG_PROPERTY_DESCRIPTION',
          suggestion: 'Provide a string description for better documentation',
        });
      }
    }
  }

  private validateDependencies(dependencies: string[], errors: ValidationError[], warnings: ValidationWarning[]): void {
    if (!Array.isArray(dependencies)) {
      errors.push({
        message: 'Dependencies must be an array',
        path: 'dependencies',
        severity: 'error',
        code: 'INVALID_DEPENDENCIES_TYPE',
      });
      return;
    }

    dependencies.forEach((dep, index) => {
      if (typeof dep !== 'string') {
        errors.push({
          message: `Dependency at index ${index} must be a string`,
          path: `dependencies[${index}]`,
          severity: 'error',
          code: 'INVALID_DEPENDENCY_TYPE',
        });
      } else if (!this.isValidDependencyFormat(dep)) {
        errors.push({
          message: `Invalid dependency format: ${dep}`,
          path: `dependencies[${index}]`,
          severity: 'error',
          code: 'INVALID_DEPENDENCY_FORMAT',
        });
      }
    });

    // 检查重复依赖
    const uniqueDeps = new Set(dependencies);
    if (uniqueDeps.size !== dependencies.length) {
      warnings.push({
        message: 'Duplicate dependencies detected',
        path: 'dependencies',
        code: 'DUPLICATE_DEPENDENCIES',
        suggestion: 'Remove duplicate dependencies to clean up the plugin manifest',
      });
    }
  }

  private validateEngineCompatibility(engineVersion: string, errors: ValidationError[], warnings: ValidationWarning[]): void {
    if (!this.isValidVersionRange(engineVersion)) {
      errors.push({
        message: `Invalid engine version format: ${engineVersion}`,
        path: 'engineVersion',
        severity: 'error',
        code: 'INVALID_ENGINE_VERSION_FORMAT',
      });
      return;
    }

    if (!this.isVersionCompatible(engineVersion, this.ENGINE_VERSION)) {
      errors.push({
        message: `Plugin requires engine version ${engineVersion}, but current version is ${this.ENGINE_VERSION}`,
        path: 'engineVersion',
        severity: 'error',
        code: 'ENGINE_VERSION_INCOMPATIBLE',
      });
    }
  }

  private async validateSecurity(plugin: Plugin, errors: ValidationError[], warnings: ValidationWarning[]): Promise<void> {
    // 检查可疑的规则名称或描述
    const suspiciousPatterns = [
      /eval\s*\(/i,
      /Function\s*\(/i,
      /child_process/i,
      /spawn/i,
      /exec/i,
      /file\s*system/i,
      /network\s*request/i,
    ];

    for (const rule of plugin.rules) {
      const ruleText = `${rule.name} ${rule.id}`.toLowerCase();
      
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(ruleText)) {
          warnings.push({
            message: `Rule '${rule.id}' contains potentially suspicious content`,
            path: `rules.${rule.id}`,
            code: 'SUSPICIOUS_RULE_CONTENT',
            suggestion: 'Review this rule for security implications',
          });
        }
      }
    }

    // 检查插件描述中的可疑内容
    if (plugin.description) {
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(plugin.description)) {
          warnings.push({
            message: 'Plugin description contains potentially suspicious content',
            path: 'description',
            code: 'SUSPICIOUS_DESCRIPTION',
            suggestion: 'Review the plugin description for security implications',
          });
        }
      }
    }
  }

  private validateConfigAgainstSchema(
    config: any,
    schema: ConfigSchema,
    path: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    if (schema.type === 'object' && schema.properties) {
      if (typeof config !== 'object' || config === null) {
        errors.push({
          message: `Expected object at ${path || 'root'}`,
          path,
          severity: 'error',
          code: 'TYPE_MISMATCH',
        });
        return;
      }

      // 检查必需属性
      if (schema.required) {
        for (const requiredProp of schema.required) {
          if (!(requiredProp in config)) {
            errors.push({
              message: `Missing required property: ${requiredProp}`,
              path: path ? `${path}.${requiredProp}` : requiredProp,
              severity: 'error',
              code: 'MISSING_REQUIRED_PROPERTY',
            });
          }
        }
      }

      // 验证属性
      for (const [propName, propValue] of Object.entries(config)) {
        const propSchema = schema.properties[propName];
        const propPath = path ? `${path}.${propName}` : propName;

        if (!propSchema) {
          if (schema.additionalProperties === false) {
            errors.push({
              message: `Unknown property: ${propName}`,
              path: propPath,
              severity: 'error',
              code: 'UNKNOWN_PROPERTY',
            });
          }
          continue;
        }

        this.validateConfigValue(propValue, propSchema, propPath, errors, warnings);
      }
    }
  }

  private validateConfigValue(
    value: any,
    schema: any,
    path: string,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ): void {
    // 类型检查
    const actualType = Array.isArray(value) ? 'array' : typeof value;
    if (actualType !== schema.type) {
      errors.push({
        message: `Expected ${schema.type} but got ${actualType} at ${path}`,
        path,
        severity: 'error',
        code: 'TYPE_MISMATCH',
      });
      return;
    }

    // 枚举值检查
    if (schema.enum && !schema.enum.includes(value)) {
      errors.push({
        message: `Value must be one of: ${schema.enum.join(', ')}`,
        path,
        severity: 'error',
        code: 'ENUM_MISMATCH',
      });
    }

    // 数值范围检查
    if (schema.type === 'number') {
      if (schema.minimum !== undefined && value < schema.minimum) {
        errors.push({
          message: `Value must be >= ${schema.minimum}`,
          path,
          severity: 'error',
          code: 'VALUE_TOO_SMALL',
        });
      }
      if (schema.maximum !== undefined && value > schema.maximum) {
        errors.push({
          message: `Value must be <= ${schema.maximum}`,
          path,
          severity: 'error',
          code: 'VALUE_TOO_LARGE',
        });
      }
    }

    // 数组项验证
    if (schema.type === 'array' && schema.items && Array.isArray(value)) {
      value.forEach((item, index) => {
        this.validateConfigValue(item, schema.items, `${path}[${index}]`, errors, warnings);
      });
    }

    // 对象属性验证
    if (schema.type === 'object' && schema.properties) {
      this.validateConfigAgainstSchema(value, schema as ConfigSchema, path, errors, warnings);
    }
  }

  private isValidVersion(version: string): boolean {
    // 简化的语义版本验证
    return /^\d+\.\d+\.\d+(-[\w.-]+)?(\+[\w.-]+)?$/.test(version);
  }

  private isValidVersionRange(versionRange: string): boolean {
    // 简化的版本范围验证
    return /^[\d\.\*\>\=\<\^\~\-\s\|]+$/.test(versionRange);
  }

  private isValidDependencyFormat(dependency: string): boolean {
    // 简化的依赖格式验证（支持 name@version 或 name）
    return /^[a-zA-Z0-9@\.\-_\/]+$/.test(dependency);
  }

  private isVersionCompatible(required: string, actual: string): boolean {
    // 简化的版本兼容性检查
    // 在实际实现中应使用 semver 库
    if (required.startsWith('>=')) {
      const minVersion = required.substring(2);
      return this.compareVersions(actual, minVersion) >= 0;
    }
    
    if (required.startsWith('^')) {
      const baseVersion = required.substring(1);
      const [actualMajor] = actual.split('.');
      const [baseMajor] = baseVersion.split('.');
      return actualMajor === baseMajor && this.compareVersions(actual, baseVersion) >= 0;
    }
    
    return required === actual;
  }

  private compareVersions(v1: string, v2: string): number {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const part1 = parts1[i] || 0;
      const part2 = parts2[i] || 0;
      
      if (part1 > part2) return 1;
      if (part1 < part2) return -1;
    }
    
    return 0;
  }
}