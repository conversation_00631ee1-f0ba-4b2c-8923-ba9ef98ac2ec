/**
 * 插件管理器核心实现
 * 支持动态加载、热更新、依赖管理和安全沙箱的现代插件系统
 */

import { EventEmitter } from 'events';
import * as fs from 'fs';
import * as path from 'path';
import * as crypto from 'crypto';
import type {
  Plugin,
  PluginRule,
  PluginManager,
  LoadedPlugin,
  PluginSource,
  PluginStatus,
  PluginMetadata,
  PluginStatistics,
  PluginHealth,
  PluginSandbox,
  ValidationResult,
  DependencyValidationResult,
  PluginEvent,
  PluginEventData,
  PluginEventHandler,
  PluginConfigUpdateOptions,
  HealthIssue,
  VersionConflict,
} from './types';
import { PluginError } from './types';
import type { AsyncRuleEngine } from '../engine/types';
import { PluginSandboxImpl } from './sandbox';
import { PluginValidator } from './validator';
import { DependencyResolver } from './dependency-resolver';

/**
 * 内部可变的已加载插件接口
 */
interface InternalLoadedPlugin extends Omit<LoadedPlugin, 'status' | 'config'> {
  status: PluginStatus;
  config: any;
}

/**
 * 插件管理器实现 - 使用组合模式集成EventEmitter
 */
export class PluginManagerImpl implements PluginManager {
  private eventEmitter: EventEmitter;
  private plugins = new Map<string, InternalLoadedPlugin>();
  private pluginWatcher?: fs.FSWatcher;
  private watchedPaths = new Set<string>();
  private validator: PluginValidator;
  private dependencyResolver: DependencyResolver;
  private engine: AsyncRuleEngine;
  private isWatchingEnabled = false;

  constructor(engine: AsyncRuleEngine) {
    this.eventEmitter = new EventEmitter();
    this.engine = engine;
    this.validator = new PluginValidator();
    this.dependencyResolver = new DependencyResolver();
    this.setupErrorHandling();
  }

  async loadPlugin(source: PluginSource): Promise<LoadedPlugin> {
    const startTime = performance.now();
    
    try {
      this.emit('plugin:loading', { 
        pluginId: 'unknown', 
        timestamp: Date.now() 
      } as PluginEventData);

      // 1. 解析插件源
      const pluginData = await this.resolvePluginSource(source);
      const plugin = pluginData.plugin;
      
      // 2. 验证插件
      const validationResult = await this.validator.validatePlugin(plugin);
      if (!validationResult.isValid) {
        throw this.createTypedPluginError(
          `Plugin validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`,
          plugin.id,
          'validation',
          false,
          { validationErrors: validationResult.errors }
        );
      }

      // 3. 检查是否已加载
      if (this.plugins.has(plugin.id)) {
        throw this.createTypedPluginError(
          `Plugin '${plugin.id}' is already loaded`,
          plugin.id,
          'loading'
        );
      }

      // 4. 验证依赖
      const dependencyValidation = this.dependencyResolver.validateDependencies(
        plugin,
        Array.from(this.plugins.values()).map(p => p.plugin)
      );
      if (!dependencyValidation.isValid) {
        throw this.createTypedPluginError(
          `Dependency validation failed: ${dependencyValidation.errors.map(e => e.message).join(', ')}`,
          plugin.id,
          'dependency',
          false,
          { dependencyErrors: dependencyValidation.errors }
        );
      }

      // 5. 创建沙箱
      const sandbox = this.createSandbox(plugin.id);

      // 6. 创建插件元数据
      const metadata = await this.createPluginMetadata(source, pluginData, startTime);

      // 7. 初始化插件配置
      const config = this.initializePluginConfig(plugin);

      // 8. 创建加载的插件对象
      const loadedPlugin: InternalLoadedPlugin = {
        plugin,
        metadata,
        status: 'loaded',
        loadTime: Date.now(),
        rules: new Map(),
        config,
        sandbox,
        dependencies: plugin.dependencies || [],
        stats: this.initializePluginStats(),
        health: this.initializePluginHealth(),
      };

      // 9. 注册插件规则
      await this.registerPluginRules(loadedPlugin);

      // 10. 调用插件的加载钩子
      if (typeof plugin.onLoad === 'function') {
        await this.executeSandboxFunction(sandbox, async () => {
          await plugin.onLoad!(this.engine, config);
        });
      }

      // 11. 存储插件
      this.plugins.set(plugin.id, loadedPlugin);
      loadedPlugin.status = 'active';

      // 12. 设置监听（如果启用）
      if (this.isWatchingEnabled) {
        await this.watchPlugin(loadedPlugin);
      }

      // 13. 发出事件
      this.emit('plugin:loaded', {
        pluginId: plugin.id,
        plugin,
        timestamp: Date.now(),
      } as PluginEventData);

      console.log(`Plugin '${plugin.id}' loaded successfully with ${plugin.rules.length} rules`);
      return this.toPublicLoadedPlugin(loadedPlugin);

    } catch (error) {
      const pluginError = this.createTypedPluginError(
        error instanceof Error ? error.message : 'Unknown plugin loading error',
        this.extractPluginId(source),
        'loading',
        false,
        error
      );
      
      this.emit('plugin:error', {
        pluginId: pluginError.pluginId,
        error: pluginError,
        timestamp: Date.now(),
      } as PluginEventData);

      throw pluginError;
    }
  }

  async unloadPlugin(pluginId: string): Promise<void> {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      throw this.createTypedPluginError(
        `Plugin '${pluginId}' is not loaded`,
        pluginId, 
        'loading'
      );
    }

    try {
      this.emit('plugin:unloading', {
        pluginId,
        plugin: loadedPlugin.plugin,
        timestamp: Date.now(),
      } as PluginEventData);

      loadedPlugin.status = 'unloading';

      // 1. 停止监听
      await this.unwatchPlugin(loadedPlugin);

      // 2. 调用插件的卸载钩子
      if (typeof loadedPlugin.plugin.onUnload === 'function') {
        try {
          await this.executeSandboxFunction(loadedPlugin.sandbox, async () => {
            await loadedPlugin.plugin.onUnload!(this.engine);
          });
        } catch (error) {
          console.warn(`Error calling onUnload for plugin '${pluginId}':`, error);
        }
      }

      // 3. 卸载插件规则
      await this.unregisterPluginRules(loadedPlugin);

      // 4. 清理沙箱
      if (loadedPlugin.sandbox) {
        await loadedPlugin.sandbox.cleanup();
        await loadedPlugin.sandbox.destroy();
      }

      // 5. 移除插件
      this.plugins.delete(pluginId);

      // 6. 发出事件
      this.emit('plugin:unloaded', {
        pluginId,
        plugin: loadedPlugin.plugin,
        timestamp: Date.now(),
      } as PluginEventData);

      console.log(`Plugin '${pluginId}' unloaded successfully`);

    } catch (error) {
      loadedPlugin.status = 'error';
      
      const pluginError = this.createTypedPluginError(
        `Failed to unload plugin '${pluginId}': ${error}`,
        pluginId,
        'loading',
        false,
        error
      );
      
      this.emit('plugin:error', {
        pluginId,
        error: pluginError,
        timestamp: Date.now(),
      } as PluginEventData);

      throw pluginError;
    }
  }

  async reloadPlugin(pluginId: string): Promise<void> {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      throw this.createTypedPluginError(
        `Plugin '${pluginId}' is not loaded`,
        pluginId,
        'loading'
      );
    }

    const source = loadedPlugin.metadata.source;
    
    try {
      // 先卸载
      await this.unloadPlugin(pluginId);
      
      // 再加载
      await this.loadPlugin(source);
      
      console.log(`Plugin '${pluginId}' reloaded successfully`);
    } catch (error) {
      throw this.createTypedPluginError(
        `Failed to reload plugin '${pluginId}': ${error}`,
        pluginId,
        'loading',
        true,
        error
      );
    }
  }

  getPlugin(pluginId: string): LoadedPlugin | null {
    const plugin = this.plugins.get(pluginId);
    return plugin ? this.toPublicLoadedPlugin(plugin) : null;
  }

  getAllPlugins(): LoadedPlugin[] {
    return Array.from(this.plugins.values()).map(p => this.toPublicLoadedPlugin(p));
  }

  getActivePlugins(): LoadedPlugin[] {
    return this.getAllPlugins().filter(plugin => plugin.status === 'active');
  }

  getInactivePlugins(): LoadedPlugin[] {
    return this.getAllPlugins().filter(plugin => plugin.status === 'inactive');
  }

  async enablePlugin(pluginId: string): Promise<void> {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      throw this.createTypedPluginError(
        `Plugin '${pluginId}' is not loaded`,
        pluginId,
        'loading'
      );
    }

    if (loadedPlugin.status === 'active') {
      return; // 已经启用
    }

    try {
      this.emit('plugin:activating', {
        pluginId,
        plugin: loadedPlugin.plugin,
        timestamp: Date.now(),
      } as PluginEventData);

      // 重新注册规则
      await this.registerPluginRules(loadedPlugin);
      
      loadedPlugin.status = 'active';

      this.emit('plugin:activated', {
        pluginId,
        plugin: loadedPlugin.plugin,
        timestamp: Date.now(),
      } as PluginEventData);

      console.log(`Plugin '${pluginId}' enabled`);
    } catch (error) {
      loadedPlugin.status = 'error';
      throw this.createTypedPluginError(
        `Failed to enable plugin '${pluginId}': ${error}`,
        pluginId,
        'execution',
        true,
        error
      );
    }
  }

  async disablePlugin(pluginId: string): Promise<void> {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      throw this.createTypedPluginError(
        `Plugin '${pluginId}' is not loaded`,
        pluginId,
        'loading'
      );
    }

    if (loadedPlugin.status === 'inactive') {
      return; // 已经禁用
    }

    try {
      this.emit('plugin:deactivating', {
        pluginId,
        plugin: loadedPlugin.plugin,
        timestamp: Date.now(),
      } as PluginEventData);

      // 卸载规则
      await this.unregisterPluginRules(loadedPlugin);
      
      loadedPlugin.status = 'inactive';

      this.emit('plugin:deactivated', {
        pluginId,
        plugin: loadedPlugin.plugin,
        timestamp: Date.now(),
      } as PluginEventData);

      console.log(`Plugin '${pluginId}' disabled`);
    } catch (error) {
      throw this.createTypedPluginError(
        `Failed to disable plugin '${pluginId}': ${error}`,
        pluginId,
        'execution',
        true,
        error
      );
    }
  }

  resolveDependencies(pluginId: string): string[] {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      return [];
    }

    return this.dependencyResolver.resolveDependencies(
      loadedPlugin.plugin,
      Array.from(this.plugins.values()).map(p => p.plugin)
    );
  }

  validateDependencies(pluginId: string): DependencyValidationResult {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      return {
        isValid: false,
        errors: [{ message: `Plugin '${pluginId}' not found`, severity: 'error' }],
        warnings: [],
        missingDependencies: [],
        versionConflicts: [],
        circularDependencies: [],
      };
    }

    return this.dependencyResolver.validateDependencies(
      loadedPlugin.plugin,
      Array.from(this.plugins.values()).map(p => p.plugin)
    );
  }

  async updatePluginConfig(
    pluginId: string, 
    config: any, 
    options: PluginConfigUpdateOptions = {}
  ): Promise<void> {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      throw this.createTypedPluginError(
        `Plugin '${pluginId}' is not loaded`,
        pluginId,
        'loading'
      );
    }

    const oldConfig = { ...loadedPlugin.config };

    try {
      // 1. 验证配置Schema（如果启用）
      if (options.validateSchema !== false && loadedPlugin.plugin.configSchema) {
        const validationResult = this.validator.validateConfig(config, loadedPlugin.plugin.configSchema);
        if (!validationResult.isValid) {
          throw this.createTypedPluginError(
            `Config validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`,
            pluginId,
            'configuration',
            false,
            { validationErrors: validationResult.errors }
          );
        }
      }

      // 2. 备份旧配置（如果启用）
      if (options.backup) {
        await this.backupPluginConfig(pluginId, oldConfig);
      }

      // 3. 更新配置
      loadedPlugin.config = { ...loadedPlugin.config, ...config };

      // 4. 通知插件配置变更（如果启用）
      if (options.notifyPlugin !== false && typeof loadedPlugin.plugin.onConfigChange === 'function') {
        await this.executeSandboxFunction(loadedPlugin.sandbox, async () => {
          await loadedPlugin.plugin.onConfigChange!(config, oldConfig);
        });
      }

      // 5. 重启插件（如果启用）
      if (options.restartPlugin) {
        await this.reloadPlugin(pluginId);
      }

      // 6. 发出事件
      this.emit('plugin:config-changed', {
        pluginId,
        plugin: loadedPlugin.plugin,
        config,
        timestamp: Date.now(),
      } as PluginEventData);

      console.log(`Plugin '${pluginId}' configuration updated`);

    } catch (error) {
      // 恢复旧配置
      loadedPlugin.config = oldConfig;
      
      throw this.createTypedPluginError(
        `Failed to update config for plugin '${pluginId}': ${error}`,
        pluginId,
        'configuration',
        true,
        error
      );
    }
  }

  getPluginConfig(pluginId: string): any {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      throw this.createTypedPluginError(
        `Plugin '${pluginId}' is not loaded`,
        pluginId,
        'loading'
      );
    }

    return { ...loadedPlugin.config };
  }

  getPluginStatistics(): PluginStatistics {
    const plugins = this.getAllPlugins();
    const byStatus: Record<PluginStatus, number> = {
      loading: 0,
      loaded: 0,
      active: 0,
      inactive: 0,
      error: 0,
      unloading: 0,
    };
    
    const byCategory: Record<string, number> = {};
    let totalRules = 0;
    let totalMemory = 0;
    let totalExecutionTime = 0;
    let totalErrors = 0;
    let totalRuleExecutions = 0;

    for (const loadedPlugin of plugins) {
      // 按状态统计
      byStatus[loadedPlugin.status]++;

      // 按类别统计
      for (const rule of loadedPlugin.plugin.rules) {
        const category = rule.category || 'uncategorized';
        byCategory[category] = (byCategory[category] || 0) + 1;
      }

      // 累计统计
      totalRules += loadedPlugin.plugin.rules.length;
      totalMemory += loadedPlugin.stats.memoryUsage;
      totalExecutionTime += loadedPlugin.stats.executionTime;
      totalErrors += loadedPlugin.stats.errorCount;
      totalRuleExecutions += loadedPlugin.stats.ruleExecutions;
    }

    return {
      totalPlugins: plugins.length,
      activePlugins: byStatus.active,
      loadedRules: totalRules,
      memoryUsage: totalMemory,
      executionTime: totalExecutionTime,
      errorCount: totalErrors,
      ruleExecutions: totalRuleExecutions,
      byStatus,
      byCategory,
    };
  }

  getPluginHealth(pluginId: string): PluginHealth {
    const loadedPlugin = this.plugins.get(pluginId);
    if (!loadedPlugin) {
      return {
        status: 'unknown',
        lastCheck: 0,
        issues: [],
        recommendations: [],
        metrics: {
          memoryLeaks: false,
          errorRate: 0,
          responseTime: 0,
          cacheHitRate: 0,
        },
      };
    }

    return { ...loadedPlugin.health };
  }

  watchPlugins(enabled: boolean): void {
    this.isWatchingEnabled = enabled;
    
    if (enabled) {
      // 为所有已加载插件启用监听
      for (const loadedPlugin of Array.from(this.plugins.values())) {
        this.watchPlugin(loadedPlugin).catch(error => {
          console.warn(`Failed to watch plugin '${loadedPlugin.plugin.id}':`, error);
        });
      }
    } else {
      // 停止所有监听
      if (this.pluginWatcher) {
        this.pluginWatcher.close();
        this.pluginWatcher = undefined;
      }
      this.watchedPaths.clear();
    }
  }

  isWatching(): boolean {
    return this.isWatchingEnabled;
  }

  validatePlugin(plugin: Plugin): ValidationResult {
    // 由于接口期望同步返回，我们需要使用一个同步包装
    // 在实际实现中，我们在调用点处理异步逻辑
    const result = this.validator.validatePlugin(plugin);
    if (result instanceof Promise) {
      // 如果是Promise，我们返回一个临时结果，实际验证将在异步上下文中处理
      return {
        isValid: true, // 临时假设有效，实际验证在异步调用中处理
        errors: [],
        warnings: [],
      };
    }
    return result;
  }

  createSandbox(pluginId: string): PluginSandbox {
    return new PluginSandboxImpl(pluginId, {
      fileSystem: {
        read: ['**/*.js', '**/*.ts', '**/*.json'],
        write: [`plugins/${pluginId}/**/*`],
      },
      network: {
        outbound: [],
        inbound: false,
      },
      process: {
        spawn: false,
        signals: false,
      },
      memory: {
        limit: 50 * 1024 * 1024, // 50MB
        timeout: 5000, // 5秒
      },
    });
  }

  // 事件系统方法 - 委托给内部EventEmitter
  on(event: PluginEvent, handler: PluginEventHandler): void {
    this.eventEmitter.on(event, handler);
  }

  off(event: PluginEvent, handler: PluginEventHandler): void {
    this.eventEmitter.off(event, handler);
  }

  emit(event: PluginEvent, data: any): boolean {
    return this.eventEmitter.emit(event, data);
  }

  // 私有方法

  /**
   * 执行沙箱函数
   */
  private async executeSandboxFunction(
    sandbox: PluginSandbox | undefined,
    fn: () => Promise<void>
  ): Promise<void> {
    if (sandbox && typeof (sandbox as any).execute === 'function') {
      await (sandbox as any).execute(fn);
    } else {
      await fn();
    }
  }

  /**
   * 将内部LoadedPlugin转换为公共接口
   */
  private toPublicLoadedPlugin(internal: InternalLoadedPlugin): LoadedPlugin {
    return {
      plugin: internal.plugin,
      metadata: internal.metadata,
      status: internal.status,
      loadTime: internal.loadTime,
      rules: internal.rules,
      config: internal.config,
      sandbox: internal.sandbox,
      dependencies: internal.dependencies,
      stats: internal.stats,
      health: internal.health,
    };
  }

  /**
   * 创建类型化的插件错误
   */
  private createTypedPluginError(
    message: string,
    pluginId: string,
    errorType: 'loading' | 'validation' | 'dependency' | 'execution' | 'configuration',
    isRecoverable: boolean = false,
    context?: any
  ): PluginError {
    return new PluginError(message, pluginId, errorType, isRecoverable, context);
  }

  /**
   * 从插件源中提取插件ID
   */
  private extractPluginId(source: PluginSource): string {
    switch (source.type) {
      case 'file':
        // 尝试从文件路径中提取插件名
        return path.basename(source.path, path.extname(source.path));
      case 'package':
        return source.name;
      case 'url':
        // 尝试从URL中提取插件名
        try {
          const url = new URL(source.url);
          return path.basename(url.pathname, path.extname(url.pathname)) || 'unknown';
        } catch {
          return 'url-plugin';
        }
      case 'inline':
        return source.plugin.id;
      default:
        return 'unknown';
    }
  }

  private async resolvePluginSource(source: PluginSource): Promise<{ plugin: Plugin; data?: Buffer }> {
    switch (source.type) {
      case 'file':
        return this.loadPluginFromFile(source.path);
      case 'package':
        return this.loadPluginFromPackage(source.name, source.version);
      case 'url':
        return this.loadPluginFromUrl(source.url);
      case 'inline':
        return { plugin: source.plugin };
      default:
        throw this.createTypedPluginError(
          `Unknown plugin source type: ${(source as any).type}`,
          'unknown',
          'loading'
        );
    }
  }

  private async loadPluginFromFile(filePath: string): Promise<{ plugin: Plugin; data: Buffer }> {
    if (!fs.existsSync(filePath)) {
      throw this.createTypedPluginError(
        `Plugin file not found: ${filePath}`,
        'unknown',
        'loading'
      );
    }

    const data = fs.readFileSync(filePath);
    const absolutePath = path.resolve(filePath);
    
    // 清除require缓存
    delete require.cache[absolutePath];
    
    try {
      const pluginModule = require(absolutePath);
      const plugin = pluginModule.default || pluginModule;
      
      if (!this.isValidPluginObject(plugin)) {
        throw this.createTypedPluginError(
          `Invalid plugin object in file: ${filePath}`,
          plugin?.id || 'unknown',
          'validation'
        );
      }

      return { plugin, data };
    } catch (error) {
      throw this.createTypedPluginError(
        `Failed to load plugin from file '${filePath}': ${error}`,
        'unknown',
        'loading',
        false,
        error
      );
    }
  }

  private async loadPluginFromPackage(name: string, version?: string): Promise<{ plugin: Plugin; data: Buffer }> {
    try {
      const packagePath = require.resolve(name, { paths: [process.cwd()] });
      return this.loadPluginFromFile(packagePath);
    } catch (error) {
      throw this.createTypedPluginError(
        `Failed to load plugin package '${name}${version ? `@${version}` : ''}': ${error}`,
        name,
        'loading',
        false,
        error
      );
    }
  }

  private async loadPluginFromUrl(url: string): Promise<{ plugin: Plugin; data: Buffer }> {
    // 简化实现，实际应使用HTTP客户端
    throw this.createTypedPluginError(
      `URL-based plugin loading not implemented: ${url}`,
      'unknown',
      'loading'
    );
  }

  private isValidPluginObject(plugin: any): plugin is Plugin {
    return (
      plugin &&
      typeof plugin.id === 'string' &&
      typeof plugin.name === 'string' &&
      typeof plugin.version === 'string' &&
      Array.isArray(plugin.rules) &&
      plugin.rules.every((rule: any) => 
        rule &&
        typeof rule.id === 'string' &&
        typeof rule.evaluate === 'function' &&
        typeof rule.canHandle === 'function'
      )
    );
  }

  private async createPluginMetadata(
    source: PluginSource,
    pluginData: { plugin: Plugin; data?: Buffer },
    startTime: number
  ): Promise<PluginMetadata> {
    const data = pluginData.data || Buffer.from(JSON.stringify(pluginData.plugin));
    
    return {
      source,
      loadPath: source.type === 'file' ? source.path : `${source.type}:${JSON.stringify(source)}`,
      checksum: crypto.createHash('sha256').update(data).digest('hex'),
      size: data.length,
      lastModified: Date.now(),
      loadDuration: performance.now() - startTime,
      memoryUsage: process.memoryUsage().heapUsed,
      apiVersion: '1.0.0',
    };
  }

  private initializePluginConfig(plugin: Plugin): any {
    let config = {};

    // 使用插件的默认配置
    if (typeof plugin.getConfigDefaults === 'function') {
      config = { ...config, ...plugin.getConfigDefaults() };
    }

    return config;
  }

  private initializePluginStats(): PluginStatistics {
    return {
      totalPlugins: 0,
      activePlugins: 0,
      loadedRules: 0,
      memoryUsage: 0,
      executionTime: 0,
      errorCount: 0,
      ruleExecutions: 0,
      byStatus: {
        loading: 0,
        loaded: 0,
        active: 0,
        inactive: 0,
        error: 0,
        unloading: 0,
      },
      byCategory: {},
    };
  }

  private initializePluginHealth(): PluginHealth {
    return {
      status: 'healthy',
      lastCheck: Date.now(),
      issues: [],
      recommendations: [],
      metrics: {
        memoryLeaks: false,
        errorRate: 0,
        responseTime: 0,
        cacheHitRate: 0,
      },
    };
  }

  private async registerPluginRules(loadedPlugin: InternalLoadedPlugin): Promise<void> {
    for (const rule of loadedPlugin.plugin.rules) {
      try {
        // 添加插件ID到规则
        const pluginRule: PluginRule = {
          ...rule,
          pluginId: loadedPlugin.plugin.id,
        };

        // 注册到引擎
        this.engine.registerRule(pluginRule);
        
        // 存储到插件规则集合
        loadedPlugin.rules.set(rule.id, pluginRule);

        this.emit('rule:registered', {
          pluginId: loadedPlugin.plugin.id,
          ruleId: rule.id,
          rule: pluginRule,
          timestamp: Date.now(),
        } as PluginEventData);

      } catch (error) {
        console.warn(`Failed to register rule '${rule.id}' from plugin '${loadedPlugin.plugin.id}':`, error);
        
        this.emit('rule:error', {
          pluginId: loadedPlugin.plugin.id,
          ruleId: rule.id,
          rule,
          error: error as Error,
          timestamp: Date.now(),
        } as PluginEventData);
      }
    }
  }

  private async unregisterPluginRules(loadedPlugin: InternalLoadedPlugin): Promise<void> {
    for (const [ruleId, rule] of Array.from(loadedPlugin.rules.entries())) {
      try {
        this.engine.unregisterRule(ruleId);
        loadedPlugin.rules.delete(ruleId);

        this.emit('rule:unregistered', {
          pluginId: loadedPlugin.plugin.id,
          ruleId,
          rule,
          timestamp: Date.now(),
        } as PluginEventData);

      } catch (error) {
        console.warn(`Failed to unregister rule '${ruleId}' from plugin '${loadedPlugin.plugin.id}':`, error);
      }
    }
  }

  private async watchPlugin(loadedPlugin: InternalLoadedPlugin): Promise<void> {
    if (loadedPlugin.metadata.source.type !== 'file') {
      return; // 只监听文件类型的插件
    }

    const filePath = loadedPlugin.metadata.source.path;
    
    if (this.watchedPaths.has(filePath)) {
      return; // 已经在监听
    }

    try {
      const watcher = fs.watch(filePath, { persistent: false }, async (eventType) => {
        if (eventType === 'change') {
          console.log(`Plugin file changed: ${filePath}, reloading...`);
          
          try {
            await this.reloadPlugin(loadedPlugin.plugin.id);
          } catch (error) {
            console.error(`Failed to reload plugin '${loadedPlugin.plugin.id}' after file change:`, error);
          }
        }
      });

      this.watchedPaths.add(filePath);
      
      if (!this.pluginWatcher) {
        this.pluginWatcher = watcher;
      }

    } catch (error) {
      console.warn(`Failed to watch plugin file '${filePath}':`, error);
    }
  }

  private async unwatchPlugin(loadedPlugin: InternalLoadedPlugin): Promise<void> {
    if (loadedPlugin.metadata.source.type !== 'file') {
      return;
    }

    const filePath = loadedPlugin.metadata.source.path;
    this.watchedPaths.delete(filePath);
  }

  private async backupPluginConfig(pluginId: string, config: any): Promise<void> {
    const backupPath = path.join(process.cwd(), '.plugin-backups', `${pluginId}-config-${Date.now()}.json`);
    
    try {
      const backupDir = path.dirname(backupPath);
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }
      
      fs.writeFileSync(backupPath, JSON.stringify(config, null, 2));
      console.log(`Plugin config backed up to: ${backupPath}`);
    } catch (error) {
      console.warn(`Failed to backup plugin config for '${pluginId}':`, error);
    }
  }

  private setupErrorHandling(): void {
    // 处理未捕获的插件错误
    process.on('uncaughtException', (error) => {
      if (error.name === 'PluginError') {
        const pluginError = error as PluginError;
        console.error(`Uncaught plugin error in '${pluginError.pluginId}':`, pluginError);
        
        this.emit('plugin:error', {
          pluginId: pluginError.pluginId,
          error: pluginError,
          timestamp: Date.now(),
        } as PluginEventData);
      }
    });

    process.on('unhandledRejection', (reason) => {
      if (reason instanceof PluginError) {
        console.error(`Unhandled plugin rejection in '${reason.pluginId}':`, reason);
        
        this.emit('plugin:error', {
          pluginId: reason.pluginId,
          error: reason,
          timestamp: Date.now(),
        } as PluginEventData);
      }
    });
  }
}