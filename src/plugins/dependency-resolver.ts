/**
 * 插件依赖解析器
 * 负责解析插件间的依赖关系，检测循环依赖和版本冲突
 */

import type {
  Plugin,
  DependencyValidationResult,
  VersionConflict,
} from './types';

export class DependencyResolver {
  
  /**
   * 解析单个插件的依赖
   */
  resolveDependencies(targetPlugin: Plugin, allPlugins: Plugin[]): string[] {
    const resolved: string[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();
    
    const resolve = (pluginId: string): void => {
      if (visited.has(pluginId)) {
        return;
      }
      
      if (visiting.has(pluginId)) {
        throw new Error(`Circular dependency detected: ${Array.from(visiting).join(' -> ')} -> ${pluginId}`);
      }
      
      visiting.add(pluginId);
      
      const plugin = allPlugins.find(p => p.id === pluginId);
      if (plugin && plugin.dependencies) {
        for (const depId of plugin.dependencies) {
          resolve(depId);
        }
      }
      
      visiting.delete(pluginId);
      visited.add(pluginId);
      resolved.push(pluginId);
    };
    
    try {
      resolve(targetPlugin.id);
      return resolved;
    } catch (error) {
      throw new Error(`Failed to resolve dependencies for plugin '${targetPlugin.id}': ${error}`);
    }
  }

  /**
   * 验证插件依赖
   */
  validateDependencies(targetPlugin: Plugin, allPlugins: Plugin[]): DependencyValidationResult {
    const errors: any[] = [];
    const warnings: any[] = [];
    const missingDependencies: string[] = [];
    const versionConflicts: VersionConflict[] = [];
    const circularDependencies: string[] = [];
    
    // 创建插件映射
    const pluginMap = new Map<string, Plugin>();
    for (const plugin of allPlugins) {
      pluginMap.set(plugin.id, plugin);
    }
    
    // 1. 检查缺失的依赖
    if (targetPlugin.dependencies) {
      for (const depId of targetPlugin.dependencies) {
        if (!pluginMap.has(depId)) {
          missingDependencies.push(depId);
          errors.push({
            message: `Missing dependency: ${depId}`,
            path: `dependencies`,
            severity: 'error',
            code: 'MISSING_DEPENDENCY',
          });
        }
      }
    }
    
    // 2. 检查循环依赖
    try {
      this.detectCircularDependencies(targetPlugin, allPlugins);
    } catch (error) {
      if (error instanceof Error && error.message.includes('Circular dependency')) {
        const circularPath = this.extractCircularPath(error.message);
        circularDependencies.push(...circularPath);
        errors.push({
          message: error.message,
          path: 'dependencies',
          severity: 'error',
          code: 'CIRCULAR_DEPENDENCY',
        });
      }
    }
    
    // 3. 检查版本冲突
    const conflicts = this.detectVersionConflicts(targetPlugin, allPlugins);
    versionConflicts.push(...conflicts);
    
    for (const conflict of conflicts) {
      errors.push({
        message: `Version conflict for dependency '${conflict.dependency}': required ${conflict.required}, but found ${conflict.actual}`,
        path: 'dependencies',
        severity: conflict.conflictType === 'incompatible' ? 'error' : 'warning',
        code: 'VERSION_CONFLICT',
      });
    }
    
    // 4. 检查依赖深度
    const maxDepth = this.calculateDependencyDepth(targetPlugin, allPlugins);
    if (maxDepth > 10) {
      warnings.push({
        message: `Deep dependency chain detected (depth: ${maxDepth}). This may impact performance.`,
        path: 'dependencies',
        code: 'DEEP_DEPENDENCY_CHAIN',
        suggestion: 'Consider reducing dependency depth for better performance',
      });
    }
    
    // 5. 检查互相依赖
    const mutualDeps = this.detectMutualDependencies(targetPlugin, allPlugins);
    for (const mutualDep of mutualDeps) {
      warnings.push({
        message: `Mutual dependency detected with plugin '${mutualDep}'`,
        path: 'dependencies',
        code: 'MUTUAL_DEPENDENCY',
        suggestion: 'Consider refactoring to eliminate mutual dependencies',
      });
    }
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      missingDependencies,
      versionConflicts,
      circularDependencies,
    };
  }

  /**
   * 获取依赖图
   */
  buildDependencyGraph(plugins: Plugin[]): Map<string, string[]> {
    const graph = new Map<string, string[]>();
    
    for (const plugin of plugins) {
      graph.set(plugin.id, plugin.dependencies || []);
    }
    
    return graph;
  }

  /**
   * 计算插件的加载顺序
   */
  calculateLoadOrder(plugins: Plugin[]): string[] {
    const graph = this.buildDependencyGraph(plugins);
    const visited = new Set<string>();
    const result: string[] = [];
    const temp = new Set<string>();
    
    const visit = (pluginId: string): void => {
      if (temp.has(pluginId)) {
        throw new Error(`Circular dependency detected involving: ${pluginId}`);
      }
      
      if (visited.has(pluginId)) {
        return;
      }
      
      temp.add(pluginId);
      
      const dependencies = graph.get(pluginId) || [];
      for (const depId of dependencies) {
        visit(depId);
      }
      
      temp.delete(pluginId);
      visited.add(pluginId);
      result.push(pluginId);
    };
    
    // 访问所有插件
    for (const plugin of plugins) {
      if (!visited.has(plugin.id)) {
        visit(plugin.id);
      }
    }
    
    return result;
  }

  /**
   * 检查插件是否可以安全移除
   */
  canSafelyRemove(pluginId: string, allPlugins: Plugin[]): {
    canRemove: boolean;
    dependents: string[];
    reasons: string[];
  } {
    const dependents: string[] = [];
    const reasons: string[] = [];
    
    // 查找依赖于此插件的其他插件
    for (const plugin of allPlugins) {
      if (plugin.id !== pluginId && plugin.dependencies?.includes(pluginId)) {
        dependents.push(plugin.id);
        reasons.push(`Plugin '${plugin.id}' depends on '${pluginId}'`);
      }
    }
    
    return {
      canRemove: dependents.length === 0,
      dependents,
      reasons,
    };
  }

  /**
   * 获取插件的所有传递依赖
   */
  getTransitiveDependencies(pluginId: string, allPlugins: Plugin[]): string[] {
    const pluginMap = new Map<string, Plugin>();
    for (const plugin of allPlugins) {
      pluginMap.set(plugin.id, plugin);
    }
    
    const visited = new Set<string>();
    const result = new Set<string>();
    
    const collect = (currentId: string): void => {
      if (visited.has(currentId)) {
        return;
      }
      
      visited.add(currentId);
      const plugin = pluginMap.get(currentId);
      
      if (plugin && plugin.dependencies) {
        for (const depId of plugin.dependencies) {
          result.add(depId);
          collect(depId);
        }
      }
    };
    
    collect(pluginId);
    return Array.from(result);
  }

  private detectCircularDependencies(targetPlugin: Plugin, allPlugins: Plugin[]): void {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();
    const pluginMap = new Map<string, Plugin>();
    
    for (const plugin of allPlugins) {
      pluginMap.set(plugin.id, plugin);
    }
    
    const hasCycle = (pluginId: string, path: string[] = []): boolean => {
      if (recursionStack.has(pluginId)) {
        const cycleStart = path.indexOf(pluginId);
        const cyclePath = path.slice(cycleStart).concat(pluginId);
        throw new Error(`Circular dependency detected: ${cyclePath.join(' -> ')}`);
      }
      
      if (visited.has(pluginId)) {
        return false;
      }
      
      visited.add(pluginId);
      recursionStack.add(pluginId);
      
      const plugin = pluginMap.get(pluginId);
      if (plugin && plugin.dependencies) {
        for (const depId of plugin.dependencies) {
          if (hasCycle(depId, [...path, pluginId])) {
            return true;
          }
        }
      }
      
      recursionStack.delete(pluginId);
      return false;
    };
    
    hasCycle(targetPlugin.id);
  }

  private detectVersionConflicts(targetPlugin: Plugin, allPlugins: Plugin[]): VersionConflict[] {
    const conflicts: VersionConflict[] = [];
    
    // 简化版本冲突检测
    // 在实际实现中应该使用更复杂的版本解析逻辑
    
    if (targetPlugin.dependencies) {
      for (const depId of targetPlugin.dependencies) {
        const dependency = allPlugins.find(p => p.id === depId);
        if (dependency) {
          // 检查引擎版本兼容性
          if (targetPlugin.engineVersion && dependency.engineVersion) {
            if (!this.areVersionsCompatible(targetPlugin.engineVersion, dependency.engineVersion)) {
              conflicts.push({
                dependency: depId,
                required: targetPlugin.engineVersion,
                actual: dependency.engineVersion,
                conflictType: 'incompatible',
              });
            }
          }
        }
      }
    }
    
    return conflicts;
  }

  private calculateDependencyDepth(targetPlugin: Plugin, allPlugins: Plugin[]): number {
    const pluginMap = new Map<string, Plugin>();
    for (const plugin of allPlugins) {
      pluginMap.set(plugin.id, plugin);
    }
    
    const visited = new Set<string>();
    
    const getDepth = (pluginId: string): number => {
      if (visited.has(pluginId)) {
        return 0; // 避免无限循环
      }
      
      visited.add(pluginId);
      
      const plugin = pluginMap.get(pluginId);
      if (!plugin || !plugin.dependencies || plugin.dependencies.length === 0) {
        visited.delete(pluginId);
        return 0;
      }
      
      let maxDepth = 0;
      for (const depId of plugin.dependencies) {
        const depth = getDepth(depId);
        maxDepth = Math.max(maxDepth, depth + 1);
      }
      
      visited.delete(pluginId);
      return maxDepth;
    };
    
    return getDepth(targetPlugin.id);
  }

  private detectMutualDependencies(targetPlugin: Plugin, allPlugins: Plugin[]): string[] {
    const mutualDeps: string[] = [];
    
    if (!targetPlugin.dependencies) {
      return mutualDeps;
    }
    
    for (const depId of targetPlugin.dependencies) {
      const dependency = allPlugins.find(p => p.id === depId);
      if (dependency && dependency.dependencies?.includes(targetPlugin.id)) {
        mutualDeps.push(depId);
      }
    }
    
    return mutualDeps;
  }

  private extractCircularPath(errorMessage: string): string[] {
    const match = errorMessage.match(/Circular dependency detected: (.+)/);
    if (match) {
      return match[1].split(' -> ');
    }
    return [];
  }

  private areVersionsCompatible(version1: string, version2: string): boolean {
    // 简化的版本兼容性检查
    // 在实际实现中应该使用 semver 库进行更准确的版本比较
    
    // 移除版本前缀（如 ^, ~, >=）
    const cleanVersion1 = version1.replace(/^[\^\~\>\=\<]+/, '');
    const cleanVersion2 = version2.replace(/^[\^\~\>\=\<]+/, '');
    
    const parts1 = cleanVersion1.split('.').map(Number);
    const parts2 = cleanVersion2.split('.').map(Number);
    
    // 主版本号必须相同
    return parts1[0] === parts2[0];
  }

  /**
   * 优化插件加载顺序
   */
  optimizeLoadOrder(plugins: Plugin[]): {
    order: string[];
    parallelGroups: string[][];
    optimizations: string[];
  } {
    const graph = this.buildDependencyGraph(plugins);
    const optimizations: string[] = [];
    
    // 计算每个插件的依赖深度
    const depths = new Map<string, number>();
    const visited = new Set<string>();
    
    const calculateDepth = (pluginId: string): number => {
      if (depths.has(pluginId)) {
        return depths.get(pluginId)!;
      }
      
      if (visited.has(pluginId)) {
        return 0; // 循环依赖处理
      }
      
      visited.add(pluginId);
      
      const dependencies = graph.get(pluginId) || [];
      let maxDepth = 0;
      
      for (const depId of dependencies) {
        const depDepth = calculateDepth(depId);
        maxDepth = Math.max(maxDepth, depDepth + 1);
      }
      
      visited.delete(pluginId);
      depths.set(pluginId, maxDepth);
      return maxDepth;
    };
    
    // 计算所有插件的深度
    for (const plugin of plugins) {
      calculateDepth(plugin.id);
    }
    
    // 按深度分组
    const depthGroups = new Map<number, string[]>();
    for (const [pluginId, depth] of depths.entries()) {
      if (!depthGroups.has(depth)) {
        depthGroups.set(depth, []);
      }
      depthGroups.get(depth)!.push(pluginId);
    }
    
    // 生成加载顺序和并行组
    const order: string[] = [];
    const parallelGroups: string[][] = [];
    
    const sortedDepths = Array.from(depthGroups.keys()).sort((a, b) => a - b);
    
    for (const depth of sortedDepths) {
      const group = depthGroups.get(depth)!;
      parallelGroups.push(group);
      order.push(...group);
      
      if (group.length > 1) {
        optimizations.push(`可以并行加载第 ${depth} 层的 ${group.length} 个插件: ${group.join(', ')}`);
      }
    }
    
    return {
      order,
      parallelGroups,
      optimizations,
    };
  }

  /**
   * 生成依赖关系报告
   */
  generateDependencyReport(plugins: Plugin[]): {
    totalPlugins: number;
    withDependencies: number;
    maxDepth: number;
    averageDepth: number;
    circularDependencies: string[];
    heaviestPlugins: Array<{ id: string; dependencyCount: number }>;
    independentPlugins: string[];
  } {
    const pluginsWithDeps = plugins.filter(p => p.dependencies && p.dependencies.length > 0);
    
    let maxDepth = 0;
    let totalDepth = 0;
    const circularDependencies: string[] = [];
    const dependencyCounts: Array<{ id: string; dependencyCount: number }> = [];
    
    for (const plugin of plugins) {
      const depCount = plugin.dependencies?.length || 0;
      dependencyCounts.push({ id: plugin.id, dependencyCount: depCount });
      
      try {
        const depth = this.calculateDependencyDepth(plugin, plugins);
        maxDepth = Math.max(maxDepth, depth);
        totalDepth += depth;
      } catch (error) {
        if (error instanceof Error && error.message.includes('Circular dependency')) {
          circularDependencies.push(plugin.id);
        }
      }
    }
    
    const averageDepth = plugins.length > 0 ? totalDepth / plugins.length : 0;
    
    // 找出依赖最多的插件
    const heaviestPlugins = dependencyCounts
      .sort((a, b) => b.dependencyCount - a.dependencyCount)
      .slice(0, 5);
    
    // 找出独立插件（没有依赖的插件）
    const independentPlugins = plugins
      .filter(p => !p.dependencies || p.dependencies.length === 0)
      .map(p => p.id);
    
    return {
      totalPlugins: plugins.length,
      withDependencies: pluginsWithDeps.length,
      maxDepth,
      averageDepth,
      circularDependencies,
      heaviestPlugins,
      independentPlugins,
    };
  }
}