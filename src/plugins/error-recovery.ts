/**
 * 插件错误恢复和降级机制
 * 提供插件错误处理、自动恢复、性能降级和故障隔离功能
 */

import { EventEmitter } from 'events';
import type {
  Plugin,
  LoadedPlugin,
  PluginManager,
  PluginError,
  PluginHealth,
  RecoveryStrategy,
  RecoveryStrategyType,
  RecoveryAction,
  RecoveryResult,
  HealthCheck,
  HealthStatus,
  FallbackConfig,
  CircuitBreakerConfig,
  CircuitBreakerState,
  ErrorPattern,
  RecoveryPolicy,
  DegradationLevel,
  PluginEventData,
} from './types';

/**
 * 断路器实现
 */
class CircuitBreaker {
  private state: CircuitBreakerState = 'closed';
  private failureCount = 0;
  private lastFailureTime = 0;
  private successCount = 0;

  constructor(private config: CircuitBreakerConfig) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime > this.config.timeout) {
        this.state = 'half-open';
        this.successCount = 0;
      } else {
        throw new Error('断路器处于开放状态');
      }
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failureCount = 0;
    
    if (this.state === 'half-open') {
      this.successCount++;
      if (this.successCount >= this.config.successThreshold) {
        this.state = 'closed';
      }
    }
  }

  private onFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    if (this.failureCount >= this.config.failureThreshold) {
      this.state = 'open';
    }
  }

  getState(): CircuitBreakerState {
    return this.state;
  }

  reset(): void {
    this.state = 'closed';
    this.failureCount = 0;
    this.successCount = 0;
    this.lastFailureTime = 0;
  }
}

/**
 * 健康检查器
 */
class HealthChecker {
  private healthChecks = new Map<string, HealthCheck>();
  private healthStatus = new Map<string, HealthStatus>();
  private intervalIds = new Map<string, NodeJS.Timeout>();

  constructor(private eventEmitter: EventEmitter) {}

  registerHealthCheck(pluginId: string, healthCheck: HealthCheck): void {
    this.healthChecks.set(pluginId, healthCheck);
    
    // 启动定期健康检查
    if (healthCheck.interval && healthCheck.interval > 0) {
      const intervalId = setInterval(() => {
        this.performHealthCheck(pluginId);
      }, healthCheck.interval);
      
      this.intervalIds.set(pluginId, intervalId);
    }

    // 立即执行一次健康检查
    this.performHealthCheck(pluginId);
  }

  unregisterHealthCheck(pluginId: string): void {
    this.healthChecks.delete(pluginId);
    this.healthStatus.delete(pluginId);
    
    const intervalId = this.intervalIds.get(pluginId);
    if (intervalId) {
      clearInterval(intervalId);
      this.intervalIds.delete(pluginId);
    }
  }

  async performHealthCheck(pluginId: string): Promise<HealthStatus> {
    const healthCheck = this.healthChecks.get(pluginId);
    if (!healthCheck) {
      return { status: 'unknown', timestamp: Date.now(), details: {} };
    }

    try {
      const result = await healthCheck.check();
      const status: HealthStatus = {
        status: result.healthy ? 'healthy' : 'unhealthy',
        timestamp: Date.now(),
        details: result.details || {},
        message: result.message,
      };

      this.healthStatus.set(pluginId, status);
      
      this.eventEmitter.emit('plugin:health-check', {
        pluginId,
        status,
        timestamp: Date.now(),
      });

      return status;
    } catch (error) {
      const status: HealthStatus = {
        status: 'error',
        timestamp: Date.now(),
        details: { error: (error as Error).message },
        message: `健康检查失败: ${(error as Error).message}`,
      };

      this.healthStatus.set(pluginId, status);
      
      this.eventEmitter.emit('plugin:health-check', {
        pluginId,
        status,
        timestamp: Date.now(),
      });

      return status;
    }
  }

  getHealthStatus(pluginId: string): HealthStatus | undefined {
    return this.healthStatus.get(pluginId);
  }

  getAllHealthStatus(): Map<string, HealthStatus> {
    return new Map(this.healthStatus);
  }

  cleanup(): void {
    for (const intervalId of this.intervalIds.values()) {
      clearInterval(intervalId);
    }
    
    this.healthChecks.clear();
    this.healthStatus.clear();
    this.intervalIds.clear();
  }
}

/**
 * 错误模式匹配器
 */
class ErrorPatternMatcher {
  constructor(private patterns: ErrorPattern[]) {}

  matchError(error: Error): ErrorPattern | null {
    for (const pattern of this.patterns) {
      if (this.matchPattern(error, pattern)) {
        return pattern;
      }
    }
    return null;
  }

  private matchPattern(error: Error, pattern: ErrorPattern): boolean {
    // 检查错误类型
    if (pattern.errorType && error.constructor.name !== pattern.errorType) {
      return false;
    }

    // 检查错误消息模式
    if (pattern.messagePattern) {
      const regex = new RegExp(pattern.messagePattern, 'i');
      if (!regex.test(error.message)) {
        return false;
      }
    }

    // 检查自定义匹配器
    if (pattern.matcher && !pattern.matcher(error)) {
      return false;
    }

    return true;
  }
}

/**
 * 插件错误恢复和降级管理器
 */
export class ErrorRecoveryManager extends EventEmitter {
  private pluginManager: PluginManager;
  private circuitBreakers = new Map<string, CircuitBreaker>();
  private healthChecker: HealthChecker;
  private errorPatternMatcher: ErrorPatternMatcher;
  private recoveryPolicies = new Map<string, RecoveryPolicy>();
  private fallbackConfigs = new Map<string, FallbackConfig>();
  private degradationLevels = new Map<string, DegradationLevel>();
  private recoveryAttempts = new Map<string, number>();
  private maxRecoveryAttempts = 3;

  constructor(
    pluginManager: PluginManager,
    errorPatterns: ErrorPattern[] = [],
    circuitBreakerConfig: CircuitBreakerConfig = {
      failureThreshold: 5,
      successThreshold: 3,
      timeout: 60000,
    }
  ) {
    super();
    this.pluginManager = pluginManager;
    this.healthChecker = new HealthChecker(this);
    this.errorPatternMatcher = new ErrorPatternMatcher(errorPatterns);

    this.setupEventHandlers();
    this.initializeCircuitBreakers(circuitBreakerConfig);
  }

  /**
   * 注册插件恢复策略
   */
  registerRecoveryPolicy(pluginId: string, policy: RecoveryPolicy): void {
    this.recoveryPolicies.set(pluginId, policy);
    
    // 注册健康检查
    if (policy.healthCheck) {
      this.healthChecker.registerHealthCheck(pluginId, policy.healthCheck);
    }

    console.log(`已注册插件 '${pluginId}' 的恢复策略`);
  }

  /**
   * 注册插件降级配置
   */
  registerFallbackConfig(pluginId: string, config: FallbackConfig): void {
    this.fallbackConfigs.set(pluginId, config);
    console.log(`已注册插件 '${pluginId}' 的降级配置`);
  }

  /**
   * 处理插件错误
   */
  async handlePluginError(pluginId: string, error: Error): Promise<RecoveryResult> {
    const startTime = performance.now();

    try {
      this.emit('plugin:error-detected', {
        pluginId,
        error,
        timestamp: Date.now(),
      });

      // 匹配错误模式
      const errorPattern = this.errorPatternMatcher.matchError(error);
      
      // 获取恢复策略
      const recoveryPolicy = this.recoveryPolicies.get(pluginId);
      if (!recoveryPolicy) {
        return this.createFailureResult(pluginId, startTime, new Error('未找到恢复策略'));
      }

      // 检查是否超过最大恢复尝试次数
      const attempts = this.recoveryAttempts.get(pluginId) || 0;
      if (attempts >= this.maxRecoveryAttempts) {
        console.warn(`插件 '${pluginId}' 已达到最大恢复尝试次数，执行降级`);
        return await this.performDegradation(pluginId, 'critical', startTime);
      }

      // 增加恢复尝试次数
      this.recoveryAttempts.set(pluginId, attempts + 1);

      // 选择恢复策略
      const strategy = this.selectRecoveryStrategy(errorPattern, recoveryPolicy);
      
      // 执行恢复
      const result = await this.executeRecoveryStrategy(pluginId, strategy, error, startTime);

      // 重置恢复尝试次数（如果成功）
      if (result.success) {
        this.recoveryAttempts.delete(pluginId);
        this.circuitBreakers.get(pluginId)?.reset();
      }

      return result;

    } catch (recoveryError) {
      return this.createFailureResult(pluginId, startTime, recoveryError as Error);
    }
  }

  /**
   * 手动触发插件恢复
   */
  async recoverPlugin(pluginId: string, strategy?: RecoveryStrategy): Promise<RecoveryResult> {
    const startTime = performance.now();
    const recoveryPolicy = this.recoveryPolicies.get(pluginId);
    
    if (!recoveryPolicy) {
      return this.createFailureResult(pluginId, startTime, new Error('未找到恢复策略'));
    }

    const selectedStrategy = strategy || recoveryPolicy.defaultStrategy;
    
    try {
      return await this.executeRecoveryStrategy(pluginId, selectedStrategy, null, startTime);
    } catch (error) {
      return this.createFailureResult(pluginId, startTime, error as Error);
    }
  }

  /**
   * 设置插件降级级别
   */
  async setDegradationLevel(pluginId: string, level: DegradationLevel): Promise<void> {
    const currentLevel = this.degradationLevels.get(pluginId) || 'none';
    
    if (currentLevel === level) {
      return;
    }

    this.degradationLevels.set(pluginId, level);

    this.emit('plugin:degradation-changed', {
      pluginId,
      previousLevel: currentLevel,
      currentLevel: level,
      timestamp: Date.now(),
    });

    // 应用降级配置
    await this.applyDegradation(pluginId, level);

    console.log(`插件 '${pluginId}' 降级级别已设置为: ${level}`);
  }

  /**
   * 获取插件健康状态
   */
  getPluginHealth(pluginId: string): HealthStatus | undefined {
    return this.healthChecker.getHealthStatus(pluginId);
  }

  /**
   * 获取所有插件健康状态
   */
  getAllPluginHealth(): Map<string, HealthStatus> {
    return this.healthChecker.getAllHealthStatus();
  }

  /**
   * 获取断路器状态
   */
  getCircuitBreakerState(pluginId: string): CircuitBreakerState | undefined {
    return this.circuitBreakers.get(pluginId)?.getState();
  }

  /**
   * 执行插件操作（通过断路器）
   */
  async executeWithCircuitBreaker<T>(
    pluginId: string,
    operation: () => Promise<T>
  ): Promise<T> {
    const circuitBreaker = this.circuitBreakers.get(pluginId);
    if (!circuitBreaker) {
      throw new Error(`插件 '${pluginId}' 的断路器未初始化`);
    }

    return await circuitBreaker.execute(operation);
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.healthChecker.cleanup();
    this.circuitBreakers.clear();
    this.recoveryPolicies.clear();
    this.fallbackConfigs.clear();
    this.degradationLevels.clear();
    this.recoveryAttempts.clear();
    this.removeAllListeners();
  }

  // 私有方法

  private setupEventHandlers(): void {
    // 监听插件错误事件
    this.pluginManager.on('plugin:error', async (data: PluginEventData) => {
      if (data.error) {
        await this.handlePluginError(data.pluginId, data.error);
      }
    });

    // 监听插件加载事件
    this.pluginManager.on('plugin:loaded', (data: PluginEventData) => {
      // 重置恢复尝试次数
      this.recoveryAttempts.delete(data.pluginId);
      
      // 重置降级级别
      this.degradationLevels.set(data.pluginId, 'none');
    });

    // 监听插件卸载事件
    this.pluginManager.on('plugin:unloaded', (data: PluginEventData) => {
      this.healthChecker.unregisterHealthCheck(data.pluginId);
      this.circuitBreakers.delete(data.pluginId);
      this.recoveryAttempts.delete(data.pluginId);
      this.degradationLevels.delete(data.pluginId);
    });

    // 监听健康检查事件
    this.on('plugin:health-check', (data: any) => {
      if (data.status.status === 'unhealthy' || data.status.status === 'error') {
        // 触发自动恢复
        this.handlePluginError(data.pluginId, new Error(data.status.message || '健康检查失败'))
          .catch(error => {
            console.error(`自动恢复失败:`, error);
          });
      }
    });
  }

  private initializeCircuitBreakers(config: CircuitBreakerConfig): void {
    // 为所有已加载的插件初始化断路器
    const plugins = this.pluginManager.getAllPlugins();
    for (const plugin of plugins) {
      this.circuitBreakers.set(plugin.plugin.id, new CircuitBreaker(config));
    }

    // 监听新插件加载
    this.pluginManager.on('plugin:loaded', (data: PluginEventData) => {
      this.circuitBreakers.set(data.pluginId, new CircuitBreaker(config));
    });
  }

  private selectRecoveryStrategy(
    errorPattern: ErrorPattern | null,
    recoveryPolicy: RecoveryPolicy
  ): RecoveryStrategy {
    // 如果有匹配的错误模式，使用其推荐的策略
    if (errorPattern && errorPattern.recommendedStrategy) {
      return errorPattern.recommendedStrategy;
    }

    // 否则使用默认策略
    return recoveryPolicy.defaultStrategy;
  }

  private async executeRecoveryStrategy(
    pluginId: string,
    strategy: RecoveryStrategyType,
    error: Error | null,
    startTime: number
  ): Promise<RecoveryResult> {
    console.log(`执行插件 '${pluginId}' 的恢复策略: ${strategy}`);

    try {
      switch (strategy) {
        case 'restart':
          return await this.restartPlugin(pluginId, startTime);
        
        case 'reload':
          return await this.reloadPlugin(pluginId, startTime);
        
        case 'reset':
          return await this.resetPlugin(pluginId, startTime);
        
        case 'fallback':
          return await this.fallbackPlugin(pluginId, startTime);
        
        case 'disable':
          return await this.disablePlugin(pluginId, startTime);
        
        case 'isolate':
          return await this.isolatePlugin(pluginId, startTime);
        
        default:
          throw new Error(`未知的恢复策略: ${strategy}`);
      }
    } catch (error) {
      return this.createFailureResult(pluginId, startTime, error as Error);
    }
  }

  private async restartPlugin(pluginId: string, startTime: number): Promise<RecoveryResult> {
    await this.pluginManager.disablePlugin(pluginId);
    await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
    await this.pluginManager.enablePlugin(pluginId);

    return {
      success: true,
      pluginId,
      strategy: 'restart',
      action: 'plugin_restarted',
      duration: performance.now() - startTime,
      timestamp: Date.now(),
    };
  }

  private async reloadPlugin(pluginId: string, startTime: number): Promise<RecoveryResult> {
    await this.pluginManager.reloadPlugin(pluginId);

    return {
      success: true,
      pluginId,
      strategy: 'reload',
      action: 'plugin_reloaded',
      duration: performance.now() - startTime,
      timestamp: Date.now(),
    };
  }

  private async resetPlugin(pluginId: string, startTime: number): Promise<RecoveryResult> {
    const plugin = this.pluginManager.getPlugin(pluginId);
    if (plugin && plugin.plugin.reset) {
      await plugin.sandbox?.execute(async () => {
        await plugin.plugin.reset!();
      });
    }

    return {
      success: true,
      pluginId,
      strategy: 'reset',
      action: 'plugin_reset',
      duration: performance.now() - startTime,
      timestamp: Date.now(),
    };
  }

  private async fallbackPlugin(pluginId: string, startTime: number): Promise<RecoveryResult> {
    const fallbackConfig = this.fallbackConfigs.get(pluginId);
    if (!fallbackConfig) {
      throw new Error(`插件 '${pluginId}' 没有降级配置`);
    }

    // 设置降级级别
    await this.setDegradationLevel(pluginId, fallbackConfig.degradationLevel || 'partial');

    return {
      success: true,
      pluginId,
      strategy: 'fallback',
      action: 'plugin_degraded',
      duration: performance.now() - startTime,
      timestamp: Date.now(),
      details: { degradationLevel: fallbackConfig.degradationLevel },
    };
  }

  private async disablePlugin(pluginId: string, startTime: number): Promise<RecoveryResult> {
    await this.pluginManager.disablePlugin(pluginId);

    return {
      success: true,
      pluginId,
      strategy: 'disable',
      action: 'plugin_disabled',
      duration: performance.now() - startTime,
      timestamp: Date.now(),
    };
  }

  private async isolatePlugin(pluginId: string, startTime: number): Promise<RecoveryResult> {
    // 隔离插件：禁用所有与其他插件的交互
    await this.setDegradationLevel(pluginId, 'isolated');

    return {
      success: true,
      pluginId,
      strategy: 'isolate',
      action: 'plugin_isolated',
      duration: performance.now() - startTime,
      timestamp: Date.now(),
    };
  }

  private async performDegradation(
    pluginId: string,
    level: DegradationLevel,
    startTime: number
  ): Promise<RecoveryResult> {
    await this.setDegradationLevel(pluginId, level);

    return {
      success: true,
      pluginId,
      strategy: 'fallback',
      action: 'plugin_degraded',
      duration: performance.now() - startTime,
      timestamp: Date.now(),
      details: { degradationLevel: level },
    };
  }

  private async applyDegradation(pluginId: string, level: DegradationLevel): Promise<void> {
    const plugin = this.pluginManager.getPlugin(pluginId);
    if (!plugin) {
      return;
    }

    const fallbackConfig = this.fallbackConfigs.get(pluginId);
    if (!fallbackConfig) {
      return;
    }

    // 根据降级级别应用不同的配置
    switch (level) {
      case 'none':
        // 恢复正常配置
        if (fallbackConfig.onRestore) {
          await plugin.sandbox?.execute(async () => {
            await fallbackConfig.onRestore!();
          });
        }
        break;

      case 'partial':
        // 部分功能降级
        if (fallbackConfig.onPartialDegradation) {
          await plugin.sandbox?.execute(async () => {
            await fallbackConfig.onPartialDegradation!();
          });
        }
        break;

      case 'minimal':
        // 最小功能模式
        if (fallbackConfig.onMinimalMode) {
          await plugin.sandbox?.execute(async () => {
            await fallbackConfig.onMinimalMode!();
          });
        }
        break;

      case 'isolated':
        // 隔离模式
        if (fallbackConfig.onIsolation) {
          await plugin.sandbox?.execute(async () => {
            await fallbackConfig.onIsolation!();
          });
        }
        break;

      case 'critical':
        // 临界模式
        if (fallbackConfig.onCriticalMode) {
          await plugin.sandbox?.execute(async () => {
            await fallbackConfig.onCriticalMode!();
          });
        }
        break;
    }
  }

  private createFailureResult(
    pluginId: string,
    startTime: number,
    error: Error
  ): RecoveryResult {
    return {
      success: false,
      pluginId,
      strategy: 'unknown',
      action: 'recovery_failed',
      duration: performance.now() - startTime,
      timestamp: Date.now(),
      error,
    };
  }
}

/**
 * 创建错误恢复管理器
 */
export function createErrorRecoveryManager(
  pluginManager: PluginManager,
  errorPatterns?: ErrorPattern[],
  circuitBreakerConfig?: CircuitBreakerConfig
): ErrorRecoveryManager {
  return new ErrorRecoveryManager(pluginManager, errorPatterns, circuitBreakerConfig);
}