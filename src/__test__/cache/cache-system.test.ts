/**
 * 智能缓存系统测试
 * 验证缓存系统的核心功能和性能
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { IntelligentCacheManager, AdvancedCacheMonitor, CacheSystemFactory } from '../../cache/index';
import type { Node } from '@swc/core';
import type { AnalysisContext, NodeAnalysis, RuleResult } from '../../engine/types';

// 模拟数据
const createMockNode = (type: string = 'IfStatement'): Node => ({
  type: type as any,
  span: { start: 0, end: 10, ctxt: 0 },
} as Node);

const createMockNodeAnalysis = (complexity: number = 1): NodeAnalysis => ({
  node: createMockNode(),
  complexity,
  appliedRules: [],
  exemptions: [],
  children: [],
  aggregatedComplexity: complexity,
  analysisTime: 10,
  cacheUtilization: 0.5,
});

const createMockRuleResult = (complexity: number = 1): RuleResult => ({
  ruleId: 'test-rule',
  complexity,
  isExempted: false,
  shouldIncreaseNesting: false,
  reason: 'Test rule result',
  suggestions: [],
  metadata: {},
  executionTime: 5,
  cacheHit: false,
});

const createMockContext = (): AnalysisContext => ({
  filePath: '/test/file.ts',
  fileContent: 'test content',
  ast: {} as any,
  functionName: 'testFunction',
  nestingLevel: 1,
  config: {} as any,
  jsxMode: 'disabled',
  rules: {} as any,
  cache: {} as any,
  metrics: {} as any,
  plugins: [],
  customData: new Map(),
});

describe('IntelligentCacheManager', () => {
  let cacheManager: IntelligentCacheManager;

  beforeEach(() => {
    cacheManager = CacheSystemFactory.createCacheManager();
  });

  afterEach(() => {
    cacheManager.destroy();
  });

  describe('节点缓存', () => {
    test('应该能够设置和获取节点分析结果', async () => {
      const nodeHash = 'test-node-hash';
      const analysis = createMockNodeAnalysis(2);

      // 设置缓存
      await cacheManager.setCachedNodeResult(nodeHash, analysis);
      
      // 获取缓存
      const cached = await cacheManager.getCachedNodeResult(nodeHash);
      
      expect(cached).toEqual(analysis);
    });

    test('应该返回null对于未缓存的节点', async () => {
      const result = await cacheManager.getCachedNodeResult('non-existent');
      expect(result).toBeNull();
    });

    test('应该正确处理缓存未命中', async () => {
      const stats = cacheManager.getHitRate();
      const initialMisses = stats.misses;

      await cacheManager.getCachedNodeResult('non-existent');
      
      const newStats = cacheManager.getHitRate();
      expect(newStats.misses).toBe(initialMisses + 1);
    });
  });

  describe('规则缓存', () => {
    test('应该能够设置和获取规则结果', async () => {
      const ruleId = 'test-rule';
      const nodeHash = 'test-node';
      const result = createMockRuleResult(3);

      await cacheManager.setCachedRuleResult(ruleId, nodeHash, result);
      const cached = await cacheManager.getCachedRuleResult(ruleId, nodeHash);
      
      expect(cached).toEqual(result);
    });

    test('应该为不同的规则-节点组合创建独立的缓存条目', async () => {
      const result1 = createMockRuleResult(1);
      const result2 = createMockRuleResult(2);

      await cacheManager.setCachedRuleResult('rule1', 'node1', result1);
      await cacheManager.setCachedRuleResult('rule2', 'node1', result2);

      const cached1 = await cacheManager.getCachedRuleResult('rule1', 'node1');
      const cached2 = await cacheManager.getCachedRuleResult('rule2', 'node1');

      expect(cached1?.complexity).toBe(1);
      expect(cached2?.complexity).toBe(2);
    });
  });

  describe('类型缓存', () => {
    test('应该能够设置和获取类型信息', () => {
      const nodeType = 'IfStatement';
      const typeInfo = {
        nodeType,
        isApplicable: (node: Node) => node.type === nodeType,
        defaultComplexity: 1,
        exemptionRules: [],
        lastUpdated: Date.now(),
        hitCount: 0,
      };

      cacheManager.setCachedTypeInfo(nodeType, typeInfo);
      const cached = cacheManager.getCachedTypeInfo(nodeType);
      
      expect(cached?.nodeType).toBe(nodeType);
      expect(cached?.defaultComplexity).toBe(1);
    });

    test('应该更新访问次数', () => {
      const nodeType = 'WhileStatement';
      const typeInfo = {
        nodeType,
        isApplicable: (node: Node) => node.type === nodeType,
        defaultComplexity: 1,
        exemptionRules: [],
        lastUpdated: Date.now(),
        hitCount: 0,
      };

      cacheManager.setCachedTypeInfo(nodeType, typeInfo);
      
      // 多次访问
      cacheManager.getCachedTypeInfo(nodeType);
      cacheManager.getCachedTypeInfo(nodeType);
      const cached = cacheManager.peekCachedTypeInfo(nodeType); // 使用peek不增加计数
      
      expect(cached?.hitCount).toBe(2);
    });
  });

  describe('缓存统计', () => {
    test('应该正确计算命中率', async () => {
      const nodeHash = 'test-stats';
      const analysis = createMockNodeAnalysis();

      // 设置缓存
      await cacheManager.setCachedNodeResult(nodeHash, analysis);
      
      // 一次命中
      await cacheManager.getCachedNodeResult(nodeHash);
      
      // 一次未命中
      await cacheManager.getCachedNodeResult('non-existent');

      const stats = cacheManager.getHitRate();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe(0.5);
    });

    test('应该提供详细的缓存大小信息', async () => {
      const analysis = createMockNodeAnalysis();
      await cacheManager.setCachedNodeResult('test1', analysis);
      await cacheManager.setCachedNodeResult('test2', analysis);

      const size = cacheManager.getSize();
      expect(size.nodeCache).toBe(2);
      expect(size.total).toBeGreaterThan(0);
    });
  });

  describe('缓存失效', () => {
    test('应该能够清空所有缓存', async () => {
      const analysis = createMockNodeAnalysis();
      await cacheManager.setCachedNodeResult('test', analysis);
      
      cacheManager.clearCache();
      
      const cached = await cacheManager.getCachedNodeResult('test');
      expect(cached).toBeNull();
    });

    test('应该能够选择性失效缓存', async () => {
      const analysis = createMockNodeAnalysis();
      await cacheManager.setCachedNodeResult('test1', analysis);
      await cacheManager.setCachedNodeResult('test2', analysis);

      cacheManager.invalidateCache('node', ['test1']);
      
      const cached1 = await cacheManager.getCachedNodeResult('test1');
      const cached2 = await cacheManager.getCachedNodeResult('test2');
      
      expect(cached1).toBeNull();
      expect(cached2).toEqual(analysis);
    });
  });

  describe('缓存预热', () => {
    test('应该能够预热常见节点类型', async () => {
      const nodes = [
        createMockNode('IfStatement'),
        createMockNode('IfStatement'),
        createMockNode('WhileStatement'),
        createMockNode('ForStatement'),
      ];

      await cacheManager.preWarmCache(nodes);

      // 检查类型缓存是否已预热
      const ifTypeInfo = cacheManager.getCachedTypeInfo('IfStatement');
      const whileTypeInfo = cacheManager.getCachedTypeInfo('WhileStatement');
      
      expect(ifTypeInfo).not.toBeNull();
      expect(whileTypeInfo).not.toBeNull();
    });
  });

  describe('增量分析', () => {
    test('应该能够处理增量分析', async () => {
      // 设置初始缓存
      const analysis = createMockNodeAnalysis();
      await cacheManager.setCachedNodeResult('old-node', analysis);

      // 执行增量分析
      const newNodes = [createMockNode('NewType')];
      const removedHashes = ['old-node'];
      
      await cacheManager.performIncrementalAnalysis(newNodes, removedHashes);

      // 验证旧节点被清理
      const oldCached = await cacheManager.getCachedNodeResult('old-node');
      expect(oldCached).toBeNull();

      // 验证新类型被预热
      const newTypeInfo = cacheManager.getCachedTypeInfo('NewType');
      expect(newTypeInfo).not.toBeNull();
    });
  });
});

describe('AdvancedCacheMonitor', () => {
  let monitor: AdvancedCacheMonitor;
  const mockListener = {
    onCacheEvent: vi.fn(),
  };

  beforeEach(() => {
    monitor = CacheSystemFactory.createCacheMonitor();
    vi.clearAllMocks();
  });

  afterEach(() => {
    monitor.destroy();
  });

  describe('事件监听', () => {
    test('应该能够添加和触发事件监听器', () => {
      monitor.addListener(mockListener);
      
      monitor.recordHit('node', 'test-key', 10);
      
      expect(mockListener.onCacheEvent).toHaveBeenCalledWith({
        type: 'hit',
        key: 'test-key',
        layer: 'node',
      });
    });

    test('应该能够移除事件监听器', () => {
      monitor.addListener(mockListener);
      monitor.removeListener(mockListener);
      
      monitor.recordHit('node', 'test-key', 10);
      
      expect(mockListener.onCacheEvent).not.toHaveBeenCalled();
    });
  });

  describe('性能监控', () => {
    test('应该正确记录命中和未命中', () => {
      monitor.recordHit('node', 'key1', 5);
      monitor.recordMiss('node', 'key2', 10);
      
      const stats = monitor.getStatistics();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe(0.5);
    });

    test('应该计算平均访问时间', () => {
      monitor.recordHit('node', 'key1', 10);
      monitor.recordHit('node', 'key2', 20);
      
      const stats = monitor.getStatistics();
      expect(stats.averageHitTime).toBe(15);
    });
  });

  describe('效率报告', () => {
    test('应该生成缓存效率报告', () => {
      // 生成一些测试数据
      for (let i = 0; i < 10; i++) {
        monitor.recordHit('node', `key${i}`, 5 + Math.random() * 10);
      }
      for (let i = 0; i < 5; i++) {
        monitor.recordMiss('rule', `miss${i}`, 15 + Math.random() * 10);
      }

      const report = monitor.getEfficiencyReport();
      
      expect(report.overallEfficiency).toBeGreaterThan(0);
      expect(report.layerEfficiency.node).toBeGreaterThan(0);
      expect(report.improvementSuggestions).toBeInstanceOf(Array);
    });
  });
});

describe('CacheSystemFactory', () => {
  test('应该创建开发环境缓存系统', () => {
    const { manager, monitor } = CacheSystemFactory.createDevelopmentCacheSystem();
    
    expect(manager).toBeInstanceOf(IntelligentCacheManager);
    expect(monitor).toBeInstanceOf(AdvancedCacheMonitor);
    
    manager.destroy();
    monitor.destroy();
  });

  test('应该创建生产环境缓存系统', () => {
    const { manager, monitor } = CacheSystemFactory.createProductionCacheSystem();
    
    expect(manager).toBeInstanceOf(IntelligentCacheManager);
    expect(monitor).toBeInstanceOf(AdvancedCacheMonitor);
    
    manager.destroy();
    monitor.destroy();
  });

  test('应该创建测试环境缓存系统', () => {
    const { manager, monitor } = CacheSystemFactory.createTestingCacheSystem();
    
    expect(manager).toBeInstanceOf(IntelligentCacheManager);
    expect(monitor).toBeInstanceOf(AdvancedCacheMonitor);
    
    manager.destroy();
    monitor.destroy();
  });
});