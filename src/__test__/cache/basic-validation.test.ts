/**
 * 智能缓存系统基础验证
 */

import { test, expect } from 'vitest';

test('缓存系统模块可以正常导入', async () => {
  const { IntelligentCacheManager, DEFAULT_CACHE_CONFIG } = await import('../../cache/types');
  const { IntelligentCacheManager: Manager } = await import('../../cache/manager');
  
  expect(Manager).toBeDefined();
  expect(DEFAULT_CACHE_CONFIG).toBeDefined();
  expect(DEFAULT_CACHE_CONFIG.enableCaching).toBe(true);
});

test('可以创建缓存管理器实例', async () => {
  const { IntelligentCacheManager } = await import('../../cache/manager');
  const cacheManager = new IntelligentCacheManager();
  
  expect(cacheManager).toBeDefined();
  expect(typeof cacheManager.getHitRate).toBe('function');
  expect(typeof cacheManager.getSize).toBe('function');
  
  // 清理
  cacheManager.destroy();
});

test('缓存统计信息初始状态正确', async () => {
  const { IntelligentCacheManager } = await import('../../cache/manager');
  const cacheManager = new IntelligentCacheManager();
  
  const stats = cacheManager.getHitRate();
  expect(stats.totalRequests).toBe(0);
  expect(stats.hits).toBe(0);
  expect(stats.misses).toBe(0);
  expect(stats.hitRate).toBe(0);
  
  const size = cacheManager.getSize();
  expect(size.total).toBe(0);
  expect(size.nodeCache).toBe(0);
  expect(size.ruleCache).toBe(0);
  
  // 清理
  cacheManager.destroy();
});