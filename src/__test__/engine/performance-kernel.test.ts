/**
 * 性能内核测试
 * 测试大文件处理、缓存压缩和时间缓存机制
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PerformanceKernel, createPerformanceKernel, DEFAULT_PERFORMANCE_KERNEL_CONFIG } from '../../engine/performance-kernel';
import type { Module, Node } from '@swc/core';
import type { FunctionAnalysis } from '../../engine/types';

// 模拟大文件AST
function createMockLargeAST(functionCount: number = 2000): Module {
  const functions: any[] = [];
  
  for (let i = 0; i < functionCount; i++) {
    functions.push({
      type: 'FunctionDeclaration',
      identifier: {
        type: 'Identifier',
        value: `function${i}`,
      },
      params: [],
      body: {
        type: 'BlockStatement',
        stmts: Array.from({ length: 10 }, (_, j) => ({
          type: 'IfStatement',
          test: { type: 'Identifier', value: `condition${j}` },
          consequent: { type: 'BlockStatement', stmts: [] },
        })),
      },
    });
  }

  return {
    type: 'Module',
    body: functions,
    span: { start: 0, end: 1000000 },
  } as Module;
}

// 模拟分析器
const mockAnalyzer = vi.fn(async (nodes: Node[]): Promise<FunctionAnalysis[]> => {
  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 10));
  
  return nodes.map((node, index) => ({
    functionName: `function${index}`,
    startLine: index * 10,
    endLine: index * 10 + 10,
    complexity: Math.floor(Math.random() * 10) + 1,
    totalComplexity: Math.floor(Math.random() * 20) + 5,
    details: [],
    metrics: {
      nodeCount: 20,
      ruleApplications: 15,
      cacheHits: 5,
      cacheMisses: 10,
      executionTime: 50,
      memoryUsage: 1024,
    },
    cacheHitRate: 0.33,
  }));
});

describe('PerformanceKernel', () => {
  let kernel: PerformanceKernel;

  beforeEach(() => {
    kernel = createPerformanceKernel({
      compression: {
        enabled: true,
        algorithm: 'gzip',
        level: 6,
        threshold: 100, // 低阈值用于测试
        enableStreaming: true,
      },
      largeFile: {
        sizeThreshold: 0.5, // 0.5MB，低阈值用于测试
        chunkSize: 50, // 小块用于测试
        maxConcurrentChunks: 2,
        enableProgressiveAnalysis: true,
        memoryLimit: 100, // 100MB
      },
      timeCache: {
        enabled: true,
        defaultTTL: 1000, // 1秒用于测试
        maxAge: 5000, // 5秒
        cleanupInterval: 500, // 0.5秒
        adaptiveTTL: true,
      },
    });
  });

  afterEach(() => {
    kernel.destroy();
  });

  describe('大文件处理', () => {
    it('应该正确处理大文件', async () => {
      const largeAST = createMockLargeAST(500); // 500个函数
      const filePath = '/test/large-file.ts';

      const result = await kernel.processLargeFile(filePath, largeAST, mockAnalyzer);

      expect(result).toMatchObject({
        filePath,
        totalFunctions: 500,
      });

      expect(typeof result.processedChunks).toBe('number');
      expect(typeof result.processingTime).toBe('number');
      expect(typeof result.memoryPeak).toBe('number');
      expect(result.totalFunctions).toBe(500);
      expect(mockAnalyzer).toHaveBeenCalled();
    });

    it('应该控制并发块数量', async () => {
      const largeAST = createMockLargeAST(200);
      const filePath = '/test/concurrent-file.ts';

      let maxConcurrentCalls = 0;
      let currentConcurrentCalls = 0;

      const concurrentAnalyzer = vi.fn(async (nodes: Node[]): Promise<FunctionAnalysis[]> => {
        currentConcurrentCalls++;
        maxConcurrentCalls = Math.max(maxConcurrentCalls, currentConcurrentCalls);
        
        await new Promise(resolve => setTimeout(resolve, 100));
        
        currentConcurrentCalls--;
        return mockAnalyzer(nodes);
      });

      await kernel.processLargeFile(filePath, largeAST, concurrentAnalyzer);

      // 并发数量应该不超过配置的最大值
      expect(maxConcurrentCalls).toBeLessThanOrEqual(2);
    });

    it('应该正确处理小文件', async () => {
      const smallAST = createMockLargeAST(5); // 5个函数
      const filePath = '/test/small-file.ts';

      const result = await kernel.processLargeFile(filePath, smallAST, mockAnalyzer);

      expect(result).toMatchObject({
        filePath,
        totalFunctions: 5,
        processedChunks: 1, // 小文件应该作为单个块处理
      });
    });
  });

  describe('缓存压缩', () => {
    it('应该能够压缩和解压数据', async () => {
      const testData = {
        functions: Array.from({ length: 100 }, (_, i) => ({
          name: `function${i}`,
          complexity: Math.floor(Math.random() * 20),
          details: Array.from({ length: 10 }, (_, j) => `detail${j}`),
        })),
        metadata: {
          timestamp: Date.now(),
          version: '1.0.0',
        },
      };

      const compressed = await kernel.compressData(testData);
      expect(compressed).not.toBeNull();
      
      if (compressed) {
        expect(compressed.compressedSize).toBeLessThan(compressed.originalSize);
        expect(compressed.metadata.compressionRatio).toBeLessThan(1);

        const decompressed = await kernel.decompressData(compressed);
        expect(decompressed).toEqual(testData);
      }
    });

    it('应该跳过压缩小数据', async () => {
      const smallData = { test: 'small' };
      const compressed = await kernel.compressData(smallData);
      
      // 小数据应该不被压缩
      expect(compressed).toBeNull();
    });

    it('应该更新压缩指标', async () => {
      const testData = { 
        largeData: 'x'.repeat(1000),
        numbers: Array.from({ length: 100 }, (_, i) => i) 
      };

      await kernel.compressData(testData);
      
      const metrics = kernel.getMetrics();
      expect(metrics.compressionStats.totalCompressions).toBeGreaterThan(0);
      expect(metrics.compressionStats.totalBytesSaved).toBeGreaterThan(0);
    });
  });

  describe('时间感知缓存', () => {
    it('应该能够存储和获取缓存', async () => {
      const key = 'test-key';
      const data = { value: 'test-data', timestamp: Date.now() };

      await kernel.cacheAnalysis(key, data);
      
      const cached = await kernel.getCachedAnalysis(key);
      expect(cached).toEqual(data);
      expect(kernel.hasCachedAnalysis(key)).toBe(true);
    });

    it('应该正确处理TTL过期', async () => {
      const key = 'expire-test';
      const data = { value: 'will-expire' };

      await kernel.cacheAnalysis(key, data, 100); // 100ms TTL
      
      expect(kernel.hasCachedAnalysis(key)).toBe(true);
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(kernel.hasCachedAnalysis(key)).toBe(false);
      const cached = await kernel.getCachedAnalysis(key);
      expect(cached).toBeUndefined();
    });

    it('应该支持自适应TTL', async () => {
      const key = 'adaptive-ttl-test';
      const data = { value: 'adaptive-data' };

      // 首次缓存
      await kernel.cacheAnalysis(key, data);
      
      // 多次访问以建立访问模式
      await kernel.getCachedAnalysis(key);
      await kernel.getCachedAnalysis(key);
      await kernel.getCachedAnalysis(key);
      
      // 高频访问的项应该有更长的TTL
      expect(kernel.hasCachedAnalysis(key)).toBe(true);
    });

    it('应该支持缓存清理', async () => {
      // 添加多个缓存项
      for (let i = 0; i < 10; i++) {
        await kernel.cacheAnalysis(`key${i}`, { value: i }, 100);
      }

      // 等待部分过期
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // 手动清理
      kernel.cleanup();
      
      const metrics = kernel.getMetrics();
      expect(metrics.cacheStats.ttlExpiredCount).toBeGreaterThan(0);
    });

    it('应该支持基于热度的缓存淘汰', async () => {
      // 添加多个缓存项
      for (let i = 0; i < 20; i++) {
        await kernel.cacheAnalysis(`evict-key${i}`, { value: i });
      }

      // 多次访问某些项以提高热度
      for (let j = 0; j < 3; j++) {
        for (let i = 0; i < 5; i++) {
          await kernel.getCachedAnalysis(`evict-key${i}`);
        }
      }

      // 淘汰到目标大小
      kernel.evictCache(10);
      
      const metrics = kernel.getMetrics();
      expect(metrics.cacheStats.evictionCount).toBeGreaterThan(0);
      
      // 检查高热度项是否更有可能被保留
      let highHeatRetained = 0;
      let lowHeatRetained = 0;
      
      for (let i = 0; i < 5; i++) {
        if (kernel.hasCachedAnalysis(`evict-key${i}`)) {
          highHeatRetained++;
        }
      }
      
      for (let i = 15; i < 20; i++) {
        if (kernel.hasCachedAnalysis(`evict-key${i}`)) {
          lowHeatRetained++;
        }
      }
      
      // 高热度项应该比低热度项保留得更多
      expect(highHeatRetained).toBeGreaterThanOrEqual(lowHeatRetained);
    });
  });

  describe('性能指标', () => {
    it('应该正确跟踪性能指标', async () => {
      const testData = { largeData: 'x'.repeat(2000) };
      
      // 执行一些操作
      await kernel.compressData(testData);
      await kernel.cacheAnalysis('metrics-test', testData);
      await kernel.getCachedAnalysis('metrics-test');

      const metrics = kernel.getMetrics();

      expect(metrics).toMatchObject({
        compressionStats: {
          totalCompressions: expect.any(Number),
          totalDecompressions: expect.any(Number),
          totalBytesSaved: expect.any(Number),
          avgCompressionRatio: expect.any(Number),
          avgCompressionTime: expect.any(Number),
          avgDecompressionTime: expect.any(Number),
        },
        cacheStats: {
          totalEntries: expect.any(Number),
          compressedEntries: expect.any(Number),
          totalMemoryUsed: expect.any(Number),
          hitRate: expect.any(Number),
          evictionCount: expect.any(Number),
          ttlExpiredCount: expect.any(Number),
        },
        largeFileStats: {
          filesProcessed: expect.any(Number),
          totalChunksProcessed: expect.any(Number),
          avgProcessingTime: expect.any(Number),
          memoryEfficiency: expect.any(Number),
        },
      });
    });
  });

  describe('集成测试', () => {
    it('应该在大文件处理中使用缓存和压缩', async () => {
      const largeAST = createMockLargeAST(100);
      const filePath = '/test/integrated-file.ts';

      // 第一次处理
      const result1 = await kernel.processLargeFile(filePath, largeAST, mockAnalyzer);
      
      // 缓存一些结果
      await kernel.cacheAnalysis(`${filePath}:chunk:0`, result1);
      
      // 压缩一些数据
      const compressed = await kernel.compressData(result1);
      
      expect(result1.totalFunctions).toBe(100);
      expect(compressed).not.toBeNull();
      expect(kernel.hasCachedAnalysis(`${filePath}:chunk:0`)).toBe(true);

      const metrics = kernel.getMetrics();
      expect(metrics.largeFileStats.filesProcessed).toBe(1);
      expect(metrics.compressionStats.totalCompressions).toBeGreaterThan(0);
      expect(metrics.cacheStats.totalEntries).toBeGreaterThan(0);
    });
  });

  describe('错误处理', () => {
    it('应该正确处理压缩错误', async () => {
      // 创建一个无法JSON序列化的对象
      const circularObj: any = {};
      circularObj.self = circularObj;

      const result = await kernel.compressData(circularObj);
      expect(result).toBeNull();
    });

    it('应该正确处理分析器错误', async () => {
      const errorAnalyzer = vi.fn(async () => {
        throw new Error('Analysis failed');
      });

      const smallAST = createMockLargeAST(5);
      
      await expect(
        kernel.processLargeFile('/test/error-file.ts', smallAST, errorAnalyzer)
      ).rejects.toThrow('Analysis failed');
    });
  });

  describe('内存管理', () => {
    it('应该在内存压力下触发清理', async () => {
      // 创建大量缓存条目
      const promises = [];
      for (let i = 0; i < 100; i++) {
        promises.push(kernel.cacheAnalysis(`memory-test-${i}`, {
          data: 'x'.repeat(1000),
          index: i,
        }));
      }
      
      await Promise.all(promises);
      
      // 模拟内存压力，触发淘汰
      kernel.evictCache(50);
      
      const metrics = kernel.getMetrics();
      expect(metrics.cacheStats.evictionCount).toBeGreaterThan(0);
    });
  });

  describe('配置测试', () => {
    it('应该使用默认配置', () => {
      const defaultKernel = createPerformanceKernel();
      const metrics = defaultKernel.getMetrics();
      
      expect(metrics).toBeDefined();
      
      defaultKernel.destroy();
    });

    it('应该支持配置覆盖', () => {
      const customKernel = createPerformanceKernel({
        compression: {
          enabled: false,
          algorithm: 'deflate',
          level: 9,
          threshold: 2048,
          enableStreaming: false,
        },
      });

      expect(customKernel).toBeDefined();
      
      customKernel.destroy();
    });
  });
});