/**
 * Null Object 模式实现的单元测试
 * 验证 LightweightExecutionPool 和 NullPerformanceMonitor 的空对象行为
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { LightweightExecutionPool } from '../../engine/execution-pool';
import { NullPerformanceMonitor } from '../../engine/performance-monitor';
import type { Rule, RuleResult, AnalysisContext, Node } from '../../engine/types';

// Mock 规则和上下文用于测试
const mockRule: Rule = {
  id: 'test-rule',
  name: 'Test Rule',
  priority: 1,
  canHandle: () => true,
  evaluate: async () => ({
    ruleId: 'test-rule',
    complexity: 1,
    isExempted: false,
    shouldIncreaseNesting: false,
    reason: 'test',
    suggestions: [],
    metadata: {},
    executionTime: 10,
    cacheHit: false
  }),
  getDependencies: () => []
};

const mockNode = { type: 'TestNode' } as Node;

const mockContext: AnalysisContext = {
  filePath: 'test.ts',
  fileContent: 'test code',
  ast: {} as any,
  functionName: 'testFunction',
  nestingLevel: 0,
  config: {} as any,
  jsxMode: 'standard' as any,
  rules: {} as any,
  cache: {} as any,
  metrics: {} as any,
  plugins: [],
  customData: new Map()
};

describe('LightweightExecutionPool', () => {
  let pool: LightweightExecutionPool;

  beforeEach(() => {
    pool = new LightweightExecutionPool();
  });

  afterEach(() => {
    // 验证没有定时器泄漏
    pool.shutdown();
  });

  describe('构造函数行为', () => {
    test('应该创建实例而不启动任何定时器', () => {
      const initialHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      
      new LightweightExecutionPool();
      
      const finalHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      expect(finalHandles).toBe(initialHandles);
    });

    test('应该接受配置选项但不使用它们', () => {
      const configWithComplexOptions = {
        maxConcurrency: 10,
        timeout: 5000,
        enableProfiling: true,
        isolateErrors: true
      };
      
      const pool = new LightweightExecutionPool(configWithComplexOptions);
      expect(pool).toBeInstanceOf(LightweightExecutionPool);
    });
  });

  describe('规则执行', () => {
    test('应该顺序执行所有规则', async () => {
      const mockRules: Rule[] = [
        { 
          ...mockRule, 
          id: 'rule-1',
          evaluate: async () => ({
            ...mockRule.evaluate(mockNode, mockContext),
            ruleId: 'rule-1'
          }) as any
        },
        { 
          ...mockRule, 
          id: 'rule-2',
          evaluate: async () => ({
            ...mockRule.evaluate(mockNode, mockContext),
            ruleId: 'rule-2'
          }) as any
        },
        { 
          ...mockRule, 
          id: 'rule-3',
          evaluate: async () => ({
            ...mockRule.evaluate(mockNode, mockContext),
            ruleId: 'rule-3'
          }) as any
        }
      ];

      const results = await pool.executeRules(mockRules, mockNode, mockContext);
      
      expect(results).toHaveLength(3);
      expect(results[0].ruleId).toBe('rule-1');
      expect(results[1].ruleId).toBe('rule-2');
      expect(results[2].ruleId).toBe('rule-3');
    });

    test('应该处理规则执行错误而不中断', async () => {
      const failingRule: Rule = {
        ...mockRule,
        id: 'failing-rule',
        evaluate: async () => {
          throw new Error('Rule execution failed');
        }
      };

      const mockRules: Rule[] = [
        { 
          ...mockRule, 
          id: 'rule-1',
          evaluate: async () => ({
            ...mockRule.evaluate(mockNode, mockContext),
            ruleId: 'rule-1'
          }) as any
        },
        failingRule,
        { 
          ...mockRule, 
          id: 'rule-3',
          evaluate: async () => ({
            ...mockRule.evaluate(mockNode, mockContext),
            ruleId: 'rule-3'
          }) as any
        }
      ];

      const results = await pool.executeRules(mockRules, mockNode, mockContext);
      
      // 应该只返回成功执行的规则结果
      expect(results).toHaveLength(2);
      expect(results[0].ruleId).toBe('rule-1');
      expect(results[1].ruleId).toBe('rule-3');
    });

    test('应该过滤不能处理节点的规则', async () => {
      const unsupportedRule: Rule = {
        ...mockRule,
        id: 'unsupported-rule',
        canHandle: () => false
      };

      const mockRules: Rule[] = [
        { 
          ...mockRule, 
          id: 'rule-1',
          evaluate: async () => ({
            ...mockRule.evaluate(mockNode, mockContext),
            ruleId: 'rule-1'
          }) as any
        },
        unsupportedRule,
        { 
          ...mockRule, 
          id: 'rule-3',
          evaluate: async () => ({
            ...mockRule.evaluate(mockNode, mockContext),
            ruleId: 'rule-3'
          }) as any
        }
      ];

      const results = await pool.executeRules(mockRules, mockNode, mockContext);
      
      expect(results).toHaveLength(2);
      expect(results[0].ruleId).toBe('rule-1');
      expect(results[1].ruleId).toBe('rule-3');
    });
  });

  describe('任务执行', () => {
    test('应该直接执行异步任务', async () => {
      const testValue = 42;
      const task = async () => testValue;
      
      const result = await pool.execute(task);
      
      expect(result).toBe(testValue);
    });

    test('应该传播任务执行中的错误', async () => {
      const errorMessage = 'Task execution failed';
      const failingTask = async () => {
        throw new Error(errorMessage);
      };
      
      await expect(pool.execute(failingTask)).rejects.toThrow(errorMessage);
    });
  });

  describe('统计和监控', () => {
    test('应该返回空的统计信息', () => {
      const stats = pool.getStats();
      
      expect(stats.totalTasks).toBe(0);
      expect(stats.completedTasks).toBe(0);
      expect(stats.failedTasks).toBe(0);
      expect(stats.activeTasks).toBe(0);
      expect(stats.queuedTasks).toBe(0);
      expect(stats.averageExecutionTime).toBe(0);
      expect(stats.throughput).toBe(0);
      expect(stats.errorRate).toBe(0);
      expect(stats.workers).toEqual([]);
    });

    test('应该返回空的负载信息', () => {
      const load = pool.getCurrentLoad();
      
      expect(load.activeWorkers).toBe(0);
      expect(load.queuedTasks).toBe(0);
      expect(load.totalCapacity).toBe(1);
      expect(load.utilizationRate).toBe(0);
    });

    test('应该返回零活跃任务数', () => {
      expect(pool.getActiveTaskCount()).toBe(0);
    });

    test('应该返回空的指标', () => {
      const metrics = pool.getMetrics();
      
      expect(metrics.executedTasks).toBe(0);
      expect(metrics.averageExecutionTime).toBe(0);
      expect(metrics.errorRate).toBe(0);
      expect(metrics.throughput).toBe(0);
    });
  });

  describe('生命周期管理', () => {
    test('应该允许注册错误处理器但不实际使用', () => {
      const errorHandler = vi.fn();
      
      // 不应该抛出错误
      expect(() => pool.onError(errorHandler)).not.toThrow();
    });

    test('应该允许取消所有任务但不实际执行任何操作', () => {
      expect(() => pool.cancelAll()).not.toThrow();
    });

    test('应该允许关闭但不清理任何资源', () => {
      expect(() => pool.shutdown()).not.toThrow();
    });
  });

  describe('性能特征', () => {
    test('应该具有最小的创建开销', () => {
      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        new LightweightExecutionPool();
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / iterations;
      
      // 平均创建时间应该小于1毫秒
      expect(averageTime).toBeLessThan(1);
    });

    test('应该具有最小的执行开销', async () => {
      const simpleTask = async () => 42;
      const iterations = 100;
      
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        await pool.execute(simpleTask);
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / iterations;
      
      // 平均执行时间应该小于1毫秒
      expect(averageTime).toBeLessThan(1);
    });
  });
});

describe('NullPerformanceMonitor', () => {
  let monitor: NullPerformanceMonitor;

  beforeEach(() => {
    monitor = new NullPerformanceMonitor();
  });

  afterEach(() => {
    // 验证没有定时器泄漏
    monitor.stop();
  });

  describe('构造函数行为', () => {
    test('应该创建实例而不启动任何定时器', () => {
      const initialHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      
      new NullPerformanceMonitor();
      
      const finalHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      expect(finalHandles).toBe(initialHandles);
    });

    test('应该接受配置选项但不使用它们', () => {
      const config = {
        enabled: true,
        enableRealTimeMonitoring: true,
        enableMemoryTracking: true,
        enableTrendAnalysis: true
      };
      
      const monitor = new NullPerformanceMonitor(config);
      expect(monitor).toBeInstanceOf(NullPerformanceMonitor);
    });
  });

  describe('记录方法', () => {
    const mockRuleResult: RuleResult = {
      ruleId: 'test-rule',
      complexity: 1,
      isExempted: false,
      shouldIncreaseNesting: false,
      reason: 'test',
      suggestions: [],
      metadata: {},
      executionTime: 10,
      cacheHit: false
    };

    const mockFunctionAnalysis = {
      functionName: 'testFunction',
      totalComplexity: 5,
      nodeAnalyses: [],
      location: { startLine: 1, endLine: 10, startColumn: 0, endColumn: 0, file: 'test.ts' },
      metrics: { executionTime: 20 }
    };

    const mockFileAnalysis = {
      filePath: 'test.ts',
      functions: [],
      totalComplexity: 0,
      averageComplexity: 0,
      analysisTime: 100,
      cacheHitRate: 0
    };

    test('所有记录方法应该是无操作', () => {
      // 这些调用不应该抛出错误或产生副作用
      expect(() => {
        monitor.recordRuleStart('test-rule', mockNode, mockContext);
        monitor.recordRuleEnd('test-rule', mockRuleResult, 10);
        monitor.recordFunctionAnalysis('testFunction', mockFunctionAnalysis);
        monitor.recordFileAnalysis('test.ts', mockFileAnalysis);
        monitor.recordCacheEvent('layer1', 'hit', 5);
        monitor.recordMemorySnapshot();
        monitor.updateMetrics({});
      }).not.toThrow();
    });
  });

  describe('报告生成', () => {
    test('应该生成空的性能报告', () => {
      const report = monitor.generateReport();
      
      expect(report.overview.totalAnalysisTime).toBe(0);
      expect(report.overview.filesProcessed).toBe(0);
      expect(report.overview.functionsAnalyzed).toBe(0);
      expect(report.overview.overallThroughput).toBe(0);
      expect(report.overview.memoryEfficiency).toBe(100);
      expect(report.overview.cacheEfficiency).toBe(100);
      
      expect(report.hotspots).toEqual([]);
      expect(report.bottlenecks).toEqual([]);
      expect(report.rules).toEqual([]);
      expect(report.cache).toEqual([]);
      expect(report.memory).toEqual([]);
      expect(report.trends).toEqual([]);
      expect(report.events).toEqual([]);
      expect(report.recommendations).toEqual([]);
      
      expect(report.insights.performanceGrade).toBe('A');
      expect(report.insights.topConcerns).toEqual([]);
      expect(report.insights.improvementPotential).toBe(0);
      expect(report.insights.analysisQuality).toBe(100);
      
      expect(report.metadata.sessionDuration).toBe(0);
      expect(report.metadata.analysisScope).toBe('No monitoring enabled');
      expect(report.metadata.dataPoints).toBe(0);
    });

    test('应该返回空的性能快照', () => {
      const snapshot = monitor.getPerformanceSnapshot();
      
      expect(snapshot.metrics).toEqual({});
      expect(snapshot.hotspots).toEqual([]);
      expect(snapshot.memory.heapUsed).toBe(0);
      expect(snapshot.memory.heapTotal).toBe(0);
      expect(snapshot.cache.hitRate).toBe(0);
      expect(snapshot.cache.size).toBe(0);
      expect(typeof snapshot.timestamp).toBe('number');
    });
  });

  describe('查询方法', () => {
    test('应该返回空的热点列表', () => {
      expect(monitor.getHotspots()).toEqual([]);
    });

    test('应该返回空的瓶颈列表', () => {
      expect(monitor.getBottlenecks()).toEqual([]);
    });

    test('应该返回空的趋势数据', () => {
      expect(monitor.getTrends()).toEqual([]);
    });

    test('应该返回空的引擎指标', () => {
      const metrics = monitor.getEngineMetrics();
      
      expect(metrics.totalAnalysisTime).toBe(0);
      expect(metrics.filesProcessed).toBe(0);
      expect(metrics.functionsAnalyzed).toBe(0);
      expect(metrics.rulesExecuted).toBe(0);
      expect(metrics.cacheHitRate).toBe(0);
      expect(metrics.memoryUsage).toBe(0);
      expect(metrics.averageComplexity).toBe(0);
      expect(metrics.throughput).toBe(0);
    });
  });

  describe('生命周期管理', () => {
    test('应该允许启用和禁用但不实际执行任何操作', () => {
      expect(() => {
        monitor.enable();
        monitor.disable();
      }).not.toThrow();
    });

    test('应该允许停止和销毁但不清理任何资源', () => {
      expect(() => {
        monitor.stop();
        monitor.dispose();
      }).not.toThrow();
    });
  });

  describe('性能特征', () => {
    test('应该具有最小的创建开销', () => {
      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        new NullPerformanceMonitor();
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / iterations;
      
      // 平均创建时间应该小于1毫秒
      expect(averageTime).toBeLessThan(1);
    });

    test('应该具有最小的方法调用开销', () => {
      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        monitor.recordRuleStart('test', mockNode, mockContext);
        monitor.recordMemorySnapshot();
        monitor.getHotspots();
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / iterations;
      
      // 平均方法调用时间应该极小
      expect(averageTime).toBeLessThan(0.1);
    });
  });

  describe('内存使用', () => {
    test('应该不持有任何长期引用', () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // 创建多个实例并调用各种方法
      for (let i = 0; i < 100; i++) {
        const monitor = new NullPerformanceMonitor();
        monitor.recordRuleStart('test', mockNode, mockContext);
        monitor.recordMemorySnapshot();
        monitor.generateReport();
        monitor.dispose();
      }
      
      // 强制垃圾回收 (如果可用)
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // 内存增长应该很小 (小于1MB)
      expect(memoryIncrease).toBeLessThan(1024 * 1024);
    });
  });
});

describe('空对象模式集成测试', () => {
  test('空对象应该能完全替代原始对象', async () => {
    const lightweightPool = new LightweightExecutionPool();
    const nullMonitor = new NullPerformanceMonitor();
    
    // 测试它们能正常协同工作
    nullMonitor.recordRuleStart('test-rule', mockNode, mockContext);
    
    const results = await lightweightPool.executeRules([mockRule], mockNode, mockContext);
    
    nullMonitor.recordRuleEnd('test-rule', results[0], 10);
    
    // 验证结果
    expect(results).toHaveLength(1);
    expect(lightweightPool.getStats().totalTasks).toBe(0); // 空对象不记录统计
    expect(nullMonitor.getHotspots()).toEqual([]); // 空对象不记录热点
  });

  test('空对象应该不产生任何副作用', () => {
    const initialHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
    
    const pool = new LightweightExecutionPool();
    const monitor = new NullPerformanceMonitor();
    
    // 执行各种操作
    monitor.recordMemorySnapshot();
    pool.getStats();
    monitor.generateReport();
    pool.shutdown();
    monitor.stop();
    
    const finalHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
    
    // 不应该有任何句柄泄漏
    expect(finalHandles).toBe(initialHandles);
  });
});