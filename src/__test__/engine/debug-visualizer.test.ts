/**
 * 简化的调试可视化器测试
 * 专注于核心功能验证
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { DebugVisualizer, VisualizationConfig } from '../../engine/debug-visualizer';

describe('DebugVisualizer - 简化测试', () => {
  let visualizer: DebugVisualizer;

  const simpleEvents = [
    {
      id: 'event-1',
      type: 'error' as const,
      level: 'error' as const,
      timestamp: Date.now(),
      sessionId: 'test-session',
      context: { filePath: '/test.ts' },
      data: { error: new Error('test') },
    },
  ];

  const simpleVisualTrace = {
    sessionId: 'test-session',
    startTime: Date.now(),
    endTime: Date.now() + 1000,
    files: [],
    hotspots: [],
    flowGraph: { nodes: [], edges: [] },
  };

  const simpleDiagnostics = [
    {
      id: 'diag-1',
      severity: 'warning' as const,
      category: 'performance' as const,
      title: 'Test diagnostic',
      description: 'Test description',
      location: { filePath: '/test.ts', line: 1, column: 1 },
      rootCause: {
        type: 'code_issue' as const,
        description: 'Test root cause',
        evidence: ['test evidence'],
      },
      impact: {
        performance: 50,
        description: 'Test impact',
      },
      solutions: [
        {
          type: 'refactor' as const,
          title: 'Test solution',
          description: 'Test solution description',
          effort: 'low' as const,
          impact: 'medium' as const,
          autoFixAvailable: false,
          steps: ['step 1'],
        },
      ],
      timestamp: Date.now(),
      relatedEvents: [],
      metadata: {},
    },
  ];

  beforeEach(() => {
    visualizer = new DebugVisualizer();
  });

  describe('基本功能', () => {
    test('应该创建实例', () => {
      expect(visualizer).toBeDefined();
    });

    test('应该生成文本报告', () => {
      const textReport = visualizer.generateTextReport(
        simpleEvents,
        simpleVisualTrace,
        simpleDiagnostics
      );

      expect(textReport).toBeTruthy();
      expect(typeof textReport).toBe('string');
      expect(textReport.length).toBeGreaterThan(0);
    });

    test('应该生成JSON报告', () => {
      const jsonReport = visualizer.generateJSONReport(
        simpleEvents,
        simpleVisualTrace,
        simpleDiagnostics,
        []
      );

      expect(jsonReport).toBeTruthy();
      expect(typeof jsonReport).toBe('string');
      
      // 验证是否为有效JSON
      expect(() => JSON.parse(jsonReport)).not.toThrow();
    });

    test('应该生成HTML报告', () => {
      const htmlReport = visualizer.generateHTMLReport(
        simpleEvents,
        simpleVisualTrace,
        simpleDiagnostics,
        []
      );

      expect(htmlReport).toBeTruthy();
      expect(typeof htmlReport).toBe('string');
      expect(htmlReport).toContain('<!DOCTYPE html>');
      expect(htmlReport).toContain('</html>');
    });
  });

  describe('错误处理', () => {
    test('应该处理空数据', () => {
      const emptyVisualTrace = {
        sessionId: 'empty',
        startTime: Date.now(),
        files: [],
        hotspots: [],
        flowGraph: { nodes: [], edges: [] },
      };

      expect(() => {
        visualizer.generateTextReport([], emptyVisualTrace, []);
      }).not.toThrow();

      expect(() => {
        visualizer.generateJSONReport([], emptyVisualTrace, [], []);
      }).not.toThrow();

      expect(() => {
        visualizer.generateHTMLReport([], emptyVisualTrace, [], []);
      }).not.toThrow();
    });
  });

  describe('配置', () => {
    test('应该接受自定义配置', () => {
      const customConfig: Partial<VisualizationConfig> = {
        charts: {
          enableTimeline: false,
          enableFlowGraph: true,
          enablePerformanceBars: false,
          enableHeatMap: true,
        },
      };

      const customVisualizer = new DebugVisualizer(customConfig);
      expect(customVisualizer).toBeDefined();
    });
  });
});