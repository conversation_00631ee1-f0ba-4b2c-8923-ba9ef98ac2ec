/**
 * 完整异步规则引擎类型安全测试
 * 验证 CompleteAsyncRuleEngineImpl 的类型安全实现和接口兼容性
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import type { Node, Module } from '@swc/core';
import { CompleteAsyncRuleEngineImpl } from '../../engine/complete-async-engine';
import type {
  AsyncRuleEngine,
  Rule,
  AnalysisContext,
  NodeAnalysis,
  FunctionAnalysis,
  FileAnalysis,
  EngineMetrics,
  ResolvedEngineConfig
} from '../../engine/types';
import type { RuleRegistry } from '../../engine/registry';
import { createMockAsyncRuleEngine } from '../helpers/test-utils';
import {
  expectType,
  TypeValidator,
  TypeSafeTestFactory,
  AdvancedTypeValidator
} from '../helpers/type-testing-utils';

// 模拟规则实现
class MockRule implements Rule {
  constructor(
    public id: string,
    public name: string,
    public priority: number = 100,
    private canHandleNode: (node: Node) => boolean = () => true
  ) {}

  async evaluate(node: Node, context: any): Promise<{ complexity: number; reason?: string }> {
    return { complexity: 1, reason: `Evaluated by ${this.id}` };
  }

  canHandle(node: Node): boolean {
    return this.canHandleNode(node);
  }

  getDependencies(): string[] {
    return [];
  }
}

// 模拟节点
const createMockNode = (type: string): Node => ({
  type,
  span: { start: 0, end: 10, ctxt: 0 }
} as Node);

describe('Complete Async Engine Type Safety Tests', () => {
  let engine: CompleteAsyncRuleEngineImpl;
  let engineValidator: TypeValidator<AsyncRuleEngine>;
  let registryValidator: TypeValidator<RuleRegistry>;

  const createMockRule = (id: string = 'test-rule'): Rule => ({
    id,
    name: `Test Rule ${id}`,
    priority: 1,
    evaluate: vi.fn().mockResolvedValue({
      ruleId: id,
      complexity: 1,
      isExempted: false,
      shouldIncreaseNesting: true,
      reason: 'Test evaluation',
      suggestions: [],
      metadata: { nodeType: 'test' },
      executionTime: 10,
      cacheHit: false
    }),
    canHandle: vi.fn().mockReturnValue(true),
    getDependencies: vi.fn().mockReturnValue([])
  });

  const createMockNode = (): Node => ({
    type: 'Identifier',
    span: { start: 0, end: 0, ctxt: 0 }
  } as Node);

  const createMockModule = (): Module => ({
    type: 'Module',
    span: { start: 0, end: 0, ctxt: 0 },
    body: [],
    shebang: null
  });

  const createMockAnalysisContext = (): AnalysisContext => ({
    filePath: '/test/file.ts',
    fileContent: 'test content',
    ast: createMockModule(),
    functionName: 'testFunction',
    nestingLevel: 0,
    config: {} as ResolvedEngineConfig,
    jsxMode: 'standard',
    rules: { core: [], jsx: [], plugins: [] },
    cache: {} as any,
    metrics: {
      totalNodes: 0,
      processedNodes: 0,
      cacheHits: 0,
      cacheMisses: 0,
      ruleExecutions: 0,
      parallelExecutions: 0,
      errors: 0
    },
    plugins: [],
    customData: new Map()
  });

  beforeEach(() => {
    engine = new CompleteAsyncRuleEngineImpl({});
    engineValidator = expectType<AsyncRuleEngine>('AsyncRuleEngineValidator');
    registryValidator = expectType<RuleRegistry>('RuleRegistryValidator');
  });

  afterEach(() => {
    engine.shutdown();
  });

  describe('AsyncRuleEngine Interface Type Safety', () => {
    test('should validate AsyncRuleEngine interface structure', () => {
      // 编译时类型检查
      expectType<AsyncRuleEngine>().toBeAssignableTo<{
        analyzeNode(node: Node, context: AnalysisContext): Promise<NodeAnalysis>;
        analyzeFunction(func: Node): Promise<FunctionAnalysis>;
        analyzeFile(ast: Module): Promise<FileAnalysis>;
        analyzeFiles(filePaths: string[]): Promise<Map<string, FileAnalysis>>;
        analyzeCode(code: string, filePath?: string): Promise<FileAnalysis>;
        registerRule(rule: Rule): void;
        unregisterRule(ruleId: string): void;
        getMetrics(): EngineMetrics;
        clearCache(): void;
        updateConfig(newConfig: Partial<ResolvedEngineConfig>): void;
        getConfig(): ResolvedEngineConfig;
        getRuleStatistics(): any;
        getExecutionLoad(): any;
        preWarmCache(nodes: Node[]): Promise<void>;
        shutdown(): void;
      }>();

      engineValidator.toAccept(engine);
      expect(engine).toBeInstanceOf(CompleteAsyncRuleEngineImpl);
    });

    test('should validate async analysis methods return correct types', async () => {
      const mockNode = createMockNode();
      const mockContext = createMockAnalysisContext();
      const mockModule = createMockModule();

      // Mock internal methods to avoid actual analysis
      vi.spyOn(engine as any, 'analyzeNodeInternal').mockResolvedValue({
        node: mockNode,
        complexity: 1,
        appliedRules: [],
        exemptions: [],
        children: [],
        aggregatedComplexity: 1,
        analysisTime: 10,
        cacheUtilization: 0
      });

      try {
        const nodeResult = await engine.analyzeNode(mockNode, mockContext);
        expectType<NodeAnalysis>().toAccept(nodeResult);
        
        expect(nodeResult.node).toBeDefined();
        expect(typeof nodeResult.complexity).toBe('number');
        expect(Array.isArray(nodeResult.appliedRules)).toBe(true);
      } catch (error) {
        // Expected in this mock scenario
      }

      try {
        const funcResult = await engine.analyzeFunction(mockNode);
        expectType<FunctionAnalysis>().toAccept(funcResult);
      } catch (error) {
        // Expected in mock scenario
      }

      try {
        const fileResult = await engine.analyzeFile(mockModule);
        expectType<FileAnalysis>().toAccept(fileResult);
      } catch (error) {
        // Expected in mock scenario
      }
    });

    test('should validate rule management methods', () => {
      const rule = createMockRule();
      
      // 测试规则注册
      expect(() => engine.registerRule(rule)).not.toThrow();
      
      // 测试规则查询
      expect(typeof engine.hasRule).toBe('function');
      expect(typeof engine.getRule).toBe('function');
      expect(typeof engine.getAllRules).toBe('function');
      
      // 测试规则注销
      expect(() => engine.unregisterRule(rule.id)).not.toThrow();
    });
  });

  describe('RuleRegistry Interface Type Safety', () => {
    test('should validate RuleRegistry interface structure', () => {
      // 编译时类型检查
      expectType<RuleRegistry>().toBeAssignableTo<{
        getRulesForNode(node: Node): Rule[];
        getRulesByPriority(priority?: number): Rule[];
        getRulesByCategory(category: string): Rule[];
        getAllRules(): Rule[];
        hasRule(ruleId: string): boolean;
        getRule(ruleId: string): Rule | null;
        registerRule(rule: Rule): void;
        unregisterRule(ruleId: string): void;
        resolveDependencies(ruleId: string): Rule[];
        validateDependencies(ruleId: string): any;
        loadPlugin(pluginPath: string): Promise<void>;
        unloadPlugin(pluginId: string): void;
        getLoadedPlugins(): any[];
        isRuleEnabled(ruleId: string): boolean;
        enableRule(ruleId: string): void;
        disableRule(ruleId: string): void;
        detectConflicts(): any[];
        resolveConflict(conflictId: string, resolution: any): void;
      }>();

      registryValidator.toAccept(engine);
      expect(engine).toBeInstanceOf(CompleteAsyncRuleEngineImpl);
    });

    test('should validate rule query methods return correct types', () => {
      const rule1 = createMockRule('rule-1');
      const rule2 = createMockRule('rule-2');
      const mockNode = createMockNode();

      engine.registerRule(rule1);
      engine.registerRule(rule2);

      // 测试按节点获取规则
      const rulesForNode = engine.getRulesForNode(mockNode);
      expect(Array.isArray(rulesForNode)).toBe(true);
      rulesForNode.forEach(rule => {
        expectType<Rule>().toAccept(rule);
      });

      // 测试按优先级获取规则
      const rulesByPriority = engine.getRulesByPriority(1);
      expect(Array.isArray(rulesByPriority)).toBe(true);
      rulesByPriority.forEach(rule => {
        expect(rule.priority).toBe(1);
      });

      // 测试获取所有规则
      const allRules = engine.getAllRules();
      expect(Array.isArray(allRules)).toBe(true);
      expect(allRules.length).toBeGreaterThanOrEqual(2);
    });

    test('should validate rule existence checks', () => {
      const rule = createMockRule();
      
      expect(engine.hasRule(rule.id)).toBe(false);
      
      engine.registerRule(rule);
      expect(engine.hasRule(rule.id)).toBe(true);
      
      const retrievedRule = engine.getRule(rule.id);
      expect(retrievedRule).not.toBeNull();
      if (retrievedRule) {
        expectType<Rule>().toAccept(retrievedRule);
        expect(retrievedRule.id).toBe(rule.id);
      }
      
      engine.unregisterRule(rule.id);
      expect(engine.hasRule(rule.id)).toBe(false);
      expect(engine.getRule(rule.id)).toBeNull();
    });
  });

  describe('Enhanced Rule Operations Type Safety', () => {
    test('should validate rule registration with type checking', () => {
      const validRule = createMockRule();
      
      // 正常注册应该成功
      expect(() => engine.registerRule(validRule)).not.toThrow();
      
      // 重复注册应该抛出错误
      expect(() => engine.registerRule(validRule)).toThrow();
      
      // 注册无效规则应该抛出TypeError
      const invalidRule = { id: 'invalid' } as any;
      expect(() => engine.registerRule(invalidRule)).toThrow(TypeError);
    });

    test('should validate rule unregistration with existence check', () => {
      const rule = createMockRule();
      
      // 注销不存在的规则应该抛出错误
      expect(() => engine.unregisterRule('non-existent')).toThrow();
      
      // 注册后注销应该成功
      engine.registerRule(rule);
      expect(() => engine.unregisterRule(rule.id)).not.toThrow();
    });

    test('should validate query rules with complex filters', () => {
      const rule1 = createMockRule('rule-1');
      const rule2 = { ...createMockRule('rule-2'), priority: 2 };
      const mockNode = createMockNode();

      engine.registerRule(rule1);
      engine.registerRule(rule2);

      // 测试多条件查询
      const queryResults = engine.queryRules({
        priority: 1,
        enabled: true,
        node: mockNode
      });

      expect(Array.isArray(queryResults)).toBe(true);
      queryResults.forEach(rule => {
        expectType<Rule>().toAccept(rule);
        expect(rule.priority).toBe(1);
      });

      // 测试名称模式过滤
      const patternResults = engine.queryRules({
        namePattern: /rule-1/
      });

      expect(Array.isArray(patternResults)).toBe(true);
      patternResults.forEach(rule => {
        expect(rule.name).toMatch(/rule-1/);
      });
    });
  });

  describe('Rule Information and Dependencies Type Safety', () => {
    test('should validate getRuleInfo return type', () => {
      const rule = createMockRule();
      engine.registerRule(rule);

      const ruleInfo = engine.getRuleInfo(rule.id);
      expect(ruleInfo).not.toBeNull();

      if (ruleInfo) {
        // 验证返回类型结构
        expect(ruleInfo.rule).toBe(rule);
        expect(typeof ruleInfo.isEnabled).toBe('boolean');
        expect(Array.isArray(ruleInfo.dependencies)).toBe(true);
        expect(Array.isArray(ruleInfo.dependents)).toBe(true);
        expect(Array.isArray(ruleInfo.conflicts)).toBe(true);
        
        ruleInfo.dependencies.forEach(dep => {
          expectType<Rule>().toAccept(dep);
        });
      }

      // 不存在的规则应该返回null
      const nonExistentInfo = engine.getRuleInfo('non-existent');
      expect(nonExistentInfo).toBeNull();
    });

    test('should validate dependency resolution', () => {
      const rule = createMockRule();
      engine.registerRule(rule);

      const dependencies = engine.resolveDependencies(rule.id);
      expect(Array.isArray(dependencies)).toBe(true);
      
      dependencies.forEach(dep => {
        expectType<Rule>().toAccept(dep);
      });
    });
  });

  describe('Batch Operations Type Safety', () => {
    test('should validate batch operation structure and results', () => {
      const rule1 = createMockRule('batch-rule-1');
      const rule2 = createMockRule('batch-rule-2');

      const operations = [
        { type: 'register' as const, rule: rule1 },
        { type: 'register' as const, rule: rule2 },
        { type: 'enable' as const, ruleId: rule1.id },
        { type: 'disable' as const, ruleId: rule2.id }
      ];

      const result = engine.batchOperation(operations);

      // 验证返回类型结构
      expect(typeof result.success).toBe('number');
      expect(Array.isArray(result.failed)).toBe(true);
      
      result.failed.forEach(failure => {
        expect(failure.operation).toBeDefined();
        expect(typeof failure.error).toBe('string');
      });

      expect(result.success).toBeGreaterThan(0);
    });

    test('should handle invalid batch operations gracefully', () => {
      const invalidOperations = [
        { type: 'register' as const }, // missing rule
        { type: 'enable' as const }, // missing ruleId
        { type: 'unknown' as any, ruleId: 'test' }
      ];

      const result = engine.batchOperation(invalidOperations);
      
      expect(result.success).toBe(0);
      expect(result.failed.length).toBe(3);
    });
  });

  describe('Enhanced Statistics and Monitoring Type Safety', () => {
    test('should validate enhanced rule statistics structure', () => {
      const rule = createMockRule();
      engine.registerRule(rule);

      const stats = engine.getEnhancedRuleStatistics();

      // 验证基础统计
      expect(stats.basic).toBeDefined();
      
      // 验证详细统计结构
      expect(stats.detailed.rulesByPriority).toBeInstanceOf(Map);
      expect(stats.detailed.rulesByCategory).toBeInstanceOf(Map);
      expect(Array.isArray(stats.detailed.enabledRules)).toBe(true);
      expect(Array.isArray(stats.detailed.disabledRules)).toBe(true);
      expect(Array.isArray(stats.detailed.conflictingRules)).toBe(true);
      expect(stats.detailed.dependencyGraph).toBeInstanceOf(Map);

      // 验证规则数组类型
      stats.detailed.enabledRules.forEach(rule => {
        expectType<Rule>().toAccept(rule);
      });

      stats.detailed.disabledRules.forEach(rule => {
        expectType<Rule>().toAccept(rule);
      });
    });

    test('should validate metrics collection', () => {
      const metrics = engine.getMetrics();
      
      expectType<EngineMetrics>().toAccept(metrics);
      
      expect(typeof metrics.totalAnalysisTime).toBe('number');
      expect(typeof metrics.filesProcessed).toBe('number');
      expect(typeof metrics.functionsAnalyzed).toBe('number');
      expect(typeof metrics.rulesExecuted).toBe('number');
      expect(typeof metrics.cacheHitRate).toBe('number');
      expect(typeof metrics.parallelEfficiency).toBe('number');
      expect(typeof metrics.memoryUsage).toBe('object');
    });
  });

  describe('Configuration Management Type Safety', () => {
    test('should validate configuration updates', () => {
      const currentConfig = engine.getConfig();
      expectType<ResolvedEngineConfig>().toAccept(currentConfig);

      // 验证配置结构
      expect(currentConfig.rules).toBeDefined();
      expect(currentConfig.performance).toBeDefined();
      expect(currentConfig.output).toBeDefined();
      expect(currentConfig.plugins).toBeDefined();

      const partialConfig: Partial<ResolvedEngineConfig> = {
        performance: {
          maxConcurrency: 8,
          cacheSize: 20000,
          streamingThreshold: 2000,
          enableParallelExecution: true
        }
      };

      expect(() => engine.updateConfig(partialConfig)).not.toThrow();
      
      const updatedConfig = engine.getConfig();
      expect(updatedConfig.performance.maxConcurrency).toBe(8);
    });
  });

  describe('Integration with Type Testing Utils', () => {
    test('should work with TypeValidator for complex engine validations', async () => {
      const rule = createMockRule();
      engine.registerRule(rule);

      // 使用类型守卫测试引擎接口
      const engineGuard = await TypeSafeTestFactory.generateTypeGuardTests(
        'CompleteAsyncRuleEngine',
        [engine],
        [null, undefined, {}, 'string', 123]
      );

      const guardResult = await engineValidator.validateTypeGuard(engineGuard);
      expect(guardResult.passed).toBe(true);
    });

    test('should validate rule structure with advanced type checking', async () => {
      const rule = createMockRule();

      const structuralResult = await AdvancedTypeValidator.validateStructuralCompatibility(
        rule,
        (value: unknown): value is Rule => {
          return typeof value === 'object' && value !== null &&
                 'id' in value && 'name' in value && 'priority' in value &&
                 'evaluate' in value && 'canHandle' in value && 'getDependencies' in value;
        },
        'Rule Structural Compatibility'
      );

      expect(structuralResult.passed).toBe(true);
    });

    test('should generate comprehensive engine validation reports', async () => {
      const rules = [
        createMockRule('report-rule-1'),
        createMockRule('report-rule-2'),
        createMockRule('report-rule-3')
      ];

      for (const rule of rules) {
        engine.registerRule(rule);
      }

      engineValidator.toAccept(engine);
      registryValidator.toAccept(engine);

      const engineSummary = engineValidator.getSummary();
      const registrySummary = registryValidator.getSummary();

      expect(engineSummary.overallSuccess).toBe(true);
      expect(registrySummary.overallSuccess).toBe(true);
      
      expect(engineSummary.totalTests).toBeGreaterThan(0);
      expect(registrySummary.totalTests).toBeGreaterThan(0);
    });
  });

  describe('Error Handling and Type Safety', () => {
    test('should handle type validation errors gracefully', () => {
      const invalidRule = {
        id: 123, // should be string
        name: null, // should be string
        priority: 'high', // should be number
        evaluate: 'not a function',
        canHandle: 'not a function',
        getDependencies: 'not a function'
      } as any;

      expect(() => engine.registerRule(invalidRule)).toThrow(TypeError);
    });

    test('should maintain type safety during error conditions', () => {
      const rule = createMockRule();
      
      // 正常注册
      engine.registerRule(rule);
      
      // 模拟错误条件
      const mockNode = createMockNode();
      const mockContext = createMockAnalysisContext();
      
      // 即使在错误条件下，类型仍应保持安全
      engine.analyzeNode(mockNode, mockContext).catch(error => {
        expect(error).toBeInstanceOf(Error);
      });
    });
  });

  describe('Performance and Concurrency Type Safety', () => {
    test('should handle concurrent operations safely', async () => {
      const concurrentOperations: Promise<void>[] = [];
      
      // 模拟多个并发规则注册
      for (let i = 0; i < 10; i++) {
        concurrentOperations.push(
          Promise.resolve().then(() => {
            const rule = createMockRule(`concurrent-rule-${i}`);
            engine.registerRule(rule);
            
            // 验证规则属性
            expect(rule.id).toBe(`concurrent-rule-${i}`);
            expect(typeof rule.evaluate).toBe('function');
            expect(typeof rule.canHandle).toBe('function');
          })
        );
      }

      await Promise.all(concurrentOperations);
      
      const allRules = engine.getAllRules();
      expect(allRules.length).toBeGreaterThanOrEqual(10);
    });

    test('should maintain type safety during cache operations', async () => {
      const mockNodes = Array.from({ length: 5 }, () => createMockNode());
      
      // 预热缓存操作应该保持类型安全
      try {
        await engine.preWarmCache(mockNodes);
      } catch (error) {
        // Expected in mock scenario
      }

      // 清除缓存操作
      expect(() => engine.clearCache()).not.toThrow();
    });
  });

  // 保留原有测试用例以确保向后兼容
  describe('Legacy Test Compatibility', () => {
    let mockRule1: Rule;
    let mockRule2: Rule;
    let mockRule3: Rule;

    beforeEach(() => {
      mockRule1 = createMockRule('core.test-rule-1');
      mockRule2 = createMockRule('jsx.test-rule-2');
      mockRule3 = createMockRule('logical.test-rule-3');
    });

    test('registerRule 应该注册规则并进行类型验证', () => {
      // 正常注册
      expect(() => engine.registerRule(mockRule1)).not.toThrow();
      expect(engine.hasRule(mockRule1.id)).toBe(true);

      // 重复注册应该失败
      expect(() => engine.registerRule(mockRule1)).toThrow(
        /already exists/
      );

      // 无效规则应该失败
      const invalidRule = { id: 'invalid' } as any;
      expect(() => engine.registerRule(invalidRule)).toThrow(
        /Invalid rule structure/
      );
    });

    test('其他原有测试用例应该继续工作', () => {
      // 简单验证原有功能仍然正常
      engine.registerRule(mockRule1);
      engine.registerRule(mockRule2);
      
      expect(engine.getAllRules()).toHaveLength(2);
      expect(engine.getRule(mockRule1.id)).toBe(mockRule1);
      
      engine.unregisterRule(mockRule1.id);
      expect(engine.hasRule(mockRule1.id)).toBe(false);
    });
  });
});

// 模拟规则实现（保持向后兼容）
class LegacyMockRule implements Rule {
  constructor(
    public id: string,
    public name: string,
    public priority: number = 100,
    private canHandleNode: (node: Node) => boolean = () => true
  ) {}

  async evaluate(node: Node, context: any): Promise<any> {
    return { 
      ruleId: this.id,
      complexity: 1, 
      isExempted: false,
      shouldIncreaseNesting: true,
      reason: `Evaluated by ${this.id}`,
      suggestions: [],
      metadata: { nodeType: node.type },
      executionTime: 10,
      cacheHit: false
    };
  }

  canHandle(node: Node): boolean {
    return this.canHandleNode(node);
  }

  getDependencies(): string[] {
    return [];
  }
}

// 模拟节点（保持向后兼容）
const createLegacyMockNode = (type: string): Node => ({
  type,
  span: { start: 0, end: 10, ctxt: 0 }
} as Node);