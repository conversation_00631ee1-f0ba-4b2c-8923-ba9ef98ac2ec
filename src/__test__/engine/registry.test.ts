/**
 * 规则注册表测试
 * 验证规则注册、发现、依赖解析和冲突检测功能
 */

import { describe, it, expect, beforeEach } from 'vitest';
import type { Node } from '@swc/core';
import { RuleRegistryImpl } from '../../engine/registry';
import { CoreComplexityRule } from '../../rules/core-complexity-rule';
import { JSXStructuralExemptionRule } from '../../rules/jsx-structural-exemption';
import { LogicalOperatorRule } from '../../rules/logical-operator-rule';
import type { Rule, AnalysisContext, RuleResult } from '../../engine/types';

// 测试用模拟规则
class MockRule implements Rule {
  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly priority: number,
    private dependencies: string[] = []
  ) {}

  canHandle(node: Node): boolean {
    return true;
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return {
      ruleId: this.id,
      complexity: 1,
      isExempted: false,
      shouldIncreaseNesting: false,
      reason: 'Mock rule evaluation',
      suggestions: [],
      metadata: {},
      executionTime: 0,
      cacheHit: false,
    };
  }

  getDependencies(): string[] {
    return this.dependencies;
  }
}

describe('RuleRegistry', () => {
  let registry: RuleRegistryImpl;

  beforeEach(() => {
    registry = new RuleRegistryImpl();
  });

  describe('Rule Registration', () => {
    it('should register a rule successfully', () => {
      const rule = new MockRule('test.rule', 'Test Rule', 100);
      
      expect(() => registry.registerRule(rule)).not.toThrow();
      expect(registry.getRule('test.rule')).toBe(rule);
      expect(registry.isRuleEnabled('test.rule')).toBe(true);
    });

    it('should prevent duplicate rule registration', () => {
      const rule1 = new MockRule('test.rule', 'Test Rule 1', 100);
      const rule2 = new MockRule('test.rule', 'Test Rule 2', 200);
      
      registry.registerRule(rule1);
      
      expect(() => registry.registerRule(rule2)).toThrow(
        "Rule with id 'test.rule' is already registered"
      );
    });

    it('should unregister a rule successfully', () => {
      const rule = new MockRule('test.rule', 'Test Rule', 100);
      
      registry.registerRule(rule);
      expect(registry.getRule('test.rule')).toBe(rule);
      
      registry.unregisterRule('test.rule');
      expect(registry.getRule('test.rule')).toBeNull();
    });

    it('should prevent unregistering non-existent rule', () => {
      expect(() => registry.unregisterRule('non.existent')).toThrow(
        "Rule with id 'non.existent' not found"
      );
    });
  });

  describe('Rule Discovery', () => {
    it('who can handle a specific node', () => {
      const coreRule = new CoreComplexityRule();
      const jsxRule = new JSXStructuralExemptionRule();
      
      registry.registerRule(coreRule);
      registry.registerRule(jsxRule);
      
      // 模拟IF语句节点
      const ifNode: Node = { type: 'IfStatement', span: { start: 0, end: 10, ctxt: 0 } } as any;
      const applicableRules = registry.getRulesForNode(ifNode);
      
      expect(applicableRules).toHaveLength(1);
      expect(applicableRules[0].id).toBe('core.complexity');
    });

    it('should return rules by priority order', () => {
      const lowPriorityRule = new MockRule('low', 'Low Priority', 100);
      const highPriorityRule = new MockRule('high', 'High Priority', 900);
      const mediumPriorityRule = new MockRule('medium', 'Medium Priority', 500);
      
      registry.registerRule(lowPriorityRule);
      registry.registerRule(highPriorityRule);
      registry.registerRule(mediumPriorityRule);
      
      const rulesByPriority = registry.getRulesByPriority();
      expect(rulesByPriority).toHaveLength(3);
      expect(rulesByPriority[0].id).toBe('high');
      expect(rulesByPriority[1].id).toBe('medium');
      expect(rulesByPriority[2].id).toBe('low');
    });

    it('should return rules by category', () => {
      const coreRule = new CoreComplexityRule();
      const jsxRule = new JSXStructuralExemptionRule();
      
      registry.registerRule(coreRule);
      registry.registerRule(jsxRule);
      
      const coreRules = registry.getRulesByCategory('core');
      const jsxRules = registry.getRulesByCategory('jsx');
      
      expect(coreRules).toHaveLength(1);
      expect(coreRules[0].id).toBe('core.complexity');
      
      expect(jsxRules).toHaveLength(1);
      expect(jsxRules[0].id).toBe('jsx.structural.exemption');
    });
  });

  describe('Dependency Resolution', () => {
    it('should resolve rule dependencies correctly', () => {
      const baseRule = new MockRule('base', 'Base Rule', 100);
      const dependentRule = new MockRule('dependent', 'Dependent Rule', 200, ['base']);
      
      registry.registerRule(baseRule);
      registry.registerRule(dependentRule);
      
      const dependencies = registry.resolveDependencies('dependent');
      expect(dependencies).toHaveLength(1);
      expect(dependencies[0].id).toBe('base');
    });

    it('should detect circular dependencies', () => {
      const rule1 = new MockRule('rule1', 'Rule 1', 100, ['rule2']);
      const rule2 = new MockRule('rule2', 'Rule 2', 200, ['rule1']);
      
      registry.registerRule(rule1);
      registry.registerRule(rule2);
      
      expect(() => registry.resolveDependencies('rule1')).toThrow(
        /Circular dependency detected involving rule/
      );
    });

    it('should validate dependencies and report missing ones', () => {
      const dependentRule = new MockRule('dependent', 'Dependent Rule', 200, ['missing']);
      registry.registerRule(dependentRule);
      
      const validation = registry.validateDependencies('dependent');
      
      expect(validation.isValid).toBe(false);
      expect(validation.missingDependencies).toContain('missing');
      expect(validation.suggestions).toHaveLength(1);
    });

    it('should prevent unregistering rules with dependents', () => {
      const baseRule = new MockRule('base', 'Base Rule', 100);
      const dependentRule = new MockRule('dependent', 'Dependent Rule', 200, ['base']);
      
      registry.registerRule(baseRule);
      registry.registerRule(dependentRule);
      
      expect(() => registry.unregisterRule('base')).toThrow(
        "Cannot unregister rule 'base': it is depended on by rules: dependent"
      );
    });
  });

  describe('Rule State Management', () => {
    it('should enable and disable rules', () => {
      const rule = new MockRule('test.rule', 'Test Rule', 100);
      registry.registerRule(rule);
      
      expect(registry.isRuleEnabled('test.rule')).toBe(true);
      
      registry.disableRule('test.rule');
      expect(registry.isRuleEnabled('test.rule')).toBe(false);
      
      registry.enableRule('test.rule');
      expect(registry.isRuleEnabled('test.rule')).toBe(true);
    });

    it('should not return disabled rules in discovery', () => {
      const rule = new MockRule('test.rule', 'Test Rule', 100);
      registry.registerRule(rule);
      
      let enabledRules = registry.getRulesByPriority();
      expect(enabledRules).toHaveLength(1);
      
      registry.disableRule('test.rule');
      enabledRules = registry.getRulesByPriority();
      expect(enabledRules).toHaveLength(0);
    });
  });

  describe('Conflict Detection', () => {
    it('should detect priority conflicts', () => {
      const rule1 = new MockRule('rule1', 'Rule 1', 100);
      const rule2 = new MockRule('rule2', 'Rule 2', 100); // Same priority
      
      registry.registerRule(rule1);
      registry.registerRule(rule2);
      
      const conflicts = registry.detectConflicts();
      expect(conflicts).toHaveLength(1);
      expect(conflicts[0].type).toBe('priority');
      expect(conflicts[0].involvedRules).toContain('rule1');
      expect(conflicts[0].involvedRules).toContain('rule2');
    });

    it('should resolve conflicts', () => {
      const rule1 = new MockRule('rule1', 'Rule 1', 100);
      const rule2 = new MockRule('rule2', 'Rule 2', 100);
      
      registry.registerRule(rule1);
      registry.registerRule(rule2);
      
      const conflicts = registry.detectConflicts();
      expect(conflicts).toHaveLength(1);
      
      const conflictId = conflicts[0].id;
      registry.resolveConflict(conflictId, {
        type: 'disable',
        target: 'rule2',
      });
      
      const remainingConflicts = registry.detectConflicts();
      expect(remainingConflicts).toHaveLength(0);
      expect(registry.isRuleEnabled('rule2')).toBe(false);
    });
  });

  describe('Statistics', () => {
    it('should provide rule statistics', () => {
      const coreRule = new CoreComplexityRule();
      const jsxRule = new JSXStructuralExemptionRule();
      
      registry.registerRule(coreRule);
      registry.registerRule(jsxRule);
      registry.disableRule(jsxRule.id);
      
      const stats = registry.getStatistics();
      
      expect(stats.totalRegistered).toBe(2);
      // Note: enabledRules tracks all initially enabled rules, disabled tracks explicitly disabled
      expect(stats.enabled).toBe(2); // Both were initially enabled
      expect(stats.disabled).toBe(1); // One was explicitly disabled
      expect(stats.byCategory).toHaveProperty('core');
      expect(stats.byCategory).toHaveProperty('jsx');
    });
  });

  describe('Real Rule Integration', () => {
    it('should work with actual rules from the system', () => {
      const coreRule = new CoreComplexityRule();
      const jsxRule = new JSXStructuralExemptionRule();
      const logicalRule = new LogicalOperatorRule();
      
      // Test registration
      expect(() => {
        registry.registerRule(coreRule);
        registry.registerRule(jsxRule);
        registry.registerRule(logicalRule);
      }).not.toThrow();
      
      // Test discovery
      expect(registry.getAllRules()).toHaveLength(3);
      
      // Test priority ordering
      const rulesByPriority = registry.getRulesByPriority();
      expect(rulesByPriority[0].id).toBe('jsx.structural.exemption'); // Highest priority (1000)
      
      // Test category grouping
      const jsxRules = registry.getRulesByCategory('jsx');
      expect(jsxRules).toHaveLength(1);
      expect(jsxRules[0].id).toBe('jsx.structural.exemption');
    });
  });
});