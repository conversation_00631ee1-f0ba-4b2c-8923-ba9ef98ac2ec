/**
 * 测试插件规则加载功能
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { join } from 'path';
import { writeFileSync, unlinkSync, existsSync } from 'fs';
import { RuleRegistryImpl } from '../../engine/registry';
import type { Rule, AnalysisContext, RuleResult } from '../../engine/types';
import type { Node } from '@swc/core';

const TEMP_PLUGIN_PATH = join(process.cwd(), 'temp-test-plugin.js');

// 创建临时插件文件内容
const createTestPluginContent = (pluginId: string, ruleCount: number = 1) => `
// Test plugin for rule registry
const { BaseRule } = require('./src/rules/base-rule');

class TestPluginRule extends BaseRule {
  constructor(id, name, priority) {
    super();
    this.id = id;
    this.name = name;
    this.priority = priority;
  }

  canHandle(node) {
    return node.type === 'TestNode';
  }

  async evaluate(node, context) {
    return this.createComplexityResult(node, 1, 'Test plugin rule evaluation');
  }
}

const plugin = {
  id: '${pluginId}',
  name: 'Test Plugin',
  version: '1.0.0',
  rules: [
    ${Array.from({ length: ruleCount }, (_, i) => 
      `new TestPluginRule('plugin.${pluginId}.rule${i + 1}', 'Test Rule ${i + 1}', ${100 + i * 10})`
    ).join(',\n    ')}
  ],
  metadata: {
    author: 'Test Author',
    description: 'A test plugin for registry testing'
  },
  
  async onLoad() {
    console.log('Test plugin loaded successfully');
  }
};

module.exports = plugin;
`;

describe('Plugin Loading System', () => {
  let registry: RuleRegistryImpl;

  beforeEach(() => {
    registry = new RuleRegistryImpl();
  });

  afterEach(() => {
    // 清理临时插件文件
    if (existsSync(TEMP_PLUGIN_PATH)) {
      try {
        delete require.cache[require.resolve(TEMP_PLUGIN_PATH)];
        unlinkSync(TEMP_PLUGIN_PATH);
      } catch (error) {
        console.warn('Failed to cleanup temp plugin file:', error);
      }
    }
  });

  describe('Plugin Loading', () => {
    it('should load a valid plugin successfully', async () => {
      const pluginContent = createTestPluginContent('test-plugin', 2);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      await expect(async () => {
        await registry.loadPlugin(TEMP_PLUGIN_PATH);
      }).not.toThrow();

      const loadedPlugins = registry.getLoadedPlugins();
      expect(loadedPlugins).toHaveLength(1);
      expect(loadedPlugins[0].id).toBe('test-plugin');
      expect(loadedPlugins[0].rules).toHaveLength(2);
      expect(loadedPlugins[0].isActive).toBe(true);
    });

    it('should register plugin rules to the registry', async () => {
      const pluginContent = createTestPluginContent('test-plugin', 1);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      await registry.loadPlugin(TEMP_PLUGIN_PATH);

      const rule = registry.getRule('plugin.test-plugin.rule1');
      expect(rule).not.toBeNull();
      expect(rule!.name).toBe('Test Rule 1');
      expect(rule!.priority).toBe(100);
    });

    it('should prevent loading the same plugin twice', async () => {
      const pluginContent = createTestPluginContent('duplicate-plugin', 1);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      await registry.loadPlugin(TEMP_PLUGIN_PATH);
      
      await expect(registry.loadPlugin(TEMP_PLUGIN_PATH)).rejects.toThrow(
        "Plugin 'duplicate-plugin' is already loaded"
      );
    });

    it('should handle plugin loading errors gracefully', async () => {
      await expect(registry.loadPlugin('/non/existent/plugin.js')).rejects.toThrow(
        /Failed to load plugin from/
      );
    });

    it('should validate plugin structure', async () => {
      const invalidPluginContent = `
        module.exports = {
          // Missing required fields like id, rules
          name: 'Invalid Plugin'
        };
      `;
      writeFileSync(TEMP_PLUGIN_PATH, invalidPluginContent);

      await expect(registry.loadPlugin(TEMP_PLUGIN_PATH)).rejects.toThrow(
        /Invalid plugin.*must export a valid plugin object/
      );
    });
  });

  describe('Plugin Unloading', () => {
    it('should unload a plugin and its rules', async () => {
      const pluginContent = createTestPluginContent('unload-test', 2);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      await registry.loadPlugin(TEMP_PLUGIN_PATH);
      
      expect(registry.getRule('plugin.unload-test.rule1')).not.toBeNull();
      expect(registry.getRule('plugin.unload-test.rule2')).not.toBeNull();

      registry.unloadPlugin('unload-test');

      expect(registry.getRule('plugin.unload-test.rule1')).toBeNull();
      expect(registry.getRule('plugin.unload-test.rule2')).toBeNull();
      expect(registry.getLoadedPlugins()).toHaveLength(0);
    });

    it('should handle unloading non-existent plugin', () => {
      expect(() => registry.unloadPlugin('non-existent')).toThrow(
        "Plugin 'non-existent' is not loaded"
      );
    });
  });

  describe('Plugin Management', () => {
    it('should track plugin metadata', async () => {
      const pluginContent = createTestPluginContent('metadata-test', 1);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      await registry.loadPlugin(TEMP_PLUGIN_PATH);

      const plugins = registry.getLoadedPlugins();
      const plugin = plugins[0];

      expect(plugin.name).toBe('Test Plugin');
      expect(plugin.version).toBe('1.0.0');
      expect(plugin.metadata?.author).toBe('Test Author');
      expect(plugin.metadata?.description).toBe('A test plugin for registry testing');
      expect(plugin.loadTime).toBeGreaterThan(0);
    });

    it('should update statistics with plugin rules', async () => {
      const pluginContent = createTestPluginContent('stats-test', 3);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      const initialStats = registry.getStatistics();
      expect(initialStats.pluginRules).toBe(0);

      await registry.loadPlugin(TEMP_PLUGIN_PATH);

      const updatedStats = registry.getStatistics();
      expect(updatedStats.pluginRules).toBe(3);
      expect(updatedStats.totalRegistered).toBe(3);
    });

    it('should handle plugin rule registration failures gracefully', async () => {
      // 创建一个会导致规则注册失败的插件（重复ID）
      const pluginContent = createTestPluginContent('conflict-test', 1);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      // 先注册一个同名规则
      const conflictRule = {
        id: 'plugin.conflict-test.rule1',
        name: 'Existing Rule',
        priority: 200,
        canHandle: () => true,
        evaluate: async () => ({
          ruleId: 'plugin.conflict-test.rule1',
          complexity: 0,
          isExempted: false,
          shouldIncreaseNesting: false,
          reason: 'Existing rule',
          suggestions: [],
          metadata: {},
          executionTime: 0,
          cacheHit: false,
        }),
        getDependencies: () => [],
      } as Rule;

      registry.registerRule(conflictRule);

      // 插件加载应该成功，但规则注册会失败（在控制台输出警告）
      await expect(async () => {
        await registry.loadPlugin(TEMP_PLUGIN_PATH);
      }).not.toThrow();

      const plugins = registry.getLoadedPlugins();
      expect(plugins).toHaveLength(1);
      expect(plugins[0].rules).toHaveLength(0); // 没有成功注册的规则
    });
  });

  describe('Plugin Integration with Rule Discovery', () => {
    it('should include plugin rules in rule discovery', async () => {
      const pluginContent = createTestPluginContent('discovery-test', 2);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      await registry.loadPlugin(TEMP_PLUGIN_PATH);

      const allRules = registry.getAllRules();
      expect(allRules).toHaveLength(2);

      const rulesByPriority = registry.getRulesByPriority();
      expect(rulesByPriority[0].priority).toBe(110); // Higher priority rule first
      expect(rulesByPriority[1].priority).toBe(100);
    });

    it('should categorize plugin rules correctly', async () => {
      const pluginContent = createTestPluginContent('category-test', 1);
      writeFileSync(TEMP_PLUGIN_PATH, pluginContent);

      await registry.loadPlugin(TEMP_PLUGIN_PATH);

      const pluginRules = registry.getRulesByCategory('plugin');
      expect(pluginRules).toHaveLength(1);
      expect(pluginRules[0].id).toBe('plugin.category-test.rule1');
    });
  });
});