/**
 * ExecutionPool单元测试
 * 测试并行执行池的核心功能
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import type { Node } from '@swc/core';
import { ParallelExecutionPool, type ExecutionTask } from '../../engine/execution-pool';
import type { Rule, RuleResult, AnalysisContext, ExecutionOptions } from '../../engine/types';

// Mock规则实现
class MockRule implements Rule {
  constructor(
    public readonly id: string,
    public readonly name: string,
    public readonly priority: number = 100,
    private executionTime: number = 50,
    private shouldFail: boolean = false,
    private dependencies: string[] = []
  ) {}

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    // 模拟执行时间
    await new Promise(resolve => setTimeout(resolve, this.executionTime));
    
    if (this.shouldFail) {
      throw new Error(`Rule ${this.id} failed`);
    }

    return {
      ruleId: this.id,
      complexity: 1,
      isExempted: false,
      shouldIncreaseNesting: false,
      reason: `Mock rule ${this.id} executed`,
      suggestions: [],
      metadata: { nodeType: node.type },
      executionTime: this.executionTime,
      cacheHit: false,
    };
  }

  canHandle(node: Node): boolean {
    return true;
  }

  getDependencies(): string[] {
    return this.dependencies;
  }
}

// Mock节点
const mockNode: Node = {
  type: 'IfStatement',
  span: { start: 0, end: 10, ctxt: 0 },
} as Node;

// Mock分析上下文
const mockContext: AnalysisContext = {
  filePath: '/test.ts',
  fileContent: 'if (true) {}',
  ast: {} as any,
  functionName: 'testFunction',
  nestingLevel: 0,
  config: {} as any,
  jsxMode: 'standard',
  rules: { core: [], jsx: [], plugins: [] },
  cache: {} as any,
  metrics: {
    totalNodes: 0,
    processedNodes: 0,
    cacheHits: 0,
    cacheMisses: 0,
    ruleExecutions: 0,
    parallelExecutions: 0,
    errors: 0,
  },
  plugins: [],
  customData: new Map(),
};

describe('ParallelExecutionPool', () => {
  let executionPool: ParallelExecutionPool;
  let executionOptions: ExecutionOptions;

  beforeEach(() => {
    executionOptions = {
      maxConcurrency: 4,
      timeout: 5000,
      enableProfiling: false,
      isolateErrors: true,
    };
    
    executionPool = new ParallelExecutionPool(executionOptions);
  });

  afterEach(() => {
    executionPool.shutdown();
  });

  describe('基本功能测试', () => {
    test('应该能够创建执行池', () => {
      expect(executionPool).toBeDefined();
      expect(executionPool.getCurrentLoad().totalCapacity).toBe(4);
    });

    test('应该能够执行单个规则', async () => {
      const rule = new MockRule('test-rule', 'Test Rule');
      const results = await executionPool.executeRules([rule], mockNode, mockContext);
      
      expect(results).toHaveLength(1);
      expect(results[0].ruleId).toBe('test-rule');
      expect(results[0].complexity).toBe(1);
    });

    test('应该能够并行执行多个规则', async () => {
      const rules = [
        new MockRule('rule-1', 'Rule 1', 100, 100),
        new MockRule('rule-2', 'Rule 2', 200, 100),
        new MockRule('rule-3', 'Rule 3', 300, 100),
      ];

      const startTime = Date.now();
      const results = await executionPool.executeRules(rules, mockNode, mockContext);
      const executionTime = Date.now() - startTime;

      expect(results).toHaveLength(3);
      // 并行执行应该比串行执行快
      expect(executionTime).toBeLessThan(250); // 少于串行执行时间(300ms)
    });
  });

  describe('负载均衡测试', () => {
    test('应该能够动态调整工作器数量', async () => {
      // 创建大量任务测试自动扩展
      const rules = Array.from({ length: 10 }, (_, i) => 
        new MockRule(`rule-${i}`, `Rule ${i}`, 100, 200)
      );

      const initialLoad = executionPool.getCurrentLoad();
      const promise = executionPool.executeRules(rules, mockNode, mockContext);
      
      // 等待一段时间让系统调整
      await new Promise(resolve => setTimeout(resolve, 50));
      
      const loadDuringExecution = executionPool.getCurrentLoad();
      
      await promise;
      
      // 执行期间应该有工作器活跃
      expect(loadDuringExecution.activeWorkers).toBeGreaterThan(0);
    });

    test('应该能够处理工作器负载均衡', async () => {
      const stats = executionPool.getStatistics();
      expect(stats.workers.length).toBeGreaterThan(0);
      
      // 所有工作器初始状态应该是空闲的
      expect(stats.workers.every(w => !w.isActive)).toBe(true);
    });
  });

  describe('依赖管理测试', () => {
    test('应该能够处理规则依赖关系', async () => {
      const rule1 = new MockRule('rule-1', 'Rule 1', 100, 50, false, []);
      const rule2 = new MockRule('rule-2', 'Rule 2', 200, 50, false, ['rule-1']);
      const rule3 = new MockRule('rule-3', 'Rule 3', 300, 50, false, ['rule-2']);

      const results = await executionPool.executeRules([rule3, rule1, rule2], mockNode, mockContext);
      
      expect(results).toHaveLength(3);
      // 所有规则都应该成功执行
      expect(results.every(r => !r.isExempted)).toBe(true);
    });

    test('应该能够检测循环依赖', async () => {
      const rule1 = new MockRule('rule-1', 'Rule 1', 100, 50, false, ['rule-2']);
      const rule2 = new MockRule('rule-2', 'Rule 2', 200, 50, false, ['rule-1']);

      // 循环依赖应该被检测并处理
      const results = await executionPool.executeRules([rule1, rule2], mockNode, mockContext);
      
      expect(results).toHaveLength(2);
    });
  });

  describe('错误处理测试', () => {
    test('应该能够处理规则执行失败', async () => {
      const goodRule = new MockRule('good-rule', 'Good Rule');
      const badRule = new MockRule('bad-rule', 'Bad Rule', 100, 50, true);

      const results = await executionPool.executeRules([goodRule, badRule], mockNode, mockContext);
      
      expect(results).toHaveLength(2);
      
      // 好的规则应该成功执行
      const goodResult = results.find(r => r.ruleId === 'good-rule');
      expect(goodResult?.complexity).toBe(1);
      
      // 失败的规则应该返回默认结果
      const badResult = results.find(r => r.ruleId === 'bad-rule');
      expect(badResult?.complexity).toBe(0);
    });

    test('应该能够隔离错误不影响其他规则', async () => {
      const rules = [
        new MockRule('rule-1', 'Rule 1'),
        new MockRule('failing-rule', 'Failing Rule', 100, 50, true),
        new MockRule('rule-3', 'Rule 3'),
      ];

      const results = await executionPool.executeRules(rules, mockNode, mockContext);
      
      expect(results).toHaveLength(3);
      
      // 成功的规则应该正常执行
      const successfulResults = results.filter(r => r.ruleId !== 'failing-rule');
      expect(successfulResults).toHaveLength(2);
      expect(successfulResults.every(r => r.complexity === 1)).toBe(true);
    });

    test('应该能够处理超时错误', async () => {
      // 创建超时规则
      const slowRule = new MockRule('slow-rule', 'Slow Rule', 100, 6000); // 6秒，超过5秒超时限制
      const fastRule = new MockRule('fast-rule', 'Fast Rule', 100, 100);

      const results = await executionPool.executeRules([slowRule, fastRule], mockNode, mockContext);
      
      expect(results).toHaveLength(2);
      
      // 快速规则应该成功
      const fastResult = results.find(r => r.ruleId === 'fast-rule');
      expect(fastResult?.complexity).toBe(1);
      
      // 慢规则应该超时并返回默认结果
      const slowResult = results.find(r => r.ruleId === 'slow-rule');
      expect(slowResult?.complexity).toBe(0);
    });
  });

  describe('性能监控测试', () => {
    test('应该能够提供执行统计信息', async () => {
      const rules = [
        new MockRule('rule-1', 'Rule 1'),
        new MockRule('rule-2', 'Rule 2'),
      ];

      await executionPool.executeRules(rules, mockNode, mockContext);
      
      const stats = executionPool.getStatistics();
      
      expect(stats.totalTasks).toBeGreaterThan(0);
      expect(stats.completedTasks).toBeGreaterThan(0);
      expect(stats.workers.length).toBeGreaterThan(0);
    });

    test('应该能够监控吞吐量', async () => {
      const rules = Array.from({ length: 5 }, (_, i) => 
        new MockRule(`rule-${i}`, `Rule ${i}`, 100, 100)
      );

      await executionPool.executeRules(rules, mockNode, mockContext);
      
      const stats = executionPool.getStatistics();
      
      // 吞吐量应该大于0
      expect(stats.throughput).toBeGreaterThanOrEqual(0);
    });

    test('应该能够计算错误率', async () => {
      const rules = [
        new MockRule('good-rule-1', 'Good Rule 1'),
        new MockRule('bad-rule', 'Bad Rule', 100, 50, true),
        new MockRule('good-rule-2', 'Good Rule 2'),
      ];

      await executionPool.executeRules(rules, mockNode, mockContext);
      
      const stats = executionPool.getStatistics();
      
      expect(stats.errorRate).toBeGreaterThan(0);
      expect(stats.errorRate).toBeLessThan(1);
    });
  });

  describe('配置管理测试', () => {
    test('应该能够动态调整最大并发数', () => {
      expect(executionPool.getCurrentLoad().totalCapacity).toBe(4);
      
      executionPool.setMaxConcurrency(8);
      expect(executionPool.getCurrentLoad().totalCapacity).toBe(8);
      
      executionPool.setMaxConcurrency(2);
      expect(executionPool.getCurrentLoad().totalCapacity).toBe(2);
    });

    test('应该拒绝无效的并发数设置', () => {
      expect(() => {
        executionPool.setMaxConcurrency(0);
      }).toThrow('Max concurrency must be at least 1');
      
      expect(() => {
        executionPool.setMaxConcurrency(-1);
      }).toThrow('Max concurrency must be at least 1');
    });
  });

  describe('优先级测试', () => {
    test('应该按优先级执行规则', async () => {
      const executionOrder: string[] = [];
      
      // 创建具有不同优先级的规则
      const lowPriority = new MockRule('low', 'Low Priority', 100, 100);
      const highPriority = new MockRule('high', 'High Priority', 500, 100);
      const mediumPriority = new MockRule('medium', 'Medium Priority', 300, 100);
      
      // 重写evaluate方法来记录执行顺序
      const originalEvaluate = MockRule.prototype.evaluate;
      vi.spyOn(MockRule.prototype, 'evaluate').mockImplementation(async function(this: MockRule, node, context) {
        executionOrder.push(this.id);
        return originalEvaluate.call(this, node, context);
      });

      await executionPool.executeRules([lowPriority, highPriority, mediumPriority], mockNode, mockContext);
      
      // 高优先级的规则应该先执行
      expect(executionOrder[0]).toBe('high');
    });
  });
});