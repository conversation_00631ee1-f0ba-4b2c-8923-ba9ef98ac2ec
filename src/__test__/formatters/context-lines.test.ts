import { test, expect, describe, beforeEach, vi } from 'vitest';
import { TextFormatter } from '../../formatters/text';
import { JsonFormatter } from '../../formatters/json';
import type { AnalysisResult, FileResult, FunctionResult } from '../../core/types';
import type { CLIOptions, CognitiveConfig } from '../../config/types';

describe('Context Lines Formatter Integration', () => {
  let mockConfig: CognitiveConfig;
  let mockAnalysisResult: AnalysisResult;

  beforeEach(() => {
    mockConfig = {
      failOnComplexity: 15,
      exclude: [],
      report: {},
      severityMapping: []
    };

    mockAnalysisResult = {
      totalFiles: 1,
      totalFunctions: 1,
      totalComplexity: 5,
      averageComplexity: 5,
      highComplexityFunctions: 0,
      files: [
        {
          path: '/test/example.ts',
          functions: [
            {
              name: 'testFunction',
              complexity: 5,
              startLine: 1,
              endLine: 10,
              details: [
                {
                  type: 'if statement',
                  complexity: 2,
                  line: 3,
                  column: 2,
                  context: 'if condition check',
                  span: { start: { line: 3, column: 2 }, end: { line: 3, column: 15 } }
                },
                {
                  type: 'for loop',
                  complexity: 3,
                  line: 5,
                  column: 4,
                  context: 'iteration logic',
                  span: { start: { line: 5, column: 4 }, end: { line: 7, column: 6 } }
                }
              ]
            }
          ],
          totalComplexity: 5,
          averageComplexity: 5
        }
      ]
    };
  });

  describe('TextFormatter Context Lines', () => {
    let textFormatter: TextFormatter;

    beforeEach(() => {
      textFormatter = new TextFormatter(mockConfig);
    });

    test('应该使用默认的上下文行数 (2行)', async () => {
      const options: CLIOptions = {
        paths: ['/test'],
        details: true,
        showContext: true
      };

      // Mock the generateContextFrame method to verify contextLines parameter
      const generateContextFrameSpy = vi.spyOn(textFormatter as any, 'generateContextFrame')
        .mockResolvedValue('mocked context frame');

      await textFormatter.format(mockAnalysisResult, true, options);

      // Verify that generateContextFrame was called with options containing default contextLines
      expect(generateContextFrameSpy).toHaveBeenCalledWith(
        expect.any(Object), // step
        expect.any(String),  // filePath
        expect.objectContaining({
          details: true,
          showContext: true
        })
      );
    });

    test('应该使用自定义的上下文行数', async () => {
      const options: CLIOptions = {
        paths: ['/test'],
        details: true,
        showContext: true,
        contextLines: 5
      };

      const generateContextFrameSpy = vi.spyOn(textFormatter as any, 'generateContextFrame')
        .mockResolvedValue('mocked context frame with 5 lines');

      await textFormatter.format(mockAnalysisResult, true, options);

      expect(generateContextFrameSpy).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(String),
        expect.objectContaining({
          contextLines: 5
        })
      );
    });

    test('应该在没有showContext时不生成上下文', async () => {
      const options: CLIOptions = {
        paths: ['/test'],
        details: true,
        showContext: false,
        contextLines: 5
      };

      const generateContextFrameSpy = vi.spyOn(textFormatter as any, 'generateContextFrame')
        .mockResolvedValue('should not be called');

      await textFormatter.format(mockAnalysisResult, true, options);

      expect(generateContextFrameSpy).not.toHaveBeenCalled();
    });
  });

  describe('JsonFormatter Context Lines', () => {
    let jsonFormatter: JsonFormatter;

    beforeEach(() => {
      jsonFormatter = new JsonFormatter(mockConfig);
    });

    test('应该在JSON输出中包含默认上下文行数设置', async () => {
      const options: CLIOptions = {
        paths: ['/test'],
        details: true,
        showContext: true
      };

      // Mock the enrichDetailsWithContextAsync method
      const enrichDetailsSpy = vi.spyOn(jsonFormatter as any, 'enrichDetailsWithContextAsync')
        .mockResolvedValue([]);

      const result = jsonFormatter.format(mockAnalysisResult, true, options);
      
      // Parse the JSON to verify structure
      const parsedResult = JSON.parse(result);
      expect(parsedResult).toHaveProperty('files');
      expect(parsedResult.files).toHaveLength(1);
    });

    test('应该使用自定义上下文行数生成代码框架', async () => {
      const options: CLIOptions = {
        paths: ['/test'],
        details: true,
        showContext: true,
        contextLines: 8
      };

      // Mock the code frame generator
      const mockCodeFrameGenerator = {
        generateFrameFromSpan: vi.fn().mockResolvedValue({
          success: true,
          frame: 'mocked frame with 8 lines'
        }),
        generateFrame: vi.fn().mockResolvedValue({
          success: true,
          frame: 'mocked frame with 8 lines'
        })
      };

      // Replace the code frame generator
      (jsonFormatter as any).codeFrameGenerator = mockCodeFrameGenerator;

      jsonFormatter.format(mockAnalysisResult, true, options);

      // Note: Since format is synchronous in JsonFormatter, we can't easily test
      // the async enrichDetailsWithContextAsync call. This would need refactoring
      // to properly test the contextLines parameter passing.
    });

    test('应该在contextLines为0时仍然工作', async () => {
      const options: CLIOptions = {
        paths: ['/test'],
        details: true,
        showContext: true,
        contextLines: 0
      };

      expect(() => {
        jsonFormatter.format(mockAnalysisResult, true, options);
      }).not.toThrow();
    });
  });

  describe('Backward Compatibility', () => {
    test('TextFormatter应该在没有contextLines时使用默认值', async () => {
      const textFormatter = new TextFormatter(mockConfig);
      const options: CLIOptions = {
        paths: ['/test'],
        details: true,
        showContext: true
        // contextLines not specified
      };

      const generateContextFrameSpy = vi.spyOn(textFormatter as any, 'generateContextFrame')
        .mockImplementation(async (step, filePath, opts) => {
          // Verify that the default contextLines (2) is used
          expect(opts?.contextLines ?? 2).toBe(2);
          return 'mocked context frame';
        });

      await textFormatter.format(mockAnalysisResult, true, options);
      expect(generateContextFrameSpy).toHaveBeenCalled();
    });

    test('JsonFormatter应该在没有contextLines时使用默认值', () => {
      const jsonFormatter = new JsonFormatter(mockConfig);
      const options: CLIOptions = {
        paths: ['/test'],
        details: true,
        showContext: true
        // contextLines not specified
      };

      expect(() => {
        const result = jsonFormatter.format(mockAnalysisResult, true, options);
        const parsed = JSON.parse(result);
        expect(parsed).toBeDefined();
      }).not.toThrow();
    });
  });
});