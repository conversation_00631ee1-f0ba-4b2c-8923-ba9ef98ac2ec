import { describe, test, expect } from 'vitest';
import { SmartContextFilter, getDefaultFilterOptions, type FilterOptions } from '../../formatters/smart-filter';
import type { DetailStep, DiagnosticMarker } from '../../core/types';

describe('SmartContextFilter', () => {
  const createFilter = () => new SmartContextFilter();

  const createMockStep = (overrides: Partial<DetailStep> = {}): DetailStep => ({
    line: 1,
    column: 1,
    increment: 1,
    cumulative: 1,
    ruleId: 'test-rule',
    description: 'Test description',
    nestingLevel: 0,
    ...overrides
  });

  describe('filterDetailSteps', () => {
    test('should not filter when forceShowAll is true', () => {
      const filter = createFilter();
      const steps = Array.from({ length: 20 }, (_, i) => createMockStep({ line: i + 1 }));
      const options: FilterOptions = {
        minComplexityIncrement: 1,
        maxContextItems: 5,
        forceShowAll: true
      };

      const result = filter.filterDetailSteps(steps, options);
      expect(result).toHaveLength(20);
      expect(result).toEqual(steps);
    });

    test('should not filter when steps count is within limit', () => {
      const filter = createFilter();
      const steps = Array.from({ length: 5 }, (_, i) => createMockStep({ line: i + 1 }));
      const options: FilterOptions = {
        minComplexityIncrement: 1,
        maxContextItems: 10,
        forceShowAll: false
      };

      const result = filter.filterDetailSteps(steps, options);
      expect(result).toHaveLength(5);
      expect(result).toEqual(steps);
    });

    test('should apply smart filtering when steps exceed limit', () => {
      const filter = createFilter();
      const steps = [
        createMockStep({ line: 1, increment: 3, ruleId: 'if-statement' }), // High importance
        createMockStep({ line: 2, increment: 0, ruleId: 'other' }), // Low importance
        createMockStep({ line: 3, increment: 2, ruleId: 'for-loop' }), // High importance
        createMockStep({ line: 4, increment: 1, ruleId: 'simple' }), // Medium importance
        createMockStep({ line: 5, increment: 0, ruleId: 'other' }), // Low importance
      ];
      const options: FilterOptions = {
        minComplexityIncrement: 1,
        maxContextItems: 3,
        forceShowAll: false
      };

      const result = filter.filterDetailSteps(steps, options);
      expect(result.length).toBeLessThanOrEqual(3);
      // Should preserve order and include high-importance steps
      expect(result).toContainEqual(steps[0]); // if-statement
      expect(result).toContainEqual(steps[2]); // for-loop
    });

    test('should retain minimum steps even with aggressive filtering', () => {
      const filter = createFilter();
      // 创建一些有实际复杂度增量的步骤，让测试更现实
      const steps = [
        createMockStep({ line: 1, increment: 1, ruleId: 'if-statement' }),
        createMockStep({ line: 2, increment: 2, ruleId: 'if-statement' }),
        createMockStep({ line: 3, increment: 3, ruleId: 'if-statement' }),
        ...Array.from({ length: 7 }, (_, i) => 
          createMockStep({ line: i + 4, increment: 1, ruleId: 'low-importance' })
        )
      ];
      const options: FilterOptions = {
        minComplexityIncrement: 1, // 设置为1，这样所有步骤都能通过复杂度过滤
        maxContextItems: 1, // 但限制最大上下文项目为1
        forceShowAll: false
      };

      const result = filter.filterDetailSteps(steps, options);
      expect(result.length).toBeGreaterThanOrEqual(3); // Minimum retention
    });
  });

  describe('shouldShowContext', () => {
    test('should respect explicit shouldShowContext setting', () => {
      const filter = createFilter();
      const step = createMockStep({ shouldShowContext: true });
      const options = getDefaultFilterOptions();

      const result = filter.shouldShowContext(step, 0, options);
      expect(result).toBe(true);
    });

    test('should return true when forceShowAll is enabled', () => {
      const filter = createFilter();
      const step = createMockStep({ increment: 0 });
      const options: FilterOptions = {
        ...getDefaultFilterOptions(),
        forceShowAll: true
      };

      const result = filter.shouldShowContext(step, 0, options);
      expect(result).toBe(true);
    });

    test('should filter based on importance and rank', () => {
      const filter = createFilter();
      const highImportanceStep = createMockStep({ 
        increment: 3, 
        ruleId: 'if-statement',
        nestingLevel: 2
      });
      const lowImportanceStep = createMockStep({ 
        increment: 0, 
        ruleId: 'other' 
      });
      const options = getDefaultFilterOptions();

      expect(filter.shouldShowContext(highImportanceStep, 0, options)).toBe(true);
      expect(filter.shouldShowContext(lowImportanceStep, 0, options)).toBe(false);
    });
  });

  describe('getFilterSummary', () => {
    test('should provide correct summary when no filtering occurred', () => {
      const filter = createFilter();
      const summary = filter.getFilterSummary(10, 10);

      expect(summary.originalCount).toBe(10);
      expect(summary.filteredCount).toBe(10);
      expect(summary.hiddenCount).toBe(0);
      expect(summary.filterReason).toBe('未应用过滤');
    });

    test('should categorize filtering levels correctly', () => {
      const filter = createFilter();
      
      // Light filtering (< 30% hidden)
      const lightSummary = filter.getFilterSummary(10, 8);
      expect(lightSummary.filterReason).toContain('轻度过滤');
      
      // Medium filtering (30-70% hidden)
      const mediumSummary = filter.getFilterSummary(10, 5);
      expect(mediumSummary.filterReason).toContain('中度过滤');
      
      // Heavy filtering (> 70% hidden)
      const heavySummary = filter.getFilterSummary(10, 2);
      expect(heavySummary.filterReason).toContain('高度过滤');
    });
  });

  describe('calculateStepImportance (indirect testing)', () => {
    test('should prioritize high-increment steps', () => {
      const filter = createFilter();
      const highIncrement = createMockStep({ increment: 5 });
      const lowIncrement = createMockStep({ increment: 1 });
      
      const steps = [lowIncrement, highIncrement];
      const options: FilterOptions = {
        minComplexityIncrement: 1,
        maxContextItems: 1,
        forceShowAll: false
      };

      const result = filter.filterDetailSteps(steps, options);
      expect(result).toContain(highIncrement);
    });

    test('should prioritize high-importance rules', () => {
      const filter = createFilter();
      const importantRule = createMockStep({ 
        increment: 1, 
        ruleId: 'if-statement' 
      });
      const normalRule = createMockStep({ 
        increment: 1, 
        ruleId: 'other-rule' 
      });
      
      const steps = [normalRule, importantRule];
      const options: FilterOptions = {
        minComplexityIncrement: 1,
        maxContextItems: 1,
        forceShowAll: false
      };

      const result = filter.filterDetailSteps(steps, options);
      expect(result).toContain(importantRule);
    });

    test('should consider nesting level', () => {
      const filter = createFilter();
      const deepNested = createMockStep({ 
        increment: 1, 
        nestingLevel: 5 
      });
      const shallow = createMockStep({ 
        increment: 1, 
        nestingLevel: 1 
      });
      
      const steps = [shallow, deepNested];
      const options: FilterOptions = {
        minComplexityIncrement: 1,
        maxContextItems: 1,
        forceShowAll: false
      };

      const result = filter.filterDetailSteps(steps, options);
      expect(result).toContain(deepNested);
    });
  });

  describe('getDefaultFilterOptions', () => {
    test('should return sensible defaults', () => {
      const defaults = getDefaultFilterOptions();
      
      expect(defaults.minComplexityIncrement).toBe(1);
      expect(defaults.maxContextItems).toBe(10);
      expect(defaults.forceShowAll).toBe(false);
    });
  });
});