import { describe, test, expect, beforeEach } from 'vitest';
import { TextFormatter } from '@/formatters/text';
import { JsonFormatter } from '@/formatters/json';
import type { AnalysisResult, FileResult, FunctionResult, DetailStep } from '@/core/types';
import { DiagnosticMarker, RuleCategory } from '@/core/types';
import type { CognitiveConfig } from '@/config/types';

describe('格式化器详细输出测试', () => {
  let textFormatter: TextFormatter;
  let jsonFormatter: JsonFormatter;
  let mockConfig: CognitiveConfig;
  let sampleAnalysisResult: AnalysisResult;

  beforeEach(() => {
    mockConfig = {
      threshold: 10,
      severityMapping: [
        { threshold: 15, level: 'Critical' },
        { threshold: 10, level: 'Warning' },
        { threshold: 5, level: 'Info' }
      ]
    } as CognitiveConfig;

    textFormatter = new TextFormatter(mockConfig);
    jsonFormatter = new JsonFormatter(mockConfig);

    // 创建带有详细步骤的示例数据
    const detailSteps: DetailStep[] = [
      {
        line: 2,
        column: 4,
        increment: 1,
        cumulative: 1,
        ruleId: 'if-statement',
        description: 'if语句',
        nestingLevel: 0
      },
      {
        line: 3,
        column: 8,
        increment: 1,
        cumulative: 2,
        ruleId: 'logical-and',
        description: '逻辑与操作',
        nestingLevel: 1
      },
      {
        line: 5,
        column: 4,
        increment: 1,
        cumulative: 3,
        ruleId: 'for-loop',
        description: 'for循环',
        nestingLevel: 0
      },
      {
        line: 7,
        column: 8,
        increment: 1,
        cumulative: 4,
        ruleId: 'unknown-rule',
        description: '未知规则',
        nestingLevel: 1,
        diagnosticMarker: DiagnosticMarker.UNKNOWN,
        diagnosticMessage: '未知规则标识符: unknown-rule'
      }
    ];

    const sampleFunction: FunctionResult = {
      name: 'complexFunction',
      complexity: 4,
      line: 1,
      column: 0,
      filePath: '/test/sample.ts',
      severity: 'Info',
      details: detailSteps
    };

    const sampleFileResult: FileResult = {
      filePath: '/test/sample.ts',
      complexity: 4,
      functions: [sampleFunction],
      averageComplexity: 4
    };

    sampleAnalysisResult = {
      summary: {
        totalComplexity: 4,
        averageComplexity: 4,
        filesAnalyzed: 1,
        functionsAnalyzed: 1,
        highComplexityFunctions: 0
      },
      results: [sampleFileResult]
    };
  });

  describe('TextFormatter 详细输出', () => {
    test('应该在基本模式下隐藏详细步骤', () => {
      const output = textFormatter.format(sampleAnalysisResult, false);
      
      expect(output).toContain('认知复杂度分析汇总');
      // 在基本模式下，如果没有高复杂度函数，会显示"所有函数复杂度都在阈值范围内"
      expect(output).toContain('所有函数复杂度都在阈值范围内');
      expect(output).not.toContain('L2:');
      expect(output).not.toContain('累计:');
      expect(output).not.toContain('[if-statement]');
    });

    test('应该在详细模式下显示完整的计算步骤', () => {
      const output = textFormatter.format(sampleAnalysisResult, true);
      
      expect(output).toContain('complexFunction');
      expect(output).toContain('最终复杂度: 4.00');
      
      // 验证详细步骤格式
      expect(output).toContain('L2: +1 (累计: 1) - if语句 [if-statement] (嵌套层级: 0)');
      expect(output).toContain('L3: +1 (累计: 2) - 逻辑与操作 [logical-and] (嵌套层级: 1)');
      expect(output).toContain('L5: +1 (累计: 3) - for循环 [for-loop] (嵌套层级: 0)');
      expect(output).toContain('L7: +1 (累计: 4) - ❓ 未知规则 [unknown-rule] (嵌套层级: 1)');
    });

    test('应该正确显示嵌套层级的缩进', () => {
      const output = textFormatter.format(sampleAnalysisResult, true);
      
      const lines = output.split('\n');
      const detailLines = lines.filter(line => line.includes('L'));
      
      // 嵌套层级0的行应该有基础缩进
      const level0Lines = detailLines.filter(line => line.includes('嵌套层级: 0'));
      expect(level0Lines.length).toBeGreaterThan(0);
      
      // 嵌套层级1的行应该有额外缩进
      const level1Lines = detailLines.filter(line => line.includes('嵌套层级: 1'));
      expect(level1Lines.length).toBeGreaterThan(0);
      
      // 验证层级1的缩进比层级0更多
      if (level0Lines[0] && level1Lines[0]) {
        const level0Indent = level0Lines[0].match(/^(\s*)/)?.[1]?.length || 0;
        const level1Indent = level1Lines[0].match(/^(\s*)/)?.[1]?.length || 0;
        expect(level1Indent).toBeGreaterThan(level0Indent);
      }
    });

    test('应该正确处理诊断标记', () => {
      // 添加不同类型的诊断步骤
      const functionWithDiagnostics: FunctionResult = {
        name: 'diagnosticFunction',
        complexity: 6,
        line: 1,
        column: 0,
        filePath: '/test/diagnostic.ts',
        details: [
          {
            line: 2,
            column: 0,
            increment: 1,
            cumulative: 1,
            ruleId: 'if-statement',
            description: 'if语句',
            nestingLevel: 0
          },
          {
            line: 3,
            column: 0,
            increment: 1,
            cumulative: 2,
            ruleId: 'warning-rule',
            description: '可疑复杂度',
            nestingLevel: 0,
            diagnosticMarker: DiagnosticMarker.WARNING,
            diagnosticMessage: '检测到异常行为'
          },
          {
            line: 4,
            column: 0,
            increment: 1,
            cumulative: 3,
            ruleId: 'error-rule',
            description: '计算错误',
            nestingLevel: 0,
            diagnosticMarker: DiagnosticMarker.ERROR,
            diagnosticMessage: '规则计算失败'
          },
          {
            line: 5,
            column: 0,
            increment: -1,
            cumulative: 2,
            ruleId: 'exemption-rule',
            description: '复杂度豁免',
            nestingLevel: 0
          }
        ]
      };

      const testResult: AnalysisResult = {
        summary: {
          totalComplexity: 2,
          averageComplexity: 2,
          filesAnalyzed: 1,
          functionsAnalyzed: 1,
          highComplexityFunctions: 0
        },
        results: [{
          filePath: '/test/diagnostic.ts',
          complexity: 2,
          functions: [functionWithDiagnostics],
          averageComplexity: 2
        }]
      };

      const output = textFormatter.format(testResult, true);

      // 验证各种诊断标记的显示
      expect(output).toContain('if语句'); // 正常规则
      expect(output).toContain('⚠️'); // 负增量（豁免）会有警告标记
    });

    test('应该正确格式化复杂度数值', () => {
      const highComplexityFunction: FunctionResult = {
        name: 'highComplexityFunction',
        complexity: 20,
        line: 1,
        column: 0,
        filePath: '/test/high.ts',
        severity: 'Critical',
        details: []
      };

      const testResult: AnalysisResult = {
        summary: {
          totalComplexity: 20,
          averageComplexity: 20,
          filesAnalyzed: 1,
          functionsAnalyzed: 1,
          highComplexityFunctions: 1
        },
        results: [{
          filePath: '/test/high.ts',
          complexity: 20,
          functions: [highComplexityFunction],
          averageComplexity: 20
        }]
      };

      const output = textFormatter.format(testResult, true);

      expect(output).toContain('20.00'); // 复杂度数值
      expect(output).toContain('🚨'); // Critical 级别的图标
    });

    test('应该处理空的详细信息', () => {
      const emptyDetailsFunction: FunctionResult = {
        name: 'emptyFunction',
        complexity: 1,
        line: 1,
        column: 0,
        filePath: '/test/empty.ts',
        details: []
      };

      const testResult: AnalysisResult = {
        summary: {
          totalComplexity: 1,
          averageComplexity: 1,
          filesAnalyzed: 1,
          functionsAnalyzed: 1,
          highComplexityFunctions: 0
        },
        results: [{
          filePath: '/test/empty.ts',
          complexity: 1,
          functions: [emptyDetailsFunction],
          averageComplexity: 1
        }]
      };

      const output = textFormatter.format(testResult, true);

      expect(output).toContain('emptyFunction');
      expect(output).not.toContain('L');
      expect(output).not.toContain('累计:');
    });
  });

  describe('JsonFormatter 详细输出', () => {
    test('应该在基本模式下排除详细信息', () => {
      const output = jsonFormatter.format(sampleAnalysisResult, false);
      const parsed = JSON.parse(output);

      expect(parsed.metadata.detailsEnabled).toBe(false);
      // 在基本模式下，详细信息可能不会被包含
      if (parsed.results[0]?.functions[0]?.details) {
        expect(parsed.results[0].functions[0].details).toBeDefined();
      }
      expect(parsed.diagnostics).toBeUndefined(); // 基本模式下不应该有诊断信息
    });

    test('应该在详细模式下包含完整的详细信息', () => {
      const output = jsonFormatter.format(sampleAnalysisResult, true);
      const parsed = JSON.parse(output);

      expect(parsed.metadata).toMatchObject({
        schemaVersion: '1.0.0',
        format: 'cognitive-complexity-json',
        detailsEnabled: true
      });

      expect(parsed.metadata.generatedAt).toBeDefined();

      const functionDetails = parsed.results[0].functions[0].details;
      expect(functionDetails).toHaveLength(4);

      // 验证详细步骤的完整性
      expect(functionDetails[0]).toMatchObject({
        line: 2,
        column: 4,
        increment: 1,
        cumulative: 1,
        ruleId: 'if-statement',
        description: 'if语句',
        nestingLevel: 0
      });

      expect(functionDetails[3]).toMatchObject({
        line: 7,
        column: 8,
        increment: 1,
        cumulative: 4,
        ruleId: 'unknown-rule',
        description: '未知规则',
        nestingLevel: 1,
        diagnosticMarker: DiagnosticMarker.UNKNOWN,
        diagnosticMessage: '未知规则标识符: unknown-rule'
      });
    });

    test('应该正确收集诊断统计信息', () => {
      // 创建包含各种诊断标记的测试数据
      const diagnosticSteps: DetailStep[] = [
        {
          line: 1,
          column: 0,
          increment: 1,
          cumulative: 1,
          ruleId: 'normal-rule',
          description: '正常规则',
          nestingLevel: 0
        },
        {
          line: 2,
          column: 0,
          increment: 1,
          cumulative: 2,
          ruleId: 'warning-rule',
          description: '警告规则',
          nestingLevel: 0,
          diagnosticMarker: DiagnosticMarker.WARNING
        },
        {
          line: 3,
          column: 0,
          increment: 1,
          cumulative: 3,
          ruleId: 'error-rule',
          description: '错误规则',
          nestingLevel: 0,
          diagnosticMarker: DiagnosticMarker.ERROR
        },
        {
          line: 4,
          column: 0,
          increment: 1,
          cumulative: 4,
          ruleId: 'unknown-rule',
          description: '未知规则',
          nestingLevel: 0,
          diagnosticMarker: DiagnosticMarker.UNKNOWN
        }
      ];

      const diagnosticFunction: FunctionResult = {
        name: 'diagnosticFunction',
        complexity: 4,
        line: 1,
        column: 0,
        filePath: '/test/diagnostic.ts',
        details: diagnosticSteps
      };

      const diagnosticResult: AnalysisResult = {
        summary: {
          totalComplexity: 4,
          averageComplexity: 4,
          filesAnalyzed: 1,
          functionsAnalyzed: 1,
          highComplexityFunctions: 0
        },
        results: [{
          filePath: '/test/diagnostic.ts',
          complexity: 4,
          functions: [diagnosticFunction],
          averageComplexity: 4
        }]
      };

      const output = jsonFormatter.format(diagnosticResult, true);
      const parsed = JSON.parse(output);

      expect(parsed.diagnostics).toBeDefined();
      expect(parsed.diagnostics.hasIssues).toBe(true);
      expect(parsed.diagnostics.totalWarnings).toBe(1);
      expect(parsed.diagnostics.totalErrors).toBe(1);
      
      // 验证至少有一些未知规则
      expect(typeof parsed.diagnostics.totalUnknown).toBe('number');
      expect(parsed.diagnostics.totalUnknown).toBeGreaterThanOrEqual(1);
    });

    test('应该在没有诊断问题时省略诊断字段', () => {
      const cleanSteps: DetailStep[] = [
        {
          line: 1,
          column: 0,
          increment: 1,
          cumulative: 1,
          ruleId: 'if-statement',
          description: 'if语句',
          nestingLevel: 0
        }
      ];

      const cleanFunction: FunctionResult = {
        name: 'cleanFunction',
        complexity: 1,
        line: 1,
        column: 0,
        filePath: '/test/clean.ts',
        details: cleanSteps
      };

      const cleanResult: AnalysisResult = {
        summary: {
          totalComplexity: 1,
          averageComplexity: 1,
          filesAnalyzed: 1,
          functionsAnalyzed: 1,
          highComplexityFunctions: 0
        },
        results: [{
          filePath: '/test/clean.ts',
          complexity: 1,
          functions: [cleanFunction],
          averageComplexity: 1
        }]
      };

      const output = jsonFormatter.format(cleanResult, true);
      const parsed = JSON.parse(output);

      expect(parsed.diagnostics).toBeUndefined();
    });

    test('应该保持JSON格式的有效性', () => {
      const output = jsonFormatter.format(sampleAnalysisResult, true);
      
      // 验证是否为有效的JSON
      expect(() => JSON.parse(output)).not.toThrow();
      
      const parsed = JSON.parse(output);
      
      // 验证必需的字段
      expect(parsed.metadata).toBeDefined();
      expect(parsed.summary).toBeDefined();
      expect(parsed.results).toBeDefined();
      expect(Array.isArray(parsed.results)).toBe(true);
    });

    test('应该正确处理嵌套复杂的数据结构', () => {
      // 创建包含多个文件和函数的复杂结果
      const complexResult: AnalysisResult = {
        summary: {
          totalComplexity: 10,
          averageComplexity: 5,
          filesAnalyzed: 2,
          functionsAnalyzed: 2,
          highComplexityFunctions: 1
        },
        results: [
          {
            filePath: '/test/file1.ts',
            complexity: 3,
            functions: [
              {
                name: 'simpleFunction',
                complexity: 3,
                line: 1,
                column: 0,
                filePath: '/test/file1.ts',
                details: [
                  {
                    line: 2,
                    column: 0,
                    increment: 1,
                    cumulative: 1,
                    ruleId: 'if-statement',
                    description: 'if语句',
                    nestingLevel: 0
                  }
                ]
              }
            ],
            averageComplexity: 3
          },
          {
            filePath: '/test/file2.ts',
            complexity: 7,
            functions: [
              {
                name: 'complexFunction',
                complexity: 7,
                line: 1,
                column: 0,
                filePath: '/test/file2.ts',
                severity: 'Warning',
                details: [
                  {
                    line: 2,
                    column: 0,
                    increment: 2,
                    cumulative: 2,
                    ruleId: 'switch-case',
                    description: 'switch语句',
                    nestingLevel: 0
                  },
                  {
                    line: 5,
                    column: 4,
                    increment: 1,
                    cumulative: 3,
                    ruleId: 'nested-if',
                    description: '嵌套if语句',
                    nestingLevel: 1
                  }
                ]
              }
            ],
            averageComplexity: 7
          }
        ]
      };

      const output = jsonFormatter.format(complexResult, true);
      const parsed = JSON.parse(output);

      expect(parsed.results).toHaveLength(2);
      expect(parsed.results[0].functions).toHaveLength(1);
      expect(parsed.results[1].functions).toHaveLength(1);
      expect(parsed.results[1].functions[0].details).toHaveLength(2);
    });
  });

  describe('格式化器兼容性测试', () => {
    test('两种格式化器应该处理相同的数据结构', () => {
      const textOutput = textFormatter.format(sampleAnalysisResult, true);
      const jsonOutput = jsonFormatter.format(sampleAnalysisResult, true);
      
      expect(textOutput).toBeTruthy();
      expect(jsonOutput).toBeTruthy();
      
      const parsedJson = JSON.parse(jsonOutput);
      
      // 验证两种格式都包含相同的核心信息
      expect(textOutput).toContain('complexFunction');
      expect(parsedJson.results[0].functions[0].name).toBe('complexFunction');
      
      expect(textOutput).toContain('最终复杂度: 4.00');
      expect(parsedJson.results[0].functions[0].complexity).toBe(4);
    });

    test('应该在没有详细信息时正常工作', () => {
      const minimalResult: AnalysisResult = {
        summary: {
          totalComplexity: 0,
          averageComplexity: 0,
          filesAnalyzed: 0,
          functionsAnalyzed: 0,
          highComplexityFunctions: 0
        },
        results: []
      };

      const textOutput = textFormatter.format(minimalResult, true);
      const jsonOutput = jsonFormatter.format(minimalResult, true);

      expect(textOutput).toContain('未发现需要分析的文件');
      
      const parsedJson = JSON.parse(jsonOutput);
      expect(parsedJson.results).toHaveLength(0);
      expect(parsedJson.metadata.detailsEnabled).toBe(true);
    });
  });
});