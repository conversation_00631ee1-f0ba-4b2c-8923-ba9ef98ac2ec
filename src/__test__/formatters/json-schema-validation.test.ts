import { describe, it, expect, beforeEach } from 'vitest';
import { JsonFormatter } from '@/formatters/json';
import type { AnalysisResult, FunctionResult, DetailStep } from '@/core/types';
import type { CognitiveConfig } from '@/config/types';
import { OutputValidator } from '../helpers/output-validator';

/**
 * JSON Schema 2.0.0 验证测试套件
 * 测试 JSON 输出格式规范、内容验证和兼容性
 */
describe('JSON Schema Validation - Task 6.2', () => {
  let formatter: JsonFormatter;
  let mockResult: AnalysisResult;
  let mockConfig: CognitiveConfig;

  beforeEach(() => {
    // 创建测试用的配置
    mockConfig = {
      maxComplexity: 10,
      severityMapping: [
        { threshold: 7, level: 'Critical' },
        { threshold: 4, level: 'Warning' },
        { threshold: 1, level: 'Info' }
      ]
    } as CognitiveConfig;
    
    formatter = new JsonFormatter(mockConfig);
    
    // 创建包含详细信息和上下文的测试数据
    const detailSteps: DetailStep[] = [
      {
        line: 5,
        column: 10,
        increment: 2,
        cumulative: 2,
        ruleId: 'nested-if',
        description: 'Nested if statement increases complexity',
        nestingLevel: 2,
        context: '  if (condition) {\n    if (nested) {\n      // code\n    }\n  }',
        span: { start: 120, end: 180 },
        shouldShowContext: true,
        contextRank: 1,
        diagnosticMarker: '⚠️',
        diagnosticMessage: 'Consider simplifying nested logic'
      },
      {
        line: 8,
        column: 6,
        increment: 1,
        cumulative: 3,
        ruleId: 'logical-operator',
        description: 'Logical AND operator',
        nestingLevel: 1
        // 没有 span 信息，也没有 shouldShowContext
      },
      {
        line: 12,
        column: 4,
        increment: -1,
        cumulative: 2,
        ruleId: 'jsx-exemption',
        description: 'JSX structural exemption reduces complexity',
        nestingLevel: 0,
        context: '<Component prop={value} />',
        span: { start: 300, end: 330 },
        diagnosticMarker: '❓',
        diagnosticMessage: 'JSX exemption applied'
      }
    ];

    const functionResult: FunctionResult = {
      name: 'testFunction',
      complexity: 3,
      startLine: 1,
      endLine: 15,
      details: detailSteps
    };

    mockResult = {
      results: [
        {
          filePath: '/test/sample.ts',
          functions: [functionResult],
          averageComplexity: 3,
          totalComplexity: 3
        }
      ],
      summary: {
        totalFiles: 1,
        totalFunctions: 1,
        averageComplexity: 3,
        maxComplexity: 3,
        totalComplexity: 3,
        highComplexityFunctions: 0
      }
    };
  });

  describe('Schema Version 2.0.0 Validation', () => {
    it('应该包含正确的 schema 版本和元数据', () => {
      const output = formatter.format(mockResult, true, { 
        showContext: true,
        showAllContext: false
      });
      const parsed = JSON.parse(output);

      // 验证元数据结构
      expect(parsed.metadata).toBeDefined();
      expect(parsed.metadata.schemaVersion).toBe('2.0.0');
      expect(parsed.metadata.generatedAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(parsed.metadata.format).toBe('cognitive-complexity-json');
      expect(parsed.metadata.detailsEnabled).toBe(true);
      expect(parsed.metadata.contextEnabled).toBe(true);
      expect(parsed.metadata.contextAllEnabled).toBe(false);
    });

    it('应该在不同配置下生成正确的元数据', () => {
      const testCases = [
        { options: {}, expectedDetails: false, expectedContext: false, expectedAll: false },
        { options: { showContext: true }, expectedDetails: false, expectedContext: true, expectedAll: false },
        { options: { showAllContext: true }, expectedDetails: false, expectedContext: false, expectedAll: true },
        { options: { showContext: true, showAllContext: true }, expectedDetails: false, expectedContext: true, expectedAll: true }
      ];

      for (const testCase of testCases) {
        const output = formatter.format(mockResult, true, testCase.options);
        const parsed = JSON.parse(output);

        expect(parsed.metadata.detailsEnabled).toBe(true); // 总是 true 因为 showDetails=true
        expect(parsed.metadata.contextEnabled).toBe(testCase.expectedContext);
        expect(parsed.metadata.contextAllEnabled).toBe(testCase.expectedAll);
      }
    });
  });

  describe('JSON 输出内容验证', () => {
    it('应该保持原有的基础结构', () => {
      const output = formatter.format(mockResult, false);
      const parsed = JSON.parse(output);

      // 验证基础结构保持不变
      expect(parsed.results).toBeDefined();
      expect(parsed.summary).toBeDefined();
      expect(parsed.results[0].filePath).toBe('/test/sample.ts');
      expect(parsed.results[0].functions).toHaveLength(1);
      expect(parsed.results[0].functions[0].name).toBe('testFunction');
      expect(parsed.results[0].functions[0].complexity).toBe(3);
    });

    it('应该在详细模式下包含增强的详细步骤', () => {
      const output = formatter.format(mockResult, true, { showContext: true });
      const parsed = JSON.parse(output);

      const func = parsed.results[0].functions[0];
      expect(func.details).toBeDefined();
      expect(func.details).toHaveLength(3);

      // 验证第一个详细步骤的增强信息
      const firstStep = func.details[0];
      expect(firstStep.line).toBe(5);
      expect(firstStep.column).toBe(10);
      expect(firstStep.increment).toBe(2);
      expect(firstStep.ruleId).toBe('nested-if');
      expect(firstStep.contextAvailable).toBe(true);
      expect(firstStep.position).toEqual({ line: 5, column: 10 });
      expect(firstStep.codeFrame).toBe('  if (condition) {\n    if (nested) {\n      // code\n    }\n  }');
      expect(firstStep.contextRank).toBe(1);
    });

    it('应该包含正确的诊断统计信息', () => {
      const output = formatter.format(mockResult, true);
      const parsed = JSON.parse(output);

      expect(parsed.diagnostics).toBeDefined();
      expect(parsed.diagnostics.hasIssues).toBe(true);
      expect(parsed.diagnostics.totalWarnings).toBe(2); // ⚠️ + 负增量
      expect(parsed.diagnostics.totalErrors).toBe(0);
      expect(parsed.diagnostics.totalUnknown).toBe(1); // ❓
    });

    it('应该正确处理不同的诊断标记', () => {
      // 添加更多诊断标记的测试数据
      const steps: DetailStep[] = [
        {
          line: 1, column: 1, increment: 1, cumulative: 1,
          ruleId: 'test', description: 'test',
          nestingLevel: 0, diagnosticMarker: '❌'
        },
        {
          line: 2, column: 1, increment: 1, cumulative: 2,
          ruleId: 'unknown-rule', description: 'unknown',
          nestingLevel: 0
        },
        {
          line: 3, column: 1, increment: -1, cumulative: 1,
          ruleId: 'exemption', description: 'exemption',
          nestingLevel: 0
        }
      ];

      const testResult: AnalysisResult = {
        ...mockResult,
        results: [{
          filePath: '/test.ts',
          functions: [{
            name: 'test',
            complexity: 1,
            startLine: 1,
            endLine: 3,
            details: steps
          }],
          averageComplexity: 1,
          totalComplexity: 1
        }]
      };

      const output = formatter.format(testResult, true);
      const parsed = JSON.parse(output);

      expect(parsed.diagnostics.totalErrors).toBe(1); // ❌
      expect(parsed.diagnostics.totalWarnings).toBe(1); // 负增量
      expect(parsed.diagnostics.totalUnknown).toBe(1); // unknown-rule
    });
  });

  describe('兼容性验证', () => {
    it('应该在非详细模式下移除 details 字段', () => {
      const output = formatter.format(mockResult, false);
      const parsed = JSON.parse(output);

      const func = parsed.results[0].functions[0];
      expect(func.details).toBeUndefined();
      expect(func.severity).toBeDefined(); // 但应该保留 severity
    });

    it('应该保持现有的 severity 增强功能', () => {
      const output = formatter.format(mockResult, false);
      const parsed = JSON.parse(output);

      const func = parsed.results[0].functions[0];
      expect(func.severity).toBeDefined();
      // 复杂度为 3，应该根据配置映射为 'Info' 级别
      expect(['Critical', 'Warning', 'Info']).toContain(func.severity);
    });

    it('应该只在有诊断问题时包含 diagnostics 字段', () => {
      // 创建没有诊断问题的结果
      const cleanResult: AnalysisResult = {
        ...mockResult,
        results: [{
          filePath: '/clean.ts',
          functions: [{
            name: 'clean',
            complexity: 1,
            startLine: 1,
            endLine: 3,
            details: [{
              line: 1, column: 1, increment: 1, cumulative: 1,
              ruleId: 'basic', description: 'basic increment',
              nestingLevel: 0
            }]
          }],
          averageComplexity: 1,
          totalComplexity: 1
        }]
      };

      const output = formatter.format(cleanResult, true);
      const parsed = JSON.parse(output);

      expect(parsed.diagnostics).toBeUndefined();
    });

    it('应该支持文件输出模式', async () => {
      const tempPath = '/tmp/test-output.json';
      
      // 模拟文件写入（实际测试中可能需要真实的文件系统操作）
      const writePromise = formatter.writeToFile(mockResult, tempPath, { showContext: true });
      
      // 验证不会抛出错误
      await expect(writePromise).resolves.toBeUndefined();
    });
  });

  describe('上下文信息验证', () => {
    it('应该正确处理 showContext 选项', () => {
      const output = formatter.format(mockResult, true, { showContext: true });
      const parsed = JSON.parse(output);

      const steps = parsed.results[0].functions[0].details;
      
      // 第一个步骤有 shouldShowContext: true，应该包含上下文
      expect(steps[0].contextAvailable).toBe(true);
      expect(steps[0].codeFrame).toBeDefined();
      expect(steps[0].position).toBeDefined();
      
      // 第二个步骤没有 shouldShowContext，且 increment <= 1，不应该包含上下文
      expect(steps[1].contextAvailable).toBe(false);
      expect(steps[1].codeFrame).toBeUndefined();
    });

    it('应该正确处理 showAllContext 选项', () => {
      const output = formatter.format(mockResult, true, { showAllContext: true });
      const parsed = JSON.parse(output);

      const steps = parsed.results[0].functions[0].details;
      
      // 所有有 span 信息的步骤都应该标记为可显示上下文
      for (const step of steps) {
        if (step.span) {
          expect(step.contextAvailable).toBe(true);
        }
      }
    });

    it('应该处理缺少上下文的情况', () => {
      // 创建只有 span 但没有 context 的步骤，increment > 1 以触发上下文显示
      const testSteps: DetailStep[] = [{
        line: 1, column: 1, increment: 2, cumulative: 2,
        ruleId: 'test', description: 'test',
        nestingLevel: 0,
        span: { start: 100, end: 120 }
        // 没有 context 字段
      }];

      const testResult: AnalysisResult = {
        ...mockResult,
        results: [{
          filePath: '/test.ts',
          functions: [{
            name: 'test',
            complexity: 1,
            startLine: 1,
            endLine: 3,
            details: testSteps
          }],
          averageComplexity: 1,
          totalComplexity: 1
        }]
      };

      const output = formatter.format(testResult, true, { showContext: true });
      const parsed = JSON.parse(output);

      const step = parsed.results[0].functions[0].details[0];
      expect(step.contextAvailable).toBe(true);
      expect(step.contextError).toBe('Context generation would require file access');
    });
  });

  describe('JSON Schema 结构验证', () => {
    it('应该通过 OutputValidator 的 JSON 验证', async () => {
      const output = formatter.format(mockResult, true, { showContext: true });
      
      // 定义期望的 Schema 结构
      const expectedSchema = {
        metadata: {
          schemaVersion: '2.0.0',
          generatedAt: 'string',
          format: 'cognitive-complexity-json',
          detailsEnabled: true,
          contextEnabled: true,
          contextAllEnabled: false
        },
        results: [],
        summary: {},
        diagnostics: {}
      };

      const validation = await OutputValidator.validateJSONOutput(
        output, 
        expectedSchema,
        { strictMode: false, allowPartial: true }
      );

      expect(validation.passed).toBe(true);
      expect(validation.matches).toHaveLength(1);
    });

    it('应该包含所有必需的字段', () => {
      const output = formatter.format(mockResult, true, { showContext: true });
      const parsed = JSON.parse(output);

      // 验证顶级字段
      expect(parsed).toHaveProperty('metadata');
      expect(parsed).toHaveProperty('results');
      expect(parsed).toHaveProperty('summary');
      expect(parsed).toHaveProperty('diagnostics');

      // 验证元数据字段
      const requiredMetadataFields = [
        'schemaVersion', 'generatedAt', 'format', 
        'detailsEnabled', 'contextEnabled', 'contextAllEnabled'
      ];
      for (const field of requiredMetadataFields) {
        expect(parsed.metadata).toHaveProperty(field);
      }

      // 验证结果结构
      expect(Array.isArray(parsed.results)).toBe(true);
      if (parsed.results.length > 0) {
        const result = parsed.results[0];
        expect(result).toHaveProperty('filePath');
        expect(result).toHaveProperty('functions');
        expect(result).toHaveProperty('averageComplexity');
        expect(result).toHaveProperty('totalComplexity');
      }
    });

    it('应该生成有效的 JSON 格式', () => {
      const output = formatter.format(mockResult, true, { showContext: true });
      
      // 验证是有效的 JSON
      expect(() => JSON.parse(output)).not.toThrow();
      
      // 验证格式化正确（包含缩进）
      const parsed = JSON.parse(output);
      const reformatted = JSON.stringify(parsed, null, 2);
      expect(output).toBe(reformatted);
    });
  });

  describe('错误处理验证', () => {
    it('应该处理空结果', () => {
      const emptyResult: AnalysisResult = {
        results: [],
        summary: {
          totalFiles: 0,
          totalFunctions: 0,
          averageComplexity: 0,
          maxComplexity: 0,
          totalComplexity: 0,
          highComplexityFunctions: 0
        }
      };

      const output = formatter.format(emptyResult, true, { showContext: true });
      const parsed = JSON.parse(output);

      expect(parsed.metadata).toBeDefined();
      expect(parsed.results).toEqual([]);
      expect(parsed.summary.totalFiles).toBe(0);
      expect(parsed.diagnostics).toBeUndefined(); // 没有问题时不应该有 diagnostics
    });

    it('应该处理没有详细信息的函数', () => {
      const resultWithoutDetails: AnalysisResult = {
        ...mockResult,
        results: [{
          filePath: '/simple.ts',
          functions: [{
            name: 'simple',
            complexity: 1,
            startLine: 1,
            endLine: 3
            // 没有 details 字段
          }],
          averageComplexity: 1,
          totalComplexity: 1
        }]
      };

      const output = formatter.format(resultWithoutDetails, true, { showContext: true });
      const parsed = JSON.parse(output);

      expect(parsed.results[0].functions[0].details).toBeUndefined();
      expect(parsed.diagnostics).toBeUndefined();
    });
  });
});