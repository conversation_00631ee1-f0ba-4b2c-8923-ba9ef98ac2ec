/**
 * JSX事件处理器规则测试用例
 * 验证事件处理器复杂度分析的准确性
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { JSXEventHandlerRule } from '../../rules/jsx-event-handler-rule';
import type { AnalysisContext } from '../../engine/types';
import { createMockAnalysisContext, parseCodeToAST } from '../helpers/test-utils';

describe('JSXEventHandlerRule', () => {
  let rule: JSXEventHandlerRule;
  let context: AnalysisContext;

  beforeEach(() => {
    rule = new JSXEventHandlerRule();
    context = createMockAnalysisContext({
      jsx: {
        enabled: true,
        scoring: {
          eventHandlers: true,
        },
      },
    });
  });

  describe('Rule Identification', () => {
    test('should have correct rule id and priority', () => {
      expect(rule.id).toBe('jsx.event.handler');
      expect(rule.name).toBe('JSX Event Handler Analysis');
      expect(rule.priority).toBe(750);
    });

    test('should not have dependencies', () => {
      expect(rule.getDependencies()).toEqual([]);
    });
  });

  describe('Handler Detection', () => {
    test('should detect inline arrow function event handlers', async () => {
      const code = `
        function Component() {
          return <button onClick={() => console.log('clicked')} />;
        }
      `;
      const ast = parseCodeToAST(code);
      const jsxAttribute = findJSXAttribute(ast, 'onClick');
      
      expect(rule.canHandle(jsxAttribute)).toBe(true);
    });

    test('should detect function reference event handlers', async () => {
      const code = `
        function Component() {
          const handleClick = () => {};
          return <button onClick={handleClick} />;
        }
      `;
      const ast = parseCodeToAST(code);
      const jsxAttribute = findJSXAttribute(ast, 'onClick');
      
      expect(rule.canHandle(jsxAttribute)).toBe(true);
    });

    test('should detect various event types', async () => {
      const eventTypes = [
        'onClick', 'onMouseDown', 'onKeyPress', 'onChange', 
        'onSubmit', 'onFocus', 'onBlur', 'onScroll'
      ];

      for (const eventType of eventTypes) {
        const code = `<button ${eventType}={() => {}} />`;
        const ast = parseCodeToAST(code);
        const jsxAttribute = findJSXAttribute(ast, eventType);
        
        expect(rule.canHandle(jsxAttribute)).toBe(true);
      }
    });

    test('should not handle non-event attributes', async () => {
      const code = `<button className="btn" data-test="value" />`;
      const ast = parseCodeToAST(code);
      const classAttribute = findJSXAttribute(ast, 'className');
      const dataAttribute = findJSXAttribute(ast, 'data-test');
      
      expect(rule.canHandle(classAttribute)).toBe(false);
      expect(rule.canHandle(dataAttribute)).toBe(false);
    });
  });

  describe('Simple Inline Handlers', () => {
    test('should analyze simple inline arrow function with low complexity', async () => {
      const code = `<button onClick={() => setCount(count + 1)} />`;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.complexity).toBe(1);
      expect(result.isExempted).toBe(false);
      expect(result.metadata.pattern).toBe('simple-inline');
      expect(result.metadata.handlerType).toBe('arrow');
      expect(result.metadata.isOptimal).toBe(true);
    });

    test('should analyze simple state setter with minimal complexity', async () => {
      const code = `<button onClick={() => setVisible(true)} />`;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.complexity).toBe(1);
      expect(result.metadata.pattern).toBe('simple-inline');
      expect(result.metadata.nodeInfo.stateUpdates).toBe(1);
    });
  });

  describe('Complex Inline Handlers', () => {
    test('should analyze complex inline handler with increased complexity', async () => {
      const code = `
        <button onClick={() => {
          console.log('start');
          setCount(count + 1);
          setVisible(true);
          onComplete?.();
        }} />
      `;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.complexity).toBeGreaterThan(2);
      expect(result.metadata.pattern).toBe('complex-inline');
      expect(result.metadata.nodeInfo.hasMultipleStatements).toBe(true);
      expect(result.metadata.nodeInfo.stateUpdates).toBe(2);
      expect(result.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'refactor',
          message: expect.stringContaining('extract this complex inline event handler'),
          priority: 'medium'
        })
      );
    });

    test('should detect nested functions in handlers', async () => {
      const code = `
        <button onClick={() => {
          const helper = () => console.log('nested');
          helper();
        }} />
      `;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.metadata.nodeInfo.hasNestedFunctions).toBe(true);
      expect(result.complexity).toBeGreaterThan(2);
    });
  });

  describe('Function Reference Handlers', () => {
    test('should analyze function reference with optimal rating', async () => {
      const code = `<button onClick={handleClick} />`;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.complexity).toBe(0);
      expect(result.metadata.pattern).toBe('function-reference');
      expect(result.metadata.handlerType).toBe('reference');
      expect(result.metadata.isOptimal).toBe(true);
    });

    test('should detect bound method handlers', async () => {
      const code = `<button onClick={this.handleClick.bind(this)} />`;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.metadata.pattern).toBe('bound-method');
      expect(result.metadata.handlerType).toBe('bound');
      expect(result.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'optimize',
          message: expect.stringContaining('Consider using arrow functions or useCallback'),
          priority: 'medium'
        })
      );
    });
  });

  describe('Async Event Handlers', () => {
    test('should analyze async inline handlers', async () => {
      const code = `
        <button onClick={async () => {
          const result = await fetch('/api/data');
          setData(result);
        }} />
      `;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.metadata.pattern).toBe('async-handler');
      expect(result.metadata.handlerType).toBe('async');
      expect(result.metadata.nodeInfo.hasAsyncOperations).toBe(true);
      expect(result.complexity).toBeGreaterThan(1);
    });

    test('should suggest refactoring for complex async handlers', async () => {
      const code = `
        <button onClick={async () => {
          setLoading(true);
          try {
            const result = await fetch('/api');
            setData(result);
            setSuccess(true);
          } catch (error) {
            setError(error);
          } finally {
            setLoading(false);
          }
        }} />
      `;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.metadata.nodeInfo.hasMultipleStatements).toBe(true);
      expect(result.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'refactor',
          message: expect.stringContaining('Complex async event handlers should be extracted'),
          priority: 'medium'
        })
      );
    });
  });

  describe('Performance Anti-patterns', () => {
    test('should detect expensive operations in handlers', async () => {
      const code = `
        <button onClick={() => {
          data.map(item => item.value).filter(v => v > 0).sort();
        }} />
      `;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.metadata.pattern).toBe('performance-anti');
      expect(result.metadata.hasPerformanceIssues).toBe(true);
      expect(result.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'optimize',
          message: expect.stringContaining('expensive operations that will run on every render'),
          priority: 'high'
        })
      );
    });

    test('should detect loop-based operations', async () => {
      const code = `
        <button onClick={() => {
          for (let i = 0; i < 1000; i++) {
            console.log(i);
          }
        }} />
      `;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.metadata.hasPerformanceIssues).toBe(true);
      expect(result.complexity).toBeGreaterThan(2);
    });
  });

  describe('Event Delegation Patterns', () => {
    test('should detect delegated event handlers', async () => {
      const code = `
        <button onClick={(e) => handleGenericClick(e, 'submit')} />
      `;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.metadata.pattern).toBe('delegated');
      expect(result.complexity).toBe(2); // 基础 + 委托模式
    });
  });

  describe('Suggestion Generation', () => {
    test('should generate appropriate suggestions for different patterns', async () => {
      const testCases = [
        {
          code: `<button onClick={() => { /* complex logic */ }} />`,
          expectedSuggestion: 'refactor'
        },
        {
          code: `<button onClick={() => data.map(x => x)} />`,
          expectedSuggestion: 'optimize'
        },
        {
          code: `<button onClick={this.handleClick.bind(this)} />`,
          expectedSuggestion: 'optimize'
        }
      ];

      for (const testCase of testCases) {
        const ast = parseCodeToAST(testCase.code);
        const handler = findEventHandler(ast);
        const result = await rule.evaluate(handler, context);
        
        expect(result.suggestions.length).toBeGreaterThan(0);
        expect(result.suggestions.some(s => s.type === testCase.expectedSuggestion)).toBe(true);
      }
    });

    test('should provide code examples in suggestions', async () => {
      const code = `<button onClick={this.handleClick.bind(this)} />`;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      const optimizeSuggestion = result.suggestions.find(s => s.type === 'optimize');
      expect(optimizeSuggestion?.codeExample).toContain('useCallback');
    });
  });

  describe('Configuration Handling', () => {
    test('should respect disabled event handler analysis', async () => {
      const disabledContext = createMockAnalysisContext({
        jsx: {
          enabled: true,
          scoring: {
            eventHandlers: false,
          },
        },
      });

      const code = `<button onClick={() => console.log('test')} />`;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, disabledContext);
      
      expect(result.complexity).toBe(0);
      expect(result.isExempted).toBe(false);
      expect(result.reason).toBe('Event handler analysis disabled');
    });

    test('should respect disabled JSX analysis', async () => {
      const disabledContext = createMockAnalysisContext({
        jsx: {
          enabled: false,
        },
      });

      const code = `<button onClick={() => console.log('test')} />`;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, disabledContext);
      
      expect(result.complexity).toBe(0);
      expect(result.reason).toBe('Event handler analysis disabled');
    });
  });

  describe('Multiple State Updates', () => {
    test('should track multiple state updates and suggest batching', async () => {
      const code = `
        <button onClick={() => {
          setCount(count + 1);
          setName('updated');
          setVisible(true);
          setLoading(false);
        }} />
      `;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      const result = await rule.evaluate(handler, context);
      
      expect(result.metadata.nodeInfo.stateUpdates).toBe(4);
      expect(result.suggestions).toContainEqual(
        expect.objectContaining({
          type: 'optimize',
          message: expect.stringContaining('Multiple state updates can be batched'),
          priority: 'medium'
        })
      );
    });
  });

  describe('Caching Behavior', () => {
    test('should utilize caching for repeated evaluations', async () => {
      const code = `<button onClick={() => console.log('test')} />`;
      const ast = parseCodeToAST(code);
      const handler = findEventHandler(ast);
      
      // First evaluation
      const result1 = await rule.evaluate(handler, context);
      expect(result1.cacheHit).toBe(false);
      
      // Second evaluation should hit cache
      const result2 = await rule.evaluate(handler, context);
      expect(result2.cacheHit).toBe(true);
      expect(result2.complexity).toBe(result1.complexity);
    });
  });
});

// Helper functions for test utilities
function findJSXAttribute(ast: any, attributeName: string): any {
  // Simplified implementation for test purposes
  // In real implementation, this would traverse the AST to find JSX attributes
  return {
    type: 'JSXAttribute',
    name: { type: 'Identifier', name: attributeName },
    value: { type: 'JSXExpressionContainer', expression: { type: 'ArrowFunctionExpression' } }
  };
}

function findEventHandler(ast: any): any {
  // Simplified implementation for test purposes
  // In real implementation, this would find the actual event handler node
  return {
    type: 'ArrowFunctionExpression',
    body: { type: 'CallExpression' }
  };
}