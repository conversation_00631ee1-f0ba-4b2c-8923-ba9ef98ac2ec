/**
 * IfStatementRule测试用例
 * 验证if语句复杂度计算的正确性
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { IfStatementRule } from '../../rules/if-statement-rule';
import type { AnalysisContext } from '../../engine/types';
import { parse } from '@swc/core';

// 创建模拟的分析上下文
function createMockContext(nestingLevel: number = 0): AnalysisContext {
  return {
    filePath: 'test.ts',
    fileContent: '',
    ast: {} as any,
    functionName: 'testFunction',
    nestingLevel,
    config: {} as any,
    jsxMode: 'standard',
    rules: { core: [], jsx: [], plugins: [] },
    cache: {
      getCachedRuleResult: async () => null,
      setCachedRuleResult: async () => {},
    } as any,
    metrics: {} as any,
    plugins: [],
    customData: new Map(),
  };
}

// 解析代码并获取第一个if语句节点
async function parseIfStatement(code: string): Promise<any> {
  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  // 查找if语句节点
  function findIfStatement(node: any): any {
    if (node?.type === 'IfStatement') {
      return node;
    }
    
    if (node && typeof node === 'object') {
      for (const key in node) {
        if (key !== 'parent') {
          const child = node[key];
          if (Array.isArray(child)) {
            for (const item of child) {
              const found = findIfStatement(item);
              if (found) return found;
            }
          } else {
            const found = findIfStatement(child);
            if (found) return found;
          }
        }
      }
    }
    
    return null;
  }

  return findIfStatement(result);
}

describe('IfStatementRule', () => {
  let rule: IfStatementRule;

  beforeEach(() => {
    rule = new IfStatementRule();
  });

  describe('基本属性', () => {
    it('应该有正确的规则ID和名称', () => {
      expect(rule.id).toBe('if-statement');
      expect(rule.name).toBe('If Statement Complexity');
      expect(rule.priority).toBe(400);
    });
  });

  describe('canHandle方法', () => {
    it('应该识别if语句', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (true) {
            console.log('test');
          }
        }
      `);

      expect(rule.canHandle(ifNode)).toBe(true);
    });

    it('应该拒绝非if语句节点', async () => {
      const whileNode = { type: 'WhileStatement' } as any;
      const forNode = { type: 'ForStatement' } as any;

      expect(rule.canHandle(whileNode)).toBe(false);
      expect(rule.canHandle(forNode)).toBe(false);
    });
  });

  describe('简单if语句', () => {
    it('应该为简单if语句返回基础复杂度1', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (condition) {
            doSomething();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(ifNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(true);
      expect(result.reason).toContain('If statement increases cognitive complexity by 1');
    });

    it('应该为带嵌套的if语句应用嵌套惩罚', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (outerCondition) {
            if (innerCondition) {
              doSomething();
            }
          }
        }
      `);

      const context = createMockContext(2); // 嵌套层级为2
      const result = await rule.evaluate(ifNode, context);

      expect(result.complexity).toBe(3); // 基础1 + 嵌套惩罚2
      expect(result.metadata?.nestingLevel).toBe(2);
      expect(result.reason).toContain('nesting penalty: +2');
    });
  });

  describe('if-else语句', () => {
    it('应该为if-else语句计算正确复杂度', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (condition) {
            doSomething();
          } else {
            doSomethingElse();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(ifNode, context);

      expect(result.complexity).toBe(1); // if语句基础复杂度，else不增加复杂度
      expect(result.metadata?.hasElseBlock).toBe(true);
      expect(result.reason).toContain('includes else clause');
    });
  });

  describe('else-if链', () => {
    it('应该为else-if链计算额外复杂度', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (condition1) {
            action1();
          } else if (condition2) {
            action2();
          } else if (condition3) {
            action3();
          } else {
            defaultAction();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(ifNode, context);

      expect(result.complexity).toBe(3); // 基础1 + 2个else-if
      expect(result.metadata?.elseIfCount).toBe(2);
      expect(result.metadata?.hasElseIf).toBe(true);
      expect(result.metadata?.hasElseBlock).toBe(true);
      expect(result.reason).toContain('includes 2 else-if clauses');
    });

    it('应该为长else-if链生成重构建议', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (type === 'A') {
            handleA();
          } else if (type === 'B') {
            handleB();
          } else if (type === 'C') {
            handleC();
          } else if (type === 'D') {
            handleD();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(ifNode, context);

      expect(result.complexity).toBe(4); // 基础1 + 3个else-if
      expect(result.suggestions).toHaveLength(2); // 基础建议 + else-if链建议
      
      const refactorSuggestion = result.suggestions.find(s => s.message.includes('switch statement'));
      expect(refactorSuggestion).toBeDefined();
      expect(refactorSuggestion?.type).toBe('refactor');
    });
  });

  describe('复杂嵌套场景', () => {
    it('应该处理深度嵌套的if语句', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (level1) {
            if (level2) {
              if (level3) {
                doSomething();
              }
            }
          }
        }
      `);

      const context = createMockContext(3); // 深度嵌套
      const result = await rule.evaluate(ifNode, context);

      expect(result.complexity).toBe(4); // 基础1 + 嵌套惩罚3
      expect(result.suggestions.length).toBeGreaterThan(1);
      
      const nestingSuggestion = result.suggestions.find(s => s.message.includes('early return'));
      expect(nestingSuggestion).toBeDefined();
    });
  });

  describe('元数据验证', () => {
    it('应该提供完整的元数据信息', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (condition1) {
            action1();
          } else if (condition2) {
            action2();
          } else {
            defaultAction();
          }
        }
      `);

      const context = createMockContext(1);
      const result = await rule.evaluate(ifNode, context);

      expect(result.metadata).toMatchObject({
        nodeType: 'IfStatement',
        baseComplexity: 2, // 基础1 + 1个else-if
        nestingLevel: 1,
        hasElseIf: true,
        elseIfCount: 1,
        hasElseBlock: true,
      });
    });
  });

  describe('边界条件', () => {
    it('应该处理空的if语句', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (condition) {
            // 空块
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(ifNode, context);

      expect(result.complexity).toBe(1);
      expect(result.isExempted).toBe(false);
    });

    it('应该处理只有else-if没有最终else的情况', async () => {
      const ifNode = await parseIfStatement(`
        function test() {
          if (condition1) {
            action1();
          } else if (condition2) {
            action2();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(ifNode, context);

      expect(result.complexity).toBe(2); // 基础1 + 1个else-if
      expect(result.metadata?.hasElseBlock).toBe(false);
      expect(result.metadata?.elseIfCount).toBe(1);
    });
  });
});