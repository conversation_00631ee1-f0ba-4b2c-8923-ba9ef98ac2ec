/**
 * CatchClauseRule测试用例
 * 验证异常处理复杂度计算的正确性
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { CatchClauseRule } from '../../rules/catch-clause-rule';
import type { AnalysisContext } from '../../engine/types';
import { parse } from '@swc/core';

// 创建模拟的分析上下文
function createMockContext(nestingLevel: number = 0): AnalysisContext {
  return {
    filePath: 'test.ts',
    fileContent: '',
    ast: {} as any,
    functionName: 'testFunction',
    nestingLevel,
    config: {} as any,
    jsxMode: 'standard',
    rules: { core: [], jsx: [], plugins: [] },
    cache: {
      getCachedRuleResult: async () => null,
      setCachedRuleResult: async () => {},
    } as any,
    metrics: {} as any,
    plugins: [],
    customData: new Map(),
  };
}

// 解析代码并获取第一个 catch 子句节点
async function parseCatchClause(code: string): Promise<any> {
  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  // 查找 catch 子句节点
  function findCatchClause(node: any): any {
    if (node?.type === 'CatchClause') {
      return node;
    }
    
    if (node && typeof node === 'object') {
      for (const key in node) {
        if (key !== 'parent') {
          const child = node[key];
          if (Array.isArray(child)) {
            for (const item of child) {
              const found = findCatchClause(item);
              if (found) return found;
            }
          } else {
            const found = findCatchClause(child);
            if (found) return found;
          }
        }
      }
    }
    
    return null;
  }

  return findCatchClause(result);
}

describe('CatchClauseRule', () => {
  let rule: CatchClauseRule;

  beforeEach(() => {
    rule = new CatchClauseRule();
  });

  describe('基本属性', () => {
    it('应该有正确的规则ID和名称', () => {
      expect(rule.id).toBe('catch-clause');
      expect(rule.name).toBe('Exception Handling Complexity');
      expect(rule.priority).toBe(500);
    });
  });

  describe('canHandle方法', () => {
    it('应该识别catch子句', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            handleError(error);
          }
        }
      `);

      expect(rule.canHandle(catchNode)).toBe(true);
    });

    it('应该拒绝非catch子句节点', async () => {
      const ifNode = { type: 'IfStatement' } as any;
      const tryNode = { type: 'TryStatement' } as any;

      expect(rule.canHandle(ifNode)).toBe(false);
      expect(rule.canHandle(tryNode)).toBe(false);
    });
  });

  describe('简单catch子句', () => {
    it('应该为简单catch子句返回基础复杂度1', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            console.log(error);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(true); // catch子句增加嵌套层级
      expect(result.reason).toContain('Catch clause increases cognitive complexity by 1');
      expect(result.metadata?.nodeType).toBe('CatchClause');
      expect(result.metadata?.baseComplexity).toBe(1);
      expect(result.metadata?.hasErrorParameter).toBe(true);
    });

    it('应该为带嵌套的catch子句应用嵌套惩罚', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          if (condition) {
            try {
              riskyOperation();
            } catch (error) {
              handleError(error);
            }
          }
        }
      `);

      const context = createMockContext(2); // 嵌套层级为2
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(3); // 基础1 + 嵌套惩罚2
      expect(result.metadata?.nestingLevel).toBe(2);
      expect(result.reason).toContain('nesting penalty: +2');
    });

    it('应该检测没有错误参数的catch子句', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch {
            handleError();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasErrorParameter).toBe(false);
      
      const warningSuggestion = result.suggestions.find(s => 
        s.message.includes('without error parameter')
      );
      expect(warningSuggestion).toBeDefined();
      expect(warningSuggestion?.type).toBe('warning');
    });
  });

  describe('复杂错误处理分析', () => {
    it('应该检测复杂的错误处理逻辑', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            if (error.type === 'NetworkError') {
              handleNetworkError(error);
            } else if (error.type === 'ValidationError') {
              handleValidationError(error);
            } else {
              handleGenericError(error);
            }
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexErrorHandling).toBe(true);
      expect(result.reason).toContain('complex error handling detected');
    });

    it('应该检测嵌套的try-catch语句', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            outerOperation();
          } catch (outerError) {
            try {
              fallbackOperation();
            } catch (innerError) {
              handleInnerError(innerError);
            }
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasNestedTryCatch).toBe(true);
      expect(result.reason).toContain('contains nested try-catch statements');
    });

    it('应该检测包含多个函数调用的catch子句', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            logError(error);
            notifyUser(error);
            sendToMonitoring(error);
            updateState(error);
            cleanupResources(error);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      
      const performanceSuggestion = result.suggestions.find(s => 
        s.message.includes('Multiple function calls')
      );
      expect(performanceSuggestion).toBeDefined();
      expect(performanceSuggestion?.type).toBe('performance');
    });

    it('应该检测包含复杂表达式的catch子句', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            const isNetworkError = error instanceof NetworkError && error.code >= 500;
            if (isNetworkError || error.retryable) {
              retryOperation();
            }
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexErrorHandling).toBe(true);
    });
  });

  describe('建议生成', () => {
    it('应该为复杂错误处理生成重构建议', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            if (error.type === 'NetworkError') {
              handleNetworkError(error);
            } else if (error.type === 'ValidationError') {
              handleValidationError(error);
            } else {
              handleGenericError(error);
            }
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      const refactorSuggestion = result.suggestions.find(s => 
        s.message.includes('Complex error handling logic detected')
      );
      expect(refactorSuggestion).toBeDefined();
      expect(refactorSuggestion?.type).toBe('refactor');
      expect(refactorSuggestion?.priority).toBe('high');
    });

    it('应该为嵌套try-catch生成重构建议', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            outerOperation();
          } catch (outerError) {
            try {
              fallbackOperation();
            } catch (innerError) {
              handleInnerError(innerError);
            }
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      const nestingSuggestion = result.suggestions.find(s => 
        s.message.includes('Nested try-catch statements detected')
      );
      expect(nestingSuggestion).toBeDefined();
      expect(nestingSuggestion?.type).toBe('refactor');
      expect(nestingSuggestion?.priority).toBe('high');
    });

    it('应该为没有错误参数的catch生成警告建议', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch {
            handleError();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      const warningSuggestion = result.suggestions.find(s => 
        s.message.includes('without error parameter')
      );
      expect(warningSuggestion).toBeDefined();
      expect(warningSuggestion?.type).toBe('warning');
      expect(warningSuggestion?.priority).toBe('medium');
    });

    it('应该为深度嵌套生成重构建议', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            handleError(error);
          }
        }
      `);

      const context = createMockContext(3); // 深度嵌套
      const result = await rule.evaluate(catchNode, context);

      const nestingSuggestion = result.suggestions.find(s => 
        s.message.includes('extracting nested catch logic')
      );
      expect(nestingSuggestion).toBeDefined();
      expect(nestingSuggestion?.type).toBe('refactor');
    });

    it('应该为简单catch子句提供最佳实践建议', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            console.log(error);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      const infoSuggestion = result.suggestions.find(s => 
        s.message.includes('specific error types')
      );
      expect(infoSuggestion).toBeDefined();
      expect(infoSuggestion?.type).toBe('info');
      expect(infoSuggestion?.priority).toBe('low');
    });
  });

  describe('元数据验证', () => {
    it('应该提供完整的元数据信息', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            if (error.retryable) {
              retryOperation();
            } else {
              logError(error);
            }
          }
        }
      `);

      const context = createMockContext(1);
      const result = await rule.evaluate(catchNode, context);

      expect(result.metadata).toMatchObject({
        nodeType: 'CatchClause',
        baseComplexity: 1,
        nestingLevel: 1,
        hasErrorParameter: true,
        hasComplexErrorHandling: true,
        hasNestedTryCatch: false,
      });
    });
  });

  describe('边界条件', () => {
    it('应该处理空catch块', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            // 空catch块
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexErrorHandling).toBe(false);
      expect(result.metadata?.hasNestedTryCatch).toBe(false);
    });

    it('应该处理只有注释的catch块', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            /* 忽略错误 */
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexErrorHandling).toBe(false);
    });

    it('应该处理单行简单错误处理', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            return null;
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexErrorHandling).toBe(false);
    });

    it('应该处理重新抛出错误的情况', async () => {
      const catchNode = await parseCatchClause(`
        function test() {
          try {
            riskyOperation();
          } catch (error) {
            logError(error);
            throw error;
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(catchNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexErrorHandling).toBe(false);
    });
  });
});