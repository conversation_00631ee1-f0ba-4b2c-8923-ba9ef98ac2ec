/**
 * WhileStatementRule测试用例
 * 验证while循环语句复杂度计算的正确性
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { WhileStatementRule } from '../../rules/while-statement-rule';
import type { AnalysisContext } from '../../engine/types';
import { parse } from '@swc/core';

// 创建模拟的分析上下文
function createMockContext(nestingLevel: number = 0): AnalysisContext {
  return {
    filePath: 'test.ts',
    fileContent: '',
    ast: {} as any,
    functionName: 'testFunction',
    nestingLevel,
    config: {} as any,
    jsxMode: 'standard',
    rules: { core: [], jsx: [], plugins: [] },
    cache: {
      getCachedRuleResult: async () => null,
      setCachedRuleResult: async () => {},
    } as any,
    metrics: {} as any,
    plugins: [],
    customData: new Map(),
  };
}

// 解析代码并获取第一个while语句节点
async function parseWhileStatement(code: string): Promise<any> {
  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  // 查找while语句节点
  function findWhileStatement(node: any): any {
    if (node?.type === 'WhileStatement' || node?.type === 'DoWhileStatement') {
      return node;
    }
    
    if (node && typeof node === 'object') {
      for (const key in node) {
        if (key !== 'parent') {
          const child = node[key];
          if (Array.isArray(child)) {
            for (const item of child) {
              const found = findWhileStatement(item);
              if (found) return found;
            }
          } else {
            const found = findWhileStatement(child);
            if (found) return found;
          }
        }
      }
    }
    
    return null;
  }

  return findWhileStatement(result);
}

describe('WhileStatementRule', () => {
  let rule: WhileStatementRule;

  beforeEach(() => {
    rule = new WhileStatementRule();
  });

  describe('基本属性', () => {
    it('应该有正确的规则ID和名称', () => {
      expect(rule.id).toBe('while-statement');
      expect(rule.name).toBe('While Statement Complexity');
      expect(rule.priority).toBe(500);
    });
  });

  describe('canHandle方法', () => {
    it('应该识别while语句', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (condition) {
            console.log('loop');
          }
        }
      `);

      expect(rule.canHandle(whileNode)).toBe(true);
    });

    it('应该识别do-while语句', async () => {
      const doWhileNode = await parseWhileStatement(`
        function test() {
          do {
            console.log('loop');
          } while (condition);
        }
      `);

      expect(rule.canHandle(doWhileNode)).toBe(true);
    });

    it('应该拒绝非while语句节点', async () => {
      const ifNode = { type: 'IfStatement' } as any;
      const forNode = { type: 'ForStatement' } as any;

      expect(rule.canHandle(ifNode)).toBe(false);
      expect(rule.canHandle(forNode)).toBe(false);
    });
  });

  describe('while循环', () => {
    it('应该为简单while循环返回基础复杂度1', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (i < 10) {
            i++;
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(true);
      expect(result.reason).toContain('While loop increases cognitive complexity by 1');
      expect(result.metadata?.loopType).toBe('while-loop');
      expect(result.metadata?.isDoWhile).toBe(false);
    });

    it('应该为带嵌套的while循环应用嵌套惩罚', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          if (condition) {
            while (i < 10) {
              i++;
            }
          }
        }
      `);

      const context = createMockContext(2); // 嵌套层级为2
      const result = await rule.evaluate(whileNode, context);

      expect(result.complexity).toBe(3); // 基础1 + 嵌套惩罚2
      expect(result.metadata?.nestingLevel).toBe(2);
      expect(result.reason).toContain('nesting penalty: +2');
    });

    it('应该检测复杂的循环条件', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (isValid(data) && data.length > 0 && !stopped) {
            process(data);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.reason).toContain('complex condition detected');
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(0);
    });

    it('应该检测条件中的赋值操作', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while ((line = reader.readLine()) !== null) {
            process(line);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(1); // 赋值增加额外复杂度
      
      const assignmentWarning = result.suggestions.find(s => 
        s.message.includes('Assignment in loop condition')
      );
      expect(assignmentWarning).toBeDefined();
      expect(assignmentWarning?.priority).toBe('high');
    });
  });

  describe('do-while循环', () => {
    it('应该为do-while循环返回基础复杂度1', async () => {
      const doWhileNode = await parseWhileStatement(`
        function test() {
          do {
            i++;
          } while (i < 10);
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(doWhileNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(true);
      expect(result.reason).toContain('Do-while loop increases cognitive complexity by 1');
      expect(result.metadata?.loopType).toBe('do-while-loop');
      expect(result.metadata?.isDoWhile).toBe(true);
      expect(result.reason).toContain('do-while executes body at least once');
    });

    it('应该为带嵌套的do-while循环应用嵌套惩罚', async () => {
      const doWhileNode = await parseWhileStatement(`
        function test() {
          if (condition) {
            do {
              process();
            } while (shouldContinue);
          }
        }
      `);

      const context = createMockContext(1);
      const result = await rule.evaluate(doWhileNode, context);

      expect(result.complexity).toBe(2); // 基础1 + 嵌套惩罚1
      expect(result.metadata?.nestingLevel).toBe(1);
      expect(result.metadata?.isDoWhile).toBe(true);
    });
  });

  describe('复杂条件分析', () => {
    it('应该计算包含多个逻辑运算符的条件复杂度', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (a && b || c && d) {
            process();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(1);
    });

    it('应该计算包含函数调用的条件复杂度', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (isValid(data) && checkPermission(user)) {
            process();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(0);
      
      const functionCallSuggestion = result.suggestions.find(s => 
        s.message.includes('Multiple function calls')
      );
      expect(functionCallSuggestion).toBeDefined();
    });

    it('应该检测条件中的复杂表达式', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (obj.deep.property.chain > threshold ? true : false) {
            process();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.metadata?.hasComplexCondition).toBe(true);
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(0);
    });
  });

  describe('建议生成', () => {
    it('应该为复杂条件生成重构建议', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (isValid(data) && data.length > 0 && !stopped && checkStatus()) {
            process();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      const conditionSuggestion = result.suggestions.find(s => 
        s.message.includes('Complex loop condition')
      );
      expect(conditionSuggestion).toBeDefined();
      expect(conditionSuggestion?.type).toBe('refactor');
    });

    it('应该为条件中的赋值生成警告', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while ((data = getData()) !== null) {
            process(data);
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      const assignmentWarning = result.suggestions.find(s => 
        s.message.includes('Assignment in loop condition')
      );
      expect(assignmentWarning).toBeDefined();
      expect(assignmentWarning?.type).toBe('warning');
      expect(assignmentWarning?.priority).toBe('high');
    });

    it('应该为深度嵌套生成重构建议', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (condition) {
            process();
          }
        }
      `);

      const context = createMockContext(3); // 深度嵌套
      const result = await rule.evaluate(whileNode, context);

      const nestingSuggestion = result.suggestions.find(s => 
        s.message.includes('extracting nested loop logic')
      );
      expect(nestingSuggestion).toBeDefined();
      expect(nestingSuggestion?.type).toBe('refactor');
    });

    it('应该为do-while提供信息性建议', async () => {
      const doWhileNode = await parseWhileStatement(`
        function test() {
          do {
            process();
          } while (condition);
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(doWhileNode, context);

      const doWhileInfo = result.suggestions.find(s => 
        s.message.includes('execute the body at least once')
      );
      expect(doWhileInfo).toBeDefined();
      expect(doWhileInfo?.type).toBe('info');
    });

    it('应该为潜在无限循环生成警告', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (true) {
            if (shouldBreak) break;
            process();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      const infiniteLoopWarning = result.suggestions.find(s => 
        s.message.includes('Potential infinite loop')
      );
      expect(infiniteLoopWarning).toBeDefined();
      expect(infiniteLoopWarning?.type).toBe('warning');
      expect(infiniteLoopWarning?.priority).toBe('high');
    });
  });

  describe('元数据验证', () => {
    it('应该提供完整的元数据信息', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (isValid(data) && data.active) {
            process(data);
          }
        }
      `);

      const context = createMockContext(1);
      const result = await rule.evaluate(whileNode, context);

      expect(result.metadata).toMatchObject({
        nodeType: 'WhileStatement',
        baseComplexity: 1,
        nestingLevel: 1,
        loopType: 'while-loop',
        hasComplexCondition: true,
        isDoWhile: false,
      });
      expect(result.metadata?.conditionComplexity).toBeGreaterThan(0);
    });
  });

  describe('边界条件', () => {
    it('应该处理空条件的while循环', async () => {
      // 注意：这种语法在实际代码中是不合法的，但测试边界情况
      const whileNode = {
        type: 'WhileStatement',
        test: null,
        body: { type: 'BlockStatement', stmts: [] }
      } as any;

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(false);
      expect(result.metadata?.conditionComplexity).toBe(0);
    });

    it('应该处理简单标识符条件', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (running) {
            process();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.hasComplexCondition).toBe(false);
    });

    it('应该处理数字字面量条件', async () => {
      const whileNode = await parseWhileStatement(`
        function test() {
          while (1) {
            if (shouldBreak) break;
            process();
          }
        }
      `);

      const context = createMockContext(0);
      const result = await rule.evaluate(whileNode, context);

      expect(result.complexity).toBe(1);
      
      const infiniteLoopWarning = result.suggestions.find(s => 
        s.message.includes('Potential infinite loop')
      );
      expect(infiniteLoopWarning).toBeDefined();
    });
  });
});