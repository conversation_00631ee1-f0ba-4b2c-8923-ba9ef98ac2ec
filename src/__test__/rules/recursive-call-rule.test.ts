/**
 * RecursiveCallRule测试用例
 * 验证递归调用复杂度计算的正确性
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { RecursiveCallRule } from '../../rules/recursive-call-rule';
import type { AnalysisContext } from '../../engine/types';
import { parse } from '@swc/core';

// 创建模拟的分析上下文
function createMockContext(functionName: string = 'testFunction', nestingLevel: number = 0): AnalysisContext {
  return {
    filePath: 'test.ts',
    fileContent: '',
    ast: {} as any,
    functionName,
    nestingLevel,
    config: {} as any,
    jsxMode: 'standard',
    rules: { core: [], jsx: [], plugins: [] },
    cache: {
      getCachedRuleResult: async () => null,
      setCachedRuleResult: async () => {},
    } as any,
    metrics: {} as any,
    plugins: [],
    customData: new Map(),
  };
}

// 解析代码并获取第一个调用表达式节点
async function parseCallExpression(code: string): Promise<any> {
  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  // 查找调用表达式节点
  function findCallExpression(node: any): any {
    if (node?.type === 'CallExpression') {
      return node;
    }
    
    if (node && typeof node === 'object') {
      for (const key in node) {
        if (key !== 'parent') {
          const child = node[key];
          if (Array.isArray(child)) {
            for (const item of child) {
              const found = findCallExpression(item);
              if (found) return found;
            }
          } else {
            const found = findCallExpression(child);
            if (found) return found;
          }
        }
      }
    }
    
    return null;
  }

  return findCallExpression(result);
}

describe('RecursiveCallRule', () => {
  let rule: RecursiveCallRule;

  beforeEach(() => {
    rule = new RecursiveCallRule();
  });

  describe('基本属性', () => {
    it('应该有正确的规则ID和名称', () => {
      expect(rule.id).toBe('recursive-call');
      expect(rule.name).toBe('Recursive Call Complexity');
      expect(rule.priority).toBe(600);
    });
  });

  describe('canHandle方法', () => {
    it('应该识别调用表达式', async () => {
      const callNode = await parseCallExpression(`
        function test() {
          someFunction();
        }
      `);

      expect(rule.canHandle(callNode)).toBe(true);
    });

    it('应该拒绝非调用表达式节点', async () => {
      const ifNode = { type: 'IfStatement' } as any;
      const identifierNode = { type: 'Identifier' } as any;

      expect(rule.canHandle(ifNode)).toBe(false);
      expect(rule.canHandle(identifierNode)).toBe(false);
    });
  });

  describe('直接递归调用检测', () => {
    it('应该检测直接递归调用', async () => {
      const callNode = await parseCallExpression(`
        function factorial(n) {
          if (n <= 1) return 1;
          return n * factorial(n - 1);
        }
      `);

      const context = createMockContext('factorial');
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(false); // 递归调用不增加嵌套
      expect(result.reason).toContain("Recursive function call 'factorial()' increases cognitive complexity by 1");
      expect(result.metadata?.recursionType).toBe('direct');
      expect(result.metadata?.isDirectRecursion).toBe(true);
      expect(result.metadata?.calleeName).toBe('factorial');
    });

    it('应该忽略非递归的函数调用', async () => {
      const callNode = await parseCallExpression(`
        function testFunction() {
          someOtherFunction();
        }
      `);

      const context = createMockContext('testFunction');
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a recursive call');
    });

    it('应该处理缺少函数名的情况', async () => {
      const callNode = await parseCallExpression(`
        function testFunction() {
          someFunction();
        }
      `);

      const context = createMockContext(''); // 空函数名
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a recursive call');
    });
  });

  describe('方法递归调用检测', () => {
    it('应该检测this.method()形式的递归调用', async () => {
      const callNode = await parseCallExpression(`
        class TreeNode {
          traverse() {
            this.traverse();
          }
        }
      `);

      const context = createMockContext('traverse');
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(1);
      expect(result.reason).toContain("Recursive method call 'this.traverse()' increases cognitive complexity by 1");
      expect(result.metadata?.recursionType).toBe('method');
      expect(result.metadata?.isMethodRecursion).toBe(true);
      expect(result.metadata?.callPattern).toBe('this.traverse()');
    });

    it('应该忽略非递归的方法调用', async () => {
      const callNode = await parseCallExpression(`
        class TestClass {
          method1() {
            this.method2();
          }
        }
      `);

      const context = createMockContext('method1');
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a recursive call');
    });
  });

  describe('建议生成', () => {
    it('应该为直接递归生成警告建议', async () => {
      const callNode = await parseCallExpression(`
        function fibonacci(n) {
          if (n <= 1) return n;
          return fibonacci(n-1) + fibonacci(n-2);
        }
      `);

      const context = createMockContext('fibonacci');
      const result = await rule.evaluate(callNode, context);

      const warningSuggestion = result.suggestions.find(s => 
        s.message.includes('Direct recursive function detected')
      );
      expect(warningSuggestion).toBeDefined();
      expect(warningSuggestion?.type).toBe('warning');
      expect(warningSuggestion?.priority).toBe('high');
    });

    it('应该为方法递归生成警告建议', async () => {
      const callNode = await parseCallExpression(`
        class BinaryTree {
          search(value) {
            this.search(value);
          }
        }
      `);

      const context = createMockContext('search');
      const result = await rule.evaluate(callNode, context);

      const warningSuggestion = result.suggestions.find(s => 
        s.message.includes('Recursive method call detected')
      );
      expect(warningSuggestion).toBeDefined();
      expect(warningSuggestion?.type).toBe('warning');
      expect(warningSuggestion?.priority).toBe('high');
    });

    it('应该为递归调用生成性能建议', async () => {
      const callNode = await parseCallExpression(`
        function expensiveRecursion(n) {
          if (n <= 0) return 1;
          return expensiveRecursion(n-1) + expensiveRecursion(n-2);
        }
      `);

      const context = createMockContext('expensiveRecursion');
      const result = await rule.evaluate(callNode, context);

      const performanceSuggestion = result.suggestions.find(s => 
        s.message.includes('stack overflow')
      );
      expect(performanceSuggestion).toBeDefined();
      expect(performanceSuggestion?.type).toBe('performance');
      expect(performanceSuggestion?.priority).toBe('medium');
    });

    it('应该为嵌套结构中的递归生成重构建议', async () => {
      const callNode = await parseCallExpression(`
        function processData(data) {
          if (data.hasChildren) {
            for (const child of data.children) {
              processData(child);
            }
          }
        }
      `);

      const context = createMockContext('processData', 2); // 嵌套层级为2
      const result = await rule.evaluate(callNode, context);

      const refactorSuggestion = result.suggestions.find(s => 
        s.message.includes('within nested structure')
      );
      expect(refactorSuggestion).toBeDefined();
      expect(refactorSuggestion?.type).toBe('refactor');
      expect(refactorSuggestion?.priority).toBe('medium');
    });

    it('应该为递归函数提供最佳实践建议', async () => {
      const callNode = await parseCallExpression(`
        function simpleRecursion(n) {
          if (n <= 0) return 0;
          return simpleRecursion(n - 1);
        }
      `);

      const context = createMockContext('simpleRecursion');
      const result = await rule.evaluate(callNode, context);

      const infoSuggestion = result.suggestions.find(s => 
        s.message.includes('Best practices for recursive functions')
      );
      expect(infoSuggestion).toBeDefined();
      expect(infoSuggestion?.type).toBe('info');
      expect(infoSuggestion?.priority).toBe('low');
    });
  });

  describe('元数据验证', () => {
    it('应该为直接递归提供完整的元数据', async () => {
      const callNode = await parseCallExpression(`
        function quickSort() {
          quickSort();
        }
      `);

      const context = createMockContext('quickSort', 1);
      const result = await rule.evaluate(callNode, context);

      expect(result.metadata).toMatchObject({
        nodeType: 'CallExpression',
        recursionType: 'direct',
        functionName: 'quickSort',
        calleeName: 'quickSort',
        isDirectRecursion: true,
        isMethodRecursion: false,
        callPattern: 'quickSort()',
      });
    });

    it('应该为方法递归提供完整的元数据', async () => {
      const callNode = await parseCallExpression(`
        class LinkedList {
          print() {
            this.print();
          }
        }
      `);

      const context = createMockContext('print');
      const result = await rule.evaluate(callNode, context);

      expect(result.metadata).toMatchObject({
        nodeType: 'CallExpression',
        recursionType: 'method',
        functionName: 'print',
        calleeName: 'print',
        isDirectRecursion: false,
        isMethodRecursion: true,
        callPattern: 'this.print()',
      });
    });
  });

  describe('边界条件', () => {
    it('应该处理没有callee的调用表达式', async () => {
      const malformedCallNode = {
        type: 'CallExpression',
        callee: null,
        arguments: []
      } as any;

      const context = createMockContext('testFunction');
      const result = await rule.evaluate(malformedCallNode, context);

      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a recursive call');
    });

    it('应该处理复杂的成员表达式', async () => {
      const callNode = await parseCallExpression(`
        function testFunction() {
          obj.deep.property.method();
        }
      `);

      const context = createMockContext('testFunction');
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a recursive call');
    });

    it('应该处理动态属性访问', async () => {
      const callNode = await parseCallExpression(`
        function testFunction() {
          obj[methodName]();
        }
      `);

      const context = createMockContext('testFunction');
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a recursive call');
    });

    it('应该处理箭头函数中的递归调用', async () => {
      const callNode = await parseCallExpression(`
        const factorial = (n) => {
          if (n <= 1) return 1;
          return n * factorial(n - 1);
        };
      `);

      const context = createMockContext('factorial');
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.recursionType).toBe('direct');
    });
  });

  describe('复杂递归场景', () => {
    it('应该处理相互递归（函数A调用函数B）', async () => {
      const callNode = await parseCallExpression(`
        function isEven(n) {
          if (n === 0) return true;
          return isOdd(n - 1);
        }
      `);

      const context = createMockContext('isEven');
      const result = await rule.evaluate(callNode, context);

      // 这不是直接递归，所以不应该被检测为递归
      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Not a recursive call');
    });

    it('应该处理内部函数的递归调用', async () => {
      const callNode = await parseCallExpression(`
        function outerFunction() {
          function innerRecursive(n) {
            if (n <= 0) return 0;
            return innerRecursive(n - 1);
          }
          return innerRecursive(5);
        }
      `);

      const context = createMockContext('innerRecursive');
      const result = await rule.evaluate(callNode, context);

      expect(result.complexity).toBe(1);
      expect(result.metadata?.recursionType).toBe('direct');
    });
  });
});