/**
 * JSX Hook复杂度分析规则基础测试
 */

import { describe, test, expect, beforeAll } from 'vitest';
import { JSXHookComplexityRule } from '../../rules/jsx-hook-complexity';
import { ASTParser } from '../../core/parser';

describe('JSXHookComplexityRule基础功能', () => {
  let rule: JSXHookComplexityRule;
  let parser: ASTParser;

  beforeAll(() => {
    rule = new JSXHookComplexityRule();
    parser = new ASTParser();
  });

  describe('canHandle方法', () => {
    test('应该识别useEffect调用', async () => {
      const code = `useEffect(() => {}, []);`;
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useEffect');
      
      expect(callExpression).toBeTruthy();
      expect(rule.canHandle(callExpression)).toBe(true);
    });

    test('应该识别useState调用', async () => {
      const code = `const [state, setState] = useState(0);`;
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useState');
      
      expect(callExpression).toBeTruthy();
      expect(rule.canHandle(callExpression)).toBe(true);
    });

    test('应该识别自定义Hook调用', async () => {
      const code = `const value = useCustomHook();`;
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useCustomHook');
      
      expect(callExpression).toBeTruthy();
      expect(rule.canHandle(callExpression)).toBe(true);
    });

    test('应该拒绝非Hook调用', async () => {
      const code = `const result = normalFunction();`;
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'normalFunction');
      
      expect(callExpression).toBeTruthy();
      expect(rule.canHandle(callExpression)).toBe(false);
    });
  });

  test('规则基本属性', () => {
    expect(rule.id).toBe('jsx.hook.complexity');
    expect(rule.name).toBe('JSX Hook Complexity Analysis');
    expect(rule.priority).toBe(700);
  });
});

// 辅助函数
function findCallExpression(ast: any, functionName: string): any {
  let found: any = null;
  
  const traverse = (node: any) => {
    if (!node || typeof node !== 'object') return;
    
    if (node.type === 'CallExpression' && 
        node.callee && 
        node.callee.type === 'Identifier' && 
        (node.callee.value === functionName || node.callee.name === functionName)) {
      found = node;
      return;
    }
    
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      const value = node[key];
      
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  };
  
  traverse(ast);
  return found;
}