/**
 * 智能条件渲染规则测试
 * 验证条件渲染智能分析的准确性和功能完整性
 */

import { describe, it, expect } from 'vitest';
import { SmartConditionalRenderingRule } from '../../../rules/smart-conditional-rendering';
import { createTestContext, createTestNode } from '../../helpers/test-utils';
import { DEFAULT_ENGINE_CONFIG } from '../../../engine/types';

describe('SmartConditionalRenderingRule', () => {
  const rule = new SmartConditionalRenderingRule();

  describe('Rule Configuration', () => {
    it('should have correct rule metadata', () => {
      expect(rule.id).toBe('jsx.conditional.rendering');
      expect(rule.name).toBe('Smart Conditional Rendering Analysis');
      expect(rule.priority).toBe(800);
    });

    it('should handle conditional expressions and logical expressions', () => {
      const conditionalNode = createTestNode('ConditionalExpression');
      const logicalAndNode = createTestNode('LogicalExpression', { operator: '&&' });
      const logicalOrNode = createTestNode('LogicalExpression', { operator: '||' });
      const logicalNullishNode = createTestNode('LogicalExpression', { operator: '??' });
      const otherNode = createTestNode('BinaryExpression');

      expect(rule.canHandle(conditionalNode)).toBe(true);
      expect(rule.canHandle(logicalAndNode)).toBe(true);
      expect(rule.canHandle(logicalOrNode)).toBe(true);
      expect(rule.canHandle(logicalNullishNode)).toBe(true);
      expect(rule.canHandle(otherNode)).toBe(false);
    });
  });

  describe('Simple Display Logic Detection', () => {
    it('should detect and exempt simple null checks in JSX context', async () => {
      const nullCheckNode = createTestNode('LogicalExpression', {
        operator: '&&',
        left: { type: 'Identifier', value: 'user' },
        right: { type: 'JSXElement' }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        fileContent: 'const Component = () => user && <div>Content</div>;',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              exemptions: {
                ...DEFAULT_ENGINE_CONFIG.rules.jsx.exemptions,
                simpleConditionals: true
              },
              scoring: {
                ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring,
                conditionalRendering: true
              }
            }
          }
        }
      });

      const result = await rule.evaluate(nullCheckNode, context);

      expect(result.isExempted).toBe(true);
      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Simple null/undefined check');
      expect(result.metadata.pattern).toBe('null-check');
      expect(result.metadata.exemptionType).toBe('simple-display');
    });

    it('should detect simple member expression null checks', async () => {
      const memberNullCheckNode = createTestNode('LogicalExpression', {
        operator: '&&',
        left: { 
          type: 'MemberExpression',
          object: { type: 'Identifier', value: 'user' },
          property: { type: 'Identifier', value: 'profile' }
        },
        right: { type: 'JSXElement' }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              exemptions: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.exemptions, simpleConditionals: true },
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const result = await rule.evaluate(memberNullCheckNode, context);

      expect(result.isExempted).toBe(true);
      expect(result.complexity).toBe(0);
      expect(result.metadata.pattern).toBe('null-check');
    });
  });

  describe('Permission Check Detection', () => {
    it('should detect permission checks with appropriate complexity', async () => {
      const permissionCheckNode = createTestNode('LogicalExpression', {
        operator: '&&',
        left: {
          type: 'MemberExpression',
          object: { type: 'Identifier', value: 'user' },
          property: { type: 'Identifier', value: 'canEdit' }
        },
        right: { type: 'JSXElement' }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const result = await rule.evaluate(permissionCheckNode, context);

      expect(result.isExempted).toBe(false);
      expect(result.complexity).toBe(1);
      expect(result.shouldIncreaseNesting).toBe(true);
      expect(result.reason).toContain('Permission or capability check');
      expect(result.metadata.pattern).toBe('permission');
    });

    it('should detect various permission property patterns', async () => {
      const testCases = [
        'canEdit', 'hasPermission', 'isAllowed', 'allowEdit', 'permitted'
      ];

      for (const propertyName of testCases) {
        const permissionNode = createTestNode('LogicalExpression', {
          operator: '&&',
          left: {
            type: 'MemberExpression',
            property: { type: 'Identifier', value: propertyName, name: propertyName }
          }
        });

        const context = createTestContext({
          filePath: 'test.tsx',
          config: {
            ...DEFAULT_ENGINE_CONFIG,
            rules: {
              ...DEFAULT_ENGINE_CONFIG.rules,
              jsx: {
                ...DEFAULT_ENGINE_CONFIG.rules.jsx,
                scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
              }
            }
          }
        });

        const result = await rule.evaluate(permissionNode, context);
        expect(result.metadata.pattern).toBe('permission');
      }
    });
  });

  describe('Nested Conditional Detection', () => {
    it('should detect nested conditional expressions with increasing complexity', async () => {
      const nestedConditionalNode = createTestNode('ConditionalExpression', {
        test: { type: 'Identifier', value: 'condition1' },
        consequent: {
          type: 'ConditionalExpression',
          test: { type: 'Identifier', value: 'condition2' },
          consequent: { type: 'JSXElement' },
          alternate: { type: 'JSXElement' }
        },
        alternate: { type: 'JSXElement' }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const result = await rule.evaluate(nestedConditionalNode, context);

      expect(result.isExempted).toBe(false);
      expect(result.complexity).toBeGreaterThan(1);
      expect(result.shouldIncreaseNesting).toBe(true);
      expect(result.reason).toContain('Nested conditional rendering');
      expect(result.metadata.pattern).toBe('nested');
      expect(result.metadata.depth).toBeGreaterThan(1);
    });

    it('should provide refactoring suggestions for deep nesting', async () => {
      const deepNestedNode = createTestNode('ConditionalExpression', {
        // 模拟深度嵌套
        consequent: {
          type: 'ConditionalExpression',
          consequent: {
            type: 'ConditionalExpression'
          }
        }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const result = await rule.evaluate(deepNestedNode, context);

      const refactorSuggestion = result.suggestions.find(s => 
        s.type === 'refactor' && s.message.includes('nested conditional')
      );
      
      expect(refactorSuggestion).toBeDefined();
      expect(refactorSuggestion?.priority).toBe('high');
      expect(refactorSuggestion?.codeExample).toContain('early returns');
    });
  });

  describe('Complex Logic Detection', () => {
    it('should detect complex logic with multiple conditions', async () => {
      const complexLogicNode = createTestNode('LogicalExpression', {
        operator: '&&',
        left: {
          type: 'LogicalExpression',
          operator: '||',
          left: { type: 'CallExpression' },
          right: { type: 'Identifier' }
        },
        right: { type: 'JSXElement' }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const result = await rule.evaluate(complexLogicNode, context);

      expect(result.isExempted).toBe(false);
      expect(result.complexity).toBeGreaterThan(1);
      expect(result.metadata.pattern).toBe('complex-logic');
      expect(result.reason).toContain('Complex conditional logic');
    });

    it('should apply mixed operator penalty', async () => {
      const mixedOperatorNode = createTestNode('LogicalExpression', {
        operator: '&&',
        left: {
          type: 'LogicalExpression',
          operator: '||', // 不同的操作符
          left: { type: 'Identifier' },
          right: { type: 'Identifier' }
        },
        right: { type: 'JSXElement' }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const result = await rule.evaluate(mixedOperatorNode, context);

      // 混合操作符应该增加复杂度
      expect(result.complexity).toBeGreaterThan(1);
      expect(result.metadata.conditions).toBeDefined();
    });
  });

  describe('JSX Context Awareness', () => {
    it('should reduce complexity in JSX context', async () => {
      const standardLogicNode = createTestNode('LogicalExpression', {
        operator: '&&',
        left: { type: 'Identifier', value: 'condition' },
        right: { type: 'JSXElement' }
      });

      const jsxContext = createTestContext({
        filePath: 'test.tsx', // JSX file
        fileContent: 'const Component = () => condition && <div />;',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              exemptions: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.exemptions, simpleConditionals: false },
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const nonJsxContext = createTestContext({
        filePath: 'test.js', // 非JSX文件
        fileContent: 'const result = condition && getValue();',
        config: jsxContext.config
      });

      const jsxResult = await rule.evaluate(standardLogicNode, jsxContext);
      const nonJsxResult = await rule.evaluate(standardLogicNode, nonJsxContext);

      // JSX上下文中的复杂度应该较低
      expect(jsxResult.complexity).toBeLessThanOrEqual(nonJsxResult.complexity);
    });
  });

  describe('Configuration Handling', () => {
    it('should respect disabled conditional rendering scoring', async () => {
      const conditionalNode = createTestNode('ConditionalExpression');
      
      const context = createTestContext({
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              scoring: {
                ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring,
                conditionalRendering: false // 禁用条件渲染计分
              }
            }
          }
        }
      });

      const result = await rule.evaluate(conditionalNode, context);

      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('disabled');
    });

    it('should respect simple conditionals exemption setting', async () => {
      const nullCheckNode = createTestNode('LogicalExpression', {
        operator: '&&',
        left: { type: 'Identifier' },
        right: { type: 'JSXElement' }
      });

      // 启用简单条件豁免
      const exemptContext = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              exemptions: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.exemptions, simpleConditionals: true },
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      // 禁用简单条件豁免
      const noExemptContext = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              exemptions: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.exemptions, simpleConditionals: false },
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const exemptResult = await rule.evaluate(nullCheckNode, exemptContext);
      const noExemptResult = await rule.evaluate(nullCheckNode, noExemptContext);

      expect(exemptResult.isExempted).toBe(true);
      expect(noExemptResult.isExempted).toBe(false);
    });
  });

  describe('Performance and Caching', () => {
    it('should utilize caching for repeated evaluations', async () => {
      const node = createTestNode('ConditionalExpression');
      const context = createTestContext();

      // 第一次评估
      const result1 = await rule.evaluate(node, context);
      expect(result1.cacheHit).toBe(false);

      // 第二次评估应该命中缓存
      const result2 = await rule.evaluate(node, context);
      expect(result2.cacheHit).toBe(true);
      expect(result2.complexity).toBe(result1.complexity);
    });
  });

  describe('Suggestion Generation', () => {
    it('should provide optimization suggestions for complex conditions', async () => {
      const highComplexityNode = createTestNode('LogicalExpression', {
        // 模拟高复杂度条件
        operator: '&&',
        left: {
          type: 'LogicalExpression',
          operator: '||',
          left: { type: 'CallExpression' },
          right: {
            type: 'LogicalExpression',
            operator: '&&',
            left: { type: 'CallExpression' },
            right: { type: 'CallExpression' }
          }
        }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const result = await rule.evaluate(highComplexityNode, context);

      expect(result.suggestions.length).toBeGreaterThan(0);
      
      const refactorSuggestion = result.suggestions.find(s => s.type === 'refactor');
      expect(refactorSuggestion).toBeDefined();
      expect(refactorSuggestion?.codeExample).toBeDefined();
      
      const optimizeSuggestion = result.suggestions.find(s => s.type === 'optimize');
      expect(optimizeSuggestion).toBeDefined();
    });

    it('should suggest useMemo for performance optimization', async () => {
      const complexLogicWithFunctionsNode = createTestNode('LogicalExpression', {
        operator: '&&',
        left: { type: 'CallExpression' },
        right: { type: 'JSXElement' }
      });

      const context = createTestContext({
        filePath: 'test.tsx',
        config: {
          ...DEFAULT_ENGINE_CONFIG,
          rules: {
            ...DEFAULT_ENGINE_CONFIG.rules,
            jsx: {
              ...DEFAULT_ENGINE_CONFIG.rules.jsx,
              scoring: { ...DEFAULT_ENGINE_CONFIG.rules.jsx.scoring, conditionalRendering: true }
            }
          }
        }
      });

      const result = await rule.evaluate(complexLogicWithFunctionsNode, context);

      const memoSuggestion = result.suggestions.find(s => 
        s.message.includes('useMemo') || s.codeExample?.includes('useMemo')
      );
      
      expect(memoSuggestion).toBeDefined();
    });
  });
});