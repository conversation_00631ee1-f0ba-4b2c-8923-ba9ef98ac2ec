/**
 * JSX Hook复杂度分析规则测试
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { JSXHookComplexityRule } from '../../rules/jsx-hook-complexity';
import { ASTParser } from '../../core/parser';
import type { AnalysisContext } from '../../engine/types';
import { IntelligentCacheManager } from '../../cache/manager';

describe('JSXHookComplexityRule', () => {
  let rule: JSXHookComplexityRule;
  let parser: ASTParser;
  let mockContext: AnalysisContext;

  beforeEach(() => {
    rule = new JSXHookComplexityRule();
    parser = new ASTParser();
    
    mockContext = {
      filePath: 'test.tsx',
      fileContent: '',
      ast: {} as any,
      currentFunction: {} as any,
      functionName: 'TestComponent',
      nestingLevel: 0,
      config: {
        rules: { jsx: { enabled: true } },
        performance: { maxConcurrency: 4 },
        output: { enableDebugInfo: false },
        plugins: { enabled: false }
      } as any,
      jsxMode: 'standard',
      rules: { core: [], jsx: [], plugins: [] },
      cache: new IntelligentCacheManager(),
      metrics: {
        totalNodes: 0,
        processedNodes: 0,
        cacheHits: 0,
        cacheMisses: 0,
        ruleExecutions: 0,
        parallelExecutions: 0,
        errors: 0,
      },
      plugins: [],
      customData: new Map(),
    };
  });

  describe('canHandle', () => {
    test('应该识别useEffect调用', async () => {
      const code = `
        useEffect(() => {
          console.log('effect');
        }, [dependency]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useEffect');
      
      expect(rule.canHandle(callExpression)).toBe(true);
    });

    test('应该识别useCallback调用', async () => {
      const code = `
        const callback = useCallback(() => {
          return value * 2;
        }, [value]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useCallback');
      
      expect(rule.canHandle(callExpression)).toBe(true);
    });

    test('应该识别useMemo调用', async () => {
      const code = `
        const memoValue = useMemo(() => {
          return expensiveCalculation(data);
        }, [data]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useMemo');
      
      expect(rule.canHandle(callExpression)).toBe(true);
    });

    test('应该识别自定义Hook调用', async () => {
      const code = `
        const value = useCustomHook(param1, param2);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useCustomHook');
      
      expect(rule.canHandle(callExpression)).toBe(true);
    });

    test('应该忽略非Hook调用', async () => {
      const code = `
        const result = normalFunction();
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'normalFunction');
      
      expect(rule.canHandle(callExpression)).toBe(false);
    });

    test('应该识别React.useEffect调用', async () => {
      const code = `
        React.useEffect(() => {
          console.log('effect');
        }, []);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findMemberCallExpression(ast, 'React', 'useEffect');
      
      expect(rule.canHandle(callExpression)).toBe(true);
    });
  });

  describe('useEffect 复杂度分析', () => {
    test('简单useEffect应该复杂度为0（豁免）', async () => {
      const code = `
        useEffect(() => {
          console.log('simple effect');
        }, []);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useEffect');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.isExempted).toBe(true);
      expect(result.complexity).toBe(0);
      expect(result.reason).toContain('Simple useEffect hook');
    });

    test('有条件逻辑的useEffect应该增加复杂度', async () => {
      const code = `
        useEffect(() => {
          if (condition) {
            doSomething();
          } else {
            doSomethingElse();
          }
        }, [condition]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useEffect');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.isExempted).toBe(false);
      expect(result.complexity).toBeGreaterThan(0);
      expect(result.shouldIncreaseNesting).toBe(true);
    });

    test('缺少依赖数组应该增加复杂度', async () => {
      const code = `
        useEffect(() => {
          console.log('effect without deps');
        });
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useEffect');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.complexity).toBeGreaterThan(0);
      expect(result.suggestions.some(s => s.message.includes('missing dependency array'))).toBe(true);
    });

    test('复杂依赖应该增加复杂度', async () => {
      const code = `
        useEffect(() => {
          console.log('effect');
        }, [user.profile.name, calculateValue(), condition ? a : b]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useEffect');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.complexity).toBeGreaterThan(0);
      expect(result.metadata.hasComplexDependencies).toBe(true);
    });

    test('过多依赖应该增加复杂度和建议', async () => {
      const code = `
        useEffect(() => {
          console.log('effect');
        }, [dep1, dep2, dep3, dep4, dep5, dep6, dep7]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useEffect');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.complexity).toBeGreaterThan(0);
      expect(result.metadata.dependencyCount).toBe(7);
      expect(result.suggestions.some(s => s.message.includes('Too many dependencies'))).toBe(true);
    });
  });

  describe('useCallback 复杂度分析', () => {
    test('简单useCallback应该复杂度为0', async () => {
      const code = `
        const callback = useCallback(() => {
          return value;
        }, [value]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useCallback');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.isExempted).toBe(true);
      expect(result.complexity).toBe(0);
    });

    test('复杂useCallback应该增加复杂度', async () => {
      const code = `
        const callback = useCallback(() => {
          if (condition) {
            for (let i = 0; i < items.length; i++) {
              processItem(items[i]);
            }
          }
          return result;
        }, [condition, items]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useCallback');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.isExempted).toBe(false);
      expect(result.complexity).toBeGreaterThan(0);
      expect(result.shouldIncreaseNesting).toBe(true);
    });
  });

  describe('useMemo 复杂度分析', () => {
    test('简单useMemo应该复杂度为0', async () => {
      const code = `
        const memoValue = useMemo(() => {
          return a + b;
        }, [a, b]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useMemo');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.isExempted).toBe(true);
      expect(result.complexity).toBe(0);
    });

    test('复杂计算的useMemo应该增加复杂度', async () => {
      const code = `
        const memoValue = useMemo(() => {
          if (condition) {
            return items.filter(item => item.active)
                       .map(item => item.value)
                       .reduce((sum, val) => sum + val, 0);
          }
          return 0;
        }, [condition, items]);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useMemo');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.isExempted).toBe(false);
      expect(result.complexity).toBeGreaterThan(0);
    });
  });

  describe('自定义Hook复杂度分析', () => {
    test('简单自定义Hook应该有适度复杂度', async () => {
      const code = `
        const value = useCustomHook(param);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useCustomHook');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.isExempted).toBe(false);
      expect(result.complexity).toBe(1); // 基础抽象复杂度
      expect(result.reason).toContain('Custom hook useCustomHook');
    });

    test('参数复杂的自定义Hook应该增加复杂度', async () => {
      const code = `
        const value = useCustomHook(
          { config: complexConfig },
          () => callback(),
          condition ? option1 : option2
        );
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useCustomHook');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.complexity).toBeGreaterThan(1);
      expect(result.suggestions.some(s => s.message.includes('complex parameters'))).toBe(true);
    });
  });

  describe('状态Hook复杂度分析', () => {
    test('简单useState应该复杂度为0', async () => {
      const code = `
        const [state, setState] = useState(initialValue);
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useState');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.isExempted).toBe(true);
      expect(result.complexity).toBe(0);
    });

    test('复杂初始化的useState应该增加复杂度', async () => {
      const code = `
        const [state, setState] = useState(() => {
          return condition ? expensiveCalculation() : defaultValue;
        });
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useState');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      expect(result.complexity).toBeGreaterThan(0);
      expect(result.suggestions.some(s => s.message.includes('Complex initialization'))).toBe(true);
    });
  });

  describe('边界情况', () => {
    test('应该处理不完整的Hook调用', async () => {
      const code = `
        useEffect();
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      const callExpression = findCallExpression(ast, 'useEffect');
      
      const result = await rule.evaluate(callExpression, mockContext);
      
      // 缺少参数应该增加复杂度（缺少依赖数组）
      expect(result.complexity).toBeGreaterThan(0);
    });

    test('应该处理无效的Hook调用节点', async () => {
      const invalidNode = {
        type: 'CallExpression',
        callee: null,
        arguments: []
      } as any;

      const result = await rule.evaluate(invalidNode, mockContext);
      
      expect(result.isExempted).toBe(false);
      expect(result.complexity).toBe(0);
      expect(result.reason).toBe('Not a valid hook call');
    });
  });
});

// 辅助函数
function findCallExpression(ast: any, functionName: string): any {
  let found: any = null;
  
  const traverse = (node: any) => {
    if (!node || typeof node !== 'object') return;
    
    if (node.type === 'CallExpression' && 
        node.callee && 
        node.callee.type === 'Identifier' && 
        (node.callee.value === functionName || node.callee.name === functionName)) {
      found = node;
      return;
    }
    
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      const value = node[key];
      
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  };
  
  traverse(ast);
  return found;
}

function findMemberCallExpression(ast: any, objectName: string, propertyName: string): any {
  let found: any = null;
  
  const traverse = (node: any) => {
    if (!node || typeof node !== 'object') return;
    
    if (node.type === 'CallExpression' && 
        node.callee && 
        node.callee.type === 'MemberExpression' &&
        node.callee.object &&
        node.callee.object.type === 'Identifier' &&
        (node.callee.object.value === objectName || node.callee.object.name === objectName) &&
        node.callee.property &&
        node.callee.property.type === 'Identifier' &&
        (node.callee.property.value === propertyName || node.callee.property.name === propertyName)) {
      found = node;
      return;
    }
    
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      const value = node[key];
      
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  };
  
  traverse(ast);
  return found;
}