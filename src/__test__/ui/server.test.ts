import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { UIServer } from "../../ui/server";
import type { CognitiveConfig } from "../../config/types";
import type { AnalysisResult } from "../../core/types";
import { TestUtils } from "../helpers/test-utils";
import { existsSync, unlinkSync } from "fs";
import { join } from "path";

describe("UIServer - Hono Migration Unit Tests", () => {
  let server: UIServer;
  let config: CognitiveConfig;
  const testResultPath = join(process.cwd(), '.cognitive-complexity-result.json');

  beforeEach(() => {
    // 创建测试配置
    config = {
      failOnComplexity: 10,
      exclude: [],
      report: {},
      severityMapping: [
        { level: 'Critical', threshold: 20 },
        { level: 'Warning', threshold: 10 }
      ],
      ui: {
        port: 0, // 使用随机端口避免冲突
        host: 'localhost',
        openBrowser: false
      }
    } as CognitiveConfig;

    // 清理之前的结果文件
    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }
  });

  afterEach(async () => {
    // 停止服务器
    if (server) {
      try {
        await server.stop();
      } catch (error) {
        // 忽略停止错误
      }
    }
    
    // 清理结果文件
    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }
  });

  describe("构造函数和初始化", () => {
    test("应该能够创建UIServer实例", () => {
      server = new UIServer(config, { openBrowser: false });
      expect(server).toBeDefined();
      expect(server).toBeInstanceOf(UIServer);
    });

    test("应该能够接受自定义选项", () => {
      const options = {
        port: 3001,
        host: '127.0.0.1',
        openBrowser: false,
        autoShutdown: true
      };
      
      server = new UIServer(config, options);
      expect(server).toBeDefined();
    });

    test("应该使用默认选项值", () => {
      server = new UIServer(config, { openBrowser: false });
      expect(server).toBeDefined();
      // 默认选项应该被正确设置（通过行为验证）
    });
  });

  describe("服务器生命周期管理", () => {
    test("应该能够启动服务器", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      const result = await server.start();
      
      expect(result).toHaveProperty('url');
      expect(result).toHaveProperty('port');
      expect(result.url).toMatch(/^http:\/\/localhost:\d+$/);
      expect(result.port).toBeGreaterThan(0);
    });

    test("应该能够停止服务器", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      await server.start();
      await expect(server.stop()).resolves.toBeUndefined();
    });

    test("应该能够处理端口冲突", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      // 启动第一个服务器
      const result1 = await server.start();
      expect(result1.port).toBeGreaterThan(0);
      
      // 尝试启动第二个服务器，应该自动找到新端口
      const server2 = new UIServer(config, { port: result1.port, openBrowser: false });
      
      try {
        const result2 = await server2.start();
        // 如果成功启动，端口应该不同
        expect(result2.port).not.toBe(result1.port);
        await server2.stop();
      } catch (error) {
        // 如果启动失败，这也是可以接受的（表示端口冲突处理正确）
        expect(error).toBeDefined();
      }
    });

    test("应该在指定端口范围内找到可用端口", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      const result = await server.start();
      // 应该在合理的端口范围内
      expect(result.port).toBeGreaterThanOrEqual(3000);
      expect(result.port).toBeLessThan(65536);
    });

    test("应该处理无效端口范围", async () => {
      server = new UIServer(config, { port: 65530, openBrowser: false }); // 接近端口上限
      
      // 这应该会失败或找到其他可用端口
      await expect(server.start()).resolves.toBeDefined();
    });
  });

  describe("结果存储系统", () => {
    beforeEach(() => {
      server = new UIServer(config, { openBrowser: false });
    });

    test("应该能够存储分析结果", async () => {
      const mockResult = TestUtils.createMockAnalysisResult({
        summary: {
          totalComplexity: 15,
          averageComplexity: 7.5,
          filesAnalyzed: 2,
          functionsAnalyzed: 2,
          highComplexityFunctions: 1
        }
      });

      await server.storeResult(mockResult);
      
      expect(existsSync(testResultPath)).toBe(true);
    });

    test("应该能够清理分析结果", async () => {
      const mockResult = TestUtils.createMockAnalysisResult();
      
      await server.storeResult(mockResult);
      expect(existsSync(testResultPath)).toBe(true);
      
      await server.cleanupResult();
      expect(existsSync(testResultPath)).toBe(false);
    });

    test("存储操作应该处理错误情况", async () => {
      const mockResult = TestUtils.createMockAnalysisResult();
      
      // 这不应该抛出错误，即使文件系统操作失败
      await expect(server.storeResult(mockResult)).resolves.toBeUndefined();
    });

    test("清理操作应该处理不存在的文件", async () => {
      // 清理不存在的文件不应该抛出错误
      await expect(server.cleanupResult()).resolves.toBeUndefined();
    });
  });

  describe("配置集成", () => {
    test("应该使用配置中的UI设置", () => {
      const customConfig = {
        ...config,
        ui: {
          port: 3002,
          host: '0.0.0.0',
          openBrowser: false
        }
      };

      server = new UIServer(customConfig);
      expect(server).toBeDefined();
    });

    test("应该处理缺少UI配置的情况", () => {
      const configWithoutUI = {
        failOnComplexity: 10,
        exclude: [],
        report: {},
        severityMapping: [
          { level: 'Critical', threshold: 20 },
          { level: 'Warning', threshold: 10 }
        ]
      } as CognitiveConfig;

      server = new UIServer(configWithoutUI);
      expect(server).toBeDefined();
    });
  });

  describe("错误处理", () => {
    test("应该处理无效的配置", () => {
      const invalidConfig = {} as CognitiveConfig;
      
      expect(() => new UIServer(invalidConfig)).not.toThrow();
    });

    test("启动失败时应该抛出有意义的错误", async () => {
      server = new UIServer(config, { port: 70000, openBrowser: false }); // 超出端口范围
      
      await expect(server.start()).rejects.toThrow();
    });

    test("多次停止服务器不应该出错", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      await server.start();
      await server.stop();
      
      // 第二次停止不应该出错
      await expect(server.stop()).resolves.toBeUndefined();
    });
  });

  describe("HtmlFormatter集成", () => {
    beforeEach(() => {
      server = new UIServer(config, { openBrowser: false });
    });

    test("应该正确集成HtmlFormatter", async () => {
      const mockResult = TestUtils.createMockAnalysisResult();
      await server.storeResult(mockResult);
      
      // 这个测试验证HtmlFormatter集成不会出错
      // 实际的HTML输出测试在HTTP集成测试中进行
      expect(server).toBeDefined();
    });
  });

  describe("Node.js兼容性验证", () => {
    test("应该只使用Node.js兼容的API", () => {
      server = new UIServer(config, { openBrowser: false });
      
      // 验证没有使用Bun特定的API
      // 这是一个编译时和运行时的验证
      expect(server).toBeDefined();
    });

    test("应该能够在Node.js环境中运行", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      // 完整的启动和停止流程应该在Node.js中工作
      const result = await server.start();
      expect(result).toBeDefined();
      
      await server.stop();
    });
  });
});