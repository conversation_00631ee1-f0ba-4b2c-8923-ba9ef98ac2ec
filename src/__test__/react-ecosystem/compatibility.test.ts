import { describe, test, expect, beforeAll } from 'vitest';
import { ComplexityCalculator } from '../../core/calculator';
import { TestUtils } from '../helpers/test-utils';

/**
 * React生态系统兼容性验证测试套件
 * 测试工具对不同React框架和模式的兼容性
 */
describe('React生态系统兼容性验证', () => {
  let calculator: ComplexityCalculator;

  beforeAll(() => {
    calculator = new ComplexityCalculator();
  });

  describe('主流React框架支持', () => {
    test('Next.js 应用结构支持', async () => {
      const nextjsCode = `
        // Next.js 页面组件
        import { GetServerSideProps } from 'next';
        import { useState, useEffect } from 'react';

        interface Props {
          initialData: any;
        }

        export default function NextPage({ initialData }: Props) {
          const [data, setData] = useState(initialData);
          const [loading, setLoading] = useState(false);

          useEffect(() => {
            if (!data) {
              setLoading(true);
              fetchData().then(result => {
                setData(result);
                setLoading(false);
              });
            }
          }, [data]);

          if (loading) {
            return <div>Loading...</div>;
          }

          return (
            <div>
              {data?.items?.map((item: any) => (
                <div key={item.id}>
                  {item.name}
                  {item.featured && <span>Featured</span>}
                </div>
              ))}
            </div>
          );
        }

        export const getServerSideProps: GetServerSideProps = async (context) => {
          try {
            const data = await fetchDataFromAPI(context.params?.id);
            return { props: { initialData: data } };
          } catch (error) {
            return { notFound: true };
          }
        };

        async function fetchDataFromAPI(id?: string) {
          if (!id) throw new Error('Missing ID');
          return await fetch(\`/api/data/\${id}\`).then(r => r.json());
        }

        async function fetchData() {
          return await fetch('/api/data').then(r => r.json());
        }
      `;

      const result = await calculator.calculateCode(nextjsCode, 'pages/index.tsx');
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      
      const componentFunction = result.find(fn => fn.name === 'NextPage');
      expect(componentFunction).toBeDefined();
      expect(componentFunction?.complexity).toBeGreaterThan(0);
    });

    test('Create React App (CRA) 结构支持', async () => {
      const craCode = `
        import React, { useState, useEffect, useCallback } from 'react';
        import './App.css';

        function App() {
          const [count, setCount] = useState(0);
          const [users, setUsers] = useState([]);
          const [loading, setLoading] = useState(true);
          const [error, setError] = useState(null);

          const fetchUsers = useCallback(async () => {
            try {
              setLoading(true);
              const response = await fetch('/api/users');
              if (!response.ok) {
                throw new Error('Failed to fetch users');
              }
              const userData = await response.json();
              setUsers(userData);
            } catch (err) {
              setError(err.message);
              console.error('Error fetching users:', err);
            } finally {
              setLoading(false);
            }
          }, []);

          useEffect(() => {
            fetchUsers();
          }, [fetchUsers]);

          const handleIncrement = () => {
            setCount(prev => prev + 1);
          };

          const handleReset = () => {
            if (window.confirm('Are you sure you want to reset?')) {
              setCount(0);
              setError(null);
            }
          };

          if (loading) {
            return (
              <div className="App">
                <div className="spinner">Loading...</div>
              </div>
            );
          }

          if (error) {
            return (
              <div className="App">
                <div className="error">
                  Error: {error}
                  <button onClick={fetchUsers}>Retry</button>
                </div>
              </div>
            );
          }

          return (
            <div className="App">
              <header className="App-header">
                <h1>Count: {count}</h1>
                <div>
                  <button onClick={handleIncrement}>Increment</button>
                  <button onClick={handleReset}>Reset</button>
                </div>
                <div className="users-list">
                  {users.length > 0 ? (
                    users.map(user => (
                      <div key={user.id} className="user-card">
                        <h3>{user.name}</h3>
                        <p>{user.email}</p>
                        {user.active && <span className="badge">Active</span>}
                      </div>
                    ))
                  ) : (
                    <p>No users found</p>
                  )}
                </div>
              </header>
            </div>
          );
        }

        export default App;
      `;

      const result = await calculator.calculateCode(craCode, 'src/App.tsx');
      
      expect(result).toBeDefined();
      const appFunction = result.find(fn => fn.name === 'App');
      expect(appFunction).toBeDefined();
      expect(appFunction?.complexity).toBeGreaterThan(5); // CRA应用通常有一定复杂度
    });

    test('Gatsby 静态站点生成器支持', async () => {
      const gatsbyCode = `
        import React from 'react';
        import { graphql, useStaticQuery } from 'gatsby';
        import { Helmet } from 'react-helmet';

        const IndexPage = () => {
          const data = useStaticQuery(graphql\`
            query {
              site {
                siteMetadata {
                  title
                  description
                }
              }
              allMarkdownRemark(sort: { fields: [frontmatter___date], order: DESC }) {
                edges {
                  node {
                    excerpt
                    fields {
                      slug
                    }
                    frontmatter {
                      date(formatString: "MMMM DD, YYYY")
                      title
                      description
                    }
                  }
                }
              }
            }
          \`);

          const posts = data.allMarkdownRemark.edges;
          const siteTitle = data.site.siteMetadata.title;

          return (
            <>
              <Helmet title={siteTitle} />
              <div className="gatsby-page">
                <h1>Welcome to {siteTitle}</h1>
                <div className="posts-container">
                  {posts && posts.length > 0 ? (
                    posts.map(({ node }, index) => {
                      const title = node.frontmatter.title || node.fields.slug;
                      return (
                        <article
                          key={node.fields.slug}
                          className={\`post-item \${index === 0 ? 'featured' : ''}\`}
                        >
                          <header>
                            <h3>
                              <Link to={node.fields.slug}>{title}</Link>
                            </h3>
                            <small>{node.frontmatter.date}</small>
                          </header>
                          <section>
                            <p
                              dangerouslySetInnerHTML={{
                                __html: node.frontmatter.description || node.excerpt,
                              }}
                            />
                          </section>
                        </article>
                      );
                    })
                  ) : (
                    <p>No blog posts found.</p>
                  )}
                </div>
              </div>
            </>
          );
        };

        export default IndexPage;

        export const pageQuery = graphql\`
          query BlogIndex {
            site {
              siteMetadata {
                title
              }
            }
          }
        \`;
      `;

      const result = await calculator.calculateCode(gatsbyCode, 'src/pages/index.tsx');
      
      expect(result).toBeDefined();
      const indexPageFunction = result.find(fn => fn.name === 'IndexPage');
      expect(indexPageFunction).toBeDefined();
    });
  });

  describe('TypeScript和JavaScript混合项目', () => {
    test('TypeScript React组件', async () => {
      const tsxCode = `
        import React, { useState, useEffect } from 'react';

        interface User {
          id: number;
          name: string;
          email: string;
          role: 'admin' | 'user' | 'guest';
        }

        interface Props {
          initialUsers?: User[];
          onUserSelect?: (user: User) => void;
          filterByRole?: User['role'];
        }

        const UserList: React.FC<Props> = ({ 
          initialUsers = [], 
          onUserSelect, 
          filterByRole 
        }) => {
          const [users, setUsers] = useState<User[]>(initialUsers);
          const [loading, setLoading] = useState<boolean>(false);
          const [selectedUser, setSelectedUser] = useState<User | null>(null);

          useEffect(() => {
            if (filterByRole) {
              const filtered = users.filter(user => user.role === filterByRole);
              setUsers(filtered);
            }
          }, [filterByRole, users]);

          const handleUserClick = (user: User): void => {
            setSelectedUser(user);
            if (onUserSelect) {
              onUserSelect(user);
            }
          };

          const renderUserRole = (role: User['role']): JSX.Element => {
            switch (role) {
              case 'admin':
                return <span className="role-admin">Administrator</span>;
              case 'user':
                return <span className="role-user">User</span>;
              case 'guest':
                return <span className="role-guest">Guest</span>;
              default:
                return <span className="role-unknown">Unknown</span>;
            }
          };

          if (loading) {
            return <div>Loading users...</div>;
          }

          return (
            <div className="user-list">
              {users.map(user => (
                <div 
                  key={user.id}
                  className={\`user-item \${selectedUser?.id === user.id ? 'selected' : ''}\`}
                  onClick={() => handleUserClick(user)}
                >
                  <h3>{user.name}</h3>
                  <p>{user.email}</p>
                  {renderUserRole(user.role)}
                </div>
              ))}
              {users.length === 0 && (
                <div className="empty-state">No users found</div>
              )}
            </div>
          );
        };

        export default UserList;
      `;

      const result = await calculator.calculateCode(tsxCode, 'components/UserList.tsx');
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      
      const userListFunction = result.find(fn => fn.name === 'UserList');
      const renderUserRoleFunction = result.find(fn => fn.name === 'renderUserRole');
      
      expect(userListFunction).toBeDefined();
      expect(renderUserRoleFunction).toBeDefined();
    });

    test('JavaScript React组件（JSX）', async () => {
      const jsxCode = `
        import React, { useState, useCallback } from 'react';
        import PropTypes from 'prop-types';

        function TaskManager({ initialTasks, onTaskUpdate }) {
          const [tasks, setTasks] = useState(initialTasks || []);
          const [filter, setFilter] = useState('all');
          const [newTaskText, setNewTaskText] = useState('');

          const addTask = useCallback(() => {
            if (newTaskText.trim()) {
              const newTask = {
                id: Date.now(),
                text: newTaskText.trim(),
                completed: false,
                createdAt: new Date().toISOString()
              };
              setTasks(prev => [...prev, newTask]);
              setNewTaskText('');
              if (onTaskUpdate) {
                onTaskUpdate([...tasks, newTask]);
              }
            }
          }, [newTaskText, tasks, onTaskUpdate]);

          const toggleTask = useCallback((taskId) => {
            setTasks(prev => 
              prev.map(task => 
                task.id === taskId 
                  ? { ...task, completed: !task.completed }
                  : task
              )
            );
          }, []);

          const deleteTask = useCallback((taskId) => {
            if (window.confirm('Are you sure you want to delete this task?')) {
              setTasks(prev => prev.filter(task => task.id !== taskId));
            }
          }, []);

          const getFilteredTasks = () => {
            switch (filter) {
              case 'completed':
                return tasks.filter(task => task.completed);
              case 'pending':
                return tasks.filter(task => !task.completed);
              case 'all':
              default:
                return tasks;
            }
          };

          const filteredTasks = getFilteredTasks();
          const completedCount = tasks.filter(task => task.completed).length;
          const totalCount = tasks.length;

          return (
            <div className="task-manager">
              <div className="task-input">
                <input
                  type="text"
                  value={newTaskText}
                  onChange={(e) => setNewTaskText(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addTask()}
                  placeholder="Add new task..."
                />
                <button onClick={addTask} disabled={!newTaskText.trim()}>
                  Add Task
                </button>
              </div>

              <div className="task-filters">
                {['all', 'pending', 'completed'].map(filterType => (
                  <button
                    key={filterType}
                    className={filter === filterType ? 'active' : ''}
                    onClick={() => setFilter(filterType)}
                  >
                    {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                  </button>
                ))}
              </div>

              <div className="task-stats">
                <span>{completedCount} of {totalCount} completed</span>
              </div>

              <div className="task-list">
                {filteredTasks.length > 0 ? (
                  filteredTasks.map(task => (
                    <div 
                      key={task.id} 
                      className={\`task-item \${task.completed ? 'completed' : ''}\`}
                    >
                      <input
                        type="checkbox"
                        checked={task.completed}
                        onChange={() => toggleTask(task.id)}
                      />
                      <span className="task-text">{task.text}</span>
                      <button 
                        className="delete-btn"
                        onClick={() => deleteTask(task.id)}
                      >
                        Delete
                      </button>
                    </div>
                  ))
                ) : (
                  <div className="empty-state">
                    {filter === 'all' 
                      ? 'No tasks yet. Add one above!' 
                      : \`No \${filter} tasks.\`
                    }
                  </div>
                )}
              </div>
            </div>
          );
        }

        TaskManager.propTypes = {
          initialTasks: PropTypes.array,
          onTaskUpdate: PropTypes.func
        };

        export default TaskManager;
      `;

      const result = await calculator.calculateCode(jsxCode, 'components/TaskManager.jsx');
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      
      const taskManagerFunction = result.find(fn => fn.name === 'TaskManager');
      expect(taskManagerFunction).toBeDefined();
      expect(taskManagerFunction?.complexity).toBeGreaterThan(8); // 相对复杂的组件
    });
  });

  describe('复杂React模式支持', () => {
    test('高阶组件 (HOC) 模式', async () => {
      const hocCode = `
        import React, { Component } from 'react';

        // HOC工厂函数
        function withAuth(WrappedComponent) {
          return class AuthenticatedComponent extends Component {
            constructor(props) {
              super(props);
              this.state = {
                isAuthenticated: false,
                isLoading: true,
                user: null,
                error: null
              };
            }

            async componentDidMount() {
              try {
                const token = localStorage.getItem('authToken');
                if (token) {
                  const response = await fetch('/api/verify-token', {
                    headers: { Authorization: \`Bearer \${token}\` }
                  });
                  
                  if (response.ok) {
                    const userData = await response.json();
                    this.setState({
                      isAuthenticated: true,
                      user: userData,
                      isLoading: false
                    });
                  } else {
                    throw new Error('Token verification failed');
                  }
                } else {
                  throw new Error('No token found');
                }
              } catch (error) {
                this.setState({
                  isAuthenticated: false,
                  isLoading: false,
                  error: error.message
                });
                
                if (this.props.onAuthError) {
                  this.props.onAuthError(error);
                }
              }
            }

            handleLogout = () => {
              if (window.confirm('Are you sure you want to logout?')) {
                localStorage.removeItem('authToken');
                this.setState({
                  isAuthenticated: false,
                  user: null
                });
                
                if (this.props.onLogout) {
                  this.props.onLogout();
                }
              }
            };

            render() {
              const { isAuthenticated, isLoading, user, error } = this.state;

              if (isLoading) {
                return <div className="auth-loading">Checking authentication...</div>;
              }

              if (!isAuthenticated) {
                return (
                  <div className="auth-error">
                    <h2>Authentication Required</h2>
                    <p>{error || 'Please log in to continue'}</p>
                    <button onClick={() => window.location.href = '/login'}>
                      Go to Login
                    </button>
                  </div>
                );
              }

              return (
                <WrappedComponent
                  {...this.props}
                  user={user}
                  onLogout={this.handleLogout}
                  isAuthenticated={isAuthenticated}
                />
              );
            }
          };
        }

        // 使用HOC的组件
        function Dashboard({ user, onLogout }) {
          return (
            <div className="dashboard">
              <header>
                <h1>Welcome, {user?.name}!</h1>
                <button onClick={onLogout}>Logout</button>
              </header>
              <main>
                <p>Dashboard content here...</p>
              </main>
            </div>
          );
        }

        export default withAuth(Dashboard);
      `;

      const result = await calculator.calculateCode(hocCode, 'components/Dashboard.jsx');
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(0);
      
      const withAuthFunction = result.find(fn => fn.name === 'withAuth');
      expect(withAuthFunction).toBeDefined();
      expect(withAuthFunction?.complexity).toBeGreaterThan(5);
    });

    test('Render Props 模式', async () => {
      const renderPropsCode = `
        import React, { Component } from 'react';

        class DataFetcher extends Component {
          constructor(props) {
            super(props);
            this.state = {
              data: null,
              loading: false,
              error: null,
              retryCount: 0
            };
          }

          componentDidMount() {
            if (this.props.url) {
              this.fetchData();
            }
          }

          componentDidUpdate(prevProps) {
            if (prevProps.url !== this.props.url && this.props.url) {
              this.fetchData();
            }
          }

          fetchData = async () => {
            const { url, onStart, onSuccess, onError } = this.props;
            const { retryCount } = this.state;

            try {
              this.setState({ loading: true, error: null });
              
              if (onStart) {
                onStart();
              }

              const response = await fetch(url, {
                method: this.props.method || 'GET',
                headers: {
                  'Content-Type': 'application/json',
                  ...this.props.headers
                },
                body: this.props.body ? JSON.stringify(this.props.body) : undefined
              });

              if (!response.ok) {
                throw new Error(\`HTTP error! status: \${response.status}\`);
              }

              const data = await response.json();
              
              this.setState({ 
                data, 
                loading: false, 
                error: null,
                retryCount: 0 
              });

              if (onSuccess) {
                onSuccess(data);
              }
            } catch (error) {
              this.setState({ 
                loading: false, 
                error: error.message,
                retryCount: retryCount + 1
              });

              if (onError) {
                onError(error);
              }

              // 自动重试逻辑
              if (retryCount < (this.props.maxRetries || 3)) {
                setTimeout(() => {
                  this.fetchData();
                }, Math.pow(2, retryCount) * 1000); // 指数退避
              }
            }
          };

          handleRetry = () => {
            this.setState({ retryCount: 0 });
            this.fetchData();
          };

          render() {
            const { data, loading, error, retryCount } = this.state;
            const { children, render } = this.props;

            const renderProps = {
              data,
              loading,
              error,
              retryCount,
              retry: this.handleRetry,
              refetch: this.fetchData
            };

            if (typeof children === 'function') {
              return children(renderProps);
            }

            if (render && typeof render === 'function') {
              return render(renderProps);
            }

            return null;
          }
        }

        // 使用 Render Props 的组件
        function UserProfile({ userId }) {
          return (
            <DataFetcher
              url={\`/api/users/\${userId}\`}
              maxRetries={2}
              onError={(error) => console.error('Failed to fetch user:', error)}
            >
              {({ data, loading, error, retry }) => {
                if (loading) {
                  return <div className="loading">Loading user profile...</div>;
                }

                if (error) {
                  return (
                    <div className="error">
                      <p>Error: {error}</p>
                      <button onClick={retry}>Retry</button>
                    </div>
                  );
                }

                if (!data) {
                  return <div>No user data available</div>;
                }

                return (
                  <div className="user-profile">
                    <h2>{data.name}</h2>
                    <p>Email: {data.email}</p>
                    <p>Role: {data.role}</p>
                    {data.avatar && (
                      <img src={data.avatar} alt={\`\${data.name}'s avatar\`} />
                    )}
                  </div>
                );
              }}
            </DataFetcher>
          );
        }

        export { DataFetcher, UserProfile };
      `;

      const result = await calculator.calculateCode(renderPropsCode, 'components/DataFetcher.jsx');
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(1);
      
      const dataFetcherRender = result.find(fn => fn.name === 'render');
      const userProfileFunction = result.find(fn => fn.name === 'UserProfile');
      
      expect(dataFetcherRender).toBeDefined();
      expect(userProfileFunction).toBeDefined();
    });

    test('复杂Hooks模式', async () => {
      const hooksCode = `
        import React, { useState, useEffect, useReducer, useContext, useCallback, useMemo, useRef } from 'react';

        // 自定义Hook - 数据获取
        function useDataFetcher(url, options = {}) {
          const [state, setState] = useState({
            data: null,
            loading: false,
            error: null
          });

          const abortControllerRef = useRef(null);

          const fetchData = useCallback(async () => {
            try {
              setState(prev => ({ ...prev, loading: true, error: null }));
              
              // 取消之前的请求
              if (abortControllerRef.current) {
                abortControllerRef.current.abort();
              }
              
              abortControllerRef.current = new AbortController();

              const response = await fetch(url, {
                ...options,
                signal: abortControllerRef.current.signal
              });

              if (!response.ok) {
                throw new Error(\`Error: \${response.status}\`);
              }

              const data = await response.json();
              setState({ data, loading: false, error: null });
            } catch (error) {
              if (error.name !== 'AbortError') {
                setState(prev => ({ ...prev, loading: false, error: error.message }));
              }
            }
          }, [url, options]);

          useEffect(() => {
            if (url) {
              fetchData();
            }

            return () => {
              if (abortControllerRef.current) {
                abortControllerRef.current.abort();
              }
            };
          }, [fetchData]);

          return { ...state, refetch: fetchData };
        }

        // 复杂状态管理Hook
        function useComplexState(initialState) {
          const reducer = (state, action) => {
            switch (action.type) {
              case 'SET_LOADING':
                return { ...state, loading: action.payload };
              case 'SET_DATA':
                return { ...state, data: action.payload, loading: false, error: null };
              case 'SET_ERROR':
                return { ...state, error: action.payload, loading: false };
              case 'RESET':
                return initialState;
              case 'UPDATE_ITEM':
                return {
                  ...state,
                  data: {
                    ...state.data,
                    items: state.data?.items?.map(item =>
                      item.id === action.payload.id
                        ? { ...item, ...action.payload.updates }
                        : item
                    )
                  }
                };
              case 'ADD_ITEM':
                return {
                  ...state,
                  data: {
                    ...state.data,
                    items: [...(state.data?.items || []), action.payload]
                  }
                };
              case 'REMOVE_ITEM':
                return {
                  ...state,
                  data: {
                    ...state.data,
                    items: state.data?.items?.filter(item => item.id !== action.payload)
                  }
                };
              default:
                throw new Error(\`Unknown action type: \${action.type}\`);
            }
          };

          const [state, dispatch] = useReducer(reducer, initialState);

          const actions = useMemo(() => ({
            setLoading: (loading) => dispatch({ type: 'SET_LOADING', payload: loading }),
            setData: (data) => dispatch({ type: 'SET_DATA', payload: data }),
            setError: (error) => dispatch({ type: 'SET_ERROR', payload: error }),
            reset: () => dispatch({ type: 'RESET' }),
            updateItem: (id, updates) => dispatch({ type: 'UPDATE_ITEM', payload: { id, updates } }),
            addItem: (item) => dispatch({ type: 'ADD_ITEM', payload: item }),
            removeItem: (id) => dispatch({ type: 'REMOVE_ITEM', payload: id })
          }), []);

          return [state, actions];
        }

        // 使用复杂Hooks的组件
        function ComplexHooksComponent({ apiUrl, initialData }) {
          const [localState, localActions] = useComplexState({
            data: initialData,
            loading: false,
            error: null
          });

          const { data: fetchedData, loading: fetchLoading, error: fetchError, refetch } = useDataFetcher(apiUrl);

          const [filter, setFilter] = useState('all');
          const [sortOrder, setSortOrder] = useState('asc');

          // 复杂的计算逻辑
          const processedData = useMemo(() => {
            const dataSource = fetchedData || localState.data;
            if (!dataSource?.items) return [];

            let filtered = dataSource.items;
            
            if (filter !== 'all') {
              filtered = filtered.filter(item => {
                switch (filter) {
                  case 'active':
                    return item.status === 'active';
                  case 'inactive':
                    return item.status === 'inactive';
                  case 'pending':
                    return item.status === 'pending';
                  default:
                    return true;
                }
              });
            }

            filtered.sort((a, b) => {
              const multiplier = sortOrder === 'asc' ? 1 : -1;
              if (a.name < b.name) return -1 * multiplier;
              if (a.name > b.name) return 1 * multiplier;
              return 0;
            });

            return filtered;
          }, [fetchedData, localState.data, filter, sortOrder]);

          const handleItemAction = useCallback((action, itemId, data = {}) => {
            switch (action) {
              case 'update':
                localActions.updateItem(itemId, data);
                break;
              case 'delete':
                if (window.confirm('Are you sure?')) {
                  localActions.removeItem(itemId);
                }
                break;
              case 'activate':
                localActions.updateItem(itemId, { status: 'active' });
                break;
              case 'deactivate':
                localActions.updateItem(itemId, { status: 'inactive' });
                break;
              default:
                console.warn(\`Unknown action: \${action}\`);
            }
          }, [localActions]);

          const isLoading = localState.loading || fetchLoading;
          const error = localState.error || fetchError;

          if (isLoading) {
            return <div>Loading complex data...</div>;
          }

          if (error) {
            return (
              <div>
                <p>Error: {error}</p>
                <button onClick={refetch}>Retry</button>
                <button onClick={localActions.reset}>Reset</button>
              </div>
            );
          }

          return (
            <div className="complex-hooks-component">
              <div className="controls">
                <select value={filter} onChange={(e) => setFilter(e.target.value)}>
                  <option value="all">All</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="pending">Pending</option>
                </select>
                <button onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}>
                  Sort {sortOrder === 'asc' ? '↑' : '↓'}
                </button>
                <button onClick={refetch}>Refresh</button>
              </div>

              <div className="items-list">
                {processedData.length > 0 ? (
                  processedData.map(item => (
                    <div key={item.id} className="item">
                      <h3>{item.name}</h3>
                      <p>Status: {item.status}</p>
                      <div className="item-actions">
                        <button onClick={() => handleItemAction('activate', item.id)}>
                          Activate
                        </button>
                        <button onClick={() => handleItemAction('deactivate', item.id)}>
                          Deactivate
                        </button>
                        <button onClick={() => handleItemAction('delete', item.id)}>
                          Delete
                        </button>
                      </div>
                    </div>
                  ))
                ) : (
                  <p>No items to display</p>
                )}
              </div>
            </div>
          );
        }

        export { useDataFetcher, useComplexState, ComplexHooksComponent };
      `;

      const result = await calculator.calculateCode(hooksCode, 'components/ComplexHooks.jsx');
      
      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(2);
      
      const useDataFetcherHook = result.find(fn => fn.name === 'useDataFetcher');
      const useComplexStateHook = result.find(fn => fn.name === 'useComplexState');
      const complexComponent = result.find(fn => fn.name === 'ComplexHooksComponent');
      
      expect(useDataFetcherHook).toBeDefined();
      expect(useComplexStateHook).toBeDefined(); 
      expect(complexComponent).toBeDefined();
      
      // 验证复杂度合理性
      expect(complexComponent?.complexity).toBeGreaterThan(10);
    });
  });

  describe('大型项目性能和稳定性', () => {
    test('大型React应用模拟', async () => {
      // 创建一个模拟大型React应用的代码结构
      const largeAppCode = `
        import React, { Suspense, lazy, useState, useEffect, useContext, useMemo } from 'react';
        import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

        // 懒加载组件
        const Dashboard = lazy(() => import('./pages/Dashboard'));
        const UserManagement = lazy(() => import('./pages/UserManagement'));
        const Analytics = lazy(() => import('./pages/Analytics'));
        const Settings = lazy(() => import('./pages/Settings'));

        // 上下文提供者
        const AppContext = React.createContext(null);

        function AppProvider({ children }) {
          const [user, setUser] = useState(null);
          const [theme, setTheme] = useState('light');
          const [notifications, setNotifications] = useState([]);
          const [permissions, setPermissions] = useState([]);
          const [loading, setLoading] = useState(true);

          useEffect(() => {
            // 初始化应用数据
            const initializeApp = async () => {
              try {
                setLoading(true);
                
                // 获取用户信息
                const userResponse = await fetch('/api/user/profile');
                if (userResponse.ok) {
                  const userData = await userResponse.json();
                  setUser(userData);
                  
                  // 获取用户权限
                  const permissionsResponse = await fetch(\`/api/user/\${userData.id}/permissions\`);
                  if (permissionsResponse.ok) {
                    const permissionsData = await permissionsResponse.json();
                    setPermissions(permissionsData);
                  }
                  
                  // 获取通知
                  const notificationsResponse = await fetch(\`/api/user/\${userData.id}/notifications\`);
                  if (notificationsResponse.ok) {
                    const notificationsData = await notificationsResponse.json();
                    setNotifications(notificationsData);
                  }
                  
                  // 应用用户主题偏好
                  if (userData.preferences?.theme) {
                    setTheme(userData.preferences.theme);
                  }
                } else {
                  throw new Error('Failed to authenticate');
                }
              } catch (error) {
                console.error('App initialization failed:', error);
                // 重定向到登录页面
                window.location.href = '/login';
              } finally {
                setLoading(false);
              }
            };

            initializeApp();
          }, []);

          const contextValue = useMemo(() => ({
            user,
            setUser,
            theme,
            setTheme,
            notifications,
            setNotifications,
            permissions,
            setPermissions,
            loading
          }), [user, theme, notifications, permissions, loading]);

          return (
            <AppContext.Provider value={contextValue}>
              {children}
            </AppContext.Provider>
          );
        }

        // 权限检查Hook
        function usePermissions() {
          const context = useContext(AppContext);
          if (!context) {
            throw new Error('usePermissions must be used within AppProvider');
          }

          const { permissions } = context;

          const hasPermission = (requiredPermission) => {
            if (!permissions || !Array.isArray(permissions)) {
              return false;
            }
            
            return permissions.some(permission => {
              if (typeof permission === 'string') {
                return permission === requiredPermission;
              }
              
              if (typeof permission === 'object' && permission.name) {
                return permission.name === requiredPermission && permission.granted;
              }
              
              return false;
            });
          };

          const hasAnyPermission = (requiredPermissions) => {
            if (!Array.isArray(requiredPermissions)) {
              return hasPermission(requiredPermissions);
            }
            
            return requiredPermissions.some(permission => hasPermission(permission));
          };

          const hasAllPermissions = (requiredPermissions) => {
            if (!Array.isArray(requiredPermissions)) {
              return hasPermission(requiredPermissions);
            }
            
            return requiredPermissions.every(permission => hasPermission(permission));
          };

          return {
            hasPermission,
            hasAnyPermission,
            hasAllPermissions,
            permissions
          };
        }

        // 受保护的路由组件
        function ProtectedRoute({ children, requiredPermission, requiredPermissions, requireAll = false }) {
          const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();
          const { user, loading } = useContext(AppContext);

          if (loading) {
            return <div className="loading-spinner">Loading...</div>;
          }

          if (!user) {
            return <Navigate to="/login" replace />;
          }

          let hasAccess = true;

          if (requiredPermission) {
            hasAccess = hasPermission(requiredPermission);
          } else if (requiredPermissions) {
            if (requireAll) {
              hasAccess = hasAllPermissions(requiredPermissions);
            } else {
              hasAccess = hasAnyPermission(requiredPermissions);
            }
          }

          if (!hasAccess) {
            return (
              <div className="access-denied">
                <h2>Access Denied</h2>
                <p>You don't have permission to view this page.</p>
                <button onClick={() => window.history.back()}>Go Back</button>
              </div>
            );
          }

          return children;
        }

        // 主应用组件
        function App() {
          return (
            <AppProvider>
              <Router>
                <div className="app">
                  <Suspense fallback={<div className="loading">Loading page...</div>}>
                    <Routes>
                      <Route path="/" element={<Navigate to="/dashboard" replace />} />
                      
                      <Route 
                        path="/dashboard" 
                        element={
                          <ProtectedRoute requiredPermission="dashboard.view">
                            <Dashboard />
                          </ProtectedRoute>
                        } 
                      />
                      
                      <Route 
                        path="/users" 
                        element={
                          <ProtectedRoute requiredPermissions={['users.view', 'admin']}>
                            <UserManagement />
                          </ProtectedRoute>
                        } 
                      />
                      
                      <Route 
                        path="/analytics" 
                        element={
                          <ProtectedRoute 
                            requiredPermissions={['analytics.view', 'reports.view']} 
                            requireAll={false}
                          >
                            <Analytics />
                          </ProtectedRoute>
                        } 
                      />
                      
                      <Route 
                        path="/settings" 
                        element={
                          <ProtectedRoute requiredPermission="settings.manage">
                            <Settings />
                          </ProtectedRoute>
                        } 
                      />
                      
                      <Route path="*" element={<div>404 - Page Not Found</div>} />
                    </Routes>
                  </Suspense>
                </div>
              </Router>
            </AppProvider>
          );
        }

        export default App;
      `;

      const startTime = performance.now();
      const result = await calculator.calculateCode(largeAppCode, 'App.tsx');
      const analysisTime = performance.now() - startTime;

      expect(result).toBeDefined();
      expect(result.length).toBeGreaterThan(3);
      
      // 验证能够处理复杂的React应用
      const appFunction = result.find(fn => fn.name === 'App');
      const appProviderFunction = result.find(fn => fn.name === 'AppProvider');
      const protectedRouteFunction = result.find(fn => fn.name === 'ProtectedRoute');
      
      expect(appFunction).toBeDefined();
      expect(appProviderFunction).toBeDefined();
      expect(protectedRouteFunction).toBeDefined();

      // 性能要求：大型应用分析应该在合理时间内完成
      expect(analysisTime).toBeLessThan(1000); // 1秒内完成

      console.log(`大型React应用分析完成: ${result.length}个函数, ${analysisTime.toFixed(2)}ms`);
    });
  });
});