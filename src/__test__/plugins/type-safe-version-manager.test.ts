/**
 * 类型安全版本管理器测试
 * 验证版本解析、比较和范围检查的类型安全性
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { 
  TypeSafeVersionManager,
  VersionParsingError,
  VersionComparisonError,
  VersionRangeError,
  type ValidatedVersionParts 
} from '../../plugins/type-safe-version-manager';

describe('TypeSafeVersionManager', () => {
  let versionManager: TypeSafeVersionManager;

  beforeEach(() => {
    versionManager = new TypeSafeVersionManager();
  });

  describe('parseVersion', () => {
    test('解析有效的版本字符串', () => {
      const result = versionManager.parseVersion('1.2.3');
      
      expect(result).toEqual({
        major: 1,
        minor: 2,
        patch: 3,
        prerelease: null,
        build: null,
        raw: '1.2.3'
      });
    });

    test('解析带预发布版本的字符串', () => {
      const result = versionManager.parseVersion('1.2.3-alpha.1');
      
      expect(result).toEqual({
        major: 1,
        minor: 2,
        patch: 3,
        prerelease: 'alpha.1',
        build: null,
        raw: '1.2.3-alpha.1'
      });
    });

    test('解析带构建元数据的版本字符串', () => {
      const result = versionManager.parseVersion('1.2.3+build.123');
      
      expect(result).toEqual({
        major: 1,
        minor: 2,
        patch: 3,
        prerelease: null,
        build: 'build.123',
        raw: '1.2.3+build.123'
      });
    });

    test('解析完整的版本字符串', () => {
      const result = versionManager.parseVersion('1.2.3-beta.2+build.456');
      
      expect(result).toEqual({
        major: 1,
        minor: 2,
        patch: 3,
        prerelease: 'beta.2',
        build: 'build.456',
        raw: '1.2.3-beta.2+build.456'
      });
    });

    test('拒绝无效的版本格式', () => {
      expect(() => versionManager.parseVersion('invalid')).toThrow(VersionParsingError);
      expect(() => versionManager.parseVersion('1.2')).toThrow(VersionParsingError);
      expect(() => versionManager.parseVersion('1.2.3.4')).toThrow(VersionParsingError);
      expect(() => versionManager.parseVersion('')).toThrow(VersionParsingError);
    });

    test('拒绝非字符串输入', () => {
      expect(() => versionManager.parseVersion(null as any)).toThrow(VersionParsingError);
      expect(() => versionManager.parseVersion(undefined as any)).toThrow(VersionParsingError);
      expect(() => versionManager.parseVersion(123 as any)).toThrow(VersionParsingError);
    });

    test('拒绝负数版本号', () => {
      expect(() => versionManager.parseVersion('-1.2.3')).toThrow(VersionParsingError);
      expect(() => versionManager.parseVersion('1.-2.3')).toThrow(VersionParsingError);
      expect(() => versionManager.parseVersion('1.2.-3')).toThrow(VersionParsingError);
    });

    test('处理前后空格', () => {
      const result = versionManager.parseVersion('  1.2.3  ');
      expect(result.raw).toBe('1.2.3');
      expect(result.major).toBe(1);
      expect(result.minor).toBe(2);
      expect(result.patch).toBe(3);
    });
  });

  describe('compareVersions', () => {
    test('比较相同版本', () => {
      expect(versionManager.compareVersions('1.2.3', '1.2.3')).toBe(0);
    });

    test('比较不同主版本', () => {
      expect(versionManager.compareVersions('2.0.0', '1.9.9')).toBe(1);
      expect(versionManager.compareVersions('1.0.0', '2.0.0')).toBe(-1);
    });

    test('比较不同次版本', () => {
      expect(versionManager.compareVersions('1.2.0', '1.1.9')).toBe(1);
      expect(versionManager.compareVersions('1.1.0', '1.2.0')).toBe(-1);
    });

    test('比较不同修订版本', () => {
      expect(versionManager.compareVersions('1.2.3', '1.2.2')).toBe(1);
      expect(versionManager.compareVersions('1.2.2', '1.2.3')).toBe(-1);
    });

    test('比较预发布版本', () => {
      expect(versionManager.compareVersions('1.2.3-alpha', '1.2.3')).toBe(-1);
      expect(versionManager.compareVersions('1.2.3', '1.2.3-alpha')).toBe(1);
      expect(versionManager.compareVersions('1.2.3-alpha', '1.2.3-beta')).toBe(-1);
      expect(versionManager.compareVersions('1.2.3-beta', '1.2.3-alpha')).toBe(1);
    });

    test('抛出错误对于无效版本', () => {
      expect(() => versionManager.compareVersions('invalid', '1.2.3')).toThrow(VersionComparisonError);
      expect(() => versionManager.compareVersions('1.2.3', 'invalid')).toThrow(VersionComparisonError);
    });
  });

  describe('satisfiesRange', () => {
    test('通配符范围', () => {
      expect(versionManager.satisfiesRange('1.2.3', '*')).toBe(true);
      expect(versionManager.satisfiesRange('0.0.1', '*')).toBe(true);
    });

    test('脱字符范围 (^)', () => {
      expect(versionManager.satisfiesRange('1.2.3', '^1.0.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.9.9', '^1.0.0')).toBe(true);
      expect(versionManager.satisfiesRange('2.0.0', '^1.0.0')).toBe(false);
      expect(versionManager.satisfiesRange('0.9.9', '^1.0.0')).toBe(false);
    });

    test('波浪号范围 (~)', () => {
      expect(versionManager.satisfiesRange('1.2.3', '~1.2.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.2.9', '~1.2.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.3.0', '~1.2.0')).toBe(false);
      expect(versionManager.satisfiesRange('1.1.9', '~1.2.0')).toBe(false);
    });

    test('大于等于范围 (>=)', () => {
      expect(versionManager.satisfiesRange('1.2.3', '>=1.2.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.2.0', '>=1.2.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.1.9', '>=1.2.0')).toBe(false);
    });

    test('小于等于范围 (<=)', () => {
      expect(versionManager.satisfiesRange('1.2.0', '<=1.2.3')).toBe(true);
      expect(versionManager.satisfiesRange('1.2.3', '<=1.2.3')).toBe(true);
      expect(versionManager.satisfiesRange('1.2.4', '<=1.2.3')).toBe(false);
    });

    test('大于范围 (>)', () => {
      expect(versionManager.satisfiesRange('1.2.1', '>1.2.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.2.0', '>1.2.0')).toBe(false);
    });

    test('小于范围 (<)', () => {
      expect(versionManager.satisfiesRange('1.1.9', '<1.2.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.2.0', '<1.2.0')).toBe(false);
    });

    test('连字符范围', () => {
      expect(versionManager.satisfiesRange('1.2.0', '1.0.0 - 1.5.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.0.0', '1.0.0 - 1.5.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.5.0', '1.0.0 - 1.5.0')).toBe(true);
      expect(versionManager.satisfiesRange('0.9.9', '1.0.0 - 1.5.0')).toBe(false);
      expect(versionManager.satisfiesRange('1.5.1', '1.0.0 - 1.5.0')).toBe(false);
    });

    test('或范围 (||)', () => {
      expect(versionManager.satisfiesRange('1.0.0', '1.0.0 || 2.0.0')).toBe(true);
      expect(versionManager.satisfiesRange('2.0.0', '1.0.0 || 2.0.0')).toBe(true);
      expect(versionManager.satisfiesRange('1.5.0', '1.0.0 || 2.0.0')).toBe(false);
    });

    test('精确匹配', () => {
      expect(versionManager.satisfiesRange('1.2.3', '1.2.3')).toBe(true);
      expect(versionManager.satisfiesRange('1.2.4', '1.2.3')).toBe(false);
    });

    test('抛出错误对于无效范围', () => {
      expect(() => versionManager.satisfiesRange('1.2.3', '')).toThrow(VersionRangeError);
      expect(() => versionManager.satisfiesRange('1.2.3', null as any)).toThrow(VersionRangeError);
    });

    test('抛出错误对于无效版本', () => {
      expect(() => versionManager.satisfiesRange('invalid', '^1.0.0')).toThrow(VersionRangeError);
    });
  });

  describe('validateVersionFormat', () => {
    test('验证有效版本格式', () => {
      const result = versionManager.validateVersionFormat('1.2.3');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('检测无效版本格式', () => {
      const result = versionManager.validateVersionFormat('invalid');
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]?.code).toBe('PARSING_ERROR');
    });

    test('检测空版本', () => {
      const result = versionManager.validateVersionFormat('');
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]?.code).toBe('EMPTY_VERSION');
    });

    test('检测非字符串类型', () => {
      const result = versionManager.validateVersionFormat(123 as any);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]?.code).toBe('INVALID_VERSION_TYPE');
    });

    test('警告过大的版本号', () => {
      const result = versionManager.validateVersionFormat('999999.999999.999999');
      expect(result.isValid).toBe(false); // 应该超过最大值限制
      expect(result.errors).toHaveLength(1);
    });

    test('检测预发布版本格式', () => {
      const validResult = versionManager.validateVersionFormat('1.2.3-alpha.1');
      expect(validResult.isValid).toBe(true);

      const invalidResult = versionManager.validateVersionFormat('1.2.3-@invalid');
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.some(e => e.code === 'INVALID_PRERELEASE_FORMAT')).toBe(true);
    });
  });

  describe('compareVersionsDetailed', () => {
    test('提供详细的兼容性信息', () => {
      const result = versionManager.compareVersionsDetailed('1.2.3', '1.2.4');
      
      expect(result.result).toBe(-1);
      expect(result.compatible).toBe(true);
      expect(result.reason).toContain('compatible');
    });

    test('识别主版本不兼容', () => {
      const result = versionManager.compareVersionsDetailed('1.2.3', '2.0.0');
      
      expect(result.result).toBe(-1);
      expect(result.compatible).toBe(false);
      expect(result.reason).toContain('Major version');
      expect(result.recommendation).toBeDefined();
    });

    test('处理相同版本', () => {
      const result = versionManager.compareVersionsDetailed('1.2.3', '1.2.3');
      
      expect(result.result).toBe(0);
      expect(result.compatible).toBe(true);
      expect(result.reason).toContain('compatible');
    });
  });

  describe('getSafeUpgradePath', () => {
    test('生成安全的升级路径', () => {
      const availableVersions = ['1.0.1', '1.1.0', '1.2.0', '2.0.0'] as const;
      const path = versionManager.getSafeUpgradePath('1.0.0', '1.2.0', availableVersions);

      expect(path.steps).toHaveLength(3);
      expect(path.totalSteps).toBe(3);
      expect(path.isDowngrade).toBe(false);
      expect(path.estimatedDuration).toBeGreaterThan(0);
    });

    test('识别降级情况', () => {
      const availableVersions = ['1.0.0'] as const;
      const path = versionManager.getSafeUpgradePath('1.2.0', '1.0.0', availableVersions);

      expect(path.steps).toHaveLength(0);
      expect(path.isDowngrade).toBe(true);
    });

    test('处理已经是目标版本的情况', () => {
      const availableVersions = ['1.2.0'] as const;
      const path = versionManager.getSafeUpgradePath('1.2.0', '1.2.0', availableVersions);

      expect(path.steps).toHaveLength(0);
      expect(path.totalSteps).toBe(0);
    });

    test('过滤无效版本', () => {
      const availableVersions = ['invalid', '1.1.0', 'also-invalid', '1.2.0'] as const;
      const path = versionManager.getSafeUpgradePath('1.0.0', '1.2.0', availableVersions);

      expect(path.steps).toHaveLength(2); // 只包含有效版本
    });
  });

  describe('错误处理', () => {
    test('VersionParsingError包含详细信息', () => {
      try {
        versionManager.parseVersion('invalid-version');
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error).toBeInstanceOf(VersionParsingError);
        expect(error.version).toBe('invalid-version');
        expect(error.reason).toBeDefined();
      }
    });

    test('VersionComparisonError包含版本信息', () => {
      try {
        versionManager.compareVersions('invalid', '1.0.0');
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error).toBeInstanceOf(VersionComparisonError);
        expect(error.version1).toBe('invalid');
        expect(error.version2).toBe('1.0.0');
      }
    });

    test('VersionRangeError包含范围和版本信息', () => {
      try {
        versionManager.satisfiesRange('invalid', '^1.0.0');
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error).toBeInstanceOf(VersionRangeError);
        expect(error.version).toBe('invalid');
        expect(error.range).toBe('^1.0.0');
      }
    });
  });

  describe('边界条件', () => {
    test('处理零版本', () => {
      const result = versionManager.parseVersion('0.0.0');
      expect(result.major).toBe(0);
      expect(result.minor).toBe(0);
      expect(result.patch).toBe(0);
    });

    test('处理极大版本号', () => {
      expect(() => versionManager.parseVersion('1000000.0.0')).toThrow(VersionParsingError);
    });

    test('处理空预发布版本', () => {
      expect(() => versionManager.parseVersion('1.2.3-')).toThrow(VersionParsingError);
    });

    test('处理复杂的预发布版本', () => {
      const result = versionManager.parseVersion('1.2.3-alpha.beta.1');
      expect(result.prerelease).toBe('alpha.beta.1');
    });
  });
});