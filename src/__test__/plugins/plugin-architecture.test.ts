import { test, expect, describe, beforeEach, afterEach } from 'vitest';
import { PluginManagerImpl } from '../../plugins/manager';
import { createMockAsyncRuleEngine } from '../helpers/test-utils';
import type { Plugin, PluginRule } from '../../plugins/types';
import type { Node, Module } from '@swc/core';
import type { AnalysisContext, RuleResult } from '../../engine/types';

describe('插件架构基础', () => {
  let pluginManager: PluginManagerImpl;
  let mockEngine: any;

  beforeEach(() => {
    mockEngine = createMockAsyncRuleEngine();
    pluginManager = new PluginManagerImpl(mockEngine);
  });

  afterEach(async () => {
    // 清理所有已加载的插件
    const allPlugins = pluginManager.getAllPlugins();
    for (const plugin of allPlugins) {
      try {
        await pluginManager.unloadPlugin(plugin.plugin.id);
      } catch (error) {
        // 忽略清理错误
      }
    }
  });

  test('应该能够创建插件管理器', () => {
    expect(pluginManager).toBeDefined();
    expect(pluginManager.getAllPlugins()).toHaveLength(0);
  });

  test('应该能够加载内联插件', async () => {
    const mockRule: PluginRule = {
      id: 'test-rule',
      name: 'Test Rule',
      priority: 100,
      pluginId: 'test-plugin',
      async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
        return {
          ruleId: this.id,
          complexity: 1,
          isExempted: false,
          shouldIncreaseNesting: false,
          reason: 'Test rule executed',
          suggestions: [],
          metadata: {},
          executionTime: 0,
          cacheHit: false,
        };
      },
      canHandle(node: Node): boolean {
        return node.type === 'Identifier';
      },
      getDependencies(): string[] {
        return [];
      },
    };

    const mockPlugin: Plugin = {
      id: 'test-plugin',
      name: 'Test Plugin',
      version: '1.0.0',
      description: 'A test plugin',
      rules: [mockRule],
    };

    const loadedPlugin = await pluginManager.loadPlugin({
      type: 'inline',
      plugin: mockPlugin,
    });

    expect(loadedPlugin).toBeDefined();
    expect(loadedPlugin.plugin.id).toBe('test-plugin');
    expect(loadedPlugin.status).toBe('active');
    expect(loadedPlugin.rules.size).toBe(1);
    expect(pluginManager.getAllPlugins()).toHaveLength(1);
  });

  test('应该能够卸载插件', async () => {
    const mockPlugin: Plugin = {
      id: 'test-plugin-2',
      name: 'Test Plugin 2',
      version: '1.0.0',
      rules: [{
        id: 'test-rule-2',
        name: 'Test Rule 2',
        priority: 100,
        pluginId: 'test-plugin-2',
        async evaluate(): Promise<RuleResult> {
          return {
            ruleId: 'test-rule-2',
            complexity: 0,
            isExempted: true,
            shouldIncreaseNesting: false,
            reason: 'Test',
            suggestions: [],
            metadata: {},
            executionTime: 0,
            cacheHit: false,
          };
        },
        canHandle(): boolean { return false; },
        getDependencies(): string[] { return []; },
      }],
    };

    await pluginManager.loadPlugin({
      type: 'inline',
      plugin: mockPlugin,
    });

    expect(pluginManager.getAllPlugins()).toHaveLength(1);

    await pluginManager.unloadPlugin('test-plugin-2');

    expect(pluginManager.getAllPlugins()).toHaveLength(0);
    expect(pluginManager.getPlugin('test-plugin-2')).toBeNull();
  });

  test('应该能够启用和禁用插件', async () => {
    const mockPlugin: Plugin = {
      id: 'test-plugin-3',
      name: 'Test Plugin 3',
      version: '1.0.0',
      rules: [{
        id: 'test-rule-3',
        name: 'Test Rule 3',
        priority: 100,
        pluginId: 'test-plugin-3',
        async evaluate(): Promise<RuleResult> {
          return {
            ruleId: 'test-rule-3',
            complexity: 0,
            isExempted: true,
            shouldIncreaseNesting: false,
            reason: 'Test',
            suggestions: [],
            metadata: {},
            executionTime: 0,
            cacheHit: false,
          };
        },
        canHandle(): boolean { return false; },
        getDependencies(): string[] { return []; },
      }],
    };

    const loadedPlugin = await pluginManager.loadPlugin({
      type: 'inline',
      plugin: mockPlugin,
    });

    expect(loadedPlugin.status).toBe('active');
    expect(pluginManager.getActivePlugins()).toHaveLength(1);

    await pluginManager.disablePlugin('test-plugin-3');

    const disabledPlugin = pluginManager.getPlugin('test-plugin-3');
    expect(disabledPlugin?.status).toBe('inactive');
    expect(pluginManager.getActivePlugins()).toHaveLength(0);
    expect(pluginManager.getInactivePlugins()).toHaveLength(1);

    await pluginManager.enablePlugin('test-plugin-3');

    const enabledPlugin = pluginManager.getPlugin('test-plugin-3');
    expect(enabledPlugin?.status).toBe('active');
    expect(pluginManager.getActivePlugins()).toHaveLength(1);
  });

  test('应该能够获取插件统计信息', async () => {
    const stats = pluginManager.getPluginStatistics();
    
    expect(stats).toBeDefined();
    expect(stats.totalPlugins).toBe(0);
    expect(stats.activePlugins).toBe(0);
    expect(stats.loadedRules).toBe(0);

    // 加载一个插件后再次检查
    const mockPlugin: Plugin = {
      id: 'stats-test-plugin',
      name: 'Stats Test Plugin',
      version: '1.0.0',
      rules: [
        {
          id: 'stats-rule-1',
          name: 'Stats Rule 1',
          priority: 100,
          pluginId: 'stats-test-plugin',
          category: 'test',
          async evaluate(): Promise<RuleResult> {
            return {
              ruleId: 'stats-rule-1',
              complexity: 0,
              isExempted: false,
              shouldIncreaseNesting: false,
              reason: 'Test',
              suggestions: [],
              metadata: {},
              executionTime: 0,
              cacheHit: false,
            };
          },
          canHandle(): boolean { return false; },
          getDependencies(): string[] { return []; },
        },
        {
          id: 'stats-rule-2',
          name: 'Stats Rule 2',
          priority: 200,
          pluginId: 'stats-test-plugin',
          category: 'test',
          async evaluate(): Promise<RuleResult> {
            return {
              ruleId: 'stats-rule-2',
              complexity: 1,
              isExempted: false,
              shouldIncreaseNesting: true,
              reason: 'Test',
              suggestions: [],
              metadata: {},
              executionTime: 0,
              cacheHit: false,
            };
          },
          canHandle(): boolean { return true; },
          getDependencies(): string[] { return []; },
        },
      ],
    };

    await pluginManager.loadPlugin({
      type: 'inline',
      plugin: mockPlugin,
    });

    const newStats = pluginManager.getPluginStatistics();
    expect(newStats.totalPlugins).toBe(1);
    expect(newStats.activePlugins).toBe(1);
    expect(newStats.loadedRules).toBe(2);
    expect(newStats.byCategory.test).toBe(2);
    expect(newStats.byStatus.active).toBe(1);
  });

  test('应该能够验证插件', async () => {
    const validPlugin: Plugin = {
      id: 'valid-plugin',
      name: 'Valid Plugin',
      version: '1.0.0',
      rules: [{
        id: 'valid-rule',
        name: 'Valid Rule',
        priority: 100,
        pluginId: 'valid-plugin',
        async evaluate(): Promise<RuleResult> {
          return {
            ruleId: 'valid-rule',
            complexity: 0,
            isExempted: false,
            shouldIncreaseNesting: false,
            reason: 'Valid',
            suggestions: [],
            metadata: {},
            executionTime: 0,
            cacheHit: false,
          };
        },
        canHandle(): boolean { return false; },
        getDependencies(): string[] { return []; },
      }],
    };

    const validationResult = await pluginManager.validatePlugin(validPlugin);
    expect(validationResult.isValid).toBe(true);
    expect(validationResult.errors).toHaveLength(0);
  });

  test('应该能够处理插件事件', async () => {
    const events: string[] = [];

    pluginManager.on('plugin:loading', () => events.push('loading'));
    pluginManager.on('plugin:loaded', () => events.push('loaded'));
    pluginManager.on('plugin:unloading', () => events.push('unloading'));
    pluginManager.on('plugin:unloaded', () => events.push('unloaded'));

    const mockPlugin: Plugin = {
      id: 'event-test-plugin',
      name: 'Event Test Plugin',
      version: '1.0.0',
      rules: [{
        id: 'event-rule',
        name: 'Event Rule',
        priority: 100,
        pluginId: 'event-test-plugin',
        async evaluate(): Promise<RuleResult> {
          return {
            ruleId: 'event-rule',
            complexity: 0,
            isExempted: false,
            shouldIncreaseNesting: false,
            reason: 'Event test',
            suggestions: [],
            metadata: {},
            executionTime: 0,
            cacheHit: false,
          };
        },
        canHandle(): boolean { return false; },
        getDependencies(): string[] { return []; },
      }],
    };

    await pluginManager.loadPlugin({
      type: 'inline',
      plugin: mockPlugin,
    });

    await pluginManager.unloadPlugin('event-test-plugin');

    expect(events).toContain('loading');
    expect(events).toContain('loaded');
    expect(events).toContain('unloading');
    expect(events).toContain('unloaded');
  });
});