import { describe, test, expect, beforeEach, afterEach, vi } from "vitest";
import {
  Plugin,
  PluginRule,
  PluginManager,
  LoadedPlugin,
  PluginSource,
  PluginStatus,
  PluginMetadata,
  PluginStatistics,
  PluginHealth,
  PluginEvent,
  PluginEventData,
  PluginEventHandler,
  ValidationResult,
  DependencyValidationResult,
  PluginError,
  PluginSandbox,
  SandboxPermissions,
  PluginConfigUpdateOptions,
  VersionConflict,
  HealthIssue
} from "../../plugins/types";
import { PluginManagerImpl } from "../../plugins/manager";
import { createMockAsyncRuleEngine } from "../helpers/test-utils";
import {
  expectType,
  TypeValidator,
  TypeSafeTestFactory,
  AdvancedTypeValidator,
  TypeSafeTestHelper
} from "../helpers/type-testing-utils";
import { TestUtils, PerformanceTestUtils } from "../helpers/test-utils";
import { FixtureManager } from "../helpers/fixture-manager";
import type { AsyncRuleEngine } from "../../engine/types";

describe('Enhanced Plugin Manager Type Safety Tests', () => {
  let mockEngine: AsyncRuleEngine;
  let pluginManager: PluginManagerImpl;
  let pluginValidator: TypeValidator<Plugin>;
  let loadedPluginValidator: TypeValidator<LoadedPlugin>;

  const createMockPlugin = (id: string = 'test-plugin'): Plugin => ({
    id,
    name: `Test Plugin ${id}`,
    version: '1.0.0',
    description: 'A test plugin',
    author: 'Test Author',
    rules: [
      {
        id: `${id}-rule`,
        pluginId: id,
        name: `${id} Rule`,
        description: 'Test rule',
        category: 'test',
        priority: 1,
        apply: vi.fn().mockResolvedValue({ complexity: 1, details: [] })
      } as PluginRule
    ],
    configSchema: {
      type: 'object',
      properties: {
        enabled: { type: 'boolean', default: true }
      }
    },
    dependencies: [],
    peerDependencies: [],
    engineVersion: '>=1.0.0'
  });

  beforeEach(() => {
    mockEngine = createMockAsyncRuleEngine();
    pluginManager = new PluginManagerImpl(mockEngine);
    pluginValidator = expectType<Plugin>('PluginValidator');
    loadedPluginValidator = expectType<LoadedPlugin>('LoadedPluginValidator');
  });

  afterEach(() => {
    pluginManager.watchPlugins(false);
  });

  describe('Plugin Interface Type Safety', () => {
    test('should validate Plugin interface structure', async () => {
      const plugin = createMockPlugin();
      
      // 编译时类型检查
      expectType<Plugin>().toBeAssignableTo<{
        readonly id: string;
        readonly name: string;
        readonly version: string;
        readonly description?: string;
        readonly author?: string;
        readonly homepage?: string;
        readonly keywords?: string[];
        readonly rules: PluginRule[];
        readonly configSchema?: any;
        readonly dependencies?: string[];
        readonly peerDependencies?: string[];
        readonly engineVersion?: string;
      }>();

      pluginValidator.toAccept(plugin);

      // 验证必需属性
      expect(plugin.id).toBeDefined();
      expect(plugin.name).toBeDefined();
      expect(plugin.version).toBeDefined();
      expect(Array.isArray(plugin.rules)).toBe(true);
    });

    test('should validate PluginRule extends Rule interface', async () => {
      const plugin = createMockPlugin();
      const rule = plugin.rules[0];

      expectType<PluginRule>().toBeAssignableTo<{
        readonly pluginId: string;
        readonly category?: string;
        readonly tags?: string[];
        readonly isExperimental?: boolean;
        readonly requiresConfig?: boolean;
        readonly configKey?: string;
      }>();

      expect(rule.pluginId).toBe(plugin.id);
      expect(typeof rule.apply).toBe('function');
    });

    test('should validate optional lifecycle hooks type signatures', () => {
      const pluginWithHooks: Plugin = {
        ...createMockPlugin(),
        onLoad: vi.fn().mockResolvedValue(undefined),
        onUnload: vi.fn().mockResolvedValue(undefined),
        onConfigChange: vi.fn().mockResolvedValue(undefined),
        getConfigDefaults: vi.fn().mockReturnValue({}),
        validateConfig: vi.fn().mockReturnValue({ isValid: true, errors: [] }),
        getMetadata: vi.fn().mockReturnValue({})
      };

      // 验证生命周期钩子的类型签名
      expect(typeof pluginWithHooks.onLoad).toBe('function');
      expect(typeof pluginWithHooks.onUnload).toBe('function');
      expect(typeof pluginWithHooks.onConfigChange).toBe('function');
      expect(typeof pluginWithHooks.getConfigDefaults).toBe('function');
      expect(typeof pluginWithHooks.validateConfig).toBe('function');
      expect(typeof pluginWithHooks.getMetadata).toBe('function');
    });

    test('should ensure Plugin properties are readonly', () => {
      const plugin = createMockPlugin();
      
      // TypeScript 编译时应该阻止这些操作
      // @ts-expect-error - readonly property should not be assignable
      // plugin.id = 'modified-id';
      
      // @ts-expect-error - readonly property should not be assignable
      // plugin.version = '2.0.0';
      
      // @ts-expect-error - readonly array should not be assignable
      // plugin.rules = [];
      
      expect(plugin.id).toBeDefined();
      expect(plugin.version).toBeDefined();
      expect(plugin.rules).toBeDefined();
    });
  });

  describe('PluginManager Interface Type Safety', () => {
    test('should validate PluginManager interface methods', () => {
      // 验证插件管理器接口类型结构
      expectType<PluginManager>().toBeAssignableTo<{
        loadPlugin(source: PluginSource): Promise<LoadedPlugin>;
        unloadPlugin(pluginId: string): Promise<void>;
        reloadPlugin(pluginId: string): Promise<void>;
        getPlugin(pluginId: string): LoadedPlugin | null;
        getAllPlugins(): LoadedPlugin[];
        getActivePlugins(): LoadedPlugin[];
        getInactivePlugins(): LoadedPlugin[];
        enablePlugin(pluginId: string): Promise<void>;
        disablePlugin(pluginId: string): Promise<void>;
        resolveDependencies(pluginId: string): string[];
        validateDependencies(pluginId: string): DependencyValidationResult;
        updatePluginConfig(pluginId: string, config: any): Promise<void>;
        getPluginConfig(pluginId: string): any;
        getPluginStatistics(): PluginStatistics;
        getPluginHealth(pluginId: string): PluginHealth;
        watchPlugins(enabled: boolean): void;
        isWatching(): boolean;
        validatePlugin(plugin: Plugin): ValidationResult;
        createSandbox(pluginId: string): any;
        on(event: PluginEvent, handler: PluginEventHandler): void;
        off(event: PluginEvent, handler: PluginEventHandler): void;
        emit(event: PluginEvent, data: any): void;
      }>();

      expect(pluginManager).toBeInstanceOf(PluginManagerImpl);
    });

    test('should validate PluginSource union type', async () => {
      const fileSources: PluginSource[] = [
        { type: 'file', path: '/path/to/plugin.js' },
        { type: 'package', name: 'test-plugin' },
        { type: 'package', name: 'test-plugin', version: '1.0.0' },
        { type: 'url', url: 'https://example.com/plugin.js' },
        { type: 'inline', plugin: createMockPlugin() }
      ];

      fileSources.forEach(source => {
        expect(source.type).toMatch(/^(file|package|url|inline)$/);
      });

      // 验证联合类型的判别属性
      const fileSource = fileSources[0];
      if (fileSource.type === 'file') {
        expect(fileSource.path).toBeDefined();
      }

      const packageSource = fileSources[1];
      if (packageSource.type === 'package') {
        expect(packageSource.name).toBeDefined();
      }
    });

    test('should validate PluginStatus enum', () => {
      const statuses: PluginStatus[] = [
        'loading',
        'loaded', 
        'active',
        'inactive',
        'error',
        'unloading'
      ];

      statuses.forEach(status => {
        expect(typeof status).toBe('string');
        expect(['loading', 'loaded', 'active', 'inactive', 'error', 'unloading']).toContain(status);
      });
    });
  });

  describe('LoadedPlugin Interface Type Safety', () => {
    test('should validate LoadedPlugin structure', async () => {
      const plugin = createMockPlugin();
      const mockLoadedPlugin: LoadedPlugin = {
        plugin,
        metadata: {
          source: { type: 'inline', plugin },
          loadPath: '/test/path',
          checksum: 'abc123',
          size: 1024,
          lastModified: Date.now(),
          loadDuration: 100,
          memoryUsage: 2048,
          apiVersion: '1.0.0'
        },
        status: 'loaded',
        loadTime: Date.now(),
        rules: new Map([['test-rule', plugin.rules[0]]]),
        config: { enabled: true },
        dependencies: [],
        stats: {
          totalExecutions: 0,
          totalErrors: 0,
          averageExecutionTime: 0,
          lastExecutionTime: 0,
          memoryUsage: 0,
          cacheHitRate: 0
        },
        health: {
          status: 'healthy',
          issues: [],
          lastCheck: Date.now(),
          uptime: 0,
          responseTime: 0
        }
      };

      loadedPluginValidator.toAccept(mockLoadedPlugin);

      // 验证只读属性
      expectType<LoadedPlugin>().toBeAssignableTo<{
        readonly plugin: Plugin;
        readonly metadata: PluginMetadata;
        readonly status: PluginStatus;
        readonly loadTime: number;
        readonly rules: Map<string, PluginRule>;
        readonly config: any;
        readonly dependencies: string[];
        readonly stats: PluginStatistics;
        readonly health: PluginHealth;
      }>();

      expect(mockLoadedPlugin.plugin).toBe(plugin);
      expect(mockLoadedPlugin.rules).toBeInstanceOf(Map);
      expect(mockLoadedPlugin.status).toBe('loaded');
    });
  });

  describe('Event System Type Safety', () => {
    test('should validate event types and handlers', () => {
      const events: PluginEvent[] = [
        'plugin:loading',
        'plugin:loaded',
        'plugin:unloading',
        'plugin:unloaded',
        'plugin:enabled',
        'plugin:disabled',
        'plugin:error',
        'plugin:config-changed'
      ];

      events.forEach(event => {
        expect(typeof event).toBe('string');
      });

      // 验证事件处理器类型
      const handler: PluginEventHandler = (data: PluginEventData) => {
        expect(data.pluginId).toBeDefined();
        expect(data.timestamp).toBeDefined();
      };

      expect(typeof handler).toBe('function');
    });

    test('should validate PluginEventData structure', async () => {
      const eventData: PluginEventData = {
        pluginId: 'test-plugin',
        timestamp: Date.now(),
        error: new Error('Test error'),
        config: { enabled: true },
        metadata: { version: '1.0.0' }
      };

      const result = await AdvancedTypeValidator.validateTypeShape(
        eventData,
        {
          pluginId: 'string',
          timestamp: 'number',
          error: (v: any) => v instanceof Error,
          config: 'object',
          metadata: 'object'
        },
        'PluginEventData'
      );

      expect(result.passed).toBe(true);
    });

    test('should validate event handler registration and emission', () => {
      const handlerMock = vi.fn();
      
      // 注册事件处理器
      pluginManager.on('plugin:loaded', handlerMock);
      
      // 发射事件
      const eventData: PluginEventData = {
        pluginId: 'test-plugin',
        timestamp: Date.now()
      };
      
      pluginManager.emit('plugin:loaded', eventData);
      
      expect(handlerMock).toHaveBeenCalledWith(eventData);
      
      // 注销事件处理器
      pluginManager.off('plugin:loaded', handlerMock);
      
      pluginManager.emit('plugin:loaded', eventData);
      expect(handlerMock).toHaveBeenCalledTimes(1); // 不应该再次调用
    });
  });

  describe('ValidationResult and Error Handling Type Safety', () => {
    test('should validate ValidationResult interface', async () => {
      const validResult: ValidationResult = {
        isValid: true,
        errors: []
      };

      const invalidResult: ValidationResult = {
        isValid: false,
        errors: [
          {
            code: 'INVALID_ID',
            message: 'Plugin ID is invalid',
            field: 'id',
            severity: 'error'
          }
        ]
      };

      const validationResults = [validResult, invalidResult];

      for (const result of validationResults) {
        const validation = await AdvancedTypeValidator.validateTypeShape(
          result,
          {
            isValid: 'boolean',
            errors: (v: any) => Array.isArray(v)
          },
          'ValidationResult'
        );

        expect(validation.passed).toBe(true);
      }
    });

    test('should validate PluginError type structure', () => {
      const error = new PluginError(
        'Test error message',
        'test-plugin-id',
        'loading',
        true,
        { additionalInfo: 'test context' }
      );

      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(PluginError);
      expect(error.name).toBe('PluginError');
      expect(error.pluginId).toBe('test-plugin-id');
      expect(error.operation).toBe('loading');
      expect(error.isRecoverable).toBe(true);
      expect(error.context).toEqual({ additionalInfo: 'test context' });
    });

    test('should validate DependencyValidationResult interface', async () => {
      const dependencyResult: DependencyValidationResult = {
        isValid: true,
        missingDependencies: [],
        conflictingDependencies: [],
        circularDependencies: [],
        details: 'All dependencies are satisfied'
      };

      const result = await AdvancedTypeValidator.validateTypeShape(
        dependencyResult,
        {
          isValid: 'boolean',
          missingDependencies: (v: any) => Array.isArray(v),
          conflictingDependencies: (v: any) => Array.isArray(v),
          circularDependencies: (v: any) => Array.isArray(v),
          details: 'string'
        },
        'DependencyValidationResult'
      );

      expect(result.passed).toBe(true);
    });
  });

  describe('Plugin Statistics and Health Type Safety', () => {
    test('should validate PluginStatistics interface', async () => {
      const stats: PluginStatistics = {
        totalExecutions: 100,
        totalErrors: 2,
        averageExecutionTime: 15.5,
        lastExecutionTime: Date.now(),
        memoryUsage: 1024000,
        cacheHitRate: 85.7
      };

      const result = await AdvancedTypeValidator.validateTypeShape(
        stats,
        {
          totalExecutions: 'number',
          totalErrors: 'number',
          averageExecutionTime: 'number',
          lastExecutionTime: 'number',
          memoryUsage: 'number',
          cacheHitRate: 'number'
        },
        'PluginStatistics'
      );

      expect(result.passed).toBe(true);
    });

    test('should validate PluginHealth interface', async () => {
      const health: PluginHealth = {
        status: 'healthy',
        issues: [
          {
            type: 'warning',
            message: 'Memory usage is high',
            timestamp: Date.now(),
            severity: 'medium'
          }
        ],
        lastCheck: Date.now(),
        uptime: 3600000,
        responseTime: 25.3
      };

      const result = await AdvancedTypeValidator.validateTypeShape(
        health,
        {
          status: (v: any) => typeof v === 'string',
          issues: (v: any) => Array.isArray(v),
          lastCheck: 'number',
          uptime: 'number',
          responseTime: 'number'
        },
        'PluginHealth'
      );

      expect(result.passed).toBe(true);
    });
  });

  describe('Plugin Loading and Management Type Safety', () => {
    test('should handle plugin loading with proper type constraints', async () => {
      const plugin = createMockPlugin('loadable-plugin');
      const source: PluginSource = { type: 'inline', plugin };

      // Mock the internal validation to avoid actual plugin loading complexity
      vi.spyOn(pluginManager as any, 'resolvePluginSource').mockResolvedValue({
        plugin,
        metadata: {}
      });
      vi.spyOn(pluginManager as any, 'validator').mockImplementation({
        validatePlugin: () => ({ isValid: true, errors: [] })
      });

      try {
        // This would normally load the plugin, but we're testing type safety
        // await pluginManager.loadPlugin(source);
      } catch (error) {
        // Expected in this mock scenario
      }

      // Verify type constraints are maintained
      expect(source.type).toBe('inline');
      if (source.type === 'inline') {
        expect(source.plugin).toBe(plugin);
      }
    });

    test('should validate plugin queries return correct types', () => {
      const plugins = pluginManager.getAllPlugins();
      const activePlugins = pluginManager.getActivePlugins();
      const inactivePlugins = pluginManager.getInactivePlugins();

      expect(Array.isArray(plugins)).toBe(true);
      expect(Array.isArray(activePlugins)).toBe(true);
      expect(Array.isArray(inactivePlugins)).toBe(true);

      // Verify return types
      plugins.forEach(plugin => {
        expectType<LoadedPlugin>().toAccept(plugin);
      });
    });

    test('should handle plugin configuration with type safety', async () => {
      const pluginId = 'test-plugin';
      const config = { enabled: true, threshold: 10 };

      // Mock plugin existence
      vi.spyOn(pluginManager, 'getPlugin').mockReturnValue({
        plugin: createMockPlugin(pluginId),
        config: {},
        status: 'active'
      } as LoadedPlugin);

      try {
        await pluginManager.updatePluginConfig(pluginId, config);
        const retrievedConfig = pluginManager.getPluginConfig(pluginId);
        
        // Type safety validation
        expect(typeof retrievedConfig).toBe('object');
      } catch (error) {
        // Expected in mock scenario
      }
    });
  });

  describe('Integration with Type Testing Utils', () => {
    test('should work with TypeValidator for complex validations', async () => {
      const plugin = createMockPlugin();
      
      // 使用类型守卫测试
      const pluginGuard = await TypeSafeTestFactory.generateTypeGuardTests(
        'Plugin',
        [plugin],
        [null, undefined, {}, 'string', 123, { id: 123 }]
      );

      const guardResult = await pluginValidator.validateTypeGuard(pluginGuard);
      expect(guardResult.passed).toBe(true);
    });

    test('should validate plugin metadata with advanced type checking', async () => {
      const metadata: PluginMetadata = {
        source: { type: 'file', path: '/test/plugin.js' },
        loadPath: '/absolute/path/plugin.js',
        checksum: 'sha256-abcd1234',
        size: 2048,
        lastModified: Date.now(),
        loadDuration: 150,
        memoryUsage: 1024000,
        apiVersion: '1.0.0'
      };

      const compatibilityResult = await AdvancedTypeValidator.validateStructuralCompatibility(
        metadata,
        (value: unknown): value is PluginMetadata => {
          return typeof value === 'object' && value !== null &&
                 'source' in value && 'loadPath' in value && 'checksum' in value;
        },
        'PluginMetadata Structural Compatibility'
      );

      expect(compatibilityResult.passed).toBe(true);
    });

    test('should generate comprehensive plugin validation reports', async () => {
      const plugins = [
        createMockPlugin('plugin-1'),
        createMockPlugin('plugin-2'),
        createMockPlugin('plugin-3')
      ];

      for (const plugin of plugins) {
        pluginValidator.toAccept(plugin);
        
        // 验证插件规则也是有效的
        plugin.rules.forEach(rule => {
          expect(rule.pluginId).toBe(plugin.id);
          expect(typeof rule.apply).toBe('function');
        });
      }

      const summary = pluginValidator.getSummary();
      expect(summary.overallSuccess).toBe(true);
      expect(summary.totalTests).toBe(plugins.length);
    });

    test('should integrate with performance testing utils', async () => {
      const { duration } = await PerformanceTestUtils.measureAsync(async () => {
        // 模拟插件加载性能测试
        const plugin = createMockPlugin('performance-test-plugin');
        pluginValidator.toAccept(plugin);
        
        // 创建多个插件以测试大规模验证性能
        const plugins = Array.from({ length: 100 }, (_, i) => 
          createMockPlugin(`bulk-plugin-${i}`)
        );
        
        // 批量验证性能
        for (const p of plugins) {
          pluginValidator.toAccept(p);
        }
      });

      // 验证性能在合理范围内（允许1秒执行时间）
      expect(duration).toBeLessThan(1000);
    });

    test('should validate complex plugin configurations', async () => {
      const complexPlugin: Plugin = {
        ...createMockPlugin('complex-plugin'),
        configSchema: {
          type: 'object',
          properties: {
            enabled: { type: 'boolean', default: true },
            threshold: { type: 'number', default: 10, minimum: 1, maximum: 100 },
            categories: { 
              type: 'array', 
              items: { type: 'string' },
              default: ['all']
            },
            advanced: {
              type: 'object',
              properties: {
                strictMode: { type: 'boolean', default: false },
                customRules: { type: 'array', items: { type: 'string' } }
              }
            }
          },
          required: ['enabled']
        },
        dependencies: ['@other/plugin', 'helper-plugin'],
        peerDependencies: ['core-engine@>=1.0.0'],
        engineVersion: '>=1.0.0 <2.0.0'
      };

      const validation = await AdvancedTypeValidator.validateTypeShape(
        complexPlugin,
        {
          id: 'string',
          name: 'string',
          version: 'string',
          rules: (v: any) => Array.isArray(v),
          configSchema: 'object',
          dependencies: (v: any) => Array.isArray(v),
          peerDependencies: (v: any) => Array.isArray(v),
          engineVersion: 'string'
        },
        'Complex Plugin Configuration'
      );

      expect(validation.passed).toBe(true);
    });
  });

  describe('Concurrency and Thread Safety', () => {
    test('should handle concurrent plugin operations safely', async () => {
      const concurrentOperations: Promise<void>[] = [];
      
      // 模拟多个并发操作
      for (let i = 0; i < 10; i++) {
        concurrentOperations.push(
          Promise.resolve().then(() => {
            const plugin = createMockPlugin(`concurrent-plugin-${i}`);
            pluginValidator.toAccept(plugin);
            
            // 验证插件属性
            expect(plugin.id).toBe(`concurrent-plugin-${i}`);
            expect(plugin.rules).toHaveLength(1);
            expect(plugin.rules[0].pluginId).toBe(plugin.id);
          })
        );
      }

      await Promise.all(concurrentOperations);
      
      const summary = pluginValidator.getSummary();
      expect(summary.overallSuccess).toBe(true);
    });

    test('should maintain type safety during plugin state transitions', () => {
      const plugin = createMockPlugin();
      const states: PluginStatus[] = ['loading', 'loaded', 'active', 'inactive', 'error'];
      
      states.forEach(status => {
        const loadedPlugin: Partial<LoadedPlugin> = {
          plugin,
          status,
          loadTime: Date.now()
        };
        
        expect(loadedPlugin.status).toBe(status);
        expect(typeof loadedPlugin.status).toBe('string');
      });
    });
  });

  describe('Advanced Plugin System Features', () => {
    test('should validate plugin sandbox permissions and security', async () => {
      const sandboxPermissions: SandboxPermissions = {
        fileSystem: {
          read: ['**/*.js', '**/*.ts', '**/*.json'],
          write: ['plugins/temp/**/*', 'output/*.log']
        },
        network: {
          outbound: ['https://api.example.com/*'],
          inbound: false
        },
        process: {
          spawn: false,
          signals: false
        },
        memory: {
          limit: 100 * 1024 * 1024, // 100MB
          timeout: 30000 // 30 seconds
        }
      };

      const validation = await AdvancedTypeValidator.validateTypeShape(
        sandboxPermissions,
        {
          fileSystem: (v: any) => v && Array.isArray(v.read) && Array.isArray(v.write),
          network: (v: any) => v && Array.isArray(v.outbound) && typeof v.inbound === 'boolean',
          process: (v: any) => v && typeof v.spawn === 'boolean' && typeof v.signals === 'boolean',
          memory: (v: any) => v && typeof v.limit === 'number' && typeof v.timeout === 'number'
        },
        'SandboxPermissions Validation'
      );

      expect(validation.passed).toBe(true);
    });

    test('should validate plugin configuration update options', async () => {
      const updateOptions: PluginConfigUpdateOptions = {
        validateSchema: true,
        restartPlugin: false,
        notifyPlugin: true,
        backup: true
      };

      const validation = await AdvancedTypeValidator.validateTypeShape(
        updateOptions,
        {
          validateSchema: 'boolean',
          restartPlugin: 'boolean',
          notifyPlugin: 'boolean',
          backup: 'boolean'
        },
        'PluginConfigUpdateOptions'
      );

      expect(validation.passed).toBe(true);
    });

    test('should validate version conflict detection', async () => {
      const versionConflict: VersionConflict = {
        dependency: '@test/plugin-core',
        required: '>=2.0.0',
        actual: '1.5.0',
        conflictType: 'incompatible'
      };

      const validation = await AdvancedTypeValidator.validateTypeShape(
        versionConflict,
        {
          dependency: 'string',
          required: 'string',
          actual: 'string',
          conflictType: (v: any) => ['missing', 'incompatible', 'circular'].includes(v)
        },
        'VersionConflict'
      );

      expect(validation.passed).toBe(true);
    });

    test('should validate health issue reporting', async () => {
      const healthIssue: HealthIssue = {
        type: 'memory',
        severity: 'high',
        message: 'Memory usage exceeds 90% of allocated limit',
        timestamp: Date.now(),
        suggestions: [
          'Consider reducing memory footprint',
          'Enable memory debugging',
          'Review for memory leaks'
        ]
      };

      const validation = await AdvancedTypeValidator.validateTypeShape(
        healthIssue,
        {
          type: (v: any) => ['memory', 'performance', 'error', 'security', 'compatibility'].includes(v),
          severity: (v: any) => ['low', 'medium', 'high', 'critical'].includes(v),
          message: 'string',
          timestamp: 'number',
          suggestions: (v: any) => Array.isArray(v) && v.every((s: any) => typeof s === 'string')
        },
        'HealthIssue'
      );

      expect(validation.passed).toBe(true);
    });

    test('should validate plugin rule configuration and execution', async () => {
      const pluginRule: PluginRule = {
        ...createMockPlugin().rules[0],
        category: 'complexity',
        tags: ['performance', 'maintainability'],
        isExperimental: false,
        requiresConfig: true,
        configKey: 'complexityRules.customRule',
        getDocumentation: () => ({
          description: 'Analyzes code complexity patterns',
          rationale: 'Complex code is harder to maintain and debug',
          examples: [],
          configuration: [],
          relatedRules: ['core.complexity'],
          references: ['https://example.com/complexity-guide']
        }),
        getExamples: () => [],
        canDisable: () => true,
        getCompatibilityInfo: () => ({
          engineVersions: ['1.0.0', '1.1.0'],
          nodeVersions: ['>=14.0.0'],
          typescript: { minVersion: '4.0.0' }
        })
      };

      // 验证插件规则包含所有必需的方法
      expect(typeof pluginRule.apply).toBe('function');
      expect(typeof pluginRule.canHandle).toBe('function');
      expect(typeof pluginRule.getDocumentation).toBe('function');
      expect(typeof pluginRule.getExamples).toBe('function');
      expect(typeof pluginRule.canDisable).toBe('function');
      expect(typeof pluginRule.getCompatibilityInfo).toBe('function');

      // 验证可选功能
      expect(pluginRule.category).toBe('complexity');
      expect(pluginRule.tags).toContain('performance');
      expect(pluginRule.isExperimental).toBe(false);
      expect(pluginRule.requiresConfig).toBe(true);
      expect(pluginRule.configKey).toBe('complexityRules.customRule');
    });

    test('should validate plugin dependency resolution', () => {
      const mockPlugin: Plugin = {
        ...createMockPlugin('dependent-plugin'),
        dependencies: ['@core/base-plugin', '@utils/helper-plugin'],
        peerDependencies: ['@engine/complexity-engine@>=1.0.0'],
        engineVersion: '>=1.0.0 <2.0.0'
      };

      expect(Array.isArray(mockPlugin.dependencies)).toBe(true);
      expect(Array.isArray(mockPlugin.peerDependencies)).toBe(true);
      expect(typeof mockPlugin.engineVersion).toBe('string');

      // 验证依赖格式
      mockPlugin.dependencies!.forEach(dep => {
        expect(typeof dep).toBe('string');
        expect(dep.length).toBeGreaterThan(0);
      });

      mockPlugin.peerDependencies!.forEach(dep => {
        expect(typeof dep).toBe('string');
        expect(dep).toMatch(/^[@\w-]+/); // 基本包名格式验证
      });
    });
  });

  describe('Plugin Lifecycle and Hooks Type Safety', () => {
    test('should validate plugin lifecycle hook signatures', async () => {
      const mockEngine = createMockAsyncRuleEngine();
      
      const pluginWithHooks: Plugin = {
        ...createMockPlugin('lifecycle-plugin'),
        onLoad: vi.fn().mockResolvedValue(undefined) as (engine: any, config?: any) => Promise<void>,
        onUnload: vi.fn().mockResolvedValue(undefined) as (engine: any) => Promise<void>,
        onConfigChange: vi.fn().mockResolvedValue(undefined) as (newConfig: any, oldConfig?: any) => Promise<void>,
        getConfigDefaults: vi.fn().mockReturnValue({ enabled: true, threshold: 10 }),
        validateConfig: vi.fn().mockReturnValue({ isValid: true, errors: [], warnings: [] }),
        getMetadata: vi.fn().mockReturnValue({ version: '1.0.0', lastModified: Date.now() })
      };

      // 验证生命周期钩子类型签名
      if (pluginWithHooks.onLoad) {
        await pluginWithHooks.onLoad(mockEngine, { enabled: true });
        expect(pluginWithHooks.onLoad).toHaveBeenCalledWith(mockEngine, { enabled: true });
      }

      if (pluginWithHooks.onUnload) {
        await pluginWithHooks.onUnload(mockEngine);
        expect(pluginWithHooks.onUnload).toHaveBeenCalledWith(mockEngine);
      }

      if (pluginWithHooks.onConfigChange) {
        await pluginWithHooks.onConfigChange({ enabled: false }, { enabled: true });
        expect(pluginWithHooks.onConfigChange).toHaveBeenCalledWith({ enabled: false }, { enabled: true });
      }

      // 验证配置相关方法
      if (pluginWithHooks.getConfigDefaults) {
        const defaults = pluginWithHooks.getConfigDefaults();
        expect(defaults).toEqual({ enabled: true, threshold: 10 });
      }

      if (pluginWithHooks.validateConfig) {
        const validation = pluginWithHooks.validateConfig({ enabled: true });
        expect(validation.isValid).toBe(true);
        expect(Array.isArray(validation.errors)).toBe(true);
        expect(Array.isArray(validation.warnings)).toBe(true);
      }

      if (pluginWithHooks.getMetadata) {
        const metadata = pluginWithHooks.getMetadata();
        expect(metadata).toHaveProperty('version');
        expect(metadata).toHaveProperty('lastModified');
      }
    });

    test('should handle plugin hook error scenarios', async () => {
      const failingPlugin: Plugin = {
        ...createMockPlugin('failing-plugin'),
        onLoad: vi.fn().mockRejectedValue(new Error('Load failed')),
        onUnload: vi.fn().mockRejectedValue(new Error('Unload failed')),
        onConfigChange: vi.fn().mockRejectedValue(new Error('Config change failed')),
        validateConfig: vi.fn().mockReturnValue({
          isValid: false,
          errors: [{ message: 'Invalid configuration', severity: 'error' as const }],
          warnings: []
        })
      };

      const mockEngine = createMockAsyncRuleEngine();

      // 验证错误处理
      if (failingPlugin.onLoad) {
        await expect(failingPlugin.onLoad(mockEngine)).rejects.toThrow('Load failed');
      }

      if (failingPlugin.onUnload) {
        await expect(failingPlugin.onUnload(mockEngine)).rejects.toThrow('Unload failed');
      }

      if (failingPlugin.onConfigChange) {
        await expect(failingPlugin.onConfigChange({}, {})).rejects.toThrow('Config change failed');
      }

      if (failingPlugin.validateConfig) {
        const validation = failingPlugin.validateConfig({});
        expect(validation.isValid).toBe(false);
        expect(validation.errors).toHaveLength(1);
        expect(validation.errors[0].severity).toBe('error');
      }
    });
  });

  describe('Advanced Error Handling and Recovery', () => {
    test('should validate complex error scenarios with context', () => {
      const complexError = new PluginError(
        'Complex plugin operation failed',
        'complex-plugin-id',
        'execution',
        true,
        {
          operation: 'rule-execution',
          ruleId: 'complex-rule',
          nodeType: 'FunctionExpression',
          callStack: ['rule.apply', 'engine.analyzeNode', 'processor.process'],
          errorCode: 'RULE_EXECUTION_FAILED',
          retryCount: 2,
          lastAttempt: Date.now()
        }
      );

      expect(complexError).toBeInstanceOf(PluginError);
      expect(complexError.pluginId).toBe('complex-plugin-id');
      expect(complexError.errorType).toBe('execution');
      expect(complexError.isRecoverable).toBe(true);
      expect(complexError.context).toHaveProperty('operation');
      expect(complexError.context).toHaveProperty('ruleId');
      expect(complexError.context).toHaveProperty('callStack');
      expect(Array.isArray(complexError.context.callStack)).toBe(true);
    });

    test('should validate plugin error type enumeration', () => {
      const errorTypes: Array<Parameters<typeof PluginError>[2]> = [
        'loading',
        'validation',
        'dependency',
        'configuration',
        'execution',
        'security',
        'compatibility',
        'sandbox',
        'timeout',
        'memory'
      ];

      errorTypes.forEach(errorType => {
        const error = new PluginError('Test error', 'test-plugin', errorType, false);
        expect(error.errorType).toBe(errorType);
        expect(error).toBeInstanceOf(PluginError);
        expect(error).toBeInstanceOf(Error);
      });
    });
  });

  describe('Integration with Fixture Manager and Dynamic Testing', () => {
    test('should integrate with fixture manager for comprehensive plugin testing', async () => {
      // 生成动态测试固件
      const fixture = FixtureManager.generateDynamicFixture('plugin-system-test', {
        fileCount: 5,
        complexityRange: [10, 25],
        category: 'integration'
      });

      expect(fixture.name).toBe('plugin-system-test');
      expect(fixture.files.length).toBeGreaterThan(0);

      // 创建基于固件的插件测试场景
      const plugins = fixture.files.slice(0, 3).map((_, index) => 
        createMockPlugin(`fixture-plugin-${index}`)
      );

      // 批量验证插件
      for (const plugin of plugins) {
        pluginValidator.toAccept(plugin);
        expect(plugin.rules).toHaveLength(1);
        expect(plugin.rules[0].pluginId).toBe(plugin.id);
      }

      const summary = pluginValidator.getSummary();
      expect(summary.overallSuccess).toBe(true);
      expect(summary.totalTests).toBe(plugins.length);
    });

    test('should support dynamic plugin generation for stress testing', async () => {
      // 生成大量插件进行压力测试
      const stressPlugins = Array.from({ length: 50 }, (_, index) => {
        const plugin = createMockPlugin(`stress-plugin-${index}`);
        
        // 为部分插件添加复杂配置
        if (index % 10 === 0) {
          (plugin as any).configSchema = {
            type: 'object',
            properties: {
              enabled: { type: 'boolean', default: true },
              rules: {
                type: 'array',
                items: { type: 'string' },
                default: [`rule-${index}`]
              }
            }
          };
        }

        // 为部分插件添加依赖
        if (index % 15 === 0) {
          (plugin as any).dependencies = [`dep-plugin-${Math.floor(index / 15)}`];
        }

        return plugin;
      });

      // 测试批量验证性能
      const { duration } = await PerformanceTestUtils.measureAsync(async () => {
        for (const plugin of stressPlugins) {
          pluginValidator.toAccept(plugin);
        }
      });

      // 验证在合理时间内完成（允许2秒）
      expect(duration).toBeLessThan(2000);

      const summary = pluginValidator.getSummary();
      expect(summary.overallSuccess).toBe(true);
      expect(summary.totalTests).toBe(stressPlugins.length);
      expect(summary.passedTests).toBe(stressPlugins.length);
    });
  });
});