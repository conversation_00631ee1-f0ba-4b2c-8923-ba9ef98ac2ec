/**
 * 安全插件沙箱测试
 * 验证类型安全的沙箱权限管理和API访问控制
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { 
  SecurePluginSandbox, 
  TypeSafeSecurityProxy,
  createSecurePluginSandbox,
  type TypeSafeSandboxOptions,
  type SecurityAuditEntry 
} from '../../secure-sandbox';
import * as path from 'path';

describe('SecurePluginSandbox', () => {
  let sandbox: SecurePluginSandbox;
  let options: TypeSafeSandboxOptions;

  beforeEach(() => {
    options = {
      memory: {
        limit: 64 * 1024 * 1024, // 64MB
        timeout: 5000,           // 5秒
        enableGC: true,
      },
      fileSystem: {
        readPaths: [path.join(process.cwd(), 'test-data')],
        writePaths: [path.join(process.cwd(), 'temp')],
        enableWatching: false,
      },
      network: {
        allowedHosts: ['example.com', 'api.test.com'],
        enableOutbound: true,
      },
      permissions: {
        allowDynamicImport: false,
        allowEval: false,
        allowModuleAccess: ['fs', 'path', 'crypto'],
      },
    };

    sandbox = new SecurePluginSandbox('test-plugin', options);
  });

  afterEach(async () => {
    await sandbox.destroy();
  });

  describe('类型安全配置验证', () => {
    it('应该验证内存限制配置', () => {
      expect(() => {
        new SecurePluginSandbox('test', {
          ...options,
          memory: { ...options.memory, limit: -1 }
        });
      }).toThrow('Memory limit must be positive');
    });

    it('应该验证超时配置', () => {
      expect(() => {
        new SecurePluginSandbox('test', {
          ...options,
          memory: { ...options.memory, timeout: 0 }
        });
      }).toThrow('Timeout must be positive');
    });

    it('应该规范化文件路径', () => {
      const testSandbox = new SecurePluginSandbox('test', {
        ...options,
        fileSystem: {
          ...options.fileSystem,
          readPaths: ['./relative/path'],
          writePaths: ['../another/path'],
        }
      });

      expect(testSandbox.canAccess('fs:' + path.resolve('./relative/path/file.txt'))).toBe(true);
      testSandbox.destroy();
    });

    it('应该验证网络配置', () => {
      expect(() => {
        new SecurePluginSandbox('test', {
          ...options,
          network: {
            allowedHosts: [],
            enableOutbound: true, // 启用出站但没有允许的主机
          }
        });
      }).toThrow('Outbound network access enabled but no allowed hosts specified');
    });
  });

  describe('文件系统权限检查', () => {
    it('should allow access to permitted read paths', () => {
      const testPath = path.join(process.cwd(), 'test-data', 'config.json');
      expect(sandbox.canAccess(`fs:${testPath}`)).toBe(true);
    });

    it('should allow access to permitted write paths', () => {
      const testPath = path.join(process.cwd(), 'temp', 'output.txt');
      expect(sandbox.canAccess(`fs:${testPath}`)).toBe(true);
    });

    it('should deny access to unpermitted paths', () => {
      const testPath = path.join(process.cwd(), 'protected', 'secret.txt');
      expect(sandbox.canAccess(`fs:${testPath}`)).toBe(false);
    });

    it('should handle nested paths correctly', () => {
      const nestedPath = path.join(process.cwd(), 'test-data', 'nested', 'deep', 'file.txt');
      expect(sandbox.canAccess(`fs:${nestedPath}`)).toBe(true);
    });
  });

  describe('网络权限检查', () => {
    it('should allow access to permitted hosts', () => {
      expect(sandbox.canAccess('network:https://example.com/api')).toBe(true);
      expect(sandbox.canAccess('network:http://api.test.com/data')).toBe(true);
    });

    it('should deny access to unpermitted hosts', () => {
      expect(sandbox.canAccess('network:https://malicious.com')).toBe(false);
      expect(sandbox.canAccess('network:http://unauthorized.site')).toBe(false);
    });

    it('should handle wildcard hosts', () => {
      const wildcardOptions: TypeSafeSandboxOptions = {
        ...options,
        network: {
          allowedHosts: ['*'],
          enableOutbound: true,
        }
      };
      
      const wildcardSandbox = new SecurePluginSandbox('wildcard-test', wildcardOptions);
      expect(wildcardSandbox.canAccess('network:https://any-site.com')).toBe(true);
      wildcardSandbox.destroy();
    });

    it('should handle subdomain matching', () => {
      const subdomainOptions: TypeSafeSandboxOptions = {
        ...options,
        network: {
          allowedHosts: ['example.com'],
          enableOutbound: true,
        }
      };
      
      const subdomainSandbox = new SecurePluginSandbox('subdomain-test', subdomainOptions);
      expect(subdomainSandbox.canAccess('network:https://api.example.com')).toBe(true);
      expect(subdomainSandbox.canAccess('network:https://cdn.example.com')).toBe(true);
      subdomainSandbox.destroy();
    });

    it('should deny access when outbound is disabled', () => {
      const noOutboundOptions: TypeSafeSandboxOptions = {
        ...options,
        network: {
          allowedHosts: ['example.com'],
          enableOutbound: false,
        }
      };
      
      const noOutboundSandbox = new SecurePluginSandbox('no-outbound-test', noOutboundOptions);
      expect(noOutboundSandbox.canAccess('network:https://example.com')).toBe(false);
      noOutboundSandbox.destroy();
    });
  });

  describe('模块权限检查', () => {
    it('should allow access to permitted modules', () => {
      expect(sandbox.canAccess('module:fs')).toBe(true);
      expect(sandbox.canAccess('module:path')).toBe(true);
      expect(sandbox.canAccess('module:crypto')).toBe(true);
    });

    it('should deny access to unpermitted modules', () => {
      expect(sandbox.canAccess('module:child_process')).toBe(false);
      expect(sandbox.canAccess('module:cluster')).toBe(false);
      expect(sandbox.canAccess('module:http')).toBe(false);
    });

    it('should allow access to built-in modules by default', () => {
      expect(sandbox.canAccess('module:util')).toBe(true);
      expect(sandbox.canAccess('module:stream')).toBe(true);
      expect(sandbox.canAccess('module:events')).toBe(true);
    });

    it('should allow access to safe third-party modules', () => {
      expect(sandbox.canAccess('module:lodash')).toBe(true);
      expect(sandbox.canAccess('module:moment')).toBe(true);
      expect(sandbox.canAccess('module:uuid')).toBe(true);
    });
  });

  describe('内存管理', () => {
    it('should track memory allocation', () => {
      const initialUsage = sandbox.getMemoryUsage();
      const allocated = sandbox.allocateMemory(1024);
      
      expect(allocated).toBe(true);
      expect(sandbox.getMemoryUsage()).toBeGreaterThan(initialUsage);
    });

    it('should deny allocation when limit exceeded', () => {
      const largeAllocation = options.memory.limit + 1;
      const success = sandbox.allocateMemory(largeAllocation);
      
      expect(success).toBe(false);
    });

    it('should release memory when requested', () => {
      sandbox.allocateMemory(1024);
      const beforeRelease = sandbox.getMemoryUsage();
      
      sandbox.releaseMemory();
      const afterRelease = sandbox.getMemoryUsage();
      
      expect(afterRelease).toBeLessThanOrEqual(beforeRelease);
    });

    it('should check if within limits', () => {
      expect(sandbox.isWithinLimits()).toBe(true);
      
      // 分配接近限制的内存
      sandbox.allocateMemory(options.memory.limit * 0.9);
      expect(sandbox.isWithinLimits()).toBe(true);
    });
  });

  describe('安全审计日志', () => {
    it('should log access attempts', () => {
      // 执行一些权限检查
      sandbox.canAccess('fs:/allowed/path');
      sandbox.canAccess('fs:/denied/path');
      sandbox.canAccess('network:https://example.com');
      sandbox.canAccess('module:fs');
      
      const auditLog = sandbox.getAuditLog();
      expect(auditLog.length).toBeGreaterThan(0);
      
      // 检查日志条目格式
      const firstEntry = auditLog[0];
      expect(firstEntry).toHaveProperty('type');
      expect(firstEntry).toHaveProperty('resource');
      expect(firstEntry).toHaveProperty('allowed');
      expect(firstEntry).toHaveProperty('timestamp');
    });

    it('should record detailed audit information', () => {
      sandbox.canAccess('fs:/test/path');
      
      const auditLog = sandbox.getAuditLog();
      const entry = auditLog.find(e => e.resource === '/test/path');
      
      expect(entry).toBeDefined();
      expect(entry?.type).toBe('filesystem');
      expect(entry?.allowed).toBe(false);
      expect(entry?.timestamp).toBeTypeOf('number');
    });
  });

  describe('代码执行', () => {
    it('should execute safe code', async () => {
      const result = await sandbox.execute('1 + 1');
      expect(result).toBe(2);
    });

    it('should execute functions with timeout', async () => {
      const result = await sandbox.execute(() => Promise.resolve(42));
      expect(result).toBe(42);
    });

    it('should enforce execution timeout', async () => {
      const longRunningCode = `
        let start = Date.now();
        while (Date.now() - start < 10000) {
          // 运行10秒的循环
        }
        return 'completed';
      `;
      
      await expect(sandbox.execute(longRunningCode)).rejects.toThrow('timeout');
    });

    it('should provide secure context', async () => {
      const contextCheck = `
        typeof __pluginContext !== 'undefined' && 
        __pluginContext.id === 'test-plugin'
      `;
      
      const result = await sandbox.execute(contextCheck);
      expect(result).toBe(true);
    });
  });

  describe('权限请求', () => {
    it('should handle permission requests', async () => {
      const granted = await sandbox.requestPermission('fs:/allowed/path');
      expect(granted).toBe(true);
      
      const denied = await sandbox.requestPermission('fs:/denied/path');
      expect(denied).toBe(false);
    });

    it('should emit permission request events', async () => {
      let eventReceived = false;
      
      sandbox.on('permission-requested', (data) => {
        eventReceived = true;
        expect(data.pluginId).toBe('test-plugin');
        expect(data.permission).toBe('module:test');
      });
      
      await sandbox.requestPermission('module:test');
      expect(eventReceived).toBe(true);
    });
  });

  describe('清理和销毁', () => {
    it('should cleanup resources', async () => {
      await sandbox.cleanup();
      // 验证清理后的状态
      expect(sandbox.isWithinLimits()).toBe(false);
    });

    it('should destroy sandbox completely', async () => {
      await sandbox.destroy();
      
      // 销毁后的操作应该失败
      expect(sandbox.canAccess('fs:/any/path')).toBe(false);
      await expect(sandbox.execute('1 + 1')).rejects.toThrow('destroyed');
    });

    it('should emit cleanup events', async () => {
      let cleanupReceived = false;
      let destroyReceived = false;
      
      sandbox.on('cleanup', () => {
        cleanupReceived = true;
      });
      
      sandbox.on('destroyed', () => {
        destroyReceived = true;
      });
      
      await sandbox.destroy();
      
      expect(cleanupReceived).toBe(true);
      expect(destroyReceived).toBe(true);
    });
  });
});

describe('TypeSafeSecurityProxy', () => {
  let proxy: TypeSafeSecurityProxy;
  let options: TypeSafeSandboxOptions;

  beforeEach(() => {
    options = {
      memory: {
        limit: 64 * 1024 * 1024,
        timeout: 5000,
        enableGC: true,
      },
      fileSystem: {
        readPaths: ['/allowed/read'],
        writePaths: ['/allowed/write'],
        enableWatching: false,
      },
      network: {
        allowedHosts: ['safe.com'],
        enableOutbound: true,
      },
      permissions: {
        allowDynamicImport: false,
        allowEval: false,
        allowModuleAccess: ['fs', 'path'],
      },
    };

    proxy = new TypeSafeSecurityProxy(options);
  });

  describe('文件系统权限检查', () => {
    it('should allow access to permitted read paths', () => {
      expect(proxy.canAccessFileSystem('/allowed/read/file.txt')).toBe(true);
    });

    it('should allow access to permitted write paths', () => {
      expect(proxy.canAccessFileSystem('/allowed/write/file.txt')).toBe(true);
    });

    it('should deny access to unpermitted paths', () => {
      expect(proxy.canAccessFileSystem('/forbidden/path')).toBe(false);
    });
  });

  describe('网络权限检查', () => {
    it('should allow access to permitted hosts', () => {
      expect(proxy.canAccessNetwork('https://safe.com/api')).toBe(true);
    });

    it('should deny access to unpermitted hosts', () => {
      expect(proxy.canAccessNetwork('https://evil.com')).toBe(false);
    });

    it('should handle invalid URLs gracefully', () => {
      expect(proxy.canAccessNetwork('not-a-url')).toBe(false);
    });
  });

  describe('模块权限检查', () => {
    it('should allow access to permitted modules', () => {
      expect(proxy.canAccessModule('fs')).toBe(true);
      expect(proxy.canAccessModule('path')).toBe(true);
    });

    it('should deny access to unpermitted modules', () => {
      expect(proxy.canAccessModule('child_process')).toBe(false);
    });
  });

  describe('安全上下文创建', () => {
    it('should create secure context with basic objects', () => {
      const context = proxy.createSecureContext();
      
      expect(context.Object).toBe(Object);
      expect(context.Array).toBe(Array);
      expect(context.String).toBe(String);
      expect(context.global).toBeUndefined();
      expect(context.globalThis).toBeUndefined();
    });

    it('should respect eval permissions', () => {
      const allowEvalOptions = {
        ...options,
        permissions: {
          ...options.permissions,
          allowEval: true,
        }
      };
      
      const allowEvalProxy = new TypeSafeSecurityProxy(allowEvalOptions);
      const context = allowEvalProxy.createSecureContext();
      
      expect(context.eval).toBeDefined();
      expect(context.Function).toBeDefined();
    });
  });

  describe('审计日志', () => {
    it('should maintain audit log', () => {
      proxy.canAccessFileSystem('/test/path');
      proxy.canAccessNetwork('https://test.com');
      proxy.canAccessModule('test-module');
      
      const auditLog = proxy.getAuditLog();
      expect(auditLog.length).toBe(3);
    });

    it('should clear audit log when requested', () => {
      proxy.canAccessFileSystem('/test/path');
      expect(proxy.getAuditLog().length).toBe(1);
      
      proxy.clearAuditLog();
      expect(proxy.getAuditLog().length).toBe(0);
    });
  });
});

describe('createSecurePluginSandbox', () => {
  it('should create sandbox with default options', () => {
    const sandbox = createSecurePluginSandbox('test-plugin');
    
    expect(sandbox.pluginId).toBe('test-plugin');
    expect(sandbox.isWithinLimits()).toBe(true);
    
    sandbox.destroy();
  });

  it('should merge custom options with defaults', () => {
    const customOptions = {
      memory: {
        limit: 32 * 1024 * 1024, // 32MB
      },
      network: {
        enableOutbound: true,
        allowedHosts: ['example.com'],
      }
    };
    
    const sandbox = createSecurePluginSandbox('test-plugin', customOptions);
    
    expect(sandbox.canAccess('network:https://example.com')).toBe(true);
    
    sandbox.destroy();
  });

  it('should create isolated sandboxes', () => {
    const sandbox1 = createSecurePluginSandbox('plugin-1');
    const sandbox2 = createSecurePluginSandbox('plugin-2');
    
    expect(sandbox1.pluginId).toBe('plugin-1');
    expect(sandbox2.pluginId).toBe('plugin-2');
    
    sandbox1.destroy();
    sandbox2.destroy();
  });
});