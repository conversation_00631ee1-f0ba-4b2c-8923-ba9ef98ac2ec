import { describe, test, expect, it, vi, afterEach } from 'vitest';
import { 
  debugParameterValidationRule,
  breakpointParameterValidationRule,
  uiParameterValidationRule,
  validateMinFileComplexity,
  smartFilterDependencyRule,
  outputEnhancementSuggestionRule
} from '../../utils/concurrent-validation-service';
import type { CLIOptions } from '../../config/types';

describe('Debug Parameter Validation Rule', () => {
  test('应该对缺少--debug的debug参数发出警告', () => {
    const options: CLIOptions = {
      paths: ['.'],
      debugLevel: 'trace',
      debugOutput: './debug.log',
      visualReport: true
    };

    const result = debugParameterValidationRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toContain('--debug-level 参数仅在使用 --debug 时有效');
    expect(result.warnings).toContain('--debug-output 参数仅在使用 --debug 时有效');
    expect(result.warnings).toContain('--visual-report 参数仅在使用 --debug 时有效');
  });

  test('应该在有--debug标志时通过验证', () => {
    const options: CLIOptions = {
      paths: ['.'],
      debug: true,
      debugLevel: 'trace',
      debugOutput: './debug.log',
      visualReport: true
    };

    const result = debugParameterValidationRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });

  test('应该在没有debug相关参数时通过验证', () => {
    const options: CLIOptions = {
      paths: ['.']
    };

    const result = debugParameterValidationRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });

  test('应该只对设置的参数发出警告', () => {
    const options: CLIOptions = {
      paths: ['.'],
      debugLevel: 'trace'
      // 只设置debugLevel，不设置其他参数
    };

    const result = debugParameterValidationRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.warnings).toHaveLength(1);
    expect(result.warnings).toContain('--debug-level 参数仅在使用 --debug 时有效');
  });
});

describe('Breakpoint Parameter Validation Rule', () => {
  test('应该对缺少--enable-breakpoints的断点参数发出警告', () => {
    const options: CLIOptions = {
      paths: ['.'],
      breakOnRule: ['complexity-rule'],
      breakOnComplexity: 15,
      stepByStep: true
    };

    const result = breakpointParameterValidationRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toContain('--break-on-rule 参数仅在使用 --enable-breakpoints 时有效');
    expect(result.warnings).toContain('--break-on-complexity 参数仅在使用 --enable-breakpoints 时有效');
    expect(result.warnings).toContain('--step-by-step 参数仅在使用 --enable-breakpoints 时有效');
  });

  test('应该在有--enable-breakpoints标志时通过验证', () => {
    const options: CLIOptions = {
      paths: ['.'],
      enableBreakpoints: true,
      breakOnRule: ['complexity-rule'],
      breakOnComplexity: 15,
      stepByStep: true
    };

    const result = breakpointParameterValidationRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });
});

describe('UI Parameter Validation Rule', () => {
  test('应该对缺少--ui的--open参数发出警告', () => {
    const options: CLIOptions = {
      paths: ['.'],
      open: true
    };

    const result = uiParameterValidationRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toContain('--open 参数仅在使用 --ui 时有效');
  });

  test('应该在有--ui标志时通过验证', () => {
    const options: CLIOptions = {
      paths: ['.'],
      ui: true,
      open: true
    };

    const result = uiParameterValidationRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });
});

describe('Smart Filter Dependency Rule', () => {
  test('应该对缺少上下文显示标志的智能过滤参数发出警告', () => {
    const options: CLIOptions = {
      paths: ['.'],
      maxContextItems: 5,
      minComplexityIncrement: 1
    };

    const result = smartFilterDependencyRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toContain('--max-context-items 参数仅在使用 --show-context 或 --show-all-context 时有效');
    expect(result.warnings).toContain('--min-complexity-increment 参数仅在使用 --show-context 或 --show-all-context 时有效');
  });

  test('应该在有showContext标志时通过验证', () => {
    const options: CLIOptions = {
      paths: ['.'],
      showContext: true,
      maxContextItems: 5,
      minComplexityIncrement: 1
    };

    const result = smartFilterDependencyRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });

  test('应该在有showAllContext标志时通过验证', () => {
    const options: CLIOptions = {
      paths: ['.'],
      showAllContext: true,
      maxContextItems: 5,
      minComplexityIncrement: 1
    };

    const result = smartFilterDependencyRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });

  test('应该处理undefined值的边界情况', () => {
    const options: CLIOptions = {
      paths: ['.'],
      maxContextItems: undefined,
      minComplexityIncrement: undefined
    };

    const result = smartFilterDependencyRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });
});

describe('Output Enhancement Suggestion Rule', () => {
  test('应该对text格式的文件输出提供建议', () => {
    const options: CLIOptions = {
      paths: ['.'],
      outputDir: './reports',
      format: 'text'
    };

    const result = outputEnhancementSuggestionRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toContain('建议使用 --format json 或 --format html 以获得更好的文件输出效果');
  });

  test('应该在使用json格式时不提供建议', () => {
    const options: CLIOptions = {
      paths: ['.'],
      outputDir: './reports',
      format: 'json'
    };

    const result = outputEnhancementSuggestionRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });

  test('应该在没有outputDir时不提供建议', () => {
    const options: CLIOptions = {
      paths: ['.'],
      format: 'text'
    };

    const result = outputEnhancementSuggestionRule.validate(options);

    expect(result.valid).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.warnings).toHaveLength(0);
  });
});

// 新增的文件复杂度参数验证测试
describe('validateMinFileComplexity', () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('有效输入验证', () => {
    it('应该接受有效的正整数', () => {
      expect(validateMinFileComplexity('1')).toBe(1);
      expect(validateMinFileComplexity('5')).toBe(5);
      expect(validateMinFileComplexity('100')).toBe(100);
    });

    it('应该接受0作为有效值', () => {
      expect(validateMinFileComplexity('0')).toBe(0);
    });

    it('应该处理字符串形式的大数字', () => {
      expect(validateMinFileComplexity('999999')).toBe(999999);
    });

    it('应该处理包含前后空格的有效数字', () => {
      expect(validateMinFileComplexity(' 5 ')).toBe(5);
      expect(validateMinFileComplexity('\t10\n')).toBe(10);
    });
  });

  describe('无效输入处理', () => {
    it('应该拒绝空字符串', () => {
      expect(() => validateMinFileComplexity('')).toThrow(
        '参数 --min-file-complexity 的值不能为空'
      );
    });

    it('应该拒绝只包含空格的字符串', () => {
      expect(() => validateMinFileComplexity('   ')).toThrow(
        '参数 --min-file-complexity 的值不能为空'
      );
    });

    it('应该拒绝非数字字符串', () => {
      expect(() => validateMinFileComplexity('abc')).toThrow(
        '参数 --min-file-complexity 的值 "abc" 无效，期望一个有效的数字'
      );
      
      expect(() => validateMinFileComplexity('1abc')).toThrow(
        '参数 --min-file-complexity 的值 "1abc" 无效，期望一个有效的数字'
      );
    });

    it('应该拒绝负数', () => {
      expect(() => validateMinFileComplexity('-1')).toThrow(
        '参数 --min-file-complexity 的值 "-1" 无效，必须是非负数（>= 0）'
      );
      
      expect(() => validateMinFileComplexity('-100')).toThrow(
        '参数 --min-file-complexity 的值 "-100" 无效，必须是非负数（>= 0）'
      );
    });

    it('应该拒绝浮点数', () => {
      expect(() => validateMinFileComplexity('1.5')).toThrow(
        '参数 --min-file-complexity 的值 "1.5" 无效，必须是一个整数'
      );
      
      expect(() => validateMinFileComplexity('0.1')).toThrow(
        '参数 --min-file-complexity 的值 "0.1" 无效，必须是一个整数'
      );
    });

    it('应该拒绝特殊数字值', () => {
      expect(() => validateMinFileComplexity('Infinity')).toThrow(
        '参数 --min-file-complexity 的值 "Infinity" 无效，期望一个有效的数字'
      );
      
      expect(() => validateMinFileComplexity('NaN')).toThrow(
        '参数 --min-file-complexity 的值 "NaN" 无效，期望一个有效的数字'
      );
    });
  });

  describe('边界值测试', () => {
    it('应该处理最大安全整数', () => {
      const maxSafeInt = Number.MAX_SAFE_INTEGER.toString();
      expect(validateMinFileComplexity(maxSafeInt)).toBe(Number.MAX_SAFE_INTEGER);
    });

    it('应该正确处理0和1的边界情况', () => {
      expect(validateMinFileComplexity('0')).toBe(0);
      expect(validateMinFileComplexity('1')).toBe(1);
    });
  });

  describe('错误消息格式', () => {
    it('应该在错误消息中包含原始输入值', () => {
      const invalidInput = 'invalid-value';
      
      try {
        validateMinFileComplexity(invalidInput);
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.message).toContain(invalidInput);
        expect(error.message).toContain('--min-file-complexity');
      }
    });

    it('应该为不同类型的错误提供不同的错误消息', () => {
      // 空值错误
      try {
        validateMinFileComplexity('');
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.message).toContain('不能为空');
      }

      // 非数字错误
      try {
        validateMinFileComplexity('abc');
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.message).toContain('期望一个有效的数字');
      }

      // 负数错误
      try {
        validateMinFileComplexity('-1');
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.message).toContain('必须是非负数');
      }

      // 非整数错误
      try {
        validateMinFileComplexity('1.5');
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.message).toContain('必须是一个整数');
      }
    });
  });

  describe('性能测试', () => {
    it('应该快速处理大量验证请求', () => {
      const testValues = Array.from({ length: 1000 }, (_, i) => i.toString());
      const startTime = performance.now();

      testValues.forEach(value => {
        expect(validateMinFileComplexity(value)).toBe(parseInt(value));
      });

      const endTime = performance.now();
      const executionTime = endTime - startTime;

      // 验证性能要求（应该在合理时间内完成）
      expect(executionTime).toBeLessThan(100); // 100ms内完成1000次验证
    });
  });

  describe('类型安全性', () => {
    it('应该总是返回数字类型', () => {
      const result = validateMinFileComplexity('42');
      expect(typeof result).toBe('number');
      expect(Number.isInteger(result)).toBe(true);
    });

    it('应该在所有有效情况下返回预期的数字值', () => {
      const testCases = [
        { input: '0', expected: 0 },
        { input: '1', expected: 1 },
        { input: '10', expected: 10 },
        { input: '999', expected: 999 }
      ];

      testCases.forEach(({ input, expected }) => {
        const result = validateMinFileComplexity(input);
        expect(result).toBe(expected);
        expect(result).toBeTypeOf('number');
      });
    });
  });
});