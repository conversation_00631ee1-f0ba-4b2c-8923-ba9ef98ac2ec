import { test, expect, beforeEach } from 'vitest';
import { getCompatibilityService, CompatibilityService } from '../../utils/compatibility-service';

test('兼容性服务基本功能测试', () => {
  const compatibilityService = getCompatibilityService();
  
  expect(compatibilityService).toBeDefined();
  expect(typeof compatibilityService.validateDetailsCommandCompatibility).toBe('function');
  expect(typeof compatibilityService.applyCompatibilitySettings).toBe('function');
  expect(typeof compatibilityService.generateCompatibleCodeFrame).toBe('function');
});

test('validateDetailsCommandCompatibility - showContext需要details', () => {
  const compatibilityService = getCompatibilityService();
  const options = { showContext: true, details: false };
  
  const result = compatibilityService.validateDetailsCommandCompatibility(options);
  
  expect(result.compatible).toBe(false);
  expect(result.issues).toContain('--show-context 参数必须与 --details 一起使用');
});

test('validateDetailsCommandCompatibility - showAllContext需要details', () => {
  const compatibilityService = getCompatibilityService();
  const options = { showAllContext: true, details: false };
  
  const result = compatibilityService.validateDetailsCommandCompatibility(options);
  
  expect(result.compatible).toBe(false);
  expect(result.issues).toContain('--show-all-context 参数必须与 --details 一起使用');
});

test('validateDetailsCommandCompatibility - showContext和showAllContext不能同时使用', () => {
  const compatibilityService = getCompatibilityService();
  const options = { showContext: true, showAllContext: true, details: true };
  
  const result = compatibilityService.validateDetailsCommandCompatibility(options);
  
  expect(result.compatible).toBe(false);
  expect(result.issues).toContain('--show-context 和 --show-all-context 不能同时使用');
});

test('validateDetailsCommandCompatibility - 有效的组合应该通过', () => {
  const compatibilityService = getCompatibilityService();
  const options = { showContext: true, details: true };
  
  const result = compatibilityService.validateDetailsCommandCompatibility(options);
  
  expect(result.compatible).toBe(true);
  expect(result.issues).toHaveLength(0);
});

test('validateDetailsCommandCompatibility - 过滤器参数需要details', () => {
  const compatibilityService = getCompatibilityService();
  
  const options1 = { maxContextItems: 5, details: false };
  const result1 = compatibilityService.validateDetailsCommandCompatibility(options1);
  
  expect(result1.compatible).toBe(false);
  expect(result1.issues).toContain('--max-context-items 参数必须与 --details 一起使用');

  const options2 = { minComplexityIncrement: 2, details: false };
  const result2 = compatibilityService.validateDetailsCommandCompatibility(options2);
  
  expect(result2.compatible).toBe(false);
  expect(result2.issues).toContain('--min-complexity-increment 参数必须与 --details 一起使用');
});

test('applyCompatibilitySettings - 基本功能', () => {
  const compatibilityService = getCompatibilityService();
  const options = {
    showContext: true,
    details: true,
    format: 'text'
  };
  
  const result = compatibilityService.applyCompatibilitySettings(options);
  
  // 应该返回一个对象
  expect(result).toBeDefined();
  expect(typeof result).toBe('object');
});

test('getCompatibilityReport - 未执行检查时', () => {
  const compatibilityService = getCompatibilityService();
  compatibilityService.reset(); // 确保状态清洁
  
  const report = compatibilityService.getCompatibilityReport();
  expect(report).toBe('兼容性检查尚未执行');
});

test('getDegradeOptions - 返回降级选项', () => {
  const compatibilityService = getCompatibilityService();
  
  const options = compatibilityService.getDegradeOptions();
  
  expect(options).toBeDefined();
  expect(typeof options.enableFallback).toBe('boolean');
  expect(typeof options.useBasicOutput).toBe('boolean');
  expect(typeof options.skipCodeFrames).toBe('boolean');
  expect(typeof options.disableColors).toBe('boolean');
});

test('单例模式验证', () => {
  const service1 = getCompatibilityService();
  const service2 = getCompatibilityService();
  
  expect(service1).toBe(service2);
});