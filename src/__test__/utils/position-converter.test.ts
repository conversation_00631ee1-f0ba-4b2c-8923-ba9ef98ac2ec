import { PositionConverter, Position } from '../../utils/position-converter';

describe('PositionConverter', () => {
  const sampleCode = `function hello() {
  console.log('Hello');
  return true;
}`;

  describe('spanToPosition', () => {
    it('should convert span start to correct line and column', () => {
      // Position at start of function
      const pos1 = PositionConverter.spanToPosition(sampleCode, 0);
      expect(pos1).toEqual({ line: 1, column: 1 });

      // Position at 'c' in console (offset 21)
      const pos2 = PositionConverter.spanToPosition(sampleCode, 21);
      expect(pos2).toEqual({ line: 2, column: 3 });

      // Position at 'r' in return
      const pos3 = PositionConverter.spanToPosition(sampleCode, 45);
      expect(pos3).toEqual({ line: 3, column: 3 });
    });

    it('should handle edge cases', () => {
      // Empty string
      const emptyPos = PositionConverter.spanToPosition('', 0);
      expect(emptyPos).toEqual({ line: 1, column: 1 });

      // Position beyond end
      const endPos = PositionConverter.spanToPosition(sampleCode, sampleCode.length);
      expect(endPos.line).toBeGreaterThan(0);
      expect(endPos.column).toBeGreaterThan(0);
    });
  });

  describe('lineColumnToOffset', () => {
    it('should convert line and column to correct offset', () => {
      // Start of file
      const offset1 = PositionConverter.lineColumnToOffset(sampleCode, 1, 1);
      expect(offset1).toBe(0);

      // Start of second line (after "function hello() {\n")
      const offset2 = PositionConverter.lineColumnToOffset(sampleCode, 2, 1);
      expect(offset2).toBe(19);
    });

    it('should throw error for invalid positions', () => {
      expect(() => {
        PositionConverter.lineColumnToOffset(sampleCode, 0, 1);
      }).toThrow('Line 0 is out of range');

      expect(() => {
        PositionConverter.lineColumnToOffset(sampleCode, 1, 0);
      }).toThrow('Column 0 is out of range');

      expect(() => {
        PositionConverter.lineColumnToOffset(sampleCode, 10, 1);
      }).toThrow('Line 10 is out of range');
    });
  });

  describe('getLineContent', () => {
    it('should return correct line content', () => {
      const line1 = PositionConverter.getLineContent(sampleCode, 1);
      expect(line1).toBe('function hello() {');

      const line2 = PositionConverter.getLineContent(sampleCode, 2);
      expect(line2).toBe("  console.log('Hello');");
    });

    it('should throw error for invalid line numbers', () => {
      expect(() => {
        PositionConverter.getLineContent(sampleCode, 0);
      }).toThrow('Line 0 is out of range');

      expect(() => {
        PositionConverter.getLineContent(sampleCode, 10);
      }).toThrow('Line 10 is out of range');
    });
  });

  describe('extractSpanText', () => {
    it('should extract correct text for given span', () => {
      // Extract "function"
      const text1 = PositionConverter.extractSpanText(sampleCode, { start: 0, end: 8 });
      expect(text1).toBe('function');

      // Extract "hello"
      const text2 = PositionConverter.extractSpanText(sampleCode, { start: 9, end: 14 });
      expect(text2).toBe('hello');
    });

    it('should throw error for invalid spans', () => {
      expect(() => {
        PositionConverter.extractSpanText(sampleCode, { start: -1, end: 5 });
      }).toThrow('Invalid span');

      expect(() => {
        PositionConverter.extractSpanText(sampleCode, { start: 5, end: 1000 });
      }).toThrow('Invalid span');

      expect(() => {
        PositionConverter.extractSpanText(sampleCode, { start: 10, end: 5 });
      }).toThrow('Invalid span');
    });
  });

  describe('isValidPosition', () => {
    it('should return true for valid positions', () => {
      expect(PositionConverter.isValidPosition(sampleCode, { line: 1, column: 1 })).toBe(true);
      expect(PositionConverter.isValidPosition(sampleCode, { line: 2, column: 5 })).toBe(true);
    });

    it('should return false for invalid positions', () => {
      expect(PositionConverter.isValidPosition(sampleCode, { line: 0, column: 1 })).toBe(false);
      expect(PositionConverter.isValidPosition(sampleCode, { line: 1, column: 0 })).toBe(false);
      expect(PositionConverter.isValidPosition(sampleCode, { line: 10, column: 1 })).toBe(false);
    });
  });

  describe('roundtrip conversion', () => {
    it('should maintain consistency between span and position conversion', () => {
      // Test various positions in the code
      const testPositions = [
        { line: 1, column: 1 },
        { line: 1, column: 9 }, // "function"
        { line: 2, column: 3 }, // start of console
        { line: 3, column: 1 }, // start of return line
      ];

      testPositions.forEach(originalPos => {
        // Convert position to offset and back
        const offset = PositionConverter.lineColumnToOffset(sampleCode, originalPos.line, originalPos.column);
        const convertedPos = PositionConverter.spanToPosition(sampleCode, offset);
        
        expect(convertedPos).toEqual(originalPos);
      });
    });
  });

  describe('memory optimization tests', () => {
    it('should handle large files efficiently without excessive memory usage', () => {
      // 生成大文件内容（约100KB）
      const lines = Array.from({ length: 2000 }, (_, i) => 
        `function test${i}() { console.log('line ${i}'); return ${i}; }`
      );
      const largeCode = lines.join('\n');

      const startMemory = process.memoryUsage().heapUsed;
      
      // 执行多次转换操作
      for (let i = 0; i < 100; i++) {
        const randomOffset = Math.floor(Math.random() * largeCode.length);
        PositionConverter.spanToPosition(largeCode, randomOffset);
      }

      const endMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = endMemory - startMemory;
      
      // 内存增长应该控制在合理范围内（小于10MB）
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should reuse line map calculations for the same source code', () => {
      const iterations = 1000;
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        const offset = Math.floor(Math.random() * sampleCode.length);
        PositionConverter.spanToPosition(sampleCode, offset);
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // 大量重复操作应该在合理时间内完成（小于100ms）
      expect(duration).toBeLessThan(100);
    });

    it('should handle concurrent position conversion without memory leaks', async () => {
      const testCode = Array.from({ length: 500 }, (_, i) => 
        `const value${i} = ${i} * 2; // line ${i}`
      ).join('\n');

      const startMemory = process.memoryUsage().heapUsed;
      
      // 并发执行多个转换操作
      const promises = Array.from({ length: 50 }, async (_, i) => {
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            for (let j = 0; j < 20; j++) {
              const offset = Math.floor(Math.random() * testCode.length);
              PositionConverter.spanToPosition(testCode, offset);
            }
            resolve();
          }, Math.random() * 10);
        });
      });

      await Promise.all(promises);
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const endMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = endMemory - startMemory;
      
      // 并发操作后内存增长应该控制在合理范围内
      expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024); // 5MB
    });

    it('should optimize buildLineMap for different file sizes', () => {
      const testCases = [
        { size: 'small', lines: 10 },
        { size: 'medium', lines: 1000 },
        { size: 'large', lines: 10000 }
      ];

      testCases.forEach(testCase => {
        const testCode = Array.from({ length: testCase.lines }, (_, i) => 
          `line ${i} content`
        ).join('\n');

        const startTime = performance.now();
        
        // 测试多次构建行映射
        for (let i = 0; i < 10; i++) {
          PositionConverter.spanToPosition(testCode, Math.floor(testCode.length / 2));
        }
        
        const endTime = performance.now();
        const avgTime = (endTime - startTime) / 10;
        
        // 不同大小文件的处理时间应该合理缩放
        if (testCase.size === 'small') {
          expect(avgTime).toBeLessThan(1); // 小文件应该很快
        } else if (testCase.size === 'medium') {
          expect(avgTime).toBeLessThan(10); // 中等文件
        } else {
          expect(avgTime).toBeLessThan(50); // 大文件
        }
      });
    });

    it('should handle edge cases without memory explosion', () => {
      const edgeCases = [
        '',                           // 空文件
        'a',                         // 单字符
        'a\n',                       // 单行带换行
        '\n'.repeat(10000),          // 只有换行符
        'x'.repeat(100000),          // 长单行
        Array.from({ length: 1000 }, () => '').join('\n') // 大量空行
      ];

      edgeCases.forEach((testCode, index) => {
        const startMemory = process.memoryUsage().heapUsed;
        
        try {
          // 对每种边界情况执行多次操作
          for (let i = 0; i < 100; i++) {
            const offset = Math.min(i, testCode.length);
            const pos = PositionConverter.spanToPosition(testCode, offset);
            expect(pos.line).toBeGreaterThan(0);
            expect(pos.column).toBeGreaterThan(0);
          }
        } catch (error) {
          // 边界情况不应该崩溃
          fail(`Edge case ${index} caused error: ${error}`);
        }
        
        const endMemory = process.memoryUsage().heapUsed;
        const memoryIncrease = endMemory - startMemory;
        
        // 边界情况不应该导致过度内存消耗
        expect(memoryIncrease).toBeLessThan(1024 * 1024); // 1MB
      });
    });
  });
});