/**
 * 文件缓存性能测试
 * 验证文件内容缓存和并行代码框架生成的性能改进
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FileContentCache, createFileCache } from '../../utils/file-cache';
import { 
  ParallelCodeFrameGenerator, 
  createParallelGenerator,
  GenerationTask 
} from '../../utils/parallel-code-frame-generator';
import { promises as fs } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';

describe('文件缓存性能测试', () => {
  let fileCache: FileContentCache;
  let tempDir: string;
  let testFiles: string[];

  beforeEach(async () => {
    fileCache = createFileCache({
      maxSize: 100,
      maxMemory: 10 * 1024 * 1024, // 10MB
      defaultTTL: 30000, // 30秒
      enableStatistics: true,
    });

    // 创建临时测试文件
    tempDir = await fs.mkdtemp(join(tmpdir(), 'file-cache-test-'));
    testFiles = [];

    for (let i = 0; i < 10; i++) {
      const filePath = join(tempDir, `test-${i}.ts`);
      const content = `// Test file ${i}
function testFunction${i}() {
  if (condition) {
    return value${i};
  }
  return defaultValue;
}

const complexFunction${i} = (param: string) => {
  for (let j = 0; j < 10; j++) {
    if (j % 2 === 0) {
      console.log('Even:', j);
    } else {
      console.log('Odd:', j);
    }
  }
  return param.length;
};
`;
      await fs.writeFile(filePath, content);
      testFiles.push(filePath);
    }
  });

  afterEach(async () => {
    fileCache.destroy();
    
    // 清理临时文件
    for (const file of testFiles) {
      try {
        await fs.unlink(file);
      } catch {
        // 忽略删除错误
      }
    }
    try {
      await fs.rmdir(tempDir);
    } catch {
      // 忽略删除错误
    }
  });

  it('应该缓存文件内容并提高访问性能', async () => {
    const testFile = testFiles[0];
    
    // 第一次访问（缓存未命中）
    const start1 = performance.now();
    const content1 = await fileCache.getFileContent(testFile);
    const duration1 = performance.now() - start1;
    
    expect(content1).toContain('testFunction0');
    
    // 第二次访问（缓存命中）
    const start2 = performance.now();
    const content2 = await fileCache.getFileContent(testFile);
    const duration2 = performance.now() - start2;
    
    expect(content2).toBe(content1);
    expect(duration2).toBeLessThan(duration1); // 缓存访问应该更快
    
    const stats = fileCache.getCacheStats();
    expect(stats.hitCount).toBe(1);
    expect(stats.missCount).toBe(1);
    expect(stats.hitRate).toBe(0.5);
  });

  it('应该正确管理缓存大小和内存限制', async () => {
    // 创建小容量缓存进行测试
    const smallCache = createFileCache({
      maxSize: 3,
      maxMemory: 1024, // 1KB
      enableStatistics: true,
    });

    // 添加多个文件到缓存
    for (let i = 0; i < 5; i++) {
      await smallCache.getFileContent(testFiles[i]);
    }

    const stats = smallCache.getCacheStats();
    expect(stats.totalFiles).toBeLessThanOrEqual(3); // 应该受到maxSize限制
    
    smallCache.destroy();
  });

  it('应该支持TTL过期机制', async () => {
    const shortTTLCache = createFileCache({
      defaultTTL: 100, // 100ms
      enableStatistics: true,
    });

    const testFile = testFiles[0];
    
    // 添加到缓存
    await shortTTLCache.getFileContent(testFile);
    expect(shortTTLCache.hasValidCache(testFile)).toBe(true);
    
    // 等待过期
    await new Promise(resolve => setTimeout(resolve, 150));
    
    expect(shortTTLCache.hasValidCache(testFile)).toBe(false);
    
    shortTTLCache.destroy();
  });

  it('应该支持批量预加载', async () => {
    const start = performance.now();
    await fileCache.preloadFiles(testFiles);
    const preloadDuration = performance.now() - start;
    
    // 验证所有文件都已缓存
    for (const file of testFiles) {
      expect(fileCache.hasValidCache(file)).toBe(true);
    }
    
    // 访问缓存的文件应该很快
    const accessStart = performance.now();
    for (const file of testFiles) {
      await fileCache.getFileContent(file);
    }
    const accessDuration = performance.now() - accessStart;
    
    const stats = fileCache.getCacheStats();
    expect(stats.hitRate).toBeGreaterThanOrEqual(0.5); // 大部分访问应该命中缓存
    
    console.log(`预加载耗时: ${preloadDuration.toFixed(2)}ms`);
    console.log(`缓存访问耗时: ${accessDuration.toFixed(2)}ms`);
    console.log(`缓存命中率: ${(stats.hitRate * 100).toFixed(1)}%`);
  });

  it('应该提供详细的缓存统计信息', async () => {
    // 访问一些文件
    await fileCache.getFileContent(testFiles[0]);
    await fileCache.getFileContent(testFiles[1]);
    await fileCache.getFileContent(testFiles[0]); // 重复访问

    const stats = fileCache.getCacheStats();
    expect(stats.totalFiles).toBe(2);
    expect(stats.hitCount).toBe(1);
    expect(stats.missCount).toBe(2);
    expect(stats.hitRate).toBe(1/3);
    expect(stats.averageFileSize).toBeGreaterThan(0);

    const details = fileCache.getCacheDetails();
    expect(details.entries).toHaveLength(2);
    expect(details.entries[0].accessCount).toBeGreaterThanOrEqual(1);
  });

  it('应该支持缓存优化', async () => {
    // 添加多个文件
    for (const file of testFiles) {
      await fileCache.getFileContent(file);
    }

    const statsBefore = fileCache.getCacheStats();
    const result = await fileCache.optimizeCache();
    const statsAfter = fileCache.getCacheStats();

    expect(result.removedExpired).toBeGreaterThanOrEqual(0);
    expect(result.removedLRU).toBeGreaterThanOrEqual(0);
    expect(result.memoryFreed).toBeGreaterThanOrEqual(0);
  });
});

describe('并行代码框架生成器性能测试', () => {
  let parallelGenerator: ParallelCodeFrameGenerator;
  let tempDir: string;
  let testFiles: string[];

  beforeEach(async () => {
    parallelGenerator = createParallelGenerator({
      maxConcurrency: 5,
      cacheSize: 100,
    });

    // 创建临时测试文件
    tempDir = await fs.mkdtemp(join(tmpdir(), 'parallel-test-'));
    testFiles = [];

    for (let i = 0; i < 5; i++) {
      const filePath = join(tempDir, `test-${i}.ts`);
      const content = `// Test file ${i}
function testFunction${i}() {
  if (condition) {
    return value${i};
  }
  while (loop) {
    if (nested) {
      for (let j = 0; j < count; j++) {
        console.log(j);
      }
    }
  }
  return defaultValue;
}
`;
      await fs.writeFile(filePath, content);
      testFiles.push(filePath);
    }
  });

  afterEach(async () => {
    await parallelGenerator.cleanup();
    
    // 清理临时文件
    for (const file of testFiles) {
      try {
        await fs.unlink(file);
      } catch {
        // 忽略删除错误
      }
    }
    try {
      await fs.rmdir(tempDir);
    } catch {
      // 忽略删除错误
    }
  });

  it('应该支持并行生成代码框架', async () => {
    const tasks: GenerationTask[] = [];
    
    // 为每个文件创建多个任务
    testFiles.forEach((file, fileIndex) => {
      for (let line = 2; line <= 6; line++) {
        tasks.push({
          id: `task-${fileIndex}-${line}`,
          filePath: file,
          line,
          column: 1,
        });
      }
    });

    let progressCallbacks = 0;
    const start = performance.now();
    
    const results = await parallelGenerator.generateFrames(
      tasks,
      {
        concurrency: 5,
        enablePreloading: true,
        enableBatching: true,
      },
      (progress) => {
        progressCallbacks++;
        expect(progress.completed).toBeLessThanOrEqual(progress.total);
        expect(progress.percentage).toBeLessThanOrEqual(100);
      }
    );
    
    const duration = performance.now() - start;
    
    expect(results).toHaveLength(tasks.length);
    expect(progressCallbacks).toBeGreaterThan(0);
    
    // 验证结果
    const successfulResults = results.filter(r => r.result.success);
    const failedResults = results.filter(r => !r.result.success);
    
    console.log(`并行生成 ${tasks.length} 个代码框架耗时: ${duration.toFixed(2)}ms`);
    console.log(`成功: ${successfulResults.length}, 失败: ${failedResults.length}`);
    console.log(`平均耗时: ${(duration / tasks.length).toFixed(2)}ms per task`);
    
    // 大部分应该成功
    expect(successfulResults.length).toBeGreaterThan(failedResults.length);
  });

  it('应该利用缓存提高重复访问性能', async () => {
    const file = testFiles[0];
    
    // 第一次生成（缓存未命中）
    const tasks1: GenerationTask[] = [
      { id: 'task1', filePath: file, line: 2, column: 1 },
      { id: 'task2', filePath: file, line: 4, column: 1 },
    ];
    
    const start1 = performance.now();
    const results1 = await parallelGenerator.generateFrames(tasks1);
    const duration1 = performance.now() - start1;
    
    // 第二次生成（缓存命中）
    const tasks2: GenerationTask[] = [
      { id: 'task3', filePath: file, line: 3, column: 1 },
      { id: 'task4', filePath: file, line: 5, column: 1 },
    ];
    
    const start2 = performance.now();
    const results2 = await parallelGenerator.generateFrames(tasks2);
    const duration2 = performance.now() - start2;
    
    expect(results1).toHaveLength(2);
    expect(results2).toHaveLength(2);
    
    // 第二次应该更快（利用了文件缓存）
    expect(duration2).toBeLessThan(duration1);
    
    const stats = parallelGenerator.getCacheStats();
    expect(stats.fileCache.hitRate).toBeGreaterThan(0);
    
    console.log(`第一次生成耗时: ${duration1.toFixed(2)}ms`);
    console.log(`第二次生成耗时: ${duration2.toFixed(2)}ms`);
    console.log(`性能提升: ${((duration1 - duration2) / duration1 * 100).toFixed(1)}%`);
  });

  it('应该支持预加载优化', async () => {
    const tasks: GenerationTask[] = testFiles.map((file, index) => ({
      id: `task-${index}`,
      filePath: file,
      line: 2,
      column: 1,
    }));

    // 不使用预加载
    const start1 = performance.now();
    const results1 = await parallelGenerator.generateFrames(tasks, {
      enablePreloading: false,
    });
    const duration1 = performance.now() - start1;

    // 清理缓存
    await parallelGenerator.cleanup();
    parallelGenerator = createParallelGenerator();

    // 使用预加载
    const start2 = performance.now();
    const results2 = await parallelGenerator.generateFrames(tasks, {
      enablePreloading: true,
    });
    const duration2 = performance.now() - start2;

    expect(results1).toHaveLength(testFiles.length);
    expect(results2).toHaveLength(testFiles.length);

    console.log(`不使用预加载: ${duration1.toFixed(2)}ms`);
    console.log(`使用预加载: ${duration2.toFixed(2)}ms`);
    
    // 主要验证预加载功能正确性，两种方式都应该成功
    expect(results1.every(r => r.result.success)).toBe(true);
    expect(results2.every(r => r.result.success)).toBe(true);
    
    // 验证缓存有效性
    const stats = parallelGenerator.getCacheStats();
    expect(stats.fileCache.totalFiles).toBeGreaterThan(0);
  });
});