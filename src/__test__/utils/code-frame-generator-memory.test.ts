import { CodeFrameGenerator, CodeFrameOptions, CodeFrameResult } from '../../utils/code-frame-generator';
import { createFileCache } from '../../utils/file-cache';
import { PositionConverter } from '../../utils/position-converter';
import { promises as fs } from 'fs';
import { join } from 'path';

describe('CodeFrameGenerator - Memory Optimization', () => {
  let generator: CodeFrameGenerator;
  let testFilePath: string;
  let testContent: string;

  beforeEach(async () => {
    // 创建自定义文件缓存用于测试
    const fileCache = createFileCache({
      maxSize: 100,
      maxMemory: 10 * 1024 * 1024, // 10MB
      defaultTTL: 5000,
      cleanupInterval: 1000
    });
    
    generator = new CodeFrameGenerator(fileCache);
    
    // 创建测试文件内容
    testContent = `function testFunction() {
  console.log('Hello World');
  return 42;
}

class TestClass {
  constructor() {
    this.value = 0;
  }
  
  method() {
    return this.value;
  }
}`;

    // 创建临时测试文件
    testFilePath = join(process.cwd(), `test-temp-${Date.now()}/test.ts`);
    const testDir = testFilePath.substring(0, testFilePath.lastIndexOf('/'));
    
    await fs.mkdir(testDir, { recursive: true });
    await fs.writeFile(testFilePath, testContent);
  });

  afterEach(async () => {
    // 清理
    generator.destroy();
    
    try {
      const testDir = testFilePath.substring(0, testFilePath.lastIndexOf('/'));
      await fs.rm(testDir, { recursive: true });
    } catch {
      // 忽略清理错误
    }
  });

  describe('memory usage tracking', () => {
    it('should track memory usage for frame generation', async () => {
      const result = await generator.generateFrame(testFilePath, 2, 3);
      
      expect(result.success).toBe(true);
      expect(result.memoryUsage).toBeDefined();
      expect(typeof result.memoryUsage).toBe('number');
      
      const stats = generator.getCacheStats();
      expect(stats.memoryStats.currentUsage).toBeGreaterThanOrEqual(0);
      expect(stats.memoryStats.maxUsage).toBeGreaterThan(0);
      expect(stats.memoryStats.utilizationPercent).toBeLessThanOrEqual(100);
    });

    it('should monitor memory pressure during intensive operations', async () => {
      const initialStats = generator.getCacheStats();
      const initialMemory = initialStats.memoryStats.currentUsage;
      
      // 执行大量代码框架生成操作
      const promises = Array.from({ length: 50 }, (_, i) => 
        generator.generateFrame(testFilePath, i % 13 + 1, (i % 10) + 1)
      );
      
      const results = await Promise.all(promises);
      
      // 验证所有操作都成功完成
      expect(results.every(r => r.success)).toBe(true);
      
      const finalStats = generator.getCacheStats();
      const memoryGrowth = finalStats.memoryStats.currentUsage - initialMemory;
      
      // 内存增长应该在合理范围内
      expect(memoryGrowth).toBeLessThan(5 * 1024 * 1024); // 5MB
    });

    it('should handle memory cleanup automatically', async function() {
      this.timeout(10000); // 增加超时时间
      
      // 生成大量内容以触发内存压力
      const largeContent = Array.from({ length: 1000 }, (_, i) => 
        `function test${i}() { console.log('test ${i}'); return ${i}; }`
      ).join('\n');
      
      const largeFilePath = join(process.cwd(), `test-temp-${Date.now()}/large.ts`);
      const largeDir = largeFilePath.substring(0, largeFilePath.lastIndexOf('/'));
      
      await fs.mkdir(largeDir, { recursive: true });
      await fs.writeFile(largeFilePath, largeContent);
      
      try {
        const initialStats = generator.getCacheStats();
        
        // 执行大量操作触发内存清理
        for (let batch = 0; batch < 5; batch++) {
          const batchPromises = Array.from({ length: 20 }, (_, i) => 
            generator.generateFrame(largeFilePath, (batch * 20 + i) % 1000 + 1, 1)
          );
          await Promise.all(batchPromises);
        }
        
        const finalStats = generator.getCacheStats();
        
        // 验证内存使用没有无限增长
        expect(finalStats.memoryStats.utilizationPercent).toBeLessThan(100);
        
        // 清理大文件
        await fs.rm(largeDir, { recursive: true });
      } catch (error) {
        await fs.rm(largeDir, { recursive: true }).catch(() => {});
        throw error;
      }
    });
  });

  describe('batch processing optimization', () => {
    it('should process multiple frames efficiently in batch', async () => {
      const requests = Array.from({ length: 25 }, (_, i) => ({
        filePath: testFilePath,
        line: (i % 13) + 1,
        column: (i % 20) + 1,
        options: { highlightCode: true, linesAbove: 1, linesBelow: 1 } as CodeFrameOptions
      }));
      
      const startTime = performance.now();
      const results = await generator.generateFramesBatch(requests);
      const endTime = performance.now();
      
      expect(results).toHaveLength(25);
      expect(results.every(r => r.success)).toBe(true);
      
      // 批量处理应该相对高效
      const avgTimePerFrame = (endTime - startTime) / 25;
      expect(avgTimePerFrame).toBeLessThan(50); // 每个框架生成时间小于50ms
    });

    it('should group requests by file for optimal cache utilization', async () => {
      // 创建多个测试文件
      const file1Path = join(process.cwd(), `test-temp-${Date.now()}/file1.ts`);
      const file2Path = join(process.cwd(), `test-temp-${Date.now()}/file2.ts`);
      
      const dir1 = file1Path.substring(0, file1Path.lastIndexOf('/'));
      const dir2 = file2Path.substring(0, file2Path.lastIndexOf('/'));
      
      await fs.mkdir(dir1, { recursive: true });
      await fs.mkdir(dir2, { recursive: true });
      await fs.writeFile(file1Path, testContent);
      await fs.writeFile(file2Path, testContent + '\n\n// Additional content');
      
      try {
        const requests = [
          { filePath: file1Path, line: 1, column: 1 },
          { filePath: file2Path, line: 2, column: 3 },
          { filePath: file1Path, line: 3, column: 5 },
          { filePath: file2Path, line: 5, column: 1 },
          { filePath: file1Path, line: 7, column: 2 }
        ];
        
        const results = await generator.generateFramesBatch(requests);
        
        expect(results).toHaveLength(5);
        expect(results.every(r => r.success)).toBe(true);
        
        // 验证缓存利用率
        const stats = generator.getCacheStats();
        expect(stats.fileCache.hitRate).toBeGreaterThan(0.5); // 至少50%的缓存命中率
        
        // 清理
        await Promise.all([
          fs.rm(dir1, { recursive: true }),
          fs.rm(dir2, { recursive: true })
        ]);
      } catch (error) {
        // 清理失败的情况
        await Promise.all([
          fs.rm(dir1, { recursive: true }).catch(() => {}),
          fs.rm(dir2, { recursive: true }).catch(() => {})
        ]);
        throw error;
      }
    });
  });

  describe('cache optimization', () => {
    it('should optimize cache and report memory freed', async () => {
      // 填充缓存
      const promises = Array.from({ length: 30 }, (_, i) => 
        generator.generateFrame(testFilePath, (i % 10) + 1, (i % 5) + 1)
      );
      await Promise.all(promises);
      
      const beforeStats = generator.getCacheStats();
      const optimizationResult = await generator.optimizeCache();
      const afterStats = generator.getCacheStats();
      
      expect(optimizationResult.beforeMemory).toBeGreaterThanOrEqual(0);
      expect(optimizationResult.afterMemory).toBeGreaterThanOrEqual(0);
      expect(optimizationResult.freedMemory).toBeGreaterThanOrEqual(0);
      
      // 优化后内存使用应该不会增加
      expect(afterStats.memoryStats.currentUsage).toBeLessThanOrEqual(beforeStats.memoryStats.currentUsage);
    });

    it('should preload files for better performance', async () => {
      const filesToPreload = [testFilePath];
      
      // 预加载文件
      await generator.preloadFiles(filesToPreload);
      
      // 验证缓存中有文件
      const stats = generator.getCacheStats();
      expect(stats.fileCache.totalFiles).toBeGreaterThan(0);
      
      // 生成框架应该使用缓存
      const result = await generator.generateFrame(testFilePath, 2, 3);
      expect(result.cached).toBe(true);
    });
  });

  describe('concurrent memory safety', () => {
    it('should handle concurrent frame generation without memory leaks', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // 并发生成大量代码框架
      const concurrentPromises = Array.from({ length: 100 }, async (_, i) => {
        return new Promise<void>((resolve) => {
          setTimeout(async () => {
            try {
              await generator.generateFrame(testFilePath, (i % 10) + 1, (i % 5) + 1);
            } catch {
              // 忽略个别失败
            }
            resolve();
          }, Math.random() * 50);
        });
      });
      
      await Promise.all(concurrentPromises);
      
      // 强制垃圾回收
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // 内存增长应该在合理范围内（小于10MB）
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });

    it('should maintain cache statistics accuracy under concurrent load', async () => {
      const concurrentOperations = Array.from({ length: 50 }, (_, i) => 
        generator.generateFrame(testFilePath, (i % 13) + 1, (i % 10) + 1)
      );
      
      await Promise.all(concurrentOperations);
      
      const stats = generator.getCacheStats();
      
      // 验证统计信息的一致性
      expect(stats.fileCache.totalFiles).toBeGreaterThanOrEqual(0);
      expect(stats.memoryStats.currentUsage).toBeGreaterThanOrEqual(0);
      expect(stats.memoryStats.utilizationPercent).toBeGreaterThanOrEqual(0);
      expect(stats.memoryStats.utilizationPercent).toBeLessThanOrEqual(100);
    });
  });

  describe('position converter integration', () => {
    it('should use optimized position conversion with caching', async () => {
      const span = { start: 20, end: 35 }; // 大致在 "console.log" 位置
      
      // 清除位置转换器缓存确保测试一致性
      PositionConverter.clearCache();
      
      const result = await generator.generateFrameFromSpan(testFilePath, span);
      
      expect(result.success).toBe(true);
      expect(result.frame).toContain('console.log');
      
      // 验证位置转换器缓存被使用
      const cacheStats = PositionConverter.getCacheStats();
      expect(cacheStats.size).toBeGreaterThan(0);
    });

    it('should handle large files efficiently with position caching', async () => {
      // 创建大文件
      const largeContent = Array.from({ length: 2000 }, (_, i) => 
        `const value${i} = ${i} * 2; // Line ${i + 1}`
      ).join('\n');
      
      const largeFilePath = join(process.cwd(), `test-temp-${Date.now()}/large.ts`);
      const largeDir = largeFilePath.substring(0, largeFilePath.lastIndexOf('/'));
      
      await fs.mkdir(largeDir, { recursive: true });
      await fs.writeFile(largeFilePath, largeContent);
      
      try {
        // 清除缓存
        PositionConverter.clearCache();
        
        const startTime = performance.now();
        
        // 多次访问不同位置
        const operations = Array.from({ length: 20 }, (_, i) => {
          const lineOffset = i * 100;
          const charOffset = lineOffset * 30; // 估算字符偏移
          return generator.generateFrameFromSpan(
            largeFilePath, 
            { start: charOffset, end: charOffset + 10 }
          );
        });
        
        const results = await Promise.all(operations);
        const endTime = performance.now();
        
        expect(results.every(r => r.success)).toBe(true);
        
        // 验证位置转换缓存提升了性能
        const cacheStats = PositionConverter.getCacheStats();
        expect(cacheStats.hitRate).toBeGreaterThan(0);
        
        // 大文件处理应该在合理时间内完成
        expect(endTime - startTime).toBeLessThan(2000); // 2秒内
        
        await fs.rm(largeDir, { recursive: true });
      } catch (error) {
        await fs.rm(largeDir, { recursive: true }).catch(() => {});
        throw error;
      }
    });
  });
});