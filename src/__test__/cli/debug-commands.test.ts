/**
 * 调试CLI命令处理器简化测试
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { EnhancedCommandProcessor, DebugCLIOptions } from '../../cli/debug-commands';

describe('EnhancedCommandProcessor - 简化测试', () => {
  let processor: EnhancedCommandProcessor;
  let mockConsoleLog: ReturnType<typeof vi.spyOn>;
  let mockConsoleError: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    processor = new EnhancedCommandProcessor();
    mockConsoleLog = vi.spyOn(console, 'log').mockImplementation(() => {});
    mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    mockConsoleLog.mockRestore();
    mockConsoleError.mockRestore();
    vi.clearAllMocks();
  });

  describe('基本功能', () => {
    test('应该能够创建处理器实例', () => {
      expect(processor).toBeDefined();
      expect(typeof processor.executeWithDebug).toBe('function');
    });

    test('应该处理空路径数组', async () => {
      const options: DebugCLIOptions = {
        paths: [],
        debug: true,
        quiet: true,
      };

      await processor.executeWithDebug(options);

      // 应该记录错误
      expect(mockConsoleError).toHaveBeenCalled();
    });

    test('应该处理基本调试选项', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: true,
        debugLevel: 'info',
        quiet: true,
      };

      await processor.executeWithDebug(options);

      // 应该初始化调试系统
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Debug system initialized')
      );
    });

    test('应该在非调试模式下跳过初始化', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: false,
        quiet: true,
      };

      await processor.executeWithDebug(options);

      // 不应该初始化调试系统
      expect(mockConsoleLog).not.toHaveBeenCalledWith(
        expect.stringContaining('Debug system initialized')
      );
    });
  });

  describe('断点配置', () => {
    test('应该处理规则断点配置', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: true,
        enableBreakpoints: true,
        breakOnRule: ['test-rule'],
        quiet: true,
      };

      await processor.executeWithDebug(options);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Breakpoint set on rule: test-rule')
      );
    });

    test('应该处理复杂度断点配置', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: true,
        enableBreakpoints: true,
        breakOnComplexity: 10,
        quiet: true,
      };

      await processor.executeWithDebug(options);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Breakpoint set on complexity >= 10')
      );
    });
  });

  describe('调试级别', () => {
    const testLevels = ['trace', 'debug', 'info', 'warn', 'error'] as const;

    testLevels.forEach(level => {
      test(`应该支持${level}调试级别`, async () => {
        const options: DebugCLIOptions = {
          paths: ['/test/file.ts'],
          debug: true,
          debugLevel: level,
          quiet: true,
        };

        await processor.executeWithDebug(options);

        expect(mockConsoleLog).toHaveBeenCalledWith(
          expect.stringContaining(`Debug system initialized (level: ${level})`)
        );
      });
    });
  });

  describe('输出控制', () => {
    test('应该在静默模式下减少输出', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: true,
        quiet: true,
      };

      await processor.executeWithDebug(options);

      // 应该有一些输出，但不是所有消息
      expect(mockConsoleLog).toHaveBeenCalled();
    });

    test('应该在非静默模式下显示详细信息', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: true,
        quiet: false,
      };

      await processor.executeWithDebug(options);

      // 应该显示分析消息
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Analyzing 1 path(s) with debug enabled')
      );
    });
  });

  describe('错误处理', () => {
    test('应该处理执行错误', async () => {
      const options: DebugCLIOptions = {
        paths: [], // 空路径会导致错误
        debug: true,
        quiet: true,
      };

      await processor.executeWithDebug(options);

      expect(mockConsoleError).toHaveBeenCalledWith(
        expect.stringContaining('Analysis failed')
      );
    });

    test('应该记录执行时间', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: true,
        quiet: false,
      };

      await processor.executeWithDebug(options);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringMatching(/Analysis completed in \d+\.\d+s/)
      );
    });
  });

  describe('性能配置', () => {
    test('应该处理性能监控选项', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: true,
        enableProfiling: true,
        showPerformanceMetrics: true,
        quiet: false,
      };

      await processor.executeWithDebug(options);

      // 性能指标应该被显示
      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Performance Metrics:')
      );
    });

    test('应该处理实时监控选项', async () => {
      const options: DebugCLIOptions = {
        paths: ['/test/file.ts'],
        debug: true,
        enableRealtimeMonitoring: true,
        quiet: true,
      };

      await processor.executeWithDebug(options);

      expect(mockConsoleLog).toHaveBeenCalledWith(
        expect.stringContaining('Debug system initialized')
      );
    });
  });
});