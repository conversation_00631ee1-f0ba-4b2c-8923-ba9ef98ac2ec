import { test, expect } from 'vitest';
import { ComplexityCalculator } from '../../core/calculator';

test('CLI参数正确传递到计算器 - enableDetails=true', () => {
  const options = {
    enableMixedLogicOperatorPenalty: false,
    recursionChainThreshold: 10,
    enableDetails: true
  };
  
  const calculator = new ComplexityCalculator(options);
  
  // 验证计算器实例确实接收了enableDetails参数
  // 通过访问私有属性来验证（这是测试环境，可以这样做）
  expect((calculator as any).options.enableDetails).toBe(true);
});

test('CLI参数正确传递到计算器 - enableDetails=false', () => {
  const options = {
    enableMixedLogicOperatorPenalty: false,
    recursionChainThreshold: 10,
    enableDetails: false
  };
  
  const calculator = new ComplexityCalculator(options);
  
  // 验证计算器实例确实接收了enableDetails参数
  expect((calculator as any).options.enableDetails).toBe(false);
});

test('CLI参数默认值处理 - enableDetails未定义时为false', () => {
  // 模拟CLI中的Boolean转换行为
  const detailsOption = undefined;
  const enableDetails = Boolean(detailsOption); // 这是CLI中实际发生的转换
  
  const options = {
    enableMixedLogicOperatorPenalty: false,
    recursionChainThreshold: 10,
    enableDetails
  };
  
  const calculator = new ComplexityCalculator(options);
  
  // 验证CLI转换后的结果为false
  expect((calculator as any).options.enableDetails).toBe(false);
});

test('验证Boolean转换在CLI中正确工作', () => {
  // 测试CLI中的Boolean转换逻辑
  const detailsOption = true;
  const enableDetails = Boolean(detailsOption);
  expect(enableDetails).toBe(true);
  
  const detailsOptionUndefined = undefined;
  const enableDetailsUndefined = Boolean(detailsOptionUndefined);
  expect(enableDetailsUndefined).toBe(false);
  
  const detailsOptionFalse = false;
  const enableDetailsFalse = Boolean(detailsOptionFalse);
  expect(enableDetailsFalse).toBe(false);
});

test('验证混合逻辑运算符规则参数传递', () => {
  const options = {
    enableMixedLogicOperatorPenalty: true,
    recursionChainThreshold: 20,
    enableDetails: true
  };
  
  const calculator = new ComplexityCalculator(options);
  
  expect((calculator as any).options.enableMixedLogicOperatorPenalty).toBe(true);
  expect((calculator as any).options.recursionChainThreshold).toBe(20);
  expect((calculator as any).options.enableDetails).toBe(true);
});