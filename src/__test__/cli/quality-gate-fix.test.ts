import { test, expect } from 'vitest';
import { execSync } from 'child_process';
import { writeFileSync, unlinkSync } from 'fs';

test('质量门禁失败时的用户体验改进测试', async () => {
  const testFile = 'temp-test-file.ts';
  
  // 创建包含高复杂度函数的测试文件
  const testContent = `
function highComplexityFunction(x: number): number {
  if (x > 0) {
    if (x > 1) {
      if (x > 2) {
        if (x > 3) {
          if (x > 4) {
            if (x > 5) {
              if (x > 6) {
                if (x > 7) {
                  if (x > 8) {
                    if (x > 9) {
                      if (x > 10) {
                        if (x > 11) {
                          if (x > 12) {
                            if (x > 13) {
                              if (x > 14) {
                                return x;
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return 0;
}
`;

  try {
    writeFileSync(testFile, testContent);
    
    // 测试质量门禁失败的情况
    let output = '';
    let exitCode = 0;
    
    try {
      output = execSync(`bun run src/cli/index.ts ${testFile}`, { 
        encoding: 'utf8',
        stdio: 'pipe'
      });
    } catch (error: any) {
      output = error.stdout || error.stderr || '';
      exitCode = error.status;
    }
    
    // 验证退出码为1（质量门禁失败）
    expect(exitCode).toBe(1);
    
    // 验证输出包含改进的错误信息
    expect(output).toContain('质量门禁失败');
    expect(output).toContain('解决方案');
    expect(output).toContain('重构高复杂度函数');
    expect(output).toContain('--fail-on');
    expect(output).toContain('阈值');
    
    console.log('质量门禁失败时的输出:', output);
    
    // 测试质量门禁通过的情况
    try {
      output = execSync(`bun run src/cli/index.ts ${testFile} --fail-on 150`, { 
        encoding: 'utf8',
        stdio: 'pipe'
      });
      exitCode = 0;
    } catch (error: any) {
      output = error.stdout || error.stderr || '';
      exitCode = error.status;
    }
    
    // 验证退出码为0（质量门禁通过）
    expect(exitCode).toBe(0);
    expect(output).toContain('质量门禁通过');
    
    console.log('质量门禁通过时的输出:', output);
    
  } finally {
    // 清理测试文件
    try {
      unlinkSync(testFile);
    } catch (e) {
      // 忽略删除错误
    }
  }
});