import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { CLIUIHelper } from '@/cli/ui-helper';

describe('CLI Progress Bar Fix', () => {
  let uiHelper: CLIUIHelper;
  let consoleLogSpy: any;
  let consoleWarnSpy: any;

  beforeEach(() => {
    uiHelper = new CLIUIHelper();
    consoleLogSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    consoleLogSpy?.mockRestore();
    consoleWarnSpy?.mockRestore();
  });

  test('应该支持带文件名的进度条更新', () => {
    // 创建进度条
    uiHelper.createProgressBar(5, '分析进度');
    
    // 更新进度条包含文件名
    uiHelper.updateProgress(1, 'src/test/file1.ts');
    uiHelper.updateProgress(2, 'src/test/file2.ts');
    
    // 停止进度条
    uiHelper.stopProgress();
    
    // 验证创建了进度条（这个测试主要验证API兼容性）
    expect(true).toBe(true);
  });

  test('应该在进度条活动时缓冲警告消息', () => {
    // 创建进度条
    uiHelper.createProgressBar(3, '分析进度');
    
    // 在进度条活动时发送警告
    uiHelper.warning('这是一个测试警告');
    
    // 此时警告应该被缓冲，console.warn不应该被调用
    expect(consoleWarnSpy).not.toHaveBeenCalled();
    
    // 停止进度条
    uiHelper.stopProgress();
    
    // 现在警告应该被输出
    expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining('这是一个测试警告'));
  });

  test('应该在非进度条模式时立即显示警告', () => {
    // 不创建进度条，直接发送警告
    uiHelper.warning('立即显示的警告');
    
    // 警告应该立即显示
    expect(consoleWarnSpy).toHaveBeenCalledWith(expect.stringContaining('立即显示的警告'));
  });

  test('应该支持不带文件名的进度条更新（向后兼容）', () => {
    // 创建进度条
    uiHelper.createProgressBar(3, '分析进度');
    
    // 更新进度条不包含文件名（向后兼容）
    uiHelper.updateProgress(1);
    uiHelper.updateProgress(2);
    
    // 停止进度条
    uiHelper.stopProgress();
    
    // 验证API兼容性
    expect(true).toBe(true);
  });
});