import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { CommandProcessor } from "../../cli/commands";
import type { CLIOptions } from "../../config/types";
import { writeFileSync, unlinkSync, existsSync, mkdirSync, rmSync } from "fs";
import { join } from "path";

describe("CommandProcessor - 排除功能", () => {
  const testDir = join(process.cwd(), "test-command-processor");
  const testFiles = {
    nodeModulesFile: join(testDir, "node_modules", "lib", "index.ts"),
    srcFile: join(testDir, "src", "main.ts"), 
    testFile: join(testDir, "src", "main.test.ts"),
    configFile: join(testDir, "package.json"),
    pythonConfig: join(testDir, "requirements.txt"),
    javaConfig: join(testDir, "pom.xml"),
    rustConfig: join(testDir, "Cargo.toml"),
    goConfig: join(testDir, "go.mod")
  };

  beforeEach(() => {
    // 创建测试目录结构
    if (!existsSync(testDir)) {
      mkdirSync(testDir, { recursive: true });
    }
    mkdirSync(join(testDir, "node_modules", "lib"), { recursive: true });
    mkdirSync(join(testDir, "src"), { recursive: true });

    // 创建测试文件
    writeFileSync(testFiles.srcFile, `
function mainFunction() {
  return "main";
}
    `);

    writeFileSync(testFiles.testFile, `
function testFunction() {
  return "test";
}
    `);

    writeFileSync(testFiles.nodeModulesFile, `
function libraryFunction() {
  return "library";
}
    `);

    // 创建项目配置文件
    writeFileSync(testFiles.configFile, JSON.stringify({
      name: "test-project",
      version: "1.0.0"
    }, null, 2));
  });

  afterEach(() => {
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
  });

  test("应该智能检测Node.js项目并排除相应文件", async () => {
    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      details: true
    };

    const result = await processor.analyzeFiles([testDir], options);

    // 应该包含src文件，排除node_modules文件
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("src/main.ts"))).toBe(true);
    expect(filePaths.some(path => path.includes("node_modules"))).toBe(false);
  });

  test("应该支持CLI排除参数", async () => {
    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      exclude: ["**/*.test.ts"]
    };

    const result = await processor.analyzeFiles([testDir], options);

    // 应该排除test文件
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("main.ts"))).toBe(true);
    expect(filePaths.some(path => path.includes("test.ts"))).toBe(false);
  });

  test("应该支持excludePattern参数", async () => {
    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      excludePattern: ["**/src/**/*.test.ts"]
    };

    const result = await processor.analyzeFiles([testDir], options);

    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("main.ts"))).toBe(true);
    expect(filePaths.some(path => path.includes("test.ts"))).toBe(false);
  });

  test("应该支持includeDeps标志禁用智能排除", async () => {
    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      includeDeps: true,
      details: true
    };

    const result = await processor.analyzeFiles([testDir], options);

    // includeDeps=true时应该包含node_modules文件
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("node_modules"))).toBe(true);
  });

  test("应该支持excludeDefaults=false禁用默认排除", async () => {
    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      excludeDefaults: false
    };

    const result = await processor.analyzeFiles([testDir], options);

    // 应该有分析结果（可能包含通常被排除的文件）
    expect(result.results.length).toBeGreaterThan(0);
  });

  test("应该检测Python项目类型", async () => {
    writeFileSync(testFiles.pythonConfig, "requests==2.25.1\nflask==2.0.1");
    mkdirSync(join(testDir, "venv", "lib"), { recursive: true });
    writeFileSync(join(testDir, "venv", "lib", "python.py"), "# Python library code");
    writeFileSync(join(testDir, "main.py"), "# Main python file");

    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      details: true
    };

    // 创建一个特殊的processor来测试项目检测
    // 由于detectProjectType是私有方法，我们通过分析结果间接测试
    const result = await processor.analyzeFiles([testDir], options);
    
    // 应该排除venv目录下的文件
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("venv"))).toBe(false);
  });

  test("应该检测Java项目类型", async () => {
    writeFileSync(testFiles.javaConfig, `
<?xml version="1.0" encoding="UTF-8"?>
<project>
    <groupId>com.example</groupId>
    <artifactId>test-project</artifactId>
    <version>1.0.0</version>
</project>
    `);
    mkdirSync(join(testDir, "target", "classes"), { recursive: true });
    writeFileSync(join(testDir, "target", "classes", "App.class"), "// Compiled Java class");

    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      details: true
    };

    const result = await processor.analyzeFiles([testDir], options);
    
    // 应该排除target目录下的文件
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("target"))).toBe(false);
  });

  test("应该检测Rust项目类型", async () => {
    writeFileSync(testFiles.rustConfig, `
[package]
name = "test-project"
version = "0.1.0"
edition = "2021"
    `);
    mkdirSync(join(testDir, "target", "debug"), { recursive: true });
    writeFileSync(join(testDir, "target", "debug", "test-project"), "// Rust binary");

    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      details: true
    };

    const result = await processor.analyzeFiles([testDir], options);
    
    // 应该排除target目录下的文件
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("target"))).toBe(false);
  });

  test("应该检测Go项目类型", async () => {
    writeFileSync(testFiles.goConfig, `
module test-project

go 1.19

require (
    github.com/gorilla/mux v1.8.0
)
    `);
    mkdirSync(join(testDir, "vendor", "github.com"), { recursive: true });
    writeFileSync(join(testDir, "vendor", "github.com", "package.go"), "// Go vendor package");

    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      details: true
    };

    const result = await processor.analyzeFiles([testDir], options);
    
    // 应该排除vendor目录下的文件
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("vendor"))).toBe(false);
  });

  test("应该显示排除统计信息", async () => {
    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      details: true,
      showExcluded: true
    };

    // 捕获控制台输出
    const originalLog = console.log;
    const logs: string[] = [];
    console.log = (...args: any[]) => {
      logs.push(args.join(' '));
    };

    try {
      await processor.analyzeFiles([testDir], options);
      
      // 应该输出项目类型检测信息
      expect(logs.some(log => log.includes("检测到项目类型"))).toBe(true);
    } finally {
      console.log = originalLog;
    }
  });

  test("应该处理多个排除模式的优先级", async () => {
    const processor = new CommandProcessor();
    
    // 设置配置管理器加载包含排除规则的配置
    const configPath = join(testDir, "cognitive.config.json");
    writeFileSync(configPath, JSON.stringify({
      exclude: ["**/config/**"],
      excludeDefaults: true
    }, null, 2));

    const options: CLIOptions = {
      paths: [testDir],
      config: configPath,
      exclude: ["**/*.test.ts"], // CLI参数应该有更高优先级
      excludePattern: ["**/temp/**"]
    };

    const result = await processor.analyzeFiles([testDir], options);

    // CLI参数应该生效
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("test.ts"))).toBe(false);
  });

  test("应该处理空的排除选项", async () => {
    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir]
      // 不提供任何排除选项
    };

    const result = await processor.analyzeFiles([testDir], options);

    // 应该有默认的排除行为
    expect(result.results.length).toBeGreaterThanOrEqual(0);
    
    // 应该排除node_modules（通过智能检测）
    const filePaths = result.results.map(r => r.filePath);
    expect(filePaths.some(path => path.includes("node_modules"))).toBe(false);
  });

  test("应该处理无效的glob模式", async () => {
    const processor = new CommandProcessor();
    const options: CLIOptions = {
      paths: [testDir],
      exclude: ["../dangerous-path", "valid-pattern/**"],
      excludePattern: ["[unclosed-bracket", "**/*.test.ts"]
    };

    // 捕获警告
    const originalWarn = console.warn;
    const warnings: string[] = [];
    console.warn = (...args: any[]) => {
      warnings.push(args.join(' '));
    };

    try {
      const result = await processor.analyzeFiles([testDir], options);
      
      // 应该仍然能够分析文件
      expect(result.results.length).toBeGreaterThanOrEqual(0);
      
      // 有效的排除模式应该生效
      const filePaths = result.results.map(r => r.filePath);
      expect(filePaths.some(path => path.includes("test.ts"))).toBe(false);
    } finally {
      console.warn = originalWarn;
    }
  });
});