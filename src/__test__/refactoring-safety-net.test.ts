import { describe, test, expect, beforeEach } from 'vitest';
import { analyzeFile } from '@/index';
import { RuleRegistry } from '@/core/rule-registry';
import { ComplexityVisitor } from '@/core/complexity-visitor';
import { DetailCollector } from '@/core/detail-collector';
import { initializeRules } from '@/core/default-rules';
import type { CalculationOptions } from '@/core/types';

/**
 * 重构安全网测试套件
 * 确保重构过程中关键功能不会被破坏
 */
describe('Refactoring Safety Net', () => {
  beforeEach(() => {
    // Initialize rules before each test
    initializeRules(true); // quiet mode
  });

  describe('Core Calculation Baseline Verification', () => {
    test('should calculate basic complexity correctly', async () => {
      const code = `
        function simple() {
          return 42;
        }
      `;
      
      const result = await analyzeFile('test-inline', { enableDetails: false });
      // For inline code testing, we'll use a file-based approach
      // This tests the core functionality of the new architecture
      expect(result.functions).toHaveLength(0); // No functions found in empty file
      expect(result.complexity).toBe(0);
    });

    test('should calculate if statement complexity', async () => {
      const code = `
        function withIf(x: number) {
          if (x > 0) {
            return x;
          }
          return 0;
        }
      `;
      
      const results = await calculator.calculateCode(code, 'test.ts');
      expect(results).toHaveLength(1);
      expect(results[0]!.complexity).toBe(1);
    });

    test('should calculate nested complexity with penalty', async () => {
      const code = `
        function nested(x: number, y: number) {
          if (x > 0) {        // +1
            if (y > 0) {      // +2 (nested)
              return x + y;
            }
          }
          return 0;
        }
      `;
      
      const results = await calculator.calculateCode(code, 'test.ts');
      expect(results).toHaveLength(1);
      expect(results[0]!.complexity).toBe(3); // 1 + 2
    });

    test('should handle multiple functions', async () => {
      const code = `
        function first() {
          if (true) return 1;
        }
        
        function second() {
          if (true) {
            if (false) return 2;
          }
        }
      `;
      
      const results = await calculator.calculateCode(code, 'test.ts');
      expect(results).toHaveLength(2);
      expect(results[0]!.complexity).toBe(1);
      expect(results[1]!.complexity).toBe(3);
    });
  });

  describe('Visitor Pattern Architecture Verification', () => {
    test('should create ComplexityVisitor with correct configuration', () => {
      const sourceCode = 'function test() {}';
      const detailCollector = new DetailCollector();
      const options: CalculationOptions = { enableDebugLog: true };
      
      const visitor = new ComplexityVisitor(sourceCode, detailCollector, options);
      
      expect(visitor).toBeInstanceOf(ComplexityVisitor);
      expect(visitor.getTotalComplexity()).toBe(0);
    });

    test('should maintain visitor state correctly', async () => {
      const code = `
        function test() {
          if (true) {
            while (condition) {
              return value;
            }
          }
        }
      `;
      
      // This should work through the existing visitor pattern
      const results = await calculator.calculateCode(code, 'test.ts');
      expect(results[0]!.complexity).toBeGreaterThan(0);
    });
  });

  describe('Rule Registry Verification', () => {
    test('should have rules registered in registry', () => {
      const allRules = RuleRegistry.getAllRules();
      expect(allRules.length).toBeGreaterThan(0);
      
      // Should have at least logical operator rules  
      const logicalRule = RuleRegistry.getRule('logical-operators');
      expect(logicalRule).toBeDefined();
    });

    test('should support rule registration operations', () => {
      const initialCount = RuleRegistry.getAllRules().length;
      
      // Register a test rule
      RuleRegistry.register('test-rule', 'Test rule for refactoring');
      
      expect(RuleRegistry.getAllRules().length).toBe(initialCount + 1);
      expect(RuleRegistry.getRule('test-rule')).toBeDefined();
      
      // Clean up - check if delete method exists first
      if (typeof RuleRegistry.delete === 'function') {
        RuleRegistry.delete('test-rule');
        expect(RuleRegistry.getAllRules().length).toBe(initialCount);
      }
    });
  });

  describe('Static API Compatibility Verification', () => {
    test('should support analyze static method', async () => {
      const code = 'function test() { if (true) return; }';
      
      const results = await ComplexityCalculator.analyze(code);
      expect(results).toHaveLength(1);
      expect(results[0]!.complexity).toBe(1);
    });

    test('should support quickAnalyze static method', async () => {
      const code = 'function test() { if (true) return; }';
      
      const overview = await ComplexityCalculator.quickAnalyze(code);
      expect(overview.functionCount).toBe(1);
      expect(overview.totalComplexity).toBe(1);
    });
  });

  describe('DetailCollector Integration Verification', () => {
    test('should collect details when enabled', async () => {
      const calc = new ComplexityCalculator({ enableDetails: true }, factory);
      const code = `
        function withDetails() {
          if (condition) {
            return value;
          }
        }
      `;
      
      const results = await calc.calculateCode(code, 'test.ts');
      expect(results[0]!.details).toBeDefined();
      expect(results[0]!.details!.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling and Resilience', () => {
    test('should handle malformed code gracefully', async () => {
      const malformedCode = 'function broken( { invalid syntax }';
      
      // Should either return empty array or handle error gracefully
      try {
        const results = await calculator.calculateCode(malformedCode, 'test.ts');
        expect(Array.isArray(results)).toBe(true);
      } catch (error) {
        // If it throws, should be a well-structured error
        expect(error).toBeInstanceOf(Error);
      }
    });

    test('should handle empty code', async () => {
      const results = await calculator.calculateCode('', 'test.ts');
      expect(results).toEqual([]);
    });

    test('should handle code without functions', async () => {
      const code = 'const x = 42; console.log(x);';
      const results = await calculator.calculateCode(code, 'test.ts');
      expect(results).toEqual([]);
    });
  });

  describe('Resource Management Verification', () => {
    test('should properly dispose resources', async () => {
      const calc = new ComplexityCalculator({}, createFullFeaturedFactory());
      
      // Use the calculator
      await calc.calculateCode('function test() {}', 'test.ts');
      
      // Should be able to dispose without errors
      await expect(calc.dispose()).resolves.toBeUndefined();
    });
  });

  describe('Performance Baseline Verification', () => {
    test('should complete analysis within reasonable time', async () => {
      const largeCode = Array(50).fill(0).map((_, i) => `
        function func${i}() {
          if (condition${i}) {
            while (loop${i}) {
              for (let j = 0; j < 10; j++) {
                if (nested${i}) {
                  return result${i};
                }
              }
            }
          }
        }
      `).join('\n');
      
      const startTime = performance.now();
      const results = await calculator.calculateCode(largeCode, 'large.ts');
      const duration = performance.now() - startTime;
      
      expect(results).toHaveLength(50);
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });
});