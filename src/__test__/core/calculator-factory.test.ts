/**
 * CalculatorFactory 测试
 * 验证IoC工厂模式和组件创建
 */

import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { 
  CalculatorFactory, 
  createCalculatorFactory, 
  createLightweightFactory,
  createFullFeaturedFactory,
  type ComponentFactory 
} from '../../core/calculator-factory';
import type { CalculatorOptions } from '../../engine/types';

describe('CalculatorFactory', () => {
  let factory: CalculatorFactory;

  beforeEach(() => {
    factory = new CalculatorFactory();
  });

  afterEach(() => {
    // 清理任何创建的组件
  });

  describe('构造函数和配置', () => {
    test('应该使用默认配置创建工厂', () => {
      const factory = new CalculatorFactory();
      const options = factory.getOptions();
      
      expect(options.enableMonitoring).toBe(false);
      expect(options.enableCaching).toBe(true);
      expect(options.maxConcurrency).toBe(4);
      expect(options.debugMode).toBe(false);
      expect(options.quiet).toBe(false);
      expect(options.detailLevel).toBe('standard');
    });

    test('应该使用自定义配置创建工厂', () => {
      const customOptions: CalculatorOptions = {
        enableMonitoring: true,
        maxConcurrency: 8,
        debugMode: true,
        detailLevel: 'verbose',
        monitorConfig: {
          collectMetrics: true,
          performanceThreshold: 50
        }
      };

      const factory = new CalculatorFactory(customOptions);
      const options = factory.getOptions();
      
      expect(options.enableMonitoring).toBe(true);
      expect(options.maxConcurrency).toBe(8);
      expect(options.debugMode).toBe(true);
      expect(options.detailLevel).toBe('verbose');
      expect(options.monitorConfig.collectMetrics).toBe(true);
      expect(options.monitorConfig.performanceThreshold).toBe(50);
    });
  });

  describe('执行池创建', () => {
    test('应该创建轻量级执行池当监控被禁用', () => {
      const factory = new CalculatorFactory({ enableMonitoring: false });
      const pool = factory.createExecutionPool();
      
      expect(pool).toBeDefined();
      expect(pool.constructor.name).toBe('LightweightExecutionPool');
    });

    test('应该创建轻量级执行池当并发数为1', () => {
      const factory = new CalculatorFactory({ maxConcurrency: 1 });
      const pool = factory.createExecutionPool();
      
      expect(pool).toBeDefined();
      expect(pool.constructor.name).toBe('LightweightExecutionPool');
    });

    test('应该创建并行执行池当监控启用且并发数大于1', () => {
      const factory = new CalculatorFactory({ 
        enableMonitoring: true, 
        maxConcurrency: 4 
      });
      const pool = factory.createExecutionPool();
      
      expect(pool).toBeDefined();
      expect(pool.constructor.name).toBe('ParallelExecutionPool');
    });

    test('应该使用自定义执行选项', () => {
      const pool = factory.createExecutionPool({
        maxConcurrency: 2,
        timeout: 3000,
        enableProfiling: true
      });
      
      expect(pool).toBeDefined();
    });
  });

  describe('性能监控器创建', () => {
    test('应该创建空监控器当监控被禁用', () => {
      const factory = new CalculatorFactory({ enableMonitoring: false });
      const monitor = factory.createPerformanceMonitor();
      
      expect(monitor).toBeDefined();
      expect(monitor.constructor.name).toBe('NullPerformanceMonitor');
    });

    test('应该创建高级监控器当监控启用', () => {
      const factory = new CalculatorFactory({ enableMonitoring: true });
      const monitor = factory.createPerformanceMonitor();
      
      expect(monitor).toBeDefined();
      expect(monitor.constructor.name).toBe('AdvancedPerformanceMonitor');
    });

    test('应该使用自定义监控配置', () => {
      const factory = new CalculatorFactory({ 
        enableMonitoring: true,
        monitorConfig: {
          collectMetrics: true,
          performanceThreshold: 100
        }
      });
      
      const monitor = factory.createPerformanceMonitor({
        enabled: true,
        enableRealTimeMonitoring: false
      });
      
      expect(monitor).toBeDefined();
    });
  });

  describe('缓存管理器创建', () => {
    test('应该创建空缓存管理器当缓存被禁用', () => {
      const factory = new CalculatorFactory({ enableCaching: false });
      const cache = factory.createCacheManager();
      
      expect(cache).toBeDefined();
      expect(cache.constructor.name).toBe('NullCacheManager');
      expect(cache.size()).toBe(0);
      expect(cache.get('test')).toBeUndefined();
    });

    test('应该创建缓存管理器当缓存启用', () => {
      const factory = new CalculatorFactory({ enableCaching: true });
      const cache = factory.createCacheManager(5000);
      
      expect(cache).toBeDefined();
      expect(cache.size).toBe(5000);
      expect(cache.enabled).toBe(true);
    });
  });

  describe('插件管理器创建', () => {
    test('应该创建空插件管理器当无插件配置', () => {
      const factory = new CalculatorFactory({ plugins: [] });
      const pluginManager = factory.createPluginManager();
      
      expect(pluginManager).toBeDefined();
      expect(pluginManager.constructor.name).toBe('NullPluginManager');
      expect(pluginManager.getLoadedPlugins()).toHaveLength(0);
    });

    test('应该创建插件管理器当有插件配置', () => {
      const plugins = [
        { path: './plugin1', options: {} },
        { path: './plugin2', options: {} }
      ];
      
      const factory = new CalculatorFactory({ plugins });
      const pluginManager = factory.createPluginManager();
      
      expect(pluginManager).toBeDefined();
      expect(pluginManager.plugins).toHaveLength(2);
    });
  });

  describe('工厂方法', () => {
    test('withOptions 应该创建新的工厂实例', () => {
      const originalOptions: CalculatorOptions = {
        enableMonitoring: false,
        maxConcurrency: 2
      };
      
      const factory1 = new CalculatorFactory(originalOptions);
      const factory2 = factory1.withOptions({
        enableMonitoring: true,
        debugMode: true
      });
      
      expect(factory1).not.toBe(factory2);
      expect(factory1.getOptions().enableMonitoring).toBe(false);
      expect(factory2.getOptions().enableMonitoring).toBe(true);
      expect(factory2.getOptions().maxConcurrency).toBe(2); // 继承原配置
      expect(factory2.getOptions().debugMode).toBe(true);
    });

    test('isFeatureEnabled 应该正确检查功能状态', () => {
      const factory = new CalculatorFactory({
        enableMonitoring: true,
        enableCaching: false,
        debugMode: true
      });
      
      expect(factory.isFeatureEnabled('enableMonitoring')).toBe(true);
      expect(factory.isFeatureEnabled('enableCaching')).toBe(false);
      expect(factory.isFeatureEnabled('debugMode')).toBe(true);
    });

    test('getFeatureSummary 应该返回功能概览', () => {
      const factory = new CalculatorFactory({
        enableMonitoring: true,
        enableCaching: true,
        debugMode: false,
        maxConcurrency: 8,
        plugins: [{ path: './test-plugin', options: {} }]
      });
      
      const summary = factory.getFeatureSummary();
      
      expect(summary.monitoring).toBe(true);
      expect(summary.caching).toBe(true);
      expect(summary.debugging).toBe(false);
      expect(summary.plugins).toBe(true);
      expect(summary.parallelExecution).toBe(true);
    });
  });

  describe('便捷工厂函数', () => {
    test('createCalculatorFactory 应该创建默认工厂', () => {
      const factory = createCalculatorFactory();
      expect(factory).toBeInstanceOf(CalculatorFactory);
      expect(factory.getOptions().enableMonitoring).toBe(false);
    });

    test('createCalculatorFactory 应该支持自定义选项', () => {
      const factory = createCalculatorFactory({
        enableMonitoring: true,
        maxConcurrency: 6
      });
      
      expect(factory.getOptions().enableMonitoring).toBe(true);
      expect(factory.getOptions().maxConcurrency).toBe(6);
    });

    test('createLightweightFactory 应该创建轻量级工厂', () => {
      const factory = createLightweightFactory();
      const summary = factory.getFeatureSummary();
      
      expect(summary.monitoring).toBe(false);
      expect(summary.caching).toBe(false);
      expect(summary.debugging).toBe(false);
      expect(summary.parallelExecution).toBe(false);
      expect(factory.getOptions().quiet).toBe(true);
      expect(factory.getOptions().detailLevel).toBe('minimal');
    });

    test('createFullFeaturedFactory 应该创建完整功能工厂', () => {
      const factory = createFullFeaturedFactory();
      const summary = factory.getFeatureSummary();
      
      expect(summary.monitoring).toBe(true);
      expect(summary.caching).toBe(true);
      expect(summary.parallelExecution).toBe(true);
      expect(factory.getOptions().detailLevel).toBe('verbose');
      expect(factory.getOptions().monitorConfig.collectMetrics).toBe(true);
    });

    test('createFullFeaturedFactory 应该支持覆盖选项', () => {
      const factory = createFullFeaturedFactory({
        maxConcurrency: 16,
        debugMode: true
      });
      
      expect(factory.getOptions().maxConcurrency).toBe(16);
      expect(factory.getOptions().debugMode).toBe(true);
      expect(factory.getOptions().enableMonitoring).toBe(true); // 仍然保持默认启用
    });
  });

  describe('组件创建集成测试', () => {
    test('应该创建一致的组件配置', () => {
      const factory = new CalculatorFactory({
        enableMonitoring: true,
        enableCaching: true,
        maxConcurrency: 4
      });
      
      const pool = factory.createExecutionPool();
      const monitor = factory.createPerformanceMonitor();
      const cache = factory.createCacheManager();
      const pluginManager = factory.createPluginManager();
      
      // 验证组件类型一致性
      expect(pool.constructor.name).toBe('ParallelExecutionPool');
      expect(monitor.constructor.name).toBe('AdvancedPerformanceMonitor');
      expect(cache.enabled).toBe(true);
      expect(pluginManager.constructor.name).toBe('NullPluginManager'); // 无插件配置时
    });

    test('轻量级配置应该创建轻量级组件', () => {
      const factory = createLightweightFactory();
      
      const pool = factory.createExecutionPool();
      const monitor = factory.createPerformanceMonitor();
      const cache = factory.createCacheManager();
      
      expect(pool.constructor.name).toBe('LightweightExecutionPool');
      expect(monitor.constructor.name).toBe('NullPerformanceMonitor');
      expect(cache.constructor.name).toBe('NullCacheManager');
    });
  });

  describe('资源管理', () => {
    test('创建的组件应该有正确的生命周期方法', () => {
      const factory = new CalculatorFactory({ enableMonitoring: true });
      
      const pool = factory.createExecutionPool();
      const monitor = factory.createPerformanceMonitor();
      
      expect(typeof pool.shutdown).toBe('function');
      expect(typeof monitor.stop).toBe('function');
    });

    test('轻量级组件应该有无操作的生命周期方法', () => {
      const factory = createLightweightFactory();
      
      const pool = factory.createExecutionPool();
      const monitor = factory.createPerformanceMonitor();
      
      expect(typeof pool.shutdown).toBe('function');
      expect(typeof monitor.stop).toBe('function');
      
      // 这些调用不应该抛出错误
      expect(() => pool.shutdown()).not.toThrow();
      expect(() => monitor.stop()).not.toThrow();
    });
  });
});