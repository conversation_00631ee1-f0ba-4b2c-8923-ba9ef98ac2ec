import { ASTParser } from '../../core/parser';
import { ParseError } from '../../core/errors';
import type { Module } from '@swc/core';

describe('ASTParser', () => {
  let parser: ASTParser;

  beforeEach(() => {
    parser = new ASTParser();
  });

  describe('parseCode', () => {
    it('should parse valid TypeScript code', async () => {
      const code = `
        function hello(): string {
          return 'world';
        }
      `;
      
      const ast = await parser.parseCode(code, 'test.ts');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
      expect(ast.body).toBeDefined();
    });

    it('should parse valid JavaScript code', async () => {
      const code = `
        function hello() {
          return 'world';
        }
      `;
      
      const ast = await parser.parseCode(code, 'test.js');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
      expect(ast.body).toBeDefined();
    });

    it('should parse JSX code', async () => {
      const code = `
        import React from 'react';
        function Component() {
          return <div>Hello</div>;
        }
      `;
      
      const ast = await parser.parseCode(code, 'test.jsx');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });

    it('should parse TSX code', async () => {
      const code = `
        import React from 'react';
        function Component(): JSX.Element {
          return <div>Hello</div>;
        }
      `;
      
      const ast = await parser.parseCode(code, 'test.tsx');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });

    it('should throw ParseError for invalid code', async () => {
      const invalidCode = 'function invalid() { return';
      
      await expect(parser.parseCode(invalidCode, 'test.ts')).rejects.toThrow(ParseError);
    });

    it('should handle decorators in TypeScript', async () => {
      const code = `
        @Component
        class MyClass {
          @Property
          public prop: string = '';
        }
      `;
      
      const ast = await parser.parseCode(code, 'test.ts');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });

    it('should handle dynamic imports', async () => {
      const code = `
        async function loadModule() {
          const module = await import('./some-module');
          return module;
        }
      `;
      
      const ast = await parser.parseCode(code, 'test.ts');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });
  });

  describe('parseFile', () => {
    it('should throw ParseError for non-existent file', async () => {
      await expect(parser.parseFile('/non/existent/file.ts')).rejects.toThrow(ParseError);
    });
  });

  describe('getFunctionName', () => {
    it('should get name from FunctionDeclaration', () => {
      const node = {
        type: 'FunctionDeclaration',
        identifier: { value: 'testFunction' }
      };
      
      expect(parser.getFunctionName(node)).toBe('testFunction');
    });

    it('should get name from FunctionDeclaration with name property', () => {
      const node = {
        type: 'FunctionDeclaration',
        identifier: { name: 'testFunction' }
      };
      
      expect(parser.getFunctionName(node)).toBe('testFunction');
    });

    it('should get name from MethodDefinition', () => {
      const node = {
        type: 'MethodDefinition',
        key: { value: 'methodName' }
      };
      
      expect(parser.getFunctionName(node)).toBe('methodName');
    });

    it('should get name from MethodDefinition with Identifier key', () => {
      const node = {
        type: 'MethodDefinition',
        key: { type: 'Identifier', value: 'methodName' }
      };
      
      expect(parser.getFunctionName(node)).toBe('methodName');
    });

    it('should get name from ClassMethod', () => {
      const node = {
        type: 'ClassMethod',
        key: { value: 'classMethod' }
      };
      
      expect(parser.getFunctionName(node)).toBe('classMethod');
    });

    it('should get name from VariableDeclarator', () => {
      const node = {
        type: 'VariableDeclarator',
        id: { value: 'variableFunction' }
      };
      
      expect(parser.getFunctionName(node)).toBe('variableFunction');
    });

    it('should get name from VariableDeclarator with name property', () => {
      const node = {
        type: 'VariableDeclarator',
        id: { name: 'variableFunction' }
      };
      
      expect(parser.getFunctionName(node)).toBe('variableFunction');
    });

    it('should handle ExportDeclaration wrapping', () => {
      const node = {
        type: 'ExportDeclaration',
        declaration: {
          type: 'FunctionDeclaration',
          identifier: { value: 'exportedFunction' }
        }
      };
      
      expect(parser.getFunctionName(node)).toBe('exportedFunction');
    });

    it('should return <anonymous> for ArrowFunctionExpression', () => {
      const node = {
        type: 'ArrowFunctionExpression'
      };
      
      expect(parser.getFunctionName(node)).toBe('<anonymous>');
    });

    it('should return <anonymous> for FunctionExpression', () => {
      const node = {
        type: 'FunctionExpression'
      };
      
      expect(parser.getFunctionName(node)).toBe('<anonymous>');
    });

    it('should return <method> for MethodDefinition without key value', () => {
      const node = {
        type: 'MethodDefinition',
        key: {}
      };
      
      expect(parser.getFunctionName(node)).toBe('<method>');
    });

    it('should return <anonymous> for unknown node types', () => {
      const node = {
        type: 'UnknownType'
      };
      
      expect(parser.getFunctionName(node)).toBe('<anonymous>');
    });

    it('should return <anonymous> for VariableDeclarator without id', () => {
      const node = {
        type: 'VariableDeclarator'
      };
      
      expect(parser.getFunctionName(node)).toBe('<anonymous>');
    });
  });

  describe('getLocation', () => {
    beforeEach(async () => {
      // Set up parser with some source code for location testing
      const code = 'function test() {\n  return true;\n}';
      await parser.parseCode(code, 'test.ts');
    });

    it('should get location from node with valid span', () => {
      const node = {
        type: 'FunctionDeclaration',
        span: { start: 0, end: 10 }
      };
      
      const location = parser.getLocation(node);
      
      expect(location.line).toBe(1);
      expect(location.column).toBeGreaterThan(0);
    });

    it('should handle ExportDeclaration wrapping', () => {
      const node = {
        type: 'ExportDeclaration',
        declaration: {
          type: 'FunctionDeclaration',
          span: { start: 7, end: 17 }
        }
      };
      
      const location = parser.getLocation(node);
      
      expect(location.line).toBeGreaterThan(0);
      expect(location.column).toBeGreaterThan(0);
    });

    it('should handle VariableDeclarator with init', () => {
      const node = {
        type: 'VariableDeclarator',
        init: {
          type: 'ArrowFunctionExpression',
          span: { start: 5, end: 15 }
        }
      };
      
      const location = parser.getLocation(node);
      
      expect(location.line).toBeGreaterThan(0);
      expect(location.column).toBeGreaterThan(0);
    });

    it('should return default location for node without span', () => {
      const node = {
        type: 'FunctionDeclaration'
      };
      
      const location = parser.getLocation(node);
      
      expect(location).toEqual({ line: 1, column: 0 });
    });

    it('should return default location for node with invalid span', () => {
      const node = {
        type: 'FunctionDeclaration',
        span: {}
      };
      
      const location = parser.getLocation(node);
      
      expect(location).toEqual({ line: 1, column: 0 });
    });
  });

  describe('isLineIgnored', () => {
    beforeEach(async () => {
      const code = `function test1() {
  return 1;
}
// cognitive-complexity-ignore-next-line
function test2() {
  if (true) {
    return 2;
  }
}
/* cognitive-complexity-ignore-next-line */
function test3() {
  return 3;
}`;
      await parser.parseCode(code, 'test.ts');
    });

    it('should return false for lines not preceded by ignore comment', () => {
      expect(parser.isLineIgnored(1)).toBe(false);
      expect(parser.isLineIgnored(2)).toBe(false);
      expect(parser.isLineIgnored(3)).toBe(false);
    });

    it('should return true for lines preceded by // ignore comment', () => {
      expect(parser.isLineIgnored(5)).toBe(true); // function test2() is on line 5
    });

    it('should return true for lines preceded by /* */ ignore comment', () => {
      expect(parser.isLineIgnored(11)).toBe(true); // function test3() is on line 11
    });

    it('should return false for invalid line numbers', () => {
      expect(parser.isLineIgnored(0)).toBe(false);
      expect(parser.isLineIgnored(-1)).toBe(false);
      expect(parser.isLineIgnored(100)).toBe(false);
    });

    it('should return false when no source lines are available', () => {
      const emptyParser = new ASTParser();
      expect(emptyParser.isLineIgnored(1)).toBe(false);
    });
  });

  describe('findIgnoreExemptions', () => {
    beforeEach(async () => {
      const code = `function test1() {
  return 1;
}
// cognitive-complexity-ignore-next-line
function test2() {
  return 2;
}
/* cognitive-complexity-ignore-next-line */
function test3() {
  return 3;
}`;
      await parser.parseCode(code, 'test.ts');
    });

    it('should find all ignore exemption comments', () => {
      const exemptions = parser.findIgnoreExemptions();
      
      expect(exemptions).toHaveLength(2);
      
      expect(exemptions[0]).toEqual({
        line: 5,
        type: 'ignore-next-line',
        complexityReduced: 0
      });
      
      expect(exemptions[1]).toEqual({
        line: 9,
        type: 'ignore-next-line',
        complexityReduced: 0
      });
    });

    it('should return empty array when there are no ignore comments', async () => {
      const code = `function test() {
  return 1;
}`;
      await parser.parseCode(code, 'test.ts');
      
      const exemptions = parser.findIgnoreExemptions();
      
      expect(exemptions).toHaveLength(0);
    });

    it('should handle edge cases with empty lines', async () => {
      const code = `function test1() {
  return 1;
}
// cognitive-complexity-ignore-next-line

function test2() {
  return 1;
}`;
      await parser.parseCode(code, 'test.ts');
      
      const exemptions = parser.findIgnoreExemptions();
      
      expect(exemptions).toHaveLength(1);
      expect(exemptions[0].line).toBe(5); // Comment is on line 4, applies to line 5 (empty line)
    });
  });

  describe('detectSyntax', () => {
    it('should detect TypeScript syntax for .ts files', async () => {
      const code = 'const x: number = 1;';
      const ast = await parser.parseCode(code, 'test.ts');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });

    it('should detect TypeScript syntax for .tsx files', async () => {
      const code = 'const Component = (): JSX.Element => <div />;';
      const ast = await parser.parseCode(code, 'test.tsx');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });

    it('should detect ECMAScript syntax for .js files', async () => {
      const code = 'const x = 1;';
      const ast = await parser.parseCode(code, 'test.js');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });

    it('should detect ECMAScript syntax for .jsx files', async () => {
      const code = 'const Component = () => React.createElement("div");';
      const ast = await parser.parseCode(code, 'test.jsx');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });

    it('should detect ECMAScript syntax for .mjs files', async () => {
      const code = 'export const x = 1;';
      const ast = await parser.parseCode(code, 'test.mjs');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });

    it('should default to TypeScript syntax for unknown extensions', async () => {
      const code = 'const x = 1;';
      const ast = await parser.parseCode(code, 'test.unknown');
      
      expect(ast).toBeDefined();
      expect(ast.type).toBe('Module');
    });
  });

  describe('integration with source code tracking', () => {
    it('should maintain source code and lines for ignore comment processing', async () => {
      const code = `const line1 = 1;
// cognitive-complexity-ignore-next-line  
const line3 = 3;
const line4 = 4;`;
      
      await parser.parseCode(code, 'test.ts');
      
      expect(parser.isLineIgnored(3)).toBe(true);
      expect(parser.isLineIgnored(1)).toBe(false);
      expect(parser.isLineIgnored(4)).toBe(false);
    });

    it('should handle different line ending styles', async () => {
      // Test with Windows-style line endings
      const codeWindows = 'const line1 = 1;\r\n// cognitive-complexity-ignore-next-line\r\nconst line3 = 3;';
      await parser.parseCode(codeWindows, 'test.ts');
      
      expect(parser.isLineIgnored(3)).toBe(true);
    });

    it('should provide accurate location information', async () => {
      const code = `function first() {
  return 1;
}

function second() {
  return 2;
}`;
      
      const ast = await parser.parseCode(code, 'test.ts');
      
      // Test that we can get locations for nodes
      const mockNode = {
        type: 'FunctionDeclaration',
        span: { start: 0, end: 10 }
      };
      
      const location = parser.getLocation(mockNode);
      expect(location.line).toBe(1);
      expect(location.column).toBe(1);
    });
  });
});