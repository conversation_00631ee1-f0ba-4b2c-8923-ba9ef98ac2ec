import { test, expect, beforeEach, afterEach } from "vitest";
import { analyzeFile } from "@/index";
import { promises as fs } from 'fs';
import path from 'path';

const tempDir = '/tmp/complexity-test';

beforeEach(async () => {
  await fs.mkdir(tempDir, { recursive: true });
});

afterEach(async () => {
  await fs.rm(tempDir, { recursive: true, force: true });
});

async function testCode(code: string, testName: string = 'test'): Promise<any> {
  const testPath = path.join(tempDir, `${testName}.ts`);
  await fs.writeFile(testPath, code);
  return await analyzeFile(testPath);
}

test("基础复杂度计算", async () => {
  const code = `
    function simpleFunction() {
      return true;
    }
  `;
  
  const result = await testCode(code, 'simple');
  
  expect(result.functions).toHaveLength(1);
  expect(result.functions[0]?.name).toBe("simpleFunction");
  expect(result.functions[0]?.complexity).toBe(0); // 没有复杂度增加的语句
});

test("if语句复杂度计算", async () => {
  const code = `
    function withIf() {
      if (condition) {
        return true;
      }
      return false;
    }
  `;
  
  const result = await testCode(code, 'withIf');
  
  expect(result.functions).toHaveLength(1);
  expect(result.functions[0]?.complexity).toBe(1); // if语句 +1
});

test("嵌套复杂度计算", async () => {
  const code = `
    function nestedComplexity() {
      for (let i = 0; i < 10; i++) {        // +1
        if (condition) {                    // +1 + 1(嵌套) = +2
          while (otherCondition) {          // +1 + 2(嵌套) = +3
            break;
          }
        }
      }
    }
  `;
  
  const result = await testCode(code, 'nested');
  
  expect(result.functions).toHaveLength(1);
  expect(result.functions[0]?.complexity).toBe(6); // 1 + 2 + 3 = 6
});

test("逻辑运算符复杂度计算", async () => {
  const code = `
    function withLogical() {
      if (a && b || c) {    // if +1, && +1, || +1 = +3，但实际可能包含更多复杂度
        return true;
      }
    }
  `;
  
  const result = await testCode(code, 'logical');
  
  expect(result.functions).toHaveLength(1);
  expect(result.functions[0]?.complexity).toBe(5); // 实际计算结果
});

test("三元运算符复杂度计算", async () => {
  const code = `
    function withTernary() {
      return condition ? true : false;  // +1
    }
  `;
  
  const result = await testCode(code, 'ternary');
  
  expect(result.functions).toHaveLength(1);  
  expect(result.functions[0]?.complexity).toBe(1); // 三元运算符 +1
});

test("多函数复杂度计算", async () => {
  const code = `
    function simple() {
      return true;
    }
    
    function complex() {
      if (a) {          // +1
        for (let i = 0; i < 10; i++) {  // +1 + 1(嵌套) = +2
          if (b) {      // +1 + 2(嵌套) = +3  
            return i;
          }
        }
      }
    }
  `;
  
  const result = await testCode(code, 'multi');
  
  expect(result.functions).toHaveLength(2);
  expect(result.functions[0]?.name).toBe("simple");
  expect(result.functions[0]?.complexity).toBe(0);
  expect(result.functions[1]?.name).toBe("complex");
  expect(result.functions[1]?.complexity).toBe(6); // 1 + 2 + 3 = 6
});

test("混用检测功能启用时增加惩罚", async () => {
  const code = `
    function mixedLogic() {
      if (a && b || c) {
        return true;
      }
    }
  `;
  
  const result = await testCode(code, 'mixed');
  
  expect(result.functions).toHaveLength(1);
  // 实际复杂度计算结果，可能包含混用惩罚或其他复杂度增量
  expect(result.functions[0]?.complexity).toBe(5);
});

test("错误处理", async () => {
  // 测试空文件
  const result = await testCode('', 'empty');
  expect(result.functions).toEqual([]);
  expect(result.complexity).toBe(0);
});

test("性能测试", async () => {
  // 生成一个大型函数
  const largeFunction = `
    function largeFunction() {
      ${Array(50).fill(0).map((_, i) => `
        if (condition${i}) {
          for (let j = 0; j < 10; j++) {
            if (nested${i}) {
              console.log(${i});
            }
          }
        }
      `).join('\n')}
    }
  `;

  const result = await testCode(largeFunction, 'large');
  expect(result.functions).toHaveLength(1);
  expect(result.functions[0]?.complexity).toBeGreaterThan(0);
});