import { describe, test, expect, beforeEach, afterEach, it } from "vitest";
import {
  ReadonlyDetailStep,
  InternalDetailStep,
  DetailStepFactory,
  DefaultDetailStepFactory,
  ObjectPool,
  FactoryObjectPool,
  ObjectPoolOptions,
  ObjectPoolError,
  ObjectPoolStats,
  ReadonlyFunctionContext,
  InternalFunctionContext,
  DefaultFunctionContextFactory
} from "../../core/object-pool-types";
import {
  TypeSafeDetailStepPool,
  TypeSafeFunctionContextPool,
  ObjectPoolManager,
  DetailStepPool,
  FunctionContextPool
} from "../../core/object-pool";
import { DiagnosticMarker } from "../../core/types";
import {
  expectType,
  TypeValidator,
  TypeSafeTestFactory,
  AdvancedTypeValidator,
  TypeSafeTestHelper
} from "../helpers/type-testing-utils";
import { TestUtils, PerformanceTestUtils } from "../helpers/test-utils";
import { FixtureManager } from "../helpers/fixture-manager";

describe('Enhanced Object Pool Type Safety Tests', () => {
  let factory: DefaultDetailStepFactory;
  let validator: TypeValidator<ReadonlyDetailStep>;

  beforeEach(() => {
    factory = new DefaultDetailStepFactory();
    validator = expectType<ReadonlyDetailStep>('DetailStepValidator');
    // 清理对象池管理器实例
    ObjectPoolManager.resetInstance();
    // 初始化性能测试
    PerformanceTestUtils.initializeBenchmarks();
  });

  afterEach(() => {
    // 清理性能测试数据
    PerformanceTestUtils.cleanup();
  });

  describe('ReadonlyDetailStep Interface Type Safety', () => {
    test('should ensure ReadonlyDetailStep interface is correctly typed', () => {
      const step = factory.create();
      
      // 编译时类型检查
      expectType<ReadonlyDetailStep>().toBeAssignableTo<{
        readonly line: number;
        readonly column: number;
        readonly increment: number;
        readonly cumulative: number;
        readonly ruleId: string;
        readonly description: string;
        readonly nestingLevel: number;
        readonly context: string;
        readonly diagnosticMarker?: DiagnosticMarker;
        readonly diagnosticMessage?: string;
      }>();

      // 运行时类型验证
      validator.toAccept(step);
      
      // 验证所有属性都是只读的
      expect(typeof step.line).toBe('number');
      expect(typeof step.column).toBe('number');
      expect(typeof step.increment).toBe('number');
      expect(typeof step.cumulative).toBe('number');
      expect(typeof step.ruleId).toBe('string');
      expect(typeof step.description).toBe('string');
      expect(typeof step.nestingLevel).toBe('number');
      expect(typeof step.context).toBe('string');
    });

    test('should validate readonly properties cannot be modified', () => {
      const step = factory.create();
      
      // TypeScript 编译时应该阻止这些操作
      // @ts-expect-error - readonly property should not be assignable
      // step.line = 10;
      
      // @ts-expect-error - readonly property should not be assignable  
      // step.ruleId = 'new-rule';
      
      // 运行时验证属性确实是只读的
      expect(() => {
        // 尝试直接修改会在严格模式下抛出错误
        Object.defineProperty(step, 'line', { value: 999 });
      }).not.toThrow(); // Object.defineProperty 可能成功，但值不应该改变
    });

    test('should support optional diagnostic properties', async () => {
      const stepWithDiagnostic: ReadonlyDetailStep = {
        line: 1,
        column: 0,
        increment: 1,
        cumulative: 1,
        ruleId: 'test-rule',
        description: 'Test rule',
        nestingLevel: 0,
        context: 'test context',
        diagnosticMarker: 'warning' as DiagnosticMarker,
        diagnosticMessage: 'Test diagnostic message'
      };

      const result = await AdvancedTypeValidator.validateTypeShape(
        stepWithDiagnostic,
        {
          line: 'number',
          column: 'number',
          increment: 'number',
          cumulative: 'number',
          ruleId: 'string',
          description: 'string',
          nestingLevel: 'number',
          context: 'string',
          diagnosticMarker: (v: any) => typeof v === 'string',
          diagnosticMessage: 'string'
        },
        'ReadonlyDetailStepWithDiagnostic'
      );

      expect(result.passed).toBe(true);
    });
  });

  describe('InternalDetailStep Interface Type Safety', () => {
    test('should ensure InternalDetailStep allows mutations', () => {
      // 创建内部步骤对象
      const internalStep: InternalDetailStep = {
        line: 0,
        column: 0,
        increment: 0,
        cumulative: 0,
        ruleId: '',
        description: '',
        nestingLevel: 0,
        context: ''
      };

      // 验证可以修改属性（编译时检查）
      internalStep.line = 10;
      internalStep.column = 5;
      internalStep.increment = 2;
      internalStep.cumulative = 2;
      internalStep.ruleId = 'test-rule-id';
      internalStep.description = 'Test description';
      internalStep.nestingLevel = 1;
      internalStep.context = 'test context';

      expect(internalStep.line).toBe(10);
      expect(internalStep.column).toBe(5);
      expect(internalStep.increment).toBe(2);
      expect(internalStep.cumulative).toBe(2);
      expect(internalStep.ruleId).toBe('test-rule-id');
      expect(internalStep.description).toBe('Test description');
      expect(internalStep.nestingLevel).toBe(1);
      expect(internalStep.context).toBe('test context');
    });

    test('should be compatible with ReadonlyDetailStep interface', () => {
      const internalStep: InternalDetailStep = {
        line: 1,
        column: 2,
        increment: 3,
        cumulative: 4,
        ruleId: 'rule-1',
        description: 'Description 1',
        nestingLevel: 1,
        context: 'Context 1'
      };

      // InternalDetailStep 应该可以赋值给 ReadonlyDetailStep
      const readonlyStep: ReadonlyDetailStep = internalStep;
      
      expectType<InternalDetailStep>().toBeAssignableTo<ReadonlyDetailStep>();
      
      expect(readonlyStep.line).toBe(1);
      expect(readonlyStep.ruleId).toBe('rule-1');
    });
  });

  describe('DetailStepFactory Type Safety', () => {
    test('should validate factory interface methods', async () => {
      const factoryValidator = expectType<DetailStepFactory>('FactoryValidator');
      
      // 验证工厂接口类型
      expectType<DetailStepFactory>().toBeAssignableTo<{
        create(): ReadonlyDetailStep;
        reset(step: InternalDetailStep): void;
        validate(step: ReadonlyDetailStep): boolean;
      }>();

      factoryValidator.toAccept(factory);
    });

    test('should validate DefaultDetailStepFactory implementation', () => {
      expect(factory).toBeInstanceOf(DefaultDetailStepFactory);
      
      // 测试 create 方法
      const step = factory.create();
      expect(step).toBeDefined();
      expect(typeof step.line).toBe('number');
      expect(step.line).toBe(0);
      
      // 测试 validate 方法
      expect(factory.validate(step)).toBe(true);
      
      // 测试无效对象的验证
      const invalidStep = { line: -1 } as ReadonlyDetailStep;
      expect(factory.validate(invalidStep)).toBe(false);
    });

    test('should validate reset method with proper type constraints', () => {
      const step = factory.create();
      const internalStep = step as InternalDetailStep;
      
      // 修改属性
      internalStep.line = 42;
      internalStep.ruleId = 'modified-rule';
      internalStep.increment = 5;
      
      expect(internalStep.line).toBe(42);
      expect(internalStep.ruleId).toBe('modified-rule');
      expect(internalStep.increment).toBe(5);
      
      // 重置
      factory.reset(internalStep);
      
      expect(internalStep.line).toBe(0);
      expect(internalStep.ruleId).toBe('');
      expect(internalStep.increment).toBe(0);
    });

    test('should handle diagnostic properties in factory operations', () => {
      const step = factory.create();
      const internalStep = step as InternalDetailStep;
      
      // 设置诊断属性
      internalStep.diagnosticMarker = 'error' as DiagnosticMarker;
      internalStep.diagnosticMessage = 'Test error';
      
      expect(factory.validate(step)).toBe(true);
      
      // 重置后诊断属性应该被清除
      factory.reset(internalStep);
      expect(internalStep.diagnosticMarker).toBeUndefined();
      expect(internalStep.diagnosticMessage).toBeUndefined();
    });
  });

  describe('ObjectPool Interface Type Safety', () => {
    test('should validate generic ObjectPool interface', async () => {
      // 验证泛型对象池接口类型结构
      const poolValidator = expectType<ObjectPool<ReadonlyDetailStep>>('ObjectPoolValidator');
      
      expectType<ObjectPool<ReadonlyDetailStep>>().toBeAssignableTo<{
        acquire(): ReadonlyDetailStep;
        release(obj: ReadonlyDetailStep): void;
        size(): number;
        clear(): void;
        getStats(): ObjectPoolStats;
      }>();
    });

    test('should validate ObjectPoolStats interface', async () => {
      const statsExample: ObjectPoolStats = {
        poolSize: 5,
        maxSize: 10,
        totalCreated: 15,
        totalReused: 8,
        reuseRate: '53.3%'
      };

      const result = await AdvancedTypeValidator.validateTypeShape(
        statsExample,
        {
          poolSize: 'number',
          maxSize: 'number',
          totalCreated: 'number',
          totalReused: 'number',
          reuseRate: 'string'
        },
        'ObjectPoolStats'
      );

      expect(result.passed).toBe(true);
    });
  });

  describe('FactoryObjectPool Type Safety', () => {
    test('should validate FactoryObjectPool interface inheritance', () => {
      // 验证 FactoryObjectPool 继承自 ObjectPool
      expectType<FactoryObjectPool<ReadonlyDetailStep, InternalDetailStep>>()
        .toBeAssignableTo<ObjectPool<ReadonlyDetailStep>>();
      
      // 验证额外的方法签名
      expectType<FactoryObjectPool<ReadonlyDetailStep, InternalDetailStep>>()
        .toBeAssignableTo<{
          getFactory(): DetailStepFactory;
          warmUp(count: number): void;
          validatePool(): boolean;
        }>();
    });

    test('should ensure proper generic type constraints', () => {
      // 验证泛型类型约束
      type ValidFactoryPool = FactoryObjectPool<ReadonlyDetailStep, InternalDetailStep>;
      
      // 这些应该编译通过
      expectType<ValidFactoryPool['acquire']>().toBeAssignableTo<() => ReadonlyDetailStep>();
      expectType<ValidFactoryPool['release']>().toBeAssignableTo<(obj: ReadonlyDetailStep) => void>();
    });
  });

  describe('ObjectPoolOptions Type Safety', () => {
    test('should validate configuration options', async () => {
      const validOptions: ObjectPoolOptions = {
        maxSize: 100,
        enableValidation: true,
        warmUpSize: 10,
        factory: new DefaultDetailStepFactory()
      };

      const result = await AdvancedTypeValidator.validateTypeShape(
        validOptions,
        {
          maxSize: 'number',
          enableValidation: 'boolean',
          warmUpSize: 'number',
          factory: (v: any) => v instanceof DefaultDetailStepFactory
        },
        'ObjectPoolOptions'
      );

      expect(result.passed).toBe(true);
    });

    test('should support optional properties', () => {
      // 所有属性都应该是可选的
      const minimalOptions: ObjectPoolOptions = {};
      
      expect(minimalOptions).toBeDefined();
      expect(minimalOptions.maxSize).toBeUndefined();
      expect(minimalOptions.enableValidation).toBeUndefined();
    });
  });

  describe('ObjectPoolError Type Safety', () => {
    test('should validate error class structure', async () => {
      const error = new ObjectPoolError(
        'Test error message',
        'DetailStepPool',
        'acquire',
        { additionalInfo: 'test context' }
      );

      expect(error).toBeInstanceOf(Error);
      expect(error).toBeInstanceOf(ObjectPoolError);
      expect(error.name).toBe('ObjectPoolError');
      expect(error.message).toContain('ObjectPool Error [DetailStepPool:acquire]: Test error message');
      expect(error.poolType).toBe('DetailStepPool');
      expect(error.operation).toBe('acquire');
      expect(error.context).toEqual({ additionalInfo: 'test context' });
    });

    test('should validate error constructor parameters', () => {
      // 验证必需参数
      expect(() => {
        new ObjectPoolError('message', 'poolType', 'operation');
      }).not.toThrow();

      // 验证可选参数
      expect(() => {
        new ObjectPoolError('message', 'poolType', 'operation', { key: 'value' });
      }).not.toThrow();
    });

    test('should maintain Error prototype chain', () => {
      const error = new ObjectPoolError('test', 'pool', 'op');
      
      expect(error instanceof Error).toBe(true);
      expect(error instanceof ObjectPoolError).toBe(true);
      expect(Object.getPrototypeOf(error)).toBe(ObjectPoolError.prototype);
    });
  });

  describe('Type Compatibility and Conversion', () => {
    test('should handle safe type conversions', () => {
      const readonlyStep = factory.create();
      
      // 类型转换应该是安全的（尽管在实际使用中应该避免）
      const internalStep = readonlyStep as InternalDetailStep;
      
      // 验证转换后仍然可以使用
      expect(factory.validate(internalStep)).toBe(true);
      
      // 修改内部步骤不应该影响原始只读步骤的语义
      internalStep.line = 999;
      expect(internalStep.line).toBe(999);
    });

    test('should prevent unsafe operations through type system', () => {
      const step = factory.create();
      
      // 这些操作应该在编译时被阻止
      // @ts-expect-error
      // step.line = 42;
      
      // @ts-expect-error  
      // delete step.ruleId;
      
      // 运行时验证属性仍然存在
      expect(step.line).toBeDefined();
      expect(step.ruleId).toBeDefined();
    });
  });

  describe('Integration with Type Testing Utils', () => {
    test('should work with TypeValidator for runtime checks', async () => {
      const step = factory.create();
      
      validator.toAccept(step);
      
      // 验证类型守卫
      const stepGuard = await TypeSafeTestFactory.generateTypeGuardTests(
        'ReadonlyDetailStep',
        [step],
        [null, undefined, {}, 'string', 123, { line: 'invalid' }]
      );

      const stepValidator = expectType<ReadonlyDetailStep>();
      const guardResult = await stepValidator.validateTypeGuard(stepGuard);
      
      expect(guardResult.passed).toBe(true);
      expect(guardResult.summary.totalTests).toBeGreaterThan(0);
    });

    test('should validate complex type constraints', async () => {
      const step = factory.create();
      
      // 使用高级类型验证器测试结构兼容性
      const compatibilityResult = await AdvancedTypeValidator.validateStructuralCompatibility(
        step,
        (value: unknown): value is ReadonlyDetailStep => {
          return factory.validate(value as ReadonlyDetailStep);
        },
        'DetailStep Structural Compatibility'
      );

      expect(compatibilityResult.passed).toBe(true);
    });

    test('should generate comprehensive validation reports', async () => {
      const steps = [
        factory.create(),
        factory.create(),
        factory.create()
      ];

      // 修改一些步骤以创建多样性
      const internalSteps = steps.map(s => s as InternalDetailStep);
      internalSteps[0].line = 10;
      internalSteps[0].ruleId = 'rule-1';
      internalSteps[1].line = 20;
      internalSteps[1].ruleId = 'rule-2';
      internalSteps[2].line = 30;
      internalSteps[2].ruleId = 'rule-3';

      // 验证所有步骤
      for (const step of steps) {
        expect(factory.validate(step)).toBe(true);
        validator.toAccept(step);
      }

      const summary = validator.getSummary();
      expect(summary.overallSuccess).toBe(true);
      expect(summary.totalTests).toBe(steps.length);
    });
  });

  describe('Performance and Memory Type Safety', () => {
    test('should validate object reuse patterns', () => {
      const pool: ReadonlyDetailStep[] = [];
      
      // 模拟对象池的获取和释放
      for (let i = 0; i < 10; i++) {
        const step = factory.create();
        const internal = step as InternalDetailStep;
        internal.line = i;
        internal.ruleId = `rule-${i}`;
        
        pool.push(step);
      }

      expect(pool).toHaveLength(10);
      
      // 验证所有对象都是有效的
      pool.forEach(step => {
        expect(factory.validate(step)).toBe(true);
      });
      
      // 模拟释放和重置
      pool.forEach(step => {
        factory.reset(step as InternalDetailStep);
        expect(factory.validate(step)).toBe(true);
        expect((step as InternalDetailStep).line).toBe(0);
      });
    });

    test('should handle concurrent access patterns safely', async () => {
      const concurrentOperations: Promise<ReadonlyDetailStep>[] = [];
      
      // 创建多个并发操作
      for (let i = 0; i < 50; i++) {
        concurrentOperations.push(
          Promise.resolve().then(() => {
            const step = factory.create();
            const internal = step as InternalDetailStep;
            internal.line = i;
            internal.ruleId = `concurrent-rule-${i}`;
            return step;
          })
        );
      }

      const results = await Promise.all(concurrentOperations);
      
      expect(results).toHaveLength(50);
      results.forEach((step, index) => {
        expect(factory.validate(step)).toBe(true);
        expect((step as InternalDetailStep).line).toBe(index);
        expect((step as InternalDetailStep).ruleId).toBe(`concurrent-rule-${index}`);
      });
    });
  });
});

// 新增的全面类型安全测试
describe('TypeSafeDetailStepPool - Comprehensive Type Safety', () => {
  let testValidator: TypeValidator<ReadonlyDetailStep>;
  
  beforeEach(() => {
    testValidator = new TypeValidator<ReadonlyDetailStep>('TypeSafeDetailStepPool', { debugMode: false });
    ObjectPoolManager.resetInstance();
  });
  
  afterEach(() => {
    PerformanceTestUtils.cleanup();
  });

  describe('Factory Pattern Type Constraints', () => {
    it('should use factory pattern for object creation', () => {
      const factory = new DefaultDetailStepFactory();
      const pool = new TypeSafeDetailStepPool({ factory });

      expect(pool.getFactory()).toBe(factory);
      expect(pool.getFactory()).toBeInstanceOf(DefaultDetailStepFactory);
    });

    it('should create valid objects through factory', () => {
      const pool = new TypeSafeDetailStepPool();
      const step = pool.acquire();

      // 验证工厂创建的对象符合规范
      expect(typeof step.line).toBe('number');
      expect(typeof step.column).toBe('number');
      expect(typeof step.increment).toBe('number');
      expect(typeof step.cumulative).toBe('number');
      expect(typeof step.ruleId).toBe('string');
      expect(typeof step.description).toBe('string');
      expect(typeof step.nestingLevel).toBe('number');
      expect(typeof step.context).toBe('string');

      // 验证默认值
      expect(step.line).toBe(0);
      expect(step.column).toBe(0);
      expect(step.increment).toBe(0);
      expect(step.cumulative).toBe(0);
      expect(step.ruleId).toBe('');
      expect(step.description).toBe('');
      expect(step.nestingLevel).toBe(0);
      expect(step.context).toBe('');

      pool.release(step);
    });

    it('should properly reset objects using factory', () => {
      const pool = new TypeSafeDetailStepPool();
      const step = pool.acquire();

      // 修改内部状态（模拟使用后的对象）
      const internalStep = step as InternalDetailStep;
      internalStep.line = 42;
      internalStep.column = 10;
      internalStep.increment = 5;
      internalStep.cumulative = 15;
      internalStep.ruleId = 'modified-rule';
      internalStep.description = 'Modified description';
      internalStep.nestingLevel = 3;
      internalStep.context = 'modified context';

      // 释放对象（应该重置状态）
      pool.release(step);

      // 重新获取对象，应该是重置后的状态
      const resetStep = pool.acquire();
      expect(resetStep.line).toBe(0);
      expect(resetStep.column).toBe(0);
      expect(resetStep.increment).toBe(0);
      expect(resetStep.cumulative).toBe(0);
      expect(resetStep.ruleId).toBe('');
      expect(resetStep.description).toBe('');
      expect(resetStep.nestingLevel).toBe(0);
      expect(resetStep.context).toBe('');

      pool.release(resetStep);
    });

    it('should validate factory error handling', () => {
      class FailingFactory extends DefaultDetailStepFactory {
        create(): ReadonlyDetailStep {
          throw new Error('Factory creation failed');
        }
      }

      const pool = new TypeSafeDetailStepPool({ factory: new FailingFactory() });

      expect(() => pool.acquire()).toThrow(ObjectPoolError);
      expect(() => pool.acquire()).toThrow(/Factory failed to create new step/);
    });
  });

  describe('Memory Management and Reuse', () => {
    it('should reuse objects efficiently', () => {
      const pool = new TypeSafeDetailStepPool({ maxSize: 5 });
      const steps: ReadonlyDetailStep[] = [];

      // 获取多个对象
      for (let i = 0; i < 3; i++) {
        steps.push(pool.acquire());
      }

      const statsAfterAcquire = pool.getStats();
      expect(statsAfterAcquire.totalCreated).toBe(3);
      expect(statsAfterAcquire.totalReused).toBe(0);
      expect(pool.size()).toBe(0);

      // 释放对象
      steps.forEach(step => pool.release(step));

      const statsAfterRelease = pool.getStats();
      expect(statsAfterRelease.totalCreated).toBe(3);
      expect(statsAfterRelease.totalReused).toBe(0);
      expect(pool.size()).toBe(3);

      // 重新获取对象，应该重用现有对象
      const reusedSteps: ReadonlyDetailStep[] = [];
      for (let i = 0; i < 3; i++) {
        reusedSteps.push(pool.acquire());
      }

      const statsAfterReuse = pool.getStats();
      expect(statsAfterReuse.totalCreated).toBe(3);
      expect(statsAfterReuse.totalReused).toBe(3);
      expect(statsAfterReuse.reuseRate).toBe('50.0%');
      expect(pool.size()).toBe(0);

      // 清理
      reusedSteps.forEach(step => pool.release(step));
    });

    it('should respect maximum pool size', () => {
      const maxSize = 2;
      const pool = new TypeSafeDetailStepPool({ maxSize });
      const steps: ReadonlyDetailStep[] = [];

      // 获取超过最大容量的对象
      for (let i = 0; i < 5; i++) {
        steps.push(pool.acquire());
      }

      // 释放所有对象
      steps.forEach(step => pool.release(step));

      // 池大小不应超过最大值
      expect(pool.size()).toBe(maxSize);
      expect(pool.getStats().poolSize).toBe(maxSize);
    });

    it('should warm up pool correctly', () => {
      const warmUpSize = 10;
      const pool = new TypeSafeDetailStepPool({ warmUpSize });

      expect(pool.size()).toBe(warmUpSize);

      // 预热后获取对象应该重用预创建的对象
      const step = pool.acquire();
      const stats = pool.getStats();
      expect(stats.totalReused).toBe(1);
      expect(stats.totalCreated).toBe(warmUpSize);

      pool.release(step);
    });
  });

  describe('Type Guard Validation', () => {
    it('should validate type guard functions', async () => {
      // 创建类型守卫测试数据
      const typeGuardTest = await TypeSafeTestFactory.generateTypeGuardTests<ReadonlyDetailStep>(
        'ReadonlyDetailStep',
        [
          // 有效的 ReadonlyDetailStep 对象
          {
            line: 1, column: 0, increment: 1, cumulative: 1,
            ruleId: 'test-rule', description: 'Test rule', 
            nestingLevel: 0, context: 'test'
          } as ReadonlyDetailStep,
          {
            line: 10, column: 5, increment: 2, cumulative: 3,
            ruleId: 'complex-rule', description: 'Complex rule',
            nestingLevel: 1, context: 'nested'
          } as ReadonlyDetailStep
        ],
        [
          // 无效的对象
          null,
          undefined,
          {},
          { line: 'invalid' },
          { line: 1 }, // 缺少必需属性
          { line: -1, column: 0, increment: 1, cumulative: 1, ruleId: '', description: '', nestingLevel: 0, context: '' }, // 无效值
          'string',
          123,
          []
        ]
      );

      const result = await testValidator.validateTypeGuard(typeGuardTest);
      expect(result.passed).toBe(true);
      expect(result.summary.coverage).toBeGreaterThan(95);
    });

    it('should validate object structure', async () => {
      const pool = new TypeSafeDetailStepPool();
      const step = pool.acquire();

      // 验证对象结构
      await testValidator.toMatchStructure(
        step,
        {
          line: 'number',
          column: 'number',
          increment: 'number',
          cumulative: 'number',
          ruleId: 'string',
          description: 'string',
          nestingLevel: 'number',
          context: 'string'
        },
        'ReadonlyDetailStep structure validation'
      );

      pool.release(step);
    });
  });

  describe('Error Handling', () => {
    it('should validate objects when validation is enabled', () => {
      const pool = new TypeSafeDetailStepPool({ enableValidation: true });
      const step = pool.acquire();

      // 正常对象应该通过验证 
      expect(() => pool.release(step)).not.toThrow();
    });

    it('should detect invalid objects during validation', () => {
      class InvalidatingFactory extends DefaultDetailStepFactory {
        reset(step: InternalDetailStep): void {
          super.reset(step);
          // 创建无效状态
          (step as any).line = 'invalid';
        }
      }

      const pool = new TypeSafeDetailStepPool({ 
        factory: new InvalidatingFactory(),
        enableValidation: true
      });
      const step = pool.acquire();

      expect(() => pool.release(step)).toThrow(ObjectPoolError);
    });

    it('should throw typed errors with proper context', () => {
      class FailingFactory extends DefaultDetailStepFactory {
        reset(step: InternalDetailStep): void {
          throw new Error('Reset failed');
        }
      }

      const pool = new TypeSafeDetailStepPool({ factory: new FailingFactory() });
      const step = pool.acquire();

      expect(() => pool.release(step)).toThrow(ObjectPoolError);
      
      try {
        pool.release(step);
      } catch (error) {
        expect(error).toBeInstanceOf(ObjectPoolError);
        if (error instanceof ObjectPoolError) {
          expect(error.poolType).toBe('DetailStepPool');
          expect(error.operation).toBe('release');
          expect(error.context).toBeDefined();
        }
      }
    });
  });
});

describe('TypeSafeFunctionContextPool - Type Safety', () => {
  beforeEach(() => {
    ObjectPoolManager.resetInstance();
  });

  describe('Function Context Type Constraints', () => {
    it('should enforce function context type constraints', () => {
      expectType<ReadonlyFunctionContext>().toBeAssignableTo<{
        readonly name: string;
        readonly line: number;
        readonly column: number;
        readonly complexity: number;
        readonly steps: readonly ReadonlyDetailStep[];
        readonly nestingLevel: number;
      }>();
    });

    it('should manage function contexts correctly', () => {
      const pool = new TypeSafeFunctionContextPool();
      const context = pool.acquire();

      expect(typeof context.name).toBe('string');
      expect(typeof context.line).toBe('number');
      expect(typeof context.column).toBe('number');
      expect(typeof context.complexity).toBe('number');
      expect(Array.isArray(context.steps)).toBe(true);
      expect(typeof context.nestingLevel).toBe('number');

      // 验证默认值
      expect(context.name).toBe('');
      expect(context.line).toBe(0);
      expect(context.column).toBe(0);
      expect(context.complexity).toBe(0);
      expect(context.steps).toHaveLength(0);
      expect(context.nestingLevel).toBe(0);

      pool.release(context);
    });

    it('should reset function context properly', () => {
      const pool = new TypeSafeFunctionContextPool();
      const context = pool.acquire();

      // 修改内部状态
      const internalContext = context as InternalFunctionContext;
      internalContext.name = 'testFunction';
      internalContext.line = 10;
      internalContext.complexity = 5;
      internalContext.steps.push({} as any);

      pool.release(context);

      const resetContext = pool.acquire();
      expect(resetContext.name).toBe('');
      expect(resetContext.line).toBe(0);
      expect(resetContext.complexity).toBe(0);
      expect(resetContext.steps).toHaveLength(0);

      pool.release(resetContext);
    });
  });
});

describe('ObjectPoolManager - Integration Tests', () => {
  beforeEach(() => {
    ObjectPoolManager.resetInstance();
  });

  describe('Type Safe Pool Management', () => {
    it('should manage multiple pools with type safety', () => {
      const manager = ObjectPoolManager.getInstance();
      
      const detailStepPool = manager.getTypeSafeDetailStepPool();
      const functionContextPool = manager.getTypeSafeFunctionContextPool();

      expect(detailStepPool).toBeInstanceOf(TypeSafeDetailStepPool);
      expect(functionContextPool).toBeInstanceOf(TypeSafeFunctionContextPool);

      // 验证对象类型
      const step = detailStepPool.acquire();
      const context = functionContextPool.acquire();

      expectType<ReadonlyDetailStep>().toAccept(step);
      expectType<ReadonlyFunctionContext>().toAccept(context);

      detailStepPool.release(step);
      functionContextPool.release(context);
    });

    it('should provide backward compatibility', () => {
      const manager = ObjectPoolManager.getInstance();
      
      const legacyDetailPool = manager.getDetailStepPool();
      const legacyContextPool = manager.getFunctionContextPool();

      expect(legacyDetailPool).toBeInstanceOf(DetailStepPool);
      expect(legacyContextPool).toBeInstanceOf(FunctionContextPool);

      // 验证向后兼容性
      const legacyStep = legacyDetailPool.acquire();
      const legacyContext = legacyContextPool.acquire();

      expect(legacyStep).toBeDefined();
      expect(legacyContext).toBeDefined();

      legacyDetailPool.release(legacyStep);
      legacyContextPool.release(legacyContext);
    });

    it('should validate all pools', () => {
      const manager = ObjectPoolManager.getInstance();
      manager.warmUpAllPools();

      const allStats = manager.getAllStats();
      expect(allStats.detailStepPool.poolSize).toBeGreaterThan(0);
      expect(allStats.functionContextPool.poolSize).toBeGreaterThan(0);
      expect(allStats.poolValidation.detailStepPoolValid).toBe(true);
      expect(allStats.poolValidation.functionContextPoolValid).toBe(true);

      expect(manager.validateAllPools()).toBe(true);
    });
  });
});

describe('Performance and Integration Tests', () => {
  beforeEach(() => {
    PerformanceTestUtils.initializeBenchmarks();
  });

  afterEach(() => {
    PerformanceTestUtils.cleanup();
  });

  describe('Performance Benchmarks', () => {
    it('should meet performance benchmarks', async () => {
      const benchmarkConfig = {
        maxExecutionTime: 100,
        maxMemoryUsage: 10 * 1024 * 1024, // 10MB
        maxCpuUsage: 80
      };

      PerformanceTestUtils.addBenchmark('object-pool-test', {
        name: 'Object Pool Performance Test',
        ...benchmarkConfig,
        description: 'Object pool acquire/release performance'
      });

      // 模拟性能测试
      const { duration } = await TestUtils.withTempDir(async () => {
        return PerformanceTestUtils.measure(() => {
          const pool = new TypeSafeDetailStepPool({ maxSize: 1000 });
          const steps: ReadonlyDetailStep[] = [];

          // 执行大量获取/释放操作
          for (let i = 0; i < 500; i++) {
            steps.push(pool.acquire());
          }

          steps.forEach(step => pool.release(step));

          // 重复操作测试重用效率
          for (let i = 0; i < 500; i++) {
            const step = pool.acquire();
            pool.release(step);
          }
        });
      });

      expect(duration).toBeLessThan(benchmarkConfig.maxExecutionTime);
    });

    it('should handle concurrent access safely', async () => {
      const pool = new TypeSafeDetailStepPool({ maxSize: 100 });
      const concurrentOperations = 10;
      const operationsPerThread = 50;

      const promises = Array.from({ length: concurrentOperations }, async () => {
        const steps: ReadonlyDetailStep[] = [];
        
        // 并发获取对象
        for (let i = 0; i < operationsPerThread; i++) {
          steps.push(pool.acquire());
        }
        
        // 随机延迟
        await TestUtils.wait(Math.random() * 10);
        
        // 并发释放对象
        steps.forEach(step => pool.release(step));
      });

      await Promise.all(promises);

      // 验证池状态一致性
      const stats = pool.getStats();
      expect(stats.totalCreated + stats.totalReused).toBe(concurrentOperations * operationsPerThread);
      expect(pool.validatePool()).toBe(true);
    });
  });

  describe('Integration with Test Infrastructure', () => {
    it('should integrate with fixture manager', async () => {
      // 使用固件管理器创建测试数据
      const fixture = FixtureManager.generateDynamicFixture('object-pool-test', {
        fileCount: 3,
        complexityRange: [5, 15],
        category: 'unit'
      });

      expect(fixture.name).toBe('object-pool-test');
      expect(fixture.files).toHaveLength(4); // 3 code files + 1 package.json

      // 创建对象池并使用固件数据进行测试
      const pool = new TypeSafeDetailStepPool();
      const steps: ReadonlyDetailStep[] = [];

      for (let i = 0; i < fixture.files.length; i++) {
        const step = pool.acquire();
        const internalStep = step as InternalDetailStep;
        internalStep.line = i + 1;
        internalStep.ruleId = `fixture-rule-${i}`;
        steps.push(step);
      }

      steps.forEach(step => pool.release(step));
      
      const stats = pool.getStats();
      expect(stats.totalCreated).toBe(fixture.files.length);
    });

    it('should support comprehensive result validation', async () => {
      const pool = new TypeSafeDetailStepPool();
      const mockResult = TestUtils.createMockAnalysisResult();

      // 使用类型安全测试助手验证结果
      const validation = await TypeSafeTestHelper.validateComplexityResults(
        mockResult.results,
        'FunctionResult[]'
      );

      expect(validation.passed).toBe(true);
      expect(validation.matches).toContain(`Array of ${mockResult.results.length} FunctionResults`);
    });
  });
});