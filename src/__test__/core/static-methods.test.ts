/**
 * ComplexityCalculator 静态方法测试
 * 验证新的静态analyze方法和资源管理
 */

import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { ComplexityCalculator } from '../../core/calculator';

describe('ComplexityCalculator Static Methods', () => {
  describe('静态 analyze 方法', () => {
    test('应该分析简单代码并返回结果', async () => {
      const code = `
        function simple() {
          return 42;
        }
        
        function withCondition(x) {
          if (x > 0) {
            return x * 2;
          }
          return 0;
        }
      `;

      const results = await ComplexityCalculator.analyze(code);
      
      expect(results).toHaveLength(2);
      expect(results[0].name).toBe('simple');
      expect(results[0].complexity).toBe(0);
      expect(results[1].name).toBe('withCondition');
      expect(results[1].complexity).toBe(1); // if语句增加1复杂度
    });

    test('应该处理空代码', async () => {
      const results = await ComplexityCalculator.analyze('');
      expect(results).toHaveLength(0);
    });

    test('应该处理语法错误代码', async () => {
      const results = await ComplexityCalculator.analyze('function invalid() { if ( }');
      expect(results).toHaveLength(0); // 语法错误时返回空结果
    });

    test('应该支持配置选项', async () => {
      const code = `
        function test() {
          if (true && false) {
            console.log('mixed operators');
          }
        }
      `;

      const results = await ComplexityCalculator.analyze(code, {
        enableMixedLogicOperatorPenalty: true
      });
      
      expect(results).toHaveLength(1);
      expect(results[0].complexity).toBeGreaterThanOrEqual(1);
    });
  });

  describe('静态 analyzeFile 方法', () => {
    test('应该处理不存在的文件', async () => {
      const results = await ComplexityCalculator.analyzeFile('/nonexistent/file.ts');
      expect(results).toHaveLength(0);
    });
  });

  describe('静态 analyzeFiles 方法', () => {
    test('应该处理空文件列表', async () => {
      const results = await ComplexityCalculator.analyzeFiles([]);
      expect(results.size).toBe(0);
    });

    test('应该处理包含不存在文件的列表', async () => {
      const results = await ComplexityCalculator.analyzeFiles([
        '/nonexistent/file1.ts',
        '/nonexistent/file2.ts'
      ]);
      
      expect(results.size).toBe(2);
      expect(results.get('/nonexistent/file1.ts')).toEqual([]);
      expect(results.get('/nonexistent/file2.ts')).toEqual([]);
    });
  });

  describe('静态 quickAnalyze 方法', () => {
    test('应该返回复杂度概览', async () => {
      const code = `
        function simple() { return 42; }
        function complex() {
          if (x > 0) {
            while (x > 0) {
              if (x % 2 === 0) {
                console.log(x);
              }
              x--;
            }
          }
        }
        function veryComplex() {
          for (let i = 0; i < 10; i++) {
            if (i % 2 === 0) {
              while (i > 0) {
                if (i % 3 === 0) {
                  break;
                }
                i--;
              }
            }
          }
        }
      `;

      const overview = await ComplexityCalculator.quickAnalyze(code);
      
      expect(overview.functionCount).toBe(3);
      expect(overview.totalComplexity).toBeGreaterThan(0);
      expect(overview.averageComplexity).toBeGreaterThan(0);
      expect(overview.maxComplexity).toBeGreaterThan(overview.averageComplexity);
      expect(overview.complexFunctions.length).toBeGreaterThan(0);
      
      // 检查复杂函数按复杂度降序排列
      for (let i = 1; i < overview.complexFunctions.length; i++) {
        expect(overview.complexFunctions[i-1].complexity).toBeGreaterThanOrEqual(
          overview.complexFunctions[i].complexity
        );
      }
    });

    test('应该处理无复杂函数的情况', async () => {
      const code = `
        function simple1() { return 1; }
        function simple2() { return 2; }
      `;

      const overview = await ComplexityCalculator.quickAnalyze(code);
      
      expect(overview.functionCount).toBe(2);
      expect(overview.totalComplexity).toBe(0);
      expect(overview.averageComplexity).toBe(0);
      expect(overview.maxComplexity).toBe(0);
      expect(overview.complexFunctions).toHaveLength(0);
    });

    test('应该处理错误代码', async () => {
      const overview = await ComplexityCalculator.quickAnalyze('invalid syntax');
      
      expect(overview.functionCount).toBe(0);
      expect(overview.totalComplexity).toBe(0);
      expect(overview.averageComplexity).toBe(0);
      expect(overview.maxComplexity).toBe(0);
      expect(overview.complexFunctions).toHaveLength(0);
    });
  });

  describe('资源管理', () => {
    test('静态方法应该不会泄漏资源', async () => {
      const initialHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      
      // 连续调用多次静态方法
      await ComplexityCalculator.analyze('function test() { return 1; }');
      await ComplexityCalculator.quickAnalyze('function test() { return 2; }');
      await ComplexityCalculator.analyzeFiles([]);
      
      const finalHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
      
      // 静态方法应该自动清理资源，不产生句柄泄漏
      expect(finalHandles).toBe(initialHandles);
    });
  });

  describe('性能特征', () => {
    test('静态方法应该有合理的性能开销', async () => {
      const code = 'function test() { return 42; }';
      const iterations = 10;
      
      const startTime = performance.now();
      
      for (let i = 0; i < iterations; i++) {
        await ComplexityCalculator.analyze(code);
      }
      
      const endTime = performance.now();
      const averageTime = (endTime - startTime) / iterations;
      
      // 平均每次分析应该在合理时间内完成（小于100ms）
      expect(averageTime).toBeLessThan(100);
    });

    test('quickAnalyze 应该比 analyze 更快', async () => {
      const code = `
        function complex() {
          for (let i = 0; i < 10; i++) {
            if (i % 2 === 0) {
              while (i > 0) {
                if (i % 3 === 0) {
                  break;
                }
                i--;
              }
            }
          }
        }
      `;

      const iterations = 5;

      // 测试 analyze 方法
      const analyzeStart = performance.now();
      for (let i = 0; i < iterations; i++) {
        await ComplexityCalculator.analyze(code);
      }
      const analyzeTime = performance.now() - analyzeStart;

      // 测试 quickAnalyze 方法
      const quickStart = performance.now();
      for (let i = 0; i < iterations; i++) {
        await ComplexityCalculator.quickAnalyze(code);
      }
      const quickTime = performance.now() - quickStart;

      // quickAnalyze 应该不会比 analyze 慢很多（因为它内部调用analyze）
      // 但由于它有额外的统计计算，可能稍慢一些，这是可以接受的
      expect(quickTime).toBeLessThan(analyzeTime * 2);
    });
  });
});