import { test, expect, describe } from "vitest";
import { ComplexityCalculator } from "../../core/calculator";
import { basicCases } from "../fixtures/cases/basic";
import { logicalCases } from "../fixtures/cases/logical";
import { nestedCases } from "../fixtures/cases/nested";
import { edgeCases } from "../fixtures/cases/edge-cases";

describe("ComplexityCalculator - 基础功能", () => {
  const calculator = new ComplexityCalculator();
  
  basicCases.forEach(testCase => {
    test(testCase.name, async () => {
      const results = await calculator.calculateCode(testCase.code, "test.ts");
      
      expect(results).toHaveLength(testCase.expected.length);
      
      testCase.expected.forEach((expected, index) => {
        expect(results[index]?.name).toBe(expected.name);
        expect(results[index]?.complexity).toBe(expected.complexity);
      });
    });
  });
});

describe("ComplexityCalculator - 逻辑运算符", () => {
  const calculator = new ComplexityCalculator();
  
  logicalCases.forEach(testCase => {
    test(testCase.name, async () => {
      const results = await calculator.calculateCode(testCase.code, "test.ts");
      
      expect(results).toHaveLength(testCase.expected.length);
      
      testCase.expected.forEach((expected, index) => {
        expect(results[index]?.name).toBe(expected.name);
        expect(results[index]?.complexity).toBe(expected.complexity);
      });
    });
  });
});

describe("ComplexityCalculator - 嵌套复杂度", () => {
  const calculator = new ComplexityCalculator();
  
  nestedCases.forEach(testCase => {
    test(testCase.name, async () => {
      const results = await calculator.calculateCode(testCase.code, "test.ts");
      
      expect(results).toHaveLength(testCase.expected.length);
      
      testCase.expected.forEach((expected, index) => {
        expect(results[index]?.name).toBe(expected.name);
        expect(results[index]?.complexity).toBe(expected.complexity);
      });
    });
  });
});

describe("ComplexityCalculator - 边界情况", () => {
  const calculator = new ComplexityCalculator();
  
  edgeCases.forEach(testCase => {
    test(testCase.name, async () => {
      const results = await calculator.calculateCode(testCase.code, "test.ts");
      
      expect(results).toHaveLength(testCase.expected.length);
      
      testCase.expected.forEach((expected, index) => {
        expect(results[index]?.name).toBe(expected.name);
        expect(results[index]?.complexity).toBe(expected.complexity);
      });
    });
  });
});

describe("ComplexityCalculator - 错误处理", () => {
  const calculator = new ComplexityCalculator();
  
  test("应该处理语法错误的代码", async () => {
    const invalidCode = `
      function broken() {
        if (condition {  // 缺少右括号
          return true;
        }
      }
    `;
    
    await expect(calculator.calculateCode(invalidCode, "test.ts")).rejects.toThrow();
  });
  
  test("应该处理空代码", async () => {
    const results = await calculator.calculateCode("", "test.ts");
    expect(results).toHaveLength(0);
  });
  
  test("应该处理只有注释的代码", async () => {
    const codeWithComments = `
      // 这是注释
      /* 这也是注释 */
    `;
    
    const results = await calculator.calculateCode(codeWithComments, "test.ts");
    expect(results).toHaveLength(0);
  });
  
  test("应该处理没有函数的代码", async () => {
    const codeWithoutFunctions = `
      const x = 1;
      const y = 2;
      console.log(x + y);
    `;
    
    const results = await calculator.calculateCode(codeWithoutFunctions, "test.ts");
    expect(results).toHaveLength(0);
  });
});

describe("ComplexityCalculator - 选项配置", () => {
  test("应该使用默认配置", () => {
    const calculator = new ComplexityCalculator();
    expect(calculator).toBeDefined();
  });
  
  test("应该接受自定义配置", () => {
    const calculator = new ComplexityCalculator({
      enableMixedLogicOperatorPenalty: true,
      recursionChainThreshold: 5
    });
    expect(calculator).toBeDefined();
  });
});

describe("ComplexityCalculator - 文件分析", () => {
  const calculator = new ComplexityCalculator();
  
  test("应该正确计算文件路径和行号", async () => {
    const code = `
function test() {
  if (condition) {
    return true;
  }
}
    `;
    
    const results = await calculator.calculateCode(code, "sample.ts");
    
    expect(results).toHaveLength(1);
    expect(results[0]?.filePath).toBe("sample.ts");
    expect(results[0]?.line).toBeGreaterThan(0);
    expect(results[0]?.column).toBeGreaterThanOrEqual(0);
  });
});