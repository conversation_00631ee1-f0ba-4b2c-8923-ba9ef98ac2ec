import { test, expect, describe, beforeEach } from "vitest";
import { BaseVisitor } from "../../core/base-visitor";
import { ASTParser } from "../../core/parser";
import type { Node, Module } from '@swc/core';

/**
 * 具体的 BaseVisitor 实现，用于测试
 */
class TestVisitor extends BaseVisitor {
  public visitedNodes: Node[] = [];
  public visitedNodeTypes: string[] = [];
  public parentStackSnapshots: Node[][] = [];

  protected visitNode(node: Node): Node {
    this.visitedNodes.push(node);
    this.visitedNodeTypes.push(node.type);
    // 记录当前父节点栈的快照（深拷贝）
    this.parentStackSnapshots.push([...this.getParentPath()]);
    return node;
  }

  // 暴露受保护的方法以供测试
  public getParentPublic(): Node | undefined {
    return this.getParent();
  }

  public getGrandParentPublic(): Node | undefined {
    return this.getGrandParent();
  }

  public getParentPathPublic(): readonly Node[] {
    return this.getParentPath();
  }

  public hasAncestorOfTypePublic(nodeType: string): boolean {
    return this.hasAncestorOfType(nodeType);
  }

  public findNearestAncestorOfTypePublic(nodeType: string): Node | undefined {
    return this.findNearestAncestorOfType(nodeType);
  }

  public getDepthPublic(): number {
    return this.getDepth();
  }

  public resetPublic(): void {
    this.reset();
  }
}

describe("BaseVisitor", () => {
  let visitor: TestVisitor;
  let parser: ASTParser;

  beforeEach(() => {
    visitor = new TestVisitor();
    parser = new ASTParser();
  });

  describe("父节点栈管理", () => {
    test("应该正确维护父节点栈的推入和弹出", async () => {
      const code = `
        function outer() {
          if (condition) {
            return true;
          }
        }
      `;

      const ast = await parser.parseCode(code, "test.ts");
      visitor.visit(ast);

      // 验证访问了正确数量的节点
      expect(visitor.visitedNodes.length).toBeGreaterThan(0);
      // 验证每个节点访问时都有对应的父节点栈快照
      expect(visitor.parentStackSnapshots.length).toBe(visitor.visitedNodes.length);
    });

    test("应该在访问根节点时父节点栈只包含根节点自己", async () => {
      const code = `const x = 1;`;
      const ast = await parser.parseCode(code, "test.ts");
      
      visitor.visit(ast);
      
      // 第一个访问的节点（根节点）时，栈中应该只有根节点自己
      const firstSnapshot = visitor.parentStackSnapshots[0];
      expect(firstSnapshot).toHaveLength(0); // getParentPath() 排除当前节点
    });

    test("应该正确维护嵌套节点的父节点关系", async () => {
      const code = `
        function test() {
          if (true) {
            while (condition) {
              break;
            }
          }
        }
      `;

      const ast = await parser.parseCode(code, "test.ts");
      visitor.visit(ast);

      // 查找 while 语句的访问记录
      const whileIndex = visitor.visitedNodeTypes.findIndex(type => type === 'WhileStatement');
      if (whileIndex !== -1) {
        const whileNodeSnapshot = visitor.parentStackSnapshots[whileIndex];
        // while 语句应该有多个父节点（至少包含 if 语句和函数）
        expect(whileNodeSnapshot.length).toBeGreaterThan(1);
        
        // 检查父节点类型是否合理
        const parentTypes = whileNodeSnapshot.map(node => node.type);
        expect(parentTypes).toContain('IfStatement');
      }
    });
  });

  describe("getParent() 方法", () => {
    test("应该返回父节点栈中的前一个节点", async () => {
      const code = `if (true) { const x = 1; }`;
      const ast = await parser.parseCode(code, "test.ts");

      let moduleParent: Node | undefined;
      let ifStatementParent: Node | undefined;

      // 创建专门用于捕获父节点的访问器
      class ParentCapturingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'Module') {
            moduleParent = this.getParent();
          } else if (node.type === 'IfStatement') {
            ifStatementParent = this.getParent();
          }
          return node;
        }
      }

      const capturingVisitor = new ParentCapturingVisitor();
      capturingVisitor.visit(ast);

      // Module 节点在访问时是栈中的第一个，所以没有父节点
      expect(moduleParent).toBeUndefined();
      // IfStatement 的父节点应该是 Module
      expect(ifStatementParent).toBeDefined();
      expect(ifStatementParent?.type).toBe('Module');
    });

    test("应该在根节点访问时返回 undefined", async () => {
      const code = `const x = 1;`;
      const ast = await parser.parseCode(code, "test.ts");

      let rootParent: Node | undefined;

      class RootParentCapturingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'Module') {
            rootParent = this.getParent();
          }
          return node;
        }
      }

      const capturingVisitor = new RootParentCapturingVisitor();
      capturingVisitor.visit(ast);

      expect(rootParent).toBeUndefined();
    });
  });

  describe("getGrandParent() 方法", () => {
    test("应该返回祖父节点", async () => {
      const code = `
        function test() {
          if (true) {
            const x = 1;
          }
        }
      `;
      const ast = await parser.parseCode(code, "test.ts");

      let variableDeclaratorGrandParent: Node | undefined;

      class GrandParentCapturingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'VariableDeclarator') {
            variableDeclaratorGrandParent = this.getGrandParent();
          }
          return node;
        }
      }

      const capturingVisitor = new GrandParentCapturingVisitor();
      capturingVisitor.visit(ast);

      expect(variableDeclaratorGrandParent).toBeDefined();
      // 变量声明的祖父节点应该是 if 语句或块语句
      const grandParentType = variableDeclaratorGrandParent?.type;
      expect(['IfStatement', 'BlockStatement', 'VariableDeclaration']).toContain(grandParentType);
    });

    test("应该在层级不足时返回 undefined", async () => {
      const code = `const x = 1;`;
      const ast = await parser.parseCode(code, "test.ts");

      let rootGrandParent: Node | undefined;

      class GrandParentCapturingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'Module') {
            rootGrandParent = this.getGrandParent();
          }
          return node;
        }
      }

      const capturingVisitor = new GrandParentCapturingVisitor();
      capturingVisitor.visit(ast);

      expect(rootGrandParent).toBeUndefined();
    });
  });

  describe("getParentPath() 方法", () => {
    test("应该返回完整的父节点路径", async () => {
      const code = `
        function test() {
          if (true) {
            while (condition) {
              break;
            }
          }
        }
      `;
      const ast = await parser.parseCode(code, "test.ts");

      let breakStatementPath: readonly Node[] = [];

      class PathCapturingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'BreakStatement') {
            breakStatementPath = this.getParentPath();
          }
          return node;
        }
      }

      const capturingVisitor = new PathCapturingVisitor();
      capturingVisitor.visit(ast);

      expect(breakStatementPath.length).toBeGreaterThan(2);
      
      // 验证路径中包含预期的节点类型
      const pathTypes = breakStatementPath.map(node => node.type);
      expect(pathTypes).toContain('Module');
      expect(pathTypes).toContain('WhileStatement');
    });

    test("应该返回数组的副本而不是原始引用", async () => {
      const code = `if (true) { const x = 1; }`;
      const ast = await parser.parseCode(code, "test.ts");

      let path1: readonly Node[] = [];
      let path2: readonly Node[] = [];

      class PathCapturingVisitor extends BaseVisitor {
        private captureCount = 0;

        protected visitNode(node: Node): Node {
          if (node.type === 'VariableDeclarator') {
            if (this.captureCount === 0) {
              path1 = this.getParentPath();
              this.captureCount++;
            } else {
              path2 = this.getParentPath();
            }
          }
          return node;
        }
      }

      const capturingVisitor = new PathCapturingVisitor();
      capturingVisitor.visit(ast);

      // 即使是同一个访问器的多次调用，返回的也应该是不同的数组实例
      expect(path1).not.toBe(path2);
      if (path1.length > 0 && path2.length > 0) {
        expect(path1).toEqual(path2); // 内容相同
      }
    });
  });

  describe("hasAncestorOfType() 方法", () => {
    test("应该正确检测指定类型的祖先节点", async () => {
      const code = `
        function test() {
          if (true) {
            while (condition) {
              const x = 1;
            }
          }
        }
      `;
      const ast = await parser.parseCode(code, "test.ts");

      let hasIfAncestor = false;
      let hasWhileAncestor = false;
      let hasFunctionAncestor = false;

      class AncestorCheckingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'VariableDeclarator') {
            hasIfAncestor = this.hasAncestorOfType('IfStatement');
            hasWhileAncestor = this.hasAncestorOfType('WhileStatement');
            hasFunctionAncestor = this.hasAncestorOfType('FunctionDeclaration');
          }
          return node;
        }
      }

      const checkingVisitor = new AncestorCheckingVisitor();
      checkingVisitor.visit(ast);

      expect(hasIfAncestor).toBe(true);
      expect(hasWhileAncestor).toBe(true);
      expect(hasFunctionAncestor).toBe(true);
    });

    test("应该在没有指定类型祖先时返回 false", async () => {
      const code = `const x = 1;`;
      const ast = await parser.parseCode(code, "test.ts");

      let hasIfAncestor = false;

      class AncestorCheckingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'VariableDeclarator') {
            hasIfAncestor = this.hasAncestorOfType('IfStatement');
          }
          return node;
        }
      }

      const checkingVisitor = new AncestorCheckingVisitor();
      checkingVisitor.visit(ast);

      expect(hasIfAncestor).toBe(false);
    });
  });

  describe("findNearestAncestorOfType() 方法", () => {
    test("应该返回最近的指定类型祖先节点", async () => {
      const code = `
        if (outer) {
          if (inner) {
            const x = 1;
          }
        }
      `;
      const ast = await parser.parseCode(code, "test.ts");

      let nearestIfAncestor: Node | undefined;

      class NearestAncestorCapturingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'VariableDeclarator') {
            nearestIfAncestor = this.findNearestAncestorOfType('IfStatement');
          }
          return node;
        }
      }

      const capturingVisitor = new NearestAncestorCapturingVisitor();
      capturingVisitor.visit(ast);

      expect(nearestIfAncestor).toBeDefined();
      expect(nearestIfAncestor?.type).toBe('IfStatement');
      
      // 检查这确实是内层的 if 语句（通过检查测试条件）
      // 注意：这个测试依赖于 AST 结构的细节，可能需要根据实际情况调整
    });

    test("应该在没有指定类型祖先时返回 undefined", async () => {
      const code = `const x = 1;`;
      const ast = await parser.parseCode(code, "test.ts");

      let nearestWhileAncestor: Node | undefined;

      class NearestAncestorCapturingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          if (node.type === 'VariableDeclarator') {
            nearestWhileAncestor = this.findNearestAncestorOfType('WhileStatement');
          }
          return node;
        }
      }

      const capturingVisitor = new NearestAncestorCapturingVisitor();
      capturingVisitor.visit(ast);

      expect(nearestWhileAncestor).toBeUndefined();
    });
  });

  describe("getDepth() 方法", () => {
    test("应该正确返回当前访问深度", async () => {
      const code = `
        function test() {
          if (true) {
            while (condition) {
              const x = 1;
            }
          }
        }
      `;
      const ast = await parser.parseCode(code, "test.ts");

      const depths: number[] = [];
      const nodeTypes: string[] = [];

      class DepthCapturingVisitor extends BaseVisitor {
        protected visitNode(node: Node): Node {
          depths.push(this.getDepth());
          nodeTypes.push(node.type);
          return node;
        }
      }

      const capturingVisitor = new DepthCapturingVisitor();
      capturingVisitor.visit(ast);

      // 根节点深度应该为 0（修正后不计入当前节点）
      const moduleIndex = nodeTypes.indexOf('Module');
      if (moduleIndex !== -1) {
        expect(depths[moduleIndex]).toBe(0);
      }

      // 验证深度随嵌套增加
      expect(Math.max(...depths)).toBeGreaterThan(0);
    });
  });

  describe("reset() 方法", () => {
    test("应该清空父节点栈", async () => {
      const code = `if (true) { const x = 1; }`;
      const ast = await parser.parseCode(code, "test.ts");

      // 先访问一些节点，建立父节点栈
      visitor.visit(ast);
      
      // 确认栈中有内容（通过检查是否访问了节点）
      expect(visitor.visitedNodes.length).toBeGreaterThan(0);
      
      // 重置
      visitor.resetPublic();
      
      // 验证深度回到 0
      expect(visitor.getDepthPublic()).toBe(0);
      expect(visitor.getParentPublic()).toBeUndefined();
    });
  });

  describe("边界条件测试", () => {
    test("应该处理空 AST", async () => {
      const code = ``;
      const ast = await parser.parseCode(code, "test.ts");

      expect(() => {
        visitor.visit(ast);
      }).not.toThrow();

      expect(visitor.visitedNodes.length).toBeGreaterThanOrEqual(1); // 至少访问了根节点
    });

    test("应该处理单节点 AST", async () => {
      const code = `true;`;
      const ast = await parser.parseCode(code, "test.ts");

      visitor.visit(ast);

      expect(visitor.visitedNodes.length).toBeGreaterThan(0);
      expect(visitor.visitedNodeTypes).toContain('Module');
    });

    test("应该处理深度嵌套的结构", async () => {
      const code = `
        function deep() {
          if (a) {
            if (b) {
              if (c) {
                if (d) {
                  if (e) {
                    const x = 1;
                  }
                }
              }
            }
          }
        }
      `;
      const ast = await parser.parseCode(code, "test.ts");

      expect(() => {
        visitor.visit(ast);
      }).not.toThrow();

      // 验证访问了深层嵌套的节点
      expect(visitor.visitedNodes.length).toBeGreaterThan(10);
      
      // 验证最大深度合理
      const maxDepth = Math.max(...visitor.parentStackSnapshots.map(snapshot => snapshot.length));
      expect(maxDepth).toBeGreaterThan(5);
    });
  });

  describe("访问者遍历逻辑", () => {
    test("应该访问 AST 中的所有节点", async () => {
      const code = `
        function test() {
          const x = 1;
          const y = 2;
          return x + y;
        }
      `;
      const ast = await parser.parseCode(code, "test.ts");

      visitor.visit(ast);

      // 验证访问了主要的节点类型
      expect(visitor.visitedNodeTypes).toContain('Module');
      expect(visitor.visitedNodeTypes).toContain('FunctionDeclaration');
      expect(visitor.visitedNodeTypes).toContain('VariableDeclarator');
      expect(visitor.visitedNodeTypes).toContain('ReturnStatement');
    });

    test("应该保持访问顺序的一致性", async () => {
      const code = `
        function test() {
          const a = 1;
          const b = 2;
        }
      `;
      const ast = await parser.parseCode(code, "test.ts");

      visitor.visit(ast);

      // 第一次访问
      const firstVisitTypes = [...visitor.visitedNodeTypes];
      
      // 重置并再次访问
      visitor = new TestVisitor();
      visitor.visit(ast);
      const secondVisitTypes = [...visitor.visitedNodeTypes];

      // 访问顺序应该保持一致
      expect(firstVisitTypes).toEqual(secondVisitTypes);
    });
  });
});