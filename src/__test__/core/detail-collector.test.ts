import { describe, test, expect, beforeEach } from 'vitest';
import { DetailCollector } from '@/core/detail-collector';
import { RuleCategory, DiagnosticMarker, type RuleMetadata } from '@/core/types';

describe('DetailCollector', () => {
  let collector: DetailCollector;

  beforeEach(() => {
    collector = new DetailCollector();
    
    // 注册一些测试规则
    const testRules: RuleMetadata[] = [
      {
        ruleId: 'if-statement',
        description: 'if语句',
        category: RuleCategory.CONTROL_FLOW,
        defaultIncrement: 1
      },
      {
        ruleId: 'logical-and',
        description: '逻辑与操作',
        category: RuleCategory.LOGICAL_OPERATOR,
        defaultIncrement: 1
      },
      {
        ruleId: 'for-loop',
        description: 'for循环',
        category: RuleCategory.CONTROL_FLOW,
        defaultIncrement: 1
      }
    ];
    
    collector.registerRules(testRules);
  });

  describe('基本功能测试', () => {
    test('应该正确跟踪单个函数的复杂度计算', () => {
      // 开始跟踪函数
      collector.startFunction('testFunction', 1, 0);
      
      expect(collector.isTracking()).toBe(true);
      expect(collector.getCurrentFunctionName()).toBe('testFunction');
      expect(collector.getCurrentComplexity()).toBe(0);
      
      // 添加复杂度步骤
      collector.addStep({
        line: 2,
        column: 4,
        increment: 1,
        ruleId: 'if-statement',
        description: 'if语句'
      });
      
      expect(collector.getCurrentComplexity()).toBe(1);
      
      // 结束函数跟踪
      const result = collector.endFunction();
      
      expect(result.name).toBe('testFunction');
      expect(result.complexity).toBe(1);
      expect(result.details).toHaveLength(1);
      expect(result.details[0]).toMatchObject({
        line: 2,
        column: 4,
        increment: 1,
        cumulative: 1,
        ruleId: 'if-statement',
        description: 'if语句',
        nestingLevel: 0
      });
      
      expect(collector.isTracking()).toBe(false);
    });

    test('应该正确计算累计复杂度', () => {
      collector.startFunction('complexFunction', 1, 0);
      
      // 添加多个步骤
      collector.addStep({
        line: 2,
        column: 0,
        increment: 1,
        ruleId: 'if-statement',
        description: 'if语句'
      });
      
      collector.addStep({
        line: 3,
        column: 4,
        increment: 1,
        ruleId: 'logical-and',
        description: '逻辑与操作'
      });
      
      collector.addStep({
        line: 5,
        column: 0,
        increment: 1,
        ruleId: 'for-loop',
        description: 'for循环'
      });
      
      const result = collector.endFunction();
      
      expect(result.complexity).toBe(3);
      expect(result.details).toHaveLength(3);
      
      // 验证累计复杂度的正确性
      expect(result.details[0].cumulative).toBe(1);
      expect(result.details[1].cumulative).toBe(2);
      expect(result.details[2].cumulative).toBe(3);
    });
  });

  describe('嵌套函数处理', () => {
    test('应该正确处理嵌套函数的独立计算', () => {
      // 外层函数
      collector.startFunction('outerFunction', 1, 0);
      collector.addStep({
        line: 2,
        column: 0,
        increment: 1,
        ruleId: 'if-statement',
        description: 'if语句'
      });
      
      expect(collector.getCurrentNestingLevel()).toBe(0);
      expect(collector.getCurrentComplexity()).toBe(1);
      
      // 内层函数
      collector.startFunction('innerFunction', 3, 2);
      expect(collector.getCurrentNestingLevel()).toBe(1);
      expect(collector.getCurrentComplexity()).toBe(0); // 内层函数独立计算
      
      collector.addStep({
        line: 4,
        column: 4,
        increment: 1,
        ruleId: 'for-loop',
        description: 'for循环'
      });
      
      expect(collector.getCurrentComplexity()).toBe(1);
      
      // 结束内层函数
      const innerResult = collector.endFunction();
      expect(innerResult.name).toBe('innerFunction');
      expect(innerResult.complexity).toBe(1);
      expect(innerResult.details[0].nestingLevel).toBe(1);
      
      // 外层函数应该保持原有复杂度
      expect(collector.getCurrentComplexity()).toBe(1);
      
      // 结束外层函数
      const outerResult = collector.endFunction();
      expect(outerResult.name).toBe('outerFunction');
      expect(outerResult.complexity).toBe(1);
      expect(outerResult.details[0].nestingLevel).toBe(0);
    });

    test('应该正确跟踪函数调用栈', () => {
      collector.startFunction('func1', 1, 0);
      collector.startFunction('func2', 5, 2);
      collector.startFunction('func3', 10, 4);
      
      const stack = collector.getFunctionStack();
      expect(stack).toEqual(['func1', 'func2', 'func3']);
      
      // 测试获取指定层级的函数
      const level0 = collector.getFunctionAtLevel(0);
      const level1 = collector.getFunctionAtLevel(1);
      const level2 = collector.getFunctionAtLevel(2);
      
      expect(level0?.name).toBe('func1');
      expect(level1?.name).toBe('func2');
      expect(level2?.name).toBe('func3');
      
      // 测试无效层级
      expect(collector.getFunctionAtLevel(-1)).toBeNull();
      expect(collector.getFunctionAtLevel(3)).toBeNull();
    });
  });

  describe('规则注册和查询', () => {
    test('应该正确注册和查询规则', () => {
      const newRule: RuleMetadata = {
        ruleId: 'while-loop',
        description: 'while循环',
        category: RuleCategory.CONTROL_FLOW,
        defaultIncrement: 1
      };
      
      collector.registerRule(newRule);
      
      expect(collector.hasRule('while-loop')).toBe(true);
      expect(collector.getRuleDescription('while-loop')).toBe('while循环');
      expect(collector.getRegisteredRuleIds()).toContain('while-loop');
    });

    test('应该处理未知规则', () => {
      collector.startFunction('testFunction', 1, 0);
      
      // 添加使用未知规则的步骤
      collector.addStep({
        line: 2,
        column: 0,
        increment: 1,
        ruleId: 'unknown-rule',
        description: '未知规则'
      });
      
      const result = collector.endFunction();
      
      expect(result.details[0]).toMatchObject({
        ruleId: 'unknown-rule',
        description: '未知规则',
        diagnosticMarker: DiagnosticMarker.UNKNOWN,
        diagnosticMessage: '未知规则标识符: unknown-rule'
      });
    });
  });

  describe('诊断功能', () => {
    test('应该正确添加警告步骤', () => {
      collector.startFunction('testFunction', 1, 0);
      
      collector.addWarningStep({
        line: 2,
        column: 0,
        increment: 1,
        ruleId: 'if-statement',
        description: 'if语句'
      }, '这是一个警告');
      
      const result = collector.endFunction();
      
      expect(result.details[0]).toMatchObject({
        diagnosticMarker: DiagnosticMarker.WARNING,
        diagnosticMessage: '这是一个警告'
      });
    });

    test('应该正确添加错误步骤', () => {
      collector.startFunction('testFunction', 1, 0);
      
      collector.addErrorStep({
        line: 2,
        column: 0,
        increment: 1,
        ruleId: 'if-statement',
        description: 'if语句'
      }, '这是一个错误');
      
      const result = collector.endFunction();
      
      expect(result.details[0]).toMatchObject({
        diagnosticMarker: DiagnosticMarker.ERROR,
        diagnosticMessage: '这是一个错误'
      });
    });

    test('应该检测嵌套层级异常', () => {
      // 创建过深的嵌套
      for (let i = 0; i < 12; i++) {
        collector.startFunction(`func${i}`, i + 1, 0);
      }
      
      const hasAnomaly = collector.detectNestingAnomalies();
      expect(hasAnomaly).toBe(true);
      
      // 结束最内层函数来获取警告步骤
      const result = collector.endFunction();
      
      // 检查是否有警告步骤（可能在任何函数的详细信息中）
      let hasWarningStep = false;
      if (result.details.length > 0) {
        const warningStep = result.details.find(
          step => step.diagnosticMarker === DiagnosticMarker.WARNING
        );
        if (warningStep) {
          hasWarningStep = true;
          expect(warningStep.diagnosticMessage).toContain('嵌套层级达到');
        }
      }
      
      // 如果当前函数没有警告步骤，检查是否检测到了异常（这已经被验证）
      if (!hasWarningStep) {
        // detectNestingAnomalies 返回 true 表示已经检测到异常
        expect(hasAnomaly).toBe(true);
      }
    });
  });

  describe('步骤验证', () => {
    test('应该验证步骤数据的完整性', () => {
      // 有效步骤
      const validStep = {
        line: 1,
        column: 0,
        increment: 1,
        ruleId: 'if-statement',
        description: 'if语句'
      };
      
      const validResult = collector.validateStep(validStep);
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);
      
      // 无效步骤 - 缺少必需字段
      const invalidStep = {
        line: -1,
        column: 'invalid' as any,
        increment: 'not-a-number' as any,
        ruleId: '',
        description: ''
      };
      
      const invalidResult = collector.validateStep(invalidStep);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
      expect(invalidResult.errors).toContain('Invalid line number');
      expect(invalidResult.errors).toContain('Invalid column number');
      expect(invalidResult.errors).toContain('Invalid increment value');
      expect(invalidResult.errors).toContain('Invalid rule ID');
      expect(invalidResult.errors).toContain('Invalid description');
    });
  });

  describe('状态管理', () => {
    test('应该正确重置收集器状态', () => {
      collector.startFunction('func1', 1, 0);
      collector.startFunction('func2', 5, 2);
      collector.addStep({
        line: 6,
        column: 0,
        increment: 1,
        ruleId: 'if-statement',
        description: 'if语句'
      });
      
      expect(collector.isTracking()).toBe(true);
      expect(collector.getFunctionStack()).toHaveLength(2);
      
      collector.reset();
      
      expect(collector.isTracking()).toBe(false);
      expect(collector.getFunctionStack()).toHaveLength(0);
      expect(collector.getCurrentComplexity()).toBe(0);
    });

    test('应该从错误状态恢复', () => {
      // 创建异常深度的嵌套
      for (let i = 0; i < 15; i++) {
        collector.startFunction(`func${i}`, i + 1, 0);
      }
      
      expect(collector.getFunctionStack()).toHaveLength(15);
      
      collector.recoverFromError();
      
      // 应该清理到合理的层级
      expect(collector.getFunctionStack().length).toBeLessThanOrEqual(10);
    });
  });

  describe('性能监控', () => {
    test('应该提供性能统计信息', () => {
      collector.startFunction('testFunction', 1, 0);
      
      collector.addStep({
        line: 2,
        column: 0,
        increment: 1,
        ruleId: 'if-statement',
        description: 'if语句'
      });
      
      collector.addStep({
        line: 3,
        column: 0,
        increment: 1,
        ruleId: 'for-loop',
        description: 'for循环'
      });
      
      const stats = collector.getPerformanceStats();
      
      expect(stats.stepsCollected).toBe(2);
      expect(stats.activeFunctions).toBe(1);
      expect(stats.memoryOptimized).toBe(true);
      expect(stats.objectPoolStats).toBeDefined();
    });

    test('应该支持内存优化开关', () => {
      expect(collector.getPerformanceStats().memoryOptimized).toBe(true);
      
      collector.setMemoryOptimization(false);
      expect(collector.getPerformanceStats().memoryOptimized).toBe(false);
      
      collector.setMemoryOptimization(true);
      expect(collector.getPerformanceStats().memoryOptimized).toBe(true);
    });
  });

  describe('错误处理', () => {
    test('在没有跟踪函数时添加步骤应该抛出错误', () => {
      expect(() => {
        collector.addStep({
          line: 1,
          column: 0,
          increment: 1,
          ruleId: 'if-statement',
          description: 'if语句'
        });
      }).toThrowError('No function is currently being tracked');
    });

    test('在没有跟踪函数时结束函数应该抛出错误', () => {
      expect(() => {
        collector.endFunction();
      }).toThrowError('No function is currently being tracked');
    });

    test('应该正确处理空的函数名称', () => {
      expect(collector.getCurrentFunctionName()).toBeNull();
      
      collector.startFunction('', 1, 0);
      expect(collector.getCurrentFunctionName()).toBe('');
    });
  });
});