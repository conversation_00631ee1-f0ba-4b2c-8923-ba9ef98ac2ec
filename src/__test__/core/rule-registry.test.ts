import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { RuleRegistry } from '@/core/rule-registry';
import { RuleCategory, type RuleMetadata, type RuleRegistrationConfig } from '@/core/types';

describe('RuleRegistry', () => {
  beforeEach(() => {
    // 每个测试前清空注册表
    RuleRegistry.clear();
  });

  afterEach(() => {
    // 每个测试后清空注册表
    RuleRegistry.clear();
  });

  describe('基本规则注册', () => {
    test('应该成功注册单个规则', () => {
      RuleRegistry.register('if-statement', 'if语句', RuleCategory.CONTROL_FLOW, 1);
      
      expect(RuleRegistry.hasRule('if-statement')).toBe(true);
      expect(RuleRegistry.getDescription('if-statement')).toBe('if语句');
      
      const rule = RuleRegistry.getRule('if-statement');
      expect(rule).toMatchObject({
        ruleId: 'if-statement',
        description: 'if语句',
        category: RuleCategory.CONTROL_FLOW,
        defaultIncrement: 1,
        enabled: true,
        priority: 0
      });
    });

    test('应该支持默认参数', () => {
      RuleRegistry.register('simple-rule', '简单规则');
      
      const rule = RuleRegistry.getRule('simple-rule');
      expect(rule).toMatchObject({
        ruleId: 'simple-rule',
        description: '简单规则',
        category: RuleCategory.OTHER,
        defaultIncrement: 1,
        enabled: true,
        priority: 0
      });
    });

    test('应该通过规则元数据对象注册', () => {
      const ruleMetadata: RuleMetadata = {
        ruleId: 'logical-and',
        description: '逻辑与操作',
        category: RuleCategory.LOGICAL_OPERATOR,
        defaultIncrement: 1,
        enabled: true,
        priority: 5
      };
      
      RuleRegistry.registerRule(ruleMetadata);
      
      const registeredRule = RuleRegistry.getRule('logical-and');
      expect(registeredRule).toMatchObject(ruleMetadata);
    });
  });

  describe('规则ID格式验证', () => {
    test('应该接受有效的kebab-case格式', () => {
      const validIds = [
        'if-statement',
        'logical-and',
        'for-loop',
        'try-catch',
        'switch-case',
        'while-loop',
        'function-call',
        'a',
        'a1',
        'rule-with-numbers123'
      ];
      
      validIds.forEach(id => {
        expect(RuleRegistry.validateRuleId(id)).toBe(true);
        expect(() => {
          RuleRegistry.register(id, '测试规则');
        }).not.toThrow();
        RuleRegistry.unregister(id); // 清理
      });
    });

    test('应该拒绝无效的格式', () => {
      const invalidIds = [
        'IfStatement',        // PascalCase
        'if_statement',       // snake_case
        'ifStatement',        // camelCase
        'if-',                // 以短横线结尾
        '-if',                // 以短横线开头
        'if--statement',      // 连续短横线
        'if statement',       // 包含空格
        '123-rule',           // 以数字开头
        '',                   // 空字符串
        'if.statement',       // 包含点号
        'if@statement'        // 包含特殊字符
      ];
      
      invalidIds.forEach(id => {
        expect(RuleRegistry.validateRuleId(id)).toBe(false);
        expect(() => {
          RuleRegistry.register(id, '测试规则');
        }).toThrowError(/Invalid rule ID format/);
      });
    });
  });

  describe('规则查询功能', () => {
    beforeEach(() => {
      // 注册一些测试规则
      RuleRegistry.register('if-statement', 'if语句', RuleCategory.CONTROL_FLOW, 1);
      RuleRegistry.register('logical-and', '逻辑与操作', RuleCategory.LOGICAL_OPERATOR, 1);
      RuleRegistry.register('for-loop', 'for循环', RuleCategory.CONTROL_FLOW, 1);
      RuleRegistry.register('try-catch', '异常处理', RuleCategory.EXCEPTION_HANDLING, 1);
    });

    test('应该正确获取所有规则', () => {
      const allRules = RuleRegistry.getAllRules();
      expect(allRules).toHaveLength(4);
      
      const ruleIds = allRules.map(rule => rule.ruleId);
      expect(ruleIds).toContain('if-statement');
      expect(ruleIds).toContain('logical-and');
      expect(ruleIds).toContain('for-loop');
      expect(ruleIds).toContain('try-catch');
    });

    test('应该正确获取所有规则ID', () => {
      const allIds = RuleRegistry.getAllRuleIds();
      expect(allIds).toHaveLength(4);
      expect(allIds).toContain('if-statement');
      expect(allIds).toContain('logical-and');
      expect(allIds).toContain('for-loop');
      expect(allIds).toContain('try-catch');
    });

    test('应该按分类查询规则', () => {
      const controlFlowRules = RuleRegistry.getRulesByCategory(RuleCategory.CONTROL_FLOW);
      expect(controlFlowRules).toHaveLength(2);
      expect(controlFlowRules.map(r => r.ruleId)).toEqual(
        expect.arrayContaining(['if-statement', 'for-loop'])
      );
      
      const logicalRules = RuleRegistry.getRulesByCategory(RuleCategory.LOGICAL_OPERATOR);
      expect(logicalRules).toHaveLength(1);
      expect(logicalRules[0].ruleId).toBe('logical-and');
    });

    test('对不存在的规则应该返回null', () => {
      expect(RuleRegistry.getRule('non-existent')).toBeNull();
      expect(RuleRegistry.getDescription('non-existent')).toBeNull();
      expect(RuleRegistry.hasRule('non-existent')).toBe(false);
    });
  });

  describe('批量规则注册', () => {
    test('应该成功批量注册规则', () => {
      const config: RuleRegistrationConfig = {
        rules: [
          {
            ruleId: 'if-statement',
            description: 'if语句',
            category: RuleCategory.CONTROL_FLOW,
            defaultIncrement: 1
          },
          {
            ruleId: 'logical-and',
            description: '逻辑与操作',
            category: RuleCategory.LOGICAL_OPERATOR,
            defaultIncrement: 1
          },
          {
            ruleId: 'for-loop',
            description: 'for循环',
            category: RuleCategory.CONTROL_FLOW,
            defaultIncrement: 1
          }
        ],
        version: '1.0.0',
        description: '测试规则集'
      };
      
      RuleRegistry.registerBatch(config);
      
      expect(RuleRegistry.getAllRules()).toHaveLength(3);
      expect(RuleRegistry.hasRule('if-statement')).toBe(true);
      expect(RuleRegistry.hasRule('logical-and')).toBe(true);
      expect(RuleRegistry.hasRule('for-loop')).toBe(true);
    });

    test('应该跳过已存在的规则（默认行为）', () => {
      // 先注册一个规则
      RuleRegistry.register('if-statement', '原始描述', RuleCategory.CONTROL_FLOW, 1);
      
      const config: RuleRegistrationConfig = {
        rules: [
          {
            ruleId: 'if-statement',
            description: '新描述',
            category: RuleCategory.CONTROL_FLOW,
            defaultIncrement: 2
          },
          {
            ruleId: 'new-rule',
            description: '新规则',
            category: RuleCategory.OTHER,
            defaultIncrement: 1
          }
        ],
        version: '1.0.0'
      };
      
      RuleRegistry.registerBatch(config);
      
      // 已存在的规则应该保持原有描述
      expect(RuleRegistry.getDescription('if-statement')).toBe('原始描述');
      // 新规则应该被注册
      expect(RuleRegistry.hasRule('new-rule')).toBe(true);
    });

    test('应该支持覆盖已存在的规则', () => {
      // 先注册一个规则
      RuleRegistry.register('if-statement', '原始描述', RuleCategory.CONTROL_FLOW, 1);
      
      const config: RuleRegistrationConfig = {
        rules: [
          {
            ruleId: 'if-statement',
            description: '新描述',
            category: RuleCategory.CONTROL_FLOW,
            defaultIncrement: 2
          }
        ],
        version: '1.0.0',
        overwrite: true
      };
      
      RuleRegistry.registerBatch(config);
      
      // 规则应该被覆盖
      const rule = RuleRegistry.getRule('if-statement');
      expect(rule?.description).toBe('新描述');
      expect(rule?.defaultIncrement).toBe(2);
    });
  });

  describe('规则更新和删除', () => {
    beforeEach(() => {
      RuleRegistry.register('test-rule', '测试规则', RuleCategory.OTHER, 1);
    });

    test('应该成功更新已存在的规则', () => {
      RuleRegistry.updateRule('test-rule', {
        description: '更新后的描述',
        defaultIncrement: 2,
        priority: 5
      });
      
      const rule = RuleRegistry.getRule('test-rule');
      expect(rule).toMatchObject({
        ruleId: 'test-rule',
        description: '更新后的描述',
        category: RuleCategory.OTHER,
        defaultIncrement: 2,
        priority: 5
      });
    });

    test('更新不存在的规则应该抛出错误', () => {
      expect(() => {
        RuleRegistry.updateRule('non-existent', { description: '新描述' });
      }).toThrowError('Cannot update non-existent rule');
    });

    test('应该成功删除规则', () => {
      expect(RuleRegistry.hasRule('test-rule')).toBe(true);
      
      const deleted = RuleRegistry.unregister('test-rule');
      expect(deleted).toBe(true);
      expect(RuleRegistry.hasRule('test-rule')).toBe(false);
      
      // 尝试删除不存在的规则
      const notDeleted = RuleRegistry.unregister('non-existent');
      expect(notDeleted).toBe(false);
    });
  });

  describe('数据验证', () => {
    test('应该验证规则描述不为空', () => {
      expect(() => {
        RuleRegistry.register('test-rule', '');
      }).toThrowError('Rule description cannot be empty');
      
      expect(() => {
        RuleRegistry.register('test-rule', '   ');
      }).toThrowError('Rule description cannot be empty');
    });

    test('应该验证默认增量为非负数', () => {
      expect(() => {
        RuleRegistry.register('test-rule', '测试规则', RuleCategory.OTHER, -1);
      }).toThrowError('Default increment must be a non-negative number');
      
      expect(() => {
        RuleRegistry.register('test-rule', '测试规则', RuleCategory.OTHER, 'invalid' as any);
      }).toThrowError('Default increment must be a non-negative number');
    });

    test('应该防止重复注册相同ID的规则', () => {
      RuleRegistry.register('duplicate-rule', '规则1');
      
      expect(() => {
        RuleRegistry.register('duplicate-rule', '规则2');
      }).toThrowError('Rule ID "duplicate-rule" is already registered');
    });
  });

  describe('配置验证', () => {
    test('应该验证有效的配置', () => {
      const validConfig: RuleRegistrationConfig = {
        rules: [
          {
            ruleId: 'valid-rule',
            description: '有效规则',
            category: RuleCategory.CONTROL_FLOW,
            defaultIncrement: 1
          }
        ],
        version: '1.0.0'
      };
      
      const result = RuleRegistry.validateConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('应该验证无效的配置', () => {
      const invalidConfig = {
        rules: [
          {
            ruleId: 'Invalid-Rule-ID',
            description: '',
            category: RuleCategory.CONTROL_FLOW,
            defaultIncrement: -1
          }
        ]
      } as RuleRegistrationConfig;
      
      const result = RuleRegistry.validateConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors).toContain('Config must contain a version string');
      
      // 检查是否包含各种错误信息
      const hasIdFormatError = result.errors.some(error => 
        error.includes('invalid ID format') || error.includes('has invalid ID format')
      );
      expect(hasIdFormatError).toBe(true);
      
      const hasDescriptionError = result.errors.some(error => 
        error.includes('invalid description') || error.includes('has invalid description')
      );
      expect(hasDescriptionError).toBe(true);
      
      const hasIncrementError = result.errors.some(error => 
        error.includes('invalid defaultIncrement') || error.includes('has invalid defaultIncrement')
      );
      expect(hasIncrementError).toBe(true);
    });

    test('应该验证缺少规则数组的配置', () => {
      const configWithoutRules = {
        version: '1.0.0'
      } as RuleRegistrationConfig;
      
      const result = RuleRegistry.validateConfig(configWithoutRules);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Config must contain a rules array');
    });
  });

  describe('统计信息', () => {
    beforeEach(() => {
      RuleRegistry.register('if-statement', 'if语句', RuleCategory.CONTROL_FLOW, 1);
      RuleRegistry.register('logical-and', '逻辑与操作', RuleCategory.LOGICAL_OPERATOR, 1);
      RuleRegistry.register('for-loop', 'for循环', RuleCategory.CONTROL_FLOW, 1);
      
      // 注册一个禁用的规则
      RuleRegistry.registerRule({
        ruleId: 'disabled-rule',
        description: '禁用规则',
        category: RuleCategory.OTHER,
        defaultIncrement: 1,
        enabled: false
      });
    });

    test('应该提供正确的统计信息', () => {
      const stats = RuleRegistry.getStatistics();
      
      expect(stats.total).toBe(4);
      expect(stats.enabled).toBe(3);
      expect(stats.disabled).toBe(1);
      expect(stats.byCategory[RuleCategory.CONTROL_FLOW]).toBe(2);
      expect(stats.byCategory[RuleCategory.LOGICAL_OPERATOR]).toBe(1);
      expect(stats.byCategory[RuleCategory.OTHER]).toBe(1);
    });
  });

  describe('配置导出', () => {
    beforeEach(() => {
      RuleRegistry.register('if-statement', 'if语句', RuleCategory.CONTROL_FLOW, 1);
      RuleRegistry.register('logical-and', '逻辑与操作', RuleCategory.LOGICAL_OPERATOR, 1);
    });

    test('应该正确导出配置', () => {
      const exported = RuleRegistry.exportConfig();
      
      expect(exported.rules).toHaveLength(2);
      expect(exported.version).toBe('1.0.0');
      expect(exported.description).toBe('Exported rule configuration');
      expect(exported.overwrite).toBe(false);
      
      const ruleIds = exported.rules.map(r => r.ruleId);
      expect(ruleIds).toContain('if-statement');
      expect(ruleIds).toContain('logical-and');
    });
  });

  describe('状态管理', () => {
    test('应该正确清空所有规则', () => {
      RuleRegistry.register('rule1', '规则1');
      RuleRegistry.register('rule2', '规则2');
      
      expect(RuleRegistry.getAllRules()).toHaveLength(2);
      
      RuleRegistry.clear();
      
      expect(RuleRegistry.getAllRules()).toHaveLength(0);
      expect(RuleRegistry.hasRule('rule1')).toBe(false);
      expect(RuleRegistry.hasRule('rule2')).toBe(false);
    });
  });
});