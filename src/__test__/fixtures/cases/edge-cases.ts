// 边界情况和特殊语法测试用例

export const edgeCases = [
  {
    name: "箭头函数",
    code: `
      const arrowFunction = () => {
        if (condition) {
          return true;
        }
      };
    `,
    expected: [{ name: "arrowFunction", complexity: 1 }]
  },
  
  {
    name: "方法定义",
    code: `
      class TestClass {
        method() {
          if (condition) {
            return true;
          }
        }
      }
    `,
    expected: [{ name: "method", complexity: 1 }] // 类方法现在能正确识别并计算复杂度
  },
  
  {
    name: "getter方法",
    code: `
      class TestClass {
        get value() {
          if (this.cached) {
            return this.cached;
          }
          return this.calculate();
        }
      }
    `,
    expected: [{ name: "value", complexity: 1 }] // getter现在能正确识别并计算复杂度
  },
  
  {
    name: "setter方法",
    code: `
      class TestClass {
        set value(val) {
          if (val > 0) {
            this._value = val;
          }
        }
      }
    `,
    expected: [{ name: "value", complexity: 1 }] // setter现在能正确识别并计算复杂度
  },
  
  {
    name: "递归函数调用",
    code: `
      function factorial(n) {
        if (n <= 1) {         // +1
          return 1;
        }
        return n * factorial(n - 1);  // +1(递归)
      }
    `,
    expected: [{ name: "factorial", complexity: 2 }]
  },
  
  {
    name: "异步函数",
    code: `
      async function asyncFunction() {
        try {                    // +1(catch)
          const result = await fetch('/api');
          if (result.ok) {       // +1
            return result.json();
          }
        } catch (error) {
          console.error(error);
        }
      }
    `,
    expected: [{ name: "asyncFunction", complexity: 2 }]
  },
  
  {
    name: "生成器函数",
    code: `
      function* generator() {
        for (let i = 0; i < 10; i++) {  // +1
          if (i % 2 === 0) {            // +1 + 1(嵌套) = +2
            yield i;
          }
        }
      }
    `,
    expected: [{ name: "generator", complexity: 3 }]
  },
  
  {
    name: "for-in循环",
    code: `
      function forInLoop() {
        for (const key in object) {  // +1
          if (object.hasOwnProperty(key)) {  // +1 + 1(嵌套) = +2
            process(key);
          }
        }
      }
    `,
    expected: [{ name: "forInLoop", complexity: 3 }]
  },
  
  {
    name: "for-of循环",
    code: `
      function forOfLoop() {
        for (const item of array) {  // +1
          if (item.valid) {          // +1 + 1(嵌套) = +2
            process(item);
          }
        }
      }
    `,
    expected: [{ name: "forOfLoop", complexity: 3 }]
  },
  
  {
    name: "labeled语句",
    code: `
      function labeledStatement() {
        outer: for (let i = 0; i < 10; i++) {  // +1
          for (let j = 0; j < 10; j++) {       // +1 + 1(嵌套) = +2
            if (condition) {                   // +1 + 2(嵌套) = +3
              break outer;
            }
          }
        }
      }
    `,
    expected: [{ name: "labeledStatement", complexity: 6 }]
  },
  
  {
    name: "多个函数表达式",
    code: `
      const obj = {
        method1: function() {
          if (condition) {  // +1
            return true;
          }
        },
        method2: () => {
          while (condition) {  // +1
            doSomething();
          }
        }
      };
    `,
    expected: [] // 函数表达式可能不被识别为函数
  }
];