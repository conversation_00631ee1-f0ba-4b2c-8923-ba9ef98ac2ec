// 基础复杂度测试用例

export const basicCases = [
  {
    name: "空函数",
    code: `
      function empty() {
      }
    `,
    expected: [{ name: "empty", complexity: 0 }]
  },
  
  {
    name: "简单返回函数", 
    code: `
      function simple() {
        return true;
      }
    `,
    expected: [{ name: "simple", complexity: 0 }]
  },
  
  {
    name: "单个if语句",
    code: `
      function withIf() {
        if (condition) {
          return true;
        }
      }
    `,
    expected: [{ name: "withIf", complexity: 1 }]
  },
  
  {
    name: "if-else语句",
    code: `
      function withIfElse() {
        if (condition) {
          return true;
        } else {
          return false;
        }
      }
    `,
    expected: [{ name: "withIfElse", complexity: 1 }]
  },
  
  {
    name: "switch语句",
    code: `
      function withSwitch(value: number) {
        switch (value) {
          case 1:
            return "one";
          case 2:
            return "two";
          default:
            return "other";
        }
      }
    `,
    expected: [{ name: "withSwitch", complexity: 0 }]
  },
  
  {
    name: "for循环",
    code: `
      function withFor() {
        for (let i = 0; i < 10; i++) {
          console.log(i);
        }
      }
    `,
    expected: [{ name: "withFor", complexity: 1 }]
  },
  
  {
    name: "while循环",
    code: `
      function withWhile() {
        while (condition) {
          doSomething();
        }
      }
    `,
    expected: [{ name: "withWhile", complexity: 1 }]
  },
  
  {
    name: "do-while循环",
    code: `
      function withDoWhile() {
        do {
          doSomething();
        } while (condition);
      }
    `,
    expected: [{ name: "withDoWhile", complexity: 1 }]
  },
  
  {
    name: "try-catch语句",
    code: `
      function withTryCatch() {
        try {
          riskyOperation();
        } catch (error) {
          handleError(error);
        }
      }
    `,
    expected: [{ name: "withTryCatch", complexity: 0 }]
  },
  
  {
    name: "三元运算符",
    code: `
      function withTernary() {
        return condition ? "yes" : "no";
      }
    `,
    expected: [{ name: "withTernary", complexity: 0 }]
  }
];