// 逻辑运算符复杂度测试用例

export const logicalCases = [
  {
    name: "单个&&运算符",
    code: `
      function andOperator() {
        if (a && b) {
          return true;
        }
      }
    `,
    expected: [{ name: "andOperator", complexity: 1 }] // 仅if(1)
  },
  
  {
    name: "单个||运算符",
    code: `
      function orOperator() {
        if (a || b) {
          return true;
        }
      }
    `,
    expected: [{ name: "orOperator", complexity: 1 }] // 仅if(1)
  },
  
  {
    name: "混合逻辑运算符",
    code: `
      function mixedOperators() {
        if (a && b || c) {
          return true;
        }
      }
    `,
    expected: [{ name: "mixedOperators", complexity: 1 }] // 仅if(1)
  },
  
  {
    name: "复杂逻辑表达式",
    code: `
      function complexLogical() {
        if ((a && b) || (c && d) || e) {
          return true;
        }
      }
    `,
    expected: [{ name: "complexLogical", complexity: 1 }] // 仅if(1)
  },
  
  {
    name: "可选链不增加复杂度",
    code: `
      function optionalChaining() {
        return obj?.prop?.method?.();
      }
    `,
    expected: [{ name: "optionalChaining", complexity: 0 }]
  },
  
  {
    name: "空值合并不增加复杂度",
    code: `
      function nullishCoalescing() {
        return value ?? defaultValue;
      }
    `,
    expected: [{ name: "nullishCoalescing", complexity: 0 }]
  },
  
  {
    name: "默认值赋值不增加复杂度",
    code: `
      function defaultAssignment() {
        const result = value || "default";
        return result;
      }
    `,
    expected: [{ name: "defaultAssignment", complexity: 0 }] // 默认值赋值应该豁免
  },
  
  {
    name: "逻辑运算符在条件语句中",
    code: `
      function logicalInCondition() {
        while (a && b) {
          if (c || d) {
            break;
          }
        }
      }
    `,
    expected: [{ name: "logicalInCondition", complexity: 2 }] // while(1) + if(2嵌套) = 2
  }
];