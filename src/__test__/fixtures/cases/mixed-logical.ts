// 逻辑运算符混用检测测试夹具
// 基于需求规格 - 涵盖所有验收标准的测试用例

export const mixedLogicalCases = [
  // Requirement 1: 基础混用检测与惩罚
  {
    name: "基础混用惩罚 - a && b || c",
    code: `
      function basicMixing() {
        if (a && b || c) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "basicMixing", complexity: 4 }] // if(1) + &&(1) + ||(1) + 混用惩罚(1)
  },

  {
    name: "基础混用惩罚 - a || b && c",
    code: `
      function reverseMixing() {
        if (a || b && c) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "reverseMixing", complexity: 4 }] // if(1) + ||(1) + &&(1) + 混用惩罚(1)
  },

  {
    name: "功能禁用时无混用惩罚",
    code: `
      function mixingDisabled() {
        if (a && b || c) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: false },
    expected: [{ name: "mixingDisabled", complexity: 3 }] // if(1) + &&(1) + ||(1), 无混用惩罚
  },

  // Requirement 2: 括号豁免机制
  {
    name: "括号消除惩罚 - (a && b) || c",
    code: `
      function parenthesesExemption1() {
        if ((a && b) || c) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "parenthesesExemption1", complexity: 3 }] // if(1) + &&(1) + ||(1), 无混用惩罚
  },

  {
    name: "括号消除惩罚 - a && (b || c)",
    code: `
      function parenthesesExemption2() {
        if (a && (b || c)) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "parenthesesExemption2", complexity: 3 }] // if(1) + &&(1) + ||(1), 无混用惩罚
  },

  {
    name: "多层括号正确处理",
    code: `
      function nestedParentheses() {
        if ((a && b) || (c && d)) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "nestedParentheses", complexity: 4 }] // if(1) + &&(1) + ||(1) + &&(1), 无混用惩罚
  },

  // Requirement 3: 同类运算符豁免
  {
    name: "连续&&运算符无惩罚",
    code: `
      function allAndOperators() {
        if (a && b && c) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "allAndOperators", complexity: 3 }] // if(1) + &&(1) + &&(1), 无混用惩罚
  },

  {
    name: "连续||运算符无惩罚",
    code: `
      function allOrOperators() {
        if (a || b || c) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "allOrOperators", complexity: 3 }] // if(1) + ||(1) + ||(1), 无混用惩罚
  },

  {
    name: "更长的同类运算符链",
    code: `
      function longAndChain() {
        if (a && b && c && d && e) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "longAndChain", complexity: 5 }] // if(1) + &&(1)*4, 无混用惩罚
  },

  // Requirement 4: 默认值赋值豁免
  {
    name: "默认值赋值豁免 - 普通赋值",
    code: `
      function defaultValueAssignment() {
        const name = user.name || 'Guest';
        return name;
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "defaultValueAssignment", complexity: 0 }] // 默认值赋值豁免
  },

  {
    name: "空值合并赋值豁免",
    code: `
      function nullishCoalescingAssignment() {
        const value = data ?? 'default';
        return value;
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "nullishCoalescingAssignment", complexity: 0 }] // 默认值赋值豁免
  },

  {
    name: "条件语句中的逻辑运算符有惩罚",
    code: `
      function logicalInCondition() {
        if (user.name || user.email && user.active) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "logicalInCondition", complexity: 4 }] // if(1) + ||(1) + &&(1) + 混用惩罚(1)
  },

  // Requirement 6: 复杂嵌套场景处理
  {
    name: "复杂混用场景 - a || b && c || d",
    code: `
      function complexMixing() {
        if (a || b && c || d) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "complexMixing", complexity: 5 }] // if(1) + ||(1) + &&(1) + ||(1) + 混用惩罚(1)
  },

  {
    name: "嵌套结构中的混用",
    code: `
      function nestedMixing() {
        while (condition) {
          if (a && b || c) {
            break;
          }
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "nestedMixing", complexity: 6 }] // while(1) + if(1+1嵌套) + &&(1) + ||(1) + 混用惩罚(1) = 6
  },

  {
    name: "多层嵌套混用场景",
    code: `
      function multiLevelNesting() {
        for (let i = 0; i < 10; i++) {
          if (a && b || c) {
            while (x || y && z) {
              return true;
            }
          }
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "multiLevelNesting", complexity: 12 }] 
    // for(1) + if(1+1嵌套) + &&(1) + ||(1) + 混用惩罚(1) + while(1+2嵌套) + ||(1) + &&(1) + 混用惩罚(1) = 12
  },

  // 边界条件和特殊场景
  {
    name: "三元运算符不受影响",
    code: `
      function ternaryOperator() {
        return a && b || c ? x : y;
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "ternaryOperator", complexity: 4 }] // &&(1) + ||(1) + ?(1) + 混用惩罚(1)
  },

  {
    name: "位运算符不受影响",
    code: `
      function bitwiseOperators() {
        if (a & b | c) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "bitwiseOperators", complexity: 1 }] // if(1), 位运算符不计入复杂度
  },

  {
    name: "单一运算符表达式",
    code: `
      function singleOperator() {
        if (a && b) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "singleOperator", complexity: 2 }] // if(1) + &&(1), 无混用
  },

  {
    name: "空表达式",
    code: `
      function emptyCondition() {
        if (true) {
          return true;
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "emptyCondition", complexity: 1 }] // if(1), 无逻辑运算符
  },

  // 真实代码场景
  {
    name: "真实场景 - 用户权限检查",
    code: `
      function checkUserPermission() {
        if (user.isAdmin || user.role === 'moderator' && user.active) {
          return true;
        }
        return false;
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "checkUserPermission", complexity: 4 }] // if(1) + ||(1) + &&(1) + 混用惩罚(1)
  },

  {
    name: "真实场景 - 数据验证",
    code: `
      function validateData() {
        if (data && data.length > 0 || fallback) {
          return process(data);
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "validateData", complexity: 4 }] // if(1) + &&(1) + ||(1) + 混用惩罚(1)
  },

  {
    name: "真实场景 - 配置默认值与条件检查",
    code: `
      function handleConfig() {
        const config = userConfig || defaultConfig;
        if (config.enabled && config.mode === 'production' || config.debug) {
          return initializeApp(config);
        }
      }
    `,
    options: { enableMixedLogicOperatorPenalty: true },
    expected: [{ name: "handleConfig", complexity: 4 }] // 赋值豁免(0) + if(1) + &&(1) + ||(1) + 混用惩罚(1) = 4
  }
];