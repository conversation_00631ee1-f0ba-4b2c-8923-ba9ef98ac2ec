// 嵌套复杂度测试用例

export const nestedCases = [
  {
    name: "二层嵌套",
    code: `
      function twoLevelNested() {
        if (condition1) {          // +1
          for (let i = 0; i < 10; i++) {  // +1 + 1(嵌套) = +2
            console.log(i);
          }
        }
      }
    `,
    expected: [{ name: "twoLevelNested", complexity: 3 }] // 1 + 2
  },
  
  {
    name: "三层嵌套",
    code: `
      function threeLevelNested() {
        for (let i = 0; i < 10; i++) {        // +1
          if (condition1) {                   // +1 + 1(嵌套) = +2
            while (condition2) {              // +1 + 2(嵌套) = +3
              doSomething();
            }
          }
        }
      }
    `,
    expected: [{ name: "threeLevelNested", complexity: 6 }] // 1 + 2 + 3
  },
  
  {
    name: "四层嵌套",
    code: `
      function fourLevelNested() {
        if (condition1) {                     // +1
          for (let i = 0; i < 10; i++) {     // +1 + 1(嵌套) = +2
            if (condition2) {                 // +1 + 2(嵌套) = +3
              try {                           // +1 + 3(嵌套) = +4
                riskyOperation();
              } catch (error) {
                handleError();
              }
            }
          }
        }
      }
    `,
    expected: [{ name: "fourLevelNested", complexity: 10 }] // 1 + 2 + 3 + 4
  },
  
  {
    name: "相邻条件不增加嵌套",
    code: `
      function adjacentConditions() {
        if (condition1) {     // +1
          doSomething();
        }
        if (condition2) {     // +1
          doOther();
        }
        for (let i = 0; i < 10; i++) {  // +1
          console.log(i);
        }
      }
    `,
    expected: [{ name: "adjacentConditions", complexity: 3 }] // 1 + 1 + 1
  },
  
  {
    name: "switch内部嵌套",
    code: `
      function switchNested() {
        switch (value) {      // +1
          case 1:
            if (condition) {  // +1 + 1(嵌套) = +2
              return true;
            }
            break;
          case 2:
            for (let i = 0; i < 5; i++) {  // +1 + 1(嵌套) = +2
              process(i);
            }
            break;
        }
      }
    `,
    expected: [{ name: "switchNested", complexity: 5 }] // 1 + 2 + 2
  },
  
  {
    name: "复杂嵌套与逻辑运算符",
    code: `
      function complexNestedLogical() {
        if (a && b) {                    // +1 + 1(&&)
          for (let i = 0; i < 10; i++) { // +1 + 1(嵌套) = +2
            if (c || d) {                // +1 + 1(||) + 2(嵌套) = +4
              while (e && f) {           // +1 + 1(&&) + 3(嵌套) = +5
                process();
              }
            }
          }
        }
      }
    `,
    expected: [{ name: "complexNestedLogical", complexity: 13 }] // 2 + 2 + 4 + 5
  }
];