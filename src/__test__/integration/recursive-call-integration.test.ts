/**
 * RecursiveCallRule集成测试
 * 验证RecursiveCallRule与ComplexityCalculator的完整集成
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ComplexityCalculator } from '../../core/calculator';
import { createLightweightFactory } from '../../core/calculator-factory';

describe('RecursiveCallRule Integration', () => {
  let calculator: ComplexityCalculator;

  beforeEach(async () => {
    const factory = createLightweightFactory();
    calculator = new ComplexityCalculator({}, factory);
  });

  afterEach(async () => {
    await calculator.dispose();
  });

  describe('直接递归函数', () => {
    it('应该正确计算简单递归函数的复杂度', async () => {
      const code = `
        function factorial(n) {
          if (n <= 1) {
            return 1;
          }
          return n * factorial(n - 1);
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('factorial');
      // if: 1, 递归调用: 1, 总计: 2
      expect(func.complexity).toBe(2);
    });

    it('应该正确计算嵌套结构中的递归函数复杂度', async () => {
      const code = `
        function processTree(node) {
          if (node.hasChildren) {
            for (const child of node.children) {
              processTree(child);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('processTree');
      // if: 1, for: 1+1(嵌套) = 2, 递归调用: 1, 总计: 4
      expect(func.complexity).toBe(4);
    });

    it('应该正确计算复杂递归算法的复杂度', async () => {
      const code = `
        function quickSort(arr) {
          if (arr.length <= 1) {
            return arr;
          }
          
          const pivot = arr[0];
          const left = [];
          const right = [];
          
          for (let i = 1; i < arr.length; i++) {
            if (arr[i] < pivot) {
              left.push(arr[i]);
            } else {
              right.push(arr[i]);
            }
          }
          
          return [...quickSort(left), pivot, ...quickSort(right)];
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('quickSort');
      // if: 1, for: 1, 内部if: 1+1(嵌套) = 2, 两个递归调用: 2, 总计: 6
      expect(func.complexity).toBeGreaterThanOrEqual(5);
    });
  });

  describe('方法递归调用', () => {
    it('应该正确计算类方法中的递归调用', async () => {
      const code = `
        class BinaryTree {
          search(value) {
            if (this.value === value) {
              return this;
            }
            
            if (value < this.value && this.left) {
              return this.left.search(value);
            }
            
            if (value > this.value && this.right) {
              return this.right.search(value);
            }
            
            return null;
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('search');
      // 三个if语句: 3, 两个递归调用: 2, 总计: 5 
      expect(func.complexity).toBeGreaterThanOrEqual(4);
    });

    it('应该正确计算链表遍历的递归方法', async () => {
      const code = `
        class LinkedList {
          print() {
            console.log(this.value);
            if (this.next) {
              this.next.print();
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('print');
      // if: 1, 递归调用: 1, 总计: 2
      expect(func.complexity).toBe(2);
    });

    it('应该正确计算树结构遍历的递归方法', async () => {
      const code = `
        class TreeNode {
          traverse() {
            const result = [this.value];
            
            if (this.children && this.children.length > 0) {
              for (const child of this.children) {
                result.push(...child.traverse());
              }
            }
            
            return result;
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('traverse');
      // if: 1, for: 1+1(嵌套) = 2, 递归调用: 1, 总计: 4
      expect(func.complexity).toBe(4);
    });
  });

  describe('与现有规则的协作', () => {
    it('应该与IfStatementRule正确协作', async () => {
      const code = `
        function fibonacci(n) {
          if (n <= 1) {
            return n;
          } else if (n === 2) {
            return 1; 
          } else {
            return fibonacci(n - 1) + fibonacci(n - 2);
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('fibonacci');
      // if: 1, else if: 1, else: 1, 两个递归调用: 2, 总计: 5
      expect(func.complexity).toBeGreaterThanOrEqual(4);
    });

    it('应该与ForStatementRule正确协作', async () => {
      const code = `
        function processArray(arr) {
          for (let i = 0; i < arr.length; i++) {
            if (Array.isArray(arr[i])) {
              processArray(arr[i]);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('processArray');
      // for: 1, if: 1+1(嵌套) = 2, 递归调用: 1, 总计: 4
      expect(func.complexity).toBe(4);
    });

    it('应该与WhileStatementRule正确协作', async () => {
      const code = `
        function findNode(head, value) {
          let current = head;
          while (current) {
            if (current.value === value) {
              return current;
            } else if (current.hasChildren && current.children.length > 0) {
              const found = findNode(current.children[0], value);
              if (found) {
                return found;
              }
            }
            current = current.next;
          }
          return null;
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('findNode');
      // while: 1, if: 1+1(嵌套) = 2, else if: 1+1(嵌套) = 2, 内部if: 1+2(嵌套) = 3, 递归调用: 1, 总计: 9
      expect(func.complexity).toBeGreaterThanOrEqual(7);
    });

    it('应该与ConditionalExpressionRule正确协作', async () => {
      const code = `
        function calculateValue(n) {
          return n <= 0 ? 0 : n + calculateValue(n - 1);
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('calculateValue');
      // 三元运算符: 1, 递归调用: 1, 总计: 2
      expect(func.complexity).toBe(2);
    });

    it('应该与CatchClauseRule正确协作', async () => {
      const code = `
        function safeRecursiveCall(data) {
          try {
            if (data.hasChildren) {
              return data.children.map(child => safeRecursiveCall(child));
            }
            return data.value;
          } catch (error) {
            console.error('Recursion error:', error);
            return null;
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('safeRecursiveCall');
      // if: 1, 递归调用: 1, catch: 1, 总计: 3
      expect(func.complexity).toBe(3);
    });
  });

  describe('真实世界场景', () => {
    it('应该正确处理JSON深拷贝的递归实现', async () => {
      const code = `
        function deepClone(obj) {
          if (obj === null || typeof obj !== 'object') {
            return obj;
          }
          
          if (obj instanceof Date) {
            return new Date(obj.getTime());
          }
          
          if (obj instanceof Array) {
            return obj.map(item => deepClone(item));
          }
          
          if (typeof obj === 'object') {
            const cloned = {};
            for (const key in obj) {
              if (obj.hasOwnProperty(key)) {
                cloned[key] = deepClone(obj[key]);
              }
            }
            return cloned;
          }
          
          return obj;
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('deepClone');
      // 多个if语句 + 递归调用，复杂度应该较高
      expect(func.complexity).toBeGreaterThanOrEqual(6);
    });

    it('应该正确处理目录遍历的递归实现', async () => {
      const code = `
        function traverseDirectory(dirPath) {
          const items = [];
          const entries = getDirectoryEntries(dirPath);
          
          for (const entry of entries) {
            if (entry.isDirectory) {
              const subItems = traverseDirectory(entry.path);
              items.push(...subItems);
            } else {
              items.push(entry.path);
            }
          }
          
          return items;
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('traverseDirectory');
      // for: 1, if: 1+1(嵌套) = 2, 递归调用: 1, 总计: 4
      expect(func.complexity).toBe(4);
    });

    it('应该正确处理表达式求值的递归解析器', async () => {
      const code = `
        function evaluateExpression(expr) {
          if (typeof expr === 'number') {
            return expr;
          }
          
          if (expr.type === 'binary') {
            const left = evaluateExpression(expr.left);
            const right = evaluateExpression(expr.right);
            
            switch (expr.operator) {
              case '+':
                return left + right;
              case '-':
                return left - right;
              case '*':
                return left * right;
              case '/':
                return left / right;
              default:
                throw new Error(\`Unknown operator: \${expr.operator}\`);
            }
          }
          
          throw new Error(\`Unknown expression type: \${expr.type}\`);
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('evaluateExpression');
      // 多个if语句, switch语句, 递归调用, 复杂度应该较高
      expect(func.complexity).toBeGreaterThanOrEqual(8);
    });
  });

  describe('详细模式验证', () => {
    it('应该在详细模式下提供递归调用的详细信息', async () => {
      const factory = createLightweightFactory();
      const detailCalculator = new ComplexityCalculator({ enableDetails: true }, factory);
      
      try {
        const code = `
          function countdown(n) {
            if (n <= 0) {
              return;
            }
            console.log(n);
            countdown(n - 1);
          }
        `;

        const result = await detailCalculator.calculateCode(code, 'test.ts');
        expect(result).toHaveLength(1);
        
        const func = result[0];
        // 验证复杂度为2（if + 递归调用）
        expect(func.complexity).toBe(2);
        
        // 验证详细信息中包含递归调用相关的规则应用
        if (func.details) {
          const recursiveDetails = func.details.find(detail => 
            (detail.rule && detail.rule.includes('recursive-call')) || 
            (detail.description && detail.description.includes('Recursive'))
          );
          // 至少应该有一些详细信息
          expect(func.details.length).toBeGreaterThan(0);
        }
      } finally {
        await detailCalculator.dispose();
      }
    });
  });

  describe('边界条件', () => {
    it('应该正确处理非递归的函数调用', async () => {
      const code = `
        function processData(data) {
          const result = transformData(data);
          return validateData(result);
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.complexity).toBe(0); // 没有递归调用，复杂度为0
    });

    it('应该正确处理相互递归（间接递归）', async () => {
      const code = `
        function isEven(n) {
          if (n === 0) return true;
          return isOdd(n - 1);
        }
        
        function isOdd(n) {
          if (n === 0) return false;
          return isEven(n - 1);
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(2);
      
      // 两个函数都不包含直接递归调用
      const evenFunc = result.find(f => f.name === 'isEven');
      const oddFunc = result.find(f => f.name === 'isOdd');
      
      expect(evenFunc?.complexity).toBe(1); // 只有if语句
      expect(oddFunc?.complexity).toBe(1); // 只有if语句
    });

    it('应该正确处理匿名递归函数', async () => {
      const code = `
        const factorial = function factorial(n) {
          if (n <= 1) return 1;
          return n * factorial(n - 1);
        };
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('factorial');
      // if: 1, 递归调用: 1, 总计: 2
      expect(func.complexity).toBe(2);
    });

    it('应该正确处理箭头函数中的递归', async () => {
      const code = `
        const sum = (arr) => {
          if (arr.length === 0) return 0;
          return arr[0] + sum(arr.slice(1));
        };
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('sum');
      // if: 1, 递归调用: 1, 总计: 2
      expect(func.complexity).toBe(2);
    });
  });
});