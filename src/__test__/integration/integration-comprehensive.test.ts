import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { CLITestingUtils, CLITestResult } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';
import { ComplexityCalculator } from '../../core/calculator';
import { ConfigManager } from '../../config/manager';
import { BaselineManager } from '../../baseline/manager';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * 集成测试综合套件 - Task 10.2
 * 
 * 这个测试套件验证所有组件之间的集成，包括：
 * - 配置和机制集成测试
 * - 代码测试验证机制
 * - 端到端测试
 * - 各模块间的协作测试
 */
describe('Integration Comprehensive Tests - Task 10.2', () => {
  let instances: CLITestResult[] = [];

  beforeEach(async () => {
    instances = [];
    await CLITestingUtils.cleanupAll();
  });

  afterEach(async () => {
    for (const instance of instances) {
      try {
        await CLITestingUtils.cleanup(instance);
      } catch (error) {
        console.warn('Cleanup warning:', error);
      }
    }
    instances = [];
    await CLITestingUtils.cleanupAll();
  });

  /**
   * 配置和机制集成测试
   * 验证配置系统与其他组件的集成
   */
  describe('Configuration and Mechanism Integration', () => {
    test('应该正确集成配置文件和CLI参数', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建配置文件
        const configFile = path.join(tempDir, '.complexityrc.json');
        await fs.writeFile(configFile, JSON.stringify({
          threshold: 15,
          enableMixedLogicOperatorPenalty: true,
          recursionChainThreshold: 5,
          enableDetails: false
        }, null, 2));

        const testFile = path.join(tempDir, 'test.ts');
        await fs.writeFile(testFile, `
          export function configTest(input: any): any {
            if (input.condition1) {
              if (input.condition2) {
                if (input.condition3) {
                  return 'deep';
                } else {
                  return 'medium';
                }
              } else {
                return 'shallow';
              }
            } else {
              return null;
            }
          }
        `);

        // CLI参数应该覆盖配置文件
        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--fail-on', '20',
          '--details', // 覆盖配置文件中的enableDetails: false
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        
        // 验证CLI参数生效（显示详细信息）
        expect(instance.stdout).toContain('Details:');
        expect(instance.stdout).toContain('configTest');
      });
    });

    test('应该正确集成基线管理和复杂度计算', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'baseline-test.ts');
        await fs.writeFile(testFile, `
          export function baselineFunction(input: any): any {
            if (input.type === 'A') {
              if (input.subType === '1') {
                return 'A1';
              } else if (input.subType === '2') {
                return 'A2';
              } else {
                return 'A_other';
              }
            } else if (input.type === 'B') {
              return 'B';
            } else {
              return 'unknown';
            }
          }
        `);

        // 创建基线
        const createBaselineInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--create-baseline',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(createBaselineInstance);

        let exitCode = await createBaselineInstance.waitForExit();
        expect(exitCode).toBe(0);

        // 验证基线文件已创建
        const baselineFiles = await fs.readdir(tempDir);
        const baselineFile = baselineFiles.find(f => f.includes('baseline') || f.includes('.complexity'));
        expect(baselineFile).toBeDefined();

        // 运行与基线比较
        const compareInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--compare-baseline',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(compareInstance);

        exitCode = await compareInstance.waitForExit();
        expect(exitCode).toBe(0);
        expect(compareInstance.stdout).toContain('baselineFunction');
      });
    });

    test('应该正确集成缓存系统和复杂度计算', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'cache-test.ts');
        await fs.writeFile(testFile, `
          export function cacheTest(input: any): any {
            let result = null;
            
            for (let i = 0; i < input.iterations; i++) {
              if (input.conditions[i]) {
                if (input.conditions[i].type === 'complex') {
                  result = processComplex(input.conditions[i]);
                } else if (input.conditions[i].type === 'simple') {
                  result = processSimple(input.conditions[i]);
                } else {
                  result = 'unknown';
                }
              }
            }
            
            return result;
          }
          
          function processComplex(condition: any): any {
            if (condition.level > 3) {
              return condition.value * 2;
            } else {
              return condition.value;
            }
          }
          
          function processSimple(condition: any): any {
            return condition.value || 'default';
          }
        `);

        // 第一次运行（创建缓存）
        const firstRun = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(firstRun);

        const exitCode1 = await firstRun.waitForExit();
        expect(exitCode1).toBe(0);
        
        const firstResult = JSON.parse(firstRun.stdout);
        expect(firstResult.length).toBe(3); // cacheTest, processComplex, processSimple

        // 第二次运行（使用缓存）
        const secondRun = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(secondRun);

        const exitCode2 = await secondRun.waitForExit();
        expect(exitCode2).toBe(0);
        
        const secondResult = JSON.parse(secondRun.stdout);
        
        // 结果应该相同（验证缓存正确性）
        expect(secondResult).toEqual(firstResult);
      });
    });

    test('应该正确集成插件系统和核心功能', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'plugin-test.ts');
        await fs.writeFile(testFile, `
          export function pluginTest(input: any): any {
            // JSX相关测试（如果插件系统支持JSX规则）
            const handleClick = () => {
              if (input.isButton) {
                if (input.variant === 'primary') {
                  return 'primary-click';
                } else if (input.variant === 'secondary') {
                  return 'secondary-click';
                } else {
                  return 'default-click';
                }
              } else {
                return 'non-button-click';
              }
            };
            
            return handleClick();
          }
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        
        const result = JSON.parse(instance.stdout);
        expect(result.some((r: any) => r.name === 'pluginTest')).toBe(true);
      });
    });
  });

  /**
   * 代码测试验证机制
   * 验证复杂度计算的准确性和一致性
   */
  describe('Code Testing Verification Mechanism', () => {
    test('应该验证不同格式输出的一致性', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'consistency-test.ts');
        await fs.writeFile(testFile, `
          export function consistencyTest(input: any): any {
            if (input.flag1) {
              if (input.flag2) {
                if (input.flag3) {
                  return 'all-flags';
                } else {
                  return 'two-flags';
                }
              } else {
                return 'one-flag';
              }
            } else {
              return 'no-flags';
            }
          }
          
          export function simpleTest(): string {
            return 'simple';
          }
        `);

        // JSON输出
        const jsonInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(jsonInstance);

        const jsonExitCode = await jsonInstance.waitForExit();
        expect(jsonExitCode).toBe(0);
        const jsonResult = JSON.parse(jsonInstance.stdout);

        // 文本输出
        const textInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--output', 'text',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(textInstance);

        const textExitCode = await textInstance.waitForExit();
        expect(textExitCode).toBe(0);

        // 验证函数数量一致
        expect(jsonResult.length).toBe(2);
        
        // 验证复杂度值在文本输出中也存在
        const consistencyFunction = jsonResult.find((r: any) => r.name === 'consistencyTest');
        const simpleFunction = jsonResult.find((r: any) => r.name === 'simpleTest');
        
        expect(consistencyFunction).toBeDefined();
        expect(simpleFunction).toBeDefined();
        
        expect(textInstance.stdout).toContain('consistencyTest');
        expect(textInstance.stdout).toContain('simpleTest');
        
        // 复杂度值应该在文本输出中体现
        expect(textInstance.stdout).toContain(consistencyFunction.complexity.toString());
        expect(textInstance.stdout).toContain(simpleFunction.complexity.toString());
      });
    });

    test('应该验证详细输出和简单输出的相关性', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'detail-consistency.ts');
        await fs.writeFile(testFile, `
          export function detailTest(input: any): any {
            if (input.condition1) {        // +1
              if (input.condition2) {      // +2
                if (input.condition3) {    // +3
                  return 'deep';
                } else {                  // +1
                  return 'medium';
                }
              } else {                    // +1
                return 'shallow';
              }
            } else {                      // +1
              return null;
            }
          }
        `);

        // 简单输出
        const simpleInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(simpleInstance);

        const simpleExitCode = await simpleInstance.waitForExit();
        expect(simpleExitCode).toBe(0);
        const simpleResult = JSON.parse(simpleInstance.stdout);

        // 详细输出
        const detailInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--details',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(detailInstance);

        const detailExitCode = await detailInstance.waitForExit();
        expect(detailExitCode).toBe(0);
        const detailResult = JSON.parse(detailInstance.stdout);

        // 验证基本信息一致
        expect(detailResult.length).toBe(simpleResult.length);
        
        const simpleFunction = simpleResult.find((r: any) => r.name === 'detailTest');
        const detailFunction = detailResult.find((r: any) => r.name === 'detailTest');
        
        expect(simpleFunction.complexity).toBe(detailFunction.complexity);
        expect(simpleFunction.name).toBe(detailFunction.name);
        
        // 详细输出应该包含额外信息
        expect(detailFunction.details).toBeDefined();
        expect(Array.isArray(detailFunction.details)).toBe(true);
        expect(detailFunction.details.length).toBeGreaterThan(0);
      });
    });

    test('应该验证混合逻辑运算符规则的集成', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'mixed-logic.ts');
        await fs.writeFile(testFile, `
          export function mixedLogicTest(input: any): boolean {
            // 混合逻辑运算符测试
            if (input.a && input.b || input.c && input.d) {
              return true;
            } else if (input.x || input.y && input.z) {
              return false;
            } else {
              return input.default || false;
            }
          }
        `);

        // 不启用混合逻辑运算符惩罚
        const normalInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(normalInstance);

        const normalExitCode = await normalInstance.waitForExit();
        expect(normalExitCode).toBe(0);
        const normalResult = JSON.parse(normalInstance.stdout);

        // 启用混合逻辑运算符惩罚
        const penaltyInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--enable-mixed-logic-penalty',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(penaltyInstance);

        const penaltyExitCode = await penaltyInstance.waitForExit();
        expect(penaltyExitCode).toBe(0);
        const penaltyResult = JSON.parse(penaltyInstance.stdout);

        const normalFunction = normalResult.find((r: any) => r.name === 'mixedLogicTest');
        const penaltyFunction = penaltyResult.find((r: any) => r.name === 'mixedLogicTest');

        // 启用惩罚后复杂度应该更高
        expect(penaltyFunction.complexity).toBeGreaterThanOrEqual(normalFunction.complexity);
      });
    });

    test('应该验证上下文显示功能的集成', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'context-test.ts');
        await fs.writeFile(testFile, `
          export function contextTest(input: any): any {
            if (input.showContext) {
              if (input.level === 'high') {
                return 'high-context';
              } else if (input.level === 'medium') {
                return 'medium-context';
              } else {
                return 'low-context';
              }
            } else {
              return 'no-context';
            }
          }
        `);

        // 带上下文的详细输出
        const contextInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--details',
          '--show-context',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(contextInstance);

        const exitCode = await contextInstance.waitForExit();
        expect(exitCode).toBe(0);
        
        // 验证输出包含代码上下文
        expect(contextInstance.stdout).toContain('contextTest');
        // 验证包含代码行（上下文显示）
        expect(contextInstance.stdout).toMatch(/\d+\s*\|/); // 行号格式
      });
    });
  });

  /**
   * 端到端测试
   * 验证完整的用户工作流程
   */
  describe('End-to-End Testing', () => {
    test('应该支持完整的项目分析工作流', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建一个模拟项目结构
        const srcDir = path.join(tempDir, 'src');
        await fs.mkdir(srcDir, { recursive: true });
        
        const componentsDir = path.join(srcDir, 'components');
        await fs.mkdir(componentsDir, { recursive: true });
        
        const utilsDir = path.join(srcDir, 'utils');
        await fs.mkdir(utilsDir, { recursive: true });

        // 创建项目文件
        await fs.writeFile(path.join(srcDir, 'index.ts'), `
          import { processData } from './utils/processor';
          import { UserComponent } from './components/UserComponent';
          
          export function main(data: any[]): any {
            const processed = processData(data);
            
            if (processed.length > 0) {
              return new UserComponent(processed);
            } else {
              return null;
            }
          }
        `);

        await fs.writeFile(path.join(utilsDir, 'processor.ts'), `
          export function processData(data: any[]): any[] {
            return data.filter(item => {
              if (item.type === 'user') {
                if (item.active) {
                  if (item.permissions.includes('read')) {
                    return true;
                  } else {
                    return false;
                  }
                } else {
                  return false;
                }
              } else if (item.type === 'admin') {
                return item.active;
              } else {
                return false;
              }
            });
          }
        `);

        await fs.writeFile(path.join(componentsDir, 'UserComponent.ts'), `
          export class UserComponent {
            constructor(private users: any[]) {}
            
            public render(): string {
              let output = '<div>';
              
              for (const user of this.users) {
                if (user.visible !== false) {
                  if (user.type === 'premium') {
                    output += '<div class="premium">' + user.name + '</div>';
                  } else if (user.type === 'standard') {
                    output += '<div class="standard">' + user.name + '</div>';
                  } else {
                    output += '<div class="basic">' + user.name + '</div>';
                  }
                }
              }
              
              output += '</div>';
              return output;
            }
            
            public getUserCount(): number {
              return this.users.length;
            }
          }
        `);

        // 创建配置文件
        await fs.writeFile(path.join(tempDir, '.complexityrc.json'), JSON.stringify({
          threshold: 10,
          enableDetails: true,
          patterns: ['src/**/*.ts'],
          exclude: ['**/*.test.ts', '**/node_modules/**']
        }, null, 2));

        // 1. 分析整个项目
        const projectInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          path.join(srcDir, '**', '*.ts')
        ], { 
          timeout: 15000,
          cwd: tempDir
        });
        instances.push(projectInstance);

        let exitCode = await projectInstance.waitForExit();
        expect(exitCode).toBe(0);
        
        const projectResult = JSON.parse(projectInstance.stdout);
        expect(projectResult.length).toBeGreaterThan(0);
        
        // 验证找到了所有预期的函数
        const functionNames = projectResult.map((r: any) => r.name);
        expect(functionNames).toContain('main');
        expect(functionNames).toContain('processData');
        expect(functionNames).toContain('render');
        expect(functionNames).toContain('getUserCount');

        // 2. 创建基线
        const baselineInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--create-baseline',
          path.join(srcDir, '**', '*.ts')
        ], { 
          timeout: 15000,
          cwd: tempDir
        });
        instances.push(baselineInstance);

        exitCode = await baselineInstance.waitForExit();
        expect(exitCode).toBe(0);

        // 3. 分析单个文件并显示详细信息
        const detailInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--details',
          '--show-context',
          path.join(utilsDir, 'processor.ts')
        ], { 
          timeout: 10000,
          cwd: tempDir
        });
        instances.push(detailInstance);

        exitCode = await detailInstance.waitForExit();
        expect(exitCode).toBe(0);
        expect(detailInstance.stdout).toContain('processData');
        expect(detailInstance.stdout).toContain('Details:');

        // 4. HTML报告生成
        const htmlInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--output', 'html',
          '--output-file', path.join(tempDir, 'report.html'),
          path.join(srcDir, '**', '*.ts')
        ], { 
          timeout: 15000,
          cwd: tempDir
        });
        instances.push(htmlInstance);

        exitCode = await htmlInstance.waitForExit();
        expect(exitCode).toBe(0);
        
        // 验证HTML报告文件已创建
        const reportExists = await fs.access(path.join(tempDir, 'report.html')).then(() => true).catch(() => false);
        expect(reportExists).toBe(true);
      });
    });

    test('应该支持CI/CD工作流集成', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'ci-test.ts');
        await fs.writeFile(testFile, `
          export function ciTest(input: any): any {
            // 这个函数的复杂度刚好超过阈值
            if (input.environment === 'production') {
              if (input.featureFlags.enableNewFeature) {
                if (input.user.role === 'admin') {
                  return 'admin-production-new';
                } else if (input.user.role === 'user') {
                  return 'user-production-new';
                } else {
                  return 'guest-production-new';
                }
              } else {
                if (input.user.role === 'admin') {
                  return 'admin-production-old';
                } else {
                  return 'user-production-old';
                }
              }
            } else if (input.environment === 'staging') {
              if (input.user.role === 'admin') {
                return 'admin-staging';
              } else {
                return 'user-staging';
              }
            } else {
              return 'development';
            }
          }
        `);

        // 模拟CI环境检查
        const ciInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--fail-on', '10',
          '--format=json',
          testFile
        ], { 
          timeout: 10000,
          cwd: tempDir,
          env: { ...process.env, CI: 'true' }
        });
        instances.push(ciInstance);

        const exitCode = await ciInstance.waitForExit();
        const result = JSON.parse(ciInstance.stdout);
        const ciFunction = result.find((r: any) => r.name === 'ciTest');
        
        // 如果复杂度超过阈值，CI应该失败
        if (ciFunction.complexity > 10) {
          expect(exitCode).not.toBe(0);
        } else {
          expect(exitCode).toBe(0);
        }
      });
    });

    test('应该支持Web UI集成', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'ui-test.ts');
        await fs.writeFile(testFile, `
          export function uiTest(input: any): any {
            if (input.showUI) {
              if (input.theme === 'dark') {
                return 'dark-ui';
              } else if (input.theme === 'light') {
                return 'light-ui';
              } else {
                return 'default-ui';
              }
            } else {
              return 'no-ui';
            }
          }
        `);

        // 启动UI服务器并分析文件
        const uiInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--ui',
          '--open=false',
          '--port', '3001',
          testFile
        ], { 
          timeout: 15000,
          cwd: tempDir
        });
        instances.push(uiInstance);

        // 等待服务器启动
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查是否有服务器启动的迹象
        expect(uiInstance.stdout || uiInstance.stderr).toMatch(/server|ui|port|3001/i);
        
        // 清理：强制停止实例
        try {
          if (uiInstance.process && !uiInstance.process.killed) {
            uiInstance.process.kill('SIGTERM');
          }
        } catch (error) {
          console.warn('Failed to kill UI process:', error);
        }
      });
    });

    test('应该支持大型项目的增量分析', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建大型项目结构
        const srcDir = path.join(tempDir, 'src');
        await fs.mkdir(srcDir, { recursive: true });
        
        const modules = ['auth', 'user', 'product', 'order', 'payment'];
        
        // 为每个模块创建文件
        for (const module of modules) {
          const moduleDir = path.join(srcDir, module);
          await fs.mkdir(moduleDir, { recursive: true });
          
          // 创建模块文件
          await fs.writeFile(path.join(moduleDir, `${module}.ts`), `
            export class ${module.charAt(0).toUpperCase() + module.slice(1)}Service {
              public process${module.charAt(0).toUpperCase() + module.slice(1)}(input: any): any {
                if (input.type === 'create') {
                  if (input.data && input.data.valid) {
                    return this.create${module.charAt(0).toUpperCase() + module.slice(1)}(input.data);
                  } else {
                    throw new Error('Invalid data');
                  }
                } else if (input.type === 'update') {
                  if (input.id && input.data) {
                    return this.update${module.charAt(0).toUpperCase() + module.slice(1)}(input.id, input.data);
                  } else {
                    throw new Error('Missing id or data');
                  }
                } else if (input.type === 'delete') {
                  if (input.id) {
                    return this.delete${module.charAt(0).toUpperCase() + module.slice(1)}(input.id);
                  } else {
                    throw new Error('Missing id');
                  }
                } else {
                  throw new Error('Unknown operation');
                }
              }
              
              private create${module.charAt(0).toUpperCase() + module.slice(1)}(data: any): any {
                return { id: Date.now(), ...data };
              }
              
              private update${module.charAt(0).toUpperCase() + module.slice(1)}(id: any, data: any): any {
                return { id, ...data, updated: Date.now() };
              }
              
              private delete${module.charAt(0).toUpperCase() + module.slice(1)}(id: any): any {
                return { deleted: true, id };
              }
            }
          `);
        }

        // 第一次完整分析
        const fullAnalysisInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          path.join(srcDir, '**', '*.ts')
        ], { 
          timeout: 20000,
          cwd: tempDir
        });
        instances.push(fullAnalysisInstance);

        const exitCode = await fullAnalysisInstance.waitForExit();
        expect(exitCode).toBe(0);
        
        const fullResult = JSON.parse(fullAnalysisInstance.stdout);
        expect(fullResult.length).toBe(modules.length * 4); // 每个模块4个方法

        // 修改其中一个文件，测试增量分析
        await fs.writeFile(path.join(srcDir, 'auth', 'auth.ts'), `
          export class AuthService {
            public processAuth(input: any): any {
              // 增加了复杂度
              if (input.type === 'login') {
                if (input.credentials) {
                  if (input.credentials.username && input.credentials.password) {
                    if (input.twoFactor) {
                      if (input.twoFactor.enabled) {
                        return this.loginWithTwoFactor(input.credentials, input.twoFactor);
                      } else {
                        return this.login(input.credentials);
                      }
                    } else {
                      return this.login(input.credentials);
                    }
                  } else {
                    throw new Error('Missing credentials');
                  }
                } else {
                  throw new Error('No credentials provided');
                }
              } else {
                return this.processAuth(input); // 递归调用
              }
            }
            
            private login(credentials: any): any {
              return { token: 'fake-token', user: credentials.username };
            }
            
            private loginWithTwoFactor(credentials: any, twoFactor: any): any {
              return { token: 'fake-2fa-token', user: credentials.username };
            }
          }
        `);

        // 重新分析修改后的项目
        const incrementalInstance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format=json',
          path.join(srcDir, '**', '*.ts')
        ], { 
          timeout: 20000,
          cwd: tempDir
        });
        instances.push(incrementalInstance);

        const incrementalExitCode = await incrementalInstance.waitForExit();
        expect(incrementalExitCode).toBe(0);
        
        const incrementalResult = JSON.parse(incrementalInstance.stdout);
        
        // 验证auth模块的变化被检测到
        const authFunctions = incrementalResult.filter((r: any) => r.name.toLowerCase().includes('auth'));
        expect(authFunctions.length).toBeGreaterThan(0);
        
        // processAuth函数应该有更高的复杂度
        const processAuthFunction = authFunctions.find((r: any) => r.name === 'processAuth');
        expect(processAuthFunction).toBeDefined();
        expect(processAuthFunction.complexity).toBeGreaterThan(5);
      });
    });
  });

  /**
   * 性能和可扩展性集成测试
   */
  describe('Performance and Scalability Integration', () => {
    test('应该在高并发情况下保持稳定性', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建多个测试文件
        const testFiles = [];
        for (let i = 0; i < 5; i++) {
          const fileName = path.join(tempDir, `concurrent${i}.ts`);
          await fs.writeFile(fileName, `
            export function concurrent${i}Test(input: any): any {
              if (input.index === ${i}) {
                if (input.process) {
                  return 'processed-${i}';
                } else if (input.skip) {
                  return 'skipped-${i}';
                } else {
                  return 'default-${i}';
                }
              } else {
                return null;
              }
            }
          `);
          testFiles.push(fileName);
        }

        // 并发启动多个分析实例
        const concurrentPromises = testFiles.map((file, index) =>
          CLITestingUtils.renderCLI('bun', [
            'run', 'src/cli/index.ts',
            '--format=json',
            file
          ], { 
            timeout: 15000,
            cwd: tempDir
          })
        );

        const concurrentInstances = await Promise.all(concurrentPromises);
        instances.push(...concurrentInstances);

        // 等待所有实例完成
        const exitCodes = await Promise.all(
          concurrentInstances.map(instance => instance.waitForExit())
        );

        // 验证所有实例都成功完成
        exitCodes.forEach((exitCode, index) => {
          expect(exitCode).toBe(0);
        });

        // 验证结果的一致性
        const results = concurrentInstances.map(instance => JSON.parse(instance.stdout));
        results.forEach((result, index) => {
          expect(result.length).toBe(1);
          expect(result[0].name).toBe(`concurrent${index}Test`);
        });
      });
    });
  });
});