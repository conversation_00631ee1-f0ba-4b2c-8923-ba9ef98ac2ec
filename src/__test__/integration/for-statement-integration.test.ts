/**
 * ForStatementRule集成测试
 * 验证ForStatementRule与ComplexityCalculator的完整集成
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ComplexityCalculator } from '../../core/calculator';
import { createLightweightFactory } from '../../core/calculator-factory';

describe('ForStatementRule Integration', () => {
  let calculator: ComplexityCalculator;

  beforeEach(async () => {
    const factory = createLightweightFactory();
    calculator = new ComplexityCalculator({}, factory);
  });

  afterEach(async () => {
    await calculator.dispose();
  });

  describe('传统for循环', () => {
    it('应该正确计算简单for循环的复杂度', async () => {
      const code = `
        function testFunction() {
          for (let i = 0; i < 10; i++) {
            console.log(i);
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      expect(func.complexity).toBe(1); // for循环贡献1点复杂度
    });

    it('应该正确计算嵌套for循环的复杂度', async () => {
      const code = `
        function testFunction() {
          for (let i = 0; i < 10; i++) {
            for (let j = 0; j < 5; j++) {
              console.log(i, j);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // 外层for: 1, 内层for: 1 + 嵌套惩罚1 = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });
  });

  describe('for-in循环', () => {
    it('应该正确计算for-in循环的复杂度', async () => {
      const code = `
        function testFunction() {
          for (const key in object) {
            console.log(key, object[key]);
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      expect(func.complexity).toBe(1); // for-in循环贡献1点复杂度
    });

    it('应该正确计算嵌套for-in循环的复杂度', async () => {
      const code = `
        function testFunction() {
          if (condition) {
            for (const key in object) {
              process(key);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      // if: 1, for-in: 1 + 嵌套惩罚1 = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });
  });

  describe('for-of循环', () => {
    it('应该正确计算for-of循环的复杂度', async () => {
      const code = `
        function testFunction() {
          for (const item of array) {
            console.log(item);
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      expect(func.complexity).toBe(1); // for-of循环贡献1点复杂度
    });

    it('应该正确计算嵌套for-of循环的复杂度', async () => {
      const code = `
        function testFunction() {
          for (const outer of outerArray) {
            for (const inner of outer.items) {
              process(inner);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      // 外层for-of: 1, 内层for-of: 1 + 嵌套惩罚1 = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });
  });

  describe('复杂条件和更新表达式', () => {
    it('应该正确处理复杂条件的for循环', async () => {
      const code = `
        function testFunction() {
          for (let i = 0; i < array.length && isValid(i); i++) {
            process(array[i]);
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // for循环基础复杂度1 + 逻辑运算符&&的复杂度1 = 2
      expect(func.complexity).toBeGreaterThanOrEqual(2);
    });

    it('应该正确处理复杂更新表达式的for循环', async () => {
      const code = `
        function testFunction() {
          for (let i = 0; i < 10; i = calculateNext(i)) {
            process(i);
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      expect(func.complexity).toBe(1); // for循环基础复杂度，函数调用在更新表达式中不额外计算复杂度（目前）
    });
  });

  describe('混合复杂度场景', () => {
    it('应该正确计算包含for循环和if语句的复杂度', async () => {
      const code = `
        function testFunction() {
          for (let i = 0; i < 10; i++) {
            if (i % 2 === 0) {
              console.log('even:', i);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // for: 1, if: 1 + 嵌套惩罚1 = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });

    it('应该正确处理深度嵌套的复合场景', async () => {
      const code = `
        function complexFunction() {
          for (const outer of data) {
            if (outer.isActive) {
              for (const inner of outer.items) {
                if (inner.shouldProcess && inner.value > 0) {
                  processItem(inner);
                }
              }
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('complexFunction');
      // 这是一个复杂的场景，验证总复杂度大于5
      expect(func.complexity).toBeGreaterThan(5);
    });
  });

  describe('与现有规则的协作', () => {
    it('应该与LogicalOperatorRule正确协作', async () => {
      const code = `
        function testFunction() {
          for (let i = 0; i < 10 && valid || fallback; i++) {
            process(i);
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // 验证复杂度包含了for循环和逻辑运算符的贡献
      expect(func.complexity).toBeGreaterThan(2);
    });

    it('应该与IfStatementRule正确协作', async () => {
      const code = `
        function testFunction() {
          for (const item of items) {
            if (item.isValid) {
              if (item.needsProcessing) {
                processItem(item);
              }
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // for: 1, 第一个if: 1+1(嵌套) = 2, 第二个if: 1+2(嵌套) = 3, 总计: 6
      expect(func.complexity).toBe(6);
    });
  });

  describe('详细模式验证', () => {
    it('应该在详细模式下提供for循环的详细信息', async () => {
      const factory = createLightweightFactory();
      const detailCalculator = new ComplexityCalculator({ enableDetails: true }, factory);
      
      try {
        const code = `
          function testFunction() {
            for (let i = 0; i < 10; i++) {
              if (i % 2 === 0) {
                console.log(i);
              }
            }
          }
        `;

        const result = await detailCalculator.calculateCode(code, 'test.ts');
        expect(result).toHaveLength(1);
        
        const func = result[0];
        // 验证复杂度包含了for循环和if语句的贡献
        expect(func.complexity).toBe(3);
        
        // 验证详细信息中包含for循环相关的规则应用
        if (func.details) {
          const forDetails = func.details.find(detail => 
            (detail.rule && detail.rule.includes('for-statement')) || 
            (detail.description && detail.description.includes('For loop'))
          );
          // 至少应该有一些详细信息
          expect(func.details.length).toBeGreaterThan(0);
        }
      } finally {
        await detailCalculator.dispose();
      }
    });
  });

  describe('边界条件', () => {
    it('应该正确处理空的for循环', async () => {
      const code = `
        function testFunction() {
          for (let i = 0; i < 10; i++) {
            // 空循环体
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      expect(func.complexity).toBe(1);
    });

    it('应该正确处理无限循环', async () => {
      const code = `
        function testFunction() {
          for (;;) {
            if (shouldBreak) break;
            process();
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // for: 1, if: 1 + 嵌套惩罚1 = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });
  });
});