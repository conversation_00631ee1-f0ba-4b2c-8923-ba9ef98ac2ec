/**
 * IfStatementRule集成测试
 * 验证IfStatementRule与ComplexityCalculator的完整集成
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ComplexityCalculator } from '../../core/calculator';
import { createLightweightFactory } from '../../core/calculator-factory';

describe('IfStatementRule Integration', () => {
  let calculator: ComplexityCalculator;

  beforeEach(async () => {
    const factory = createLightweightFactory();
    calculator = new ComplexityCalculator({}, factory);
  });

  afterEach(async () => {
    await calculator.dispose();
  });

  describe('基础if语句', () => {
    it('应该正确计算简单if语句的复杂度', async () => {
      const code = `
        function testFunction() {
          if (condition) {
            doSomething();
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      expect(func.complexity).toBe(1); // if语句贡献1点复杂度
    });

    it('应该正确计算嵌套if语句的复杂度', async () => {
      const code = `
        function testFunction() {
          if (condition1) {
            if (condition2) {
              doSomething();
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // 外层if: 1, 内层if: 1 + 嵌套惩罚1 = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });
  });

  describe('else-if链', () => {
    it('应该正确计算else-if链的复杂度', async () => {
      const code = `
        function testFunction() {
          if (type === 'A') {
            handleA();
          } else if (type === 'B') {
            handleB();
          } else if (type === 'C') {
            handleC();
          } else {
            handleDefault();
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // 验证复杂度符合预期（可能包含其他规则的贡献）
      expect(func.complexity).toBeGreaterThanOrEqual(3);
    });
  });

  describe('混合复杂度场景', () => {
    it('应该正确计算包含if和逻辑运算符的复杂度', async () => {
      const code = `
        function testFunction() {
          if (condition1 && condition2) {
            doSomething();
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // if: 1 + &&: 1 = 总计2
      expect(func.complexity).toBe(2);
    });

    it('应该正确处理深度嵌套的复合场景', async () => {
      const code = `
        function complexFunction() {
          if (level1) {
            if (level2) {
              if (level3 && level3.property) {
                processData();
              } else if (fallback) {
                processFallback();
              }
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('complexFunction');
      // 这是一个复杂的场景，验证总复杂度大于5
      expect(func.complexity).toBeGreaterThan(5);
    });
  });

  describe('与现有规则的协作', () => {
    it('应该与LogicalOperatorRule正确协作', async () => {
      const code = `
        function testFunction() {
          if (a && b || c) {
            if (d) {
              return true;
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // 验证复杂度包含了if语句和逻辑运算符的贡献
      expect(func.complexity).toBeGreaterThan(2);
    });

    it('应该与RecursionComplexityRule正确协作', async () => {
      const code = `
        function recursiveFunction(n) {
          if (n <= 0) {
            return 1;
          }
          return n * recursiveFunction(n - 1);
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('recursiveFunction');
      // 验证复杂度包含了if语句和递归调用的贡献（if至少1分）
      expect(func.complexity).toBeGreaterThanOrEqual(1);
    });
  });

  describe('详细模式验证', () => {
    it('应该在详细模式下提供if语句的详细信息', async () => {
      const factory = createLightweightFactory();
      const detailCalculator = new ComplexityCalculator({ enableDetails: true }, factory);
      
      try {
        const code = `
          function testFunction() {
            if (condition1) {
              action1();
            } else if (condition2) {
              action2();
            } else {
              defaultAction();
            }
          }
        `;

        const result = await detailCalculator.calculateCode(code, 'test.ts');
        expect(result).toHaveLength(1);
        
        const func = result[0];
        // 验证复杂度包含了if和else-if的贡献（可能还有其他规则）
        expect(func.complexity).toBeGreaterThanOrEqual(2);
        
        // 验证详细信息中包含if语句相关的规则应用
        if (func.details) {
          const ifDetails = func.details.find(detail => 
            (detail.rule && detail.rule.includes('if-statement')) || 
            (detail.description && detail.description.includes('If statement'))
          );
          // 至少应该有一些详细信息
          expect(func.details.length).toBeGreaterThan(0);
        }
      } finally {
        await detailCalculator.dispose();
      }
    });
  });
});