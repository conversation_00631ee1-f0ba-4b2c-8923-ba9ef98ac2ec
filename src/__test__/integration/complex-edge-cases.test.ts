import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { CLITestingUtils, CLITestResult, TestTimeoutError, BufferOverflowError } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';
import { performance } from 'perf_hooks';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as os from 'os';

/**
 * 复杂场景和边界条件测试
 * 测试 CLI 在各种极端和异常情况下的行为
 */
describe('Complex Edge Cases and Boundary Conditions', () => {
  let instances: CLITestResult[] = [];

  beforeEach(async () => {
    instances = [];
    await CLITestingUtils.cleanupAll();
  });

  afterEach(async () => {
    // 清理测试创建的实例
    for (const instance of instances) {
      try {
        await CLITestingUtils.cleanup(instance);
      } catch (error) {
        console.warn('Cleanup warning:', error);
      }
    }
    instances = [];
    
    // 确保所有进程都被清理
    await CLITestingUtils.cleanupAll();
  });

  /**
   * 错误处理和异常情况测试
   */
  describe('Error Handling and Exception Cases', () => {
    test('应该正确处理不存在的命令', async () => {
      await expect(
        CLITestingUtils.renderCLI('nonexistent-command-12345', ['--help'])
      ).rejects.toThrow('Executable not found');
    });

    test('应该正确处理无效的参数组合', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'test.ts');
        await fs.writeFile(testFile, 'function test() { return 1; }');

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--config', '/nonexistent/config.json',
          '--invalid-arg', 'invalid-value',
          testFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).not.toBe(0);
        expect(instance.stderr).toContain('unknown option');
      });
    });

    test('应该处理格式错误的 TypeScript 文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'malformed.ts');
        await fs.writeFile(testFile, `
          function broken( {
            if (condition {
              return broken syntax here
            }
          }
          
          class Invalid {
            method() {
              return "missing semicolon and brace"
          }
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          testFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        // CLI可能能够处理某些语法错误，检查是否有错误输出
        if (exitCode !== 0) {
          expect(instance.stderr.toLowerCase()).toMatch(/(syntax|parse|error)/i);
        } else {
          // 如果退出码为0，说明CLI能够处理语法错误
          expect(exitCode).toBe(0);
        }
      });
    });
  });

  /**
   * 大输出量和长时间运行场景测试
   */
  describe('Large Output and Long Running Scenarios', () => {
    test('应该处理大型项目的分析', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建一个包含多个文件的大型项目
        const projectDir = path.join(tempDir, 'large-project');
        await fs.mkdir(projectDir, { recursive: true });

        // 创建50个测试文件（减少数量以加快测试）
        const files: string[] = [];
        for (let i = 0; i < 50; i++) {
          const fileName = `file-${i}.ts`;
          const filePath = path.join(projectDir, fileName);
          
          // 创建具有一定复杂度的代码
          const content = `
            export function complexFunction${i}(param1: string, param2: number): boolean {
              let result = false;
              
              if (param1.length > 0) {
                for (let j = 0; j < param2; j++) {
                  if (j % 2 === 0) {
                    if (param1.includes('test')) {
                      result = true;
                    } else if (param1.includes('demo')) {
                      result = false;
                    } else {
                      result = !result;
                    }
                  } else {
                    switch (param1.charAt(j % param1.length)) {
                      case 'a':
                        result = true;
                        break;
                      case 'b':
                        result = false;
                        break;
                      default:
                        result = !result;
                    }
                  }
                }
              }
              
              return result && param2 > 10 ? !result : result;
            }
          `;
          
          await fs.writeFile(filePath, content);
          files.push(filePath);
        }

        // 测试分析大型项目
        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format', 'json',
          projectDir
        ], { 
          timeout: 30000, // 30秒超时
          maxBuffer: 5 * 1024 * 1024, // 5MB缓冲区
          cwd: process.cwd()
        });
        instances.push(instance);

        const startTime = performance.now();
        const exitCode = await instance.waitForExit(30000);
        const duration = performance.now() - startTime;

        // 检查结果，大型项目可能会有一些失败
        if (exitCode === 0) {
          expect(duration).toBeLessThan(30000); // 应该在30秒内完成
          expect(instance.stdout).toContain('complexFunction');
          // 验证输出的JSON格式
          expect(() => JSON.parse(instance.stdout)).not.toThrow();
        } else {
          // 如果失败，至少应该有错误信息
          expect(instance.stderr).toBeTruthy();
        }
      });
    });

    test('应该处理超时情况', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建一个正常的测试文件
        const testFile = path.join(tempDir, 'timeout-test.ts');
        await fs.writeFile(testFile, `
          export function normalFunction(): number {
            return 1 + 1;
          }
        `);

        await expect(
          CLITestingUtils.renderCLI('bun', [
            'run', 'src/cli/index.ts',
            '--format', 'json',
            testFile
          ], { 
            timeout: 1, // 极短的超时时间，应该会触发超时
            cwd: process.cwd()
          }).then(instance => {
            instances.push(instance);
            return instance.waitForExit(1000);  // 也使用短的等待时间
          })
        ).rejects.toThrow();
      });
    });
  });

  /**
   * 并发测试执行的稳定性测试
   */
  describe('Concurrent Execution Stability', () => {
    test('应该支持多个并发 CLI 实例', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const concurrentCount = 3; // 减少并发数量
        const testFiles: string[] = [];
        
        // 创建多个测试文件
        for (let i = 0; i < concurrentCount; i++) {
          const testFile = path.join(tempDir, `concurrent-${i}.ts`);
          await fs.writeFile(testFile, `
            export function concurrentFunction${i}(): number {
              let result = 0;
              for (let j = 0; j < 10; j++) {
                if (j % 2 === 0) {
                  result += j;
                } else {
                  result -= j;
                }
              }
              return result;
            }
          `);
          testFiles.push(testFile);
        }

        // 并发执行多个 CLI 实例
        const promises = testFiles.map(async (testFile, index) => {
          const instance = await CLITestingUtils.renderCLI('bun', [
            'run', 'src/cli/index.ts',
            '--format', 'json',
            testFile
          ], { 
            timeout: 10000,
            cwd: process.cwd()
          });
          instances.push(instance);
          
          const exitCode = await instance.waitForExit();
          return {
            index,
            exitCode,
            stdout: instance.stdout,
            stderr: instance.stderr
          };
        });

        const results = await Promise.all(promises);
        
        // 验证所有实例都成功完成
        for (const result of results) {
          expect(result.exitCode).toBe(0);
          // 验证输出包含函数名，对于JSON格式输出
          expect(result.stdout).toContain(`concurrentFunction${result.index}`);
        }
        
        // 验证进程管理
        expect(CLITestingUtils.getActiveProcessCount()).toBe(concurrentCount);
      });
    });

    test('应该处理并发实例的进程清理', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const concurrentCount = 2;
        const concurrentInstances: CLITestResult[] = [];
        
        // 创建测试文件
        const testFile = path.join(tempDir, 'cleanup-test.ts');
        await fs.writeFile(testFile, `
          export function testFunction(): string {
            return 'test';
          }
        `);

        // 并发启动多个实例
        for (let i = 0; i < concurrentCount; i++) {
          const instance = await CLITestingUtils.renderCLI('bun', [
            'run', 'src/index.ts',
            testFile
          ], { 
            timeout: 10000,
            cwd: process.cwd()
          });
          concurrentInstances.push(instance);
          instances.push(instance);
        }

        expect(CLITestingUtils.getActiveProcessCount()).toBe(concurrentCount);

        // 测试强制清理
        await CLITestingUtils.forceCleanupAll();
        
        // 等待一点时间让清理完成
        await new Promise(resolve => setTimeout(resolve, 100));
        
        expect(CLITestingUtils.getActiveProcessCount()).toBe(0);
      });
    });
  });

  /**
   * 跨平台兼容性测试
   */
  describe('Cross-Platform Compatibility', () => {
    test('应该正确处理不同平台的路径分隔符', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const subDir = path.join(tempDir, 'subdir');
        await fs.mkdir(subDir, { recursive: true });
        
        const testFile = path.join(subDir, 'platform-test.ts');
        await fs.writeFile(testFile, `
          export function platformFunction(): string {
            return process.platform;
          }
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format', 'json',
          testFile
        ], { 
          timeout: 10000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('platformFunction');
      });
    });

    test('应该处理长文件名和深层目录结构', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建深层目录结构
        const deepPath = path.join(
          tempDir, 
          'very', 'deep', 'directory', 'structure'
        );
        await fs.mkdir(deepPath, { recursive: true });
        
        // 创建长文件名（在合理范围内）
        const longFileName = 'this-is-a-long-filename-for-testing.ts';
        const testFile = path.join(deepPath, longFileName);
        
        await fs.writeFile(testFile, `
          export function functionWithLongName(): boolean {
            return true;
          }
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format', 'json',
          testFile
        ], { 
          timeout: 10000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('functionWithLongName');
      });
    });

    test('应该处理不同的换行符格式', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFiles = [
          { name: 'unix-linebreaks.ts', content: 'export function unixFunction(): string {\n  return "unix";\n}' },
          { name: 'windows-linebreaks.ts', content: 'export function windowsFunction(): string {\r\n  return "windows";\r\n}' },
          { name: 'mixed-linebreaks.ts', content: 'export function mixedFunction(): string {\r\n  return "mixed";\n}' }
        ];

        const createdFiles: string[] = [];
        for (const { name, content } of testFiles) {
          const testFile = path.join(tempDir, name);
          await fs.writeFile(testFile, content);
          createdFiles.push(testFile);
        }

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/cli/index.ts',
          '--format', 'json',
          ...createdFiles
        ], { 
          timeout: 10000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('unixFunction');
        expect(instance.stdout).toContain('windowsFunction');
        expect(instance.stdout).toContain('mixedFunction');
      });
    });
  });

  /**
   * 进程信号和中断处理测试
   */
  describe('Process Signal and Interruption Handling', () => {
    test('应该正确处理 SIGTERM 信号', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'signal-test.ts');
        await fs.writeFile(testFile, `
          export function signalTest(): string {
            return 'signal';
          }
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          testFile
        ], { 
          timeout: 10000,
          cwd: process.cwd()
        });
        instances.push(instance);

        // 等待一点时间让进程启动
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // 发送 SIGTERM 信号
        await instance.kill('SIGTERM');
        
        // 验证进程被正确终止
        expect(instance.isRunning).toBe(false);
      });
    });

    test('应该处理进程已经终止的清理请求', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'already-terminated.ts');
        await fs.writeFile(testFile, `
          export function alreadyTerminated(): string {
            return 'terminated';
          }
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          testFile
        ], { 
          timeout: 5000,
          cwd: process.cwd()
        });
        instances.push(instance);

        // 等待进程自然结束
        await instance.waitForExit();
        
        // 尝试再次终止已经结束的进程（应该不会抛出错误）
        await expect(instance.kill()).resolves.toBeUndefined();
      });
    });
  });

  /**
   * 性能基准和压力测试
   */
  describe('Performance Benchmarks and Stress Tests', () => {
    test('应该在合理时间内完成文件分析', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const fileCount = 10; // 减少文件数量
        const files: string[] = [];
        
        // 创建多个具有一定复杂度的文件
        for (let i = 0; i < fileCount; i++) {
          const testFile = path.join(tempDir, `perf-test-${i}.ts`);
          
          const content = `
            export class PerformanceTest${i} {
              private counter = 0;
              
              public complexMethod(input: any[]): any {
                const results = [];
                
                for (const item of input) {
                  if (typeof item === 'string') {
                    if (item.length > 5) {
                      results.push(item.toUpperCase());
                    } else if (item.length > 2) {
                      results.push(item.toLowerCase());
                    } else {
                      results.push(item.charAt(0));
                    }
                  } else if (typeof item === 'number') {
                    if (item > 100) {
                      results.push(item * 2);
                    } else if (item > 10) {
                      results.push(item + 10);
                    } else {
                      results.push(item - 1);
                    }
                  } else {
                    results.push(null);
                  }
                  
                  this.counter++;
                }
                
                return results;
              }
              
              public getCounter(): number {
                return this.counter;
              }
            }
          `;
          
          await fs.writeFile(testFile, content);
          files.push(testFile);
        }

        const startTime = performance.now();
        
        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--output', 'json',
          ...files
        ], { 
          timeout: 15000, // 15秒超时
          maxBuffer: 2 * 1024 * 1024, // 2MB缓冲区
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        const duration = performance.now() - startTime;
        
        expect(exitCode).toBe(0);
        expect(duration).toBeLessThan(15000); // 应该在15秒内完成
        
        // 计算平均每个文件的处理时间
        const averageTimePerFile = duration / fileCount;
        expect(averageTimePerFile).toBeLessThan(1500); // 每个文件不应该超过1.5秒
        
        console.log(`Performance test: ${fileCount} files processed in ${duration.toFixed(2)}ms (avg: ${averageTimePerFile.toFixed(2)}ms per file)`);
      });
    });

    test('应该处理高频率的CLI调用', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'high-frequency.ts');
        await fs.writeFile(testFile, `
          export function highFrequencyTest(): number {
            return Math.random();
          }
        `);

        const concurrentRuns = 3; // 减少并发数量
        const startTime = performance.now();
        
        // 并发执行多个快速的CLI调用
        const promises = Array.from({ length: concurrentRuns }, async (_, i) => {
          const instance = await CLITestingUtils.renderCLI('bun', [
            'run', 'src/index.ts',
            testFile
          ], { 
            timeout: 5000,
            cwd: process.cwd()
          });
          instances.push(instance);
          
          const exitCode = await instance.waitForExit();
          return { index: i, exitCode, success: exitCode === 0 };
        });

        const results = await Promise.all(promises);
        const duration = performance.now() - startTime;
        
        // 验证所有调用都成功
        const successCount = results.filter(r => r.success).length;
        expect(successCount).toBe(concurrentRuns);
        
        // 验证总体性能
        expect(duration).toBeLessThan(10000); // 应该在10秒内完成所有并发调用
        
        console.log(`High frequency test: ${concurrentRuns} concurrent runs completed in ${duration.toFixed(2)}ms`);
      });
    });
  });
});