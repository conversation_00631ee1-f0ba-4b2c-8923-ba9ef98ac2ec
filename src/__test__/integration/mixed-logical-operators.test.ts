import { test, expect, describe } from "vitest";
import { spawn } from "child_process";
import { writeFileSync, existsSync, mkdirSync } from "fs";
import { join } from "path";
import { TestUtils } from "../helpers/test-utils";

// 辅助函数：从CLI输出中提取JSON
function extractJSONFromOutput(stdout: string): any {
  // 首先尝试找到完整的JSON对象
  const jsonMatch = stdout.match(/\{[\s\S]*\}/);
  if (jsonMatch) {
    try {
      return JSON.parse(jsonMatch[0]);
    } catch (error) {
      // JSON 解析失败，尝试其他方法
    }
  }
  
  // 如果正则匹配失败，尝试按行分割处理
  const lines = stdout.split('\n');
  
  // 找到JSON开始的行
  const jsonStartIndex = lines.findIndex(line => line.trim().startsWith('{'));
  if (jsonStartIndex === -1) {
    throw new Error('No JSON found in output');
  }
  
  // 从JSON开始的行向下收集，直到找到完整的JSON
  let braceCount = 0;
  let jsonLines: string[] = [];
  
  for (let i = jsonStartIndex; i < lines.length; i++) {
    const line = lines[i]!; // 使用非空断言，因为i < lines.length
    jsonLines.push(line);
    
    // 统计大括号
    for (const char of line) {
      if (char === '{') braceCount++;
      if (char === '}') braceCount--;
    }
    
    // 如果大括号平衡了，说明JSON结束了
    if (braceCount === 0 && jsonLines.length > 0) {
      break;
    }
  }
  
  const jsonString = jsonLines.join('\n');
  return JSON.parse(jsonString);
}

describe("混合逻辑运算符 - 端到端集成测试", () => {
  
  const runCLI = async (args: string[], timeoutMs = 15000): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
    return new Promise((resolve, reject) => {
      // 使用构建后的 CLI 文件确保 Node.js 兼容性
      const proc = spawn("node", ["dist/cli/index.js", ...args], {
        cwd: process.cwd(),
        stdio: ["pipe", "pipe", "pipe"]
      });
      
      let stdout = "";
      let stderr = "";
      let resolved = false;
      
      // 设置超时
      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          proc.kill('SIGKILL');
          reject(new Error(`CLI process timed out after ${timeoutMs}ms for args: ${args.join(' ')}`));
        }
      }, timeoutMs);
      
      proc.stdout?.on("data", (data) => {
        stdout += data.toString();
      });
      
      proc.stderr?.on("data", (data) => {
        stderr += data.toString();
      });
      
      proc.on("close", (code) => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          resolve({
            stdout,
            stderr,
            exitCode: code || 0
          });
        }
      });
      
      proc.on("error", (err) => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          reject(err);
        }
      });
    });
  };

  // 创建测试文件的工具函数
  const createTestFiles = (testDir: string) => {
    // 创建包含混合逻辑运算符的测试文件
    const testFiles = {
      // 基础混用测试文件
      "src/basic-mixing.ts": `
export function basicMixingTest() {
  // 基础混用场景 - 期望复杂度: 4 (if + && + || + 混用惩罚)
  if (condition1 && condition2 || condition3) {
    return 'mixed';
  }
}

export function reverseMixingTest() {
  // 反向混用场景 - 期望复杂度: 4 (if + || + && + 混用惩罚)
  if (condition1 || condition2 && condition3) {
    return 'reverse mixed';
  }
}

export function noMixingTest() {
  // 同类运算符无混用 - 期望复杂度: 3 (if + && + &&)
  if (condition1 && condition2 && condition3) {
    return 'no mixing';
  }
}
      `,
      
      // 括号豁免测试文件
      "src/parentheses-exemption.ts": `
export function parenthesesTest1() {
  // 括号消除混用惩罚 - 期望复杂度: 3 (if + && + ||)
  if ((condition1 && condition2) || condition3) {
    return 'exempted1';
  }
}

export function parenthesesTest2() {
  // 反向括号豁免 - 期望复杂度: 3 (if + || + &&)
  if (condition1 || (condition2 && condition3)) {
    return 'exempted2';
  }
}

export function nestedParentheses() {
  // 多层括号，无混用 - 期望复杂度: 4 (if + && + && + ||)
  if ((a && b) && (c || d)) {
    return 'nested';
  }
}
      `,
      
      // 默认值赋值豁免测试文件
      "src/default-assignment.ts": `
export function defaultValueTest() {
  // 赋值操作豁免，但条件中有混用惩罚
  const value = defaultValue || backup; // 豁免: 0
  
  // 期望复杂度: 4 (if + && + || + 混用惩罚)
  if (condition && value.isValid || fallback) {
    return value;
  }
}

export function nullishCoalescingTest() {
  // 空值合并操作豁免 - 期望复杂度: 0
  const name = user?.name ?? 'Anonymous';
  const data = response.data ?? [];
  return { name, data };
}

export function mixedAssignmentContext() {
  const result = input || defaultInput; // 豁免: 0
  
  // 期望复杂度: 4 (if + && + || + 混用惩罚)
  if (result && result.valid || useBackup) {
    return processResult(result);
  }
}
      `,
      
      // 复杂嵌套场景测试文件
      "src/complex-nesting.ts": `
export function complexNestedMixing() {
  // 嵌套结构中的混用 - 期望复杂度: 6
  // while(1) + if(1+1嵌套) + &&(1) + ||(1) + 混用惩罚(1) = 6
  while (condition) {
    if (a && b || c) {
      break;
    }
  }
}

export function multiLevelNesting() {
  // 多层嵌套混用 - 期望复杂度: 10
  // for(1) + if(1+1嵌套) + &&(1) + ||(1) + 混用惩罚(1) + while(1+2嵌套) + ||(1) + &&(1) + 混用惩罚(1) = 10
  for (let i = 0; i < 10; i++) {
    if (a && b || c) {
      while (x || y && z) {
        return true;
      }
    }
  }
}
      `,
      
      // 真实业务场景测试文件
      "src/real-world-scenarios.ts": `
export function userPermissionCheck(user: any) {
  // 用户权限检查 - 期望复杂度: 4 (if + || + && + 混用惩罚)
  if (user.isAdmin || user.role === 'moderator' && user.active) {
    return { access: 'granted' };
  }
  return { access: 'denied' };
}

export function dataValidationWithFallback(data: any, fallback: any) {
  // 数据验证与回退 - 期望复杂度: 5 (if + && + || + 混用惩罚)
  if (data && data.length > 0 || fallback) {
    return processData(data || fallback);
  }
}

export function configurationHandler(userConfig: any, defaultConfig: any) {
  // 配置处理 - 混合默认值和条件检查
  const config = userConfig || defaultConfig; // 豁免: 0
  
  // 期望复杂度: 5 (if + && + || + 混用惩罚)
  if (config.enabled && config.mode === 'production' || config.debug) {
    return initializeApp(config);
  }
}

export function apiResponseHandler(response: any, retry: any) {
  // API响应处理 - 复杂的业务逻辑
  try { // +1
    if (response.ok && response.data || response.cached) { // +2(嵌套) + 1 + 1 + 1(混用惩罚) = +5
      return { success: true, data: response.data };
    } else if (response.status === 404 || response.status >= 500 && retry) { // +2(嵌套) + 1 + 1 + 1(混用惩罚) = +5  
      return handleRetry(response);
    }
  } catch (error) { // +1
    console.error(error);
  }
  // 总期望复杂度: 1 + 5 + 5 + 1 = 12
}

// 声明辅助函数以避免编译错误
declare function processData(data: any): any;
declare function initializeApp(config: any): any;
declare function handleRetry(response: any): any;
      `
    };

    // 创建测试文件
    Object.entries(testFiles).forEach(([path, content]) => {
      const fullPath = join(testDir, path);
      const dir = fullPath.substring(0, fullPath.lastIndexOf('/'));
      
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }
      
      writeFileSync(fullPath, content, 'utf8');
    });
  };

  describe("混合逻辑运算符功能验证", () => {
    test("应该检测基础混用模式并应用惩罚", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFiles(testDir);
        
        // 创建启用混用惩罚的配置
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          },
          failOnComplexity: 20
        };
        
        const configFile = join(testDir, "cognitive.config.json");
        writeFileSync(configFile, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "src/basic-mixing.ts"),
          "--config", configFile,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        // 解析JSON结果
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;
          
          // 验证基础混用惩罚
          const basicMixing = functions.find((f: any) => f.name === 'basicMixingTest');
          expect(basicMixing).toBeTruthy();
          expect(basicMixing.complexity).toBe(4); // if + && + || + 混用惩罚
          
          const reverseMixing = functions.find((f: any) => f.name === 'reverseMixingTest');
          expect(reverseMixing).toBeTruthy();
          expect(reverseMixing.complexity).toBe(4); // if + || + && + 混用惩罚
          
          const noMixing = functions.find((f: any) => f.name === 'noMixingTest');
          expect(noMixing).toBeTruthy();
          expect(noMixing.complexity).toBe(3); // if + && + &&, 无混用惩罚
        }
      });
    });

    test("应该正确处理括号豁免机制", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFiles(testDir);
        
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          }
        };
        
        const configFile = join(testDir, "cognitive.config.json");
        writeFileSync(configFile, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "src/parentheses-exemption.ts"),
          "--config", configFile,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;
          
          // 验证括号豁免
          const parentheses1 = functions.find((f: any) => f.name === 'parenthesesTest1');
          expect(parentheses1).toBeTruthy(); 
          expect(parentheses1.complexity).toBe(3); // 括号消除混用惩罚
          
          const parentheses2 = functions.find((f: any) => f.name === 'parenthesesTest2');
          expect(parentheses2).toBeTruthy();
          expect(parentheses2.complexity).toBe(3); // 括号消除混用惩罚
          
          const nested = functions.find((f: any) => f.name === 'nestedParentheses');
          expect(nested).toBeTruthy();
          expect(nested.complexity).toBe(4); // 多层括号，无混用
        }
      });
    });

    test("应该正确处理默认值赋值豁免", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFiles(testDir);
        
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          }
        };
        
        const configFile = join(testDir, "cognitive.config.json");
        writeFileSync(configFile, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "src/default-assignment.ts"),
          "--config", configFile,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;
          
          // 验证默认值赋值豁免
          const defaultValue = functions.find((f: any) => f.name === 'defaultValueTest');
          expect(defaultValue).toBeTruthy();
          expect(defaultValue.complexity).toBe(4); // 只有条件中的混用有惩罚
          
          const nullishCoalescing = functions.find((f: any) => f.name === 'nullishCoalescingTest');
          expect(nullishCoalescing).toBeTruthy();
          expect(nullishCoalescing.complexity).toBe(0); // 空值合并豁免
          
          const mixedAssignment = functions.find((f: any) => f.name === 'mixedAssignmentContext');
          expect(mixedAssignment).toBeTruthy();
          expect(mixedAssignment.complexity).toBe(4); // 赋值豁免 + 条件混用惩罚
        }
      });
    });

    test("应该处理复杂嵌套场景", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFiles(testDir);
        
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          }
        };
        
        const configFile = join(testDir, "cognitive.config.json");
        writeFileSync(configFile, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "src/complex-nesting.ts"),
          "--config", configFile,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;
          
          // 验证复杂嵌套场景
          const complexNested = functions.find((f: any) => f.name === 'complexNestedMixing');
          expect(complexNested).toBeTruthy();
          expect(complexNested.complexity).toBe(6); // 嵌套结构中的混用
          
          const multiLevel = functions.find((f: any) => f.name === 'multiLevelNesting');
          expect(multiLevel).toBeTruthy();
          expect(multiLevel.complexity).toBe(12); // 多层嵌套混用
        }
      });
    });

    test("功能禁用时不应用混用惩罚", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFiles(testDir);
        
        // 创建禁用混用惩罚的配置
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: false
          }
        };
        
        const configFile = join(testDir, "cognitive.config.json");
        writeFileSync(configFile, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "src/basic-mixing.ts"),
          "--config", configFile,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;
          
          // 验证功能禁用时无惩罚
          const basicMixing = functions.find((f: any) => f.name === 'basicMixingTest');
          expect(basicMixing).toBeTruthy();
          expect(basicMixing.complexity).toBe(3); // if + && + ||, 无混用惩罚
          
          const reverseMixing = functions.find((f: any) => f.name === 'reverseMixingTest');
          expect(reverseMixing).toBeTruthy();
          expect(reverseMixing.complexity).toBe(3); // if + || + &&, 无混用惩罚
        }
      });
    });

    test("应该验证真实业务场景", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFiles(testDir);
        
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          },
          failOnComplexity: 20  // 调整阈值以避免质量门禁失败
        };
        
        const configFile = join(testDir, "cognitive.config.json");
        writeFileSync(configFile, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "src/real-world-scenarios.ts"),
          "--config", configFile,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const output = extractJSONFromOutput(result.stdout);
        const functions = output.results[0].functions;
        
        // 验证真实业务场景
        const userPermission = functions.find((f: any) => f.name === 'userPermissionCheck');
        expect(userPermission).toBeTruthy();
        expect(userPermission.complexity).toBe(4); // 用户权限检查
        
        const dataValidation = functions.find((f: any) => f.name === 'dataValidationWithFallback');
        expect(dataValidation).toBeTruthy();
        expect(dataValidation.complexity).toBe(4); // 数据验证与回退
        
        const configHandler = functions.find((f: any) => f.name === 'configurationHandler');
        expect(configHandler).toBeTruthy();
        expect(configHandler.complexity).toBe(4); // 配置处理
        
        const apiHandler = functions.find((f: any) => f.name === 'apiResponseHandler');
        expect(apiHandler).toBeTruthy();
        expect(apiHandler.complexity).toBe(10); // API响应处理
      });
    });
  });
});