/**
 * IoC 架构重构集成测试
 * 
 * 测试完整的分析流程：配置 → 工厂 → 引擎 → 计算器
 * 验证各种配置组合的功能正确性
 * 测试与现有插件系统和配置系统的兼容性
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ComplexityCalculator } from '../../core/calculator';
import { CalculatorFactory, createCalculatorFactory, createLightweightFactory } from '../../core/calculator-factory';
import type { CalculatorOptions, CalculationOptions } from '../../engine/types';

describe('IoC 架构重构集成测试', () => {
  let calculator: ComplexityCalculator;
  let factory: CalculatorFactory;

  afterEach(async () => {
    // 清理资源
    if (calculator) {
      await calculator.dispose();
    }
  });

  describe('基本工厂配置测试', () => {
    it('应该创建默认配置的工厂', () => {
      factory = new CalculatorFactory();
      expect(factory).toBeDefined();
      
      const features = (factory as any).getFeatureSummary?.();
      expect(features).toBeDefined();
      expect(typeof features.monitoring).toBe('boolean');
    });

    it('应该创建自定义配置的工厂', () => {
      const options: CalculatorOptions = {
        enableMonitoring: true,
        enableCaching: true,
        maxConcurrency: 8,
        debugMode: true,
        quiet: false,
        ruleEngineConfig: {
          maxRuleConcurrency: 10,
          enableRuleCaching: true,
          ruleDebugMode: false,
        }
      };

      factory = new CalculatorFactory(options);
      expect(factory).toBeDefined();
      
      const features = (factory as any).getFeatureSummary?.();
      expect(features?.monitoring).toBe(true);
      expect(features?.caching).toBe(true);
    });

    it('应该创建轻量级工厂', () => {
      factory = createLightweightFactory();
      expect(factory).toBeDefined();
      
      const features = (factory as any).getFeatureSummary?.();
      expect(features?.monitoring).toBe(false);
      expect(features?.caching).toBe(false);
    });
  });

  describe('完整分析流程测试', () => {
    const testCode = `
      function simpleFunction() {
        return 42;
      }

      function complexFunction(x) {
        if (x > 0) {
          while (x > 0) {
            if (x % 2 === 0) {
              console.log(x);
            }
            x--;
          }
        }
        return x;
      }

      function logicalMixing(a, b, c) {
        return a && b || c;
      }
    `;

    it('应该使用默认工厂完成完整分析', async () => {
      factory = new CalculatorFactory();
      calculator = new ComplexityCalculator({}, factory);

      const results = await calculator.calculateCode(testCode, 'test.ts');
      
      expect(results).toHaveLength(3);
      expect(results[0].name).toBe('simpleFunction');
      expect(results[0].complexity).toBe(0); // 简单函数复杂度为0
      expect(results[1].name).toBe('complexFunction');
      expect(results[1].complexity).toBeGreaterThan(1);
      expect(results[2].name).toBe('logicalMixing');
    });

    it('应该使用监控配置完成分析', async () => {
      const options: CalculatorOptions = {
        enableMonitoring: true,
        debugMode: false,
        quiet: true,
      };

      factory = new CalculatorFactory(options);
      calculator = new ComplexityCalculator({}, factory);

      const results = await calculator.calculateCode(testCode, 'test.ts');
      
      expect(results).toHaveLength(3);
      expect(results.some(r => r.complexity >= 1)).toBe(true); // 至少有一个函数复杂度 >= 1
    });

    it('应该使用规则引擎配置完成分析', async () => {
      const options: CalculatorOptions = {
        ruleEngineConfig: {
          maxRuleConcurrency: 5,
          enableRuleCaching: false,
          ruleDebugMode: true,
        },
        quiet: true,
      };

      factory = new CalculatorFactory(options);
      calculator = new ComplexityCalculator({}, factory);

      const results = await calculator.calculateCode(testCode, 'test.ts');
      
      expect(results).toHaveLength(3);
      expect(results.some(r => r.complexity > 1)).toBe(true);
    });
  });

  describe('静态 API 测试', () => {
    const simpleCode = `function test() { return 1; }`;

    it('应该使用静态 analyze 方法', async () => {
      const results = await ComplexityCalculator.analyze(simpleCode);
      
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('test');
      expect(results[0].complexity).toBe(0); // 简单函数复杂度为0
    });

    it('应该使用静态 analyzeFile 方法', async () => {
      // 创建临时测试文件
      const testFilePath = '/tmp/test-static.ts';
      const fs = await import('fs/promises');
      await fs.writeFile(testFilePath, simpleCode);

      try {
        const results = await ComplexityCalculator.analyzeFile(testFilePath);
        
        expect(results).toHaveLength(1);
        expect(results[0].name).toBe('test');
        expect(results[0].complexity).toBe(0); // 简单函数复杂度为0
      } finally {
        // 清理测试文件
        await fs.unlink(testFilePath).catch(() => {});
      }
    });

    it('应该使用静态 quickAnalyze 方法', async () => {
      const complexCode = `
        function a() { return 1; }
        function b() { if (x) { while (y) { if (z) {} } } }
        function c() { return 3; }
      `;

      const overview = await ComplexityCalculator.quickAnalyze(complexCode);
      
      expect(overview.functionCount).toBe(3);
      expect(overview.totalComplexity).toBeGreaterThan(3);
      expect(overview.averageComplexity).toBeGreaterThan(1);
      expect(overview.maxComplexity).toBeGreaterThan(1);
      expect(overview.complexFunctions.length).toBeGreaterThan(0);
    });
  });

  describe('资源管理测试', () => {
    it('应该正确清理计算器资源', async () => {
      factory = new CalculatorFactory();
      calculator = new ComplexityCalculator({}, factory);

      const results = await calculator.calculateCode('function test() {}', 'test.ts');
      expect(results).toHaveLength(1);

      // 测试资源清理
      await expect(calculator.dispose()).resolves.toBeUndefined(); // dispose返回void/undefined
    });

    it('应该支持多次创建和清理', async () => {
      for (let i = 0; i < 3; i++) {
        factory = new CalculatorFactory();
        calculator = new ComplexityCalculator({}, factory);

        const results = await calculator.calculateCode(`function test${i}() {}`, 'test.ts');
        expect(results).toHaveLength(1);
        expect(results[0].name).toBe(`test${i}`);

        await calculator.dispose();
      }
    });
  });

  describe('配置继承和合并测试', () => {
    it('应该正确合并用户配置', async () => {
      const calculationOptions: CalculationOptions = {
        enableDebugLog: true,
        enableDetails: false,
        quiet: false,
      };

      factory = new CalculatorFactory();
      calculator = new ComplexityCalculator(calculationOptions, factory);

      const results = await calculator.calculateCode('function test() {}', 'test.ts');
      expect(results).toHaveLength(1);
    });

    it('应该支持配置更新', async () => {
      factory = new CalculatorFactory();
      calculator = new ComplexityCalculator({}, factory);

      // 初始配置
      let results = await calculator.calculateCode('function test1() {}', 'test.ts');
      expect(results).toHaveLength(1);

      // 更新配置
      calculator.updateConfiguration({
        enableDebugLog: true,
        quiet: true,
      });

      // 验证更新后的配置
      results = await calculator.calculateCode('function test2() {}', 'test.ts');
      expect(results).toHaveLength(1);
    });
  });

  describe('错误处理和健壮性测试', () => {
    it('应该处理无效代码', async () => {
      factory = new CalculatorFactory();
      calculator = new ComplexityCalculator({}, factory);

      // 无效语法应该抛出 ParseError
      await expect(
        calculator.calculateCode('invalid syntax {{{', 'test.ts')
      ).rejects.toThrow();
    });

    it('应该处理空代码', async () => {
      factory = new CalculatorFactory();
      calculator = new ComplexityCalculator({}, factory);

      const results = await calculator.calculateCode('', 'test.ts');
      expect(results).toEqual([]);
    });

    it('应该处理只有注释的代码', async () => {
      factory = new CalculatorFactory();
      calculator = new ComplexityCalculator({}, factory);

      const results = await calculator.calculateCode('// 这只是注释\n/* 块注释 */', 'test.ts');
      expect(results).toEqual([]);
    });
  });

  describe('兼容性测试', () => {
    it('应该保持API兼容性', async () => {
      // 测试传统构造方式仍然有效
      calculator = new ComplexityCalculator();
      
      const results = await calculator.calculateCode('function legacy() {}', 'test.ts');
      expect(results).toHaveLength(1);
      expect(results[0].name).toBe('legacy');
    });

    it('应该支持各种 CalculationOptions', async () => {
      const options: CalculationOptions = {
        enableDebugLog: false,
        enableDetails: true,
        quiet: true,
        enableMixedLogicOperatorPenalty: true,
      };

      calculator = new ComplexityCalculator(options);
      
      const results = await calculator.calculateCode('function withOptions() {}', 'test.ts');
      expect(results).toHaveLength(1);
    });
  });

  describe('性能基础测试', () => {
    it('应该在合理时间内完成小型代码分析', async () => {
      const startTime = performance.now();
      
      factory = createLightweightFactory();
      calculator = new ComplexityCalculator({}, factory);

      const results = await calculator.calculateCode(`
        function performance1() { return 1; }
        function performance2() { return 2; }
        function performance3() { return 3; }
      `, 'perf-test.ts');

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(3);
      expect(duration).toBeLessThan(1000); // 应该在1秒内完成
    });

    it('应该在合理时间内完成静态分析', async () => {
      const startTime = performance.now();
      
      const results = await ComplexityCalculator.analyze(`
        function static1() { return 1; }
        function static2() { if (x) { return 2; } }
        function static3() { for (let i = 0; i < 10; i++) { console.log(i); } }
      `);

      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(results).toHaveLength(3);
      expect(duration).toBeLessThan(500); // 静态分析应该更快
    });
  });
});