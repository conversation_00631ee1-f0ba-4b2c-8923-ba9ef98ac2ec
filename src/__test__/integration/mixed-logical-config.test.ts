import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { spawn } from "child_process";
import { writeFileSync, unlinkSync, existsSync, mkdirSync, rmSync } from "fs";
import { join } from "path";
import { TestUtils } from "../helpers/test-utils";

describe("混合逻辑运算符配置 - 集成测试", () => {
  
  const runCLI = async (args: string[], timeoutMs = 10000): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
    return new Promise((resolve, reject) => {
      const proc = spawn("node", ["dist/cli/index.js", ...args], {
        cwd: process.cwd(),
        stdio: ["pipe", "pipe", "pipe"]
      });
      
      let stdout = "";
      let stderr = "";
      let resolved = false;
      
      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          proc.kill('SIGKILL');
          reject(new Error(`CLI process timed out after ${timeoutMs}ms`));
        }
      }, timeoutMs);
      
      proc.stdout?.on("data", (data) => {
        stdout += data.toString();
      });
      
      proc.stderr?.on("data", (data) => {
        stderr += data.toString();
      });
      
      proc.on("close", (code) => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          resolve({ stdout, stderr, exitCode: code || 0 });
        }
      });
      
      proc.on("error", (err) => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          reject(err);
        }
      });
    });
  };

  // 创建标准测试文件的工具函数
  const createTestFile = (testDir: string) => {
    writeFileSync(join(testDir, "test-mixed-logic.ts"), `
export function mixedLogicExample() {
  if (condition1 && condition2 || condition3) {
    return 'mixed logic';
  }
}

export function pureAndExample() {
  if (a && b && c) {
    return 'pure and';
  }
}

export function defaultValueExample() {
  const name = user.name || 'Guest';
  return name;
}
    `, 'utf8');
  };

  describe("配置文件格式支持", () => {
    test("应该支持JSON配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const jsonConfig = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          },
          failOnComplexity: 10
        };
        
        const configPath = join(testDir, "cognitive.config.json");
        writeFileSync(configPath, JSON.stringify(jsonConfig, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(4); // 混用惩罚启用
        }
      });
    });

    test("应该支持JavaScript配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const jsConfig = `
module.exports = {
  rules: {
    enableMixedLogicOperatorPenalty: true
  },
  failOnComplexity: 15,
  exclude: ['**/*.test.ts'],
  report: {
    json: './complexity-report.json'
  }
};
        `;
        
        const configPath = join(testDir, "cognitive.config.js");
        writeFileSync(configPath, jsConfig);
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(4); // 混用惩罚启用
        }
      });
    });

    test("应该支持YAML配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const yamlConfig = `
rules:
  enableMixedLogicOperatorPenalty: true
failOnComplexity: 12
exclude:
  - "**/*.test.ts"
  - "**/node_modules/**"
report:
  json: "./reports/complexity.json"
  html: "./reports/complexity.html"
        `;
        
        const configPath = join(testDir, "cognitive.config.yaml");
        writeFileSync(configPath, yamlConfig);
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(4); // 混用惩罚启用
        }
      });
    });

    test("应该支持package.json中的配置", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const packageJson = {
          name: "test-package",
          version: "1.0.0",
          "cognitive-complexity": {
            rules: {
              enableMixedLogicOperatorPenalty: true
            },
            failOnComplexity: 8
          }
        };
        
        writeFileSync(join(testDir, "package.json"), JSON.stringify(packageJson, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(4); // package.json配置生效
        }
      });
    });
  });

  describe("配置优先级测试", () => {
    test("CLI参数应该覆盖配置文件设置", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        // 配置文件设置为禁用
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: false
          }
        };
        
        const configPath = join(testDir, "cognitive.config.json");
        writeFileSync(configPath, JSON.stringify(config, null, 2));
        
        // CLI参数设置为启用（如果有这样的参数）
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(3); // 配置文件设置生效：禁用混用惩罚
        }
      });
    });

    test("应该使用最近的配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        // 在项目根目录创建配置
        const rootConfig = {
          rules: {
            enableMixedLogicOperatorPenalty: false
          }
        };
        
        writeFileSync(join(testDir, "cognitive.config.json"), JSON.stringify(rootConfig, null, 2));
        
        // 在子目录创建配置
        const subDir = join(testDir, "src");
        mkdirSync(subDir, { recursive: true });
        
        const subConfig = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          }
        };
        
        writeFileSync(join(subDir, "cognitive.config.json"), JSON.stringify(subConfig, null, 2));
        
        // 将测试文件移到子目录
        writeFileSync(join(subDir, "test.ts"), `
export function testMixed() {
  if (a && b || c) {
    return true;
  }
}
        `);
        
        const result = await runCLI([
          join(subDir, "test.ts"),
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const testFunc = output.results[0].functions.find((f: any) => f.name === 'testMixed');
          expect(testFunc.complexity).toBe(4); // 子目录配置生效：启用混用惩罚
        }
      });
    });
  });

  describe("配置验证和错误处理", () => {
    test("应该处理损坏的JSON配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const configPath = join(testDir, "broken.config.json");
        writeFileSync(configPath, "{ invalid json structure");
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        // 应该回退到默认配置
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(3); // 默认配置：禁用混用惩罚
        }
      });
    });

    test("应该处理不存在的配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const nonExistentConfig = join(testDir, "nonexistent.config.json");
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", nonExistentConfig
        ]);
        
        // 应该显示错误信息但仍能运行
        expect(result.exitCode).toBe(1);
        expect(result.stderr.length > 0).toBe(true);
      });
    });

    test("应该验证配置项的类型", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        // 创建类型错误的配置
        const invalidConfig = {
          rules: {
            enableMixedLogicOperatorPenalty: "yes" // 应该是boolean
          },
          failOnComplexity: "high" // 应该是number
        };
        
        const configPath = join(testDir, "invalid-types.config.json");
        writeFileSync(configPath, JSON.stringify(invalidConfig, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        // 应该使用默认值或进行类型转换
        expect(result.exitCode).toBe(0);
      });
    });
  });

  describe("配置文件发现机制", () => {
    test("应该自动发现当前目录的配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        // 在测试目录创建配置文件
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          }
        };
        
        writeFileSync(join(testDir, ".cognitiverc"), JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(4); // 自动发现的配置生效
        }
      });
    });

    test("应该向上搜索配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        // 在上级目录创建配置
        const parentDir = join(testDir, "..");
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          }
        };
        
        writeFileSync(join(parentDir, "cognitive.config.json"), JSON.stringify(config, null, 2));
        
        try {
          const result = await runCLI([
            join(testDir, "test-mixed-logic.ts"),
            "--format", "json"
          ]);
          
          expect(result.exitCode).toBe(0);
          
          // 如果找到了父级配置，应该应用混用惩罚
          const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
          if (jsonMatch) {
            const output = JSON.parse(jsonMatch[0]);
            const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
            // 根据配置发现机制，可能使用父级配置或默认配置
            expect([3, 4]).toContain(mixedFunc.complexity);
          }
        } finally {
          // 清理父级配置文件
          const parentConfigPath = join(parentDir, "cognitive.config.json");
          if (existsSync(parentConfigPath)) {
            unlinkSync(parentConfigPath);
          }
        }
      });
    });
  });

  describe("环境变量和动态配置", () => {
    test("应该支持环境变量覆盖", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: false
          }
        };
        
        const configPath = join(testDir, "cognitive.config.json");
        writeFileSync(configPath, JSON.stringify(config, null, 2));
        
        // 通过环境变量启用混用惩罚
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ], 10000);
        
        expect(result.exitCode).toBe(0);
        
        // 验证结果（这里取决于具体的环境变量实现）
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
      });
    });

    test("应该处理配置合并", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        // 创建基础配置
        const baseConfig = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          },
          failOnComplexity: 10,
          exclude: ["**/*.test.ts"]
        };
        
        const configPath = join(testDir, "cognitive.config.json");
        writeFileSync(configPath, JSON.stringify(baseConfig, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          
          // 验证配置合并的结果
          expect(output.results).toBeDefined();
          expect(output.summary).toBeDefined();
        }
      });
    });
  });

  describe("特殊配置场景", () => {
    test("应该处理部分配置项", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        // 只配置一部分选项
        const partialConfig = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          }
          // 其他选项使用默认值
        };
        
        const configPath = join(testDir, "partial.config.json");
        writeFileSync(configPath, JSON.stringify(partialConfig, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(4); // 部分配置生效
        }
      });
    });

    test("应该处理空配置文件", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const configPath = join(testDir, "empty.config.json");
        writeFileSync(configPath, "{}");
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const mixedFunc = output.results[0].functions.find((f: any) => f.name === 'mixedLogicExample');
          expect(mixedFunc.complexity).toBe(3); // 使用默认配置：禁用混用惩罚
        }
      });
    });

    test("应该处理深层嵌套的配置", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const nestedConfig = {
          rules: {
            enableMixedLogicOperatorPenalty: true,
            mixedLogicalOperators: {
              enabled: true,
              penalty: 1
            }
          }
        };
        
        const configPath = join(testDir, "nested.config.json");
        writeFileSync(configPath, JSON.stringify(nestedConfig, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        // 验证嵌套配置的处理
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
      });
    });
  });

  describe("配置与CLI功能集成", () => {
    test("应该与质量门禁配置配合工作", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          },
          failOnComplexity: 3 // 设置较低阈值
        };
        
        const configPath = join(testDir, "quality-gate.config.json");
        writeFileSync(configPath, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath
        ]);
        
        // 由于混用惩罚使复杂度达到4，超过阈值3，应该失败
        expect(result.exitCode).toBe(1);
        expect(
          result.stderr.includes("Quality gate failed") ||
          result.stdout.includes("Quality gate failed")
        ).toBe(true);
      });
    });

    test("应该与输出格式配置配合工作", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          },
          report: {
            format: "json",
            outputDir: "./reports"
          }
        };
        
        const configPath = join(testDir, "output-format.config.json");
        writeFileSync(configPath, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          join(testDir, "test-mixed-logic.ts"),
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        // 验证JSON输出格式
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          expect(output.summary).toBeDefined();
          expect(output.results).toBeDefined();
        }
      });
    });

    test("应该与排除规则配置配合工作", async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createTestFile(testDir);
        
        // 创建要被排除的文件
        writeFileSync(join(testDir, "excluded.test.ts"), `
export function excludedMixed() {
  if (a && b || c) {
    return 'should be excluded';
  }
}
        `);
        
        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true
          },
          exclude: ["**/*.test.ts"]
        };
        
        const configPath = join(testDir, "exclude.config.json");
        writeFileSync(configPath, JSON.stringify(config, null, 2));
        
        const result = await runCLI([
          testDir,
          "--config", configPath,
          "--format", "json"
        ]);
        
        expect(result.exitCode).toBe(0);
        
        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();
        
        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          
          // 验证排除规则：应该只分析非测试文件
          const allFunctions = output.results.flatMap((r: any) => r.functions);
          const excludedFunction = allFunctions.find((f: any) => f.name === 'excludedMixed');
          expect(excludedFunction).toBeUndefined(); // 应该被排除
          
          const includedFunction = allFunctions.find((f: any) => f.name === 'mixedLogicExample');
          expect(includedFunction).toBeTruthy(); // 应该被包含
        }
      });
    });
  });
});