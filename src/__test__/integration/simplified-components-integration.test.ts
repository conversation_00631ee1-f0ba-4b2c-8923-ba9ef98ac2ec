import { ASTParser } from '../../core/parser';
import { PositionConverter } from '../../utils/position-converter';
import { FunctionFinderVisitor } from '../../core/function-finder-visitor';
import { ComplexityVisitor } from '../../core/complexity-visitor';

describe('Simplified Components Integration', () => {
  describe('ASTParser and PositionConverter Integration', () => {
    it('should work together for location processing', async () => {
      const parser = new ASTParser();
      const code = `function example() {
  if (condition) {
    return true;
  }
  return false;
}`;
      
      const ast = await parser.parseCode(code, 'test.ts');
      
      // ASTParser should delegate location conversion to PositionConverter
      const mockNode = {
        type: 'FunctionDeclaration',
        span: { start: 0, end: 10 }
      };
      
      const location = parser.getLocation(mockNode);
      const directConversion = PositionConverter.spanToPosition(code, 0);
      
      expect(location.line).toBe(directConversion.line);
      expect(location.column).toBe(directConversion.column);
    });

    it('should handle ignore comments correctly without complex logic', async () => {
      const parser = new ASTParser();
      const code = `function test1() {
  return 1;
}
// cognitive-complexity-ignore-next-line
function test2() {
  if (true) {
    return 2;
  }
}`;
      
      await parser.parseCode(code, 'test.ts');
      
      // ASTParser should identify ignore comments
      expect(parser.isLineIgnored(5)).toBe(true); // function test2 line
      expect(parser.isLineIgnored(1)).toBe(false); // function test1 line
      
      const exemptions = parser.findIgnoreExemptions();
      expect(exemptions).toHaveLength(1);
      expect(exemptions[0].line).toBe(5);
    });
  });

  describe('FunctionFinderVisitor Integration', () => {
    it('should work with simplified ASTParser', async () => {
      const parser = new ASTParser();
      const code = `function regularFunction() {
  return 1;
}

const arrowFunction = () => {
  return 2;
};

class MyClass {
  method() {
    return 3;
  }
  
  static staticMethod() {
    return 4;
  }
}`;
      
      const ast = await parser.parseCode(code, 'test.ts');
      const functions = FunctionFinderVisitor.find(ast);
      
      expect(functions).toHaveLength(4);
      
      // Test that we can get function names using parser
      const functionNames = functions.map(func => parser.getFunctionName(func));
      expect(functionNames).toContain('regularFunction');
      expect(functionNames).toContain('arrowFunction'); // arrow function variable name
      expect(functionNames).toContain('method');
      expect(functionNames).toContain('staticMethod');
    });
  });

  describe('ComplexityVisitor Integration', () => {
    it('should work with PositionConverter for span validation', async () => {
      const parser = new ASTParser();
      const code = `function complexFunction() {
  if (condition1) {
    if (condition2) {
      return true;
    }
  }
  return false;
}`;
      
      const ast = await parser.parseCode(code, 'test.ts');
      const functions = FunctionFinderVisitor.find(ast);
      
      expect(functions).toHaveLength(1);
      
      const func = functions[0];
      const visitor = new ComplexityVisitor(code);
      
      // Visit the function node to calculate complexity
      visitor.visit(func);
      const complexity = visitor.getTotalComplexity();
      
      expect(complexity).toBeGreaterThan(0);
      expect(typeof complexity).toBe('number');
    });
  });

  describe('Architecture Compliance', () => {
    it('should ensure ASTParser has no traversal methods', () => {
      const parser = new ASTParser();
      
      // These methods should not exist (removed in simplification)
      expect(parser.findFunctions).toBeUndefined();
      expect(parser.findFunctionsIterative).toBeUndefined();
      expect(parser.findFunctionsInNode).toBeUndefined();
      expect(parser.getLineFromSpan).toBeUndefined();
      expect(parser.findNearestValidCodeLine).toBeUndefined();
    });

    it('should ensure PositionConverter has no correction methods', () => {
      // These methods should not exist (removed in simplification)
      expect(PositionConverter.findLastValidCodeLine).toBeUndefined();
      expect(PositionConverter.validateAndCorrectLineIndex).toBeUndefined();
    });

    it('should verify that PositionConverter focuses on pure conversion', () => {
      const code = 'function test() { return 1; }';
      
      // Should have core conversion methods
      expect(typeof PositionConverter.spanToPosition).toBe('function');
      expect(typeof PositionConverter.lineColumnToOffset).toBe('function');
      expect(typeof PositionConverter.getLineContent).toBe('function');
      expect(typeof PositionConverter.extractSpanText).toBe('function');
      expect(typeof PositionConverter.isValidPosition).toBe('function');
      
      // Should be able to perform conversions
      const position = PositionConverter.spanToPosition(code, 0);
      expect(position.line).toBe(1);
      expect(position.column).toBe(1);
      
      const offset = PositionConverter.lineColumnToOffset(code, 1, 1);
      expect(offset).toBe(0);
    });

    it('should ensure FunctionFinderVisitor works independently', () => {
      // FunctionFinderVisitor should have a static find method
      expect(typeof FunctionFinderVisitor.find).toBe('function');
      
      // Should not require parser instance
      const mockAst = {
        type: 'Module',
        body: [],
        span: { start: 0, end: 0 }
      };
      
      const functions = FunctionFinderVisitor.find(mockAst as any);
      expect(Array.isArray(functions)).toBe(true);
    });

    it('should verify ComplexityVisitor handles span validation internally', async () => {
      const parser = new ASTParser();
      const code = `function test() {
  if (true) {
    return 1;
  }
}`;
      
      const ast = await parser.parseCode(code, 'test.ts');
      const functions = FunctionFinderVisitor.find(ast);
      
      expect(functions).toHaveLength(1);
      
      const visitor = new ComplexityVisitor(code);
      
      // Should handle span validation internally without external correction
      visitor.visit(functions[0]);
      const complexity = visitor.getTotalComplexity();
      
      expect(complexity).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle parsing errors gracefully', async () => {
      const parser = new ASTParser();
      const invalidCode = 'function invalid() { if ( }';
      
      await expect(parser.parseCode(invalidCode, 'test.ts')).rejects.toThrow();
    });

    it('should handle position converter errors gracefully', () => {
      const code = 'function test() {}';
      
      expect(() => {
        PositionConverter.lineColumnToOffset(code, 0, 1);
      }).toThrow('Line 0 is out of range');
      
      expect(() => {
        PositionConverter.getLineContent(code, 10);
      }).toThrow('Line 10 is out of range');
    });

    it('should handle visitor errors gracefully', () => {
      const visitor = new ComplexityVisitor('function test() {}');
      
      // Should not throw for empty node visits
      expect(() => {
        visitor.getTotalComplexity();
      }).not.toThrow();
    });
  });
});