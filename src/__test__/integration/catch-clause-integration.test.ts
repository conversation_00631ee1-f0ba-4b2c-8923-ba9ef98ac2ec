/**
 * CatchClauseRule集成测试
 * 验证CatchClauseRule与ComplexityCalculator的完整集成
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ComplexityCalculator } from '../../core/calculator';
import { createLightweightFactory } from '../../core/calculator-factory';

describe('CatchClauseRule Integration', () => {
  let calculator: ComplexityCalculator;

  beforeEach(async () => {
    const factory = createLightweightFactory();
    calculator = new ComplexityCalculator({}, factory);
  });

  afterEach(async () => {
    await calculator.dispose();
  });

  describe('简单try-catch语句', () => {
    it('应该正确计算简单try-catch的复杂度', async () => {
      const code = `
        function testFunction() {
          try {
            riskyOperation();
          } catch (error) {
            handleError(error);
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      expect(func.complexity).toBe(1); // catch子句贡献1点复杂度
    });

    it('应该正确计算嵌套try-catch的复杂度', async () => {
      const code = `
        function testFunction() {
          if (condition) {
            try {
              riskyOperation();
            } catch (error) {
              handleError(error);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // if: 1, catch: 1 + 嵌套惩罚1 = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });

    it('应该正确计算try-catch-finally的复杂度', async () => {
      const code = `
        function testFunction() {
          try {
            riskyOperation();
          } catch (error) {
            handleError(error);
          } finally {
            cleanupResources();
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      expect(func.complexity).toBe(1); // 只有catch增加复杂度，finally不增加
    });
  });

  describe('复杂错误处理场景', () => {
    it('应该正确计算包含if语句的catch块复杂度', async () => {
      const code = `
        function testFunction() {
          try {
            riskyOperation();
          } catch (error) {
            if (error.retryable) {
              retryOperation();
            } else {
              logError(error);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // catch: 1, if: 1+1(嵌套) = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });

    it('应该正确计算嵌套try-catch的复杂度', async () => {
      const code = `
        function testFunction() {
          try {
            outerOperation();
          } catch (outerError) {
            try {
              fallbackOperation();
            } catch (innerError) {
              handleInnerError(innerError);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // 外层catch: 1, 内层catch: 1+1(嵌套) = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });

    it('应该正确计算复杂错误分类处理的复杂度', async () => {
      const code = `
        function testFunction() {
          try {
            apiCall();
          } catch (error) {
            if (error instanceof NetworkError) {
              handleNetworkError(error);
            } else if (error instanceof ValidationError) {
              handleValidationError(error);
            } else if (error instanceof AuthenticationError) {
              handleAuthError(error);
            } else {
              handleGenericError(error);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // catch: 1, 三个if/else if: 3个分支，每个1+1(嵌套)=2，总计: 1 + 3*2 = 7
      expect(func.complexity).toBeGreaterThanOrEqual(6);
    });
  });

  describe('与现有规则的协作', () => {
    it('应该与IfStatementRule正确协作', async () => {
      const code = `
        function testFunction() {
          if (hasData()) {
            try {
              processData();
            } catch (error) {
              handleError(error);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // if: 1, catch: 1+1(嵌套) = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });

    it('应该与ForStatementRule正确协作', async () => {
      const code = `
        function testFunction() {
          for (const item of items) {
            try {
              processItem(item);
            } catch (error) {
              logError(error, item);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // for: 1, catch: 1+1(嵌套) = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });

    it('应该与WhileStatementRule正确协作', async () => {
      const code = `
        function testFunction() {
          while (hasMoreData()) {
            try {
              processNextBatch();
            } catch (error) {
              if (error.retryable) {
                scheduleRetry();
              } else {
                break;
              }
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // while: 1, catch: 1+1(嵌套) = 2, if: 1+2(嵌套) = 3, 总计: 6
      expect(func.complexity).toBe(6);
    });

    it('应该与ConditionalExpressionRule正确协作', async () => {
      const code = `
        function testFunction() {
          try {
            const result = isValid() ? processData() : getDefault();
            return result;
          } catch (error) {
            return shouldRetry ? retryOperation() : null;
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('testFunction');
      // 第一个三元运算符: 1, catch: 1, 第二个三元运算符: 1+1(嵌套) = 2, 总计: 4
      expect(func.complexity).toBe(4);
    });
  });

  describe('真实世界场景', () => {
    it('应该正确处理API调用错误处理', async () => {
      const code = `
        async function apiRequest(url) {
          try {
            const response = await fetch(url);
            if (!response.ok) {
              throw new Error(\`HTTP \${response.status}\`);
            }
            return await response.json();
          } catch (error) {
            if (error.name === 'NetworkError') {
              return handleNetworkError(error);
            } else if (error.name === 'TimeoutError') {
              return handleTimeoutError(error);
            } else {
              throw error;
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('apiRequest');
      // if: 1, catch: 1, 嵌套的if/else if: 2个分支，每个1+1(嵌套)=2，总计: 1 + 1 + 2*2 = 6
      expect(func.complexity).toBeGreaterThanOrEqual(5);
    });

    it('应该正确处理数据库事务错误处理', async () => {
      const code = `
        async function performTransaction() {
          const transaction = await db.beginTransaction();
          try {
            await transaction.insert(data);
            await transaction.update(relatedData);
            await transaction.commit();
          } catch (error) {
            await transaction.rollback();
            
            if (error.code === 'DUPLICATE_KEY') {
              throw new ValidationError('Record already exists');
            } else if (error.code === 'FOREIGN_KEY_VIOLATION') {
              throw new ValidationError('Invalid reference');
            } else {
              throw new DatabaseError('Transaction failed', error);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('performTransaction');
      // catch: 1, 两个if/else if分支，每个1+1(嵌套)=2，总计: 1 + 2*2 = 5
      expect(func.complexity).toBeGreaterThanOrEqual(4);
    });

    it('应该正确处理文件操作错误处理', async () => {
      const code = `
        function processFiles(filePaths) {
          const results = [];
          
          for (const filePath of filePaths) {
            try {
              const content = readFileSync(filePath);
              const processed = processContent(content);
              results.push(processed);
            } catch (error) {
              if (error.code === 'ENOENT') {
                console.warn(\`File not found: \${filePath}\`);
              } else if (error.code === 'EACCES') {
                console.warn(\`Permission denied: \${filePath}\`);
              } else {
                console.error(\`Failed to process \${filePath}:\`, error);
                throw error;
              }
            }
          }
          
          return results;
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.name).toBe('processFiles');
      // for: 1, catch: 1+1(嵌套) = 2, 两个if/else if: 2个分支，每个1+2(嵌套)=3，总计: 1 + 2 + 2*3 = 9
      expect(func.complexity).toBeGreaterThanOrEqual(8);
    });
  });

  describe('详细模式验证', () => {
    it('应该在详细模式下提供catch子句的详细信息', async () => {
      const factory = createLightweightFactory();
      const detailCalculator = new ComplexityCalculator({ enableDetails: true }, factory);
      
      try {
        const code = `
          function testFunction() {
            try {
              riskyOperation();
            } catch (error) {
              handleError(error);
            }
          }
        `;

        const result = await detailCalculator.calculateCode(code, 'test.ts');
        expect(result).toHaveLength(1);
        
        const func = result[0];
        // 验证复杂度为1（catch子句）
        expect(func.complexity).toBe(1);
        
        // 验证详细信息中包含catch子句相关的规则应用
        if (func.details) {
          const catchDetails = func.details.find(detail => 
            (detail.rule && detail.rule.includes('catch-clause')) || 
            (detail.description && detail.description.includes('Catch clause'))
          );
          // 至少应该有一些详细信息
          expect(func.details.length).toBeGreaterThan(0);
        }
      } finally {
        await detailCalculator.dispose();
      }
    });
  });

  describe('边界条件', () => {
    it('应该正确处理空catch块', async () => {
      const code = `
        function testFunction() {
          try {
            riskyOperation();
          } catch (error) {
            // 空catch块
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.complexity).toBe(1); // catch子句本身仍然增加复杂度
    });

    it('应该正确处理没有错误参数的catch', async () => {
      const code = `
        function testFunction() {
          try {
            riskyOperation();
          } catch {
            handleError();
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.complexity).toBe(1);
    });

    it('应该正确处理只有重新抛出的catch', async () => {
      const code = `
        function testFunction() {
          try {
            riskyOperation();
          } catch (error) {
            logError(error);
            throw error;
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      expect(func.complexity).toBe(1);
    });

    it('应该正确处理多个catch块（如果语言支持）', async () => {
      const code = `
        function testFunction() {
          try {
            riskyOperation();
          } catch (error) {
            if (error instanceof TypeError) {
              handleTypeError(error);
            } else {
              handleGenericError(error);
            }
          }
        }
      `;

      const result = await calculator.calculateCode(code, 'test.ts');
      expect(result).toHaveLength(1);
      
      const func = result[0];
      // catch: 1, if: 1+1(嵌套) = 2, 总计: 3
      expect(func.complexity).toBe(3);
    });
  });
});