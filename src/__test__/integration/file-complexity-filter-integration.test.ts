import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { writeFileSync, unlinkSync, existsSync, mkdirSync, rmSync } from 'fs';
import { join } from 'path';
import { CLITestingUtils } from '../helpers/cli-testing-utils';
import { ComplexityCalculator } from '../../core/calculator';
import { TextFormatter } from '../../formatters/text';
import { JsonFormatter } from '../../formatters/json';
import { HtmlFormatter } from '../../formatters/html';
import { createFileComplexityFilter, type FilterStatistics } from '../../utils/file-complexity-filter';
import type { AnalysisResult, FileResult, CognitiveConfig } from '../../core/types';
import type { CLIOptions } from '../../config/types';

describe('文件复杂度过滤器集成测试', () => {
  const testDir = join(process.cwd(), 'test-file-filter-integration');
  let testFiles: { [key: string]: string } = {};
  let calculator: ComplexityCalculator;
  let analysisResult: AnalysisResult;
  let config: CognitiveConfig;

  beforeEach(async () => {
    // 创建测试目录
    if (!existsSync(testDir)) {
      mkdirSync(testDir, { recursive: true });
    }

    // 创建不同复杂度的测试文件
    testFiles = {
      'zero-complexity.ts': `
// 复杂度为0的文件 - 只有简单声明
export const CONSTANT = 42;
export interface SimpleInterface {
  id: number;
  name: string;
}
      `,
      'low-complexity.ts': `
// 复杂度为2的文件
function simpleFunction() {
  if (condition) {    // +1
    return true;
  }
  return false;       // +1 (else分支)
}
      `,
      'medium-complexity.ts': `
// 复杂度为5的文件
function mediumFunction() {
  for (let i = 0; i < 10; i++) {      // +1
    if (i % 2 === 0) {               // +1 + 1(嵌套) = +2
      if (i > 5) {                   // +1 + 2(嵌套) = +3
        console.log(i);
      }
    }
  }
}
      `,
      'high-complexity.ts': `
// 复杂度为8的文件
function complexFunction() {
  for (let i = 0; i < 10; i++) {      // +1
    if (i % 2 === 0) {               // +1 + 1(嵌套) = +2
      while (condition1) {           // +1 + 2(嵌套) = +3
        if (condition2 && condition3) { // +1 + 1(&&) + 3(嵌套) = +5
          break;
        }
      }
    }
  }
}
      `
    };

    // 写入测试文件
    Object.entries(testFiles).forEach(([filename, content]) => {
      writeFileSync(join(testDir, filename), content);
    });

    // 初始化计算器和配置
    config = {
      include: [join(testDir, '**/*.ts')],
      exclude: [],
      minComplexity: 0,
      failOnComplexity: 100,
      severityMapping: []
    };

    calculator = new ComplexityCalculator(config);
    analysisResult = await calculator.analyze();
  });

  afterEach(async () => {
    // 清理测试文件
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }

    // 清理所有活动的 CLI 进程
    await CLITestingUtils.cleanupAll();
  });

  describe('基础过滤功能测试', () => {
    test('应该正确识别各文件的复杂度', () => {
      expect(analysisResult.results).toHaveLength(4);
      
      const fileComplexities = analysisResult.results.map(r => ({
        filename: r.filePath.split('/').pop(),
        complexity: r.complexity
      }));

      expect(fileComplexities).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ filename: 'zero-complexity.ts', complexity: 0 }),
          expect.objectContaining({ filename: 'low-complexity.ts', complexity: 2 }),
          expect.objectContaining({ filename: 'medium-complexity.ts', complexity: 5 }),
          expect.objectContaining({ filename: 'high-complexity.ts', complexity: 8 })
        ])
      );
    });

    test('文件复杂度过滤器应该正确过滤文件', async () => {
      const filter = createFileComplexityFilter();
      
      // 测试不同阈值的过滤效果
      const testCases = [
        { threshold: 0, expectedCount: 4, description: '阈值为0应显示所有文件' },
        { threshold: 1, expectedCount: 3, description: '阈值为1应隐藏零复杂度文件' },
        { threshold: 3, expectedCount: 2, description: '阈值为3应只显示复杂度>=3的文件' },
        { threshold: 6, expectedCount: 1, description: '阈值为6应只显示高复杂度文件' },
        { threshold: 10, expectedCount: 0, description: '阈值为10应隐藏所有文件' }
      ];

      for (const testCase of testCases) {
        const filtered = await filter.filterResults(analysisResult.results, {
          threshold: testCase.threshold,
          quiet: true
        });

        expect(filtered.filteredFiles).toHaveLength(testCase.expectedCount);
        expect(filtered.statistics.displayedFiles).toBe(testCase.expectedCount);
        expect(filtered.statistics.hiddenFiles).toBe(4 - testCase.expectedCount);
        expect(filtered.statistics.threshold).toBe(testCase.threshold);
      }
    });
  });

  describe('文本格式化器集成测试', () => {
    test('TextFormatter 应该正确应用文件复杂度过滤', async () => {
      const formatter = new TextFormatter(config);
      
      // 测试默认阈值 (1) - 应该隐藏零复杂度文件
      const options: CLIOptions = {
        paths: [testDir],
        format: 'text',
        minFileComplexity: 1
      };

      const output = await formatter.format(analysisResult, false, options);
      
      // 应该不包含零复杂度文件
      expect(output).not.toContain('zero-complexity.ts');
      // 应该包含其他文件
      expect(output).toContain('low-complexity.ts');
      expect(output).toContain('medium-complexity.ts');
      expect(output).toContain('high-complexity.ts');
      
      // 应该显示过滤统计信息
      expect(output).toContain('显示 3/4 个文件');
      expect(output).toContain('已隐藏 1 个复杂度 < 1 的文件');
    });

    test('TextFormatter 阈值为0时应显示所有文件', async () => {
      const formatter = new TextFormatter(config);
      
      const options: CLIOptions = {
        paths: [testDir],
        format: 'text',
        minFileComplexity: 0
      };

      const output = await formatter.format(analysisResult, false, options);
      
      // 应该包含所有文件
      expect(output).toContain('zero-complexity.ts');
      expect(output).toContain('low-complexity.ts');
      expect(output).toContain('medium-complexity.ts');
      expect(output).toContain('high-complexity.ts');
      
      // 不应显示过滤统计信息（因为没有文件被隐藏）
      expect(output).not.toContain('已隐藏');
    });

    test('TextFormatter 在静默模式下不应显示过滤统计', async () => {
      const formatter = new TextFormatter(config);
      
      const options: CLIOptions = {
        paths: [testDir],
        format: 'text',
        minFileComplexity: 3,
        quiet: true
      };

      const output = await formatter.format(analysisResult, false, options);
      
      // 应该隐藏低复杂度文件但不显示统计信息
      expect(output).not.toContain('zero-complexity.ts');
      expect(output).not.toContain('low-complexity.ts');
      expect(output).not.toContain('显示');
      expect(output).not.toContain('已隐藏');
    });
  });

  describe('JSON格式化器集成测试', () => {
    test('JsonFormatter 应该正确过滤结果数组', async () => {
      const formatter = new JsonFormatter(config);
      
      const options: CLIOptions = {
        paths: [testDir],
        format: 'json',
        minFileComplexity: 3
      };

      const output = await formatter.format(analysisResult, false, options);
      const parsed = JSON.parse(output);
      
      // 验证 JSON 结构
      expect(parsed).toHaveProperty('summary');
      expect(parsed).toHaveProperty('results');
      
      // results 数组应该只包含复杂度 >= 3 的文件
      expect(parsed.results).toHaveLength(2);
      
      const filenames = parsed.results.map((r: FileResult) => r.filePath.split('/').pop());
      expect(filenames).toContain('medium-complexity.ts');
      expect(filenames).toContain('high-complexity.ts');
      expect(filenames).not.toContain('zero-complexity.ts');
      expect(filenames).not.toContain('low-complexity.ts');
      
      // 汇总信息应该基于原始的所有文件
      expect(parsed.summary.filesAnalyzed).toBe(4);
      expect(parsed.summary.functionsAnalyzed).toBeGreaterThan(0);
    });

    test('JsonFormatter 应该包含过滤统计信息', async () => {
      const formatter = new JsonFormatter(config);
      
      const options: CLIOptions = {
        paths: [testDir],
        format: 'json',
        minFileComplexity: 2
      };

      const output = await formatter.format(analysisResult, false, options);
      const parsed = JSON.parse(output);
      
      // 应该包含过滤统计信息
      expect(parsed).toHaveProperty('filterStatistics');
      expect(parsed.filterStatistics).toMatchObject({
        totalFiles: 4,
        displayedFiles: 3,
        hiddenFiles: 1,
        threshold: 2,
        hasFiltering: true
      });
    });

    test('JsonFormatter 不应显示文本格式的过滤统计', async () => {
      const formatter = new JsonFormatter(config);
      
      const options: CLIOptions = {
        paths: [testDir],
        format: 'json',
        minFileComplexity: 5
      };

      const output = await formatter.format(analysisResult, false, options);
      
      // JSON 输出中不应包含文本格式的过滤统计信息
      expect(output).not.toContain('显示');
      expect(output).not.toContain('已隐藏');
      
      // 但应该有 JSON 格式的过滤统计
      const parsed = JSON.parse(output);
      expect(parsed.filterStatistics.hiddenFiles).toBeGreaterThan(0);
    });
  });

  describe('HTML格式化器集成测试', () => {
    test('HtmlFormatter 应该正确应用过滤', async () => {
      const formatter = new HtmlFormatter(config);
      
      const options: CLIOptions = {
        paths: [testDir],
        format: 'html',
        minFileComplexity: 4
      };

      const output = await formatter.format(analysisResult, false, options);
      
      // 应该只包含高复杂度文件
      expect(output).not.toContain('zero-complexity.ts');
      expect(output).not.toContain('low-complexity.ts');
      expect(output).not.toContain('medium-complexity.ts');
      expect(output).toContain('high-complexity.ts');
      
      // HTML 应该包含过滤统计信息
      expect(output).toContain('显示 1/4 个文件');
      expect(output).toContain('已隐藏 3 个');
    });

    test('HtmlFormatter 应该生成有效的HTML结构', async () => {
      const formatter = new HtmlFormatter(config);
      
      const options: CLIOptions = {
        paths: [testDir],
        format: 'html',
        minFileComplexity: 1
      };

      const output = await formatter.format(analysisResult, false, options);
      
      // 验证 HTML 结构
      expect(output).toContain('<!DOCTYPE html>');
      expect(output).toContain('<html');
      expect(output).toContain('</html>');
      expect(output).toContain('<head>');
      expect(output).toContain('<body>');
      
      // 应该包含过滤后的文件信息
      expect(output).toContain('low-complexity.ts');
      expect(output).toContain('medium-complexity.ts');
      expect(output).toContain('high-complexity.ts');
    });
  });

  describe('CLI端到端测试', () => {
    const runCLI = async (args: string[]): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
      const instance = await CLITestingUtils.renderCLI("node", ["dist/cli/index.js", ...args], {
        timeout: 15000,
        cwd: process.cwd(),
        env: process.env as Record<string, string>,
        cleanup: true,
        maxBuffer: 1024 * 1024
      });

      try {
        const exitCode = await instance.waitForExit(15000);
        return {
          stdout: instance.stdout,
          stderr: instance.stderr,
          exitCode
        };
      } finally {
        await CLITestingUtils.cleanup(instance);
      }
    };

    test('CLI应该支持--min-file-complexity参数', async () => {
      const result = await runCLI([testDir, '--min-file-complexity', '3']);
      
      expect(result.exitCode).toBe(0);
      
      // 应该只显示复杂度 >= 3 的文件
      expect(result.stdout).not.toContain('zero-complexity.ts');
      expect(result.stdout).not.toContain('low-complexity.ts');
      expect(result.stdout).toContain('medium-complexity.ts');
      expect(result.stdout).toContain('high-complexity.ts');
      
      // 应该显示过滤统计
      expect(result.stdout).toContain('显示 2/4 个文件');
    });

    test('CLI默认应该隐藏零复杂度文件', async () => {
      const result = await runCLI([testDir]);
      
      expect(result.exitCode).toBe(0);
      
      // 默认应该隐藏零复杂度文件
      expect(result.stdout).not.toContain('zero-complexity.ts');
      expect(result.stdout).toContain('low-complexity.ts');
      expect(result.stdout).toContain('显示 3/4 个文件');
    });

    test('CLI应该支持--min-file-complexity 0显示所有文件', async () => {
      const result = await runCLI([testDir, '--min-file-complexity', '0']);
      
      expect(result.exitCode).toBe(0);
      
      // 应该显示所有文件
      expect(result.stdout).toContain('zero-complexity.ts');
      expect(result.stdout).toContain('low-complexity.ts');
      expect(result.stdout).toContain('medium-complexity.ts');
      expect(result.stdout).toContain('high-complexity.ts');
      
      // 不应显示过滤统计（因为没有隐藏文件）
      expect(result.stdout).not.toContain('已隐藏');
    });

    test('CLI在JSON格式下应该正确过滤', async () => {
      const result = await runCLI([testDir, '--format', 'json', '--min-file-complexity', '5']);
      
      expect(result.exitCode).toBe(0);
      
      const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
      expect(jsonMatch).toBeTruthy();
      
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        expect(parsed.results).toHaveLength(1); // 只有 high-complexity.ts
        expect(parsed.filterStatistics.hiddenFiles).toBe(3);
      }
    });

    test('CLI在静默模式下不应显示过滤统计', async () => {
      const result = await runCLI([testDir, '--quiet', '--min-file-complexity', '2']);
      
      expect(result.exitCode).toBe(0);
      
      // 应该过滤文件但不显示统计信息
      expect(result.stdout).not.toContain('zero-complexity.ts');
      expect(result.stdout).not.toContain('显示');
      expect(result.stdout).not.toContain('已隐藏');
    });
  });

  describe('参数兼容性测试', () => {
    test('--min-file-complexity 应该与 --min 参数兼容', async () => {
      const result = await runCLI([
        testDir, 
        '--min-file-complexity', '1',  // 文件级过滤：隐藏零复杂度文件
        '--min', '3'                   // 函数级过滤：只显示复杂度>=3的函数
      ]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该应用两种过滤
      expect(result.stdout).not.toContain('zero-complexity.ts'); // 文件级过滤
      expect(result.stdout).toContain('medium-complexity.ts');   // 包含的文件
      expect(result.stdout).toContain('high-complexity.ts');     // 包含的文件
    });

    test('--min-file-complexity 应该与 --details 参数兼容', async () => {
      const result = await runCLI([
        testDir,
        '--min-file-complexity', '2',
        '--details'
      ]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该显示详细信息且应用文件过滤
      expect(result.stdout).not.toContain('zero-complexity.ts');
      expect(result.stdout).toContain('('); // 详细信息格式标识
      expect(result.stdout).toContain(':'); // 详细信息格式标识
    });

    test('--min-file-complexity 应该与 --baseline 参数兼容', async () => {
      // 先创建基线文件
      const baselineResult = await runCLI([testDir, '--create-baseline']);
      expect(baselineResult.exitCode).toBe(0);
      
      // 然后测试与基线比较时的过滤功能
      const result = await runCLI([
        testDir,
        '--min-file-complexity', '2',
        '--compare-baseline'
      ]);
      
      // 即使退出码可能不是0（因为可能有基线差异），也应该应用过滤
      expect(result.stdout).not.toContain('zero-complexity.ts');
      expect(result.stdout).toContain('显示 3/4 个文件'); // 过滤统计
      
      // 清理基线文件
      const baselinePath = join(process.cwd(), 'cognitive-baseline.json');
      if (existsSync(baselinePath)) {
        unlinkSync(baselinePath);
      }
    });

    test('无效的 --min-file-complexity 参数应该显示错误', async () => {
      const result = await runCLI([testDir, '--min-file-complexity', 'invalid']);
      
      expect(result.exitCode).toBe(1);
      expect(result.stderr.length > 0 || result.stdout.includes('错误')).toBe(true);
    });
  });

  describe('各种输出格式一致性测试', () => {
    test('不同格式应该应用相同的过滤逻辑', async () => {
      const threshold = 3;
      const expectedFileCount = 2; // medium 和 high complexity 文件
      
      // 测试文本格式
      const textResult = await runCLI([testDir, '--format', 'text', '--min-file-complexity', threshold.toString()]);
      expect(textResult.exitCode).toBe(0);
      expect(textResult.stdout).toContain(`显示 ${expectedFileCount}/4 个文件`);
      
      // 测试JSON格式
      const jsonResult = await runCLI([testDir, '--format', 'json', '--min-file-complexity', threshold.toString()]);
      expect(jsonResult.exitCode).toBe(0);
      
      const jsonMatch = jsonResult.stdout.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        expect(parsed.results).toHaveLength(expectedFileCount);
        expect(parsed.filterStatistics.displayedFiles).toBe(expectedFileCount);
      }
      
      // 测试HTML格式
      const htmlResult = await runCLI([testDir, '--format', 'html', '--min-file-complexity', threshold.toString()]);
      expect(htmlResult.exitCode).toBe(0);
      expect(htmlResult.stdout).toContain(`显示 ${expectedFileCount}/4 个文件`);
    });
  });

  describe('错误处理和优雅降级测试', () => {
    test('文件过滤失败时应该显示所有文件', async () => {
      // 创建一个会导致过滤错误的情况（通过模拟）
      // 这里我们通过极端的阈值来测试错误处理
      const result = await runCLI([testDir, '--min-file-complexity', '999999']);
      
      expect(result.exitCode).toBe(0);
      
      // 即使是极端阈值，也应该正常执行（只是隐藏所有文件）
      expect(result.stdout).toContain('显示 0/4 个文件');
    });

    test('文件复杂度计算异常时的处理', async () => {
      // 创建一个语法错误的文件来测试错误处理
      const errorFile = join(testDir, 'error.ts');
      writeFileSync(errorFile, `
        function broken() {
          if (condition {  // 缺少右括号
            return true;
          }
        }
      `);

      const result = await runCLI([testDir, '--min-file-complexity', '1']);
      
      // 工具应该能处理语法错误并继续处理其他文件
      expect(result.stdout.length > 0 || result.stderr.length > 0).toBe(true);
      expect(result.stdout).toContain('分析汇总'); // 应该仍然有分析结果
    });
  });
});