import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { CommandProcessor } from '../../cli/commands';
import { getCompatibilityService } from '../../utils/compatibility-service';

// Mock all dependencies
vi.mock('../../config/manager', () => ({
  ConfigManager: {
    loadConfig: vi.fn(() => Promise.resolve({
      failOnComplexity: 15,
      exclude: [],
      report: {},
      severityMapping: [],
      enableDetails: false
    }))
  }
}));

vi.mock('../../baseline/manager', () => ({
  BaselineManager: vi.fn(() => ({
    loadBaseline: vi.fn(() => Promise.resolve(null)),
    createBaseline: vi.fn(),
    updateBaseline: vi.fn(),
    compareWithBaseline: vi.fn()
  }))
}));

vi.mock('../../core/calculator', () => ({
  ComplexityCalculator: vi.fn(() => ({
    calculateFile: vi.fn(() => Promise.resolve([]))
  }))
}));

vi.mock('../../formatters/text', () => ({
  TextFormatter: vi.fn(() => ({
    format: vi.fn(() => Promise.resolve('mocked output'))
  }))
}));

vi.mock('../../formatters/json', () => ({
  JsonFormatter: vi.fn(() => ({
    format: vi.fn(() => Promise.resolve('{}'))
  }))
}));

vi.mock('../../formatters/html', () => ({
  HtmlFormatter: vi.fn(() => ({
    format: vi.fn(() => Promise.resolve('<html></html>'))
  }))
}));

vi.mock('./ui-helper', () => ({
  CLIUIHelper: vi.fn(() => ({
    startSpinner: vi.fn(),
    succeedSpinner: vi.fn(),
    stopSpinner: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
    error: vi.fn(),
    errorWithSuggestion: vi.fn(),
    showSummary: vi.fn(),
    showPerformanceTips: vi.fn(),
    showContextualHelp: vi.fn(),
    showQualityGate: vi.fn(),
    updateSpinner: vi.fn(),
    createProgressBar: vi.fn(),
    updateProgress: vi.fn(),
    stopProgress: vi.fn(),
    showFileProgress: vi.fn(),
    showErrorStats: vi.fn(),
    success: vi.fn(),
    formatComplexity: vi.fn()
  }))
}));

vi.mock('fast-glob', () => ({
  default: vi.fn(() => Promise.resolve([]))
}));

vi.mock('fs', () => ({
  promises: {
    stat: vi.fn(),
    mkdir: vi.fn(),
    access: vi.fn(),
    readFile: vi.fn()
  }
}));

// Mock兼容性服务
vi.mock('../../utils/compatibility-service', () => {
  const mockService = {
    checkBabelCodeFrameCompatibility: vi.fn(),
    validateDetailsCommandCompatibility: vi.fn(),
    applyCompatibilitySettings: vi.fn(),
    generateCompatibleCodeFrame: vi.fn(),
    getCompatibilityReport: vi.fn(),
    getDegradeOptions: vi.fn(),
    reset: vi.fn()
  };
  
  return {
    getCompatibilityService: vi.fn(() => mockService),
    CompatibilityService: vi.fn(() => mockService)
  };
});

describe('CLI Commands Compatibility Integration', () => {
  let commandProcessor: CommandProcessor;
  let mockCompatibilityService: any;
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // 重置进程退出模拟
    vi.spyOn(process, 'exit').mockImplementation(() => {
      throw new Error('Process.exit called');
    });
    
    commandProcessor = new CommandProcessor();
    mockCompatibilityService = getCompatibilityService();
    
    // 设置默认的兼容性服务响应
    mockCompatibilityService.checkBabelCodeFrameCompatibility.mockResolvedValue({
      compatible: true,
      version: '7.27.1',
      warnings: [],
      errors: [],
      features: {
        codeFrame: true,
        colorSupport: true,
        errorRecovery: true
      }
    });
    
    mockCompatibilityService.validateDetailsCommandCompatibility.mockReturnValue({
      compatible: true,
      issues: []
    });
    
    mockCompatibilityService.applyCompatibilitySettings.mockImplementation((options) => options);
  });

  describe('execute方法兼容性集成', () => {
    it('应该在execute开始时执行兼容性检查', async () => {
      const options = {
        paths: [],
        quiet: true,
        details: false
      };
      
      try {
        await commandProcessor.execute(options);
      } catch (error) {
        // 忽略process.exit错误
      }
      
      expect(mockCompatibilityService.checkBabelCodeFrameCompatibility).toHaveBeenCalled();
      expect(mockCompatibilityService.applyCompatibilitySettings).toHaveBeenCalledWith(options);
    });

    it('应该处理兼容性检查失败的情况', async () => {
      mockCompatibilityService.checkBabelCodeFrameCompatibility.mockResolvedValue({
        compatible: false,
        warnings: ['测试警告'],
        errors: ['测试错误'],
        features: {
          codeFrame: false,
          colorSupport: false,
          errorRecovery: false
        }
      });
      
      const options = {
        paths: [],
        quiet: false,
        details: false
      };
      
      try {
        await commandProcessor.execute(options);
      } catch (error) {
        // 忽略process.exit错误
      }
      
      expect(mockCompatibilityService.checkBabelCodeFrameCompatibility).toHaveBeenCalled();
    });

    it('应该验证details命令参数兼容性', async () => {
      const options = {
        paths: [],
        quiet: true,
        details: true,
        showContext: true
      };
      
      try {
        await commandProcessor.execute(options);
      } catch (error) {
        // 忽略process.exit错误
      }
      
      expect(mockCompatibilityService.validateDetailsCommandCompatibility).toHaveBeenCalledWith(
        expect.objectContaining({
          details: true,
          showContext: true
        })
      );
    });

    it('应该在参数验证失败时抛出错误', async () => {
      mockCompatibilityService.validateDetailsCommandCompatibility.mockReturnValue({
        compatible: false,
        issues: ['--show-context 参数必须与 --details 一起使用']
      });
      
      const options = {
        paths: [],
        quiet: true,
        details: false,
        showContext: true
      };
      
      try {
        await commandProcessor.execute(options);
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.message).toContain('--show-context 参数必须与 --details 一起使用');
      }
    });

    it('应该应用兼容性设置到选项', async () => {
      const originalOptions = {
        paths: [],
        quiet: true,
        details: true,
        showContext: true
      };
      
      const modifiedOptions = {
        ...originalOptions,
        showContext: false, // 兼容性服务禁用了showContext
        noColors: true
      };
      
      mockCompatibilityService.applyCompatibilitySettings.mockReturnValue(modifiedOptions);
      
      try {
        await commandProcessor.execute(originalOptions);
      } catch (error) {
        // 忽略process.exit错误
      }
      
      expect(mockCompatibilityService.applyCompatibilitySettings).toHaveBeenCalledWith(originalOptions);
      // 验证后续操作使用了修改后的选项
      expect(mockCompatibilityService.validateDetailsCommandCompatibility).toHaveBeenCalledWith(modifiedOptions);
    });
  });

  describe('错误处理兼容性', () => {
    it('应该在兼容性检查期间出错时优雅处理', async () => {
      mockCompatibilityService.checkBabelCodeFrameCompatibility.mockRejectedValue(
        new Error('兼容性检查失败')
      );
      
      const options = {
        paths: [],
        quiet: false,
        details: false
      };
      
      try {
        await commandProcessor.execute(options);
        expect.fail('应该抛出错误');
      } catch (error) {
        expect(error.message).toContain('兼容性检查失败');
      }
    });

    it('应该在quiet模式下静默处理兼容性问题', async () => {
      mockCompatibilityService.checkBabelCodeFrameCompatibility.mockResolvedValue({
        compatible: false,
        warnings: ['测试警告'],
        errors: ['测试错误'],
        features: {
          codeFrame: false,
          colorSupport: false,
          errorRecovery: false
        }
      });
      
      const options = {
        paths: [],
        quiet: true,
        details: false
      };
      
      try {
        await commandProcessor.execute(options);
      } catch (error) {
        // 忽略process.exit错误
      }
      
      // 在quiet模式下，不应该显示UI消息
      expect(mockCompatibilityService.checkBabelCodeFrameCompatibility).toHaveBeenCalled();
    });
  });

  describe('向后兼容性验证', () => {
    it('应该保持现有--details命令的功能不变', async () => {
      const options = {
        paths: ['src/test.ts'],
        quiet: true,
        details: true // 只使用现有的details选项
      };
      
      try {
        await commandProcessor.execute(options);
      } catch (error) {
        // 忽略process.exit错误
      }
      
      // 验证details选项没有被意外修改
      expect(mockCompatibilityService.validateDetailsCommandCompatibility).toHaveBeenCalledWith(
        expect.objectContaining({
          details: true
        })
      );
      
      // 验证兼容性服务被正确调用
      expect(mockCompatibilityService.applyCompatibilitySettings).toHaveBeenCalled();
    });

    it('应该保持现有输出格式选项的功能', async () => {
      const jsonOptions = {
        paths: ['src/test.ts'],
        quiet: true,
        format: 'json' as const
      };
      
      try {
        await commandProcessor.execute(jsonOptions);
      } catch (error) {
        // 忽略process.exit错误
      }
      
      // 验证JSON格式选项没有被兼容性处理影响
      expect(mockCompatibilityService.applyCompatibilitySettings).toHaveBeenCalledWith(
        expect.objectContaining({
          format: 'json'
        })
      );
    });
  });
});