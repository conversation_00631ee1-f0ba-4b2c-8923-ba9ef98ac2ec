import { test, expect, describe, beforeEach, afterEach, beforeAll } from "vitest";
import { UIServer } from "../../ui/server";
import type { CognitiveConfig } from "../../config/types";
import type { AnalysisResult } from "../../core/types";
import { TestUtils } from "../helpers/test-utils";
import { existsSync, unlinkSync } from "fs";
import { join } from "path";

describe("UIServer - HTTP 端点集成测试", () => {
  let server: UIServer;
  let config: CognitiveConfig;
  let baseUrl: string;
  let serverPort: number;
  const testResultPath = join(process.cwd(), '.cognitive-complexity-result.json');

  beforeAll(() => {
    // 全局设置，确保测试环境干净
    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }
  });

  beforeEach(async () => {
    // 创建测试配置
    config = {
      failOnComplexity: 10,
      exclude: [],
      report: {},
      severityMapping: [
        { level: 'Critical', threshold: 20 },
        { level: 'Warning', threshold: 10 }
      ],
      ui: {
        port: 0, // 使用随机端口
        host: 'localhost',
        openBrowser: false
      }
    } as CognitiveConfig;

    // 清理之前的结果文件
    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }

    // 创建并启动服务器
    server = new UIServer(config, { port: 0, openBrowser: false });
    const result = await server.start();
    baseUrl = result.url;
    serverPort = result.port;
  });

  afterEach(async () => {
    // 停止服务器
    if (server) {
      try {
        await server.stop();
      } catch (error) {
        // 忽略停止错误
      }
    }
    
    // 清理结果文件
    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }
  });

  describe("GET / - 主页路由", () => {
    test("应该返回HTML主页", async () => {
      const response = await fetch(`${baseUrl}/`);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('text/html');
      
      const html = await response.text();
      expect(html).toContain('认知复杂度分析 - Web UI');
      expect(html).toContain('正在等待分析结果...');
      expect(html).toContain(`${config.ui?.host}:${serverPort}`);
    });

    test("HTML应该包含JavaScript代码", async () => {
      const response = await fetch(`${baseUrl}/`);
      const html = await response.text();
      
      expect(html).toContain('checkForResults');
      expect(html).toContain('/api/status');
      expect(html).toContain('setInterval');
    });

    test("HTML应该包含CSS样式", async () => {
      const response = await fetch(`${baseUrl}/`);
      const html = await response.text();
      
      expect(html).toContain('<style>');
      expect(html).toContain('container');
      expect(html).toContain('spinner');
      expect(html).toContain('@keyframes spin');
    });
  });

  describe("GET /api/status - 状态API", () => {
    test("没有结果时应该返回正确状态", async () => {
      const response = await fetch(`${baseUrl}/api/status`);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('application/json');
      
      const data = await response.json();
      expect(data).toMatchObject({
        hasResults: false,
        status: 'running',
        timestamp: expect.any(String)
      });
      
      // 验证时间戳格式
      expect(() => new Date(data.timestamp)).not.toThrow();
    });

    test("有结果时应该返回正确状态", async () => {
      // 先存储一个测试结果
      const mockResult = TestUtils.createMockAnalysisResult();
      await server.storeResult(mockResult);
      
      const response = await fetch(`${baseUrl}/api/status`);
      const data = await response.json();
      
      expect(data).toMatchObject({
        hasResults: true,
        status: 'running',
        timestamp: expect.any(String)
      });
    });
  });

  describe("GET /api/result - 结果API", () => {
    test("没有结果时应该返回404", async () => {
      const response = await fetch(`${baseUrl}/api/result`);
      
      expect(response.status).toBe(404);
      
      const data = await response.json();
      expect(data).toMatchObject({
        error: 'No analysis result available'
      });
    });

    test("有结果时应该返回完整的分析数据", async () => {
      // 创建并存储测试结果
      const mockResult = TestUtils.createMockAnalysisResult({
        summary: {
          totalComplexity: 25,
          averageComplexity: 12.5,
          filesAnalyzed: 3,
          functionsAnalyzed: 5,
          highComplexityFunctions: 2
        },
        results: [
          TestUtils.createMockFileResult({
            filePath: 'src/test1.ts',
            complexity: 15,
            functions: [
              TestUtils.createMockFunctionResult({
                name: 'complexFunction',
                complexity: 15,
                line: 1,
                filePath: 'src/test1.ts'
              })
            ]
          })
        ]
      });
      
      await server.storeResult(mockResult);
      
      const response = await fetch(`${baseUrl}/api/result`);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('application/json');
      
      const data = await response.json();
      expect(data).toMatchObject({
        summary: {
          totalComplexity: 25,
          averageComplexity: 12.5,
          filesAnalyzed: 3,
          functionsAnalyzed: 5,
          highComplexityFunctions: 2
        },
        results: expect.arrayContaining([
          expect.objectContaining({
            filePath: 'src/test1.ts',
            complexity: 15
          })
        ])
      });
    });
  });

  describe("GET /report - 报告路由", () => {
    test("没有结果时应该返回404页面", async () => {
      const response = await fetch(`${baseUrl}/report`);
      
      expect(response.status).toBe(404);
      expect(response.headers.get('content-type')).toContain('text/html');
      
      const html = await response.text();
      expect(html).toContain('报告尚未生成');
      expect(html).toContain('请等待分析完成');
      expect(html).toContain('返回首页');
    });

    test("有结果时应该返回HTML报告", async () => {
      // 创建测试结果
      const mockResult = TestUtils.createMockAnalysisResult({
        summary: {
          totalComplexity: 20,
          averageComplexity: 10,
          filesAnalyzed: 2,
          functionsAnalyzed: 2,
          highComplexityFunctions: 1
        }
      });
      
      await server.storeResult(mockResult);
      
      const response = await fetch(`${baseUrl}/report`);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('text/html');
      
      const html = await response.text();
      // HtmlFormatter生成的内容应该包含分析结果
      expect(html).toContain('认知复杂度分析报告');
      expect(html.length).toBeGreaterThan(1000); // 确保返回了完整的HTML报告
    });
  });

  describe("GET /health - 健康检查", () => {
    test("应该返回健康状态", async () => {
      const response = await fetch(`${baseUrl}/health`);
      
      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toContain('application/json');
      
      const data = await response.json();
      expect(data).toMatchObject({
        status: 'ok',
        timestamp: expect.any(String)
      });
      
      // 验证时间戳是有效的ISO字符串
      expect(() => new Date(data.timestamp)).not.toThrow();
    });

    test("健康检查应该总是可用", async () => {
      // 即使没有分析结果，健康检查也应该工作
      const response = await fetch(`${baseUrl}/health`);
      expect(response.status).toBe(200);
    });
  });

  describe("静态文件服务", () => {
    test("应该处理不存在的静态文件", async () => {
      const response = await fetch(`${baseUrl}/nonexistent.css`);
      
      // 静态文件不存在时应该返回404
      expect(response.status).toBe(404);
    });
  });

  describe("错误处理", () => {
    test("不存在的路由应该返回404", async () => {
      const response = await fetch(`${baseUrl}/nonexistent-route`);
      
      expect(response.status).toBe(404);
      expect(response.headers.get('content-type')).toContain('application/json');
      
      const data = await response.json();
      expect(data).toMatchObject({
        error: 'Not Found'
      });
    });

    test("POST请求到GET路由应该返回404", async () => {
      const response = await fetch(`${baseUrl}/api/status`, {
        method: 'POST'
      });
      
      expect(response.status).toBe(404);
    });
  });

  describe("HTTP头部验证", () => {
    test("JSON响应应该有正确的Content-Type", async () => {
      const endpoints = ['/api/status', '/health'];
      
      for (const endpoint of endpoints) {
        const response = await fetch(`${baseUrl}${endpoint}`);
        expect(response.headers.get('content-type')).toContain('application/json');
      }
    });

    test("HTML响应应该有正确的Content-Type", async () => {
      const endpoints = ['/', '/report'];
      
      for (const endpoint of endpoints) {
        const response = await fetch(`${baseUrl}${endpoint}`);
        // 注意：/report在没有结果时返回404，但仍然是HTML
        expect(response.headers.get('content-type')).toContain('text/html');
      }
    });
  });

  describe("响应时间性能", () => {
    test("API端点应该快速响应", async () => {
      const endpoints = ['/api/status', '/health'];
      
      for (const endpoint of endpoints) {
        const start = performance.now();
        const response = await fetch(`${baseUrl}${endpoint}`);
        const duration = performance.now() - start;
        
        expect(response.status).toBe(200);
        expect(duration).toBeLessThan(100); // 应该在100ms内响应
      }
    });

    test("HTML页面应该快速加载", async () => {
      const start = performance.now();
      const response = await fetch(`${baseUrl}/`);
      const duration = performance.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(200); // HTML页面应该在200ms内加载
    });
  });

  describe("并发请求处理", () => {
    test("应该能够处理并发API请求", async () => {
      const promises = Array.from({ length: 10 }, () => 
        fetch(`${baseUrl}/api/status`)
      );
      
      const responses = await Promise.all(promises);
      
      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
    });

    test("应该能够处理混合类型的并发请求", async () => {
      const promises = [
        fetch(`${baseUrl}/`),
        fetch(`${baseUrl}/api/status`),
        fetch(`${baseUrl}/health`),
        fetch(`${baseUrl}/api/result`),
        fetch(`${baseUrl}/report`)
      ];
      
      const responses = await Promise.all(promises);
      
      // 验证每个响应的状态
      expect(responses[0].status).toBe(200); // /
      expect(responses[1].status).toBe(200); // /api/status
      expect(responses[2].status).toBe(200); // /health
      expect(responses[3].status).toBe(404); // /api/result (no data)
      expect(responses[4].status).toBe(404); // /report (no data)
    });
  });
});