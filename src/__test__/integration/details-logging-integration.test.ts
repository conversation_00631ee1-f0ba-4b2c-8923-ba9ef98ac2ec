import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { writeFileSync, existsSync, mkdirSync, rmSync } from "fs";
import { join } from "path";
import { CLITestingUtils } from "../helpers/cli-testing-utils";
import { TestUtils } from "../helpers/test-utils";
import type { CLITestResult } from "../helpers/cli-testing-utils";

describe("详细日志输出功能集成测试", () => {
  const testDir = join(process.cwd(), "test-temp-details-integration");
  
  beforeEach(() => {
    // 创建测试目录
    if (!existsSync(testDir)) {
      mkdirSync(testDir, { recursive: true });
    }
  });
  
  afterEach(async () => {
    // 清理测试文件
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
    
    // 清理所有活动的 CLI 进程
    await CLITestingUtils.cleanupAll();
  });

  /**
   * 执行 CLI 命令的封装函数
   */
  const runCLI = async (args: string[], timeoutMs = 15000): Promise<CLITestResult> => {
    const instance = await CLITestingUtils.renderCLI("node", ["dist/cli/index.js", ...args], {
      timeout: timeoutMs,
      cwd: process.cwd(),
      env: process.env as Record<string, string>,
      cleanup: true,
      maxBuffer: 2048 * 1024 // 2MB buffer for detailed output
    });
    return instance;
  };

  /**
   * 创建复杂的测试文件，用于详细日志测试
   */
  const createDetailedTestFile = (filePath: string, content: string) => {
    const dir = filePath.substring(0, filePath.lastIndexOf('/'));
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }
    writeFileSync(filePath, content, 'utf8');
  };

  describe("基本详细日志输出", () => {
    test("应该输出基本的详细计算步骤", async () => {
      const testFile = join(testDir, "basic-function.ts");
      createDetailedTestFile(testFile, `
function simpleFunction() {
  if (condition) {          // +1
    return true;
  }
  return false;
}

function complexFunction() {
  for (let i = 0; i < 10; i++) {    // +1
    if (i % 2 === 0) {              // +1 + 1(嵌套) = +2
      console.log(i);
    }
  }
}
      `);

      const result = await runCLI([testFile, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        
        // 验证详细日志包含必要的元素
        expect(output).toContain("simpleFunction");
        expect(output).toContain("complexFunction");
        expect(output).toContain("(行:");
        expect(output).toContain(":");
        expect(output).toContain("最终复杂度:");
        
        // 验证文件路径显示
        expect(output).toContain(testFile);
        
        // 验证有具体的复杂度数字
        expect(output).toMatch(/最终复杂度:\s*\d+/);
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });

    test("应该在文本格式中显示详细的计算步骤", async () => {
      const testFile = join(testDir, "step-details.ts");
      createDetailedTestFile(testFile, `
function stepByStepFunction() {
  if (a && b) {                     // +1 + 1 = +2
    for (let i = 0; i < 5; i++) {   // +1 + 1(嵌套) = +2
      if (condition) {              // +1 + 2(嵌套) = +3
        doSomething();
      }
    }
  }
}
      `);

      const result = await runCLI([testFile, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        
        // 验证包含行号信息
        expect(output).toMatch(/L\d+:/);
        
        // 验证包含增量信息
        expect(output).toMatch(/\+\d+/);
        
        // 验证包含累计信息
        expect(output).toMatch(/累计:\s*\d+/);
        
        // 验证包含规则描述或ID
        expect(output).toMatch(/\[[\w-]+\]/);
        
        // 验证包含嵌套层级信息
        expect(output).toMatch(/嵌套层级:\s*\d+/);
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });
  });

  describe("JSON格式详细输出", () => {
    test("应该输出符合Schema的JSON详细数据", async () => {
      const testFile = join(testDir, "json-details.ts");
      createDetailedTestFile(testFile, `
function jsonTestFunction() {
  if (condition1 || condition2) {    // +1 + 1 = +2
    while (loop) {                  // +1 + 1(嵌套) = +2
      if (nested) {                 // +1 + 2(嵌套) = +3
        break;
      }
    }
  }
}
      `);

      const result = await runCLI([testFile, "--details", "--format", "json"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        
        // 提取JSON部分
        const jsonMatch = output.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();

        if (jsonMatch) {
          const jsonData = JSON.parse(jsonMatch[0]);
          
          // 验证基本结构
          expect(jsonData.summary).toBeDefined();
          expect(jsonData.results).toBeDefined();
          expect(Array.isArray(jsonData.results)).toBe(true);
          
          // 验证文件结果包含函数
          const fileResult = jsonData.results[0];
          expect(fileResult.functions).toBeDefined();
          expect(Array.isArray(fileResult.functions)).toBe(true);
          
          // 验证函数包含详细信息
          const functionResult = fileResult.functions[0];
          expect(functionResult.name).toBe("jsonTestFunction");
          expect(functionResult.complexity).toBeGreaterThan(0);
          
          // 验证详细步骤数据结构
          if (functionResult.details) {
            expect(Array.isArray(functionResult.details)).toBe(true);
            
            const firstDetail = functionResult.details[0];
            if (firstDetail) {
              expect(firstDetail.line).toBeTypeOf("number");
              expect(firstDetail.increment).toBeTypeOf("number");
              expect(firstDetail.cumulative).toBeTypeOf("number");
              expect(firstDetail.ruleId).toBeDefined();
              expect(firstDetail.description).toBeDefined();
              expect(firstDetail.nestingLevel).toBeTypeOf("number");
            }
          }
        }
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });

    test("应该在JSON中包含正确的详细计算步骤", async () => {
      const testFile = join(testDir, "json-steps.ts");
      createDetailedTestFile(testFile, `
function calculateSteps() {
  if (a) {              // +1 (累计: 1)
    if (b) {            // +1 + 1(嵌套) = +2 (累计: 3)
      if (c) {          // +1 + 2(嵌套) = +3 (累计: 6)
        return true;
      }
    }
  }
}
      `);

      const result = await runCLI([testFile, "--details", "--format", "json"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        const jsonMatch = output.match(/\{[\s\S]*\}/);
        
        if (jsonMatch) {
          const jsonData = JSON.parse(jsonMatch[0]);
          const functionResult = jsonData.results[0].functions[0];
          
          if (functionResult.details && functionResult.details.length > 0) {
            // 验证累计复杂度递增
            let previousCumulative = 0;
            for (const detail of functionResult.details) {
              expect(detail.cumulative).toBeGreaterThanOrEqual(previousCumulative);
              previousCumulative = detail.cumulative;
            }
            
            // 验证最后的累计复杂度等于总复杂度
            const lastDetail = functionResult.details[functionResult.details.length - 1];
            expect(lastDetail.cumulative).toBe(functionResult.complexity);
          }
        }
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });
  });

  describe("嵌套函数详细处理", () => {
    test("应该正确处理嵌套函数的独立详细计算", async () => {
      const testFile = join(testDir, "nested-functions.ts");
      createDetailedTestFile(testFile, `
function outerFunction() {
  if (outerCondition) {              // +1
    function innerFunction() {
      if (innerCondition) {          // +1 (独立计算)
        return true;
      }
    }
    
    if (anotherCondition) {          // +1 + 1(嵌套) = +2
      innerFunction();
    }
  }
}
      `);

      const result = await runCLI([testFile, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        
        // 验证包含两个函数的信息
        expect(output).toContain("outerFunction");
        expect(output).toContain("innerFunction");
        
        // 验证各自有独立的复杂度计算
        expect(output).toMatch(/outerFunction.*最终复杂度:\s*\d+/);
        expect(output).toMatch(/innerFunction.*最终复杂度:\s*\d+/);
        
        // 验证嵌套层级信息存在
        expect(output).toMatch(/嵌套层级:\s*[0-9]+/);
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });

    test("应该在JSON中正确分离嵌套函数的详细数据", async () => {
      const testFile = join(testDir, "nested-json.ts");
      createDetailedTestFile(testFile, `
function parentFunction() {
  if (parentCondition) {
    const childFunction = () => {
      if (childCondition) {
        return true;
      }
    };
    
    return childFunction();
  }
}
      `);

      const result = await runCLI([testFile, "--details", "--format", "json"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        const jsonMatch = output.match(/\{[\s\S]*\}/);
        
        if (jsonMatch) {
          const jsonData = JSON.parse(jsonMatch[0]);
          const functions = jsonData.results[0].functions;
          
          // 验证包含多个函数
          expect(functions.length).toBeGreaterThanOrEqual(2);
          
          // 验证每个函数都有独立的详细信息
          functions.forEach((func: any) => {
            expect(func.name).toBeDefined();
            expect(func.complexity).toBeGreaterThanOrEqual(0);
            
            if (func.details) {
              expect(Array.isArray(func.details)).toBe(true);
              // 每个函数的详细信息应该从0开始累计
              if (func.details.length > 0) {
                const firstDetail = func.details[0];
                expect(firstDetail.cumulative).toBe(firstDetail.increment);
              }
            }
          });
        }
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });
  });

  describe("错误处理和诊断信息", () => {
    test("应该在遇到未知规则时显示诊断标记", async () => {
      const testFile = join(testDir, "diagnostic-test.ts");
      createDetailedTestFile(testFile, `
function diagnosticFunction() {
  // 这个函数可能会触发一些诊断信息
  if (complexCondition && (nested || deep) && another) {
    while (true) {
      break;
    }
  }
}
      `);

      const result = await runCLI([testFile, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        
        // 验证基本功能正常
        expect(output).toContain("diagnosticFunction");
        expect(output).toMatch(/最终复杂度:\s*\d+/);
        
        // 如果有诊断标记，验证其存在（可选）
        // 这些标记只在特定错误条件下出现
        const hasWarningMarker = output.includes("⚠️");
        const hasQuestionMarker = output.includes("❓");
        
        if (hasWarningMarker || hasQuestionMarker) {
          // 如果有诊断标记，验证它们的格式正确
          console.log("发现诊断标记，这是正常的调试信息");
        }
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });

    test("应该在详细模式下优雅处理解析错误", async () => {
      const testFile = join(testDir, "parse-error.ts");
      createDetailedTestFile(testFile, `
function brokenFunction() {
  if (condition {  // 缺少右括号 - 这应该能被处理
    return true;
  }
}
      `);

      const result = await runCLI([testFile, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        // 解析错误可能导致退出码不为0，这是可以接受的
        expect([0, 1]).toContain(exitCode);

        const output = result.stdout + result.stderr;
        
        // 验证有合理的输出（要么成功分析，要么有错误信息）
        expect(output.length).toBeGreaterThan(0);
        
        // 如果成功分析，应该包含详细信息
        if (exitCode === 0) {
          expect(output).toContain("brokenFunction");
        } else {
          // 如果失败，应该有错误信息
          expect(
            output.includes("解析错误") || 
            output.includes("Parse error") || 
            output.includes("语法错误") ||
            output.includes("SyntaxError")
          ).toBe(true);
        }
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });
  });

  describe("性能和内存测试", () => {
    test("应该在合理时间内完成详细分析", async () => {
      const testFile = join(testDir, "performance-test.ts");
      
      // 创建中等复杂度的测试文件
      const complexCode = `
function performanceTestFunction() {
  for (let i = 0; i < 10; i++) {
    if (i % 2 === 0) {
      for (let j = 0; j < 5; j++) {
        if (j > 2) {
          while (condition) {
            if (nested) {
              break;
            }
          }
        }
      }
    } else if (i % 3 === 0) {
      switch (value) {
        case 1:
          return handleCase1();
        case 2:
          return handleCase2();
        default:
          return handleDefault();
      }
    }
  }
}

function anotherFunction() {
  try {
    if (a && b || c && d) {
      processData();
    }
  } catch (error) {
    handleError(error);
  }
}
      `;
      
      createDetailedTestFile(testFile, complexCode);

      const startTime = Date.now();
      const result = await runCLI([testFile, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(15000); // 15秒超时
        const duration = Date.now() - startTime;
        
        expect(exitCode).toBe(0);
        expect(duration).toBeLessThan(10000); // 应该在10秒内完成
        
        const output = result.stdout + result.stderr;
        expect(output).toContain("performanceTestFunction");
        expect(output).toContain("anotherFunction");
        
        // 验证详细信息的完整性
        expect(output).toMatch(/最终复杂度:\s*\d+/);
        expect(output).toMatch(/L\d+:/);
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });

    test("应该能处理多个文件的详细分析", async () => {
      // 创建多个测试文件
      const files = [
        { name: "file1.ts", content: "function func1() { if(a) return; }" },
        { name: "file2.ts", content: "function func2() { for(let i=0; i<10; i++) { if(i>5) break; } }" },
        { name: "file3.ts", content: "function func3() { try { process(); } catch(e) { handle(e); } }" }
      ];
      
      files.forEach(file => {
        createDetailedTestFile(join(testDir, file.name), file.content);
      });

      const result = await runCLI([testDir, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(20000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        
        // 验证所有文件都被分析
        expect(output).toContain("file1.ts");
        expect(output).toContain("file2.ts");
        expect(output).toContain("file3.ts");
        
        // 验证所有函数都有详细信息
        expect(output).toContain("func1");
        expect(output).toContain("func2");
        expect(output).toContain("func3");
        
        // 验证汇总信息
        expect(output).toContain("分析汇总");
        expect(output).toMatch(/分析文件数:\s*3/);
        expect(output).toMatch(/函数数:\s*3/);
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });
  });

  describe("配置文件集成", () => {
    test("应该支持配置文件中的详细模式设置", async () => {
      const testFile = join(testDir, "config-test.ts");
      const configFile = join(testDir, "cognitive.config.json");
      
      createDetailedTestFile(testFile, `
function configuredFunction() {
  if (a && b) {
    return process();
  }
}
      `);
      
      const config = {
        enableDetails: true,
        rules: {
          enableMixedLogicOperatorPenalty: true
        }
      };
      
      writeFileSync(configFile, JSON.stringify(config, null, 2));

      const result = await runCLI([testFile, "--config", configFile]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        
        // 验证详细信息被启用（如果配置支持）
        expect(output).toContain("configuredFunction");
        expect(output).toMatch(/最终复杂度:\s*\d+/);
        
        // CLI的--details参数应该覆盖配置文件
        const detailsResult = await runCLI([testFile, "--config", configFile, "--details"]);
        const detailsExitCode = await detailsResult.waitForExit(10000);
        expect(detailsExitCode).toBe(0);
        
        await CLITestingUtils.cleanup(detailsResult);
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });
  });

  describe("边界情况和兼容性", () => {
    test("应该处理空文件的详细分析", async () => {
      const testFile = join(testDir, "empty.ts");
      createDetailedTestFile(testFile, "");

      const result = await runCLI([testFile, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        
        // 空文件应该有汇总信息，但没有函数详细信息
        expect(output).toContain("分析汇总");
        expect(output).toMatch(/函数数:\s*0/);
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });

    test("应该处理只有注释的文件", async () => {
      const testFile = join(testDir, "comments-only.ts");
      createDetailedTestFile(testFile, `
// 这是一个只包含注释的文件
/* 
 * 多行注释
 * 没有实际代码
 */
      `);

      const result = await runCLI([testFile, "--details"]);
      
      try {
        const exitCode = await result.waitForExit(10000);
        expect(exitCode).toBe(0);

        const output = result.stdout + result.stderr;
        expect(output).toContain("分析汇总");
        expect(output).toMatch(/函数数:\s*0/);
        
      } finally {
        await CLITestingUtils.cleanup(result);
      }
    });

    test("应该与其他CLI选项正确结合", async () => {
      const testFile = join(testDir, "combined-options.ts");
      createDetailedTestFile(testFile, `
function highComplexityFunction() {
  for (let i = 0; i < 10; i++) {
    if (i % 2 === 0) {
      for (let j = 0; j < 5; j++) {
        if (j > 2) {
          while (condition) {
            if (nested && deep && complex) {
              break;
            }
          }
        }
      }
    }
  }
}
      `);

      // 测试详细模式与其他选项的组合
      const combinations = [
        ["--details", "--min", "5"],
        ["--details", "--sort", "complexity"],
        ["--details", "--format", "json"],
        ["--details", "--fail-on", "20"]
      ];

      for (const args of combinations) {
        const result = await runCLI([testFile, ...args]);
        
        try {
          const exitCode = await result.waitForExit(10000);
          // fail-on 可能导致非零退出码，这是正常的
          expect([0, 1]).toContain(exitCode);
          
          const output = result.stdout + result.stderr;
          expect(output.length).toBeGreaterThan(0);
          
          // 如果成功，应该包含函数信息
          if (exitCode === 0) {
            expect(output).toContain("highComplexityFunction");
          }
          
        } finally {
          await CLITestingUtils.cleanup(result);
        }
      }
    });
  });
});