import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { CLITestingUtils, type CLITestResult } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * 边界条件和极限情况的专项测试
 * 专注于测试系统在极限条件下的行为
 */
describe('Boundary Conditions and Limit Testing', () => {
  let instances: CLITestResult[] = [];

  beforeEach(async () => {
    instances = [];
    await CLITestingUtils.cleanupAll();
  });

  afterEach(async () => {
    for (const instance of instances) {
      try {
        await CLITestingUtils.cleanup(instance);
      } catch (error) {
        console.warn('Cleanup warning:', error);
      }
    }
    instances = [];

    await CLITestingUtils.cleanupAll();
  });

  /**
   * 输入验证边界测试
   */
  describe('Input Validation Boundaries', () => {
    test('应该处理空文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const emptyFile = path.join(tempDir, 'empty.ts');
        await fs.writeFile(emptyFile, '');

        const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', emptyFile], {
          timeout: 8000,
          cwd: process.cwd(),
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
      });
    });

    test('应该处理仅包含空白字符的文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const whitespaceFile = path.join(tempDir, 'whitespace.ts');
        await fs.writeFile(whitespaceFile, '   \n\n\t\t  \n   ');

        const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', whitespaceFile], {
          timeout: 8000,
          cwd: process.cwd(),
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
      });
    });

    test('应该处理仅包含注释的文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const commentFile = path.join(tempDir, 'comments-only.ts');
        await fs.writeFile(
          commentFile,
          `
          // This is a comment
          /* This is a block comment */
          /**
           * This is a JSDoc comment
           * @param param - a parameter
           */
          // Another comment
          /*
           * Multi-line comment
           * with multiple lines
           */
        `
        );

        const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', commentFile], {
          timeout: 8000,
          cwd: process.cwd(),
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
      });
    });

    test('应该处理最小有效的 TypeScript 文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const minimalFile = path.join(tempDir, 'minimal.ts');
        await fs.writeFile(minimalFile, 'export {};');

        const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', minimalFile], {
          timeout: 8000,
          cwd: process.cwd(),
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
      });
    });

    test('应该处理单行极长的代码', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const longLineFile = path.join(tempDir, 'long-line.ts');

        // 创建一个包含极长行的文件
        const longLine = `export function veryLongFunction(${Array.from(
          { length: 20 },
          (_, i) => `param${i}: any`
        ).join(', ')}): any { return ${Array.from({ length: 10 }, (_, i) => `param${i} && `).join('')}true; }`;

        await fs.writeFile(longLineFile, longLine);

        const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', longLineFile], {
          timeout: 8000,
          cwd: process.cwd(),
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('veryLongFunction');
      });
    });
  });

  /**
   * 数值边界测试
   */
  describe('Numerical Boundaries', () => {
    test('应该处理复杂度为0的函数', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const zeroComplexityFile = path.join(tempDir, 'zero-complexity.ts');
        await fs.writeFile(
          zeroComplexityFile,
          `
          export function simpleFunction(): number {
            return 42;
          }

          export const simpleArrow = (): string => 'hello';

          export function noLogic(param: any): any {
            return param;
          }
        `
        );

        const instance = await CLITestingUtils.renderCLI(
          'bun',
          ['run', 'src/index.ts', '--output', 'json', zeroComplexityFile],
          {
            timeout: 8000,
            cwd: process.cwd(),
          }
        );
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);

        const output = JSON.parse(instance.stdout);
        expect(output.some((result: any) => result.complexity === 1)).toBe(true); // 基础复杂度应该是1
      });
    });

    test('应该处理极高复杂度的函数', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const highComplexityFile = path.join(tempDir, 'high-complexity.ts');

        // 创建一个具有很高认知复杂度的函数
        const highComplexityFunction = `
          export function extremelyComplexFunction(input: any): any {
            let result = null;

            ${Array.from(
              { length: 10 },
              (_, i) => `
              if (input.condition${i}) {
                if (input.subCondition${i}A) {
                  if (input.deepCondition${i}A1) {
                    result = 'deep${i}A1';
                  } else if (input.deepCondition${i}A2) {
                    result = 'deep${i}A2';
                  } else {
                    result = 'deep${i}A3';
                  }
                } else if (input.subCondition${i}B) {
                  switch (input.switchValue${i}) {
                    case 'case1':
                      result = 'switch1';
                      break;
                    case 'case2':
                      result = 'switch2';
                      break;
                    default:
                      result = 'switchDefault';
                  }
                } else {
                  for (let j = 0; j < input.loopCount${i}; j++) {
                    if (j % 2 === 0) {
                      result = j;
                    }
                  }
                }
              } else if (input.alternativeCondition${i}) {
                while (input.whileCondition${i}) {
                  if (input.innerWhileCondition${i}) {
                    break;
                  }
                  input.whileCondition${i} = false;
                }
              }
            `
            ).join('')}

            return result;
          }
        `;

        await fs.writeFile(highComplexityFile, highComplexityFunction);

        const instance = await CLITestingUtils.renderCLI(
          'bun',
          ['run', 'src/index.ts', '--output', 'json', highComplexityFile],
          {
            timeout: 10000,
            cwd: process.cwd(),
          }
        );
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);

        const output = JSON.parse(instance.stdout);
        const complexFunction = output.find((result: any) => result.name === 'extremelyComplexFunction');
        expect(complexFunction).toBeDefined();
        expect(complexFunction.complexity).toBeGreaterThan(30); // 应该有很高的复杂度
      });
    });
  });

  /**
   * 特殊语法结构边界测试
   */
  describe('Special Syntax Structure Boundaries', () => {
    test('应该处理复杂的泛型和类型定义', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const genericsFile = path.join(tempDir, 'complex-generics.ts');
        await fs.writeFile(
          genericsFile,
          `
          export interface ComplexInterface<T extends Record<string, any>, U = T> {
            process<V>(input: T & U & V): Promise<Partial<T> | null>;
          }

          export function complexGenericFunction<
            T extends string | number,
            U extends Record<T extends string ? string : number, any>,
            V = Partial<U>
          >(
            param1: T,
            param2: U,
            param3?: V
          ): Promise<(T extends string ? string : number) | V> {
            if (typeof param1 === 'string') {
              if (param1.length > 5) {
                if (param2 && Object.keys(param2).length > 0) {
                  return Promise.resolve(param1 as any);
                } else {
                  return Promise.resolve(param3 as any);
                }
              } else {
                return Promise.resolve(param1 as any);
              }
            } else {
              if (param1 > 10) {
                return Promise.resolve(param1 as any);
              } else {
                return Promise.resolve(param3 as any);
              }
            }
          }

          export class GenericClass<T, U extends keyof T> {
            constructor(private data: T, private key: U) {}

            public getValue(): T[U] {
              return this.data[this.key];
            }

            public processValue<V>(processor: (value: T[U]) => V): V | null {
              const value = this.getValue();
              if (value !== null && value !== undefined) {
                try {
                  return processor(value);
                } catch (error) {
                  return null;
                }
              } else {
                return null;
              }
            }
          }
        `
        );

        const instance = await CLITestingUtils.renderCLI(
          'bun',
          ['run', 'src/index.ts', '--output', 'json', genericsFile],
          {
            timeout: 10000,
            cwd: process.cwd(),
          }
        );
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);

        const output = JSON.parse(instance.stdout);
        expect(output.some((result: any) => result.name === 'complexGenericFunction')).toBe(true);
        expect(output.some((result: any) => result.name === 'getValue')).toBe(true);
        expect(output.some((result: any) => result.name === 'processValue')).toBe(true);
      });
    });

    test('应该处理复杂的异步模式', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const asyncFile = path.join(tempDir, 'complex-async.ts');
        await fs.writeFile(
          asyncFile,
          `
          export async function complexAsyncFunction(input: any[]): Promise<any[]> {
            const results = [];

            for (const item of input) {
              try {
                if (typeof item === 'string') {
                  const processed = await processString(item);
                  if (processed) {
                    results.push(processed);
                  }
                } else if (typeof item === 'number') {
                  const processed = await processNumber(item);
                  if (processed !== null) {
                    results.push(processed);
                  }
                } else if (Array.isArray(item)) {
                  const nestedResults = await complexAsyncFunction(item);
                  results.push(...nestedResults);
                } else {
                  results.push(null);
                }
              } catch (error) {
                console.error('Error processing item:', error);
                results.push(undefined);
              }
            }

            return results;
          }

          async function processString(str: string): Promise<string | null> {
            return new Promise((resolve) => {
              setTimeout(() => {
                if (str.length > 3) {
                  resolve(str.toUpperCase());
                } else {
                  resolve(null);
                }
              }, 1);
            });
          }

          async function processNumber(num: number): Promise<number | null> {
            return new Promise((resolve, reject) => {
              setTimeout(() => {
                if (num > 0) {
                  if (num > 100) {
                    resolve(num * 2);
                  } else {
                    resolve(num + 10);
                  }
                } else if (num === 0) {
                  resolve(null);
                } else {
                  reject(new Error('Negative number'));
                }
              }, 1);
            });
          }

          export function* complexGenerator(input: any[]): Generator<any, void, unknown> {
            for (let i = 0; i < input.length; i++) {
              const item = input[i];

              if (typeof item === 'string') {
                if (item.length > 5) {
                  yield item.slice(0, 5);
                } else {
                  yield item;
                }
              } else if (typeof item === 'number') {
                if (item % 2 === 0) {
                  yield item / 2;
                } else {
                  yield item * 3 + 1;
                }
              } else {
                yield null;
              }
            }
          }
        `
        );

        const instance = await CLITestingUtils.renderCLI(
          'bun',
          ['run', 'src/index.ts', '--output', 'json', asyncFile],
          {
            timeout: 10000,
            cwd: process.cwd(),
          }
        );
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);

        const output = JSON.parse(instance.stdout);
        expect(output.some((result: any) => result.name === 'complexAsyncFunction')).toBe(true);
        expect(output.some((result: any) => result.name === 'processString')).toBe(true);
        expect(output.some((result: any) => result.name === 'processNumber')).toBe(true);
        expect(output.some((result: any) => result.name === 'complexGenerator')).toBe(true);
      });
    });
  });
});

/**
 * 输入验证边界测试
 */
describe('Input Validation Boundaries', () => {
  test('应该处理空文件', async () => {
    const emptyFile = path.join(tempDir, 'empty.ts');
    await fs.writeFile(emptyFile, '');

    const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', emptyFile], {
      timeout: 8000,
      cwd: process.cwd(),
    });
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);
  });

  test('应该处理仅包含空白字符的文件', async () => {
    const whitespaceFile = path.join(tempDir, 'whitespace.ts');
    await fs.writeFile(whitespaceFile, '   \n\n\t\t  \n   ');

    const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', whitespaceFile], {
      timeout: 8000,
      cwd: process.cwd(),
    });
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);
  });

  test('应该处理仅包含注释的文件', async () => {
    const commentFile = path.join(tempDir, 'comments-only.ts');
    await fs.writeFile(
      commentFile,
      `
        // This is a comment
        /* This is a block comment */
        /**
         * This is a JSDoc comment
         * @param param - a parameter
         */
        // Another comment
        /*
         * Multi-line comment
         * with multiple lines
         */
      `
    );

    const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', commentFile], {
      timeout: 8000,
      cwd: process.cwd(),
    });
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);
  });

  test('应该处理最小有效的 TypeScript 文件', async () => {
    const minimalFile = path.join(tempDir, 'minimal.ts');
    await fs.writeFile(minimalFile, 'export {};');

    const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', minimalFile], {
      timeout: 8000,
      cwd: process.cwd(),
    });
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);
  });

  test('应该处理单行极长的代码', async () => {
    const longLineFile = path.join(tempDir, 'long-line.ts');

    // 创建一个包含极长行的文件
    const longLine = `export function veryLongFunction(${Array.from({ length: 100 }, (_, i) => `param${i}: any`).join(
      ', '
    )}): any { return ${Array.from({ length: 50 }, (_, i) => `param${i} && `).join('')}true; }`;

    await fs.writeFile(longLineFile, longLine);

    const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', longLineFile], {
      timeout: 8000,
      cwd: process.cwd(),
    });
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);
    expect(instance.stdout).toContain('veryLongFunction');
  });
});

/**
 * 数值边界测试
 */
describe('Numerical Boundaries', () => {
  test('应该处理复杂度为0的函数', async () => {
    const zeroComplexityFile = path.join(tempDir, 'zero-complexity.ts');
    await fs.writeFile(
      zeroComplexityFile,
      `
        export function simpleFunction(): number {
          return 42;
        }

        export const simpleArrow = (): string => 'hello';

        export function noLogic(param: any): any {
          return param;
        }
      `
    );

    const instance = await CLITestingUtils.renderCLI(
      'bun',
      ['run', 'src/index.ts', '--output', 'json', zeroComplexityFile],
      {
        timeout: 8000,
        cwd: process.cwd(),
      }
    );
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);

    const output = JSON.parse(instance.stdout);
    expect(output.some((result: any) => result.complexity === 1)).toBe(true); // 基础复杂度应该是1
  });

  test('应该处理极高复杂度的函数', async () => {
    const highComplexityFile = path.join(tempDir, 'high-complexity.ts');

    // 创建一个具有很高认知复杂度的函数
    const highComplexityFunction = `
        export function extremelyComplexFunction(input: any): any {
          let result = null;

          ${Array.from(
            { length: 20 },
            (_, i) => `
            if (input.condition${i}) {
              if (input.subCondition${i}A) {
                if (input.deepCondition${i}A1) {
                  result = 'deep${i}A1';
                } else if (input.deepCondition${i}A2) {
                  result = 'deep${i}A2';
                } else {
                  result = 'deep${i}A3';
                }
              } else if (input.subCondition${i}B) {
                switch (input.switchValue${i}) {
                  case 'case1':
                    result = 'switch1';
                    break;
                  case 'case2':
                    result = 'switch2';
                    break;
                  default:
                    result = 'switchDefault';
                }
              } else {
                for (let j = 0; j < input.loopCount${i}; j++) {
                  if (j % 2 === 0) {
                    result = j;
                  }
                }
              }
            } else if (input.alternativeCondition${i}) {
              while (input.whileCondition${i}) {
                if (input.innerWhileCondition${i}) {
                  break;
                }
                input.whileCondition${i} = false;
              }
            }
          `
          ).join('')}

          return result;
        }
      `;

    await fs.writeFile(highComplexityFile, highComplexityFunction);

    const instance = await CLITestingUtils.renderCLI(
      'bun',
      ['run', 'src/index.ts', '--output', 'json', highComplexityFile],
      {
        timeout: 10000,
        cwd: process.cwd(),
      }
    );
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);

    const output = JSON.parse(instance.stdout);
    const complexFunction = output.find((result: any) => result.name === 'extremelyComplexFunction');
    expect(complexFunction).toBeDefined();
    expect(complexFunction.complexity).toBeGreaterThan(50); // 应该有很高的复杂度
  });
});

/**
 * 资源限制边界测试
 */
describe('Resource Limit Boundaries', () => {
  test('应该处理具有大量嵌套的代码', async () => {
    const deepNestingFile = path.join(tempDir, 'deep-nesting.ts');

    // 创建深度嵌套的代码
    let nestedCode = 'export function deeplyNestedFunction(input: any): any {\n';
    let returnCode = '';

    for (let i = 0; i < 30; i++) {
      nestedCode += '  '.repeat(i + 1) + `if (input.level${i}) {\n`;
      returnCode = '  '.repeat(30 - i) + '}\n' + returnCode;
    }

    nestedCode += '  '.repeat(31) + 'return "deep";\n';
    nestedCode += returnCode;
    nestedCode += '}';

    await fs.writeFile(deepNestingFile, nestedCode);

    const instance = await CLITestingUtils.renderCLI(
      'bun',
      ['run', 'src/index.ts', '--output', 'json', deepNestingFile],
      {
        timeout: 15000,
        cwd: process.cwd(),
      }
    );
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);

    const output = JSON.parse(instance.stdout);
    const nestedFunction = output.find((result: any) => result.name === 'deeplyNestedFunction');
    expect(nestedFunction).toBeDefined();
    expect(nestedFunction.complexity).toBeGreaterThan(30);
  });

  test('应该处理包含大量函数的文件', async () => {
    const manyFunctionsFile = path.join(tempDir, 'many-functions.ts');

    // 创建包含大量函数的文件
    let content = '';
    for (let i = 0; i < 200; i++) {
      content += `
          export function function${i}(param: any): any {
            if (param.condition${i}) {
              return param.value${i};
            } else {
              return null;
            }
          }
        `;
    }

    await fs.writeFile(manyFunctionsFile, content);

    const instance = await CLITestingUtils.renderCLI(
      'bun',
      ['run', 'src/index.ts', '--output', 'json', manyFunctionsFile],
      {
        timeout: 20000,
        maxBuffer: 5 * 1024 * 1024, // 5MB buffer
        cwd: process.cwd(),
      }
    );
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);

    const output = JSON.parse(instance.stdout);
    expect(output.length).toBe(200); // 应该分析出所有200个函数
  });

  test('应该处理包含大量类和方法的文件', async () => {
    const manyClassesFile = path.join(tempDir, 'many-classes.ts');

    // 创建包含大量类的文件
    let content = '';
    for (let i = 0; i < 50; i++) {
      content += `
          export class TestClass${i} {
            private value${i}: number = ${i};

            public method${i}A(input: any): any {
              if (input && input.type === '${i}A') {
                return this.value${i} * 2;
              }
              return this.value${i};
            }

            public method${i}B(input: any[]): any[] {
              return input.map(item => {
                if (typeof item === 'number') {
                  return item + this.value${i};
                } else {
                  return item;
                }
              });
            }

            public method${i}C(condition: boolean): string {
              if (condition) {
                if (this.value${i} > 25) {
                  return 'high';
                } else {
                  return 'low';
                }
              } else {
                return 'none';
              }
            }
          }
        `;
    }

    await fs.writeFile(manyClassesFile, content);

    const instance = await CLITestingUtils.renderCLI(
      'bun',
      ['run', 'src/index.ts', '--output', 'json', manyClassesFile],
      {
        timeout: 20000,
        maxBuffer: 10 * 1024 * 1024, // 10MB buffer
        cwd: process.cwd(),
      }
    );
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);

    const output = JSON.parse(instance.stdout);
    expect(output.length).toBe(150); // 50个类 × 3个方法 = 150个函数
  });
});

/**
 * 特殊语法结构边界测试
 */
describe('Special Syntax Structure Boundaries', () => {
  test('应该处理复杂的泛型和类型定义', async () => {
    const genericsFile = path.join(tempDir, 'complex-generics.ts');
    await fs.writeFile(
      genericsFile,
      `
        export interface ComplexInterface<T extends Record<string, any>, U = T> {
          process<V>(input: T & U & V): Promise<Partial<T> | null>;
        }

        export function complexGenericFunction<
          T extends string | number,
          U extends Record<T extends string ? string : number, any>,
          V = Partial<U>
        >(
          param1: T,
          param2: U,
          param3?: V
        ): Promise<(T extends string ? string : number) | V> {
          if (typeof param1 === 'string') {
            if (param1.length > 5) {
              if (param2 && Object.keys(param2).length > 0) {
                return Promise.resolve(param1 as any);
              } else {
                return Promise.resolve(param3 as any);
              }
            } else {
              return Promise.resolve(param1 as any);
            }
          } else {
            if (param1 > 10) {
              return Promise.resolve(param1 as any);
            } else {
              return Promise.resolve(param3 as any);
            }
          }
        }

        export class GenericClass<T, U extends keyof T> {
          constructor(private data: T, private key: U) {}

          public getValue(): T[U] {
            return this.data[this.key];
          }

          public processValue<V>(processor: (value: T[U]) => V): V | null {
            const value = this.getValue();
            if (value !== null && value !== undefined) {
              try {
                return processor(value);
              } catch (error) {
                return null;
              }
            } else {
              return null;
            }
          }
        }
      `
    );

    const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', '--output', 'json', genericsFile], {
      timeout: 10000,
      cwd: process.cwd(),
    });
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);

    const output = JSON.parse(instance.stdout);
    expect(output.some((result: any) => result.name === 'complexGenericFunction')).toBe(true);
    expect(output.some((result: any) => result.name === 'getValue')).toBe(true);
    expect(output.some((result: any) => result.name === 'processValue')).toBe(true);
  });

  test('应该处理复杂的异步模式', async () => {
    const asyncFile = path.join(tempDir, 'complex-async.ts');
    await fs.writeFile(
      asyncFile,
      `
        export async function complexAsyncFunction(input: any[]): Promise<any[]> {
          const results = [];

          for (const item of input) {
            try {
              if (typeof item === 'string') {
                const processed = await processString(item);
                if (processed) {
                  results.push(processed);
                }
              } else if (typeof item === 'number') {
                const processed = await processNumber(item);
                if (processed !== null) {
                  results.push(processed);
                }
              } else if (Array.isArray(item)) {
                const nestedResults = await complexAsyncFunction(item);
                results.push(...nestedResults);
              } else {
                results.push(null);
              }
            } catch (error) {
              console.error('Error processing item:', error);
              results.push(undefined);
            }
          }

          return results;
        }

        async function processString(str: string): Promise<string | null> {
          return new Promise((resolve) => {
            setTimeout(() => {
              if (str.length > 3) {
                resolve(str.toUpperCase());
              } else {
                resolve(null);
              }
            }, 1);
          });
        }

        async function processNumber(num: number): Promise<number | null> {
          return new Promise((resolve, reject) => {
            setTimeout(() => {
              if (num > 0) {
                if (num > 100) {
                  resolve(num * 2);
                } else {
                  resolve(num + 10);
                }
              } else if (num === 0) {
                resolve(null);
              } else {
                reject(new Error('Negative number'));
              }
            }, 1);
          });
        }

        export function* complexGenerator(input: any[]): Generator<any, void, unknown> {
          for (let i = 0; i < input.length; i++) {
            const item = input[i];

            if (typeof item === 'string') {
              if (item.length > 5) {
                yield item.slice(0, 5);
              } else {
                yield item;
              }
            } else if (typeof item === 'number') {
              if (item % 2 === 0) {
                yield item / 2;
              } else {
                yield item * 3 + 1;
              }
            } else {
              yield null;
            }
          }
        }
      `
    );

    const instance = await CLITestingUtils.renderCLI('bun', ['run', 'src/index.ts', '--output', 'json', asyncFile], {
      timeout: 10000,
      cwd: process.cwd(),
    });
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);

    const output = JSON.parse(instance.stdout);
    expect(output.some((result: any) => result.name === 'complexAsyncFunction')).toBe(true);
    expect(output.some((result: any) => result.name === 'processString')).toBe(true);
    expect(output.some((result: any) => result.name === 'processNumber')).toBe(true);
    expect(output.some((result: any) => result.name === 'complexGenerator')).toBe(true);
  });

  test('应该处理复杂的装饰器和元数据', async () => {
    const decoratorsFile = path.join(tempDir, 'decorators.ts');
    await fs.writeFile(
      decoratorsFile,
      `
        // 装饰器工厂函数
        function ComplexDecorator(config: any) {
          return function(target: any, propertyKey?: string, descriptor?: PropertyDescriptor) {
            if (descriptor) {
              const originalMethod = descriptor.value;
              descriptor.value = function(...args: any[]) {
                if (config.validate) {
                  if (args.length === 0) {
                    throw new Error('No arguments provided');
                  }

                  for (const arg of args) {
                    if (config.requireNonNull && (arg === null || arg === undefined)) {
                      throw new Error('Null argument not allowed');
                    }
                  }
                }

                let result;
                if (config.async) {
                  result = Promise.resolve(originalMethod.apply(this, args));
                } else {
                  result = originalMethod.apply(this, args);
                }

                if (config.transform) {
                  if (config.async) {
                    return result.then(config.transform);
                  } else {
                    return config.transform(result);
                  }
                } else {
                  return result;
                }
              };
            }
            return descriptor;
          };
        }

        export class DecoratedClass {
          @ComplexDecorator({
            validate: true,
            requireNonNull: true,
            transform: (result: any) => result?.toString()?.toUpperCase()
          })
          public decoratedMethod(input: any): string {
            if (typeof input === 'string') {
              if (input.length > 10) {
                return input.substring(0, 10);
              } else if (input.length > 5) {
                return input + '_processed';
              } else {
                return input;
              }
            } else if (typeof input === 'number') {
              if (input > 100) {
                return String(input * 2);
              } else {
                return String(input + 50);
              }
            } else {
              return 'unknown';
            }
          }

          @ComplexDecorator({
            validate: false,
            async: true,
            transform: (result: any) => Array.isArray(result) ? result.length : 0
          })
          public async asyncDecoratedMethod(items: any[]): Promise<any[]> {
            const results = [];

            for (const item of items) {
              if (typeof item === 'string') {
                results.push(item.toUpperCase());
              } else if (typeof item === 'number') {
                if (item > 0) {
                  results.push(item * 2);
                } else {
                  results.push(0);
                }
              } else {
                results.push(null);
              }
            }

            return results;
          }
        }
      `
    );

    const instance = await CLITestingUtils.renderCLI(
      'bun',
      ['run', 'src/index.ts', '--output', 'json', decoratorsFile],
      {
        timeout: 10000,
        cwd: process.cwd(),
      }
    );
    instances.push(instance);

    const exitCode = await instance.waitForExit();
    expect(exitCode).toBe(0);

    const output = JSON.parse(instance.stdout);
    expect(output.some((result: any) => result.name === 'decoratedMethod')).toBe(true);
    expect(output.some((result: any) => result.name === 'asyncDecoratedMethod')).toBe(true);
  });
});
