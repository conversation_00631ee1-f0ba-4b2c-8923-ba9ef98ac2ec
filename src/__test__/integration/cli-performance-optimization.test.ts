import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { CLITestingUtils } from '../helpers/cli-testing-utils';
import { ConcurrentTestManager } from '../helpers/concurrent-test-manager';
import { CLITestDebugEnhancer } from '../helpers/debug-enhancer';
import type { TestTask } from '../helpers/concurrent-test-manager';

describe('CLI 测试性能优化验证', () => {
  beforeEach(() => {
    // 重置性能监控数据
    CLITestingUtils.resetPerformanceMonitor();
    CLITestDebugEnhancer.clearDebugData();
  });

  afterEach(async () => {
    // 清理所有进程和调试数据
    await CLITestingUtils.cleanupAll();
    CLITestDebugEnhancer.clearDebugData();
  });

  describe('性能监控和优化', () => {
    it('应该监控CLI执行性能并提供优化建议', async () => {
      // 执行多个CLI命令
      const commands = [
        ['echo', ['Performance Test 1']],
        ['echo', ['Performance Test 2']],
        ['echo', ['Performance Test 3']]
      ];

      for (const [command, args] of commands) {
        const instance = await CLITestingUtils.renderCLI(command, args as string[]);
        await instance.waitForExit();
        await CLITestingUtils.cleanup(instance);
      }

      // 获取性能指标
      const metrics = CLITestingUtils.getPerformanceMetrics();
      expect(Object.keys(metrics)).toHaveLength(3);

      // 获取优化建议
      const suggestions = CLITestingUtils.getPerformanceSuggestions();
      expect(Array.isArray(suggestions)).toBe(true);

      // 获取进程池状态
      const poolStatus = CLITestingUtils.getProcessPoolStatus();
      expect(poolStatus.activeProcesses).toBeGreaterThanOrEqual(0);
      expect(typeof poolStatus.poolCount).toBe('number');
      expect(typeof poolStatus.totalInstances).toBe('number');
    });

    it('应该提供测试套件自动优化建议', async () => {
      // 执行一些测试以产生性能数据
      const instance = await CLITestingUtils.renderCLI('echo', ['Auto Optimization Test']);
      await instance.waitForExit();
      await CLITestingUtils.cleanup(instance);

      // 获取优化建议
      const optimization = CLITestingUtils.optimizeTestSuite();
      
      expect(optimization.recommendations).toBeDefined();
      expect(Array.isArray(optimization.recommendations)).toBe(true);
      expect(optimization.optimizedConfig).toBeDefined();
      expect(typeof optimization.optimizedConfig).toBe('object');
    });

    it('应该支持批量测试执行', async () => {
      const testCases = [
        () => CLITestingUtils.renderCLI('echo', ['Batch Test 1']).then(i => i.waitForExit()),
        () => CLITestingUtils.renderCLI('echo', ['Batch Test 2']).then(i => i.waitForExit()),
        () => CLITestingUtils.renderCLI('echo', ['Batch Test 3']).then(i => i.waitForExit())
      ];

      const results = await CLITestingUtils.runTestBatch(testCases, 2);
      
      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.duration).toBeGreaterThan(0);
        expect(result.result !== undefined || result.error !== undefined).toBe(true);
      });
    });
  });

  describe('并发测试管理', () => {
    it('应该能够管理并发测试任务', async () => {
      const manager = new ConcurrentTestManager({
        maxConcurrency: 2,
        resourceLimit: {
          maxMemoryMB: 100,
          maxProcesses: 5
        },
        scheduling: {
          batchSize: 2,
          delayBetweenBatches: 50,
          priorityLevels: 2
        },
        monitoring: {
          collectMetrics: true,
          logPerformance: false
        }
      });

      const tasks: TestTask[] = [
        {
          id: 'task1',
          priority: 8,
          command: 'echo',
          args: ['Concurrent Task 1'],
          expectedDuration: 100
        },
        {
          id: 'task2',
          priority: 6,
          command: 'echo',
          args: ['Concurrent Task 2'],
          expectedDuration: 150
        },
        {
          id: 'task3',
          priority: 9,
          command: 'echo',
          args: ['Concurrent Task 3'],
          expectedDuration: 80
        }
      ];

      const { results, metrics } = await manager.executeTasks(tasks);

      // 验证结果
      expect(results).toHaveLength(3);
      expect(metrics.totalTasks).toBe(3);
      expect(metrics.completedTasks).toBeGreaterThan(0);
      expect(metrics.averageDuration).toBeGreaterThan(0);
      expect(metrics.throughput).toBeGreaterThan(0);

      // 验证任务按优先级执行（优先级9的task3应该最先完成）
      const task3Result = results.find(r => r.taskId === 'task3');
      expect(task3Result?.success).toBe(true);
    });

    it('应该正确处理任务失败和重试', async () => {
      const manager = new ConcurrentTestManager({
        maxConcurrency: 1,
        monitoring: { collectMetrics: true, logPerformance: false }
      });

      const tasks: TestTask[] = [
        {
          id: 'failing_task',
          priority: 5,
          command: 'nonexistent_command_12345',
          args: [],
          retryCount: 2
        }
      ];

      const { results, metrics } = await manager.executeTasks(tasks);

      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(false);
      expect(results[0].retries).toBe(2);
      expect(results[0].error).toBeDefined();
      expect(metrics.failedTasks).toBe(1);
    });
  });

  describe('调试体验增强', () => {
    it('应该提供增强的调试功能', async () => {
      const { result, session } = await CLITestDebugEnhancer.executeWithDebug(
        'echo',
        ['Debug Test']
      );

      expect(result).toBeDefined();
      expect(session.sessionId).toBeDefined();
      expect(session.command).toBe('echo');
      expect(session.args).toEqual(['Debug Test']);
      expect(session.startTime).toBeGreaterThan(0);
      expect(session.endTime).toBeGreaterThan(session.startTime);
      expect(session.snapshots.length).toBeGreaterThan(0);

      await CLITestingUtils.cleanup(result);
    });

    it('应该能够诊断测试失败', async () => {
      const error = new Error('Test command timed out after 5000ms');
      
      const diagnosis = CLITestDebugEnhancer.diagnoseTestFailure(error);
      
      expect(diagnosis.failureType).toBe('timeout');
      expect(diagnosis.severity).toBeDefined();
      expect(diagnosis.rootCause).toContain('超时');
      expect(diagnosis.suggestions.length).toBeGreaterThan(0);
      expect(diagnosis.evidence.length).toBeGreaterThan(0);
    });

    it('应该生成详细的调试报告', async () => {
      // 创建一个调试会话
      try {
        await CLITestDebugEnhancer.executeWithDebug('echo', ['Report Test']);
      } catch (error) {
        // 忽略错误，我们只需要会话数据
      }

      const sessions = CLITestDebugEnhancer.getSessionsSummary();
      expect(sessions.length).toBeGreaterThan(0);

      if (sessions.length > 0) {
        const report = CLITestDebugEnhancer.generateDebugReport(sessions[0].sessionId);
        expect(report).toContain('CLI 测试调试报告');
        expect(report).toContain('会话ID');
        expect(report).toContain('命令');
      }
    });

    it('应该创建格式化的失败报告', async () => {
      const testName = 'Sample Failed Test';
      const error = new Error('Command not found: missing_command');

      const report = CLITestDebugEnhancer.createFailureReport(testName, error);
      
      expect(report).toContain('❌ 测试失败');
      expect(report).toContain(testName);
      expect(report).toContain('失败类型');
      expect(report).toContain('根本原因');
      expect(report).toContain('建议解决方案');
    });
  });

  describe('资源竞争减少', () => {
    it('应该通过进程池减少资源竞争', async () => {
      const initialPoolStatus = CLITestingUtils.getProcessPoolStatus();
      
      // 执行多个相同的命令，应该能够复用进程
      const instances: any[] = [];
      
      for (let i = 0; i < 3; i++) {
        const instance = await CLITestingUtils.renderCLI('echo', [`Pool Test ${i}`]);
        instances.push(instance);
        await instance.waitForExit();
      }

      // 清理实例（这应该将一些进程放回池中）
      for (const instance of instances) {
        await CLITestingUtils.cleanup(instance);
      }

      const finalPoolStatus = CLITestingUtils.getProcessPoolStatus();
      
      // 验证进程池工作正常
      expect(finalPoolStatus.activeProcesses).toBeGreaterThanOrEqual(0);
      expect(typeof finalPoolStatus.poolCount).toBe('number');
    });

    it('应该在资源限制下正确管理并发', async () => {
      const manager = new ConcurrentTestManager({
        maxConcurrency: 2,
        resourceLimit: {
          maxMemoryMB: 50, // 较小的内存限制
          maxProcesses: 3
        }
      });

      // 检查管理器状态
      const initialStatus = manager.getRunningTasksStatus();
      expect(initialStatus.count).toBe(0);

      const tasks: TestTask[] = Array.from({ length: 5 }, (_, i) => ({
        id: `resource_test_${i}`,
        priority: 5,
        command: 'echo',
        args: [`Resource Test ${i}`]
      }));

      const { results, metrics } = await manager.executeTasks(tasks);
      
      expect(results).toHaveLength(5);
      expect(metrics.maxConcurrency).toBe(2);
      expect(metrics.totalTasks).toBe(5);
      
      // 验证资源使用在合理范围内
      expect(metrics.memoryPeakMB).toBeLessThan(100);
    });
  });

  describe('性能基准验证', () => {
    it('应该满足性能基准要求', async () => {
      const startTime = performance.now();
      
      // 执行一组标准测试
      const testCount = 10;
      const results = [];
      
      for (let i = 0; i < testCount; i++) {
        const instance = await CLITestingUtils.renderCLI('echo', [`Benchmark ${i}`]);
        const exitCode = await instance.waitForExit();
        results.push(exitCode);
        await CLITestingUtils.cleanup(instance);
      }
      
      const totalTime = performance.now() - startTime;
      const avgTimePerTest = totalTime / testCount;
      
      // 验证性能基准
      expect(avgTimePerTest).toBeLessThan(1000); // 每个测试应该少于1秒
      expect(results.every(code => code === 0)).toBe(true);
      
      // 验证吞吐量
      const throughput = (testCount / totalTime) * 1000; // 测试/秒
      expect(throughput).toBeGreaterThan(5); // 至少5测试/秒
      
      console.log(`\n性能基准结果:`);
      console.log(`总执行时间: ${totalTime.toFixed(0)}ms`);
      console.log(`平均每测试: ${avgTimePerTest.toFixed(0)}ms`);
      console.log(`吞吐量: ${throughput.toFixed(1)} 测试/秒`);
    });
  });
});