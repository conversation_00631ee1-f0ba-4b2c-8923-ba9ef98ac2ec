import { describe, test, expect } from 'vitest';
import { getDefaultValidationService } from '../../utils/concurrent-validation-service';
import type { CLIOptions } from '../../config/types';

describe('Parameter Validation Integration', () => {
  test('应该正确注册所有验证规则', () => {
    const service = getDefaultValidationService();
    const rules = service.getRules();
    
    expect(rules).toHaveLength(9);
    
    // 验证规则名称和优先级
    const expectedRules = [
      { name: 'details-context-dependency', priority: 1 },
      { name: 'smart-filter-validation', priority: 2 },
      { name: 'path-validation', priority: 3 },
      { name: 'output-format-validation', priority: 4 },
      { name: 'debug-parameter-validation', priority: 5 },
      { name: 'breakpoint-parameter-validation', priority: 6 },
      { name: 'ui-parameter-validation', priority: 7 },
      { name: 'smart-filter-dependency', priority: 8 },
      { name: 'output-enhancement-suggestion', priority: 9 }
    ];
    
    expectedRules.forEach(expected => {
      const rule = rules.find(r => r.name === expected.name);
      expect(rule).toBeDefined();
      expect(rule?.priority).toBe(expected.priority);
    });
  });

  test('应该并发执行所有验证规则', async () => {
    const service = getDefaultValidationService();
    
    const options: CLIOptions = {
      paths: ['.'],
      debugLevel: 'trace',
      breakOnRule: ['test'],
      open: true,
      maxContextItems: 5,
      outputDir: './reports',
      format: 'text'
    };

    const summary = await service.validateConcurrently(options);
    
    expect(summary.valid).toBe(true);
    expect(summary.totalRules).toBe(9);
    expect(summary.passedRules).toBe(9);
    expect(summary.failedRules).toBe(0);
    expect(summary.executionTime).toBeGreaterThan(0);
    
    // 验证警告信息
    const expectedWarnings = [
      '--debug-level 参数仅在使用 --debug 时有效',
      '--break-on-rule 参数仅在使用 --enable-breakpoints 时有效',
      '--open 参数仅在使用 --ui 时有效',
      '--max-context-items 参数仅在使用 --show-context 或 --show-all-context 时有效',
      '建议使用 --format json 或 --format html 以获得更好的文件输出效果'
    ];
    
    expectedWarnings.forEach(warning => {
      expect(summary.warnings).toContain(warning);
    });
  });

  test('应该在正确的参数组合下不产生新增规则的警告', async () => {
    const service = getDefaultValidationService();
    
    const options: CLIOptions = {
      paths: ['.'],
      debug: true,
      debugLevel: 'trace',
      debugOutput: './debug.log',
      visualReport: true,
      enableBreakpoints: true,
      breakOnRule: ['test'],
      stepByStep: true,
      ui: true,
      open: true,
      details: true,
      showContext: true,
      maxContextItems: 5,
      minComplexityIncrement: 1,
      outputDir: './reports',
      format: 'html' // 使用html格式避免现有规则的JSON警告
    };

    const summary = await service.validateConcurrently(options);
    
    expect(summary.valid).toBe(true);
    
    // 验证新增的验证规则不会产生警告
    const newRuleWarnings = summary.warnings.filter(warning => 
      warning.includes('--debug-level 参数仅在使用 --debug 时有效') ||
      warning.includes('--debug-output 参数仅在使用 --debug 时有效') ||
      warning.includes('--visual-report 参数仅在使用 --debug 时有效') ||
      warning.includes('--break-on-rule 参数仅在使用 --enable-breakpoints 时有效') ||
      warning.includes('--step-by-step 参数仅在使用 --enable-breakpoints 时有效') ||
      warning.includes('--open 参数仅在使用 --ui 时有效') ||
      warning.includes('--max-context-items 参数仅在使用 --show-context 或 --show-all-context 时有效') ||
      warning.includes('--min-complexity-increment 参数仅在使用 --show-context 或 --show-all-context 时有效') ||
      warning.includes('建议使用 --format json 或 --format html 以获得更好的文件输出效果')
    );
    
    expect(newRuleWarnings).toHaveLength(0);
  });

  test('应该在有路径错误时返回无效结果', async () => {
    const service = getDefaultValidationService();
    
    const options: CLIOptions = {
      paths: ['path/with/invalid<chars>'],
    };

    const summary = await service.validateConcurrently(options);
    
    expect(summary.valid).toBe(false);
    expect(summary.errors.length).toBeGreaterThan(0);
    expect(summary.errors.some(error => error.includes('路径包含无效字符'))).toBe(true);
  });

  test('应该支持性能测试', async () => {
    const service = getDefaultValidationService();
    
    const options: CLIOptions = {
      paths: ['.'],
    };

    const startTime = performance.now();
    const summary = await service.validateConcurrently(options);
    const totalTime = performance.now() - startTime;
    
    expect(summary.executionTime).toBeLessThan(50); // 应该在50ms内完成
    expect(totalTime).toBeLessThan(100); // 整体时间应该在100ms内
    expect(summary.valid).toBe(true);
  });

  test('应该正确处理空的参数组合', async () => {
    const service = getDefaultValidationService();
    
    const options: CLIOptions = {
      paths: ['.']
    };

    const summary = await service.validateConcurrently(options);
    
    expect(summary.valid).toBe(true);
    expect(summary.warnings).toHaveLength(0);
    expect(summary.errors).toHaveLength(0);
  });

  test('应该按优先级顺序处理规则结果', async () => {
    const service = getDefaultValidationService();
    
    const options: CLIOptions = {
      paths: ['.'],
      debugLevel: 'trace',
      open: true
    };

    const summary = await service.validateConcurrently(options);
    
    // 结果应该按规则名称排序（并发执行后的统一排序）
    const ruleNames = summary.ruleResults.map(r => r.ruleName);
    const sortedNames = [...ruleNames].sort();
    expect(ruleNames).toEqual(sortedNames);
  });
});