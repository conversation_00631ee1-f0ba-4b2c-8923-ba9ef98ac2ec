import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { UIServer } from "../../ui/server";
import type { CognitiveConfig } from "../../config/types";
import { TestUtils, PerformanceTestUtils } from "../helpers/test-utils";
import { existsSync, unlinkSync } from "fs";
import { join } from "path";

describe("UIServer - 向后兼容性和性能验证", () => {
  let server: UIServer;
  let config: CognitiveConfig;
  const testResultPath = join(process.cwd(), '.cognitive-complexity-result.json');

  beforeEach(() => {
    config = {
      failOnComplexity: 10,
      exclude: [],
      report: {},
      severityMapping: [
        { level: 'Critical', threshold: 20 },
        { level: 'Warning', threshold: 10 }
      ],
      ui: {
        port: 0,
        host: 'localhost',
        openBrowser: false
      }
    } as CognitiveConfig;

    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }
  });

  afterEach(async () => {
    if (server) {
      try {
        await server.stop();
      } catch (error) {
        // 忽略停止错误
      }
    }
    
    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }
  });

  describe("Express 到 Hono 迁移兼容性验证", () => {
    test("UIServer 公共 API 保持完全不变", () => {
      server = new UIServer(config, { openBrowser: false });
      
      // 验证所有公共方法存在且类型正确
      expect(typeof server.start).toBe('function');
      expect(typeof server.stop).toBe('function');
      expect(typeof server.storeResult).toBe('function');
      expect(typeof server.cleanupResult).toBe('function');
      
      // 验证这些方法返回正确的Promise类型
      expect(server.start()).toBeInstanceOf(Promise);
      expect(server.stop()).toBeInstanceOf(Promise);
    });

    test("构造函数参数兼容性", () => {
      // 测试各种配置组合的兼容性
      const configs = [
        config,
        { ...config, ui: undefined },
        { ...config, ui: { port: 3000 } },
        { ...config, ui: { port: 3000, host: '0.0.0.0', openBrowser: false } }
      ];
      
      configs.forEach(testConfig => {
        expect(() => new UIServer(testConfig)).not.toThrow();
      });
    });

    test("响应格式向后兼容性", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 测试所有API端点的响应格式
      const endpoints = [
        { path: '/api/status', expectedKeys: ['hasResults', 'status', 'timestamp'] },
        { path: '/health', expectedKeys: ['status', 'timestamp'] }
      ];

      for (const endpoint of endpoints) {
        const response = await fetch(`${url}${endpoint.path}`);
        expect(response.status).toBe(200);
        
        const data = await response.json();
        endpoint.expectedKeys.forEach(key => {
          expect(data).toHaveProperty(key);
        });
      }
    });

    test("错误响应格式兼容性", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 测试404响应格式
      const notFoundResponse = await fetch(`${url}/api/result`);
      expect(notFoundResponse.status).toBe(404);
      
      const notFoundData = await notFoundResponse.json();
      expect(notFoundData).toHaveProperty('error');
      expect(typeof notFoundData.error).toBe('string');

      // 测试不存在的路由
      const invalidRouteResponse = await fetch(`${url}/invalid-route`);
      expect(invalidRouteResponse.status).toBe(404);
      
      const invalidRouteData = await invalidRouteResponse.json();
      expect(invalidRouteData).toHaveProperty('error');
    });

    test("HTML 内容兼容性", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 验证主页HTML内容
      const homeResponse = await fetch(`${url}/`);
      const homeHtml = await homeResponse.text();
      
      // 验证关键HTML元素存在
      expect(homeHtml).toContain('<!DOCTYPE html>');
      expect(homeHtml).toContain('<title>');
      expect(homeHtml).toContain('认知复杂度分析');
      expect(homeHtml).toContain('checkForResults');
      expect(homeHtml).toContain('/api/status');

      // 验证404页面HTML
      const reportResponse = await fetch(`${url}/report`);
      expect(reportResponse.status).toBe(404);
      const reportHtml = await reportResponse.text();
      expect(reportHtml).toContain('<html>');
      expect(reportHtml).toContain('报告尚未生成');
    });
  });

  describe("性能指标验证", () => {
    test("服务器启动性能", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      const { duration } = await PerformanceTestUtils.measureAsync(async () => {
        return await server.start();
      });
      
      // 目标：启动时间应该比Express快100ms以上
      console.log(`Server startup time: ${duration.toFixed(2)}ms`);
      expect(duration).toBeLessThan(1000); // 启动应该在1秒内完成
      
      // 性能基准：Hono应该比Express更快
      PerformanceTestUtils.expectPerformance(duration, 500, "Server startup");
    });

    test("内存使用优化验证", async () => {
      const initialMemory = process.memoryUsage();
      
      server = new UIServer(config, { port: 0, openBrowser: false });
      await server.start();
      
      // 存储一些测试数据
      for (let i = 0; i < 10; i++) {
        const mockResult = TestUtils.createMockAnalysisResult({
          results: Array.from({ length: 50 }, () => TestUtils.createMockFileResult())
        });
        await server.storeResult(mockResult);
        await TestUtils.wait(10); // 短暂等待
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      
      // 内存增长应该保持在合理范围内
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // 少于50MB
    });

    test("请求处理延迟", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 预热
      await fetch(`${url}/api/status`);

      // 测量多个请求的平均延迟
      const latencies: number[] = [];
      
      for (let i = 0; i < 20; i++) {
        const { duration } = await PerformanceTestUtils.measureAsync(async () => {
          const response = await fetch(`${url}/api/status`);
          return response.json();
        });
        latencies.push(duration);
      }

      const averageLatency = latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
      const maxLatency = Math.max(...latencies);
      
      console.log(`Average latency: ${averageLatency.toFixed(2)}ms, Max: ${maxLatency.toFixed(2)}ms`);
      
      // 目标：平均延迟应该小于5ms，最大延迟小于50ms
      expect(averageLatency).toBeLessThan(50);
      expect(maxLatency).toBeLessThan(200);
    });

    test("并发请求性能", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 存储测试数据
      const mockResult = TestUtils.createMockAnalysisResult();
      await server.storeResult(mockResult);

      // 测试并发请求处理能力
      const concurrentRequests = 50;
      const requests = Array.from({ length: concurrentRequests }, () => 
        fetch(`${url}/api/status`)
      );

      const { duration, result: responses } = await PerformanceTestUtils.measureAsync(async () => {
        return Promise.all(requests);
      });

      // 验证所有请求都成功
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      const throughput = concurrentRequests / (duration / 1000); // requests per second
      console.log(`Concurrent requests throughput: ${throughput.toFixed(2)} req/s`);
      
      // 目标：并发处理能力应该足够高
      expect(duration).toBeLessThan(5000); // 50个并发请求应该在5秒内完成
      expect(throughput).toBeGreaterThan(10); // 至少10 req/s
    });

    test("大数据量处理性能", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 创建大量数据的分析结果
      const largeResult = TestUtils.createMockAnalysisResult({
        summary: {
          totalComplexity: 5000,
          averageComplexity: 10,
          filesAnalyzed: 500,
          functionsAnalyzed: 1000,
          highComplexityFunctions: 100
        },
        results: Array.from({ length: 500 }, (_, i) => 
          TestUtils.createMockFileResult({
            filePath: `src/file${i}.ts`,
            functions: Array.from({ length: 10 }, (_, j) =>
              TestUtils.createMockFunctionResult({ 
                name: `function${j}`,
                complexity: Math.floor(Math.random() * 20)
              })
            )
          })
        )
      });

      // 测量存储大数据的性能
      const { duration: storeDuration } = await PerformanceTestUtils.measureAsync(async () => {
        await server.storeResult(largeResult);
      });

      console.log(`Large data storage time: ${storeDuration.toFixed(2)}ms`);
      expect(storeDuration).toBeLessThan(1000); // 存储应该在1秒内完成

      // 测量读取大数据的性能
      const { duration: readDuration } = await PerformanceTestUtils.measureAsync(async () => {
        const response = await fetch(`${url}/api/result`);
        return response.json();
      });

      console.log(`Large data retrieval time: ${readDuration.toFixed(2)}ms`);
      expect(readDuration).toBeLessThan(500); // 读取应该在500ms内完成

      // 测量HTML报告生成性能
      const { duration: htmlDuration } = await PerformanceTestUtils.measureAsync(async () => {
        const response = await fetch(`${url}/report`);
        return response.text();
      });

      console.log(`Large HTML report generation time: ${htmlDuration.toFixed(2)}ms`);
      expect(htmlDuration).toBeLessThan(2000); // HTML生成应该在2秒内完成
    });
  });

  describe("Node.js 兼容性验证", () => {
    test("应该只使用Node.js标准API", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      // 这个测试通过成功运行来验证Node.js兼容性
      const result = await server.start();
      expect(result).toBeDefined();
      expect(typeof result.url).toBe('string');
      expect(typeof result.port).toBe('number');
    });

    test("文件系统操作兼容性", async () => {
      server = new UIServer(config, { openBrowser: false });
      
      const mockResult = TestUtils.createMockAnalysisResult();
      
      // 文件操作应该使用Node.js fs API
      await expect(server.storeResult(mockResult)).resolves.toBeUndefined();
      await expect(server.cleanupResult()).resolves.toBeUndefined();
    });

    test("网络操作兼容性", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      
      // 网络操作应该使用Node.js兼容的API
      const { url } = await server.start();
      
      // 标准fetch API应该工作
      const response = await fetch(`${url}/health`);
      expect(response.status).toBe(200);
    });
  });

  describe("部署环境兼容性", () => {
    test("跨平台路径处理", () => {
      server = new UIServer(config, { openBrowser: false });
      
      // 路径处理应该跨平台兼容
      // 这通过UIServer能够正确创建来验证
      expect(server).toBeDefined();
    });

    test("环境变量和配置兼容性", () => {
      // 测试不同的配置组合
      const testConfigs = [
        { ...config, ui: { port: 0, host: 'localhost' } },
        { ...config, ui: { port: 0, host: '127.0.0.1' } },
        { ...config, ui: { port: 0, host: '0.0.0.0' } }
      ];

      testConfigs.forEach(testConfig => {
        const testServer = new UIServer(testConfig);
        expect(testServer).toBeDefined();
      });
    });

    test("生产环境模拟", async () => {
      // 模拟生产环境设置
      const prodConfig = {
        ...config,
        ui: {
          port: 0,
          host: '0.0.0.0',
          openBrowser: false
        }
      } as CognitiveConfig;

      server = new UIServer(prodConfig, { port: 0, host: '0.0.0.0', openBrowser: false });
      const result = await server.start();
      
      // 验证服务器在生产模式下正常工作
      const healthResponse = await fetch(`http://localhost:${result.port}/health`);
      expect(healthResponse.status).toBe(200);
      
      // 验证端口配置正确
      expect(result.port).toBeGreaterThan(0);
    });
  });

  describe("长时间运行稳定性", () => {
    test("服务器长时间运行不应该出现内存泄漏", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      const initialMemory = process.memoryUsage().heapUsed;
      
      // 模拟长时间运行的负载
      for (let i = 0; i < 100; i++) {
        // 存储和清理结果
        const mockResult = TestUtils.createMockAnalysisResult();
        await server.storeResult(mockResult);
        
        // 执行一些请求
        await fetch(`${url}/api/status`);
        await fetch(`${url}/health`);
        
        await server.cleanupResult();
        
        if (i % 20 === 0) {
          // 强制垃圾回收（如果可用）
          if (global.gc) {
            global.gc();
          }
        }
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryGrowth = (finalMemory - initialMemory) / 1024 / 1024;
      
      console.log(`Memory growth after 100 cycles: ${memoryGrowth.toFixed(2)}MB`);
      
      // 内存增长应该保持在合理范围内
      expect(memoryGrowth).toBeLessThan(20); // 少于20MB增长
    });

    test("多次启动停止循环稳定性", async () => {
      // 测试多次启动停止的稳定性
      for (let i = 0; i < 5; i++) {
        server = new UIServer(config, { port: 0, openBrowser: false });
        
        const result = await server.start();
        expect(result).toBeDefined();
        
        // 执行一些操作
        const response = await fetch(`${result.url}/health`);
        expect(response.status).toBe(200);
        
        await server.stop();
        
        // 确保端口被释放
        await TestUtils.wait(100);
      }
    });
  });
});