import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { TestUtils, PerformanceTestUtils } from '../helpers/test-utils';
import { CLITestingUtils } from '../helpers/cli-testing-utils';
import type { PerformanceTestResult, ConcurrencyTestConfig } from '../helpers/test-utils';

describe('Performance Testing Integration', () => {
  beforeEach(() => {
    PerformanceTestUtils.resetBenchmarks();
  });

  afterEach(async () => {
    await CLITestingUtils.cleanupAll();
    PerformanceTestUtils.cleanup();
  });

  describe('基本性能测试', () => {
    it('应该能够测量CLI命令的执行性能', async () => {
      const { result, metrics } = await PerformanceTestUtils.measureCLIPerformance(
        () => CLITestingUtils.renderCLI('echo', ['Hello Performance Test']),
        'Basic Echo Test'
      );

      expect(metrics.executionTime).toBeGreaterThan(0);
      expect(metrics.memoryUsage.used).toBeDefined();
      expect(metrics.processInfo.uptime).toBeGreaterThan(0);
      expect(metrics.testDetails.scenario).toBe('Basic Echo Test');

      await CLITestingUtils.cleanup(result);
    });

    it('应该能够运行CLI性能基准测试', async () => {
      const benchmarkResult = await PerformanceTestUtils.runCLIBenchmark(
        () => CLITestingUtils.renderCLI('echo', ['Benchmark Test']),
        'cli-basic',
        'Echo Benchmark'
      );

      expect(benchmarkResult.benchmark).toBe('CLI Basic Command');
      expect(benchmarkResult.passed).toBe(true);
      expect(benchmarkResult.metrics.executionTime).toBeLessThan(5000); // 应该小于5秒
      expect(benchmarkResult.violations).toHaveLength(0);
    });

    it('应该能够检测性能违规', async () => {
      // 添加一个很严格的基准来触发违规
      PerformanceTestUtils.addBenchmark('strict-test', {
        name: 'Strict Performance Test',
        maxExecutionTime: 1,        // 1毫秒，很难达到
        maxMemoryUsage: 1024,       // 1KB，很小的内存限制
        maxCpuUsage: 1,             // 1%，很低的CPU使用
        description: '用于测试性能违规检测的严格基准'
      });

      const benchmarkResult = await PerformanceTestUtils.runCLIBenchmark(
        () => CLITestingUtils.renderCLI('echo', ['Strict Test']),
        'strict-test'
      );

      expect(benchmarkResult.passed).toBe(false);
      expect(benchmarkResult.violations.length).toBeGreaterThan(0);
      expect(benchmarkResult.suggestions.length).toBeGreaterThan(0);
    });
  });

  describe('并发性能测试', () => {
    it('应该能够运行并发性能测试', async () => {
      const config: ConcurrencyTestConfig = {
        concurrentCount: 3,
        testDuration: 5000,
        memoryLimit: 100 * 1024 * 1024, // 100MB
        cpuLimit: 80 // 80%
      };

      const concurrencyResult = await PerformanceTestUtils.runConcurrencyTest(
        () => CLITestingUtils.renderCLI('echo', ['Concurrent Test']),
        config,
        'Echo Concurrency Test'
      );

      expect(concurrencyResult.results).toHaveLength(3);
      expect(concurrencyResult.summary.concurrentProcesses).toBe(3);
      expect(concurrencyResult.summary.successRate).toBeGreaterThan(0);
      expect(concurrencyResult.summary.totalExecutionTime).toBeGreaterThan(0);
      expect(concurrencyResult.summary.averageExecutionTime).toBeGreaterThan(0);
    });

    it('应该能够处理并发测试中的失败情况', async () => {
      const config: ConcurrencyTestConfig = {
        concurrentCount: 2
      };

      // 使用一个不存在的命令来测试失败处理
      const concurrencyResult = await PerformanceTestUtils.runConcurrencyTest(
        () => CLITestingUtils.renderCLI('nonexistent-command', ['test']),
        config,
        'Failure Test'
      );

      // 检查是否正确处理了失败
      const failedResults = concurrencyResult.results.filter(r => !r.passed);
      expect(failedResults.length).toBeGreaterThan(0);
      
      failedResults.forEach(result => {
        expect(result.violations.length).toBeGreaterThan(0);
        expect(result.violations[0]).toContain('Test failed');
      });
    });
  });

  describe('性能报告生成', () => {
    it('应该能够生成详细的性能测试报告', async () => {
      const testResults: PerformanceTestResult[] = [];

      // 运行几个不同的性能测试
      const test1 = await PerformanceTestUtils.runCLIBenchmark(
        () => CLITestingUtils.renderCLI('echo', ['Test 1']),
        'cli-basic',
        'Report Test 1'
      );
      testResults.push(test1);

      const test2 = await PerformanceTestUtils.runCLIBenchmark(
        () => CLITestingUtils.renderCLI('echo', ['Test 2']),
        'cli-basic',
        'Report Test 2'
      );
      testResults.push(test2);

      const report = PerformanceTestUtils.generatePerformanceReport(
        testResults,
        'Integration Test Performance Report'
      );

      expect(report).toContain('Integration Test Performance Report');
      expect(report).toContain('Overall:');
      expect(report).toContain('tests passed');
      expect(report).toContain('Execution Time:');
      expect(report).toContain('Memory Usage:');
      expect(report).toContain('CLI Basic Command'); // 使用实际的基准名称

      console.log('\n' + report); // 输出报告以便查看
    });
  });

  describe('系统资源监控', () => {
    it('应该能够监控系统资源使用情况', async () => {
      const monitoring = PerformanceTestUtils.monitorSystemResources(2000, 500);
      
      // 在监控期间运行一些操作
      await TestUtils.wait(500);
      const testResult = await CLITestingUtils.renderCLI('echo', ['Resource Monitor Test']);
      await TestUtils.wait(500);
      await CLITestingUtils.cleanup(testResult);
      await TestUtils.wait(500);

      const monitoringResult = await monitoring;

      expect(monitoringResult.samples.length).toBeGreaterThan(0);
      expect(monitoringResult.peak.memory).toBeGreaterThan(0);
      expect(monitoringResult.peak.timestamp).toBeGreaterThan(0);
      expect(monitoringResult.average.heapUsed).toBeGreaterThan(0);
      expect(monitoringResult.average.heapTotal).toBeGreaterThan(0);

      // 验证监控数据的合理性
      monitoringResult.samples.forEach(sample => {
        expect(sample.timestamp).toBeGreaterThan(0);
        expect(sample.memory.heapUsed).toBeGreaterThan(0);
        expect(sample.uptime).toBeGreaterThan(0);
      });
    });

    it('应该能够获取当前性能统计信息', () => {
      const stats = PerformanceTestUtils.getCurrentPerformanceStats();

      expect(stats.memory).toBeDefined();
      expect(stats.memory.heapUsed).toBeGreaterThan(0);
      expect(stats.uptime).toBeGreaterThan(0);
      expect(stats.activeTimers).toBeGreaterThanOrEqual(0);

      // loadAverage 在 Windows 上不可用
      if (process.platform !== 'win32') {
        expect(stats.loadAverage).toBeDefined();
        expect(Array.isArray(stats.loadAverage)).toBe(true);
      }
    });
  });

  describe('性能基准管理', () => {
    it('应该能够添加和获取自定义性能基准', () => {
      const customBenchmark = {
        name: 'Custom Test Benchmark',
        maxExecutionTime: 1000,
        maxMemoryUsage: 50 * 1024 * 1024, // 50MB
        maxCpuUsage: 60,
        description: '自定义测试基准'
      };

      PerformanceTestUtils.addBenchmark('custom-test', customBenchmark);

      const retrieved = PerformanceTestUtils.getBenchmark('custom-test');
      expect(retrieved).toBeDefined();
      expect(retrieved!.name).toBe(customBenchmark.name);
      expect(retrieved!.maxExecutionTime).toBe(customBenchmark.maxExecutionTime);
      expect(retrieved!.description).toBe(customBenchmark.description);
    });

    it('应该能够重置基准到默认值', () => {
      // 添加自定义基准
      PerformanceTestUtils.addBenchmark('temp-test', {
        name: 'Temporary Benchmark',
        maxExecutionTime: 2000,
        maxMemoryUsage: 1024 * 1024,
        maxCpuUsage: 50
      });

      expect(PerformanceTestUtils.getBenchmark('temp-test')).toBeDefined();

      // 重置基准
      PerformanceTestUtils.resetBenchmarks();

      // 临时基准应该被删除
      expect(PerformanceTestUtils.getBenchmark('temp-test')).toBeUndefined();

      // 默认基准应该仍然存在
      expect(PerformanceTestUtils.getBenchmark('cli-basic')).toBeDefined();
      expect(PerformanceTestUtils.getBenchmark('cli-complex')).toBeDefined();
      expect(PerformanceTestUtils.getBenchmark('concurrent-basic')).toBeDefined();
    });
  });

  describe('性能测试工具与现有TestUtils集成', () => {
    it('应该能够与TestUtils的measureAsync方法协同工作', async () => {
      const { result: cliResult, duration: cliDuration } = await PerformanceTestUtils.measureAsync(
        () => CLITestingUtils.renderCLI('echo', ['Integration Test'])
      );

      expect(cliDuration).toBeGreaterThan(0);
      expect(cliResult.stdout).toContain('Integration Test');

      // 验证CLI结果的基本功能
      const found = await cliResult.findByText('Integration Test');
      expect(found).toBe(true);

      await CLITestingUtils.cleanup(cliResult);
    });

    it('应该能够在临时目录中运行性能测试', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const config = CLITestingUtils.getDefaultConfig();
        config.cwd = tempDir;

        const { result, metrics } = await PerformanceTestUtils.measureCLIPerformance(
          () => CLITestingUtils.renderCLI('pwd', [], config),
          'PWD in Temp Dir'
        );

        expect(metrics.testDetails.scenario).toBe('PWD in Temp Dir');
        // macOS 系统中 /var 实际上是 /private/var 的符号链接，所以两者都是正确的
        expect(result.stdout.trim()).toMatch(new RegExp(tempDir.replace('/private', '')));

        await CLITestingUtils.cleanup(result);
      });
    });
  });

  describe('错误处理和边界条件', () => {
    it('应该能够处理不存在的性能基准', async () => {
      await expect(
        PerformanceTestUtils.runCLIBenchmark(
          () => CLITestingUtils.renderCLI('echo', ['test']),
          'nonexistent-benchmark'
        )
      ).rejects.toThrow('Performance benchmark \'nonexistent-benchmark\' not found');
    });

    it('应该能够处理计时器错误', () => {
      expect(() => {
        PerformanceTestUtils.endTimer('nonexistent-timer');
      }).toThrow('Timer nonexistent-timer was not started');
    });

    it('应该能够处理零并发数的情况', async () => {
      const config: ConcurrencyTestConfig = {
        concurrentCount: 0
      };

      const result = await PerformanceTestUtils.runConcurrencyTest(
        () => CLITestingUtils.renderCLI('echo', ['test']),
        config
      );

      expect(result.results).toHaveLength(0);
      expect(result.summary.concurrentProcesses).toBe(0);
      expect(result.summary.successRate).toBe(0);
    });
  });
});