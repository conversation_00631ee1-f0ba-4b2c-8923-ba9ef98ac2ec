import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { CLITestingUtils, CLITestResult } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';
import { ComplexityCalculator } from '../../core/calculator';
import * as fs from 'fs/promises';
import * as path from 'path';

/**
 * 健壮性综合测试 - Task 10.1
 * 
 * 这个测试套件验证系统在各种异常和边界条件下的健壮性，包括：
 * - 完整性功能测试
 * - CLI参数解析确保测试
 * - 代码生成机制测试
 * - 错误恢复和降级机制测试
 */
describe('Robustness Comprehensive Tests - Task 10.1', () => {
  let instances: CLITestResult[] = [];

  beforeEach(async () => {
    instances = [];
    await CLITestingUtils.cleanupAll();
  });

  afterEach(async () => {
    for (const instance of instances) {
      try {
        await CLITestingUtils.cleanup(instance);
      } catch (error) {
        console.warn('Cleanup warning:', error);
      }
    }
    instances = [];
    await CLITestingUtils.cleanupAll();
  });

  /**
   * 完整性功能测试
   * 验证核心功能在异常情况下的健壮性
   */
  describe('Complete Functional Robustness', () => {
    test('应该处理损坏的语法文件并提供合理错误信息', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const brokenFile = path.join(tempDir, 'broken-syntax.ts');
        await fs.writeFile(brokenFile, `
          export function brokenFunction(
            // 故意缺少参数和括号
            if (condition) {
              return "broken";
            // 缺少函数结束括号
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          brokenFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        // 应该有非零退出码，表示发现了错误
        expect(exitCode).not.toBe(0);
        // 错误信息应该包含有用的调试信息
        expect(instance.stderr).toContain('Error');
      });
    });

    test('应该处理非TypeScript/JavaScript文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const textFile = path.join(tempDir, 'readme.txt');
        await fs.writeFile(textFile, 'This is just a text file, not code.');

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          textFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        // 应该优雅地跳过非代码文件
        expect(exitCode).toBe(0);
      });
    });

    test('应该处理文件权限问题', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const restrictedFile = path.join(tempDir, 'restricted.ts');
        await fs.writeFile(restrictedFile, 'export function test() { return true; }');
        
        // 尝试改变文件权限（在某些系统上可能不起作用）
        try {
          await fs.chmod(restrictedFile, 0o000);
          
          const instance = await CLITestingUtils.renderCLI('bun', [
            'run', 'src/index.ts',
            restrictedFile
          ], { 
            timeout: 8000,
            cwd: process.cwd()
          });
          instances.push(instance);

          const exitCode = await instance.waitForExit();
          // 应该处理权限错误
          expect(exitCode).not.toBe(0);
          
          // 恢复权限以便清理
          await fs.chmod(restrictedFile, 0o644);
        } catch (permissionError) {
          // 如果系统不支持权限变更，跳过此测试
          console.warn('Permission test skipped:', permissionError);
        }
      });
    });

    test('应该处理超大文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const largeFile = path.join(tempDir, 'large-file.ts');
        
        // 创建一个大文件
        let content = '// Large file test\n';
        for (let i = 0; i < 1000; i++) {
          content += `
            export function largeFunction${i}(param: any): any {
              if (param && param.condition${i}) {
                if (param.subCondition${i}) {
                  return param.value${i} || 'default${i}';
                } else if (param.alternativeCondition${i}) {
                  return param.alternativeValue${i} || 'alt${i}';
                } else {
                  return null;
                }
              } else {
                return undefined;
              }
            }
          `;
        }
        
        await fs.writeFile(largeFile, content);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--output', 'json',
          largeFile
        ], { 
          timeout: 30000,
          maxBuffer: 20 * 1024 * 1024, // 20MB buffer
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        
        const output = JSON.parse(instance.stdout);
        expect(output.length).toBe(1000); // 应该成功分析所有1000个函数
      });
    });

    test('应该处理内存压力情况', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建多个中等大小的文件同时处理
        const files = [];
        for (let fileIndex = 0; fileIndex < 10; fileIndex++) {
          const fileName = path.join(tempDir, `file${fileIndex}.ts`);
          let content = `// File ${fileIndex}\n`;
          
          for (let funcIndex = 0; funcIndex < 50; funcIndex++) {
            content += `
              export function func${fileIndex}_${funcIndex}(input: any): any {
                let result = null;
                
                if (input.type === 'A') {
                  if (input.subType === '1') {
                    result = input.value * 2;
                  } else if (input.subType === '2') {
                    result = input.value + 10;
                  } else {
                    result = input.value;
                  }
                } else if (input.type === 'B') {
                  switch (input.operation) {
                    case 'add':
                      result = input.a + input.b;
                      break;
                    case 'multiply':
                      result = input.a * input.b;
                      break;
                    default:
                      result = 0;
                  }
                } else {
                  for (let i = 0; i < input.count; i++) {
                    if (i % 2 === 0) {
                      result = i;
                    }
                  }
                }
                
                return result;
              }
            `;
          }
          
          await fs.writeFile(fileName, content);
          files.push(fileName);
        }

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--output', 'json',
          ...files
        ], { 
          timeout: 45000,
          maxBuffer: 50 * 1024 * 1024, // 50MB buffer
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        
        const output = JSON.parse(instance.stdout);
        expect(output.length).toBe(500); // 10文件 × 50函数 = 500个函数
      });
    });
  });

  /**
   * CLI参数解析确保测试
   * 验证CLI参数在各种异常情况下的健壮性
   */
  describe('CLI Parameter Parsing Robustness', () => {
    test('应该处理无效的CLI参数组合', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'test.ts');
        await fs.writeFile(testFile, 'export function test() { return true; }');

        // 测试 --show-context 没有 --details 的情况
        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--show-context',
          testFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).not.toBe(0);
        expect(instance.stderr).toContain('--show-context');
        expect(instance.stderr).toContain('--details');
      });
    });

    test('应该处理无效的数值参数', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'test.ts');
        await fs.writeFile(testFile, 'export function test() { return true; }');

        // 测试无效的threshold值
        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--threshold', 'not-a-number',
          testFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).not.toBe(0);
      });
    });

    test('应该处理不存在的文件路径', async () => {
      const nonExistentFile = '/path/that/does/not/exist.ts';

      const instance = await CLITestingUtils.renderCLI('bun', [
        'run', 'src/index.ts',
        nonExistentFile
      ], { 
        timeout: 8000,
        cwd: process.cwd()
      });
      instances.push(instance);

      const exitCode = await instance.waitForExit();
      expect(exitCode).not.toBe(0);
      expect(instance.stderr.toLowerCase()).toMatch(/no such file|not found|cannot access/);
    });

    test('应该处理空的文件路径参数', async () => {
      const instance = await CLITestingUtils.renderCLI('bun', [
        'run', 'src/index.ts',
        ''
      ], { 
        timeout: 8000,
        cwd: process.cwd()
      });
      instances.push(instance);

      const exitCode = await instance.waitForExit();
      expect(exitCode).not.toBe(0);
    });

    test('应该处理重复的CLI参数', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'test.ts');
        await fs.writeFile(testFile, 'export function test() { return true; }');

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--threshold', '10',
          '--threshold', '20',
          '--output', 'json',
          '--output', 'text',
          testFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        // 应该使用最后一个值或产生错误
        expect([0, 1]).toContain(exitCode);
      });
    });

    test('应该正确处理详细输出相关参数', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'complex.ts');
        await fs.writeFile(testFile, `
          export function complexFunction(input: any): any {
            if (input.condition1) {
              if (input.condition2) {
                return 'result1';
              } else if (input.condition3) {
                return 'result2';
              } else {
                return 'result3';
              }
            } else {
              return 'default';
            }
          }
        `);

        // 测试正确的参数组合
        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--details',
          '--show-context',
          testFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('complexFunction');
      });
    });
  });

  /**
   * 代码生成和框架机制测试
   * 验证代码框架生成的健壮性
   */
  describe('Code Generation Mechanism Robustness', () => {
    test('应该处理代码框架生成失败的情况', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建一个可能导致代码框架生成问题的文件
        const problematicFile = path.join(tempDir, 'problematic.ts');
        await fs.writeFile(problematicFile, `
          export function testFunction(): void {
            // 这个文件有复杂的Unicode字符和特殊格式
            const specialString = "测试中文字符 🎉 \u{1F600}";
            
            if (specialString.includes('测试')) {
              console.log('包含中文');
            } else if (specialString.includes('🎉')) {
              console.log('包含emoji');
            } else {
              console.log('其他情况');
            }
          }
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--details',
          '--show-context',
          problematicFile
        ], { 
          timeout: 10000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        // 即使代码框架生成有问题，也应该有降级处理
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('testFunction');
      });
    });

    test('应该处理极长行的代码框架生成', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const longLineFile = path.join(tempDir, 'long-line.ts');
        
        // 创建包含极长行的文件
        const longLine = `export function veryLongFunction(${Array.from({ length: 50 }, (_, i) => `param${i}: string`).join(', ')}): string { return ${Array.from({ length: 30 }, (_, i) => `param${i} && `).join('')}'result'; }`;
        await fs.writeFile(longLineFile, longLine);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--details',
          '--show-context',
          longLineFile
        ], { 
          timeout: 10000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('veryLongFunction');
      });
    });

    test('应该处理二进制或非文本文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const binaryFile = path.join(tempDir, 'binary.bin');
        
        // 创建一个包含二进制数据的文件
        const binaryData = Buffer.from([0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE, 0xFD]);
        await fs.writeFile(binaryFile, binaryData);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          binaryFile
        ], { 
          timeout: 8000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        // 应该优雅地处理二进制文件
        expect(exitCode).toBe(0);
      });
    });

    test('应该处理文件编码问题', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const encodedFile = path.join(tempDir, 'encoded.ts');
        
        // 创建包含各种编码字符的文件
        const content = `
          export function encodingTest(): string {
            // Latin characters: àáâãäåæçèéêë
            // Cyrillic: абвгдеёжзийклмн
            // Chinese: 你好世界
            // Japanese: こんにちは
            // Emoji: 🚀🎉🌟
            const mixed = "àáâã你好🚀";
            
            if (mixed.length > 5) {
              return mixed.substring(0, 5);
            } else {
              return mixed;
            }
          }
        `;
        
        await fs.writeFile(encodedFile, content, 'utf8');

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--details',
          '--show-context',
          encodedFile
        ], { 
          timeout: 10000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('encodingTest');
      });
    });
  });

  /**
   * 错误恢复和降级机制测试
   * 验证系统的错误恢复能力
   */
  describe('Error Recovery and Degradation Mechanisms', () => {
    test('应该在部分文件失败时继续处理其他文件', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建一个正常文件
        const goodFile = path.join(tempDir, 'good.ts');
        await fs.writeFile(goodFile, `
          export function goodFunction(): boolean {
            return true;
          }
        `);

        // 创建一个有问题的文件
        const badFile = path.join(tempDir, 'bad.ts');
        await fs.writeFile(badFile, `
          export function badFunction(
            // 故意语法错误
            if (condition {
              return "bad"
        `);

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          '--output', 'json',
          goodFile,
          badFile
        ], { 
          timeout: 10000,
          cwd: process.cwd()
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        // 应该至少处理好的文件
        expect(exitCode).not.toBe(0); // 因为有错误文件
        expect(instance.stderr).toContain('Error');
      });
    });

    test('应该在内存不足时使用流式处理', async () => {
      const calculator = new ComplexityCalculator({
        enableDetails: true,
        recursionChainThreshold: 10
      });

      // 创建一个模拟的大型代码字符串
      const largeCode = Array.from({ length: 100 }, (_, i) => `
        export function func${i}(input: any): any {
          if (input.condition${i}) {
            if (input.subCondition${i}) {
              return input.value${i};
            } else {
              return null;
            }
          } else {
            return undefined;
          }
        }
      `).join('\n');

      // 测试计算器能否处理大量代码
      const result = await calculator.calculateFromString(largeCode, 'test.ts');
      
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(100);
    });

    test('应该在配置加载失败时使用默认配置', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFile = path.join(tempDir, 'test.ts');
        await fs.writeFile(testFile, 'export function test() { return true; }');

        // 创建一个无效的配置文件
        const configFile = path.join(tempDir, '.complexityrc.json');
        await fs.writeFile(configFile, '{ invalid json }');

        const instance = await CLITestingUtils.renderCLI('bun', [
          'run', 'src/index.ts',
          testFile
        ], { 
          timeout: 8000,
          cwd: tempDir // 在包含无效配置的目录中运行
        });
        instances.push(instance);

        const exitCode = await instance.waitForExit();
        // 应该使用默认配置继续运行
        expect(exitCode).toBe(0);
        expect(instance.stdout).toContain('test');
      });
    });
  });

  /**
   * 系统资源和性能健壮性测试
   */
  describe('System Resource and Performance Robustness', () => {
    test('应该在并发访问时保持数据一致性', async () => {
      const calculator = new ComplexityCalculator({
        enableDetails: true
      });

      const testCode = `
        export function concurrencyTest(input: any): any {
          if (input.condition1) {
            if (input.condition2) {
              return 'result';
            } else {
              return 'other';
            }
          } else {
            return null;
          }
        }
      `;

      // 并发执行多个计算
      const promises = Array.from({ length: 10 }, () =>
        calculator.calculateFromString(testCode, 'concurrent-test.ts')
      );

      const results = await Promise.all(promises);
      
      // 所有结果应该相同
      expect(results.length).toBe(10);
      results.forEach((result, index) => {
        expect(result).toBeDefined();
        expect(result.length).toBe(1);
        expect(result[0].name).toBe('concurrencyTest');
        expect(result[0].complexity).toBe(results[0][0].complexity);
      });
    });

    test('应该处理快速连续的请求', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const testFiles = [];
        
        // 创建多个小文件
        for (let i = 0; i < 5; i++) {
          const fileName = path.join(tempDir, `rapid${i}.ts`);
          await fs.writeFile(fileName, `
            export function rapid${i}(): number {
              return ${i};
            }
          `);
          testFiles.push(fileName);
        }

        // 快速连续启动多个CLI实例
        const promises = testFiles.map(file =>
          CLITestingUtils.renderCLI('bun', [
            'run', 'src/index.ts',
            '--output', 'json',
            file
          ], { 
            timeout: 8000,
            cwd: process.cwd()
          })
        );

        const instanceResults = await Promise.all(promises);
        instances.push(...instanceResults);

        // 等待所有实例完成
        const exitCodes = await Promise.all(
          instanceResults.map(instance => instance.waitForExit())
        );

        // 所有实例都应该成功完成
        exitCodes.forEach(exitCode => {
          expect(exitCode).toBe(0);
        });
      });
    });
  });
});