import { test, expect, describe } from 'vitest';
import { spawn } from 'child_process';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { TestUtils } from '../helpers/test-utils';

describe('复杂代码文件 - 端到端混合逻辑分析', () => {
  const runCLI = async (
    args: string[],
    timeoutMs = 20000
  ): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
    return new Promise((resolve, reject) => {
      const proc = spawn('node', ['dist/cli/index.js', ...args], {
        cwd: process.cwd(),
        stdio: ['pipe', 'pipe', 'pipe'],
      });

      let stdout = '';
      let stderr = '';
      let resolved = false;

      const timeout = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          proc.kill('SIGKILL');
          reject(new Error(`CLI process timed out after ${timeoutMs}ms`));
        }
      }, timeoutMs);

      proc.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      proc.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      proc.on('close', (code) => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          resolve({ stdout, stderr, exitCode: code || 0 });
        }
      });

      proc.on('error', (err) => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeout);
          reject(err);
        }
      });
    });
  };

  // 创建复杂业务逻辑测试文件的工具函数
  const createComplexTestFiles = (testDir: string) => {
    const complexBusinessLogic = {
      // 用户认证与权限管理系统
      'src/auth/AuthenticationService.ts': `
interface User {
  id: string;
  role: 'admin' | 'user' | 'moderator';
  permissions: string[];
  isActive: boolean;
  lastLogin?: Date;
}

interface AuthRequest {
  token: string;
  refreshToken?: string;
  permissions?: string[];
}

export class AuthenticationService {
  // 复杂的用户认证逻辑 - 期望高复杂度
  public async authenticateUser(request: AuthRequest): Promise<User | null> {
    try { // +1
      const decoded = this.decodeToken(request.token);

      if (!decoded || !decoded.userId) { // +2 (嵌套) + 1
        return null;
      }

      const user = await this.getUserById(decoded.userId);

      if (!user) { // +2 (嵌套)
        throw new Error('User not found');
      }

      // 复杂的权限检查逻辑
      if (user.role === 'admin' || user.permissions.includes('full_access') && user.isActive) { // +2 (嵌套) + 1 + 1 + 1(混用惩罚) = +5
        return this.createAuthenticatedUser(user);
      } else if (user.role === 'moderator' && user.isActive || this.hasSpecialPermissions(user)) { // +2 (嵌套) + 1 + 1 + 1(混用惩罚) = +5
        return this.createLimitedUser(user);
      } else if (user.isActive && this.isWithinLoginWindow(user) || request.refreshToken) { // +2 (嵌套) + 1 + 1 + 1(混用惩罚) = +5
        return this.createBasicUser(user);
      }

      return null;
    } catch (error) { // +1
      console.error('Authentication failed:', error);
      return null;
    }
    // 总期望复杂度: 1 + 3 + 2 + 5 + 5 + 5 + 1 = 22
  }

  public hasPermission(user: User, permission: string): boolean {
    // 期望复杂度: 4 (if + || + && + 混用惩罚)
    if (user.isActive && user.permissions.includes(permission) || user.role === 'admin') {
      return true;
    }
    return false;
  }
}
      `,

      // 数据处理服务
      'src/data/DataProcessor.ts': `
export class DataProcessor {
  public processData(data: any[], filters: any, options: any): any[] {
    try { // +1
      const results = [];

      for (const item of data) { // +1
        // 复杂的过滤逻辑
        if (filters.type && item.type === filters.type || !filters.type && item.enabled) { // +2(嵌套) + 1 + 1 + 1 + 1(混用惩罚) = +6
          if (filters.category && item.category === filters.category || filters.includeAll) { // +3(嵌套) + 1 + 1 + 1(混用惩罚) = +6
            if (options.includeInactive || item.status === 'active' && item.verified) { // +4(嵌套) + 1 + 1 + 1(混用惩罚) = +7
              results.push(this.transformItem(item, options));
            }
          }
        }
      }

      return results;
    } catch (error) { // +1
      console.error('Data processing failed:', error);
      return [];
    }
    // 总期望复杂度: 1 + 1 + 6 + 6 + 7 + 1 = 22
  }

  private transformItem(item: any, options: any): any {
    const result = { ...item };

    // 期望复杂度: 4 (if + && + || + 混用惩罚)
    if (options.includeMetadata && item.metadata || options.forceInclude) {
      result.metadata = item.metadata || {};
    }

    return result;
  }
}
      `,

      // 业务规则引擎
      'src/rules/BusinessRuleEngine.ts': `
interface Rule {
  id: string;
  condition: string;
  action: string;
  priority: number;
  enabled: boolean;
}

interface Context {
  user: any;
  data: any;
  environment: string;
  timestamp: number;
}

export class BusinessRuleEngine {
  public evaluateRules(rules: Rule[], context: Context): any[] {
    const applicableRules = [];

    for (const rule of rules) { // +1
      try { // +1 (嵌套)
        // 复杂的规则评估逻辑
        if (rule.enabled && this.meetsCondition(rule, context) || rule.priority > 5 && context.environment === 'production') { // +2(嵌套) + 1 + 1 + 1 + 1(混用惩罚) = +6
          if (context.user && context.user.role === 'admin' || rule.priority > 8) { // +3(嵌套) + 1 + 1 + 1(混用惩罚) = +6
            applicableRules.push({
              ...rule,
              result: this.executeRule(rule, context)
            });
          } else if (context.user && context.user.permissions && this.hasRequiredPermission(context.user, rule) || rule.priority <= 3) { // +3(嵌套) + 1 + 1 + 1 + 1(混用惩罚) = +7
            if (context.environment === 'development' || rule.condition.includes('test') && context.data.test) { // +4(嵌套) + 1 + 1 + 1(混用惩罚) = +7
              applicableRules.push({
                ...rule,
                result: this.executeRule(rule, context)
              });
            }
          }
        }
      } catch (error) { // +2 (嵌套)
        console.error(\`Rule evaluation failed for rule \${rule.id}:\`, error);
      }
    }

    return applicableRules.sort((a, b) => b.priority - a.priority);
    // 总期望复杂度: 1 + 1 + 6 + 6 + 7 + 7 + 2 = 30
  }

  private meetsCondition(rule: Rule, context: Context): boolean {
    // 期望复杂度: 4 (if + || + && + 混用惩罚)
    if (rule.condition.includes('user') || context.user && context.user.id) {
      return true;
    }
    return false;
  }

  private hasRequiredPermission(user: any, rule: Rule): boolean {
    // 期望复杂度: 4 (if + && + || + 混用惩罚)
    if (user.permissions && user.permissions.includes(rule.condition) || user.role === 'admin') {
      return true;
    }
    return false;
  }

  private executeRule(rule: Rule, context: Context): any {
    // 期望复杂度: 6 (try + if + && + || + 混用惩罚 + catch)
    try { // +1
      if (rule.action === 'allow' && context.user || rule.action === 'deny') { // +1 + 1 + 1 + 1(混用惩罚) = +4
        return { action: rule.action, applied: true };
      }
      return { action: 'none', applied: false };
    } catch (error) { // +1
      return { action: 'error', applied: false };
    }
  }
}
      `,

      // 集成测试用的主入口文件
      'src/app/Application.ts': `
import { AuthenticationService } from '../auth/AuthenticationService';
import { DataProcessor } from '../data/DataProcessor';
import { BusinessRuleEngine } from '../rules/BusinessRuleEngine';

export class Application {
  private auth = new AuthenticationService();
  private processor = new DataProcessor();
  private ruleEngine = new BusinessRuleEngine();

  public async processRequest(request: any): Promise<any> {
    try { // +1
      // 用户认证
      const user = await this.auth.authenticateUser(request.auth);

      if (!user) { // +1 (嵌套)
        return { error: 'Authentication failed' };
      }

      // 权限检查
      if (!this.auth.hasPermission(user, 'read_data') && request.action !== 'public' || !user.isActive) { // +2(嵌套) + 1 + 1 + 1 + 1(混用惩罚) = +6
        return { error: 'Insufficient permissions' };
      }

      // 数据处理
      const processedData = this.processor.processData(
        request.data || [],
        request.filters || {},
        request.options || {}
      );

      // 业务规则应用
      if (request.applyRules && processedData.length > 0 || user.role === 'admin') { // +2(嵌套) + 1 + 1 + 1(混用惩罚) = +5
        const rules = this.getRulesForUser(user);
        const ruleResults = this.ruleEngine.evaluateRules(rules, {
          user,
          data: processedData,
          environment: process.env.NODE_ENV || 'development',
          timestamp: Date.now()
        });

        return {
          data: processedData,
          rules: ruleResults,
          user: { id: user.id, role: user.role }
        };
      }

      return { data: processedData };
    } catch (error) { // +1
      console.error('Application error:', error);
      return { error: 'Internal server error' };
    }
    // 总期望复杂度: 1 + 1 + 6 + 5 + 1 = 14
  }

  private getRulesForUser(user: any): any[] {
    // 期望复杂度: 6 (if + || + && + 混用惩罚 + else if + &&)
    if (user.role === 'admin' || user.permissions.includes('all_rules') && user.isActive) { // +1 + 1 + 1(混用惩罚) = +3
      return this.getAllRules();
    } else if (user.role === 'moderator' && user.isActive) { // +1 + 1 = +2
      return this.getModeratorRules();
    }
    return this.getBasicRules();
  }

  private getAllRules(): any[] { return []; }
  private getModeratorRules(): any[] { return []; }
  private getBasicRules(): any[] { return []; }
}
      `,
    };

    // 创建测试文件
    Object.entries(complexBusinessLogic).forEach(([path, content]) => {
      const fullPath = join(testDir, path);
      const dir = fullPath.substring(0, fullPath.lastIndexOf('/'));

      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      writeFileSync(fullPath, content, 'utf8');
    });
  };

  describe('真实业务场景分析', () => {
    test('应该正确分析认证服务的复杂度', async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createComplexTestFiles(testDir);

        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true,
          },
          failOnComplexity: 50,
        };

        const configFile = join(testDir, 'cognitive.config.json');
        writeFileSync(configFile, JSON.stringify(config, null, 2));

        const result = await runCLI([
          join(testDir, 'src/auth/AuthenticationService.ts'),
          '--config',
          configFile,
          '--format',
          'json',
        ]);

        expect(result.exitCode).toBe(0);

        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();

        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;

          // 验证认证服务复杂度
          const authenticateUser = functions.find((f: any) => f.name === 'authenticateUser');
          expect(authenticateUser).toBeTruthy();
          expect(authenticateUser.complexity).toBe(19); // 复杂的认证逻辑

          const hasPermission = functions.find((f: any) => f.name === 'hasPermission');
          expect(hasPermission).toBeTruthy();
          expect(hasPermission.complexity).toBe(4); // 权限检查逻辑
        }
      });
    });

    test('应该正确分析数据处理服务的复杂度', async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createComplexTestFiles(testDir);

        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true,
          },
          failOnComplexity: 50,
        };

        const configFile = join(testDir, 'cognitive.config.json');
        writeFileSync(configFile, JSON.stringify(config, null, 2));

        const result = await runCLI([
          join(testDir, 'src/data/DataProcessor.ts'),
          '--config',
          configFile,
          '--format',
          'json',
        ]);

        expect(result.exitCode).toBe(0);

        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();

        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;

          // 验证数据处理复杂度
          const processData = functions.find((f: any) => f.name === 'processData');
          expect(processData).toBeTruthy();
          expect(processData.complexity).toBe(21); // 复杂的数据处理逻辑

          const transformItem = functions.find((f: any) => f.name === 'transformItem');
          expect(transformItem).toBeTruthy();
          expect(transformItem.complexity).toBe(4); // 数据转换逻辑
        }
      });
    });

    test('应该正确分析业务规则引擎的复杂度', async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createComplexTestFiles(testDir);

        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true,
          },
          failOnComplexity: 50,
        };

        const configFile = join(testDir, 'cognitive.config.json');
        writeFileSync(configFile, JSON.stringify(config, null, 2));

        const result = await runCLI([
          join(testDir, 'src/rules/BusinessRuleEngine.ts'),
          '--config',
          configFile,
          '--format',
          'json',
        ]);

        expect(result.exitCode).toBe(0);

        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();

        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;

          // 验证业务规则引擎复杂度
          const evaluateRules = functions.find((f: any) => f.name === 'evaluateRules');
          expect(evaluateRules).toBeTruthy();
          expect(evaluateRules.complexity).toBe(31); // 极复杂的规则评估逻辑

          const meetsCondition = functions.find((f: any) => f.name === 'meetsCondition');
          expect(meetsCondition).toBeTruthy();
          expect(meetsCondition.complexity).toBe(4); // 条件检查逻辑

          const hasRequiredPermission = functions.find((f: any) => f.name === 'hasRequiredPermission');
          expect(hasRequiredPermission).toBeTruthy();
          expect(hasRequiredPermission.complexity).toBe(4); // 权限检查逻辑

          const executeRule = functions.find((f: any) => f.name === 'executeRule');
          expect(executeRule).toBeTruthy();
          expect(executeRule.complexity).toBe(5); // 规则执行逻辑
        }
      });
    });

    test('应该正确分析应用主入口的复杂度', async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createComplexTestFiles(testDir);

        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true,
          },
          failOnComplexity: 50,
        };

        const configFile = join(testDir, 'cognitive.config.json');
        writeFileSync(configFile, JSON.stringify(config, null, 2));

        const result = await runCLI([
          join(testDir, 'src/app/Application.ts'),
          '--config',
          configFile,
          '--format',
          'json',
        ]);

        expect(result.exitCode).toBe(0);

        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();

        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;

          // 验证应用主入口复杂度
          const processRequest = functions.find((f: any) => f.name === 'processRequest');
          expect(processRequest).toBeTruthy();
          expect(processRequest.complexity).toBe(10); // 主请求处理逻辑

          const getRulesForUser = functions.find((f: any) => f.name === 'getRulesForUser');
          expect(getRulesForUser).toBeTruthy();
          expect(getRulesForUser.complexity).toBe(7); // 用户规则获取逻辑
        }
      });
    });

    test('应该能够批量分析整个项目', async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createComplexTestFiles(testDir);

        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: true,
          },
          failOnComplexity: 50,
        };

        const configFile = join(testDir, 'cognitive.config.json');
        writeFileSync(configFile, JSON.stringify(config, null, 2));

        const result = await runCLI([join(testDir, 'src'), '--config', configFile, '--format', 'json']);

        expect(result.exitCode).toBe(0);

        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();

        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);

          // 验证整体分析结果
          expect(output.results).toHaveLength(4); // 4个文件
          expect(output.summary.filesAnalyzed).toBe(4);
          expect(output.summary.functionsAnalyzed).toBeGreaterThan(10);

          expect(output.summary.highComplexityFunctions).toBe(0);

          // 验证总复杂度合理
          expect(output.summary.totalComplexity).toBeGreaterThan(50);
        }
      });
    });

    test('功能禁用时应该显示不同的复杂度', async () => {
      await TestUtils.withTempDir(async (testDir) => {
        createComplexTestFiles(testDir);

        const config = {
          rules: {
            enableMixedLogicOperatorPenalty: false,
          },
          failOnComplexity: 50,
        };

        const configFile = join(testDir, 'cognitive.config.json');
        writeFileSync(configFile, JSON.stringify(config, null, 2));

        const result = await runCLI([
          join(testDir, 'src/auth/AuthenticationService.ts'),
          '--config',
          configFile,
          '--format',
          'json',
        ]);

        expect(result.exitCode).toBe(0);

        const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
        expect(jsonMatch).toBeTruthy();

        if (jsonMatch) {
          const output = JSON.parse(jsonMatch[0]);
          const functions = output.results[0].functions;

          // 验证禁用混用惩罚后的复杂度
          const authenticateUser = functions.find((f: any) => f.name === 'authenticateUser');
          expect(authenticateUser).toBeTruthy();
          expect(authenticateUser.complexity).toBeLessThan(22); // 应该小于启用时的复杂度

          const hasPermission = functions.find((f: any) => f.name === 'hasPermission');
          expect(hasPermission).toBeTruthy();
          expect(hasPermission.complexity).toBeLessThan(4); // 应该小于启用时的复杂度
        }
      });
    });
  });
});
