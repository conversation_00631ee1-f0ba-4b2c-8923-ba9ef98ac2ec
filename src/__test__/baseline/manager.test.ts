import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { BaselineManager } from "../../baseline/manager";
import type { AnalysisResult, FileResult, FunctionResult } from "../../core/types";
import type { BaselineData } from "../../baseline/types";
import { writeFileSync, unlinkSync, existsSync } from "fs";
import { join } from "path";

describe("BaselineManager", () => {
  const testBaselinePath = join(process.cwd(), "test-baseline.json");
  let manager: BaselineManager;
  
  beforeEach(() => {
    manager = new BaselineManager(testBaselinePath);
  });
  
  afterEach(() => {
    if (existsSync(testBaselinePath)) {
      unlinkSync(testBaselinePath);
    }
  });
  
  const createMockAnalysisResult = (): AnalysisResult => ({
    summary: {
      totalComplexity: 15,
      averageComplexity: 7.5,
      filesAnalyzed: 2,
      functionsAnalyzed: 2,
      highComplexityFunctions: 1
    },
    results: [
      {
        filePath: "src/test1.ts",
        complexity: 10,
        averageComplexity: 10,
        functions: [
          {
            name: "complexFunction",
            complexity: 10,
            line: 5,
            column: 0,
            filePath: "src/test1.ts"
          }
        ]
      },
      {
        filePath: "src/test2.ts", 
        complexity: 5,
        averageComplexity: 5,
        functions: [
          {
            name: "simpleFunction",
            complexity: 5,
            line: 3,
            column: 0,
            filePath: "src/test2.ts"
          }
        ]
      }
    ]
  });
  
  test("应该创建基线文件", async () => {
    const analysisResult = createMockAnalysisResult();
    
    await manager.createBaseline(analysisResult);
    
    expect(existsSync(testBaselinePath)).toBe(true);
    
    const baseline = await manager.loadBaseline();
    expect(baseline).toBeDefined();
    expect(baseline?.entries).toHaveLength(2);
    expect(baseline?.entries[0]?.functionName).toBe("complexFunction");
    expect(baseline?.entries[0]?.complexity).toBe(10);
    expect(baseline?.entries[1]?.functionName).toBe("simpleFunction");
    expect(baseline?.entries[1]?.complexity).toBe(5);
  });
  
  test("应该加载现有基线文件", async () => {
    const mockBaseline: BaselineData = {
      version: "1.0",
      createdAt: new Date().toISOString(),
      entries: [
        {
          filePath: "src/test.ts",
          functionName: "testFunction",
          line: 10,
          complexity: 8,
          lastUpdated: new Date().toISOString()
        }
      ]
    };
    
    writeFileSync(testBaselinePath, JSON.stringify(mockBaseline, null, 2));
    
    const baseline = await manager.loadBaseline();
    
    expect(baseline).toBeDefined();
    expect(baseline?.entries).toHaveLength(1);
    expect(baseline?.entries[0]?.functionName).toBe("testFunction");
    expect(baseline?.entries[0]?.complexity).toBe(8);
  });
  
  test("应该处理不存在的基线文件", async () => {
    const baseline = await manager.loadBaseline();
    expect(baseline).toBeNull();
  });
  
  test("应该更新基线文件", async () => {
    // 先创建基线
    const initialResult = createMockAnalysisResult();
    await manager.createBaseline(initialResult);
    
    // 修改分析结果
    const updatedResult = createMockAnalysisResult();
    updatedResult.results[0]!.functions[0]!.complexity = 12; // 复杂度增加
    updatedResult.results.push({
      filePath: "src/test3.ts",
      complexity: 3,
      averageComplexity: 3,
      functions: [
        {
          name: "newFunction",
          complexity: 3,
          line: 1,
          column: 0,
          filePath: "src/test3.ts"
        }
      ]
    });
    
    await manager.updateBaseline(updatedResult);
    
    const baseline = await manager.loadBaseline();
    expect(baseline?.entries).toHaveLength(3);
    
    // 找到更新的函数
    const updatedEntry = baseline?.entries.find(e => e.functionName === "complexFunction");
    expect(updatedEntry?.complexity).toBe(12);
    
    // 找到新增的函数
    const newEntry = baseline?.entries.find(e => e.functionName === "newFunction");
    expect(newEntry?.complexity).toBe(3);
  });
  
  test("应该比较当前结果与基线", async () => {
    // 创建基线
    const baselineResult = createMockAnalysisResult();
    await manager.createBaseline(baselineResult);
    
    // 创建当前结果
    const currentResult = createMockAnalysisResult();
    currentResult.results[0]!.functions[0]!.complexity = 12; // 复杂度恶化
    currentResult.results[1]!.functions[0]!.complexity = 3;  // 复杂度改善
    
    const baseline = await manager.loadBaseline();
    const comparison = manager.compareWithBaseline(currentResult, baseline!);
    
    expect(comparison).toBeDefined();
    expect(comparison.baseline).toEqual(baseline);
    
    // 验证比较结果包含基线信息
    expect(comparison.summary.functionsAnalyzed).toBeGreaterThan(0);
  });
  
  test("应该处理空基线的比较", async () => {
    const currentResult = createMockAnalysisResult();
    const emptyBaseline: BaselineData = {
      version: "1.0",
      createdAt: new Date().toISOString(),
      entries: []
    };
    
    const comparison = manager.compareWithBaseline(currentResult, emptyBaseline);
    
    expect(comparison.baseline).toEqual(emptyBaseline);
    expect(comparison.results).toHaveLength(2);
  });
  
  test("应该过滤超标函数创建基线", async () => {
    const analysisResult = createMockAnalysisResult();
    // 添加一个低复杂度函数
    analysisResult.results[0]!.functions.push({
      name: "lowComplexityFunction",
      complexity: 2,
      line: 15,
      column: 0,
      filePath: "src/test1.ts"
    });
    
    // 使用阈值过滤
    const manager = new BaselineManager(testBaselinePath);
    await manager.createBaseline(analysisResult, 5); // 只记录复杂度 >= 5 的函数
    
    const baseline = await manager.loadBaseline();
    expect(baseline?.entries).toHaveLength(2); // 应该只有2个函数(10和5)，不包括复杂度为2的函数
    
    const complexities = baseline?.entries.map(e => e.complexity) || [];
    expect(complexities).toContain(10);
    expect(complexities).toContain(5);
    expect(complexities).not.toContain(2);
  });
});