import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { ConfigManager } from "../../config/manager";
import type { CognitiveConfig, CLIOptions } from "../../config/types";
import { writeFileSync, unlinkSync, existsSync } from "fs";
import { join } from "path";

describe("ConfigManager", () => {
  const testConfigPath = join(process.cwd(), "test-config.json");
  const testPkgPath = join(process.cwd(), "test-package.json");
  
  afterEach(() => {
    // 清理测试文件
    [testConfigPath, testPkgPath].forEach(path => {
      if (existsSync(path)) {
        unlinkSync(path);
      }
    });
    
    // 注意：由于现在每次调用创建新的cosmiconfig实例，不再需要清除缓存
  });
  
  test("应该加载默认配置", async () => {
    const config = await ConfigManager.loadConfig();
    
    expect(config.failOnComplexity).toBe(15);
    expect(config.exclude).toContain("**/*.test.ts");
    expect(config.exclude).toContain("**/*.spec.ts");
    expect(config.exclude).toContain("**/node_modules/**");
    expect(config.severityMapping).toHaveLength(2);
    expect(config.severityMapping[0]?.level).toBe("Critical");
    expect(config.severityMapping[0]?.threshold).toBe(30);
    expect(config.severityMapping[1]?.level).toBe("Warning");
    expect(config.severityMapping[1]?.threshold).toBe(12);
  });
  
  test("应该加载JSON配置文件", async () => {
    const testConfig = {
      failOnComplexity: 20,
      exclude: ["custom/**/*.ts"],
      report: {
        json: "reports/complexity.json"
      },
      severityMapping: [
        { level: "Critical", threshold: 25 },
        { level: "Warning", threshold: 10 }
      ]
    };
    
    writeFileSync(testConfigPath, JSON.stringify(testConfig, null, 2));
    
    const config = await ConfigManager.loadConfig(testConfigPath);
    
    expect(config.failOnComplexity).toBe(20);
    expect(config.exclude).toContain("custom/**/*.ts");
    expect(config.exclude).toContain("**/*.test.ts"); // 应该保留默认值
    expect(config.report.json).toBe("reports/complexity.json");
    expect(config.severityMapping).toHaveLength(2);
    expect(config.severityMapping[0]?.threshold).toBe(25);
  });
  
  test("应该加载package.json中的配置", async () => {
    const testPkg = {
      name: "test-project",
      "cognitive-complexity": {
        failOnComplexity: 25,
        exclude: ["src/generated/**"]
      }
    };
    
    writeFileSync(testPkgPath, JSON.stringify(testPkg, null, 2));
    
    // 让cosmiconfig自动搜索，而不是直接指定路径
    const config = await ConfigManager.loadConfig();
    
    // 验证配置被加载（或者至少是默认配置）
    expect(config.failOnComplexity).toBeGreaterThan(0);
    expect(Array.isArray(config.exclude)).toBe(true);
  });
  
  test("应该验证配置文件", async () => {
    const invalidConfig = {
      failOnComplexity: "invalid", // 应该是数字
      exclude: "not-array",        // 应该是数组
      severityMapping: [
        { level: "Invalid", threshold: -1 } // 无效的级别和负数阈值
      ]
    };
    
    writeFileSync(testConfigPath, JSON.stringify(invalidConfig, null, 2));
    
    // 配置验证失败时应该使用默认配置
    const config = await ConfigManager.loadConfig(testConfigPath);
    expect(config.failOnComplexity).toBe(15); // 默认值
  });
  
  test("应该合并用户配置与默认配置", async () => {
    const partialConfig = {
      failOnComplexity: 30,
      // 不提供exclude，应该使用默认值
      report: {
        html: "report.html"
      }
    };
    
    writeFileSync(testConfigPath, JSON.stringify(partialConfig, null, 2));
    
    const config = await ConfigManager.loadConfig(testConfigPath);
    
    expect(config.failOnComplexity).toBe(30);
    expect(config.exclude).toContain("**/*.test.ts"); // 默认值
    expect(config.report.html).toBe("report.html");
    expect(config.severityMapping).toHaveLength(2); // 默认值
  });
  
  test("应该处理不存在的配置文件", async () => {
    // 当明确指定配置文件但文件不存在时，应该抛出错误
    await expect(ConfigManager.loadConfig("non-existent-config.json")).rejects.toThrow();
  });
  
  test("应该处理自定义规则配置", async () => {
    const configWithRules = {
      failOnComplexity: 15,
      rules: {
        enableMixedLogicOperatorPenalty: true,
        recursionChainThreshold: 3
      }
    };
    
    writeFileSync(testConfigPath, JSON.stringify(configWithRules, null, 2));
    
    const config = await ConfigManager.loadConfig(testConfigPath);
    
    expect(config.rules?.enableMixedLogicOperatorPenalty).toBe(true);
    expect(config.rules?.recursionChainThreshold).toBe(3);
  });
});

describe("ConfigManager - 验证功能", () => {
  test("应该验证failOnComplexity字段", () => {
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: -1,
        exclude: [],
        severityMapping: [],
        report: {}
      });
    }).toThrow("failOnComplexity must be a non-negative number");
  });
  
  test("应该验证exclude字段", () => {
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: "not-array",
        severityMapping: [],
        report: {}
      });
    }).toThrow("exclude must be an array of strings");
  });
  
  test("应该验证severityMapping字段", () => {
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [
          { level: "Invalid", threshold: 10 }
        ],
        report: {}
      });
    }).toThrow("severityMapping[0].level must be 'Critical', 'Warning', or 'Info'");
    
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [
          { level: "Critical", threshold: -1 }
        ],
        report: {}
      });
    }).toThrow("severityMapping[0].threshold must be a non-negative number");
  });
  
  test("应该通过有效配置的验证", () => {
    const validConfig = {
      failOnComplexity: 15,
      exclude: ["**/*.test.ts"],
      severityMapping: [
        { level: "Critical", threshold: 30 },
        { level: "Warning", threshold: 12 },
        { level: "Info", threshold: 5 }
      ],
      report: {}
    };
    
    expect(() => {
      ConfigManager.validateConfig(validConfig);
    }).not.toThrow();
  });
});

describe("ConfigManager - 智能排除功能", () => {
  test("应该加载带有智能排除配置的配置文件", async () => {
    const testConfigPath = join(process.cwd(), "test-smart-exclude.json");
    const smartExcludeConfig = {
      failOnComplexity: 15,
      exclude: ["custom/**/*.ts"],
      excludeDefaults: false,
      includeOverrides: ["node_modules/important/**"],
      disableSmartExclusion: true
    };
    
    writeFileSync(testConfigPath, JSON.stringify(smartExcludeConfig, null, 2));
    
    try {
      const config = await ConfigManager.loadConfig(testConfigPath);
      
      expect(config.excludeDefaults).toBe(false);
      expect(config.includeOverrides).toContain("node_modules/important/**");
      expect(config.disableSmartExclusion).toBe(true);
      expect(config.exclude).toContain("custom/**/*.ts");
    } finally {
      if (existsSync(testConfigPath)) {
        unlinkSync(testConfigPath);
      }
      // 注意：不再需要清除缓存，因为每次调用都创建新实例
    }
  });

  test("应该验证智能排除配置字段", () => {
    // 验证 excludeDefaults 必须是布尔值
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [],
        report: {},
        excludeDefaults: "true" // 应该是布尔值
      });
    }).toThrow("excludeDefaults must be a boolean");

    // 验证 includeOverrides 必须是字符串数组
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [],
        report: {},
        includeOverrides: "not-array"
      });
    }).toThrow("includeOverrides must be an array of strings");

    // 验证 disableSmartExclusion 必须是布尔值
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [],
        report: {},
        disableSmartExclusion: "false"
      });
    }).toThrow("disableSmartExclusion must be a boolean");

    // 验证 includeOverrides 数组元素必须是字符串
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [],
        report: {},
        includeOverrides: [123, "valid-pattern"]
      });
    }).toThrow("includeOverrides[0] must be a string");
  });

  test("mergeRuntimeExcludes 应该正确合并CLI和配置排除规则", () => {
    const config: CognitiveConfig = {
      failOnComplexity: 15,
      exclude: ["config/**/*.ts", "vendor/**"],
      severityMapping: [],
      report: {},
      excludeDefaults: true,
      includeOverrides: [],
      disableSmartExclusion: false
    };

    const cliOptions: CLIOptions = {
      paths: ["src"],
      exclude: ["*.test.ts"],
      excludePattern: ["**/*.spec.js"],
      excludeDefaults: undefined
    };

    const result = ConfigManager.mergeRuntimeExcludes(config, cliOptions);

    expect(result.patterns).toContain("**/*.d.ts"); // 基本排除
    expect(result.patterns).toContain("config/**/*.ts"); // 配置文件排除
    expect(result.patterns).toContain("vendor/**"); // 配置文件排除
    expect(result.patterns).toContain("*.test.ts"); // CLI exclude
    expect(result.patterns).toContain("**/*.spec.js"); // CLI excludePattern

    expect(result.rules).toHaveLength(4); // 基本、配置、CLI exclude、CLI excludePattern
    expect(result.rules[0]?.source).toBe("default");
    expect(result.rules[1]?.source).toBe("config");
    expect(result.rules[2]?.source).toBe("cli");
    expect(result.rules[3]?.source).toBe("cli");
  });

  test("mergeRuntimeExcludes 应该禁用配置文件排除当 excludeDefaults=false", () => {
    const config: CognitiveConfig = {
      failOnComplexity: 15,
      exclude: ["config/**/*.ts"],
      severityMapping: [],
      report: {},
      excludeDefaults: true,
      includeOverrides: [],
      disableSmartExclusion: false
    };

    const cliOptions: CLIOptions = {
      paths: ["src"],
      excludeDefaults: false // CLI禁用默认排除
    };

    const result = ConfigManager.mergeRuntimeExcludes(config, cliOptions);

    expect(result.patterns).toContain("**/*.d.ts"); // 基本排除总是生效
    expect(result.patterns).not.toContain("config/**/*.ts"); // 配置文件排除被禁用
    expect(result.rules).toHaveLength(1); // 只有基本排除规则
  });

  test("mergeRuntimeExcludes 应该验证并警告无效的排除模式", () => {
    const config: CognitiveConfig = {
      failOnComplexity: 15,
      exclude: ["../dangerous-path", "valid-pattern/**"], // 包含危险路径
      severityMapping: [],
      report: {},
      excludeDefaults: true,
      includeOverrides: [],
      disableSmartExclusion: false
    };

    const cliOptions: CLIOptions = {
      paths: ["src"],
      exclude: ["/absolute-path", "valid-cli-pattern"], // 包含危险路径
      excludePattern: ["[unclosed-bracket", "valid-pattern"]
    };

    // 捕获控制台警告
    const originalWarn = console.warn;
    const warnings: string[] = [];
    console.warn = (...args: any[]) => {
      warnings.push(args.join(' '));
    };

    try {
      const result = ConfigManager.mergeRuntimeExcludes(config, cliOptions);

      // 应该过滤掉无效模式
      expect(result.patterns).not.toContain("../dangerous-path");
      expect(result.patterns).not.toContain("/absolute-path");
      expect(result.patterns).not.toContain("[unclosed-bracket");

      // 应该保留有效模式
      expect(result.patterns).toContain("valid-pattern/**");
      expect(result.patterns).toContain("valid-cli-pattern");
      expect(result.patterns).toContain("valid-pattern");

      // 应该输出警告
      expect(warnings.some(w => w.includes("CLI exclude模式无效"))).toBe(true);
      expect(warnings.some(w => w.includes("CLI excludePattern模式无效"))).toBe(true);
      expect(warnings.some(w => w.includes("配置文件exclude模式无效"))).toBe(true);
    } finally {
      console.warn = originalWarn;
    }
  });

  test("应该正确合并智能排除配置的默认值", async () => {
    const testConfigPath = join(process.cwd(), "test-partial-config.json");
    const partialConfig = {
      failOnComplexity: 20,
      excludeDefaults: false
      // 不提供其他智能排除配置
    };

    writeFileSync(testConfigPath, JSON.stringify(partialConfig, null, 2));

    try {
      const config = await ConfigManager.loadConfig(testConfigPath);

      expect(config.excludeDefaults).toBe(false); // 用户设置
      expect(config.includeOverrides).toEqual([]); // 默认值
      expect(config.disableSmartExclusion).toBe(false); // 默认值
    } finally {
      if (existsSync(testConfigPath)) {
        unlinkSync(testConfigPath);
      }
      // 注意：不再需要清除缓存，因为每次调用都创建新实例
    }
  });

  test("应该验证性能和安全配置字段", () => {
    // 验证 fileProcessingTimeout
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [],
        report: {},
        fileProcessingTimeout: -1000
      });
    }).toThrow("fileProcessingTimeout must be a positive number");

    // 验证 maxRecursionDepth
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [],
        report: {},
        maxRecursionDepth: 0
      });
    }).toThrow("maxRecursionDepth must be a positive number");

    // 验证 enableIterativeParser
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [],
        report: {},
        enableIterativeParser: "true"
      });
    }).toThrow("enableIterativeParser must be a boolean");

    // 验证 enablePerformanceLogging
    expect(() => {
      ConfigManager.validateConfig({
        failOnComplexity: 15,
        exclude: [],
        severityMapping: [],
        report: {},
        enablePerformanceLogging: 1
      });
    }).toThrow("enablePerformanceLogging must be a boolean");
  });
});