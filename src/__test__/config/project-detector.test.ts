/**
 * 项目检测器单元测试
 * 
 * 测试范围:
 * 1. 基本项目类型检测
 * 2. 配置文件识别
 * 3. package.json依赖检测
 * 4. monorepo检测
 * 5. 配置推荐生成
 * 6. 边界情况处理
 */

import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { existsSync, mkdirSync, writeFileSync, rmSync } from 'fs';
import { join } from 'path';
import { 
  ProjectDetector, 
  detectProjectType, 
  generateProjectConfig,
  generateSmartConfig,
  type ProjectDetectionResult,
  type ConfigRecommendation 
} from '../../config/project-detector';
import type { ProjectType } from '../../config/schema';

// =============================================================================
// 测试工具和辅助函数
// =============================================================================

/**
 * 测试项目结构生成器
 */
class TestProjectBuilder {
  private readonly projectPath: string;
  
  constructor(basePath: string, projectName: string) {
    this.projectPath = join(basePath, projectName);
    this.ensureDirectory(this.projectPath);
  }
  
  /**
   * 添加package.json文件
   */
  addPackageJson(dependencies: Record<string, string> = {}, devDependencies: Record<string, string> = {}): this {
    const packageJson = {
      name: 'test-project',
      version: '1.0.0',
      dependencies,
      devDependencies,
    };
    
    writeFileSync(
      join(this.projectPath, 'package.json'),
      JSON.stringify(packageJson, null, 2)
    );
    
    return this;
  }
  
  /**
   * 添加配置文件
   */
  addConfigFile(filename: string, content: string | object = {}): this {
    const contentStr = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
    writeFileSync(join(this.projectPath, filename), contentStr);
    return this;
  }
  
  /**
   * 添加源代码文件
   */
  addSourceFile(relativePath: string, content: string = ''): this {
    const fullPath = join(this.projectPath, relativePath);
    this.ensureDirectory(join(fullPath, '..'));
    writeFileSync(fullPath, content);
    return this;
  }
  
  /**
   * 添加目录
   */
  addDirectory(relativePath: string): this {
    this.ensureDirectory(join(this.projectPath, relativePath));
    return this;
  }
  
  /**
   * 创建workspaces配置（用于monorepo测试）
   */
  addWorkspaces(workspaces: string[]): this {
    const packageJsonPath = join(this.projectPath, 'package.json');
    let packageJson;
    
    try {
      packageJson = JSON.parse(require('fs').readFileSync(packageJsonPath, 'utf-8'));
    } catch {
      packageJson = { name: 'test-project', version: '1.0.0' };
    }
    
    packageJson.workspaces = workspaces;
    writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    
    return this;
  }
  
  /**
   * 获取项目路径
   */
  getPath(): string {
    return this.projectPath;
  }
  
  /**
   * 清理项目
   */
  cleanup(): void {
    if (existsSync(this.projectPath)) {
      rmSync(this.projectPath, { recursive: true, force: true });
    }
  }
  
  private ensureDirectory(dirPath: string): void {
    if (!existsSync(dirPath)) {
      mkdirSync(dirPath, { recursive: true });
    }
  }
}

// =============================================================================
// 测试数据和常量
// =============================================================================

const TEST_PROJECT_BASE = join(__dirname, '../fixtures/test-projects');
const MOCK_REACT_COMPONENT = `
import React from 'react';

interface Props {
  title: string;
  onClick: () => void;
}

export const Button: React.FC<Props> = ({ title, onClick }) => {
  return (
    <button onClick={onClick} className="btn">
      {title}
    </button>
  );
};
`;

const MOCK_NEXT_PAGE = `
import { NextPage } from 'next';

const HomePage: NextPage = () => {
  return (
    <div>
      <h1>Welcome to Next.js</h1>
    </div>
  );
};

export default HomePage;
`;

const MOCK_VUE_COMPONENT = `
<template>
  <div class="hello">
    <h1>{{ msg }}</h1>
  </div>
</template>

<script>
export default {
  name: 'HelloWorld',
  props: {
    msg: String
  }
}
</script>
`;

// =============================================================================
// 主要测试套件
// =============================================================================

describe('ProjectDetector', () => {
  let detector: ProjectDetector;
  
  beforeEach(() => {
    // 确保测试目录存在
    if (!existsSync(TEST_PROJECT_BASE)) {
      mkdirSync(TEST_PROJECT_BASE, { recursive: true });
    }
  });
  
  afterEach(() => {
    // 清理测试项目
    if (existsSync(TEST_PROJECT_BASE)) {
      rmSync(TEST_PROJECT_BASE, { recursive: true, force: true });
    }
  });
  
  // =============================================================================
  // React项目检测测试
  // =============================================================================
  
  describe('React项目检测', () => {
    test('应该检测到基本React项目', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'react-basic');
      
      builder
        .addPackageJson({
          'react': '18.2.0',
          'react-dom': '18.2.0'
        }, {
          '@types/react': '18.2.0'
        })
        .addDirectory('src')
        .addDirectory('public')
        .addSourceFile('src/App.tsx', MOCK_REACT_COMPONENT)
        .addSourceFile('public/index.html', '<html></html>');
      
      detector = new ProjectDetector(builder.getPath());
      const result = await detector.detectProject();
      
      expect(result.type).toBe('react');
      expect(result.confidence).toBeGreaterThan(0.6);
      expect(result.configFiles).toContain(join(builder.getPath(), 'package.json'));
      expect(result.excludePatterns).toContain('**/node_modules/**');
      expect(result.excludePatterns).toContain('**/*.test.tsx');
      
      builder.cleanup();
    });
    
    test('应该为React项目生成正确的JSX配置推荐', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'react-jsx');
      
      builder
        .addPackageJson({ 
          'react': '18.2.0', 
          'react-dom': '18.2.0',
          '@types/react': '18.2.0'
        })
        .addDirectory('src')
        .addDirectory('public')
        .addSourceFile('src/components/Button.tsx', MOCK_REACT_COMPONENT)
        .addSourceFile('public/index.html', '<html></html>');
      
      detector = new ProjectDetector(builder.getPath());
      const detection = await detector.detectProject();
      const recommendation = detector.generateConfigRecommendation(detection);
      
      // 确保检测为React项目
      expect(detection.type).toBe('react');
      expect(recommendation.config.rules?.jsx?.enabled).toBe(true);
      expect(recommendation.config.rules?.jsx?.exemptions?.structuralNodes).toBe(true);
      expect(recommendation.config.rules?.jsx?.scoring?.conditionalRendering).toBe(true);
      expect(recommendation.reasons).toContain('检测到项目类型: react');
      expect(recommendation.suggestions).toContain('建议为组件和业务逻辑设置不同的复杂度标准');
      
      builder.cleanup();
    });
  });
  
  // =============================================================================
  // Next.js项目检测测试
  // =============================================================================
  
  describe('Next.js项目检测', () => {
    test('应该检测到Next.js项目(pages router)', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'nextjs-pages');
      
      builder
        .addPackageJson({
          'next': '13.4.0',
          'react': '18.2.0',
          'react-dom': '18.2.0'
        })
        .addConfigFile('next.config.js', 'module.exports = {}')
        .addDirectory('pages')
        .addDirectory('public')
        .addSourceFile('pages/index.tsx', MOCK_NEXT_PAGE);
      
      detector = new ProjectDetector(builder.getPath());
      const result = await detector.detectProject();
      
      expect(result.type).toBe('nextjs');
      expect(result.confidence).toBeGreaterThan(0.8);
      expect(result.excludePatterns).toContain('**/.next/**');
      expect(result.frameworkVersion).toBeDefined();
      
      builder.cleanup();
    });
    
    test('应该检测到Next.js项目(app router)', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'nextjs-app');
      
      builder
        .addPackageJson({ 'next': '14.0.0', 'react': '18.2.0' })
        .addConfigFile('next.config.ts', 'export default {}')
        .addDirectory('app')
        .addSourceFile('app/page.tsx', 'export default function Page() { return <div>App Router</div>; }');
      
      detector = new ProjectDetector(builder.getPath());
      const result = await detector.detectProject();
      
      expect(result.type).toBe('nextjs');
      expect(result.confidence).toBeGreaterThan(0.8);
      
      builder.cleanup();
    });
    
    test('Next.js项目应该启用高级JSX分析', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'nextjs-advanced');
      
      builder
        .addPackageJson({ 'next': '13.4.0', 'react': '18.2.0' })
        .addConfigFile('next.config.js', '{}');
      
      detector = new ProjectDetector(builder.getPath());
      const detection = await detector.detectProject();
      const recommendation = detector.generateConfigRecommendation(detection);
      
      expect(recommendation.config.rules?.jsx?.advanced?.detectHookComplexity).toBe(true);
      expect(recommendation.config.rules?.jsx?.advanced?.analyzeProps).toBe(true);
      expect(recommendation.config.rules?.jsx?.scoring?.nestedComponents).toBe(true);
      
      builder.cleanup();
    });
  });
  
  // =============================================================================
  // Vue项目检测测试
  // =============================================================================
  
  describe('Vue项目检测', () => {
    test('应该检测到Vue项目', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'vue-project');
      
      builder
        .addPackageJson({
          'vue': '3.3.0',
          '@vue/cli-service': '5.0.0'
        })
        .addConfigFile('vue.config.js', 'module.exports = {}')
        .addDirectory('src')
        .addSourceFile('src/components/HelloWorld.vue', MOCK_VUE_COMPONENT)
        .addSourceFile('src/main.ts', 'import { createApp } from "vue";');
      
      detector = new ProjectDetector(builder.getPath());
      const result = await detector.detectProject();
      
      expect(result.type).toBe('vue');
      expect(result.confidence).toBeGreaterThan(0.6);
      expect(result.excludePatterns).toContain('**/*.test.js');
      
      builder.cleanup();
    });
    
    test('Vue项目应该禁用JSX分析', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'vue-no-jsx');
      
      builder
        .addPackageJson({ 
          'vue': '3.3.0',
          '@vue/cli-service': '5.0.0'
        })
        .addDirectory('src')
        .addConfigFile('vue.config.js', '{}')
        .addSourceFile('src/main.ts', 'import { createApp } from "vue";')
        .addSourceFile('src/App.vue', '<template><div>Hello</div></template>');
      
      detector = new ProjectDetector(builder.getPath());
      const detection = await detector.detectProject();
      const recommendation = detector.generateConfigRecommendation(detection);
      
      // 确保检测为Vue项目
      expect(detection.type).toBe('vue');
      expect(recommendation.config.rules?.jsx?.enabled).toBe(false);
      expect(recommendation.suggestions).toContain('Vue组件的template部分通常不需要复杂度分析');
      
      builder.cleanup();
    });
  });
  
  // =============================================================================
  // monorepo检测测试
  // =============================================================================
  
  describe('Monorepo检测', () => {
    test('应该检测到workspaces monorepo', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'monorepo-workspaces');
      
      builder
        .addPackageJson()
        .addWorkspaces(['packages/*', 'apps/*'])
        .addDirectory('packages/ui')
        .addDirectory('packages/utils')
        .addDirectory('apps/web');
      
      // 添加子项目
      new TestProjectBuilder(builder.getPath(), 'packages/ui')
        .addPackageJson({ 'react': '18.2.0' })
        .addSourceFile('src/Button.tsx', MOCK_REACT_COMPONENT);
      
      new TestProjectBuilder(builder.getPath(), 'apps/web')
        .addPackageJson({ 'next': '13.4.0', 'react': '18.2.0' })
        .addConfigFile('next.config.js', '{}');
      
      detector = new ProjectDetector(builder.getPath());
      const result = await detector.detectProject();
      
      expect(result.isMonorepo).toBe(true);
      expect(result.subProjects).toBeDefined();
      expect(result.subProjects!.length).toBeGreaterThan(0);
      
      // 检查子项目检测
      const reactProject = result.subProjects!.find(p => p.type === 'react');
      const nextProject = result.subProjects!.find(p => p.type === 'nextjs');
      
      expect(reactProject).toBeDefined();
      expect(nextProject).toBeDefined();
      
      builder.cleanup();
    });
    
    test('应该检测到lerna monorepo', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'monorepo-lerna');
      
      builder
        .addConfigFile('lerna.json', {
          version: '1.0.0',
          packages: ['packages/*']
        })
        .addDirectory('packages');
      
      detector = new ProjectDetector(builder.getPath());
      const result = await detector.detectProject();
      
      expect(result.isMonorepo).toBe(true);
      
      builder.cleanup();
    });
    
    test('monorepo应该推荐增加并发数', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'monorepo-perf');
      
      builder
        .addPackageJson()
        .addWorkspaces(['packages/*']);
      
      detector = new ProjectDetector(builder.getPath());
      const detection = await detector.detectProject();
      const recommendation = detector.generateConfigRecommendation(detection);
      
      expect(recommendation.config.performance?.maxConcurrency).toBe(6);
      expect(recommendation.config.performance?.cacheSize).toBe(2000);
      expect(recommendation.reasons).toContain('检测到monorepo，增加并发数和缓存大小');
      
      builder.cleanup();
    });
  });
  
  // =============================================================================
  // 边界情况和错误处理测试
  // =============================================================================
  
  describe('边界情况处理', () => {
    test('应该处理不存在的路径', async () => {
      detector = new ProjectDetector('/non/existent/path');
      
      await expect(detector.detectProject()).rejects.toThrow('项目路径不存在');
    });
    
    test('应该处理空项目目录', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'empty-project');
      
      detector = new ProjectDetector(builder.getPath());
      const result = await detector.detectProject();
      
      expect(result.type).toBe('generic');
      expect(result.confidence).toBeLessThan(0.2);
      
      builder.cleanup();
    });
    
    test('应该处理损坏的package.json', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'broken-package');
      
      builder.addConfigFile('package.json', '{ invalid json');
      
      detector = new ProjectDetector(builder.getPath());
      const result = await detector.detectProject();
      
      // 应该仍然能够检测，只是不会读取依赖信息
      expect(result.type).toBe('generic');
      expect(result.detectionLog.some(log => log.includes('无法解析package.json'))).toBe(true);
      
      builder.cleanup();
    });
    
    test('应该缓存检测结果', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'cache-test');
      
      builder
        .addPackageJson({ 'react': '18.2.0' })
        .addSourceFile('src/App.tsx', MOCK_REACT_COMPONENT);
      
      detector = new ProjectDetector(builder.getPath());
      
      // 第一次检测
      const result1 = await detector.detectProject();
      
      // 第二次检测应该使用缓存
      const result2 = await detector.detectProject();
      
      expect(result1).toEqual(result2);
      
      // 清除缓存后应该重新检测
      detector.clearCache();
      const result3 = await detector.detectProject();
      
      expect(result3.type).toBe(result1.type);
      
      builder.cleanup();
    });
  });
  
  // =============================================================================
  // 配置推荐测试
  // =============================================================================
  
  describe('配置推荐生成', () => {
    test('应该为高置信度检测生成高优先级推荐', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'high-confidence');
      
      builder
        .addPackageJson({ 'next': '13.4.0', 'react': '18.2.0' })
        .addConfigFile('next.config.js', '{}');
      
      detector = new ProjectDetector(builder.getPath());
      const detection = await detector.detectProject();
      const recommendation = detector.generateConfigRecommendation(detection);
      
      expect(detection.confidence).toBeGreaterThan(0.8);
      expect(recommendation.priority).toBe('high');
      
      builder.cleanup();
    });
    
    test('应该为低置信度检测生成低优先级推荐', async () => {
      const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'low-confidence');
      
      builder.addSourceFile('index.js', 'console.log("hello");');
      
      detector = new ProjectDetector(builder.getPath());
      const detection = await detector.detectProject();
      const recommendation = detector.generateConfigRecommendation(detection);
      
      expect(detection.confidence).toBeLessThan(0.6);
      expect(recommendation.priority).toBe('low');
      
      builder.cleanup();
    });
    
    test('应该为不同项目类型生成不同的排除规则', async () => {
      // React项目
      const reactBuilder = new TestProjectBuilder(TEST_PROJECT_BASE, 'react-exclude');
      reactBuilder.addPackageJson({ 'react': '18.2.0' });
      
      // Next.js项目
      const nextBuilder = new TestProjectBuilder(TEST_PROJECT_BASE, 'next-exclude');
      nextBuilder
        .addPackageJson({ 'next': '13.4.0' })
        .addConfigFile('next.config.js', '{}');
      
      const reactDetector = new ProjectDetector(reactBuilder.getPath());
      const nextDetector = new ProjectDetector(nextBuilder.getPath());
      
      const reactResult = await reactDetector.detectProject();
      const nextResult = await nextDetector.detectProject();
      
      // Next.js应该有额外的.next排除规则
      expect(nextResult.excludePatterns).toContain('**/.next/**');
      expect(reactResult.excludePatterns).not.toContain('**/.next/**');
      
      reactBuilder.cleanup();
      nextBuilder.cleanup();
    });
  });
});

// =============================================================================
// 便利函数测试
// =============================================================================

describe('便利函数', () => {
  afterEach(() => {
    if (existsSync(TEST_PROJECT_BASE)) {
      rmSync(TEST_PROJECT_BASE, { recursive: true, force: true });
    }
  });
  
  test('detectProjectType 应该正确工作', async () => {
    const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'detect-convenience');
    
    builder
      .addPackageJson({ 'vue': '3.3.0' })
      .addConfigFile('vue.config.js', '{}');
    
    const result = await detectProjectType(builder.getPath());
    
    expect(result.type).toBe('vue');
    expect(result.confidence).toBeGreaterThan(0.7);
    
    builder.cleanup();
  });
  
  test('generateProjectConfig 应该正确工作', () => {
    const mockDetection: ProjectDetectionResult = {
      type: 'react',
      configFiles: ['package.json'],
      excludePatterns: ['**/node_modules/**', '**/*.test.tsx'],
      confidence: 0.9,
      isMonorepo: false,
      rootPath: '/mock/path',
      detectionLog: ['Mock detection'],
    };
    
    const recommendation = generateProjectConfig(mockDetection);
    
    expect(recommendation.config.rules?.jsx?.enabled).toBe(true);
    expect(recommendation.reasons).toContain('检测到项目类型: react');
    expect(recommendation.priority).toBe('high');
  });
  
  test('generateSmartConfig 应该返回完整的智能配置', async () => {
    const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'smart-config');
    
    builder
      .addPackageJson({ 'react': '18.2.0' })
      .addSourceFile('src/App.tsx', MOCK_REACT_COMPONENT);
    
    const result = await generateSmartConfig(builder.getPath());
    
    expect(result.detection.type).toBe('react');
    expect(result.recommendation.config.rules?.jsx?.enabled).toBe(true);
    expect(result.config).toBeDefined();
    
    builder.cleanup();
  });
});

// =============================================================================
// 性能测试
// =============================================================================

describe('性能测试', () => {
  test('大型项目检测应该在合理时间内完成', async () => {
    const builder = new TestProjectBuilder(TEST_PROJECT_BASE, 'large-project');
    
    // 模拟大型项目结构
    builder
      .addPackageJson({ 'next': '13.4.0', 'react': '18.2.0' })
      .addConfigFile('next.config.js', '{}');
    
    // 添加多个源文件
    for (let i = 0; i < 50; i++) {
      builder.addSourceFile(`src/components/Component${i}.tsx`, MOCK_REACT_COMPONENT);
      builder.addSourceFile(`pages/page-${i}.tsx`, MOCK_NEXT_PAGE);
    }
    
    const detector = new ProjectDetector(builder.getPath());
    
    const startTime = Date.now();
    const result = await detector.detectProject();
    const endTime = Date.now();
    
    expect(result.type).toBe('nextjs');
    expect(endTime - startTime).toBeLessThan(5000); // 应该在5秒内完成
    
    builder.cleanup();
  }, 10000); // 设置10秒超时
});