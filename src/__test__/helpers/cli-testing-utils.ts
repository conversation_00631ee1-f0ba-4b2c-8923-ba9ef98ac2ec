import { spawn } from 'child_process';
import type { ChildProcess } from 'child_process';

/**
 * CLI 测试结果接口
 * 提供与 cli-testing-library 兼容的测试实例
 */
export interface CLITestResult {
  readonly stdout: string;
  readonly stderr: string;
  readonly exitCode: number | null;
  readonly isRunning: boolean;
  
  // 查询方法
  findByText(text: string | RegExp): Promise<boolean>;
  queryByText(text: string | RegExp): Promise<string | null>;
  getByText(text: string | RegExp): Promise<string>;
  
  // 等待方法
  waitForOutput(text: string | RegExp, timeout?: number): Promise<void>;
  waitForExit(timeout?: number): Promise<number>;
  
  // 用户交互
  userEvent: {
    type(text: string): Promise<void>;
    keyboard(keys: string): Promise<void>;
  };
  
  // 进程控制
  clear(): Promise<void>;
  kill(signal?: NodeJS.Signals): Promise<void>;
}

/**
 * CLI 测试配置接口
 */
export interface CLITestConfig {
  readonly timeout: number;          // 测试超时时间（毫秒）
  readonly maxBuffer: number;        // 输出缓冲区大小
  readonly env: Record<string, string>; // 环境变量
  readonly cwd: string;              // 工作目录
  readonly cleanup: boolean;         // 是否自动清理
}

/**
 * 测试场景配置接口
 */
export interface TestScenario {
  readonly name: string;             // 测试场景名称
  readonly command: string;          // CLI命令
  readonly args: string[];           // 命令参数
  readonly expectedOutput: string[]; // 期望输出
  readonly expectedExitCode: number; // 期望退出码
  readonly interactive?: boolean;    // 是否交互式
  readonly timeout?: number;         // 自定义超时
}

/**
 * 测试执行结果接口
 */
export interface TestExecutionResult {
  readonly scenario: string;         // 测试场景
  readonly success: boolean;         // 执行是否成功
  readonly duration: number;         // 执行时间（毫秒）
  readonly output: {
    stdout: string;
    stderr: string;
    exitCode: number;
  };
  readonly assertions: AssertionResult[]; // 断言结果
}

/**
 * 断言结果接口
 */
export interface AssertionResult {
  readonly type: 'output' | 'exitCode' | 'performance';
  readonly expected: unknown;
  readonly actual: unknown;
  readonly passed: boolean;
  readonly message?: string;
}

/**
 * CLI 测试错误类
 */
export class CLITestError extends Error {
  constructor(
    message: string,
    public readonly command: string,
    public readonly output?: string,
    public readonly exitCode?: number
  ) {
    super(message);
    this.name = 'CLITestError';
  }
}

/**
 * 输出验证错误类
 */
export class OutputValidationError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly expected: string,
    public readonly actual: string
  ) {
    super(message, command);
    this.name = 'OutputValidationError';
  }
}

/**
 * 交互式测试错误类
 */
export class InteractiveTestError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly step: string
  ) {
    super(message, command);
    this.name = 'InteractiveTestError';
  }
}

/**
 * 测试超时错误类
 */
export class TestTimeoutError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly timeoutMs: number
  ) {
    super(message, command);
    this.name = 'TestTimeoutError';
  }
}

/**
 * 进程清理错误类
 */
export class ProcessCleanupError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly processId?: number
  ) {
    super(message, command);
    this.name = 'ProcessCleanupError';
  }
}

/**
 * 缓冲区溢出错误类
 */
export class BufferOverflowError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly bufferSize: number,
    public readonly maxBuffer: number
  ) {
    super(message, command);
    this.name = 'BufferOverflowError';
  }
}

/**
 * 内部 CLI 测试实例实现
 */
class CLITestInstance implements CLITestResult {
  private _stdout = '';
  private _stderr = '';
  private _exitCode: number | null = null;
  private _process: ChildProcess;
  private _waitPromise: Promise<number>;
  private _waitReject?: (reason?: any) => void;
  private _config: CLITestConfig;
  private _command: string;
  private _args: string[];
  private _startTime: number;
  private _timeoutId?: NodeJS.Timeout;
  private _isKilled = false;

  // Public getter for CLITestingUtils access
  get _waitPromise_internal(): Promise<number> {
    return this._waitPromise;
  }

  constructor(
    command: string,
    args: string[],
    config: CLITestConfig
  ) {
    this._command = command;
    this._args = args;
    this._config = config;
    this._startTime = Date.now();
    
    this._process = spawn(command, args, {
      env: config.env,
      cwd: config.cwd,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    // 确保 stdin 是可写的
    if (!this._process.stdin) {
      throw new CLITestError(
        'Failed to create process with writable stdin',
        `${command} ${args.join(' ')}`
      );
    }

    // 设置进程超时
    if (config.timeout > 0) {
      this._timeoutId = setTimeout(() => {
        this._handleTimeout();
      }, config.timeout);
    }

    // 监听输出并检查缓冲区大小
    this._process.stdout?.on('data', (data) => {
      const newData = data.toString();
      if (this._stdout.length + newData.length > config.maxBuffer) {
        this._handleBufferOverflow('stdout', this._stdout.length + newData.length);
        return;
      }
      this._stdout += newData;
    });

    this._process.stderr?.on('data', (data) => {
      const newData = data.toString();
      if (this._stderr.length + newData.length > config.maxBuffer) {
        this._handleBufferOverflow('stderr', this._stderr.length + newData.length);
        return;
      }
      this._stderr += newData;
    });

    // 创建等待 Promise
    this._waitPromise = new Promise((resolve, reject) => {
      this._waitReject = reject; // 保存 reject 函数引用
      
      this._process.on('exit', (code) => {
        this._clearTimeout();
        this._exitCode = code || 0;
        resolve(code || 0);
      });

      this._process.on('error', (error) => {
        this._clearTimeout();
        reject(new CLITestError(
          `Process error: ${error.message}`,
          `${command} ${args.join(' ')}`,
          this._stdout + this._stderr,
          -1
        ));
      });

      // 处理进程意外终止
      this._process.on('disconnect', () => {
        if (!this._isKilled && this._exitCode === null) {
          reject(new ProcessCleanupError(
            'Process disconnected unexpectedly',
            `${command} ${args.join(' ')}`,
            this._process.pid
          ));
        }
      });
    });
  }

  private _clearTimeout(): void {
    if (this._timeoutId) {
      clearTimeout(this._timeoutId);
      this._timeoutId = undefined;
    }
  }

  private _handleTimeout(): void {
    if (this._process && !this._process.killed && !this._isKilled) {
      this._isKilled = true;
      this._process.kill('SIGKILL');
      
      const error = new TestTimeoutError(
        `Command timed out after ${this._config.timeout}ms`,
        `${this._command} ${this._args.join(' ')}`,
        this._config.timeout
      );
      
      // 使用保存的 reject 函数来 reject Promise
      if (this._waitReject) {
        this._waitReject(error);
      }
    }
  }

  private _handleBufferOverflow(streamType: 'stdout' | 'stderr', currentSize: number): void {
    this._isKilled = true;
    this._process.kill('SIGKILL');
    
    throw new BufferOverflowError(
      `Buffer overflow in ${streamType}: ${currentSize} bytes exceeds limit of ${this._config.maxBuffer} bytes`,
      `${this._command} ${this._args.join(' ')}`,
      currentSize,
      this._config.maxBuffer
    );
  }

  get stdout(): string {
    return this._stdout;
  }

  get stderr(): string {
    return this._stderr;
  }

  get exitCode(): number | null {
    return this._exitCode;
  }

  get isRunning(): boolean {
    return this._exitCode === null;
  }

  async findByText(text: string | RegExp): Promise<boolean> {
    const pattern = typeof text === 'string' ? new RegExp(text, 'i') : text;
    const content = this._stdout + this._stderr;
    return pattern.test(content);
  }

  async queryByText(text: string | RegExp): Promise<string | null> {
    const pattern = typeof text === 'string' ? new RegExp(text, 'i') : text;
    const content = this._stdout + this._stderr;
    const match = content.match(pattern);
    return match ? match[0] : null;
  }

  async getByText(text: string | RegExp): Promise<string> {
    const result = await this.queryByText(text);
    if (result === null) {
      throw new Error(`Text not found: ${text}`);
    }
    return result;
  }

  async waitForOutput(text: string | RegExp, timeout: number = 5000): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      if (await this.findByText(text)) {
        return;
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    throw new Error(`Timeout waiting for output: ${text}`);
  }

  async waitForExit(timeout?: number): Promise<number> {
    if (timeout) {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Timeout waiting for exit: ${timeout}ms`)), timeout);
      });
      return await Promise.race([this._waitPromise, timeoutPromise]);
    }
    return await this._waitPromise;
  }

  get userEvent() {
    return {
      async type(text: string): Promise<void> {
        if (this._process.stdin && !this._process.stdin.destroyed) {
          try {
            this._process.stdin.write(text);
          } catch (error) {
            throw new InteractiveTestError(
              `Failed to write to stdin: ${error instanceof Error ? error.message : String(error)}`,
              `${this._command} ${this._args.join(' ')}`,
              'type'
            );
          }
        } else {
          throw new InteractiveTestError(
            'Process stdin is not available or has been destroyed',
            `${this._command} ${this._args.join(' ')}`,
            'type'
          );
        }
      },

      async keyboard(keys: string): Promise<void> {
        if (this._process.stdin && !this._process.stdin.destroyed) {
          try {
            // 简单的键盘模拟，处理每个字符
            for (const key of keys) {
              this._process.stdin.write(key);
            }
          } catch (error) {
            throw new InteractiveTestError(
              `Failed to write keys to stdin: ${error instanceof Error ? error.message : String(error)}`,
              `${this._command} ${this._args.join(' ')}`,
              'keyboard'
            );
          }
        } else {
          throw new InteractiveTestError(
            'Process stdin is not available or has been destroyed',
            `${this._command} ${this._args.join(' ')}`,
            'keyboard'
          );
        }
      }
    };
  }

  async clear(): Promise<void> {
    this._stdout = '';
    this._stderr = '';
  }

  async kill(signal: NodeJS.Signals = 'SIGTERM'): Promise<void> {
    this._clearTimeout();
    
    if (this._process && !this._process.killed && !this._isKilled) {
      this._isKilled = true;
      
      // 尝试优雅终止
      this._process.kill(signal);
      
      // 等待进程终止，如果超时则强制终止
      const gracefulTimeout = setTimeout(() => {
        if (this._process && !this._process.killed) {
          console.warn(`Force killing process ${this._process.pid} after graceful termination timeout`);
          this._process.kill('SIGKILL');
        }
      }, 5000); // 5秒优雅终止超时
      
      try {
        await this._waitPromise;
        clearTimeout(gracefulTimeout);
      } catch (error) {
        clearTimeout(gracefulTimeout);
        // 如果是我们自己的超时或清理错误，不需要再抛出
        if (!(error instanceof TestTimeoutError) && !(error instanceof ProcessCleanupError)) {
          throw new ProcessCleanupError(
            `Failed to kill process: ${error instanceof Error ? error.message : String(error)}`,
            `${this._command} ${this._args.join(' ')}`,
            this._process.pid
          );
        }
      }
      
      // 确保stdin/stdout/stderr流被关闭
      this._process.stdin?.destroy();
      this._process.stdout?.destroy();
      this._process.stderr?.destroy();
    }
  }

  /**
   * 获取进程运行时长（毫秒）
   */
  getRuntime(): number {
    return Date.now() - this._startTime;
  }

  /**
   * 获取进程ID
   */
  getProcessId(): number | undefined {
    return this._process.pid;
  }

  /**
   * 检查进程是否已被手动终止
   */
  isKilled(): boolean {
    return this._isKilled;
  }
}

/**
 * 进程池管理器
 * 复用进程以减少启动开销
 */
class ProcessPool {
  private static instance: ProcessPool;
  private pools = new Map<string, CLITestInstance[]>();
  private readonly maxPoolSize = 5;

  static getInstance(): ProcessPool {
    if (!ProcessPool.instance) {
      ProcessPool.instance = new ProcessPool();
    }
    return ProcessPool.instance;
  }

  getPoolKey(command: string, args: string[], config: CLITestConfig): string {
    return `${command}:${JSON.stringify(args)}:${config.cwd}`;
  }

  async borrowInstance(command: string, args: string[], config: CLITestConfig): Promise<CLITestInstance | null> {
    const key = this.getPoolKey(command, args, config);
    const pool = this.pools.get(key) || [];
    
    // 如果有可用的实例，直接返回
    if (pool.length > 0) {
      return pool.pop()!;
    }
    
    return null;
  }

  async returnInstance(instance: CLITestInstance, command: string, args: string[], config: CLITestConfig): Promise<void> {
    // 如果实例仍然可用，放回池中
    if (!instance.isKilled() && instance.exitCode === null) {
      const key = this.getPoolKey(command, args, config);
      const pool = this.pools.get(key) || [];
      
      if (pool.length < this.maxPoolSize) {
        pool.push(instance);
        this.pools.set(key, pool);
        return;
      }
    }
    
    // 否则清理实例
    await instance.kill();
  }

  async cleanup(): Promise<void> {
    const cleanupPromises: Promise<void>[] = [];
    
    for (const pool of this.pools.values()) {
      for (const instance of pool) {
        cleanupPromises.push(instance.kill().catch(console.warn));
      }
    }
    
    await Promise.allSettled(cleanupPromises);
    this.pools.clear();
  }
}

/**
 * 性能监控器
 * 监控测试执行性能并提供优化建议
 */
class PerformanceMonitor {
  private static metrics = new Map<string, {
    totalTime: number;
    avgTime: number;
    execCount: number;
    lastExecTime: number;
    memoryPeak: number;
  }>();

  static recordExecution(command: string, executionTime: number, memoryUsage: number): void {
    const key = command;
    const existing = this.metrics.get(key) || {
      totalTime: 0,
      avgTime: 0,
      execCount: 0,
      lastExecTime: 0,
      memoryPeak: 0
    };

    existing.totalTime += executionTime;
    existing.execCount += 1;
    existing.avgTime = existing.totalTime / existing.execCount;
    existing.lastExecTime = executionTime;
    existing.memoryPeak = Math.max(existing.memoryPeak, memoryUsage);

    this.metrics.set(key, existing);
  }

  static getMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    this.metrics.forEach((value, key) => {
      result[key] = { ...value };
    });
    return result;
  }

  static getSuggestions(): string[] {
    const suggestions: string[] = [];
    
    this.metrics.forEach((metrics, command) => {
      if (metrics.avgTime > 5000) {
        suggestions.push(`Command "${command}" 平均执行时间过长 (${metrics.avgTime.toFixed(0)}ms)，考虑优化或增加超时时间`);
      }
      if (metrics.memoryPeak > 50 * 1024 * 1024) {
        suggestions.push(`Command "${command}" 内存使用峰值过高 (${(metrics.memoryPeak / 1024 / 1024).toFixed(1)}MB)，检查内存泄漏`);
      }
    });

    return suggestions;
  }

  static reset(): void {
    this.metrics.clear();
  }
}

/**
 * CLI 测试工具类
 * 封装进程管理功能，提供统一的 CLI 测试接口
 */
export class CLITestingUtils {
  private static readonly DEFAULT_CONFIG: CLITestConfig = {
    timeout: 8000, // 降低默认超时以提高测试速度
    maxBuffer: 512 * 1024, // 减少缓冲区大小以节省内存
    env: { ...process.env },
    cwd: process.cwd(),
    cleanup: true
  };

  private static activeProcesses = new Set<CLITestInstance>();
  private static processPool = ProcessPool.getInstance();

  /**
   * 执行 CLI 命令并返回测试实例
   * 优化版本：使用进程池和性能监控
   */
  static async renderCLI(
    command: string, 
    args: string[] = [], 
    config: Partial<CLITestConfig> = {}
  ): Promise<CLITestResult> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const startTime = performance.now();
    const commandKey = `${command} ${args.join(' ')}`;
    
    try {
      // 尝试从进程池获取可复用的实例
      let instance = await this.processPool.borrowInstance(command, args, finalConfig);
      
      if (!instance) {
        // 如果池中没有可用实例，创建新的
        instance = new CLITestInstance(command, args, finalConfig);
      }
      
      this.activeProcesses.add(instance);
      
      // 优化的启动检查 - 减少等待时间
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(resolve, 25); // 减少到25ms以提高速度
        
        instance!._waitPromise_internal
          .then(() => {
            clearTimeout(timeout);
            resolve();
          })
          .catch(error => {
            clearTimeout(timeout);
            // 如果是 spawn 相关的错误，立即抛出
            if (error instanceof CLITestError && 
                (error.message.includes('ENOENT') || 
                 error.message.includes('Executable not found') ||
                 error.message.includes('spawn'))) {
              this.activeProcesses.delete(instance!);
              reject(error);
            } else {
              resolve(); // 其他错误让 instance 自己处理
            }
          });
      });
      
      // 记录性能指标
      const executionTime = performance.now() - startTime;
      const memoryUsage = process.memoryUsage().heapUsed;
      PerformanceMonitor.recordExecution(commandKey, executionTime, memoryUsage);
      
      return instance;
    } catch (error) {
      if (error instanceof CLITestError) {
        throw error; // 直接抛出我们的错误类型
      }
      throw new CLITestError(
        `Failed to render CLI command: ${error instanceof Error ? error.message : String(error)}`,
        `${command} ${args.join(' ')}`
      );
    }
  }

  /**
   * 等待特定文本出现
   */
  static async waitForOutput(
    instance: CLITestResult, 
    text: string | RegExp,
    timeout: number = 5000
  ): Promise<void> {
    return await instance.waitForOutput(text, timeout);
  }

  /**
   * 查询输出内容 - 查找匹配的文本
   */
  static async findByText(
    instance: CLITestResult, 
    text: string | RegExp
  ): Promise<boolean> {
    return await instance.findByText(text);
  }

  /**
   * 查询输出内容 - 可能返回 null
   */
  static async queryByText(
    instance: CLITestResult, 
    text: string | RegExp
  ): Promise<string | null> {
    return await instance.queryByText(text);
  }

  /**
   * 模拟用户输入
   */
  static async sendInput(
    instance: CLITestResult, 
    input: string
  ): Promise<void> {
    return await instance.userEvent.type(input);
  }

  /**
   * 模拟键盘按键
   */
  static async sendKeys(
    instance: CLITestResult, 
    keys: string
  ): Promise<void> {
    return await instance.userEvent.keyboard(keys);
  }

  /**
   * 进程管理和清理 - 优化版本
   * 支持进程池复用
   */
  static async cleanup(instance: CLITestResult): Promise<void> {
    try {
      if (instance instanceof CLITestInstance) {
        // 如果进程仍然可用，尝试放回进程池
        if (!instance.isKilled() && instance.exitCode === null) {
          // 获取实例的配置信息以便放回正确的池
          const command = (instance as any)._command;
          const args = (instance as any)._args;
          const config = (instance as any)._config;
          
          if (config && command && args) {
            await this.processPool.returnInstance(instance, command, args, config);
            this.activeProcesses.delete(instance);
            return;
          }
        }
        
        // 否则正常清理
        await instance.kill();
        this.activeProcesses.delete(instance);
      }
    } catch (error) {
      // 忽略清理过程中的错误
      console.warn('Warning during cleanup:', error);
    }
  }

  /**
   * 清理所有活动进程 - 优化版本
   * 使用并发清理提高性能
   */
  static async cleanupAll(): Promise<void> {
    // 先清理进程池
    await this.processPool.cleanup();
    
    // 使用 Promise.allSettled 并发清理所有活动进程
    const cleanupPromises = Array.from(this.activeProcesses).map(async (instance) => {
      try {
        await instance.kill();
      } catch (error) {
        console.warn('Warning cleaning up process:', error);
      }
    });

    await Promise.allSettled(cleanupPromises);
    this.activeProcesses.clear();
  }

  /**
   * 强制清理所有进程 - 不等待优雅终止
   * 批量处理以提高性能
   */
  static async forceCleanupAll(): Promise<void> {
    // 先强制清理进程池
    await this.processPool.cleanup();
    
    // 使用 Promise.allSettled 并发强制清理
    const cleanupPromises = Array.from(this.activeProcesses).map(async (instance) => {
      try {
        await instance.kill('SIGKILL');
      } catch (error) {
        console.warn('Warning force cleaning up process:', error);
      }
    });

    await Promise.allSettled(cleanupPromises);
    this.activeProcesses.clear();
  }

  /**
   * 获取当前活动进程数量
   */
  static getActiveProcessCount(): number {
    return this.activeProcesses.size;
  }

  /**
   * 获取活动进程的详细信息
   */
  static getActiveProcessesInfo(): Array<{
    processId?: number;
    command: string;
    runtime: number;
    isKilled: boolean;
  }> {
    return Array.from(this.activeProcesses).map(instance => ({
      processId: instance.getProcessId?.(),
      command: `${(instance as any)._command || 'unknown'} ${((instance as any)._args || []).join(' ')}`.trim(),
      runtime: instance.getRuntime?.() || 0,
      isKilled: instance.isKilled?.() || false
    }));
  }

  /**
   * 设置进程清理的全局错误处理器
   */
  static setGlobalErrorHandler(handler: (error: Error, processInfo: any) => void): void {
    process.on('exit', async () => {
      if (this.activeProcesses.size > 0) {
        console.warn(`Warning: ${this.activeProcesses.size} processes still active on exit`);
        try {
          await this.forceCleanupAll();
        } catch (error) {
          handler(error instanceof Error ? error : new Error(String(error)), {
            activeCount: this.activeProcesses.size
          });
        }
      }
    });

    process.on('SIGINT', async () => {
      console.log('\nReceived SIGINT, cleaning up processes...');
      try {
        await this.forceCleanupAll();
      } catch (error) {
        handler(error instanceof Error ? error : new Error(String(error)), {
          signal: 'SIGINT'
        });
      }
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('\nReceived SIGTERM, cleaning up processes...');
      try {
        await this.cleanupAll();
      } catch (error) {
        handler(error instanceof Error ? error : new Error(String(error)), {
          signal: 'SIGTERM'
        });
      }
      process.exit(0);
    });
  }

  /**
   * 验证 CLI 执行结果
   */
  static async validateExecution(
    instance: CLITestResult,
    expectedExitCode: number = 0,
    expectedOutput?: string[],
    timeout: number = 5000
  ): Promise<TestExecutionResult> {
    const startTime = performance.now();
    const assertions: AssertionResult[] = [];

    try {
      // 等待进程结束
      const actualExitCode = await instance.waitForExit(timeout);
      const duration = performance.now() - startTime;

      // 验证退出码
      const exitCodePassed = actualExitCode === expectedExitCode;
      assertions.push({
        type: 'exitCode',
        expected: expectedExitCode,
        actual: actualExitCode,
        passed: exitCodePassed,
        message: exitCodePassed ? undefined : `Expected exit code ${expectedExitCode}, got ${actualExitCode}`
      });

      // 验证输出内容
      if (expectedOutput) {
        for (const expectedText of expectedOutput) {
          const found = await instance.findByText(expectedText);
          assertions.push({
            type: 'output',
            expected: expectedText,
            actual: instance.stdout,
            passed: found,
            message: found ? undefined : `Expected output "${expectedText}" not found`
          });
        }
      }

      const allPassed = assertions.every(a => a.passed);

      return {
        scenario: 'CLI Execution',
        success: allPassed,
        duration,
        output: {
          stdout: instance.stdout,
          stderr: instance.stderr,
          exitCode: actualExitCode
        },
        assertions
      };
    } catch (error) {
      const duration = performance.now() - startTime;
      return {
        scenario: 'CLI Execution',
        success: false,
        duration,
        output: {
          stdout: instance.stdout,
          stderr: instance.stderr,
          exitCode: instance.exitCode || -1
        },
        assertions: [{
          type: 'exitCode',
          expected: expectedExitCode,
          actual: error,
          passed: false,
          message: `Execution failed: ${error instanceof Error ? error.message : String(error)}`
        }]
      };
    }
  }

  /**
   * 获取默认配置
   */
  static getDefaultConfig(): CLITestConfig {
    return { ...this.DEFAULT_CONFIG };
  }

  /**
   * 设置全局默认配置
   */
  static setDefaultConfig(config: Partial<CLITestConfig>): void {
    Object.assign(this.DEFAULT_CONFIG, config);
  }

  /**
   * 获取性能监控指标
   */
  static getPerformanceMetrics(): Record<string, any> {
    return PerformanceMonitor.getMetrics();
  }

  /**
   * 获取性能优化建议
   */
  static getPerformanceSuggestions(): string[] {
    return PerformanceMonitor.getSuggestions();
  }

  /**
   * 重置性能监控数据
   */
  static resetPerformanceMonitor(): void {
    PerformanceMonitor.reset();
  }

  /**
   * 获取进程池状态
   */
  static getProcessPoolStatus(): {
    poolCount: number;
    totalInstances: number;
    activeProcesses: number;
  } {
    return {
      poolCount: this.processPool['pools'].size,
      totalInstances: Array.from(this.processPool['pools'].values()).reduce((sum, pool) => sum + pool.length, 0),
      activeProcesses: this.activeProcesses.size
    };
  }

  /**
   * 优化测试套件性能
   * 提供自动调优建议和配置优化
   */
  static optimizeTestSuite(): {
    recommendations: string[];
    optimizedConfig: Partial<CLITestConfig>;
  } {
    const metrics = this.getPerformanceMetrics();
    const recommendations: string[] = [];
    const optimizedConfig: Partial<CLITestConfig> = {};

    // 分析平均执行时间并调整超时
    const avgExecutionTimes = Object.values(metrics).map((m: any) => m.avgTime);
    if (avgExecutionTimes.length > 0) {
      const maxAvgTime = Math.max(...avgExecutionTimes);
      if (maxAvgTime > this.DEFAULT_CONFIG.timeout * 0.8) {
        optimizedConfig.timeout = Math.ceil(maxAvgTime * 1.5);
        recommendations.push(`建议增加超时时间到 ${optimizedConfig.timeout}ms`);
      }
    }

    // 分析内存使用并调整缓冲区
    const memoryPeaks = Object.values(metrics).map((m: any) => m.memoryPeak);
    if (memoryPeaks.length > 0) {
      const maxMemory = Math.max(...memoryPeaks);
      if (maxMemory > this.DEFAULT_CONFIG.maxBuffer * 2) {
        optimizedConfig.maxBuffer = Math.ceil(maxMemory * 1.2);
        recommendations.push(`建议增加缓冲区大小到 ${(optimizedConfig.maxBuffer / 1024).toFixed(0)}KB`);
      }
    }

    // 进程池优化建议
    const poolStatus = this.getProcessPoolStatus();
    if (poolStatus.activeProcesses > 10) {
      recommendations.push('考虑增加进程池大小或使用更多并发测试');
    }

    return { recommendations, optimizedConfig };
  }

  /**
   * 批量执行测试用例 - 并发优化版本
   */
  static async runTestBatch<T>(
    testCases: Array<() => Promise<T>>,
    concurrency: number = 3
  ): Promise<Array<{ result?: T; error?: Error; duration: number }>> {
    const results: Array<{ result?: T; error?: Error; duration: number }> = [];
    
    // 分批执行以控制并发数
    for (let i = 0; i < testCases.length; i += concurrency) {
      const batch = testCases.slice(i, i + concurrency);
      
      const batchPromises = batch.map(async (testCase, index) => {
        const startTime = performance.now();
        try {
          const result = await testCase();
          return {
            index: i + index,
            result,
            duration: performance.now() - startTime
          };
        } catch (error) {
          return {
            index: i + index,
            error: error instanceof Error ? error : new Error(String(error)),
            duration: performance.now() - startTime
          };
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      
      batchResults.forEach((result, batchIndex) => {
        if (result.status === 'fulfilled') {
          results[result.value.index] = {
            result: result.value.result,
            error: result.value.error,
            duration: result.value.duration
          };
        } else {
          results[i + batchIndex] = {
            error: new Error(`Batch execution failed: ${result.reason}`),
            duration: 0
          };
        }
      });
    }

    return results;
  }
}