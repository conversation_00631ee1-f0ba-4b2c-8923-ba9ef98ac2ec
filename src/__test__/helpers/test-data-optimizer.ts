import { existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';
import type { TestFixture } from './fixture-manager';
import type { TestScenario, CLITestConfig } from './cli-testing-utils';
import { MockDataGenerator } from './test-utils';

/**
 * 测试数据缓存项
 */
interface TestDataCacheEntry {
  readonly id: string;
  readonly type: 'fixture' | 'scenario' | 'config' | 'result';
  readonly data: any;
  readonly metadata: {
    created: number;
    lastUsed: number;
    useCount: number;
    tags: string[];
    checksum: string;
  };
}

/**
 * 测试数据统计信息
 */
interface TestDataStatistics {
  readonly totalEntries: number;
  readonly byType: Record<string, number>;
  readonly byTags: Record<string, number>;
  readonly cacheHitRate: number;
  readonly mostUsedEntries: Array<{ id: string; useCount: number }>;
  readonly recentEntries: Array<{ id: string; lastUsed: number }>;
  readonly storageSize: number;
}

/**
 * 数据重用配置
 */
interface ReuseConfiguration {
  readonly enableCaching: boolean;
  readonly maxCacheSize: number;          // 最大缓存条目数
  readonly maxMemorySize: number;         // 最大内存使用（字节）
  readonly ttlMinutes: number;            // 生存时间（分钟）
  readonly autoCleanup: boolean;          // 自动清理过期数据
  readonly persistToDisk: boolean;        // 持久化到磁盘
  readonly compressionLevel: number;      // 压缩级别 (0-9)
}

/**
 * 测试数据优化和管理器
 * 负责测试数据的缓存、复用和性能优化
 */
export class TestDataOptimizer {
  private static cache = new Map<string, TestDataCacheEntry>();
  private static cacheHits = 0;
  private static cacheMisses = 0;
  private static config: ReuseConfiguration = {
    enableCaching: true,
    maxCacheSize: 1000,
    maxMemorySize: 100 * 1024 * 1024, // 100MB
    ttlMinutes: 60,
    autoCleanup: true,
    persistToDisk: true,
    compressionLevel: 6
  };
  
  private static cacheFile = join(process.cwd(), 'src/__test__/cache/test-data-cache.json');
  private static metricsFile = join(process.cwd(), 'src/__test__/cache/test-metrics.json');
  
  /**
   * 初始化优化器
   */
  static initialize(customConfig?: Partial<ReuseConfiguration>): void {
    if (customConfig) {
      this.config = { ...this.config, ...customConfig };
    }
    
    // 确保缓存目录存在
    const cacheDir = join(this.cacheFile, '..');
    if (!existsSync(cacheDir)) {
      mkdirSync(cacheDir, { recursive: true });
    }
    
    // 加载持久化缓存
    if (this.config.persistToDisk) {
      this.loadPersistedCache();
    }
    
    // 设置自动清理
    if (this.config.autoCleanup) {
      this.scheduleCleanup();
    }
  }
  
  /**
   * 获取或创建测试固件（带缓存）
   */
  static async getOrCreateFixture(
    id: string,
    creator: () => TestFixture | Promise<TestFixture>,
    tags: string[] = []
  ): Promise<TestFixture> {
    const cacheKey = `fixture:${id}`;
    
    // 尝试从缓存获取
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      this.cacheHits++;
      return cached as TestFixture;
    }
    
    // 创建新数据
    this.cacheMisses++;
    const fixture = await creator();
    
    // 存储到缓存
    this.setCachedData(cacheKey, fixture, 'fixture', tags);
    
    return fixture;
  }
  
  /**
   * 获取或创建测试场景（带缓存）
   */
  static async getOrCreateScenario(
    id: string,
    creator: () => TestScenario | Promise<TestScenario>,
    tags: string[] = []
  ): Promise<TestScenario> {
    const cacheKey = `scenario:${id}`;
    
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      this.cacheHits++;
      return cached as TestScenario;
    }
    
    this.cacheMisses++;
    const scenario = await creator();
    this.setCachedData(cacheKey, scenario, 'scenario', tags);
    
    return scenario;
  }
  
  /**
   * 获取或创建 CLI 配置（带缓存）
   */
  static async getOrCreateConfig(
    id: string,
    creator: () => CLITestConfig | Promise<CLITestConfig>,
    tags: string[] = []
  ): Promise<CLITestConfig> {
    const cacheKey = `config:${id}`;
    
    const cached = this.getCachedData(cacheKey);
    if (cached) {
      this.cacheHits++;
      return cached as CLITestConfig;
    }
    
    this.cacheMisses++;
    const config = await creator();
    this.setCachedData(cacheKey, config, 'config', tags);
    
    return config;
  }
  
  /**
   * 批量创建测试数据（优化版）
   */
  static async createBatchData<T>(
    prefix: string,
    count: number,
    creator: (index: number) => T | Promise<T>,
    options: {
      reusePattern?: 'none' | 'template' | 'full';
      batchSize?: number;
      tags?: string[];
    } = {}
  ): Promise<T[]> {
    const { reusePattern = 'template', batchSize = 10, tags = [] } = options;
    const results: T[] = [];
    
    switch (reusePattern) {
      case 'none':
        // 不重用，直接创建
        for (let i = 0; i < count; i++) {
          results.push(await creator(i));
        }
        break;
        
      case 'template':
        // 创建模板并变形
        const template = await creator(0);
        results.push(template);
        
        for (let i = 1; i < count; i++) {
          const variation = this.createVariation(template, i);
          results.push(variation as T);
        }
        break;
        
      case 'full':
        // 完全重用相同数据
        const baseData = await creator(0);
        for (let i = 0; i < count; i++) {
          results.push(baseData);
        }
        break;
    }
    
    // 缓存批次数据
    const batchId = `batch:${prefix}:${count}`;
    this.setCachedData(batchId, results, 'result', [...tags, 'batch']);
    
    return results;
  }
  
  /**
   * 智能数据复用 - 基于相似性查找可复用数据
   */
  static findReusableData<T>(
    targetId: string,
    type: TestDataCacheEntry['type'],
    similarityThreshold: number = 0.8
  ): T | null {
    const entries = Array.from(this.cache.values())
      .filter(entry => entry.type === type);
    
    if (entries.length === 0) return null;
    
    // 计算相似性
    const similarities = entries.map(entry => ({
      entry,
      similarity: this.calculateSimilarity(targetId, entry.id)
    }));
    
    // 找到最相似的条目
    const best = similarities
      .filter(s => s.similarity >= similarityThreshold)
      .sort((a, b) => b.similarity - a.similarity)[0];
    
    if (best) {
      this.updateUsageStats(best.entry.id);
      return best.entry.data as T;
    }
    
    return null;
  }
  
  /**
   * 预热缓存 - 提前生成常用数据
   */
  static async warmupCache(scenarios: Array<{
    id: string;
    type: 'fixture' | 'scenario' | 'config';
    creator: () => any;
    priority: number;
  }>): Promise<void> {
    // 按优先级排序
    scenarios.sort((a, b) => b.priority - a.priority);
    
    const startTime = Date.now();
    let warmedCount = 0;
    
    for (const scenario of scenarios) {
      try {
        const data = await scenario.creator();
        this.setCachedData(
          `${scenario.type}:${scenario.id}`,
          data,
          scenario.type,
          ['warmup']
        );
        warmedCount++;
      } catch (error) {
        console.warn(`Failed to warmup ${scenario.id}:`, error);
      }
    }
    
    const duration = Date.now() - startTime;
    console.log(`Cache warmup completed: ${warmedCount}/${scenarios.length} entries in ${duration}ms`);
  }
  
  /**
   * 优化内存使用
   */
  static optimizeMemoryUsage(): {
    before: number;
    after: number;
    freed: number;
    removedCount: number;
  } {
    const beforeSize = this.calculateCacheSize();
    const beforeCount = this.cache.size;
    
    // 移除过期条目
    const now = Date.now();
    const ttlMs = this.config.ttlMinutes * 60 * 1000;
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.metadata.lastUsed > ttlMs) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.cache.delete(key));
    
    // 如果仍然超过限制，移除最少使用的条目
    if (this.cache.size > this.config.maxCacheSize) {
      const entries = Array.from(this.cache.entries())
        .sort(([, a], [, b]) => a.metadata.useCount - b.metadata.useCount);
      
      const toRemove = this.cache.size - this.config.maxCacheSize;
      for (let i = 0; i < toRemove; i++) {
        const [key] = entries[i]!;
        this.cache.delete(key);
      }
    }
    
    const afterSize = this.calculateCacheSize();
    const afterCount = this.cache.size;
    
    return {
      before: beforeSize,
      after: afterSize,
      freed: beforeSize - afterSize,
      removedCount: beforeCount - afterCount
    };
  }
  
  /**
   * 生成测试数据使用报告
   */
  static generateUsageReport(): {
    statistics: TestDataStatistics;
    recommendations: string[];
    optimization: {
      potentialSavings: number;
      duplicateCount: number;
      underutilizedCount: number;
    };
  } {
    const statistics = this.getStatistics();
    const recommendations: string[] = [];
    
    // 分析缓存命中率
    if (statistics.cacheHitRate < 0.5) {
      recommendations.push('Consider increasing cache size or TTL to improve hit rate');
    }
    
    // 分析存储使用
    if (statistics.storageSize > this.config.maxMemorySize * 0.8) {
      recommendations.push('Cache size approaching limit - consider cleanup or compression');
    }
    
    // 查找重复数据
    const checksums = new Map<string, string[]>();
    let duplicateCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      const checksum = entry.metadata.checksum;
      if (!checksums.has(checksum)) {
        checksums.set(checksum, []);
      }
      checksums.get(checksum)!.push(key);
    }
    
    for (const [checksum, keys] of checksums.entries()) {
      if (keys.length > 1) {
        duplicateCount += keys.length - 1;
      }
    }
    
    // 查找未充分利用的数据
    const underutilizedCount = Array.from(this.cache.values())
      .filter(entry => entry.metadata.useCount < 2).length;
    
    if (duplicateCount > 0) {
      recommendations.push(`Found ${duplicateCount} duplicate entries - consider deduplication`);
    }
    
    if (underutilizedCount > statistics.totalEntries * 0.3) {
      recommendations.push('Many entries are rarely used - consider shorter TTL');
    }
    
    const potentialSavings = duplicateCount + underutilizedCount;
    
    return {
      statistics,
      recommendations,
      optimization: {
        potentialSavings,
        duplicateCount,
        underutilizedCount
      }
    };
  }
  
  /**
   * 创建数据变形（用于生成相似但不同的测试数据）
   */
  private static createVariation(template: any, index: number): any {
    if (typeof template !== 'object' || template === null) {
      return template;
    }
    
    const variation = JSON.parse(JSON.stringify(template));
    
    // 对不同类型的数据应用变形策略
    if (variation.name) {
      variation.name = `${variation.name}_${index}`;
    }
    
    if (variation.complexity && typeof variation.complexity === 'number') {
      variation.complexity = Math.max(0, variation.complexity + (index % 5) - 2);
    }
    
    if (variation.args && Array.isArray(variation.args)) {
      variation.args = variation.args.map((arg: string) => 
        arg.includes('${index}') ? arg.replace('${index}', index.toString()) : arg
      );
    }
    
    if (variation.files && Array.isArray(variation.files)) {
      variation.files = variation.files.map((file: any) => ({
        ...file,
        path: file.path.replace(/(\d+)/, index.toString())
      }));
    }
    
    return variation;
  }
  
  /**
   * 计算字符串相似性
   */
  private static calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) return 1.0;
    
    const editDistance = this.levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }
  
  /**
   * 计算编辑距离
   */
  private static levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null)
      .map(() => Array(str1.length + 1).fill(null));
    
    for (let i = 0; i <= str1.length; i++) matrix[0]![i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j]![0] = j;
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j]![i] = Math.min(
          matrix[j]![i - 1]! + 1,     // deletion
          matrix[j - 1]![i]! + 1,     // insertion
          matrix[j - 1]![i - 1]! + indicator // substitution
        );
      }
    }
    
    return matrix[str2.length]![str1.length]!;
  }
  
  /**
   * 从缓存获取数据
   */
  private static getCachedData(key: string): any | null {
    if (!this.config.enableCaching) return null;
    
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    // 检查是否过期
    const now = Date.now();
    const ttlMs = this.config.ttlMinutes * 60 * 1000;
    
    if (now - entry.metadata.lastUsed > ttlMs) {
      this.cache.delete(key);
      return null;
    }
    
    // 更新使用统计
    this.updateUsageStats(key);
    
    return entry.data;
  }
  
  /**
   * 设置缓存数据
   */
  private static setCachedData(
    key: string,
    data: any,
    type: TestDataCacheEntry['type'],
    tags: string[]
  ): void {
    if (!this.config.enableCaching) return;
    
    const now = Date.now();
    const checksum = this.calculateChecksum(data);
    
    const entry: TestDataCacheEntry = {
      id: key,
      type,
      data,
      metadata: {
        created: now,
        lastUsed: now,
        useCount: 1,
        tags,
        checksum
      }
    };
    
    this.cache.set(key, entry);
    
    // 检查缓存大小限制
    if (this.cache.size > this.config.maxCacheSize) {
      this.optimizeMemoryUsage();
    }
  }
  
  /**
   * 更新使用统计
   */
  private static updateUsageStats(key: string): void {
    const entry = this.cache.get(key);
    if (entry) {
      entry.metadata.lastUsed = Date.now();
      entry.metadata.useCount++;
    }
  }
  
  /**
   * 计算数据校验和
   */
  private static calculateChecksum(data: any): string {
    const str = JSON.stringify(data);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString(36);
  }
  
  /**
   * 计算缓存大小
   */
  private static calculateCacheSize(): number {
    let size = 0;
    
    for (const entry of this.cache.values()) {
      size += JSON.stringify(entry.data).length * 2; // 估算字符串字节数
    }
    
    return size;
  }
  
  /**
   * 获取统计信息
   */
  private static getStatistics(): TestDataStatistics {
    const byType: Record<string, number> = {};
    const byTags: Record<string, number> = {};
    const useCountEntries: Array<{ id: string; useCount: number }> = [];
    const recentEntries: Array<{ id: string; lastUsed: number }> = [];
    
    for (const entry of this.cache.values()) {
      // 按类型统计
      byType[entry.type] = (byType[entry.type] || 0) + 1;
      
      // 按标签统计
      entry.metadata.tags.forEach(tag => {
        byTags[tag] = (byTags[tag] || 0) + 1;
      });
      
      // 使用次数和最近使用时间
      useCountEntries.push({ id: entry.id, useCount: entry.metadata.useCount });
      recentEntries.push({ id: entry.id, lastUsed: entry.metadata.lastUsed });
    }
    
    // 排序获取最常用和最近使用的条目
    const mostUsedEntries = useCountEntries
      .sort((a, b) => b.useCount - a.useCount)
      .slice(0, 10);
    
    const sortedRecentEntries = recentEntries
      .sort((a, b) => b.lastUsed - a.lastUsed)
      .slice(0, 10);
    
    const totalRequests = this.cacheHits + this.cacheMisses;
    const cacheHitRate = totalRequests > 0 ? this.cacheHits / totalRequests : 0;
    
    return {
      totalEntries: this.cache.size,
      byType,
      byTags,
      cacheHitRate,
      mostUsedEntries,
      recentEntries: sortedRecentEntries,
      storageSize: this.calculateCacheSize()
    };
  }
  
  /**
   * 加载持久化缓存
   */
  private static loadPersistedCache(): void {
    try {
      if (existsSync(this.cacheFile)) {
        const data = readFileSync(this.cacheFile, 'utf8');
        const cached = JSON.parse(data);
        
        for (const [key, entry] of Object.entries(cached)) {
          this.cache.set(key, entry as TestDataCacheEntry);
        }
        
        console.log(`Loaded ${this.cache.size} cached test data entries`);
      }
    } catch (error) {
      console.warn('Failed to load persisted cache:', error);
    }
  }
  
  /**
   * 持久化缓存到磁盘
   */
  private static persistCache(): void {
    if (!this.config.persistToDisk) return;
    
    try {
      const data = Object.fromEntries(this.cache.entries());
      writeFileSync(this.cacheFile, JSON.stringify(data, null, 2));
      
      // 保存指标
      const metrics = {
        cacheHits: this.cacheHits,
        cacheMisses: this.cacheMisses,
        timestamp: Date.now(),
        statistics: this.getStatistics()
      };
      
      writeFileSync(this.metricsFile, JSON.stringify(metrics, null, 2));
    } catch (error) {
      console.warn('Failed to persist cache:', error);
    }
  }
  
  /**
   * 安排定期清理
   */
  private static scheduleCleanup(): void {
    const cleanupInterval = Math.max(this.config.ttlMinutes * 60 * 1000 / 4, 60000); // 至少1分钟
    
    setInterval(() => {
      this.optimizeMemoryUsage();
      
      if (this.config.persistToDisk) {
        this.persistCache();
      }
    }, cleanupInterval);
  }
  
  /**
   * 清理所有缓存
   */
  static clearCache(): void {
    this.cache.clear();
    this.cacheHits = 0;
    this.cacheMisses = 0;
  }
  
  /**
   * 获取缓存配置
   */
  static getConfiguration(): ReuseConfiguration {
    return { ...this.config };
  }
  
  /**
   * 更新配置
   */
  static updateConfiguration(updates: Partial<ReuseConfiguration>): void {
    this.config = { ...this.config, ...updates };
  }
}

/**
 * 便捷的数据创建工厂
 */
export class OptimizedDataFactory {
  /**
   * 创建优化的固件批次
   */
  static async createOptimizedFixtures(
    baseName: string,
    count: number,
    complexity: 'basic' | 'intermediate' | 'advanced' = 'intermediate'
  ): Promise<TestFixture[]> {
    return TestDataOptimizer.createBatchData(
      `fixture-${baseName}`,
      count,
      (index) => MockDataGenerator.generateTestFixturePackage(`${baseName}-${index}`),
      {
        reusePattern: 'template',
        tags: ['generated', complexity, baseName]
      }
    );
  }
  
  /**
   * 创建优化的测试场景
   */
  static async createOptimizedScenarios(
    baseName: string,
    count: number,
    scenarioType: 'success' | 'error' | 'performance' = 'success'
  ): Promise<TestScenario[]> {
    return TestDataOptimizer.createBatchData(
      `scenario-${baseName}`,
      count,
      (index) => MockDataGenerator.generateCLITestScenario({
        name: `${baseName} Test ${index + 1}`,
        expectedExitCode: scenarioType === 'error' ? 1 : 0
      }),
      {
        reusePattern: 'template',
        tags: ['scenario', scenarioType, baseName]
      }
    );
  }
  
  /**
   * 获取或创建性能测试数据
   */
  static async getOrCreatePerformanceData(
    testName: string,
    complexity: 'light' | 'moderate' | 'heavy'
  ): Promise<any> {
    return TestDataOptimizer.getOrCreateFixture(
      `perf-${testName}-${complexity}`,
      () => MockDataGenerator.generateStressTestData(complexity),
      ['performance', complexity, testName]
    );
  }
}

// 初始化优化器
TestDataOptimizer.initialize();