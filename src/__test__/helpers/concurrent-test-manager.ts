/**
 * 并发测试管理器
 * 优化测试执行调度，减少资源竞争
 */

import { CLITestingUtils } from './cli-testing-utils';
import type { CLITestResult, CLITestConfig } from './cli-testing-utils';

export interface ConcurrentTestConfig {
  maxConcurrency: number;          // 最大并发数
  resourceLimit: {
    maxMemoryMB: number;           // 最大内存限制（MB）
    maxProcesses: number;          // 最大进程数
  };
  scheduling: {
    batchSize: number;             // 批次大小
    delayBetweenBatches: number;   // 批次间延迟（毫秒）
    priorityLevels: number;        // 优先级层数
  };
  monitoring: {
    collectMetrics: boolean;       // 是否收集指标
    logPerformance: boolean;       // 是否记录性能日志
  };
}

export interface TestTask {
  id: string;
  priority: number;                // 优先级 (1-10, 10最高)
  command: string;
  args: string[];
  config?: Partial<CLITestConfig>;
  expectedDuration?: number;       // 预期执行时间（毫秒）
  retryCount?: number;            // 重试次数
}

export interface TestResult {
  taskId: string;
  success: boolean;
  duration: number;
  startTime: number;
  endTime: number;
  instance?: CLITestResult;
  error?: Error;
  retries: number;
}

export interface ConcurrencyMetrics {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageDuration: number;
  maxConcurrency: number;
  actualConcurrency: number;
  memoryPeakMB: number;
  totalDuration: number;
  throughput: number;              // 任务/秒
}

/**
 * 资源监控器
 */
class ResourceMonitor {
  private memoryPeak = 0;
  private activeTasks = new Set<string>();
  private startTime = 0;

  start(): void {
    this.startTime = Date.now();
    this.memoryPeak = 0;
    this.activeTasks.clear();
  }

  addTask(taskId: string): void {
    this.activeTasks.add(taskId);
    this.updateMemoryPeak();
  }

  removeTask(taskId: string): void {
    this.activeTasks.delete(taskId);
  }

  private updateMemoryPeak(): void {
    const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024;
    this.memoryPeak = Math.max(this.memoryPeak, currentMemory);
  }

  getCurrentConcurrency(): number {
    return this.activeTasks.size;
  }

  getMemoryPeakMB(): number {
    this.updateMemoryPeak();
    return this.memoryPeak;
  }

  getDuration(): number {
    return Date.now() - this.startTime;
  }

  isWithinLimits(config: ConcurrentTestConfig): boolean {
    return (
      this.getCurrentConcurrency() < config.resourceLimit.maxProcesses &&
      this.getMemoryPeakMB() < config.resourceLimit.maxMemoryMB
    );
  }
}

/**
 * 并发测试管理器主类
 */
export class ConcurrentTestManager {
  private static readonly DEFAULT_CONFIG: ConcurrentTestConfig = {
    maxConcurrency: 4,
    resourceLimit: {
      maxMemoryMB: 200,
      maxProcesses: 10
    },
    scheduling: {
      batchSize: 3,
      delayBetweenBatches: 100,
      priorityLevels: 3
    },
    monitoring: {
      collectMetrics: true,
      logPerformance: false
    }
  };

  private config: ConcurrentTestConfig;
  private resourceMonitor = new ResourceMonitor();
  private runningTasks = new Map<string, Promise<TestResult>>();
  private completedResults: TestResult[] = [];
  private isRunning = false;

  constructor(config: Partial<ConcurrentTestConfig> = {}) {
    this.config = { ...ConcurrentTestManager.DEFAULT_CONFIG, ...config };
  }

  /**
   * 执行并发测试任务
   */
  async executeTasks(tasks: TestTask[]): Promise<{
    results: TestResult[];
    metrics: ConcurrencyMetrics;
  }> {
    if (this.isRunning) {
      throw new Error('测试管理器已在运行中，请等待当前任务完成');
    }

    this.isRunning = true;
    this.resourceMonitor.start();
    this.completedResults = [];
    this.runningTasks.clear();

    try {
      // 按优先级排序任务
      const sortedTasks = [...tasks].sort((a, b) => (b.priority || 5) - (a.priority || 5));
      
      // 分批执行任务
      const batches = this.createBatches(sortedTasks);
      
      for (const batch of batches) {
        await this.executeBatch(batch);
        
        // 批次间延迟以减少资源竞争
        if (this.config.scheduling.delayBetweenBatches > 0) {
          await this.delay(this.config.scheduling.delayBetweenBatches);
        }
      }

      // 等待所有任务完成
      await this.waitForAllTasks();

      const metrics = this.calculateMetrics();
      
      if (this.config.monitoring.logPerformance) {
        this.logPerformanceReport(metrics);
      }

      return {
        results: this.completedResults,
        metrics
      };
    } finally {
      this.isRunning = false;
      await CLITestingUtils.cleanupAll();
    }
  }

  /**
   * 创建任务批次
   */
  private createBatches(tasks: TestTask[]): TestTask[][] {
    const batches: TestTask[][] = [];
    const batchSize = this.config.scheduling.batchSize;
    
    for (let i = 0; i < tasks.length; i += batchSize) {
      batches.push(tasks.slice(i, i + batchSize));
    }
    
    return batches;
  }

  /**
   * 执行单个批次
   */
  private async executeBatch(batch: TestTask[]): Promise<void> {
    const batchPromises: Promise<void>[] = [];

    for (const task of batch) {
      // 检查资源限制
      if (!this.canStartNewTask()) {
        // 等待一些任务完成
        await this.waitForTaskSlot();
      }

      const taskPromise = this.executeTask(task);
      this.runningTasks.set(task.id, taskPromise);
      
      // 添加完成处理
      batchPromises.push(
        taskPromise.then(result => {
          this.completedResults.push(result);
          this.runningTasks.delete(task.id);
          this.resourceMonitor.removeTask(task.id);
        })
      );
    }

    // 等待批次中所有任务完成
    await Promise.allSettled(batchPromises);
  }

  /**
   * 执行单个任务
   */
  private async executeTask(task: TestTask): Promise<TestResult> {
    const startTime = Date.now();
    this.resourceMonitor.addTask(task.id);
    
    let retries = 0;
    const maxRetries = task.retryCount || 0;

    while (retries <= maxRetries) {
      try {
        const instance = await CLITestingUtils.renderCLI(
          task.command,
          task.args,
          task.config || {}
        );

        const exitCode = await instance.waitForExit();
        const endTime = Date.now();
        
        await CLITestingUtils.cleanup(instance);

        return {
          taskId: task.id,
          success: exitCode === 0,
          duration: endTime - startTime,
          startTime,
          endTime,
          instance,
          retries
        };
      } catch (error) {
        retries++;
        
        if (retries <= maxRetries) {
          // 重试前等待一小段时间
          await this.delay(Math.min(1000 * retries, 5000));
          continue;
        }

        // 最终失败
        const endTime = Date.now();
        return {
          taskId: task.id,
          success: false,
          duration: endTime - startTime,
          startTime,
          endTime,
          error: error instanceof Error ? error : new Error(String(error)),
          retries: retries - 1
        };
      }
    }

    throw new Error('不应该到达这里');
  }

  /**
   * 检查是否可以启动新任务
   */
  private canStartNewTask(): boolean {
    return (
      this.runningTasks.size < this.config.maxConcurrency &&
      this.resourceMonitor.isWithinLimits(this.config)
    );
  }

  /**
   * 等待任务槽位可用
   */
  private async waitForTaskSlot(): Promise<void> {
    while (!this.canStartNewTask() && this.runningTasks.size > 0) {
      // 等待最快完成的任务
      const runningTaskPromises = Array.from(this.runningTasks.values());
      if (runningTaskPromises.length > 0) {
        await Promise.race(runningTaskPromises);
      }
      
      // 短暂延迟以避免忙等待
      await this.delay(50);
    }
  }

  /**
   * 等待所有任务完成
   */
  private async waitForAllTasks(): Promise<void> {
    while (this.runningTasks.size > 0) {
      const runningTaskPromises = Array.from(this.runningTasks.values());
      await Promise.allSettled(runningTaskPromises);
    }
  }

  /**
   * 计算性能指标
   */
  private calculateMetrics(): ConcurrencyMetrics {
    const totalTasks = this.completedResults.length;
    const completedTasks = this.completedResults.filter(r => r.success).length;
    const failedTasks = totalTasks - completedTasks;
    
    const durations = this.completedResults.map(r => r.duration);
    const averageDuration = durations.length > 0 
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length 
      : 0;
    
    const totalDuration = this.resourceMonitor.getDuration();
    const throughput = totalTasks > 0 ? (totalTasks / totalDuration) * 1000 : 0;

    return {
      totalTasks,
      completedTasks,
      failedTasks,
      averageDuration,
      maxConcurrency: this.config.maxConcurrency,
      actualConcurrency: Math.max(...this.completedResults.map(() => 
        this.resourceMonitor.getCurrentConcurrency()
      ), 0),
      memoryPeakMB: this.resourceMonitor.getMemoryPeakMB(),
      totalDuration,
      throughput
    };
  }

  /**
   * 记录性能报告
   */
  private logPerformanceReport(metrics: ConcurrencyMetrics): void {
    console.log('\n=== 并发测试性能报告 ===');
    console.log(`总任务数: ${metrics.totalTasks}`);
    console.log(`成功任务: ${metrics.completedTasks}`);
    console.log(`失败任务: ${metrics.failedTasks}`);
    console.log(`平均执行时间: ${metrics.averageDuration.toFixed(0)}ms`);
    console.log(`最大并发数: ${metrics.maxConcurrency}`);
    console.log(`内存峰值: ${metrics.memoryPeakMB.toFixed(1)}MB`);
    console.log(`总执行时间: ${metrics.totalDuration.toFixed(0)}ms`);
    console.log(`吞吐量: ${metrics.throughput.toFixed(2)} 任务/秒`);
    console.log('========================\n');
  }

  /**
   * 延迟工具函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取正在运行的任务状态
   */
  getRunningTasksStatus(): {
    count: number;
    tasks: string[];
    memoryUsageMB: number;
  } {
    return {
      count: this.runningTasks.size,
      tasks: Array.from(this.runningTasks.keys()),
      memoryUsageMB: this.resourceMonitor.getMemoryPeakMB()
    };
  }

  /**
   * 强制停止所有任务
   */
  async forceStop(): Promise<void> {
    this.isRunning = false;
    await CLITestingUtils.forceCleanupAll();
    this.runningTasks.clear();
  }
}