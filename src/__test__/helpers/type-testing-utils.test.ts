import { describe, test, expect } from "vitest";
import {
  expectType,
  TypeValidator,
  TypeSafeTestFactory,
  AdvancedTypeValidator,
  TypeSafeTestHelper,
  type TypeGuardValidation,
  type GenericConstraintTest,
  type ConditionalTypeValidator
} from "../helpers/type-testing-utils";
import type { FunctionResult, AnalysisResult, FileResult } from "../../core/types";
import type { AnalysisContext } from "../../engine/types";
import type { Node } from '@swc/core';
import { TestUtils, RuleTestUtils } from "../helpers/test-utils";

describe('Type Safety Testing Tools', () => {
  describe('expectType function', () => {
    test('should create TypeValidator instance with correct type', () => {
      const validator = expectType<string>();
      expect(validator).toBeInstanceOf(TypeValidator);
    });

    test('should work with custom test name', () => {
      const validator = expectType<number>('CustomNumber');
      expect(validator.generateReport()).toContain('CustomNumber');
    });
  });

  describe('TypeValidator class', () => {
    test('should validate runtime type acceptance', () => {
      const validator = expectType<string>();
      
      expect(() => {
        validator.toAccept('hello world');
      }).not.toThrow();
      
      expect(() => {
        validator.toAccept('');
      }).not.toThrow();
    });

    test('should document type rejection', () => {
      const validator = expectType<string>();
      
      // toReject 主要用于文档化，不会抛出错误
      expect(() => {
        validator.toReject(123, 'numbers should be rejected');
      }).not.toThrow();
    });

    test('should validate compile-time type extensions', () => {
      // 这些测试主要验证编译时类型约束
      const stringValidator = expectType<string>();
      const numberValidator = expectType<number>();
      
      // 这些调用验证编译时类型约束
      expect(() => {
        stringValidator.toExtend<string>();
        stringValidator.toBeCompatibleWith<string>();
        numberValidator.toBeAssignableTo<number>();
      }).not.toThrow();
    });

    test('should validate structure matching', async () => {
      const validator = expectType<object>('StructureValidator');
      
      const testObject = {
        name: 'test',
        complexity: 10,
        active: true
      };

      // 测试结构匹配不抛出错误
      await expect(
        validator.toMatchStructure(testObject, {
          name: 'string',
          complexity: 'number',
          active: 'boolean'
        })
      ).resolves.toBeUndefined();
    });

    test('should reject invalid structure', async () => {
      const validator = expectType<object>('InvalidStructureValidator');
      
      const testObject = {
        name: 'test',
        complexity: 'invalid', // 应该是 number
        active: true
      };

      await expect(
        validator.toMatchStructure(testObject, {
          name: 'string',
          complexity: 'number',
          active: 'boolean'
        })
      ).rejects.toThrow('complexity: expected number, got string');
    });

    test('should validate type guards correctly', async () => {
      const validator = expectType<string>('StringValidator');
      
      const stringGuard: TypeGuardValidation<string> = {
        name: 'isString',
        validator: (value: unknown): value is string => typeof value === 'string',
        positiveTests: ['hello', '', 'world', '123'],
        negativeTests: [123, null, undefined, {}, [], true]
      };

      const result = await validator.validateTypeGuard(stringGuard);
      
      expect(result.passed).toBe(true);
      expect(result.summary.totalTests).toBe(10); // 4 positive + 6 negative
      expect(result.summary.coverage).toBe(100);
      expect(result.failures).toHaveLength(0);
    });

    test('should detect type guard failures', async () => {
      const validator = expectType<number>('NumberValidator');
      
      const faultyGuard: TypeGuardValidation<number> = {
        name: 'isFaultyNumber',
        validator: (value: unknown): value is number => typeof value === 'string', // 故意错误的逻辑
        positiveTests: [1, 2, 3],
        negativeTests: ['hello', null, undefined]
      };

      const result = await validator.validateTypeGuard(faultyGuard);
      
      expect(result.passed).toBe(false);
      expect(result.failures.length).toBeGreaterThan(0);
      expect(result.summary.coverage).toBeLessThan(100);
    });

    test('should validate conditional types', async () => {
      const validator = expectType<number>('ConditionalValidator');
      
      const conditionalValidator: ConditionalTypeValidator<number, string | boolean> = {
        condition: (value: number) => value > 0,
        trueType: (value: number) => `positive: ${value}`,
        falseType: (value: number) => value <= 0,
        description: 'Positive number conditional type'
      };

      const testInputs = [1, -1, 0, 5, -10];
      const result = await validator.validateConditionalType(conditionalValidator, testInputs);
      
      expect(result.passed).toBe(true);
      expect(result.summary.totalTests).toBe(5);
      expect(result.summary.coverage).toBe(100);
    });

    test('should validate generic constraints', async () => {
      const validator = expectType<string>('GenericConstraintValidator');
      
      const constraintTest: GenericConstraintTest<string> = {
        name: 'NonEmptyString',
        constraintDescription: 'String must not be empty',
        validInputs: ['hello', 'world', 'test'],
        invalidInputs: ['', null, undefined, 123],
        transformer: (input: string) => input.toUpperCase()
      };

      const result = await validator.validateGenericConstraints(constraintTest);
      
      expect(result.passed).toBe(true);
      expect(result.summary.totalTests).toBe(7); // 3 valid + 4 invalid
      expect(result.summary.coverage).toBe(100);
    });

    test('should generate comprehensive summary', async () => {
      const validator = expectType<string>('ComprehensiveTest');
      
      // 运行多个验证
      const stringGuard: TypeGuardValidation<string> = {
        name: 'isString',
        validator: (value: unknown): value is string => typeof value === 'string',
        positiveTests: ['test'],
        negativeTests: [123]
      };

      await validator.validateTypeGuard(stringGuard);
      
      const summary = validator.getSummary();
      
      expect(summary.totalValidations).toBe(1);
      expect(summary.passedValidations).toBe(1);
      expect(summary.overallSuccess).toBe(true);
      expect(summary.totalTests).toBe(2);
      expect(summary.averageCoverage).toBe(100);
    });

    test('should generate detailed report', async () => {
      const validator = expectType<string>('ReportTest');
      
      const stringGuard: TypeGuardValidation<string> = {
        name: 'isString',
        validator: (value: unknown): value is string => typeof value === 'string',
        positiveTests: ['test'],
        negativeTests: [123]
      };

      await validator.validateTypeGuard(stringGuard);
      
      const report = validator.generateReport();
      
      expect(report).toContain('Type Validation Report: ReportTest');
      expect(report).toContain('Overall Success: ✅');
      expect(report).toContain('Validations: 1/1 passed');
    });
  });

  describe('TypeSafeTestFactory', () => {
    test('should create function result validator', () => {
      const validator = TypeSafeTestFactory.createFunctionResultValidator();
      expect(validator).toBeInstanceOf(TypeValidator);
    });

    test('should create analysis result validator', () => {
      const validator = TypeSafeTestFactory.createAnalysisResultValidator();
      expect(validator).toBeInstanceOf(TypeValidator);
    });

    test('should generate type guard tests', async () => {
      const validExample: FunctionResult = TestUtils.createMockFunctionResult();
      const invalidExamples = [null, undefined, {}, 'string', 123];
      
      const guard = await TypeSafeTestFactory.generateTypeGuardTests(
        'FunctionResult',
        [validExample],
        invalidExamples
      );

      expect(guard.name).toBe('isFunctionResult');
      expect(guard.positiveTests).toHaveLength(1);
      expect(guard.negativeTests).toHaveLength(5);
      expect(typeof guard.validator).toBe('function');
    });

    test('should generate generic constraint tests', async () => {
      const validInputs = ['test1', 'test2'];
      const invalidInputs = [null, undefined, 123];
      
      const constraintTest = await TypeSafeTestFactory.generateGenericConstraintTests(
        'NonEmptyString',
        validInputs,
        invalidInputs
      );

      expect(constraintTest.name).toBe('NonEmptyString');
      expect(constraintTest.validInputs).toEqual(validInputs);
      expect(constraintTest.invalidInputs).toEqual(invalidInputs);
    });

    test('should create complexity test suite', async () => {
      const testSuite = await TypeSafeTestFactory.createComplexityTestSuite();
      
      expect(testSuite.functionResultTests).toBeDefined();
      expect(testSuite.analysisResultTests).toBeDefined();
      expect(testSuite.fileResultTests).toBeDefined();
      
      expect(testSuite.functionResultTests.name).toBe('isFunctionResult');
      expect(testSuite.analysisResultTests.name).toBe('isAnalysisResult');
      expect(testSuite.fileResultTests.name).toBe('isFileResult');
    });

    test('should create rule engine type tests', async () => {
      const ruleTests = await TypeSafeTestFactory.createRuleEngineTypeTests();
      
      expect(ruleTests.contextValidation).toBeDefined();
      expect(ruleTests.nodeValidation).toBeDefined();
      
      expect(ruleTests.contextValidation.name).toBe('isAnalysisContext');
      expect(ruleTests.nodeValidation.name).toBe('isNode');
    });
  });

  describe('AdvancedTypeValidator', () => {
    test('should validate structural compatibility', async () => {
      const source = { name: 'test', value: 123 };
      const targetValidator = (value: unknown): value is { name: string; value: number } => {
        return typeof value === 'object' && value !== null &&
               'name' in value && 'value' in value &&
               typeof (value as any).name === 'string' &&
               typeof (value as any).value === 'number';
      };

      const result = await AdvancedTypeValidator.validateStructuralCompatibility(
        source,
        targetValidator,
        'Object Compatibility Test'
      );

      expect(result.passed).toBe(true);
      expect(result.message).toBeUndefined();
      expect(result.matches).toHaveLength(1);
    });

    test('should validate type shapes', async () => {
      const mockFunctionResult = TestUtils.createMockFunctionResult();
      
      const result = await AdvancedTypeValidator.validateTypeShape(
        mockFunctionResult,
        {
          name: 'string',
          complexity: 'number',
          line: 'number',
          column: 'number',
          filePath: 'string'
        },
        'FunctionResult'
      );

      expect(result.passed).toBe(true);
      expect(result.message).toBeUndefined();
    });

    test('should detect shape validation failures', async () => {
      const invalidObject = { name: 'test', complexity: 'invalid' }; // complexity 应该是 number
      
      const result = await AdvancedTypeValidator.validateTypeShape(
        invalidObject,
        {
          name: 'string',
          complexity: 'number',
          line: 'number'
        },
        'InvalidObject'
      );

      expect(result.passed).toBe(false);
      expect(result.message).toContain('complexity: expected number, got string');
      expect(result.message).toContain('Missing required property: line');
    });

    test('should validate union types', async () => {
      const validResult = await AdvancedTypeValidator.validateUnionType(
        'test',
        ['string', 'number'],
        ['test', 'valid']
      );

      expect(validResult.passed).toBe(true);

      const invalidTypeResult = await AdvancedTypeValidator.validateUnionType(
        true as any,
        ['string', 'number']
      );

      expect(invalidTypeResult.passed).toBe(false);
      expect(invalidTypeResult.message).toContain('boolean not in allowed types');
    });

    test('should validate optional properties', async () => {
      const obj = { required1: 'test', optional1: 123 };
      
      const result = await AdvancedTypeValidator.validateOptionalProperties(
        obj,
        ['required1'], // 必需属性
        ['optional1', 'optional2'] // 可选属性
      );

      expect(result.passed).toBe(true);
    });

    test('should detect missing required properties', async () => {
      const obj = { optional1: 123 };
      
      const result = await AdvancedTypeValidator.validateOptionalProperties(
        obj,
        ['required1'], // 缺失的必需属性
        ['optional1']
      );

      expect(result.passed).toBe(false);
      expect(result.message).toContain('Missing required property: required1');
    });
  });

  describe('TypeSafeTestHelper', () => {
    test('should validate complexity results - FunctionResult', async () => {
      const mockResult = TestUtils.createMockFunctionResult();
      
      const result = await TypeSafeTestHelper.validateComplexityResults(
        mockResult,
        'FunctionResult'
      );

      expect(result.passed).toBe(true);
    });

    test('should validate complexity results - FunctionResult array', async () => {
      const mockResults = [
        TestUtils.createMockFunctionResult(),
        TestUtils.createMockFunctionResult({ name: 'test2', complexity: 5 })
      ];
      
      const result = await TypeSafeTestHelper.validateComplexityResults(
        mockResults,
        'FunctionResult[]'
      );

      expect(result.passed).toBe(true);
    });

    test('should validate complexity results - AnalysisResult', async () => {
      const mockResult = TestUtils.createMockAnalysisResult();
      
      const result = await TypeSafeTestHelper.validateComplexityResults(
        mockResult,
        'AnalysisResult'
      );

      expect(result.passed).toBe(true);
    });

    test('should validate configuration types', async () => {
      const config = {
        threshold: 10,
        includeDetails: true,
        outputFormat: 'json'
      };

      const schema = {
        threshold: { type: 'number', required: true },
        includeDetails: { type: 'boolean', required: false },
        outputFormat: { 
          type: 'string', 
          required: false,
          validator: (v: any) => ['json', 'text', 'html'].includes(v)
        }
      };

      const result = await TypeSafeTestHelper.validateConfigurationTypes(config, schema);

      expect(result.passed).toBe(true);
    });

    test('should detect configuration validation failures', async () => {
      const config = {
        threshold: 'invalid', // 应该是 number
        outputFormat: 'xml' // 无效值
      };

      const schema = {
        threshold: { type: 'number', required: true },
        outputFormat: { 
          type: 'string', 
          validator: (v: any) => ['json', 'text', 'html'].includes(v)
        }
      };

      const result = await TypeSafeTestHelper.validateConfigurationTypes(config, schema);

      expect(result.passed).toBe(false);
      expect(result.message).toContain('threshold: expected number, got string');
      expect(result.message).toContain('outputFormat: failed custom validation');
    });

    test('should create mock data validator', async () => {
      const mockCreator = () => TestUtils.createMockFunctionResult();
      const typeValidator = (value: unknown): value is FunctionResult => {
        return typeof value === 'object' && value !== null &&
               'name' in value && 'complexity' in value &&
               typeof (value as any).name === 'string' &&
               typeof (value as any).complexity === 'number';
      };

      const validator = TypeSafeTestHelper.createMockDataValidator(
        'FunctionResult',
        mockCreator,
        typeValidator
      );

      const result = await validator();
      expect(result.passed).toBe(true);
    });
  });

  describe('Integration with existing test tools', () => {
    test('should work with TestUtils mock data', async () => {
      const validator = expectType<FunctionResult>('TestUtilsIntegration');
      
      const mockResult = TestUtils.createMockFunctionResult({
        name: 'integrationTest',
        complexity: 15
      });

      expect(() => {
        validator.toAccept(mockResult);
      }).not.toThrow();

      // 验证编译时类型约束
      expectType<FunctionResult>().toBeAssignableTo<{ 
        name: string; 
        complexity: number; 
        line: number; 
        column: number; 
        filePath: string; 
      }>();

      // 验证结构匹配
      await expect(
        validator.toMatchStructure(mockResult, {
          name: 'string',
          complexity: 'number',
          line: 'number',
          column: 'number',
          filePath: 'string'
        })
      ).resolves.toBeUndefined();
    });

    test('should work with RuleTestUtils context', async () => {
      const validator = expectType<AnalysisContext>('RuleTestUtilsIntegration');
      
      const mockContext = RuleTestUtils.createTestContext({
        filePath: 'integration-test.ts',
        nestingLevel: 3
      });

      expect(() => {
        validator.toAccept(mockContext);
      }).not.toThrow();

      // 验证上下文结构
      await expect(
        validator.toMatchStructure(mockContext, {
          filePath: 'string',
          nestingLevel: 'number',
          functionName: 'string',
          jsxMode: 'string',
          metrics: (v) => typeof v === 'object' && v !== null
        })
      ).resolves.toBeUndefined();
    });

    test('should validate complete analysis workflow types', async () => {
      // 模拟完整的分析工作流程
      const code = TestUtils.createTestCode('if (condition) { doSomething(); }');
      const results = await TestUtils.analyzeCode(code, 'workflow-test.ts');
      
      // 验证结果类型
      const validator = expectType<FunctionResult[]>('WorkflowValidation');
      validator.toAccept(results);

      // 使用高级验证器验证每个结果的结构
      for (const result of results) {
        const structureValidation = await AdvancedTypeValidator.validateTypeShape(
          result,
          {
            name: 'string',
            complexity: 'number',
            line: 'number',
            column: 'number',
            filePath: 'string'
          },
          'WorkflowFunctionResult'
        );
        
        expect(structureValidation.passed).toBe(true);
      }
    });

    test('should demonstrate TypeScript type safety enhancement patterns', async () => {
      // 演示设计文档中提到的类型安全模式

      // 1. 对象池类型安全测试
      interface ReadonlyDetailStep {
        readonly line: number;
        readonly column: number;
        readonly increment: number;
        readonly cumulative: number;
        readonly ruleId: string;
        readonly description: string;
        readonly nestingLevel: number;
        readonly context: string;
      }

      interface InternalDetailStep {
        line: number;
        column: number;
        increment: number;
        cumulative: number;
        ruleId: string;
        description: string;
        nestingLevel: number;
        context: string;
      }

      // 验证只读接口可以从内部接口赋值
      expectType<InternalDetailStep>().toBeAssignableTo<ReadonlyDetailStep>();
      
      // 2. 插件系统类型安全测试
      interface PluginEventHandler<T> {
        (data: T): void | Promise<void>;
      }

      interface PluginEventDataMap {
        'plugin:loaded': { pluginId: string; plugin: any; timestamp: number };
        'plugin:error': { pluginId: string; error: Error; timestamp: number };
      }

      // 验证事件处理器类型约束
      expectType<PluginEventHandler<PluginEventDataMap['plugin:loaded']>>()
        .toBeAssignableTo<(data: PluginEventDataMap['plugin:loaded']) => void>();

      // 3. 异步引擎接口完整性测试
      interface RuleRegistry {
        getRulesForNode(node: any): any[];
        getRulesByPriority(priority: number): any[];
        getRulesByCategory(category: string): any[];
        getAllRules(): any[];
        hasRule(ruleId: string): boolean;
        getRule(ruleId: string): any | null;
      }

      interface AsyncRuleEngine extends RuleRegistry {
        registerRule(rule: any): void;
        unregisterRule(ruleId: string): void;
      }

      // 验证异步引擎扩展了规则注册表
      expectType<AsyncRuleEngine>().toExtend<RuleRegistry>();

      // 4. 版本管理类型精确性测试
      interface ValidatedVersionParts {
        readonly major: number;
        readonly minor: number;
        readonly patch: number;
        readonly prerelease: string | null;
        readonly build: string | null;
        readonly raw: string;
      }

      type VersionComparisonResult = -1 | 0 | 1;

      // 验证版本比较结果类型约束
      expectType<VersionComparisonResult>().toBeAssignableTo<-1 | 0 | 1>();

      const mockVersionParts: ValidatedVersionParts = {
        major: 1,
        minor: 2,
        patch: 3,
        prerelease: null,
        build: null,
        raw: '1.2.3'
      };

      await expectType<ValidatedVersionParts>('VersionParts').toMatchStructure(mockVersionParts, {
        major: 'number',
        minor: 'number',
        patch: 'number',
        prerelease: (v) => v === null || typeof v === 'string',
        build: (v) => v === null || typeof v === 'string',
        raw: 'string'
      });
    });
  });

  describe('Performance and memory tracking', () => {
    test('should track performance metrics during validation', async () => {
      const validator = expectType<string>('PerformanceTest');
      
      const stringGuard: TypeGuardValidation<string> = {
        name: 'isString',
        validator: (value: unknown): value is string => typeof value === 'string',
        positiveTests: Array.from({ length: 100 }, (_, i) => `test${i}`),
        negativeTests: Array.from({ length: 100 }, (_, i) => i)
      };

      const result = await validator.validateTypeGuard(stringGuard);
      
      expect(result.performance.executionTime).toBeGreaterThan(0);
      expect(result.performance.memoryUsage).toBeDefined();
      expect(result.summary.totalTests).toBe(200);
    });

    test('should handle large datasets efficiently', async () => {
      const validator = expectType<number>('LargeDatasetTest');
      
      const largePositiveTests = Array.from({ length: 1000 }, (_, i) => i);
      const largeNegativeTests = Array.from({ length: 1000 }, (_, i) => `string${i}`);
      
      const numberGuard: TypeGuardValidation<number> = {
        name: 'isNumber',
        validator: (value: unknown): value is number => typeof value === 'number',
        positiveTests: largePositiveTests,
        negativeTests: largeNegativeTests
      };

      const startTime = performance.now();
      const result = await validator.validateTypeGuard(numberGuard);
      const endTime = performance.now();
      
      expect(result.passed).toBe(true);
      expect(result.summary.totalTests).toBe(2000);
      expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
    });
  });
});