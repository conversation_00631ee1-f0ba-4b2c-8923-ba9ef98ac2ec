import type { TestFixture } from './fixture-manager';
import type { CLITestResult, TestScenario } from './cli-testing-utils';
import type { FunctionResult, FileResult, AnalysisResult } from '../../core/types';
import { MockDataGenerator } from './test-utils';

/**
 * 数据一致性验证结果
 */
export interface ConsistencyValidationResult {
  readonly passed: boolean;
  readonly validationType: string;
  readonly message: string;
  readonly details?: any;
  readonly suggestions?: string[];
}

/**
 * 测试数据验证选项
 */
export interface ValidationOptions {
  readonly strictMode?: boolean;           // 严格模式验证
  readonly tolerancePercent?: number;      // 允许的误差百分比
  readonly ignoredFields?: string[];       // 忽略的字段
  readonly customValidators?: Record<string, (actual: any, expected: any) => boolean>;
}

/**
 * 数据一致性验证器
 * 确保测试数据在新 CLI 测试框架下保持一致性
 */
export class DataConsistencyValidator {
  private static readonly DEFAULT_TOLERANCE = 5; // 5% 容差
  
  /**
   * 验证固件数据的一致性
   */
  static async validateFixtureConsistency(
    fixture: TestFixture,
    actualResults: any,
    options: ValidationOptions = {}
  ): Promise<ConsistencyValidationResult[]> {
    const results: ConsistencyValidationResult[] = [];
    
    if (!fixture.expectedResults) {
      return [{
        passed: true,
        validationType: 'fixture-skip',
        message: 'No expected results defined in fixture - validation skipped'
      }];
    }
    
    // 验证汇总数据
    if (fixture.expectedResults.summary && actualResults.summary) {
      const summaryValidation = this.validateSummaryData(
        actualResults.summary,
        fixture.expectedResults.summary,
        options
      );
      results.push(summaryValidation);
    }
    
    // 验证文件级数据
    if (fixture.expectedResults.files && actualResults.results) {
      const fileValidation = this.validateFileResults(
        actualResults.results,
        fixture.expectedResults.files,
        options
      );
      results.push(fileValidation);
    }
    
    // 验证性能数据
    if (fixture.expectedResults.performance && actualResults.performance) {
      const performanceValidation = this.validatePerformanceData(
        actualResults.performance,
        fixture.expectedResults.performance,
        options
      );
      results.push(performanceValidation);
    }
    
    return results;
  }
  
  /**
   * 验证 CLI 输出的一致性
   */
  static async validateCLIOutputConsistency(
    cliResult: CLITestResult,
    expectedScenario: TestScenario,
    options: ValidationOptions = {}
  ): Promise<ConsistencyValidationResult[]> {
    const results: ConsistencyValidationResult[] = [];
    
    // 验证退出码
    const exitCodeValidation = await this.validateExitCode(cliResult, expectedScenario);
    results.push(exitCodeValidation);
    
    // 验证输出内容
    if (expectedScenario.expectedOutput && expectedScenario.expectedOutput.length > 0) {
      const outputValidation = await this.validateOutputContent(
        cliResult,
        expectedScenario.expectedOutput,
        options
      );
      results.push(outputValidation);
    }
    
    // 验证输出格式
    const formatValidation = await this.validateOutputFormat(cliResult, options);
    results.push(formatValidation);
    
    return results;
  }
  
  /**
   * 验证汇总数据一致性
   */
  private static validateSummaryData(
    actual: any,
    expected: any,
    options: ValidationOptions
  ): ConsistencyValidationResult {
    const tolerance = options.tolerancePercent || this.DEFAULT_TOLERANCE;
    const issues: string[] = [];
    
    // 检查关键字段
    const keyFields = ['totalFiles', 'totalFunctions', 'avgComplexity', 'maxComplexity'];
    
    for (const field of keyFields) {
      if (options.ignoredFields?.includes(field)) continue;
      
      const actualValue = actual[field];
      const expectedValue = expected[field];
      
      if (actualValue === undefined && expectedValue !== undefined) {
        issues.push(`Missing field: ${field}`);
        continue;
      }
      
      if (typeof actualValue === 'number' && typeof expectedValue === 'number') {
        const diff = Math.abs(actualValue - expectedValue);
        const allowedDiff = expectedValue * (tolerance / 100);
        
        if (diff > allowedDiff) {
          issues.push(`${field}: expected ~${expectedValue}, got ${actualValue} (diff: ${diff.toFixed(2)})`);
        }
      } else if (actualValue !== expectedValue) {
        issues.push(`${field}: expected ${expectedValue}, got ${actualValue}`);
      }
    }
    
    const passed = issues.length === 0;
    
    return {
      passed,
      validationType: 'summary-validation',
      message: passed ? 'Summary data validation passed' : `Summary validation failed: ${issues.join(', ')}`,
      details: { actual, expected, issues },
      suggestions: passed ? undefined : [
        'Check if the analysis algorithm has changed',
        'Verify that test data reflects current expectations',
        'Consider updating fixture expected results'
      ]
    };
  }
  
  /**
   * 验证文件结果一致性
   */
  private static validateFileResults(
    actualFiles: FileResult[],
    expectedFiles: Array<{ path: string; complexity: number; functionCount: number }>,
    options: ValidationOptions
  ): ConsistencyValidationResult {
    const tolerance = options.tolerancePercent || this.DEFAULT_TOLERANCE;
    const issues: string[] = [];
    
    // 检查文件数量
    if (actualFiles.length !== expectedFiles.length) {
      issues.push(`File count mismatch: expected ${expectedFiles.length}, got ${actualFiles.length}`);
    }
    
    // 按路径匹配文件
    const actualFileMap = new Map(actualFiles.map(f => [f.filePath, f]));
    
    for (const expectedFile of expectedFiles) {
      const actualFile = actualFileMap.get(expectedFile.path);
      
      if (!actualFile) {
        issues.push(`Missing file: ${expectedFile.path}`);
        continue;
      }
      
      // 验证复杂度
      const complexityDiff = Math.abs(actualFile.complexity - expectedFile.complexity);
      const allowedComplexityDiff = expectedFile.complexity * (tolerance / 100);
      
      if (complexityDiff > allowedComplexityDiff) {
        issues.push(`${expectedFile.path} complexity: expected ~${expectedFile.complexity}, got ${actualFile.complexity}`);
      }
      
      // 验证函数数量
      if (actualFile.functions.length !== expectedFile.functionCount) {
        issues.push(`${expectedFile.path} function count: expected ${expectedFile.functionCount}, got ${actualFile.functions.length}`);
      }
    }
    
    const passed = issues.length === 0;
    
    return {
      passed,
      validationType: 'file-validation',
      message: passed ? 'File results validation passed' : `File validation failed: ${issues.join(', ')}`,
      details: { actualFiles, expectedFiles, issues },
      suggestions: passed ? undefined : [
        'Check file paths and naming consistency',
        'Verify complexity calculation changes',
        'Update expected file results if analysis changed'
      ]
    };
  }
  
  /**
   * 验证性能数据一致性
   */
  private static validatePerformanceData(
    actual: any,
    expected: any,
    options: ValidationOptions
  ): ConsistencyValidationResult {
    const issues: string[] = [];
    
    // 检查执行时间（实际应该小于预期最大值）
    if (expected.maxExecutionTime && actual.executionTime) {
      if (actual.executionTime > expected.maxExecutionTime) {
        issues.push(`Execution time ${actual.executionTime}ms exceeds limit ${expected.maxExecutionTime}ms`);
      }
    }
    
    // 检查内存使用（实际应该小于预期最大值）
    if (expected.maxMemoryUsage && actual.memoryUsage) {
      if (actual.memoryUsage > expected.maxMemoryUsage) {
        const actualMB = (actual.memoryUsage / 1024 / 1024).toFixed(2);
        const expectedMB = (expected.maxMemoryUsage / 1024 / 1024).toFixed(2);
        issues.push(`Memory usage ${actualMB}MB exceeds limit ${expectedMB}MB`);
      }
    }
    
    const passed = issues.length === 0;
    
    return {
      passed,
      validationType: 'performance-validation',
      message: passed ? 'Performance validation passed' : `Performance validation failed: ${issues.join(', ')}`,
      details: { actual, expected, issues },
      suggestions: passed ? undefined : [
        'Check if performance has degraded',
        'Consider optimizing algorithm or caching',
        'Update performance expectations if hardware changed'
      ]
    };
  }
  
  /**
   * 验证 CLI 退出码
   */
  private static async validateExitCode(
    cliResult: CLITestResult,
    expectedScenario: TestScenario
  ): Promise<ConsistencyValidationResult> {
    const actualExitCode = await cliResult.waitForExit(10000);
    const expectedExitCode = expectedScenario.expectedExitCode;
    
    const passed = actualExitCode === expectedExitCode;
    
    return {
      passed,
      validationType: 'exit-code-validation',
      message: passed 
        ? `Exit code validation passed (${actualExitCode})`
        : `Exit code mismatch: expected ${expectedExitCode}, got ${actualExitCode}`,
      details: { actual: actualExitCode, expected: expectedExitCode },
      suggestions: passed ? undefined : [
        'Check if command arguments are correct',
        'Verify error handling has not changed',
        'Update expected exit code if behavior changed'
      ]
    };
  }
  
  /**
   * 验证 CLI 输出内容
   */
  private static async validateOutputContent(
    cliResult: CLITestResult,
    expectedOutputs: string[],
    options: ValidationOptions
  ): Promise<ConsistencyValidationResult> {
    const actualOutput = cliResult.stdout + cliResult.stderr;
    const issues: string[] = [];
    
    for (const expected of expectedOutputs) {
      const found = await cliResult.findByText(expected);
      if (!found) {
        issues.push(`Missing expected output: "${expected}"`);
      }
    }
    
    const passed = issues.length === 0;
    
    return {
      passed,
      validationType: 'output-content-validation',
      message: passed 
        ? 'Output content validation passed'
        : `Output validation failed: ${issues.join(', ')}`,
      details: { actualOutput, expectedOutputs, issues },
      suggestions: passed ? undefined : [
        'Check if output format has changed',
        'Verify CLI messages are consistent',
        'Update expected output patterns'
      ]
    };
  }
  
  /**
   * 验证输出格式
   */
  private static async validateOutputFormat(
    cliResult: CLITestResult,
    options: ValidationOptions
  ): Promise<ConsistencyValidationResult> {
    const output = cliResult.stdout + cliResult.stderr;
    const issues: string[] = [];
    
    // 检查是否为有效的 JSON（如果看起来像 JSON）
    if (output.trim().startsWith('{') || output.trim().startsWith('[')) {
      try {
        JSON.parse(output.trim());
      } catch (error) {
        issues.push('Output appears to be JSON but is malformed');
      }
    }
    
    // 检查是否包含错误标记但退出码为 0
    const hasErrorMarkers = /error|Error|ERROR/i.test(output);
    if (hasErrorMarkers && cliResult.exitCode === 0) {
      issues.push('Output contains error messages but exit code is 0');
    }
    
    // 检查输出是否为空（通常不期望）
    if (output.trim().length === 0) {
      issues.push('Output is empty');
    }
    
    const passed = issues.length === 0;
    
    return {
      passed,
      validationType: 'output-format-validation',
      message: passed 
        ? 'Output format validation passed'
        : `Format validation failed: ${issues.join(', ')}`,
      details: { output, issues },
      suggestions: passed ? undefined : [
        'Check output formatting consistency',
        'Verify JSON output is well-formed',
        'Ensure error messages match exit codes'
      ]
    };
  }
  
  /**
   * 批量验证测试数据一致性
   */
  static async validateTestSuite(
    testCases: Array<{
      name: string;
      fixture?: TestFixture;
      cliResult: CLITestResult;
      scenario: TestScenario;
      actualResults?: any;
    }>,
    options: ValidationOptions = {}
  ): Promise<{
    overall: boolean;
    results: Array<{
      testName: string;
      validations: ConsistencyValidationResult[];
      passed: boolean;
    }>;
    summary: {
      total: number;
      passed: number;
      failed: number;
      issues: string[];
    };
  }> {
    const results: Array<{
      testName: string;
      validations: ConsistencyValidationResult[];
      passed: boolean;
    }> = [];
    
    const allIssues: string[] = [];
    let totalPassed = 0;
    
    for (const testCase of testCases) {
      const validations: ConsistencyValidationResult[] = [];
      
      // CLI 一致性验证
      const cliValidations = await this.validateCLIOutputConsistency(
        testCase.cliResult,
        testCase.scenario,
        options
      );
      validations.push(...cliValidations);
      
      // 固件一致性验证（如果有）
      if (testCase.fixture && testCase.actualResults) {
        const fixtureValidations = await this.validateFixtureConsistency(
          testCase.fixture,
          testCase.actualResults,
          options
        );
        validations.push(...fixtureValidations);
      }
      
      const testPassed = validations.every(v => v.passed);
      if (testPassed) {
        totalPassed++;
      } else {
        const testIssues = validations
          .filter(v => !v.passed)
          .map(v => `${testCase.name}: ${v.message}`);
        allIssues.push(...testIssues);
      }
      
      results.push({
        testName: testCase.name,
        validations,
        passed: testPassed
      });
    }
    
    return {
      overall: totalPassed === testCases.length,
      results,
      summary: {
        total: testCases.length,
        passed: totalPassed,
        failed: testCases.length - totalPassed,
        issues: allIssues
      }
    };
  }
  
  /**
   * 生成一致性验证报告
   */
  static generateConsistencyReport(
    validationResults: Array<{
      testName: string;
      validations: ConsistencyValidationResult[];
      passed: boolean;
    }>
  ): string {
    const totalTests = validationResults.length;
    const passedTests = validationResults.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    
    let report = '\n=== Data Consistency Validation Report ===\n';
    report += `Overall: ${passedTests}/${totalTests} tests passed\n\n`;
    
    if (failedTests > 0) {
      report += '❌ Failed Tests:\n';
      validationResults
        .filter(r => !r.passed)
        .forEach(result => {
          report += `\n• ${result.testName}\n`;
          result.validations
            .filter(v => !v.passed)
            .forEach(validation => {
              report += `  - ${validation.validationType}: ${validation.message}\n`;
              if (validation.suggestions) {
                validation.suggestions.forEach(suggestion => {
                  report += `    💡 ${suggestion}\n`;
                });
              }
            });
        });
    }
    
    if (passedTests > 0) {
      report += '\n✅ Passed Tests:\n';
      validationResults
        .filter(r => r.passed)
        .forEach(result => {
          report += `• ${result.testName}\n`;
        });
    }
    
    // 添加统计信息
    const validationTypes = new Map<string, { total: number; passed: number }>();
    validationResults.forEach(result => {
      result.validations.forEach(validation => {
        const stats = validationTypes.get(validation.validationType) || { total: 0, passed: 0 };
        stats.total++;
        if (validation.passed) stats.passed++;
        validationTypes.set(validation.validationType, stats);
      });
    });
    
    report += '\n=== Validation Statistics ===\n';
    Array.from(validationTypes.entries()).forEach(([type, stats]) => {
      const percentage = ((stats.passed / stats.total) * 100).toFixed(1);
      report += `${type}: ${stats.passed}/${stats.total} (${percentage}%)\n`;
    });
    
    return report;
  }
  
  /**
   * 创建数据迁移验证助手
   */
  static createMigrationValidator(
    oldFrameworkResults: any[],
    newFrameworkResults: any[]
  ): {
    validate(): ConsistencyValidationResult[];
    getSummary(): { identical: number; similar: number; different: number; missing: number };
  } {
    return {
      validate: () => {
        const results: ConsistencyValidationResult[] = [];
        
        // 比较结果数量
        if (oldFrameworkResults.length !== newFrameworkResults.length) {
          results.push({
            passed: false,
            validationType: 'migration-count',
            message: `Result count mismatch: old=${oldFrameworkResults.length}, new=${newFrameworkResults.length}`,
            suggestions: ['Check if all test cases were migrated', 'Verify test data consistency']
          });
        }
        
        // 逐项比较结果
        const minLength = Math.min(oldFrameworkResults.length, newFrameworkResults.length);
        for (let i = 0; i < minLength; i++) {
          const oldResult = oldFrameworkResults[i];
          const newResult = newFrameworkResults[i];
          
          const isIdentical = JSON.stringify(oldResult) === JSON.stringify(newResult);
          
          results.push({
            passed: isIdentical,
            validationType: 'migration-comparison',
            message: isIdentical 
              ? `Test ${i + 1}: Results identical`
              : `Test ${i + 1}: Results differ`,
            details: { old: oldResult, new: newResult }
          });
        }
        
        return results;
      },
      
      getSummary: () => {
        const validation = this.validate();
        const comparisonResults = validation.filter(v => v.validationType === 'migration-comparison');
        
        return {
          identical: comparisonResults.filter(r => r.passed).length,
          similar: 0, // 可以添加相似度检查逻辑
          different: comparisonResults.filter(r => !r.passed).length,
          missing: Math.abs(oldFrameworkResults.length - newFrameworkResults.length)
        };
      }
    };
  }
}

/**
 * 快速验证工具函数
 */
export namespace ValidationUtils {
  /**
   * 快速验证两个复杂度结果是否一致
   */
  export function quickValidateComplexity(
    actual: FunctionResult[],
    expected: FunctionResult[],
    tolerance: number = 5
  ): boolean {
    if (actual.length !== expected.length) return false;
    
    for (let i = 0; i < actual.length; i++) {
      const a = actual[i]!;
      const e = expected[i]!;
      
      if (a.name !== e.name) return false;
      
      const diff = Math.abs(a.complexity - e.complexity);
      const allowedDiff = e.complexity * (tolerance / 100);
      
      if (diff > allowedDiff) return false;
    }
    
    return true;
  }
  
  /**
   * 快速验证 CLI 输出是否包含期望内容
   */
  export async function quickValidateOutput(
    cliResult: CLITestResult,
    expectedTexts: string[]
  ): Promise<boolean> {
    for (const text of expectedTexts) {
      const found = await cliResult.findByText(text);
      if (!found) return false;
    }
    return true;
  }
  
  /**
   * 快速验证性能是否在预期范围内
   */
  export function quickValidatePerformance(
    actual: { executionTime: number; memoryUsage: number },
    limits: { maxTime: number; maxMemory: number }
  ): boolean {
    return actual.executionTime <= limits.maxTime && 
           actual.memoryUsage <= limits.maxMemory;
  }
}