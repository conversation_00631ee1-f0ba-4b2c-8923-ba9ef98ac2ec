/**
 * 测试资源管理器
 * 减少测试间的资源竞争和干扰
 */

export interface ResourceLimits {
  maxMemoryMB: number;
  maxProcesses: number;
  maxCPUPercent: number;
  maxOpenFiles: number;
}

export interface ResourceUsage {
  memoryMB: number;
  processCount: number;
  cpuPercent: number;
  openFiles: number;
  timestamp: number;
}

export interface TestResourceAllocation {
  testId: string;
  allocatedMemoryMB: number;
  allocatedProcesses: number;
  priority: number;
  startTime: number;
  estimatedDuration: number;
}

/**
 * 系统资源监控器
 */
class SystemResourceMonitor {
  private monitoringInterval?: NodeJS.Timeout;
  private resourceHistory: ResourceUsage[] = [];
  private readonly maxHistorySize = 100;

  startMonitoring(intervalMs: number = 1000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    this.monitoringInterval = setInterval(() => {
      const usage = this.getCurrentUsage();
      this.resourceHistory.push(usage);
      
      // 保持历史记录大小
      if (this.resourceHistory.length > this.maxHistorySize) {
        this.resourceHistory.shift();
      }
    }, intervalMs);
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }

  getCurrentUsage(): ResourceUsage {
    const memoryUsage = process.memoryUsage();
    
    return {
      memoryMB: memoryUsage.heapUsed / 1024 / 1024,
      processCount: this.getProcessCount(),
      cpuPercent: this.getCPUUsage(),
      openFiles: this.getOpenFileCount(),
      timestamp: Date.now()
    };
  }

  private getProcessCount(): number {
    // 简化实现，实际可以通过系统调用获取
    return process.pid ? 1 : 0;
  }

  private getCPUUsage(): number {
    const usage = process.cpuUsage();
    // 简化的CPU使用率计算
    return (usage.user + usage.system) / 1000000; // 转换为秒
  }

  private getOpenFileCount(): number {
    // 简化实现，实际需要通过系统调用获取
    return 10; // 假设值
  }

  getResourceTrend(): {
    increasing: boolean;
    peakMemoryMB: number;
    avgMemoryMB: number;
    suggestion?: string;
  } {
    if (this.resourceHistory.length < 5) {
      return {
        increasing: false,
        peakMemoryMB: 0,
        avgMemoryMB: 0,
        suggestion: '数据不足，需要更多监控数据'
      };
    }

    const recent = this.resourceHistory.slice(-10);
    const older = this.resourceHistory.slice(-20, -10);
    
    const recentAvg = recent.reduce((sum, r) => sum + r.memoryMB, 0) / recent.length;
    const olderAvg = older.length > 0 
      ? older.reduce((sum, r) => sum + r.memoryMB, 0) / older.length 
      : recentAvg;
    
    const peakMemoryMB = Math.max(...this.resourceHistory.map(r => r.memoryMB));
    const avgMemoryMB = this.resourceHistory.reduce((sum, r) => sum + r.memoryMB, 0) / this.resourceHistory.length;
    
    const increasing = recentAvg > olderAvg * 1.1; // 10%增长阈值
    
    let suggestion: string | undefined;
    if (increasing && peakMemoryMB > 100) {
      suggestion = '内存使用呈上升趋势，建议检查内存泄漏';
    } else if (peakMemoryMB > 200) {
      suggestion = '内存使用峰值过高，考虑优化测试或增加系统资源';
    }

    return {
      increasing,
      peakMemoryMB,
      avgMemoryMB,
      suggestion
    };
  }

  getResourceHistory(): ResourceUsage[] {
    return [...this.resourceHistory];
  }

  clearHistory(): void {
    this.resourceHistory = [];
  }
}

/**
 * 资源分配管理器
 */
class ResourceAllocator {
  private allocations = new Map<string, TestResourceAllocation>();
  private limits: ResourceLimits;
  private monitor: SystemResourceMonitor;

  constructor(limits: ResourceLimits, monitor: SystemResourceMonitor) {
    this.limits = limits;
    this.monitor = monitor;
  }

  /**
   * 请求资源分配
   */
  async requestAllocation(
    testId: string,
    requiredMemoryMB: number,
    requiredProcesses: number,
    priority: number = 5,
    estimatedDuration: number = 10000
  ): Promise<boolean> {
    // 检查当前系统资源使用
    const currentUsage = this.monitor.getCurrentUsage();
    const currentAllocations = Array.from(this.allocations.values());
    
    // 计算已分配的资源
    const allocatedMemory = currentAllocations.reduce((sum, alloc) => sum + alloc.allocatedMemoryMB, 0);
    const allocatedProcesses = currentAllocations.reduce((sum, alloc) => sum + alloc.allocatedProcesses, 0);
    
    // 检查是否有足够资源
    const availableMemory = this.limits.maxMemoryMB - currentUsage.memoryMB - allocatedMemory;
    const availableProcesses = this.limits.maxProcesses - allocatedProcesses;
    
    if (availableMemory < requiredMemoryMB || availableProcesses < requiredProcesses) {
      // 如果资源不足，尝试等待或抢占低优先级测试
      return await this.handleResourceContention(
        testId,
        requiredMemoryMB,
        requiredProcesses,
        priority
      );
    }

    // 分配资源
    const allocation: TestResourceAllocation = {
      testId,
      allocatedMemoryMB: requiredMemoryMB,
      allocatedProcesses: requiredProcesses,
      priority,
      startTime: Date.now(),
      estimatedDuration
    };

    this.allocations.set(testId, allocation);
    return true;
  }

  /**
   * 释放资源分配
   */
  releaseAllocation(testId: string): void {
    this.allocations.delete(testId);
  }

  /**
   * 处理资源争用
   */
  private async handleResourceContention(
    testId: string,
    requiredMemoryMB: number,
    requiredProcesses: number,
    priority: number
  ): Promise<boolean> {
    const currentAllocations = Array.from(this.allocations.values());
    
    // 查找可以抢占的低优先级测试
    const preemptableTasks = currentAllocations
      .filter(alloc => alloc.priority < priority)
      .sort((a, b) => a.priority - b.priority);

    let freedMemory = 0;
    let freedProcesses = 0;
    const tasksToPreempt: string[] = [];

    for (const task of preemptableTasks) {
      tasksToPreempt.push(task.testId);
      freedMemory += task.allocatedMemoryMB;
      freedProcesses += task.allocatedProcesses;

      if (freedMemory >= requiredMemoryMB && freedProcesses >= requiredProcesses) {
        break;
      }
    }

    // 如果可以通过抢占获得足够资源
    if (freedMemory >= requiredMemoryMB && freedProcesses >= requiredProcesses) {
      // 抢占资源（实际实现中需要通知相关测试）
      for (const taskId of tasksToPreempt) {
        this.allocations.delete(taskId);
        console.warn(`抢占测试 ${taskId} 的资源以分配给高优先级测试 ${testId}`);
      }

      // 分配资源给新测试
      const allocation: TestResourceAllocation = {
        testId,
        allocatedMemoryMB: requiredMemoryMB,
        allocatedProcesses: requiredProcesses,
        priority,
        startTime: Date.now(),
        estimatedDuration: 10000
      };

      this.allocations.set(testId, allocation);
      return true;
    }

    // 无法获得足够资源
    return false;
  }

  /**
   * 获取当前资源分配状态
   */
  getAllocationStatus(): {
    totalAllocations: number;
    allocatedMemoryMB: number;
    allocatedProcesses: number;
    availableMemoryMB: number;
    availableProcesses: number;
    allocations: TestResourceAllocation[];
  } {
    const allocations = Array.from(this.allocations.values());
    const allocatedMemory = allocations.reduce((sum, alloc) => sum + alloc.allocatedMemoryMB, 0);
    const allocatedProcesses = allocations.reduce((sum, alloc) => sum + alloc.allocatedProcesses, 0);
    const currentUsage = this.monitor.getCurrentUsage();

    return {
      totalAllocations: allocations.length,
      allocatedMemoryMB: allocatedMemory,
      allocatedProcesses: allocatedProcesses,
      availableMemoryMB: this.limits.maxMemoryMB - currentUsage.memoryMB - allocatedMemory,
      availableProcesses: this.limits.maxProcesses - allocatedProcesses,
      allocations: [...allocations]
    };
  }

  /**
   * 清理过期分配
   */
  cleanupExpiredAllocations(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [testId, allocation] of this.allocations) {
      const elapsed = now - allocation.startTime;
      // 如果超过预期时间的2倍，认为可能已泄漏
      if (elapsed > allocation.estimatedDuration * 2) {
        console.warn(`清理可能泄漏的资源分配: ${testId} (运行了 ${elapsed}ms)`);
        this.allocations.delete(testId);
        cleaned++;
      }
    }

    return cleaned;
  }
}

/**
 * 测试隔离管理器
 */
class TestIsolationManager {
  private testNamespaces = new Map<string, Set<string>>();
  private sharedResources = new Set<string>();

  /**
   * 创建测试命名空间
   */
  createNamespace(namespaceId: string): void {
    if (!this.testNamespaces.has(namespaceId)) {
      this.testNamespaces.set(namespaceId, new Set());
    }
  }

  /**
   * 添加资源到命名空间
   */
  addResourceToNamespace(namespaceId: string, resourceId: string): void {
    const namespace = this.testNamespaces.get(namespaceId);
    if (namespace) {
      namespace.add(resourceId);
      // 从共享资源中移除
      this.sharedResources.delete(resourceId);
    }
  }

  /**
   * 检查资源冲突
   */
  checkResourceConflict(testId1: string, testId2: string): {
    hasConflict: boolean;
    conflictingResources: string[];
  } {
    const namespace1 = this.testNamespaces.get(testId1);
    const namespace2 = this.testNamespaces.get(testId2);

    if (!namespace1 || !namespace2) {
      return { hasConflict: false, conflictingResources: [] };
    }

    const conflictingResources: string[] = [];
    for (const resource of namespace1) {
      if (namespace2.has(resource)) {
        conflictingResources.push(resource);
      }
    }

    return {
      hasConflict: conflictingResources.length > 0,
      conflictingResources
    };
  }

  /**
   * 清理命名空间
   */
  cleanupNamespace(namespaceId: string): void {
    const namespace = this.testNamespaces.get(namespaceId);
    if (namespace) {
      // 将资源返回到共享池
      for (const resource of namespace) {
        this.sharedResources.add(resource);
      }
      this.testNamespaces.delete(namespaceId);
    }
  }

  /**
   * 获取隔离状态
   */
  getIsolationStatus(): {
    namespaceCount: number;
    sharedResourceCount: number;
    totalResources: number;
  } {
    let totalResources = this.sharedResources.size;
    for (const namespace of this.testNamespaces.values()) {
      totalResources += namespace.size;
    }

    return {
      namespaceCount: this.testNamespaces.size,
      sharedResourceCount: this.sharedResources.size,
      totalResources
    };
  }
}

/**
 * 测试资源管理器主类
 */
export class TestResourceManager {
  private static instance: TestResourceManager;
  private monitor: SystemResourceMonitor;
  private allocator: ResourceAllocator;
  private isolationManager: TestIsolationManager;
  private isInitialized = false;

  private constructor() {
    this.monitor = new SystemResourceMonitor();
    this.allocator = new ResourceAllocator(
      {
        maxMemoryMB: 500,
        maxProcesses: 20,
        maxCPUPercent: 80,
        maxOpenFiles: 1000
      },
      this.monitor
    );
    this.isolationManager = new TestIsolationManager();
  }

  static getInstance(): TestResourceManager {
    if (!TestResourceManager.instance) {
      TestResourceManager.instance = new TestResourceManager();
    }
    return TestResourceManager.instance;
  }

  /**
   * 初始化资源管理器
   */
  initialize(limits?: Partial<ResourceLimits>): void {
    if (this.isInitialized) {
      return;
    }

    if (limits) {
      this.allocator = new ResourceAllocator(
        {
          maxMemoryMB: 500,
          maxProcesses: 20,
          maxCPUPercent: 80,
          maxOpenFiles: 1000,
          ...limits
        },
        this.monitor
      );
    }

    this.monitor.startMonitoring(1000);
    this.isInitialized = true;

    console.log('测试资源管理器已初始化');
  }

  /**
   * 清理资源管理器
   */
  cleanup(): void {
    this.monitor.stopMonitoring();
    this.monitor.clearHistory();
    this.allocator.cleanupExpiredAllocations();
    this.isInitialized = false;
  }

  /**
   * 为测试请求资源
   */
  async requestResources(
    testId: string,
    requirements: {
      memoryMB: number;
      processes: number;
      priority?: number;
      estimatedDurationMs?: number;
    }
  ): Promise<boolean> {
    const success = await this.allocator.requestAllocation(
      testId,
      requirements.memoryMB,
      requirements.processes,
      requirements.priority || 5,
      requirements.estimatedDurationMs || 10000
    );

    if (success) {
      this.isolationManager.createNamespace(testId);
    }

    return success;
  }

  /**
   * 释放测试资源
   */
  releaseResources(testId: string): void {
    this.allocator.releaseAllocation(testId);
    this.isolationManager.cleanupNamespace(testId);
  }

  /**
   * 检查资源使用趋势
   */
  getResourceTrend(): {
    increasing: boolean;
    peakMemoryMB: number;
    avgMemoryMB: number;
    suggestion?: string;
  } {
    return this.monitor.getResourceTrend();
  }

  /**
   * 获取资源管理状态
   */
  getStatus(): {
    monitor: ResourceUsage;
    allocation: ReturnType<ResourceAllocator['getAllocationStatus']>;
    isolation: ReturnType<TestIsolationManager['getIsolationStatus']>;
    trend: ReturnType<SystemResourceMonitor['getResourceTrend']>;
  } {
    return {
      monitor: this.monitor.getCurrentUsage(),
      allocation: this.allocator.getAllocationStatus(),
      isolation: this.isolationManager.getIsolationStatus(),
      trend: this.monitor.getResourceTrend()
    };
  }

  /**
   * 执行资源优化
   */
  optimizeResources(): {
    cleanedAllocations: number;
    suggestions: string[];
  } {
    const cleanedAllocations = this.allocator.cleanupExpiredAllocations();
    const trend = this.monitor.getResourceTrend();
    const suggestions: string[] = [];

    if (trend.suggestion) {
      suggestions.push(trend.suggestion);
    }

    if (cleanedAllocations > 0) {
      suggestions.push(`清理了 ${cleanedAllocations} 个过期的资源分配`);
    }

    const allocationStatus = this.allocator.getAllocationStatus();
    if (allocationStatus.availableMemoryMB < 50) {
      suggestions.push('可用内存不足，考虑减少并发测试数量');
    }

    if (allocationStatus.availableProcesses < 3) {
      suggestions.push('可用进程数不足，考虑优化测试调度');
    }

    return {
      cleanedAllocations,
      suggestions
    };
  }
}