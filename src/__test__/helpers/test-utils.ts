import { analyzeFile } from "../../index";
import type { AnalysisResult, FileResult, FunctionResult } from "../../core/types";
import type { AnalysisContext, ResolvedEngineConfig } from "../../engine/types";
import { DEFAULT_ENGINE_CONFIG } from "../../engine/types";
import type { Node } from '@swc/core';
import { expect } from "vitest";
import { writeFileSync, unlinkSync, mkdtempSync, rmSync } from "fs";
import { spawn } from "child_process";
import { tmpdir } from "os";
import { join } from "path";

/**
 * 测试辅助工具集
 */
export class TestUtils {
  /**
   * 分析代码字符串并返回结果
   */
  static async analyzeCode(code: string, filePath: string = "test.ts"): Promise<FunctionResult[]> {
    const tempPath = await this.createTempFile(code, '.ts');
    try {
      const result = await analyzeFile(tempPath);
      return result.functions;
    } finally {
      await this.cleanupTempFile(tempPath);
    }
  }
  
  /**
   * 创建模拟的函数结果
   */
  static createMockFunctionResult(overrides: Partial<FunctionResult> = {}): FunctionResult {
    return {
      name: "testFunction",
      complexity: 0,
      line: 1,
      column: 0,
      filePath: "test.ts",
      ...overrides
    };
  }
  
  /**
   * 创建模拟的文件结果
   */
  static createMockFileResult(overrides: Partial<FileResult> = {}): FileResult {
    const defaultFunction = this.createMockFunctionResult();
    
    return {
      filePath: "test.ts",
      complexity: 0,
      averageComplexity: 0,
      functions: [defaultFunction],
      ...overrides
    };
  }
  
  /**
   * 创建模拟的分析结果
   */
  static createMockAnalysisResult(overrides: Partial<AnalysisResult> = {}): AnalysisResult {
    const defaultFileResult = this.createMockFileResult();
    
    return {
      summary: {
        totalComplexity: 0,
        averageComplexity: 0,
        filesAnalyzed: 1,
        functionsAnalyzed: 1,
        highComplexityFunctions: 0
      },
      results: [defaultFileResult],
      ...overrides
    };
  }
  
  /**
   * 验证复杂度计算结果
   */
  static expectComplexity(
    results: FunctionResult[], 
    expectedResults: Array<{ name: string; complexity: number }>
  ): void {
    expect(results).toHaveLength(expectedResults.length);
    
    expectedResults.forEach((expected, index) => {
      expect(results[index]?.name).toBe(expected.name);
      expect(results[index]?.complexity).toBe(expected.complexity);
    });
  }
  
  /**
   * 创建测试代码模板
   */
  static createTestCode(functionBody: string, functionName: string = "testFunction"): string {
    return `
      function ${functionName}() {
        ${functionBody}
      }
    `;
  }
  
  /**
   * 创建嵌套测试代码
   */
  static createNestedTestCode(levels: number): string {
    let code = "function nestedFunction() {\n";
    
    for (let i = 0; i < levels; i++) {
      const indent = "  ".repeat(i + 1);
      code += `${indent}if (condition${i}) {\n`;
    }
    
    const innerIndent = "  ".repeat(levels + 1);
    code += `${innerIndent}doSomething();\n`;
    
    for (let i = levels - 1; i >= 0; i--) {
      const indent = "  ".repeat(i + 1);
      code += `${indent}}\n`;
    }
    
    code += "}";
    return code;
  }
  
  /**
   * 计算预期的嵌套复杂度
   */
  static calculateExpectedNestedComplexity(levels: number): number {
    let total = 0;
    for (let i = 0; i < levels; i++) {
      total += 1 + i; // 基础分1 + 嵌套惩罚i
    }
    return total;
  }
  
  /**
   * 创建逻辑运算符测试代码
   */
  static createLogicalOperatorTestCode(operators: string[]): string {
    const conditions = operators.map((_, index) => `condition${index}`);
    const expression = conditions.join(` ${operators[0]} `);
    
    return `
      function logicalFunction() {
        if (${expression}) {
          return true;
        }
      }
    `;
  }
  
  /**
   * 为每个测试生成一个独立且自动清理的工作目录
   * 解决并行测试中共享目录的状态污染问题
   */
  static async withTempDir<T>(fn: (dir: string) => Promise<T> | T): Promise<T> {
    const dir = mkdtempSync(join(tmpdir(), "cognitive-"));
    try {
      return await fn(dir);
    } finally {
      rmSync(dir, { recursive: true, force: true });
    }
  }
  
  /**
   * 等待指定毫秒数
   */
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  /**
   * 执行命令行命令 (替代 Bun.$)
   */
  static async executeCommand(command: string, args: string[] = []): Promise<{ stdout: string; stderr: string; exitCode: number }> {
    return new Promise((resolve) => {
      const child = spawn(command, args);
      let stdout = '';
      let stderr = '';
      
      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });
      
      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });
      
      child.on('close', (code) => {
        resolve({
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: code || 0
        });
      });
    });
  }
  
  /**
   * 创建临时文件
   */
  static async createTempFile(content: string, extension: string = ".ts"): Promise<string> {
    const tempPath = `/tmp/test-${Date.now()}${extension}`;
    writeFileSync(tempPath, content, 'utf8');
    return tempPath;
  }
  
  /**
   * 清理临时文件
   */
  static async cleanupTempFile(filePath: string): Promise<void> {
    try {
      unlinkSync(filePath);
    } catch (error) {
      // 忽略清理错误
    }
  }
  
  // ============ CLI 测试方法 ============
  
  /**
   * 执行 CLI 测试 - 使用新的 cli-testing-library
   */
  static async executeCLITest(command: string, args: string[] = []): Promise<import('./cli-testing-utils').CLITestResult> {
    const { CLITestingUtils } = await import('./cli-testing-utils');
    return await CLITestingUtils.renderCLI(command, args);
  }
  
  /**
   * 验证 CLI 输出包含期望的文本 - 增强版
   */
  static async expectCLIOutput(
    result: import('./cli-testing-utils').CLITestResult, 
    expectedTexts: string[],
    options?: import('./output-validator').OutputValidationOptions
  ): Promise<void> {
    const { OutputValidator } = await import('./output-validator');
    const output = result.stdout + result.stderr;
    
    const validation = await OutputValidator.assertOutputContains(output, expectedTexts, options);
    expect(validation.passed).toBe(true);
    
    if (!validation.passed && validation.message) {
      throw new Error(validation.message);
    }
  }
  
  /**
   * 验证 CLI 成功执行
   */
  static async expectCLISuccess(result: import('./cli-testing-utils').CLITestResult): Promise<void> {
    const exitCode = await result.waitForExit(10000);
    expect(exitCode).toBe(0);
  }
  
  /**
   * 验证 CLI 执行失败
   */
  static async expectCLIFailure(
    result: import('./cli-testing-utils').CLITestResult, 
    expectedError?: string,
    options?: import('./output-validator').OutputValidationOptions
  ): Promise<void> {
    const exitCode = await result.waitForExit(10000);
    expect(exitCode).not.toBe(0);
    
    if (expectedError) {
      const { OutputValidator } = await import('./output-validator');
      const output = result.stdout + result.stderr;
      
      const validation = await OutputValidator.assertOutputContains(output, [expectedError], options);
      expect(validation.passed).toBe(true);
    }
  }
  
  /**
   * 运行完整 CLI 测试场景
   */
  static async runCLIScenario(scenario: import('./cli-testing-utils').TestScenario): Promise<import('./cli-testing-utils').TestExecutionResult> {
    const { CLITestingUtils } = await import('./cli-testing-utils');
    
    const instance = await CLITestingUtils.renderCLI(scenario.command, scenario.args);
    
    try {
      return await CLITestingUtils.validateExecution(
        instance,
        scenario.expectedExitCode,
        scenario.expectedOutput,
        scenario.timeout
      );
    } finally {
      await CLITestingUtils.cleanup(instance);
    }
  }

  // ============ 增强的输出验证方法 ============
  
  /**
   * 使用正则表达式验证CLI输出
   */
  static async expectCLIOutputMatches(
    result: import('./cli-testing-utils').CLITestResult,
    pattern: RegExp,
    options?: import('./output-validator').OutputValidationOptions
  ): Promise<void> {
    const { OutputValidator } = await import('./output-validator');
    const output = result.stdout + result.stderr;
    
    const validation = await OutputValidator.assertOutputMatches(output, pattern, options);
    expect(validation.passed).toBe(true);
    
    if (!validation.passed && validation.message) {
      throw new Error(validation.message);
    }
  }
  
  /**
   * 验证CLI输出的JSON格式
   */
  static async expectCLIJSONOutput(
    result: import('./cli-testing-utils').CLITestResult,
    schema: object,
    options?: import('./output-validator').JSONValidationOptions
  ): Promise<void> {
    const { OutputValidator } = await import('./output-validator');
    const output = result.stdout + result.stderr;
    
    const validation = await OutputValidator.validateJSONOutput(output, schema, options);
    expect(validation.passed).toBe(true);
    
    if (!validation.passed && validation.message) {
      throw new Error(validation.message);
    }
  }
  
  /**
   * 验证CLI输出的表格格式
   */
  static async expectCLITableOutput(
    result: import('./cli-testing-utils').CLITestResult,
    expectedColumns: string[],
    options?: import('./output-validator').TableValidationOptions
  ): Promise<void> {
    const { OutputValidator } = await import('./output-validator');
    const output = result.stdout + result.stderr;
    
    const validation = await OutputValidator.validateTableOutput(output, expectedColumns, options);
    expect(validation.passed).toBe(true);
    
    if (!validation.passed && validation.message) {
      throw new Error(validation.message);
    }
  }
  
  /**
   * 验证CLI性能输出
   */
  static async expectCLIPerformance(
    result: import('./cli-testing-utils').CLITestResult,
    maxTime: number,
    options?: import('./output-validator').OutputValidationOptions
  ): Promise<void> {
    const { OutputValidator } = await import('./output-validator');
    const output = result.stdout + result.stderr;
    
    const validation = await OutputValidator.validatePerformanceOutput(output, maxTime, options);
    expect(validation.passed).toBe(true);
    
    if (!validation.passed && validation.message) {
      throw new Error(validation.message);
    }
  }
  
  /**
   * 综合验证CLI结果 - 一次调用完成多种验证
   */
  static async validateCLIComprehensive(
    result: import('./cli-testing-utils').CLITestResult,
    validations: {
      contains?: string[];
      matches?: RegExp[];
      json?: object;
      table?: string[];
      performance?: number;
      fuzzy?: { target: string; threshold?: number }[];
    },
    options?: import('./output-validator').OutputValidationOptions
  ): Promise<void> {
    const { OutputValidator } = await import('./output-validator');
    
    const results = await OutputValidator.validateCLIResult(result, validations, options);
    const failed = results.filter(r => !r.passed);
    
    if (failed.length > 0) {
      const messages = failed.map(f => f.message).filter(Boolean);
      throw new Error(`CLI validation failed:\n${messages.join('\n')}`);
    }
  }
  
  /**
   * 调试CLI输出内容
   */
  static debugCLIOutput(
    result: import('./cli-testing-utils').CLITestResult,
    label?: string
  ): void {
    import('./output-validator').then(({ OutputDebugHelper }) => {
      const output = result.stdout + result.stderr;
      OutputDebugHelper.debugOutput(output, label || 'CLI Debug');
      
      const analysis = OutputDebugHelper.analyzeOutput(output);
      console.log('Output Analysis:', analysis);
    });
  }
}

/**
 * 增强的模拟数据生成器
 * 集成了 CLI 测试框架的数据生成能力
 */
export class MockDataGenerator {
  /**
   * 生成随机复杂度值
   */
  static randomComplexity(min: number = 0, max: number = 50): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }
  
  /**
   * 生成随机函数名
   */
  static randomFunctionName(): string {
    const names = [
      "processData", "validateInput", "calculateResult", "handleError",
      "transformValue", "filterResults", "sortItems", "parseConfig",
      "handleRequest", "generateReport", "buildQuery", "formatResponse",
      "authenticateUser", "validatePermissions", "processPayment", "sendNotification"
    ];
    return names[Math.floor(Math.random() * names.length)]!;
  }
  
  /**
   * 生成随机文件路径
   */
  static randomFilePath(): string {
    const dirs = ["src", "lib", "utils", "components", "pages", "hooks", "services", "api"];
    const files = ["index", "main", "helper", "config", "types", "constants", "middleware"];
    const dir = dirs[Math.floor(Math.random() * dirs.length)];
    const file = files[Math.floor(Math.random() * files.length)];
    return `${dir}/${file}.ts`;
  }
  
  /**
   * 生成批量测试数据
   */
  static generateTestFunctions(count: number): FunctionResult[] {
    return Array.from({ length: count }, (_, index) => ({
      name: `function${index}`,
      complexity: this.randomComplexity(),
      line: (index + 1) * 10,
      column: 0,
      filePath: this.randomFilePath()
    }));
  }
  
  // ============ CLI 测试数据生成方法 ============
  
  /**
   * 生成随机 CLI 测试场景
   */
  static generateCLITestScenario(overrides?: Partial<import('./cli-testing-utils').TestScenario>): import('./cli-testing-utils').TestScenario {
    const commands = ['scan', 'analyze', 'baseline', 'report'];
    const args = [
      ['--format', 'json'],
      ['--threshold', '10'],
      ['--output', 'result.json'],
      ['--include', '**/*.ts'],
      ['--exclude', 'node_modules'],
      ['--config', 'complexity.config.js']
    ];
    
    const randomCommand = commands[Math.floor(Math.random() * commands.length)]!;
    const randomArgs = args[Math.floor(Math.random() * args.length)]!;
    
    return {
      name: `CLI Test - ${randomCommand}`,
      command: 'cognitive-complexity',
      args: [randomCommand, ...randomArgs],
      expectedOutput: [`Command executed: ${randomCommand}`, 'Analysis complete'],
      expectedExitCode: 0,
      interactive: false,
      timeout: 5000,
      ...overrides
    };
  }
  
  /**
   * 生成复合 CLI 测试场景批次
   */
  static generateCLITestBatch(count: number, baseScenario?: Partial<import('./cli-testing-utils').TestScenario>): import('./cli-testing-utils').TestScenario[] {
    return Array.from({ length: count }, (_, index) => 
      this.generateCLITestScenario({
        name: `Batch Test ${index + 1}`,
        ...baseScenario
      })
    );
  }
  
  /**
   * 生成 CLI 测试配置
   */
  static generateCLITestConfig(overrides?: Partial<import('./cli-testing-utils').CLITestConfig>): import('./cli-testing-utils').CLITestConfig {
    return {
      timeout: 10000,
      maxBuffer: 1024 * 1024, // 1MB
      env: {
        NODE_ENV: 'test',
        CI: 'true',
        FORCE_COLOR: '0',
        ...process.env
      },
      cwd: process.cwd(),
      cleanup: true,
      ...overrides
    };
  }
  
  /**
   * 生成性能基准测试数据
   */
  static generatePerformanceBenchmark(name: string, category: 'basic' | 'complex' | 'concurrent' = 'basic'): PerformanceBenchmark {
    const benchmarks = {
      basic: {
        maxExecutionTime: 3000,
        maxMemoryUsage: 50 * 1024 * 1024,
        maxCpuUsage: 70
      },
      complex: {
        maxExecutionTime: 15000,
        maxMemoryUsage: 200 * 1024 * 1024,
        maxCpuUsage: 85
      },
      concurrent: {
        maxExecutionTime: 8000,
        maxMemoryUsage: 150 * 1024 * 1024,
        maxCpuUsage: 90
      }
    };
    
    return {
      name,
      description: `Generated benchmark for ${category} testing`,
      ...benchmarks[category]
    };
  }
  
  /**
   * 生成模拟的 CLI 输出内容
   */
  static generateCLIOutput(type: 'success' | 'error' | 'warning' | 'info', content?: string): string {
    const templates = {
      success: [
        '✅ Analysis completed successfully',
        '📊 Results saved to complexity-report.json',
        '🎯 Found 15 functions with complexity above threshold'
      ],
      error: [
        '❌ Error: File not found',
        '💥 Parse error in src/main.ts at line 42',
        '🚫 Configuration file is invalid'
      ],
      warning: [
        '⚠️ Warning: High complexity detected in function processData (score: 25)',
        '📈 Function calculateMetrics exceeds threshold (15 > 10)',
        '🔍 Consider refactoring the following functions'
      ],
      info: [
        'ℹ️ Scanning 127 TypeScript files...',
        '📁 Processing directory: src/',
        '⏱️ Analysis took 2.4 seconds'
      ]
    };
    
    const baseContent = content || templates[type][Math.floor(Math.random() * templates[type].length)]!;
    const timestamp = new Date().toISOString();
    
    return `[${timestamp}] ${baseContent}`;
  }
  
  /**
   * 生成复杂度分析结果的模拟 JSON 输出
   */
  static generateComplexityJSON(fileCount: number = 5, functionCount: number = 20): string {
    const files = Array.from({ length: fileCount }, (_, index) => ({
      filePath: this.randomFilePath(),
      functions: Array.from({ length: Math.floor(functionCount / fileCount) }, () => ({
        name: this.randomFunctionName(),
        complexity: this.randomComplexity(0, 30),
        line: Math.floor(Math.random() * 1000) + 1,
        column: Math.floor(Math.random() * 100)
      }))
    }));
    
    const totalComplexity = files.reduce((sum, file) => 
      sum + file.functions.reduce((fSum, func) => fSum + func.complexity, 0), 0
    );
    
    const result = {
      summary: {
        totalComplexity,
        averageComplexity: totalComplexity / (fileCount * Math.floor(functionCount / fileCount)),
        filesAnalyzed: fileCount,
        functionsAnalyzed: fileCount * Math.floor(functionCount / fileCount),
        highComplexityFunctions: files.reduce((count, file) => 
          count + file.functions.filter(f => f.complexity > 10).length, 0
        )
      },
      results: files
    };
    
    return JSON.stringify(result, null, 2);
  }
  
  /**
   * 生成表格格式的模拟输出
   */
  static generateTableOutput(columns: string[], rows: number = 5): string {
    const columnWidths = columns.map(col => Math.max(col.length, 15));
    const separator = '┼' + columnWidths.map(w => '─'.repeat(w + 2)).join('┼') + '┼';
    const headerRow = '│ ' + columns.map((col, i) => col.padEnd(columnWidths[i])).join(' │ ') + ' │';
    
    let table = '┌' + columnWidths.map(w => '─'.repeat(w + 2)).join('┬') + '┐\n';
    table += headerRow + '\n';
    table += separator.replace(/┼/g, '├').replace(/^├/, '├').replace(/├$/, '┤') + '\n';
    
    for (let i = 0; i < rows; i++) {
      const rowData = columns.map(() => {
        if (Math.random() > 0.7) return this.randomComplexity(0, 50).toString();
        if (Math.random() > 0.5) return this.randomFunctionName();
        return this.randomFilePath();
      });
      
      const row = '│ ' + rowData.map((data, i) => 
        data.substring(0, columnWidths[i]).padEnd(columnWidths[i])
      ).join(' │ ') + ' │';
      table += row + '\n';
    }
    
    table += '└' + columnWidths.map(w => '─'.repeat(w + 2)).join('┴') + '┘';
    return table;
  }
  
  /**
   * 生成交互式测试的用户输入序列
   */
  static generateInteractiveFlow(steps: number = 3): Array<{
    waitFor: string | RegExp;
    input: string;
    delay?: number;
  }> {
    const prompts = [
      'Enter file path:',
      'Select output format (json/text/html):',
      'Set complexity threshold (1-50):',
      'Include test files? (y/n):',
      'Save results to file? (y/n):'
    ];
    
    const responses = [
      'src/**/*.ts',
      'json',
      '15',
      'n',
      'y'
    ];
    
    return Array.from({ length: Math.min(steps, prompts.length) }, (_, index) => ({
      waitFor: prompts[index]!,
      input: responses[index]!,
      delay: Math.floor(Math.random() * 500) + 100
    }));
  }
  
  /**
   * 生成错误场景的测试数据
   */
  static generateErrorScenario(errorType: 'parse' | 'file' | 'config' | 'memory' | 'timeout'): {
    command: string;
    args: string[];
    expectedError: string;
    expectedExitCode: number;
  } {
    const scenarios = {
      parse: {
        command: 'cognitive-complexity',
        args: ['scan', 'invalid-syntax.ts'],
        expectedError: 'Parse error',
        expectedExitCode: 1
      },
      file: {
        command: 'cognitive-complexity',
        args: ['scan', 'non-existent-file.ts'],
        expectedError: 'File not found',
        expectedExitCode: 2
      },
      config: {
        command: 'cognitive-complexity',
        args: ['--config', 'invalid-config.js'],
        expectedError: 'Configuration error',
        expectedExitCode: 3
      },
      memory: {
        command: 'cognitive-complexity',
        args: ['scan', '--include', '**/*', '--no-cache'],
        expectedError: 'Memory limit exceeded',
        expectedExitCode: 4
      },
      timeout: {
        command: 'cognitive-complexity',
        args: ['scan', 'huge-project/', '--timeout', '100'],
        expectedError: 'Operation timed out',
        expectedExitCode: 5
      }
    };
    
    return scenarios[errorType];
  }
  
  /**
   * 生成压力测试数据
   */
  static generateStressTestData(complexity: 'light' | 'moderate' | 'heavy' = 'moderate'): {
    fileCount: number;
    functionCount: number;
    expectedDuration: number;
    expectedMemory: number;
  } {
    const configs = {
      light: { fileCount: 10, functionCount: 50, expectedDuration: 2000, expectedMemory: 50 * 1024 * 1024 },
      moderate: { fileCount: 100, functionCount: 500, expectedDuration: 10000, expectedMemory: 200 * 1024 * 1024 },
      heavy: { fileCount: 1000, functionCount: 5000, expectedDuration: 60000, expectedMemory: 500 * 1024 * 1024 }
    };
    
    return configs[complexity];
  }
  
  /**
   * 生成并发测试配置
   */
  static generateConcurrencyConfig(level: 'low' | 'medium' | 'high' = 'medium'): ConcurrencyTestConfig {
    const configs = {
      low: { concurrentCount: 3, testDuration: 5000 },
      medium: { concurrentCount: 8, testDuration: 10000 },
      high: { concurrentCount: 15, testDuration: 15000 }
    };
    
    const base = configs[level];
    return {
      ...base,
      memoryLimit: 300 * 1024 * 1024,
      cpuLimit: 85
    };
  }
  
  /**
   * 生成测试固件数据包
   */
  static generateTestFixturePackage(name: string): {
    name: string;
    files: Array<{ path: string; content: string }>;
    config: any;
    expectedResults: any;
  } {
    const fileCount = Math.floor(Math.random() * 10) + 3;
    
    return {
      name,
      files: Array.from({ length: fileCount }, (_, index) => ({
        path: `${name}/file${index}.ts`,
        content: this.generateTypeScriptCode(this.randomComplexity(5, 25))
      })),
      config: {
        threshold: 10,
        include: [`${name}/**/*.ts`],
        exclude: [`${name}/**/*.test.ts`]
      },
      expectedResults: {
        totalFiles: fileCount,
        avgComplexity: Math.floor(Math.random() * 20) + 5,
        highComplexityCount: Math.floor(Math.random() * 3)
      }
    };
  }
  
  /**
   * 生成指定复杂度的 TypeScript 代码
   */
  static generateTypeScriptCode(targetComplexity: number): string {
    let code = `function ${this.randomFunctionName()}() {\n`;
    let currentComplexity = 0;
    let indentLevel = 1;
    
    while (currentComplexity < targetComplexity) {
      const indent = '  '.repeat(indentLevel);
      const remaining = targetComplexity - currentComplexity;
      
      if (remaining >= 2 && Math.random() > 0.5) {
        // 添加 if 语句
        code += `${indent}if (condition${currentComplexity}) {\n`;
        indentLevel++;
        currentComplexity++;
      } else if (remaining >= 2 && Math.random() > 0.7) {
        // 添加循环
        code += `${indent}for (let i = 0; i < 10; i++) {\n`;
        indentLevel++;
        currentComplexity++;
      } else if (remaining >= 2 && Math.random() > 0.8) {
        // 添加逻辑运算符
        code += `${indent}if (a && b || c) {\n`;
        indentLevel++;
        currentComplexity += 3; // if(1) + &&(1) + ||(1)
      } else {
        // 添加简单语句
        code += `${indent}doSomething();\n`;
        if (indentLevel > 1 && Math.random() > 0.6) {
          indentLevel--;
          code += '  '.repeat(indentLevel) + '}\n';
        }
        currentComplexity++;
      }
    }
    
    // 关闭所有打开的块
    while (indentLevel > 1) {
      indentLevel--;
      code += '  '.repeat(indentLevel) + '}\n';
    }
    
    code += '}';
    return code;
  }
}

/**
 * 性能测试和监控类型定义
 */
export interface PerformanceMetrics {
  readonly executionTime: number;     // 测试执行时间（毫秒）
  readonly memoryUsage: {
    used: number;                     // 使用的内存（字节）
    total: number;                    // 总内存（字节）
    percentage: number;               // 内存使用百分比
  };
  readonly processInfo: {
    pid?: number;                     // 进程ID
    cpuUsage?: number;                // CPU使用率（百分比）
    uptime: number;                   // 运行时间（毫秒）
  };
  readonly testDetails: {
    scenario: string;                 // 测试场景名称
    iterations?: number;              // 迭代次数
    concurrency?: number;             // 并发数量
  };
}

export interface PerformanceBenchmark {
  readonly name: string;              // 基准名称
  readonly maxExecutionTime: number;  // 最大执行时间（毫秒）
  readonly maxMemoryUsage: number;    // 最大内存使用（字节）
  readonly maxCpuUsage: number;       // 最大CPU使用率（百分比）
  readonly description?: string;       // 基准描述
}

export interface ConcurrencyTestConfig {
  readonly concurrentCount: number;   // 并发测试数量
  readonly testDuration?: number;     // 测试持续时间（毫秒）
  readonly memoryLimit?: number;      // 内存限制（字节）
  readonly cpuLimit?: number;         // CPU限制（百分比）
}

export interface PerformanceTestResult {
  readonly benchmark: string;         // 基准名称
  readonly passed: boolean;           // 是否通过性能测试
  readonly metrics: PerformanceMetrics;
  readonly violations: string[];      // 性能违规信息
  readonly suggestions: string[];     // 性能优化建议
}

/**
 * 增强的性能测试工具
 * 集成了 CLI 测试框架的性能监控能力
 */
export class PerformanceTestUtils {
  private static timings: Map<string, number> = new Map();
  private static memorySnapshots: Map<string, NodeJS.MemoryUsage> = new Map();
  private static performanceBaselines: Map<string, PerformanceBenchmark> = new Map();
  
  // 默认性能基准
  private static readonly DEFAULT_BENCHMARKS: Record<string, PerformanceBenchmark> = {
    'cli-basic': {
      name: 'CLI Basic Command',
      maxExecutionTime: 5000,      // 5秒
      maxMemoryUsage: 100 * 1024 * 1024, // 100MB
      maxCpuUsage: 80,             // 80%
      description: '基本CLI命令执行性能基准'
    },
    'cli-complex': {
      name: 'CLI Complex Analysis',
      maxExecutionTime: 30000,     // 30秒
      maxMemoryUsage: 500 * 1024 * 1024, // 500MB
      maxCpuUsage: 90,             // 90%
      description: '复杂分析任务性能基准'
    },
    'concurrent-basic': {
      name: 'Concurrent Basic Tests',
      maxExecutionTime: 10000,     // 10秒
      maxMemoryUsage: 200 * 1024 * 1024, // 200MB
      maxCpuUsage: 85,             // 85%
      description: '基本并发测试性能基准'
    }
  };
  
  /**
   * 初始化性能基准
   */
  static initializeBenchmarks(): void {
    Object.entries(this.DEFAULT_BENCHMARKS).forEach(([key, benchmark]) => {
      this.performanceBaselines.set(key, benchmark);
    });
  }
  
  /**
   * 添加自定义性能基准
   */
  static addBenchmark(key: string, benchmark: PerformanceBenchmark): void {
    this.performanceBaselines.set(key, benchmark);
  }
  
  /**
   * 获取性能基准
   */
  static getBenchmark(key: string): PerformanceBenchmark | undefined {
    return this.performanceBaselines.get(key);
  }
  
  /**
   * 开始计时
   */
  static startTimer(name: string): void {
    this.timings.set(name, performance.now());
    this.memorySnapshots.set(name, process.memoryUsage());
  }
  
  /**
   * 结束计时并返回耗时（毫秒）
   */
  static endTimer(name: string): number {
    const startTime = this.timings.get(name);
    if (!startTime) {
      throw new Error(`Timer ${name} was not started`);
    }
    
    const duration = performance.now() - startTime;
    this.timings.delete(name);
    this.memorySnapshots.delete(name);
    return duration;
  }
  
  /**
   * 测量函数执行时间
   */
  static async measureAsync<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    return { result, duration };
  }
  
  /**
   * 测量同步函数执行时间
   */
  static measure<T>(fn: () => T): { result: T; duration: number } {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    return { result, duration };
  }
  
  /**
   * 验证性能基准
   */
  static expectPerformance(duration: number, maxMs: number, description: string): void {
    if (duration > maxMs) {
      console.warn(`Performance warning: ${description} took ${duration.toFixed(2)}ms (expected < ${maxMs}ms)`);
    }
    expect(duration).toBeLessThan(maxMs * 2); // 允许2倍的缓冲空间
  }
  
  /**
   * 测量 CLI 测试的性能指标
   */
  static async measureCLIPerformance(
    testFn: () => Promise<import('./cli-testing-utils').CLITestResult>,
    scenario: string = 'CLI Test'
  ): Promise<{ result: import('./cli-testing-utils').CLITestResult; metrics: PerformanceMetrics }> {
    const startTime = performance.now();
    const startMemory = process.memoryUsage();
    
    const result = await testFn();
    
    const endTime = performance.now();
    const endMemory = process.memoryUsage();
    const executionTime = endTime - startTime;
    
    // 计算内存使用情况
    const memoryUsed = endMemory.heapUsed - startMemory.heapUsed;
    const memoryTotal = endMemory.heapTotal;
    const memoryPercentage = (endMemory.heapUsed / memoryTotal) * 100;
    
    const metrics: PerformanceMetrics = {
      executionTime,
      memoryUsage: {
        used: memoryUsed,
        total: memoryTotal,
        percentage: memoryPercentage
      },
      processInfo: {
        pid: (result as any).getProcessId?.(),
        uptime: (result as any).getRuntime?.() || executionTime
      },
      testDetails: {
        scenario
      }
    };
    
    return { result, metrics };
  }
  
  /**
   * 运行 CLI 性能基准测试
   */
  static async runCLIBenchmark(
    testFn: () => Promise<import('./cli-testing-utils').CLITestResult>,
    benchmarkKey: string,
    scenario?: string
  ): Promise<PerformanceTestResult> {
    const benchmark = this.getBenchmark(benchmarkKey);
    if (!benchmark) {
      throw new Error(`Performance benchmark '${benchmarkKey}' not found. Available: ${Array.from(this.performanceBaselines.keys()).join(', ')}`);
    }
    
    const { result, metrics } = await this.measureCLIPerformance(testFn, scenario || benchmark.name);
    
    // 验证性能指标
    const violations: string[] = [];
    const suggestions: string[] = [];
    
    // 检查执行时间
    if (metrics.executionTime > benchmark.maxExecutionTime) {
      violations.push(`Execution time ${metrics.executionTime.toFixed(2)}ms exceeds limit ${benchmark.maxExecutionTime}ms`);
      suggestions.push('Consider optimizing command execution or increasing timeout');
    }
    
    // 检查内存使用
    if (metrics.memoryUsage.used > benchmark.maxMemoryUsage) {
      violations.push(`Memory usage ${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB exceeds limit ${(benchmark.maxMemoryUsage / 1024 / 1024).toFixed(2)}MB`);
      suggestions.push('Consider optimizing memory usage or processing data in chunks');
    }
    
    // 检查内存使用百分比
    if (metrics.memoryUsage.percentage > benchmark.maxCpuUsage) {
      violations.push(`Memory usage percentage ${metrics.memoryUsage.percentage.toFixed(2)}% is high`);
      suggestions.push('Monitor for potential memory leaks');
    }
    
    const passed = violations.length === 0;
    
    // 清理测试结果
    try {
      await result.kill();
    } catch (error) {
      console.warn('Warning during benchmark cleanup:', error);
    }
    
    return {
      benchmark: benchmark.name,
      passed,
      metrics,
      violations,
      suggestions
    };
  }
  
  /**
   * 运行并发性能测试
   */
  static async runConcurrencyTest(
    testFactory: () => Promise<import('./cli-testing-utils').CLITestResult>,
    config: ConcurrencyTestConfig,
    scenario: string = 'Concurrency Test'
  ): Promise<{
    results: PerformanceTestResult[];
    summary: {
      totalExecutionTime: number;
      averageExecutionTime: number;
      peakMemoryUsage: number;
      successRate: number;
      concurrentProcesses: number;
    };
  }> {
    const startTime = performance.now();
    const promises: Promise<{ result: import('./cli-testing-utils').CLITestResult; metrics: PerformanceMetrics }>[] = [];
    
    // 启动并发测试
    for (let i = 0; i < config.concurrentCount; i++) {
      const promise = this.measureCLIPerformance(
        testFactory,
        `${scenario} - Instance ${i + 1}`
      );
      promises.push(promise);
    }
    
    // 等待所有测试完成
    const concurrentResults = await Promise.allSettled(promises);
    const totalExecutionTime = performance.now() - startTime;
    
    // 处理结果
    const results: PerformanceTestResult[] = [];
    const successfulResults: PerformanceMetrics[] = [];
    let peakMemoryUsage = 0;
    
    for (let i = 0; i < concurrentResults.length; i++) {
      const settledResult = concurrentResults[i];
      
      if (settledResult!.status === 'fulfilled') {
        const { result, metrics } = settledResult.value;
        successfulResults.push(metrics);
        
        // 更新峰值内存使用
        peakMemoryUsage = Math.max(peakMemoryUsage, metrics.memoryUsage.used);
        
        // 验证单个测试的性能
        const benchmark = this.getBenchmark('concurrent-basic') || this.DEFAULT_BENCHMARKS['concurrent-basic'];
        const violations: string[] = [];
        const suggestions: string[] = [];
        
        if (metrics.executionTime > benchmark!.maxExecutionTime / config.concurrentCount) {
          violations.push(`Concurrent execution time too high: ${metrics.executionTime.toFixed(2)}ms`);
          suggestions.push('Consider reducing concurrent load or optimizing individual operations');
        }
        
        results.push({
          benchmark: `${benchmark!.name} - Instance ${i + 1}`,
          passed: violations.length === 0,
          metrics,
          violations,
          suggestions
        });
        
        // 清理测试结果
        try {
          await result.kill();
        } catch (error) {
          console.warn(`Warning cleaning up concurrent test ${i + 1}:`, error);
        }
      } else {
        // 处理失败的测试
        results.push({
          benchmark: `${scenario} - Instance ${i + 1}`,
          passed: false,
          metrics: {
            executionTime: 0,
            memoryUsage: { used: 0, total: 0, percentage: 0 },
            processInfo: { uptime: 0 },
            testDetails: { scenario: `${scenario} - Failed` }
          },
          violations: [`Test failed: ${settledResult.reason}`],
          suggestions: ['Check test configuration and system resources']
        });
      }
    }
    
    const successRate = (successfulResults.length / config.concurrentCount) * 100;
    const averageExecutionTime = successfulResults.length > 0 
      ? successfulResults.reduce((sum, m) => sum + m.executionTime, 0) / successfulResults.length
      : 0;
    
    return {
      results,
      summary: {
        totalExecutionTime,
        averageExecutionTime,
        peakMemoryUsage,
        successRate: config.concurrentCount === 0 ? 0 : successRate,
        concurrentProcesses: config.concurrentCount
      }
    };
  }
  
  /**
   * 生成性能测试报告
   */
  static generatePerformanceReport(
    results: PerformanceTestResult[],
    title: string = 'Performance Test Report'
  ): string {
    const passed = results.filter(r => r.passed).length;
    const total = results.length;
    const passRate = total > 0 ? (passed / total * 100).toFixed(1) : '0.0';
    
    let report = `\n=== ${title} ===\n`;
    report += `Overall: ${passed}/${total} tests passed (${passRate}%)\n\n`;
    
    results.forEach((result, index) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      report += `${index + 1}. ${result.benchmark} - ${status}\n`;
      report += `   Execution Time: ${result.metrics.executionTime.toFixed(2)}ms\n`;
      report += `   Memory Usage: ${(result.metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB (${result.metrics.memoryUsage.percentage.toFixed(1)}%)\n`;
      
      if (result.metrics.processInfo.pid) {
        report += `   Process ID: ${result.metrics.processInfo.pid}\n`;
      }
      
      if (result.violations.length > 0) {
        report += `   Violations:\n`;
        result.violations.forEach(v => report += `     • ${v}\n`);
      }
      
      if (result.suggestions.length > 0) {
        report += `   Suggestions:\n`;
        result.suggestions.forEach(s => report += `     • ${s}\n`);
      }
      
      report += '\n';
    });
    
    return report;
  }
  
  /**
   * 验证并发测试的系统资源监控
   */
  static async monitorSystemResources(
    duration: number = 5000,
    interval: number = 1000
  ): Promise<{
    samples: Array<{
      timestamp: number;
      memory: NodeJS.MemoryUsage;
      uptime: number;
    }>;
    peak: {
      memory: number;
      timestamp: number;
    };
    average: {
      heapUsed: number;
      heapTotal: number;
    };
  }> {
    const samples: Array<{
      timestamp: number;
      memory: NodeJS.MemoryUsage;
      uptime: number;
    }> = [];
    
    let peakMemory = 0;
    let peakTimestamp = 0;
    
    const startTime = Date.now();
    const endTime = startTime + duration;
    
    while (Date.now() < endTime) {
      const timestamp = Date.now();
      const memory = process.memoryUsage();
      const uptime = process.uptime() * 1000;
      
      samples.push({ timestamp, memory, uptime });
      
      if (memory.heapUsed > peakMemory) {
        peakMemory = memory.heapUsed;
        peakTimestamp = timestamp;
      }
      
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    const averageHeapUsed = samples.reduce((sum, s) => sum + s.memory.heapUsed, 0) / samples.length;
    const averageHeapTotal = samples.reduce((sum, s) => sum + s.memory.heapTotal, 0) / samples.length;
    
    return {
      samples,
      peak: {
        memory: peakMemory,
        timestamp: peakTimestamp
      },
      average: {
        heapUsed: averageHeapUsed,
        heapTotal: averageHeapTotal
      }
    };
  }
  
  /**
   * 获取当前性能统计信息
   */
  static getCurrentPerformanceStats(): {
    memory: NodeJS.MemoryUsage;
    uptime: number;
    activeTimers: number;
    loadAverage?: number[];
  } {
    return {
      memory: process.memoryUsage(),
      uptime: process.uptime() * 1000,
      activeTimers: this.timings.size,
      loadAverage: process.platform !== 'win32' ? require('os').loadavg() : undefined
    };
  }
  
  /**
   * 清理所有性能监控数据
   */
  static cleanup(): void {
    this.timings.clear();
    this.memorySnapshots.clear();
  }
  
  /**
   * 重置性能基准到默认值
   */
  static resetBenchmarks(): void {
    this.performanceBaselines.clear();
    this.initializeBenchmarks();
  }
}

// 初始化默认基准
PerformanceTestUtils.initializeBenchmarks();

/**
 * 规则引擎测试工具
 */
export class RuleTestUtils {
  /**
   * 创建测试分析上下文
   */
  static createTestContext(overrides?: Partial<AnalysisContext>): AnalysisContext {
    const cacheStorage = new Map<string, any>();
    
    const mockCache = {
      getCachedRuleResult: async (ruleId: string, key: string) => {
        return cacheStorage.get(`${ruleId}:${key}`) || null;
      },
      setCachedRuleResult: async (ruleId: string, key: string, result: any) => {
        cacheStorage.set(`${ruleId}:${key}`, result);
      },
      invalidateCache: () => {
        cacheStorage.clear();
      },
      getHitRate: () => 0
    };

    return {
      filePath: 'test.tsx',
      fileContent: 'const Component = () => <div />;',
      ast: {} as any,
      currentFunction: undefined,
      functionName: 'test',
      nestingLevel: 0,
      config: DEFAULT_ENGINE_CONFIG,
      jsxMode: 'standard',
      rules: {
        core: ['core.complexity'],
        jsx: ['jsx.structural.exemption', 'jsx.conditional.rendering'],
        plugins: []
      },
      cache: mockCache as any,
      metrics: {
        totalNodes: 0,
        processedNodes: 0,
        cacheHits: 0,
        cacheMisses: 0,
        ruleExecutions: 0,
        parallelExecutions: 0,
        errors: 0
      },
      plugins: [],
      customData: new Map(),
      ...overrides
    };
  }

  /**
   * 创建测试AST节点
   */
  static createTestNode(type: string, properties?: any): Node {
    return {
      type,
      span: { start: 0, end: 10, ctxt: 0 },
      ...properties
    } as Node;
  }
}

// 为了向后兼容，导出便捷函数
export const createTestContext = RuleTestUtils.createTestContext;
export const createTestNode = RuleTestUtils.createTestNode;

/**
 * 创建模拟的分析上下文 (向后兼容)
 */
export function createMockAnalysisContext(configOverrides?: any): AnalysisContext {
  return RuleTestUtils.createTestContext({
    config: {
      ...DEFAULT_ENGINE_CONFIG,
      rules: {
        ...DEFAULT_ENGINE_CONFIG.rules,
        jsx: {
          ...DEFAULT_ENGINE_CONFIG.rules.jsx,
          ...configOverrides?.jsx
        }
      }
    }
  });
}

/**
 * 创建模拟的异步规则引擎
 */
export function createMockAsyncRuleEngine(): any {
  const rules = new Map<string, any>();
  
  return {
    registerRule: (rule: any) => {
      rules.set(rule.id, rule);
    },
    unregisterRule: (ruleId: string) => {
      rules.delete(ruleId);
    },
    getRules: () => Array.from(rules.values()),
    getRule: (ruleId: string) => rules.get(ruleId),
    analyzeNode: async (node: Node, context: any) => {
      return {
        node,
        complexity: 0,
        appliedRules: [],
        exemptions: [],
        children: [],
        aggregatedComplexity: 0,
        analysisTime: 0,
        cacheUtilization: 0,
      };
    },
    analyzeFunction: async (func: any) => {
      return {
        functionName: 'test',
        totalComplexity: 0,
        nodeAnalyses: [],
        location: { line: 1, column: 0, filePath: 'test.ts' },
        metrics: {
          totalNodes: 0,
          processedNodes: 0,
          cacheHits: 0,
          cacheMisses: 0,
          ruleExecutions: 0,
          parallelExecutions: 0,
          errors: 0,
        },
      };
    },
    analyzeFile: async (ast: any) => {
      return {
        filePath: 'test.ts',
        functions: [],
        totalComplexity: 0,
        averageComplexity: 0,
        analysisTime: 0,
        cacheHitRate: 0,
      };
    },
  };
}