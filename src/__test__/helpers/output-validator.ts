import type { CLITestResult } from './cli-testing-utils';

/**
 * 输出验证选项
 */
export interface OutputValidationOptions {
  readonly caseSensitive?: boolean;
  readonly multiline?: boolean;
  readonly fuzzyThreshold?: number; // 0-1, 用于模糊匹配
  readonly debug?: boolean;
}

/**
 * 验证结果接口
 */
export interface ValidationResult {
  readonly passed: boolean;
  readonly message?: string;
  readonly matches?: string[];
  readonly debugInfo?: DebugInfo;
}

/**
 * 调试信息接口
 */
export interface DebugInfo {
  readonly actualContent: string;
  readonly searchPattern: string | RegExp;
  readonly matches: RegExpMatchArray[];
  readonly suggestions?: string[];
}

/**
 * 表格验证选项
 */
export interface TableValidationOptions extends OutputValidationOptions {
  readonly delimiter?: string;
  readonly hasHeader?: boolean;
  readonly ignoreEmptyRows?: boolean;
}

/**
 * JSON 验证选项
 */
export interface JSONValidationOptions extends OutputValidationOptions {
  readonly allowPartial?: boolean;
  readonly strictMode?: boolean;
}

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  readonly executionTime?: number;
  readonly memoryUsage?: number;
  readonly filesProcessed?: number;
  readonly functionsAnalyzed?: number;
  readonly averageComplexity?: number;
  readonly totalComplexity?: number;
  readonly highComplexityFunctions?: number;
  readonly cacheHitRate?: number;
  readonly processedNodes?: number;
  readonly ruleExecutions?: number;
}

/**
 * 高级性能验证选项
 */
export interface AdvancedPerformanceOptions extends OutputValidationOptions {
  readonly expectedMetrics?: Partial<PerformanceMetrics>;
  readonly tolerancePercent?: number; // 容忍百分比
  readonly requireAllMetrics?: boolean; // 是否要求所有指标都存在
}

/**
 * 多行输出验证选项
 */
export interface MultilineValidationOptions extends OutputValidationOptions {
  readonly preserveWhitespace?: boolean;
  readonly ignoreEmptyLines?: boolean;
  readonly lineByLine?: boolean; // 逐行验证模式
  readonly sectionDelimiter?: string | RegExp; // 段落分隔符
}

/**
 * 文本差异对比结果
 */
export interface DiffResult {
  readonly identical: boolean;
  readonly additions: string[];
  readonly deletions: string[];
  readonly modifications: Array<{
    line: number;
    expected: string;
    actual: string;
    similarity: number;
  }>;
  readonly summary: {
    addedLines: number;
    deletedLines: number;
    modifiedLines: number;
    totalLines: number;
  };
}

/**
 * 输出验证器类
 * 提供强大的CLI输出验证和查询能力
 */
export class OutputValidator {
  private static readonly DEFAULT_OPTIONS: OutputValidationOptions = {
    caseSensitive: false,
    multiline: false,
    fuzzyThreshold: 0.8,
    debug: false
  };

  /**
   * 在输出内容中查找匹配的文本
   */
  static async findInOutput(
    output: string,
    pattern: string | RegExp,
    options: OutputValidationOptions = {}
  ): Promise<string[]> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      let regex: RegExp;
      
      if (typeof pattern === 'string') {
        const flags = this.buildRegexFlags(finalOptions);
        // 转义特殊字符，除非已经是正则表达式格式
        const escapedPattern = pattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        regex = new RegExp(escapedPattern, flags);
      } else {
        regex = pattern;
      }

      // 确保全局标志存在，避免重复添加
      const globalFlags = regex.flags.includes('g') ? regex.flags : regex.flags + 'g';
      const matches = Array.from(output.matchAll(new RegExp(regex.source, globalFlags)));
      return matches.map(match => match[0]);
    } catch (error) {
      if (finalOptions.debug) {
        console.debug('FindInOutput error:', error);
        console.debug('Pattern:', pattern);
        console.debug('Output preview:', output.substring(0, 200));
      }
      return [];
    }
  }

  /**
   * 断言输出包含期望的文本
   */
  static async assertOutputContains(
    output: string,
    expected: string[],
    options: OutputValidationOptions = {}
  ): Promise<ValidationResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    const matches: string[] = [];
    const missing: string[] = [];
    
    for (const expectedText of expected) {
      const found = await this.findInOutput(output, expectedText, finalOptions);
      if (found.length > 0) {
        matches.push(...found);
      } else {
        missing.push(expectedText);
      }
    }

    const passed = missing.length === 0;
    
    return {
      passed,
      message: passed ? undefined : `Missing expected text: ${missing.join(', ')}`,
      matches,
      debugInfo: finalOptions.debug ? this.createDebugInfo(output, expected, matches) : undefined
    };
  }

  /**
   * 断言输出匹配正则表达式
   */
  static async assertOutputMatches(
    output: string,
    pattern: RegExp,
    options: OutputValidationOptions = {}
  ): Promise<ValidationResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      const matches = await this.findInOutput(output, pattern, finalOptions);
      const passed = matches.length > 0;
      
      return {
        passed,
        message: passed ? undefined : `Output does not match pattern: ${pattern}`,
        matches,
        debugInfo: finalOptions.debug ? this.createDebugInfo(output, pattern, matches) : undefined
      };
    } catch (error) {
      return {
        passed: false,
        message: `Pattern matching failed: ${error instanceof Error ? error.message : String(error)}`,
        matches: [],
        debugInfo: finalOptions.debug ? this.createDebugInfo(output, pattern, []) : undefined
      };
    }
  }

  /**
   * 验证JSON格式的输出
   */
  static async validateJSONOutput(
    output: string,
    schema: object,
    options: JSONValidationOptions = {}
  ): Promise<ValidationResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      // 提取JSON内容
      const jsonMatch = output.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        return {
          passed: false,
          message: 'No JSON content found in output',
          debugInfo: finalOptions.debug ? this.createDebugInfo(output, 'JSON', []) : undefined
        };
      }

      const jsonData = JSON.parse(jsonMatch[0]);
      
      // 基础schema验证
      const validationResult = this.validateObjectAgainstSchema(jsonData, schema, finalOptions);
      
      return {
        passed: validationResult.passed,
        message: validationResult.message,
        matches: validationResult.passed ? [jsonMatch[0]] : [],
        debugInfo: finalOptions.debug ? {
          actualContent: output,
          searchPattern: 'JSON Schema',
          matches: [],
          suggestions: validationResult.suggestions
        } : undefined
      };
    } catch (error) {
      return {
        passed: false,
        message: `JSON validation failed: ${error instanceof Error ? error.message : String(error)}`,
        matches: [],
        debugInfo: finalOptions.debug ? this.createDebugInfo(output, 'JSON', []) : undefined
      };
    }
  }

  /**
   * 验证表格格式的输出
   */
  static async validateTableOutput(
    output: string,
    expectedColumns: string[],
    options: TableValidationOptions = {}
  ): Promise<ValidationResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    const delimiter = options.delimiter || '\\s+';
    
    try {
      const lines = output.split('\n').filter(line => {
        const trimmed = line.trim();
        return trimmed.length > 0 && (options.ignoreEmptyRows !== false ? trimmed !== '' : true);
      });

      if (lines.length === 0) {
        return {
          passed: false,
          message: 'No table content found',
          debugInfo: finalOptions.debug ? this.createDebugInfo(output, 'Table', []) : undefined
        };
      }

      // 分析表头
      const headerLine = lines[0];
      const actualColumns = headerLine.split(new RegExp(delimiter)).map(col => col.trim());
      
      // 验证列
      const missingColumns = expectedColumns.filter(col => {
        return !actualColumns.some(actualCol => 
          finalOptions.caseSensitive ? actualCol === col : actualCol.toLowerCase() === col.toLowerCase()
        );
      });

      const passed = missingColumns.length === 0;
      
      return {
        passed,
        message: passed ? undefined : `Missing table columns: ${missingColumns.join(', ')}`,
        matches: passed ? lines : [],
        debugInfo: finalOptions.debug ? {
          actualContent: output,
          searchPattern: `Table with columns: ${expectedColumns.join(', ')}`,
          matches: [],
          suggestions: [`Found columns: ${actualColumns.join(', ')}`]
        } : undefined
      };
    } catch (error) {
      return {
        passed: false,
        message: `Table validation failed: ${error instanceof Error ? error.message : String(error)}`,
        matches: [],
        debugInfo: finalOptions.debug ? this.createDebugInfo(output, 'Table', []) : undefined
      };
    }
  }

  /**
   * 从输出中提取性能指标 - 增强版
   */
  static async extractMetrics(output: string): Promise<PerformanceMetrics> {
    const metrics: PerformanceMetrics = {};
    
    // 提取执行时间 - 支持更多格式
    const timeMatches = [
      /(?:in|took|time|duration|elapsed):\s*(\d+(?:\.\d+)?)\s*(ms|milliseconds|s|seconds)/gi,
      /(\d+(?:\.\d+)?)\s*(ms|milliseconds|s|seconds)(?:\s*elapsed)?/gi,
      /execution\s*time:\s*(\d+(?:\.\d+)?)\s*(ms|milliseconds|s|seconds)/gi
    ];
    
    for (const regex of timeMatches) {
      const match = regex.exec(output);
      if (match) {
        const value = parseFloat(match[1]);
        const unit = match[2].toLowerCase();
        metrics.executionTime = unit.startsWith('s') ? value * 1000 : value;
        break;
      }
    }

    // 提取内存使用 - 支持更多格式
    const memoryMatches = [
      /memory\s*(?:usage)?:\s*(\d+(?:\.\d+)?)\s*(mb|kb|gb|bytes?)/gi,
      /(?:heap|mem)\s*used:\s*(\d+(?:\.\d+)?)\s*(mb|kb|gb|bytes?)/gi,
      /peak\s*memory:\s*(\d+(?:\.\d+)?)\s*(mb|kb|gb|bytes?)/gi
    ];
    
    for (const regex of memoryMatches) {
      const match = regex.exec(output);
      if (match) {
        const value = parseFloat(match[1]);
        const unit = match[2].toLowerCase();
        let bytes = value;
        if (unit.includes('kb')) bytes *= 1024;
        else if (unit.includes('mb')) bytes *= 1024 * 1024;
        else if (unit.includes('gb')) bytes *= 1024 * 1024 * 1024;
        metrics.memoryUsage = bytes;
        break;
      }
    }

    // 提取文件处理数量
    const filesMatches = [
      /(?:processed|analyzed|scanned|with)\s*(\d+)\s*files?/gi,
      /(\d+)\s*files?\s*(?:processed|analyzed|scanned)/gi
    ];
    
    for (const regex of filesMatches) {
      regex.lastIndex = 0; // 重置正则表达式状态
      const match = regex.exec(output);
      if (match) {
        metrics.filesProcessed = parseInt(match[1], 10);
        break;
      }
    }

    // 提取函数分析数量
    const functionsMatches = [
      /(?:analyzed|found|processed)\s*(\d+)\s*functions?/gi,
      /(\d+)\s*functions?\s*(?:analyzed|found|processed)/gi
    ];
    
    for (const regex of functionsMatches) {
      regex.lastIndex = 0; // 重置正则表达式状态
      const match = regex.exec(output);
      if (match) {
        metrics.functionsAnalyzed = parseInt(match[1], 10);
        break;
      }
    }

    // 提取平均复杂度
    const avgComplexityMatch = output.match(/average\s*complexity:\s*(\d+(?:\.\d+)?)/gi);
    if (avgComplexityMatch) {
      metrics.averageComplexity = parseFloat(avgComplexityMatch[avgComplexityMatch.length - 1].match(/(\d+(?:\.\d+)?)/)[1]);
    }

    // 提取总复杂度
    const totalComplexityMatch = output.match(/total\s*complexity:\s*(\d+(?:\.\d+)?)/gi);
    if (totalComplexityMatch) {
      metrics.totalComplexity = parseFloat(totalComplexityMatch[totalComplexityMatch.length - 1].match(/(\d+(?:\.\d+)?)/)[1]);
    }

    // 提取高复杂度函数数量
    const highComplexityMatch = output.match(/(\d+)\s*(?:high\s*complexity\s*functions?|functions?\s*exceed)/gi);
    if (highComplexityMatch) {
      metrics.highComplexityFunctions = parseInt(highComplexityMatch[highComplexityMatch.length - 1].match(/(\d+)/)[1], 10);
    }

    // 提取缓存命中率
    const cacheHitMatch = output.match(/cache\s*hit\s*rate:\s*(\d+(?:\.\d+)?)\s*%/gi);
    if (cacheHitMatch) {
      metrics.cacheHitRate = parseFloat(cacheHitMatch[cacheHitMatch.length - 1].match(/(\d+(?:\.\d+)?)/)[1]);
    }

    // 提取处理的节点数量
    const nodesMatches = [
      /(?:processed|parsed)\s*(\d+)\s*nodes?/gi,
      /(\d+)\s*nodes?\s*(?:processed|parsed)/gi
    ];
    
    for (const regex of nodesMatches) {
      regex.lastIndex = 0; // 重置正则表达式状态
      const match = regex.exec(output);
      if (match) {
        metrics.processedNodes = parseInt(match[1], 10);
        break;
      }
    }

    // 提取规则执行次数
    const rulesMatches = [
      /(\d+)\s*rule\s*executions?/gi,
      /rule\s*executions?:\s*(\d+)/gi
    ];
    
    for (const regex of rulesMatches) {
      regex.lastIndex = 0; // 重置正则表达式状态
      const match = regex.exec(output);
      if (match) {
        metrics.ruleExecutions = parseInt(match[1], 10);
        break;
      }
    }

    return metrics;
  }

  /**
   * 高级性能指标验证 - 支持容忍度和多指标比较
   */
  static async validateAdvancedPerformance(
    output: string,
    options: AdvancedPerformanceOptions = {}
  ): Promise<ValidationResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    const tolerancePercent = options.tolerancePercent || 10;
    
    try {
      const actualMetrics = await this.extractMetrics(output);
      const expectedMetrics = options.expectedMetrics || {};
      
      const violations: string[] = [];
      const validations: string[] = [];
      
      // 验证每个期望的指标
      for (const [key, expectedValue] of Object.entries(expectedMetrics)) {
        const actualValue = actualMetrics[key as keyof PerformanceMetrics];
        
        if (actualValue === undefined) {
          if (options.requireAllMetrics) {
            violations.push(`Missing required metric: ${key}`);
          }
          continue;
        }
        
        if (typeof expectedValue === 'number' && typeof actualValue === 'number') {
          const tolerance = expectedValue * (tolerancePercent / 100);
          const isWithinTolerance = Math.abs(actualValue - expectedValue) <= tolerance;
          
          if (isWithinTolerance) {
            validations.push(`✓ ${key}: ${actualValue} (expected: ${expectedValue} ±${tolerancePercent}%)`);
          } else {
            violations.push(`✗ ${key}: ${actualValue} exceeds tolerance for expected ${expectedValue} (±${tolerancePercent}%)`);
          }
        }
      }
      
      const passed = violations.length === 0;
      
      return {
        passed,
        message: passed ? 
          `All performance metrics within tolerance (${validations.length} checked)` :
          `Performance violations: ${violations.join('; ')}`,
        matches: validations,
        debugInfo: finalOptions.debug ? {
          actualContent: JSON.stringify(actualMetrics, null, 2),
          searchPattern: 'Advanced Performance Validation',
          matches: [],
          suggestions: violations.length > 0 ? violations : ['All metrics within expected ranges']
        } : undefined
      };
    } catch (error) {
      return {
        passed: false,
        message: `Advanced performance validation failed: ${error instanceof Error ? error.message : String(error)}`,
        matches: [],
        debugInfo: finalOptions.debug ? this.createDebugInfo(output, 'Advanced Performance', []) : undefined
      };
    }
  }

  /**
   * 多行输出的结构化验证
   */
  static async validateMultilineStructure(
    output: string,
    expectedStructure: {
      sections?: Array<{
        name: string;
        patterns: (string | RegExp)[];
        required?: boolean;
        order?: number;
      }>;
      linePatterns?: (string | RegExp)[];
      minLines?: number;
      maxLines?: number;
    },
    options: MultilineValidationOptions = {}
  ): Promise<ValidationResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      // 预处理输出
      let processedOutput = output;
      if (options.ignoreEmptyLines) {
        processedOutput = output.split('\n').filter(line => line.trim().length > 0).join('\n');
      }
      if (!options.preserveWhitespace) {
        processedOutput = processedOutput.replace(/\s+/g, ' ').trim();
      }
      
      const lines = processedOutput.split('\n');
      const violations: string[] = [];
      const validations: string[] = [];
      
      // 验证行数限制
      if (expectedStructure.minLines && lines.length < expectedStructure.minLines) {
        violations.push(`Too few lines: ${lines.length} < ${expectedStructure.minLines}`);
      }
      if (expectedStructure.maxLines && lines.length > expectedStructure.maxLines) {
        violations.push(`Too many lines: ${lines.length} > ${expectedStructure.maxLines}`);
      }
      
      // 验证行模式
      if (expectedStructure.linePatterns) {
        for (let i = 0; i < Math.min(lines.length, expectedStructure.linePatterns.length); i++) {
          const line = lines[i];
          const pattern = expectedStructure.linePatterns[i];
          
          const matches = await this.findInOutput(line, pattern, finalOptions);
          if (matches.length === 0) {
            violations.push(`Line ${i + 1} does not match expected pattern: ${pattern}`);
          } else {
            validations.push(`✓ Line ${i + 1} matches pattern`);
          }
        }
      }
      
      // 验证段落结构
      if (expectedStructure.sections) {
        const delimiter = options.sectionDelimiter || /\n\s*\n/;
        const sections = typeof delimiter === 'string' ? 
          processedOutput.split(delimiter) : 
          processedOutput.split(delimiter);
        
        // 按顺序验证段落
        const orderedSections = expectedStructure.sections.sort((a, b) => (a.order || 0) - (b.order || 0));
        
        for (const expectedSection of orderedSections) {
          let sectionFound = false;
          
          for (const section of sections) {
            let allPatternsMatch = true;
            
            for (const pattern of expectedSection.patterns) {
              const matches = await this.findInOutput(section, pattern, finalOptions);
              if (matches.length === 0) {
                allPatternsMatch = false;
                break;
              }
            }
            
            if (allPatternsMatch) {
              sectionFound = true;
              validations.push(`✓ Found section: ${expectedSection.name}`);
              break;
            }
          }
          
          if (!sectionFound && expectedSection.required !== false) {
            violations.push(`Missing required section: ${expectedSection.name}`);
          }
        }
      }
      
      const passed = violations.length === 0;
      
      return {
        passed,
        message: passed ? 
          `Multiline structure validation passed (${validations.length} checks)` :
          `Structure violations: ${violations.join('; ')}`,
        matches: validations,
        debugInfo: finalOptions.debug ? {
          actualContent: processedOutput.substring(0, 500),
          searchPattern: 'Multiline Structure',
          matches: [],
          suggestions: violations.length > 0 ? violations : ['Structure matches expected format']
        } : undefined
      };
    } catch (error) {
      return {
        passed: false,
        message: `Multiline validation failed: ${error instanceof Error ? error.message : String(error)}`,
        matches: [],
        debugInfo: finalOptions.debug ? this.createDebugInfo(output, 'Multiline', []) : undefined
      };
    }
  }

  /**
   * 输出差异对比功能
   */
  static async compareOutputs(
    expected: string,
    actual: string,
    options: OutputValidationOptions = {}
  ): Promise<DiffResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    const expectedLines = expected.split('\n');
    const actualLines = actual.split('\n');
    
    const additions: string[] = [];
    const deletions: string[] = [];
    const modifications: DiffResult['modifications'] = [];
    
    const maxLines = Math.max(expectedLines.length, actualLines.length);
    
    for (let i = 0; i < maxLines; i++) {
      const expectedLine = expectedLines[i];
      const actualLine = actualLines[i];
      
      if (expectedLine === undefined) {
        // 实际输出有额外行
        additions.push(actualLine);
      } else if (actualLine === undefined) {
        // 期望输出有缺失行
        deletions.push(expectedLine);
      } else if (expectedLine !== actualLine) {
        // 行内容不同
        const similarity = this.calculateSimilarity(expectedLine, actualLine);
        modifications.push({
          line: i + 1,
          expected: expectedLine,
          actual: actualLine,
          similarity
        });
      }
    }
    
    const identical = additions.length === 0 && deletions.length === 0 && modifications.length === 0;
    
    return {
      identical,
      additions,
      deletions,
      modifications,
      summary: {
        addedLines: additions.length,
        deletedLines: deletions.length,
        modifiedLines: modifications.length,
        totalLines: maxLines
      }
    };
  }

  /**
   * 高级文本模式匹配 - 支持复杂查询
   */
  static async advancedPatternMatch(
    output: string,
    queries: Array<{
      name: string;
      pattern: string | RegExp;
      type: 'exact' | 'fuzzy' | 'regex' | 'contains';
      options?: {
        minMatches?: number;
        maxMatches?: number;
        fuzzyThreshold?: number;
        caseSensitive?: boolean;
      };
    }>,
    options: OutputValidationOptions = {}
  ): Promise<{
    results: Array<{
      name: string;
      matches: string[];
      passed: boolean;
      message: string;
    }>;
    overallPassed: boolean;
  }> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    const results: Array<{
      name: string;
      matches: string[];
      passed: boolean;
      message: string;
    }> = [];
    
    for (const query of queries) {
      const queryOptions = { ...finalOptions, ...query.options };
      let matches: string[] = [];
      let passed = false;
      let message = '';
      
      try {
        switch (query.type) {
          case 'exact':
            matches = await this.findInOutput(output, query.pattern as string, queryOptions);
            break;
            
          case 'fuzzy':
            const fuzzyResult = await this.fuzzyMatch(
              output, 
              query.pattern as string, 
              query.options?.fuzzyThreshold || 0.8, 
              queryOptions
            );
            matches = fuzzyResult.matches || [];
            break;
            
          case 'regex':
            matches = await this.findInOutput(output, query.pattern as RegExp, queryOptions);
            break;
            
          case 'contains':
            const containsResult = await this.assertOutputContains(
              output, 
              [query.pattern as string], 
              queryOptions
            );
            matches = containsResult.matches || [];
            break;
        }
        
        // 验证匹配数量
        const minMatches = query.options?.minMatches || 1;
        const maxMatches = query.options?.maxMatches || Infinity;
        
        if (matches.length < minMatches) {
          passed = false;
          message = `Too few matches: ${matches.length} < ${minMatches}`;
        } else if (matches.length > maxMatches) {
          passed = false;
          message = `Too many matches: ${matches.length} > ${maxMatches}`;
        } else {
          passed = true;
          message = `Found ${matches.length} matches`;
        }
        
      } catch (error) {
        passed = false;
        message = `Pattern matching failed: ${error instanceof Error ? error.message : String(error)}`;
      }
      
      results.push({
        name: query.name,
        matches,
        passed,
        message
      });
    }
    
    const overallPassed = results.every(r => r.passed);
    
    return {
      results,
      overallPassed
    };
  }

  /**
   * 验证性能输出是否在期望范围内
   */
  static async validatePerformanceOutput(
    output: string,
    maxTime: number,
    options: OutputValidationOptions = {}
  ): Promise<ValidationResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      const metrics = await this.extractMetrics(output);
      
      if (metrics.executionTime === undefined) {
        return {
          passed: false,
          message: 'No execution time found in output',
          debugInfo: finalOptions.debug ? this.createDebugInfo(output, 'Performance', []) : undefined
        };
      }

      const passed = metrics.executionTime <= maxTime;
      
      return {
        passed,
        message: passed ? undefined : `Execution time ${metrics.executionTime}ms exceeds maximum ${maxTime}ms`,
        matches: passed ? [String(metrics.executionTime)] : [],
        debugInfo: finalOptions.debug ? {
          actualContent: output,
          searchPattern: `Performance <= ${maxTime}ms`,
          matches: [],
          suggestions: [`Actual: ${metrics.executionTime}ms`]
        } : undefined
      };
    } catch (error) {
      return {
        passed: false,
        message: `Performance validation failed: ${error instanceof Error ? error.message : String(error)}`,
        matches: [],
        debugInfo: finalOptions.debug ? this.createDebugInfo(output, 'Performance', []) : undefined
      };
    }
  }

  /**
   * 模糊匹配功能 - 查找相似的文本
   */
  static async fuzzyMatch(
    output: string,
    target: string,
    threshold: number = 0.8,
    options: OutputValidationOptions = {}
  ): Promise<ValidationResult> {
    const finalOptions = { ...this.DEFAULT_OPTIONS, ...options };
    
    try {
      const words = output.split(/\s+/);
      const matches: string[] = [];
      
      for (const word of words) {
        const similarity = this.calculateSimilarity(word, target);
        if (similarity >= threshold) {
          matches.push(word);
        }
      }

      const passed = matches.length > 0;
      
      return {
        passed,
        message: passed ? undefined : `No fuzzy matches found for "${target}" (threshold: ${threshold})`,
        matches,
        debugInfo: finalOptions.debug ? {
          actualContent: output,
          searchPattern: `Fuzzy: "${target}" (${threshold})`,
          matches: [],
          suggestions: matches.slice(0, 5)
        } : undefined
      };
    } catch (error) {
      return {
        passed: false,
        message: `Fuzzy matching failed: ${error instanceof Error ? error.message : String(error)}`,
        matches: [],
        debugInfo: finalOptions.debug ? this.createDebugInfo(output, `Fuzzy: ${target}`, []) : undefined
      };
    }
  }

  /**
   * 为CLI测试结果提供便捷的验证方法
   */
  static async validateCLIResult(
    result: CLITestResult,
    validations: {
      contains?: string[];
      matches?: RegExp[];
      json?: object;
      table?: string[];
      performance?: number;
      fuzzy?: { target: string; threshold?: number }[];
    },
    options: OutputValidationOptions = {}
  ): Promise<ValidationResult[]> {
    const results: ValidationResult[] = [];
    const output = result.stdout + result.stderr;

    // 包含验证
    if (validations.contains) {
      const containsResult = await this.assertOutputContains(output, validations.contains, options);
      results.push(containsResult);
    }

    // 正则匹配验证
    if (validations.matches) {
      for (const pattern of validations.matches) {
        const matchResult = await this.assertOutputMatches(output, pattern, options);
        results.push(matchResult);
      }
    }

    // JSON验证
    if (validations.json) {
      const jsonResult = await this.validateJSONOutput(output, validations.json, options);
      results.push(jsonResult);
    }

    // 表格验证
    if (validations.table) {
      const tableResult = await this.validateTableOutput(output, validations.table, options);
      results.push(tableResult);
    }

    // 性能验证
    if (validations.performance) {
      const perfResult = await this.validatePerformanceOutput(output, validations.performance, options);
      results.push(perfResult);
    }

    // 模糊匹配验证
    if (validations.fuzzy) {
      for (const fuzzy of validations.fuzzy) {
        const fuzzyResult = await this.fuzzyMatch(output, fuzzy.target, fuzzy.threshold, options);
        results.push(fuzzyResult);
      }
    }

    return results;
  }

  // =============== 私有辅助方法 ===============

  private static buildRegexFlags(options: OutputValidationOptions): string {
    let flags = 'g'; // 全局匹配
    if (!options.caseSensitive) flags += 'i';
    if (options.multiline) flags += 'm';
    return flags;
  }

  private static createDebugInfo(
    content: string,
    pattern: string | RegExp,
    matches: string[]
  ): DebugInfo {
    return {
      actualContent: content.length > 500 ? content.substring(0, 500) + '...' : content,
      searchPattern: pattern,
      matches: [], // 可以在需要时填充具体的匹配信息
      suggestions: matches.length > 0 ? matches.slice(0, 3) : ['No matches found']
    };
  }

  private static validateObjectAgainstSchema(
    obj: any,
    schema: any,
    options: JSONValidationOptions
  ): { passed: boolean; message?: string; suggestions?: string[] } {
    try {
      // 简单的schema验证实现
      for (const [key, expectedValue] of Object.entries(schema)) {
        if (!(key in obj)) {
          return {
            passed: false,
            message: `Missing required property: ${key}`,
            suggestions: [`Available properties: ${Object.keys(obj).join(', ')}`]
          };
        }

        if (typeof expectedValue === 'object' && expectedValue !== null) {
          // 递归验证嵌套对象
          const nestedResult = this.validateObjectAgainstSchema(obj[key], expectedValue, options);
          if (!nestedResult.passed) {
            return nestedResult;
          }
        } else if (typeof obj[key] !== typeof expectedValue) {
          return {
            passed: false,
            message: `Type mismatch for property ${key}: expected ${typeof expectedValue}, got ${typeof obj[key]}`,
            suggestions: [`Actual value: ${JSON.stringify(obj[key])}`]
          };
        }
      }

      return { passed: true };
    } catch (error) {
      return {
        passed: false,
        message: `Schema validation error: ${error instanceof Error ? error.message : String(error)}`
      };
    }
  }

  private static calculateSimilarity(str1: string, str2: string): number {
    // 简单的 Levenshtein 距离算法
    const matrix: number[][] = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 1 : (maxLength - matrix[str2.length][str1.length]) / maxLength;
  }
}

/**
 * 调试助手类 - 提供输出内容检查和调试工具
 */
export class OutputDebugHelper {
  /**
   * 打印输出内容的详细信息
   */
  static debugOutput(output: string, label: string = 'Output Debug'): void {
    console.log(`\n===== ${label} =====`);
    console.log(`Length: ${output.length} characters`);
    console.log(`Lines: ${output.split('\n').length}`);
    console.log(`First 200 chars: ${output.substring(0, 200)}`);
    if (output.length > 200) {
      console.log(`Last 200 chars: ${output.substring(output.length - 200)}`);
    }
    console.log('==================\n');
  }

  /**
   * 搜索输出中的关键词并高亮显示
   */
  static highlightKeywords(output: string, keywords: string[]): string {
    let highlighted = output;
    
    for (const keyword of keywords) {
      const regex = new RegExp(`(${keyword})`, 'gi');
      highlighted = highlighted.replace(regex, '【$1】');
    }
    
    return highlighted;
  }

  /**
   * 分析输出内容的统计信息
   */
  static analyzeOutput(output: string): {
    totalLength: number;
    lineCount: number;
    wordCount: number;
    hasJSON: boolean;
    hasTable: boolean;
    hasNumbers: boolean;
    commonWords: string[];
    performanceMetrics: PerformanceMetrics;
    structuralElements: {
      hasHeaders: boolean;
      hasBulletPoints: boolean;
      hasCodeBlocks: boolean;
      hasErrorMessages: boolean;
    };
  } {
    const lines = output.split('\n');
    const words = output.split(/\s+/).filter(w => w.length > 0);
    
    // 统计词频
    const wordFreq = new Map<string, number>();
    words.forEach(word => {
      const cleaned = word.toLowerCase().replace(/[^\w]/g, '');
      if (cleaned.length > 2) {
        wordFreq.set(cleaned, (wordFreq.get(cleaned) || 0) + 1);
      }
    });
    
    const commonWords = Array.from(wordFreq.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);

    // 异步提取性能指标（这里简化处理）
    const performanceMetrics: PerformanceMetrics = {};
    
    // 检测结构元素
    const structuralElements = {
      hasHeaders: /^#{1,6}\s/.test(output) || /^[A-Z][A-Za-z\s]+:?\s*$/m.test(output),
      hasBulletPoints: /^\s*[-*+]\s/.test(output) || /^\s*\d+\.\s/.test(output),    
      hasCodeBlocks: /```[\s\S]*```/.test(output) || /`[^`]+`/.test(output),
      hasErrorMessages: /error|fail|exception|warning/i.test(output)
    };

    return {
      totalLength: output.length,
      lineCount: lines.length,
      wordCount: words.length,
      hasJSON: /\{[\s\S]*\}/.test(output),
      hasTable: /\|.*\|/.test(output) || lines.some(line => /\s+\w+\s+\w+/.test(line)),
      hasNumbers: /\d+/.test(output),
      commonWords,
      performanceMetrics,
      structuralElements
    };
  }

  /**
   * 生成详细的输出差异报告
   */
  static generateDiffReport(diffResult: DiffResult, title: string = 'Output Comparison'): string {
    let report = `\n===== ${title} =====\n`;
    
    if (diffResult.identical) {
      report += '✅ Outputs are identical\n';
      return report;
    }
    
    report += `📊 Summary:\n`;
    report += `  • Added lines: ${diffResult.summary.addedLines}\n`;
    report += `  • Deleted lines: ${diffResult.summary.deletedLines}\n`;
    report += `  • Modified lines: ${diffResult.summary.modifiedLines}\n`;
    report += `  • Total lines processed: ${diffResult.summary.totalLines}\n\n`;
    
    if (diffResult.additions.length > 0) {
      report += `➕ Additions (${diffResult.additions.length}):\n`;
      diffResult.additions.forEach((line, i) => {
        report += `  ${i + 1}: + ${line}\n`;
      });
      report += '\n';
    }
    
    if (diffResult.deletions.length > 0) {
      report += `➖ Deletions (${diffResult.deletions.length}):\n`;
      diffResult.deletions.forEach((line, i) => {
        report += `  ${i + 1}: - ${line}\n`;
      });
      report += '\n';
    }
    
    if (diffResult.modifications.length > 0) {
      report += `🔄 Modifications (${diffResult.modifications.length}):\n`;
      diffResult.modifications.forEach(mod => {
        report += `  Line ${mod.line} (similarity: ${(mod.similarity * 100).toFixed(1)}%):\n`;
        report += `    - ${mod.expected}\n`;
        report += `    + ${mod.actual}\n`;
      });
    }
    
    report += '==========================\n';
    return report;
  }

  /**
   * 创建输出验证调试会话
   */
  static createDebugSession(output: string, validations: any[] = []): {
    addValidation: (name: string, result: ValidationResult) => void;
    generateReport: () => string;
    getStats: () => any;
  } {
    const session = {
      output,
      validations: new Map<string, ValidationResult>(),
      startTime: Date.now()
    };
    
    return {
      addValidation: (name: string, result: ValidationResult) => {
        session.validations.set(name, result);
      },
      
      generateReport: () => {
        const duration = Date.now() - session.startTime;
        const passed = Array.from(session.validations.values()).filter(v => v.passed).length;
        const total = session.validations.size;
        
        let report = `\n===== Debug Session Report =====\n`;
        report += `Duration: ${duration}ms\n`;
        report += `Validations: ${passed}/${total} passed\n`;
        report += `Output length: ${output.length} chars\n\n`;
        
        for (const [name, result] of session.validations) {
          const status = result.passed ? '✅' : '❌';
          report += `${status} ${name}: ${result.message || (result.passed ? 'PASS' : 'FAIL')}\n`;
          
          if (result.debugInfo && !result.passed) {
            report += `   Debug: ${result.debugInfo.suggestions?.join(', ') || 'No debug info'}\n`;
          }
        }
        
        report += `\n==============================\n`;
        return report;
      },
      
      getStats: () => ({
        duration: Date.now() - session.startTime,
        validations: session.validations.size,
        passed: Array.from(session.validations.values()).filter(v => v.passed).length,
        analysis: this.analyzeOutput(output)
      })
    };
  }

  /**
   * 输出内容的智能建议
   */
  static getSuggestions(output: string, failedValidations: ValidationResult[] = []): string[] {
    const suggestions: string[] = [];
    const analysis = this.analyzeOutput(output);
    
    // 基于分析结果的建议
    if (analysis.lineCount === 0) {
      suggestions.push('Output appears to be empty - check command execution');
    }
    
    if (analysis.wordCount < 5 && analysis.totalLength > 0) {
      suggestions.push('Output is very short - might be incomplete');
    }
    
    if (!analysis.hasNumbers && failedValidations.some(v => v.message?.includes('performance'))) {
      suggestions.push('No numbers found in output - performance metrics might be missing');
    }
    
    if (analysis.structuralElements.hasErrorMessages) {
      suggestions.push('Error messages detected - check for command failures');
    }
    
    if (analysis.hasJSON && !analysis.structuralElements.hasCodeBlocks) {
      suggestions.push('JSON detected - consider validating JSON structure');
    }
    
    if (analysis.hasTable && analysis.lineCount < 3) {
      suggestions.push('Table structure detected but few lines - verify table completeness');
    }
    
    // 基于失败验证的建议
    for (const validation of failedValidations) {
      if (validation.message?.includes('timeout')) {
        suggestions.push('Consider increasing timeout or optimizing command performance');
      }
      
      if (validation.message?.includes('pattern') || validation.message?.includes('match')) {
        suggestions.push('Pattern matching failed - verify expected output format');
      }
      
      if (validation.message?.includes('memory')) {
        suggestions.push('Memory usage exceeded - consider memory optimization');
      }
    }
    
    return suggestions.length > 0 ? suggestions : ['Output analysis completed successfully'];
  }

  /**
   * 交互式输出探索器
   */
  static createExplorer(output: string): {
    search: (term: string) => { matches: string[]; lines: number[] };
    extract: (pattern: RegExp) => string[];
    highlight: (terms: string[]) => string;
    getSection: (startLine: number, endLine: number) => string;
    findSimilar: (target: string, threshold?: number) => Array<{ text: string; similarity: number }>;
  } {
    const lines = output.split('\n');
    
    return {
      search: (term: string) => {
        const matches: string[] = [];
        const lineNumbers: number[] = [];
        
        lines.forEach((line, index) => {
          if (line.toLowerCase().includes(term.toLowerCase())) {
            matches.push(line.trim());
            lineNumbers.push(index + 1);
          }
        });
        
        return { matches, lines: lineNumbers };
      },
      
      extract: (pattern: RegExp) => {
        const globalPattern = new RegExp(pattern.source, pattern.flags.includes('g') ? pattern.flags : pattern.flags + 'g');
        return Array.from(output.matchAll(globalPattern)).map(match => match[0]);
      },
      
      highlight: (terms: string[]) => {
        return OutputDebugHelper.highlightKeywords(output, terms);
      },
      
      getSection: (startLine: number, endLine: number) => {
        return lines.slice(startLine - 1, endLine).join('\n');
      },
      
      findSimilar: (target: string, threshold: number = 0.7) => {
        const words = output.split(/\s+/);
        const similar: Array<{ text: string; similarity: number }> = [];
        
        for (const word of words) {
          if (word.length > 2) {
            const similarity = OutputValidator.calculateSimilarity(word.toLowerCase(), target.toLowerCase());
            if (similarity >= threshold) {
              similar.push({ text: word, similarity });
            }
          }
        }
        
        return similar.sort((a, b) => b.similarity - a.similarity).slice(0, 10);
      }
    };
  }
}