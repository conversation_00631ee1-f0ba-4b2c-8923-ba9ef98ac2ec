import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { OutputValidator, OutputDebugHelper } from '../helpers/output-validator';
import { CLITestingUtils } from '../helpers/cli-testing-utils';
import type { CLITestResult } from '../helpers/cli-testing-utils';

describe('OutputValidator', () => {
  let mockCLIResult: CLITestResult;

  beforeEach(() => {
    // 创建模拟的CLI结果
    mockCLIResult = {
      stdout: 'Sample output with complexity: 15\nProcessed 5 files\nExecution took: 250ms',
      stderr: '',
      exitCode: 0,
      isRunning: false,
      findByText: async (text: string | RegExp) => {
        const content = mockCLIResult.stdout + mockCLIResult.stderr;
        const pattern = typeof text === 'string' ? new RegExp(text, 'i') : text;
        return pattern.test(content);
      },
      queryByText: async (text: string | RegExp) => {
        const content = mockCLIResult.stdout + mockCLIResult.stderr;
        const pattern = typeof text === 'string' ? new RegExp(text, 'i') : text;
        const match = content.match(pattern);
        return match ? match[0] : null;
      },
      getByText: async (text: string | RegExp) => {
        const result = await mockCLIResult.queryByText(text);
        if (result === null) {
          throw new Error(`Text not found: ${text}`);
        }
        return result;
      },
      waitForOutput: async () => {},
      waitForExit: async () => 0,
      userEvent: {
        type: async () => {},
        keyboard: async () => {}
      },
      clear: async () => {},
      kill: async () => {}
    };
  });

  describe('基础文本搜索功能', () => {
    it('应该能够找到输出中的文本', async () => {
      const output = 'Hello world\nThis is a test\nComplexity: 15';
      const matches = await OutputValidator.findInOutput(output, 'complexity');
      
      expect(matches).toHaveLength(1);
      expect(matches[0]).toBe('Complexity');
    });

    it('应该支持正则表达式搜索', async () => {
      const output = 'Function foo has complexity 15\nFunction bar has complexity 8';
      const pattern = /complexity\s+(\d+)/gi;
      const matches = await OutputValidator.findInOutput(output, pattern);
      
      expect(matches).toHaveLength(2);
      expect(matches).toContain('complexity 15');
      expect(matches).toContain('complexity 8');
    });

    it('应该支持大小写敏感选项', async () => {
      const output = 'Hello WORLD';
      
      // 大小写不敏感（默认）
      const matchesInsensitive = await OutputValidator.findInOutput(output, 'hello', { caseSensitive: false });
      expect(matchesInsensitive).toHaveLength(1);
      
      // 大小写敏感
      const matchesSensitive = await OutputValidator.findInOutput(output, 'hello', { caseSensitive: true });
      expect(matchesSensitive).toHaveLength(0);
    });
  });

  describe('输出包含验证', () => {
    it('应该验证输出包含所有期望的文本', async () => {
      const output = 'Analysis complete\nProcessed 10 files\nTotal complexity: 150';
      const expected = ['Analysis complete', 'files', 'complexity'];
      
      const result = await OutputValidator.assertOutputContains(output, expected);
      
      expect(result.passed).toBe(true);
      expect(result.matches).toHaveLength(3);
    });

    it('应该在缺少期望文本时报告失败', async () => {
      const output = 'Analysis complete\nProcessed 10 files';
      const expected = ['Analysis complete', 'missing text'];
      
      const result = await OutputValidator.assertOutputContains(output, expected);
      
      expect(result.passed).toBe(false);
      expect(result.message).toContain('Missing expected text: missing text');
    });

    it('应该在调试模式下提供详细信息', async () => {
      const output = 'Short output';
      const expected = ['missing'];
      
      const result = await OutputValidator.assertOutputContains(output, expected, { debug: true });
      
      expect(result.passed).toBe(false);
      expect(result.debugInfo).toBeDefined();
      expect(result.debugInfo?.actualContent).toBe('Short output');
    });
  });

  describe('正则表达式匹配验证', () => {
    it('应该验证输出匹配正则表达式', async () => {
      const output = 'Function main has complexity 25';
      const pattern = /complexity\s+\d+/;
      
      const result = await OutputValidator.assertOutputMatches(output, pattern);
      
      expect(result.passed).toBe(true);
      expect(result.matches).toContain('complexity 25');
    });

    it('应该在不匹配时报告失败', async () => {
      const output = 'No complexity information';
      const pattern = /complexity\s+\d+/;
      
      const result = await OutputValidator.assertOutputMatches(output, pattern);
      
      expect(result.passed).toBe(false);
      expect(result.message).toContain('Output does not match pattern');
    });
  });

  describe('JSON 输出验证', () => {
    it('应该验证有效的JSON输出', async () => {
      const output = 'Analysis result: {"totalComplexity": 150, "files": 10}';
      const schema = { totalComplexity: 0, files: 0 };
      
      const result = await OutputValidator.validateJSONOutput(output, schema);
      
      expect(result.passed).toBe(true);
    });

    it('应该在JSON无效时报告失败', async () => {
      const output = 'No JSON here';
      const schema = { complexity: 0 };
      
      const result = await OutputValidator.validateJSONOutput(output, schema);
      
      expect(result.passed).toBe(false);
      expect(result.message).toContain('No JSON content found');
    });

    it('应该验证JSON schema', async () => {
      const output = '{"totalComplexity": 150}'; // 缺少 files 字段
      const schema = { totalComplexity: 0, files: 0 };
      
      const result = await OutputValidator.validateJSONOutput(output, schema);
      
      expect(result.passed).toBe(false);
      expect(result.message).toContain('Missing required property: files');
    });
  });

  describe('表格输出验证', () => {
    it('应该验证包含期望列的表格', async () => {
      const output = `
        File      Complexity  Functions
        main.ts   25          5
        helper.ts 18          3
      `;
      const expectedColumns = ['File', 'Complexity', 'Functions'];
      
      const result = await OutputValidator.validateTableOutput(output, expectedColumns);
      
      expect(result.passed).toBe(true);
    });

    it('应该在缺少列时报告失败', async () => {
      const output = `
        File      Complexity
        main.ts   25
      `;
      const expectedColumns = ['File', 'Complexity', 'Functions'];
      
      const result = await OutputValidator.validateTableOutput(output, expectedColumns);
      
      expect(result.passed).toBe(false);
      expect(result.message).toContain('Missing table columns: Functions');
    });

    it('应该支持自定义分隔符', async () => {
      const output = `
        File|Complexity|Functions
        main.ts|25|5
      `;
      const expectedColumns = ['File', 'Complexity', 'Functions'];
      
      const result = await OutputValidator.validateTableOutput(output, expectedColumns, { delimiter: '\\|' });
      
      expect(result.passed).toBe(true);
    });
  });

  describe('性能指标提取', () => {
    it('应该提取执行时间', async () => {
      const output = 'Analysis completed in 1.5s with 200 files processed';
      
      const metrics = await OutputValidator.extractMetrics(output);
      
      expect(metrics.executionTime).toBe(1500); // 转换为毫秒
      expect(metrics.filesProcessed).toBe(200);
    });

    it('应该提取内存使用信息', async () => {
      const output = 'Memory usage: 150MB, processed 50 files';
      
      const metrics = await OutputValidator.extractMetrics(output);
      
      expect(metrics.memoryUsage).toBe(150 * 1024 * 1024); // 转换为字节
      expect(metrics.filesProcessed).toBe(50);
    });

    it('应该提取平均复杂度', async () => {
      const output = 'Average complexity: 12.5 across 100 functions';
      
      const metrics = await OutputValidator.extractMetrics(output);
      
      expect(metrics.averageComplexity).toBe(12.5);
    });
  });

  describe('性能输出验证', () => {
    it('应该在性能符合要求时通过', async () => {
      const output = 'Analysis took: 800ms';
      
      const result = await OutputValidator.validatePerformanceOutput(output, 1000);
      
      expect(result.passed).toBe(true);
    });

    it('应该在性能超出要求时失败', async () => {
      const output = 'Analysis took: 1200ms';
      
      const result = await OutputValidator.validatePerformanceOutput(output, 1000);
      
      expect(result.passed).toBe(false);
      expect(result.message).toContain('exceeds maximum 1000ms');
    });
  });

  describe('模糊匹配功能', () => {
    it('应该找到相似的文本', async () => {
      const output = 'Function complexit analysis complete';
      
      const result = await OutputValidator.fuzzyMatch(output, 'complexity', 0.7);
      
      expect(result.passed).toBe(true);
      expect(result.matches).toContain('complexit');
    });

    it('应该在相似度不足时失败', async () => {
      const output = 'Function simple analysis complete';
      
      const result = await OutputValidator.fuzzyMatch(output, 'complexity', 0.9);
      
      expect(result.passed).toBe(false);
    });
  });

  describe('综合CLI结果验证', () => {
    it('应该执行多种验证', async () => {
      const output = `
        Analysis complete
        {"totalComplexity": 150, "files": 10}
        Execution took: 500ms
      `;
      
      // 创建更真实的CLI结果
      const cliResult = {
        ...mockCLIResult,
        stdout: output,
        stderr: ''
      };
      
      const validations = {
        contains: ['Analysis complete'],
        matches: [/complexity.*\d+/i],
        json: { totalComplexity: 0, files: 0 },
        performance: 1000
      };
      
      const results = await OutputValidator.validateCLIResult(cliResult, validations);
      
      // 所有验证应该通过
      results.forEach(result => {
        expect(result.passed).toBe(true);
      });
    });
  });
});

describe('OutputDebugHelper', () => {
  it('应该分析输出内容统计信息', () => {
    const output = 'This is a test output with numbers 123 and JSON {"key": "value"}';
    
    const analysis = OutputDebugHelper.analyzeOutput(output);
    
    expect(analysis.totalLength).toBe(output.length);
    expect(analysis.lineCount).toBe(1);
    expect(analysis.hasJSON).toBe(true);
    expect(analysis.hasNumbers).toBe(true);
    expect(analysis.wordCount).toBeGreaterThan(0);
  });

  it('应该高亮关键词', () => {
    const output = 'Function complexity is 15';
    const keywords = ['complexity', 'function'];
    
    const highlighted = OutputDebugHelper.highlightKeywords(output, keywords);
    
    expect(highlighted).toContain('【Function】');
    expect(highlighted).toContain('【complexity】');
  });

  it('应该检测表格格式', () => {
    const tableOutput = `
      File      | Complexity | Functions
      main.ts   | 25         | 5
      helper.ts | 18         | 3
    `;
    
    const analysis = OutputDebugHelper.analyzeOutput(tableOutput);
    
    expect(analysis.hasTable).toBe(true);
  });
});