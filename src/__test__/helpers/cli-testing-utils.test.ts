import { describe, test, expect, beforeEach, afterEach } from "vitest";
import { 
  CLITestingUtils, 
  CLITestError, 
  TestTimeoutError, 
  ProcessCleanupError,
  BufferOverflowError 
} from "./cli-testing-utils";
import { TestUtils } from "./test-utils";

describe("CLITestingUtils", () => {
  afterEach(async () => {
    // 确保所有进程都被清理
    await CLITestingUtils.cleanupAll();
  });

  test("should create CLITestingUtils instance", () => {
    expect(CLITestingUtils).toBeDefined();
    expect(typeof CLITestingUtils.renderCLI).toBe("function");
  });

  test("应该能够获取默认配置", () => {
    const config = CLITestingUtils.getDefaultConfig();
    expect(config.timeout).toBe(10000);
    expect(config.cleanup).toBe(true);
    expect(config.maxBuffer).toBe(1024 * 1024);
  });

  test("应该能够设置默认配置", () => {
    const originalConfig = CLITestingUtils.getDefaultConfig();
    
    CLITestingUtils.setDefaultConfig({ timeout: 5000 });
    
    const newConfig = CLITestingUtils.getDefaultConfig();
    expect(newConfig.timeout).toBe(5000);
    
    // 恢复原始配置
    CLITestingUtils.setDefaultConfig(originalConfig);
  });

  test("CLITestError 应该正确创建", () => {
    const error = new CLITestError("Test error", "test-command", "output", 1);
    
    expect(error.message).toBe("Test error");
    expect(error.command).toBe("test-command");
    expect(error.output).toBe("output");
    expect(error.exitCode).toBe(1);
    expect(error.name).toBe("CLITestError");
  });

  test("应该能够执行简单的命令", async () => {
    // 使用 echo 命令测试，这在所有系统上都应该可用
    const result = await CLITestingUtils.renderCLI("echo", ["Hello World"]);
    
    expect(result).toBeDefined();
    expect(typeof result.findByText).toBe("function");
    expect(typeof result.waitForExit).toBe("function");
    expect(typeof result.userEvent).toBe("object");
    
    // 等待命令完成
    const exitCode = await result.waitForExit(5000);
    expect(exitCode).toBe(0);
    
    // 验证输出
    const found = await result.findByText("Hello World");
    expect(found).toBe(true);
    
    // 清理
    await CLITestingUtils.cleanup(result);
  });

  test("TestUtils.executeCLITest 应该能够工作", async () => {
    const result = await TestUtils.executeCLITest("echo", ["CLI Test"]);
    
    expect(result).toBeDefined();
    
    // 等待完成并验证成功
    await TestUtils.expectCLISuccess(result);
    
    // 验证输出
    await TestUtils.expectCLIOutput(result, ["CLI Test"]);
    
    // 清理
    await CLITestingUtils.cleanup(result);
  });

  test("应该能够运行测试场景", async () => {
    const scenario = {
      name: "Echo Test",
      command: "echo",
      args: ["Scenario Test"],
      expectedOutput: ["Scenario Test"],
      expectedExitCode: 0,
      timeout: 5000
    };

    const executionResult = await TestUtils.runCLIScenario(scenario);
    
    expect(executionResult.success).toBe(true);
    expect(executionResult.output.exitCode).toBe(0);
    expect(executionResult.duration).toBeGreaterThan(0);
    expect(executionResult.assertions.length).toBeGreaterThan(0);
    
    // 所有断言都应该通过
    const failedAssertions = executionResult.assertions.filter(a => !a.passed);
    expect(failedAssertions).toHaveLength(0);
  });

  test("应该能够处理失败的命令", async () => {
    // 使用一个不存在的命令，这会在 spawn 时抛出错误
    try {
      await CLITestingUtils.renderCLI("nonexistent-command-12345", []);
      // 如果到达这里，说明命令意外成功了
      throw new Error("Expected command to fail");
    } catch (spawnError) {
      // spawn 失败是预期的，验证错误类型和信息
      expect(spawnError).toBeInstanceOf(CLITestError);
      const error = spawnError as CLITestError;
      expect(error.command).toContain("nonexistent-command-12345");
    }
  });

  test("TestTimeoutError 应该正确创建", () => {
    const error = new TestTimeoutError("Timeout error", "test-command", 5000);
    
    expect(error.message).toBe("Timeout error");
    expect(error.command).toBe("test-command");
    expect(error.timeoutMs).toBe(5000);
    expect(error.name).toBe("TestTimeoutError");
  });

  test("ProcessCleanupError 应该正确创建", () => {
    const error = new ProcessCleanupError("Cleanup error", "test-command", 12345);
    
    expect(error.message).toBe("Cleanup error");
    expect(error.command).toBe("test-command");
    expect(error.processId).toBe(12345);
    expect(error.name).toBe("ProcessCleanupError");
  });

  test("BufferOverflowError 应该正确创建", () => {
    const error = new BufferOverflowError("Buffer overflow", "test-command", 2048, 1024);
    
    expect(error.message).toBe("Buffer overflow");
    expect(error.command).toBe("test-command");
    expect(error.bufferSize).toBe(2048);
    expect(error.maxBuffer).toBe(1024);
    expect(error.name).toBe("BufferOverflowError");
  });

  test("应该能够监控活动进程", async () => {
    // 启动一个长时间运行的进程用于测试
    const result = await CLITestingUtils.renderCLI("sleep", ["0.2"]);
    
    // 检查活动进程数量
    expect(CLITestingUtils.getActiveProcessCount()).toBe(1);
    
    // 获取进程信息
    const processesInfo = CLITestingUtils.getActiveProcessesInfo();
    expect(processesInfo).toHaveLength(1);
    expect(processesInfo[0]?.command).toContain("sleep");
    expect(processesInfo[0]?.processId).toBeDefined();
    expect(processesInfo[0]?.runtime).toBeGreaterThan(0);
    
    // 清理
    await CLITestingUtils.cleanup(result);
    expect(CLITestingUtils.getActiveProcessCount()).toBe(0);
  });

  test("应该能够处理超时", async () => {
    try {
      // 使用很短的超时时间测试超时处理
      const result = await CLITestingUtils.renderCLI("sleep", ["10"], { timeout: 100 });
      
      // 尝试等待进程结束，应该会超时
      await result.waitForExit(200);
      throw new Error("Expected timeout error");
    } catch (error) {
      // 应该抛出超时相关的错误
      expect(error).toBeDefined();
      // 注意：实际的超时可能在不同层级处理，这里验证错误被正常抛出
    }
  }, 10000);

  test("应该能够强制清理所有进程", async () => {
    // 启动多个进程
    const result1 = await CLITestingUtils.renderCLI("sleep", ["0.5"]);
    const result2 = await CLITestingUtils.renderCLI("sleep", ["0.5"]);
    
    expect(CLITestingUtils.getActiveProcessCount()).toBe(2);
    
    // 强制清理所有进程
    await CLITestingUtils.forceCleanupAll();
    
    expect(CLITestingUtils.getActiveProcessCount()).toBe(0);
  });

  test("应该能够处理缓冲区配置", () => {
    const config = CLITestingUtils.getDefaultConfig();
    expect(config.maxBuffer).toBe(1024 * 1024); // 1MB
    
    // 设置较小的缓冲区
    CLITestingUtils.setDefaultConfig({ maxBuffer: 1024 }); // 1KB
    
    const newConfig = CLITestingUtils.getDefaultConfig();
    expect(newConfig.maxBuffer).toBe(1024);
    
    // 恢复默认配置
    CLITestingUtils.setDefaultConfig({ maxBuffer: 1024 * 1024 });
  });

  test("应该能够处理退出码非零的命令", async () => {
    // 使用 false 命令，它会正常启动但返回退出码 1
    const result = await CLITestingUtils.renderCLI("false", []);
    
    // 等待命令完成
    const exitCode = await result.waitForExit(5000);
    
    // 验证退出码是非零的
    expect(exitCode).not.toBe(0);
    expect(exitCode).toBe(1);
    
    // 测试 expectCLIFailure 方法
    await TestUtils.expectCLIFailure(result);
    
    // 清理
    await CLITestingUtils.cleanup(result);
  });
});