import { writeFileSync, mkdirSync, rmSync, existsSync } from 'fs';
import { join } from 'path';
import { MockDataGenerator } from './test-utils';
import type { CLITestConfig, TestScenario } from './cli-testing-utils';

/**
 * 测试固件数据类型定义
 */
export interface TestFixture {
  readonly name: string;
  readonly description: string;
  readonly files: FixtureFile[];
  readonly config?: FixtureConfig;
  readonly expectedResults?: FixtureExpectedResult;
  readonly metadata?: FixtureMetadata;
}

export interface FixtureFile {
  readonly path: string;           // 相对路径
  readonly content: string;        // 文件内容
  readonly type: 'ts' | 'js' | 'tsx' | 'jsx' | 'json' | 'config';
  readonly encoding?: string;      // 文件编码，默认 utf8
}

export interface FixtureConfig {
  readonly complexity?: {
    threshold?: number;
    includePatterns?: string[];
    excludePatterns?: string[];
    rules?: Record<string, any>;
  };
  readonly cli?: Partial<CLITestConfig>;
  readonly scenarios?: TestScenario[];
}

export interface FixtureExpectedResult {
  readonly summary?: {
    totalFiles?: number;
    totalFunctions?: number;
    avgComplexity?: number;
    maxComplexity?: number;
    highComplexityCount?: number;
  };
  readonly files?: Array<{
    path: string;
    complexity: number;
    functionCount: number;
  }>;
  readonly performance?: {
    maxExecutionTime?: number;
    maxMemoryUsage?: number;
  };
}

export interface FixtureMetadata {
  readonly version: string;
  readonly created: string;
  readonly tags: string[];
  readonly difficulty: 'basic' | 'intermediate' | 'advanced' | 'expert';
  readonly category: 'unit' | 'integration' | 'e2e' | 'performance' | 'stress';
  readonly requirements?: string[];
}

/**
 * 测试固件管理器
 * 负责创建、管理和复用测试数据固件
 */
export class FixtureManager {
  private static fixtures = new Map<string, TestFixture>();
  private static fixtureDirectory = join(process.cwd(), 'src/__test__/fixtures/generated');
  
  /**
   * 初始化固件管理器
   */
  static initialize(): void {
    if (!existsSync(this.fixtureDirectory)) {
      mkdirSync(this.fixtureDirectory, { recursive: true });
    }
    
    // 注册默认固件
    this.registerDefaultFixtures();
  }
  
  /**
   * 注册固件
   */
  static registerFixture(fixture: TestFixture): void {
    this.fixtures.set(fixture.name, fixture);
  }
  
  /**
   * 获取固件
   */
  static getFixture(name: string): TestFixture | undefined {
    return this.fixtures.get(name);
  }
  
  /**
   * 获取所有固件名称
   */
  static getFixtureNames(): string[] {
    return Array.from(this.fixtures.keys());
  }
  
  /**
   * 按标签筛选固件
   */
  static getFixturesByTag(tag: string): TestFixture[] {
    return Array.from(this.fixtures.values()).filter(
      fixture => fixture.metadata?.tags.includes(tag)
    );
  }
  
  /**
   * 按类别筛选固件
   */
  static getFixturesByCategory(category: FixtureMetadata['category']): TestFixture[] {
    return Array.from(this.fixtures.values()).filter(
      fixture => fixture.metadata?.category === category
    );
  }
  
  /**
   * 按难度筛选固件
   */
  static getFixturesByDifficulty(difficulty: FixtureMetadata['difficulty']): TestFixture[] {
    return Array.from(this.fixtures.values()).filter(
      fixture => fixture.metadata?.difficulty === difficulty
    );
  }
  
  /**
   * 创建固件的物理文件
   */
  static async materializeFixture(fixtureName: string, targetDir?: string): Promise<string> {
    const fixture = this.getFixture(fixtureName);
    if (!fixture) {
      throw new Error(`Fixture '${fixtureName}' not found`);
    }
    
    const fixtureDir = targetDir || join(this.fixtureDirectory, fixtureName);
    
    // 清理并创建目录
    if (existsSync(fixtureDir)) {
      rmSync(fixtureDir, { recursive: true, force: true });
    }
    mkdirSync(fixtureDir, { recursive: true });
    
    // 创建文件
    for (const file of fixture.files) {
      const filePath = join(fixtureDir, file.path);
      const fileDir = join(filePath, '..');
      
      if (!existsSync(fileDir)) {
        mkdirSync(fileDir, { recursive: true });
      }
      
      writeFileSync(filePath, file.content, file.encoding || 'utf8');
    }
    
    // 创建配置文件（如果有）
    if (fixture.config) {
      const configPath = join(fixtureDir, 'fixture.config.json');
      writeFileSync(configPath, JSON.stringify(fixture.config, null, 2));
    }
    
    // 创建预期结果文件（如果有）
    if (fixture.expectedResults) {
      const expectedPath = join(fixtureDir, 'expected-results.json');
      writeFileSync(expectedPath, JSON.stringify(fixture.expectedResults, null, 2));
    }
    
    return fixtureDir;
  }
  
  /**
   * 清理固件的物理文件
   */
  static async cleanupFixture(fixtureName: string, targetDir?: string): Promise<void> {
    const fixtureDir = targetDir || join(this.fixtureDirectory, fixtureName);
    
    if (existsSync(fixtureDir)) {
      rmSync(fixtureDir, { recursive: true, force: true });
    }
  }
  
  /**
   * 生成动态固件
   */
  static generateDynamicFixture(
    name: string,
    options: {
      fileCount?: number;
      complexityRange?: [number, number];
      includeTypes?: FixtureFile['type'][];
      category?: FixtureMetadata['category'];
      difficulty?: FixtureMetadata['difficulty'];
    } = {}
  ): TestFixture {
    const {
      fileCount = 5,
      complexityRange = [5, 25],
      includeTypes = ['ts', 'tsx'],
      category = 'unit',
      difficulty = 'intermediate'
    } = options;
    
    const files: FixtureFile[] = [];
    const expectedFiles: FixtureExpectedResult['files'] = [];
    let totalComplexity = 0;
    let totalFunctions = 0;
    let maxComplexity = 0;
    
    // 生成代码文件
    for (let i = 0; i < fileCount; i++) {
      const fileType = includeTypes[i % includeTypes.length]!;
      const targetComplexity = MockDataGenerator.randomComplexity(complexityRange[0], complexityRange[1]);
      const functionCount = Math.floor(Math.random() * 3) + 1;
      
      let fileContent = '';
      let fileComplexity = 0;
      
      // 生成多个函数
      for (let j = 0; j < functionCount; j++) {
        const funcComplexity = Math.floor(targetComplexity / functionCount) + (Math.random() > 0.5 ? 1 : 0);
        fileContent += MockDataGenerator.generateTypeScriptCode(funcComplexity) + '\n\n';
        fileComplexity += funcComplexity;
        totalFunctions++;
      }
      
      totalComplexity += fileComplexity;
      maxComplexity = Math.max(maxComplexity, fileComplexity);
      
      files.push({
        path: `src/file${i}.${fileType}`,
        content: fileContent.trim(),
        type: fileType
      });
      
      expectedFiles.push({
        path: `src/file${i}.${fileType}`,
        complexity: fileComplexity,
        functionCount
      });
    }
    
    // 生成配置文件
    if (Math.random() > 0.5) {
      files.push({
        path: 'complexity.config.json',
        content: JSON.stringify({
          threshold: Math.floor(maxComplexity * 0.7),
          include: ['src/**/*.ts', 'src/**/*.tsx'],
          exclude: ['**/*.test.ts', '**/*.spec.ts']
        }, null, 2),
        type: 'json'
      });
    }
    
    // 生成 package.json
    files.push({
      path: 'package.json',
      content: JSON.stringify({
        name: `test-fixture-${name}`,
        version: '1.0.0',
        scripts: {
          test: 'echo "test fixture"'
        }
      }, null, 2),
      type: 'json'
    });
    
    return {
      name,
      description: `Dynamically generated fixture with ${fileCount} files`,
      files,
      config: {
        complexity: {
          threshold: Math.floor(maxComplexity * 0.7),
          includePatterns: ['src/**/*.ts', 'src/**/*.tsx'],
          excludePatterns: ['**/*.test.ts']
        }
      },
      expectedResults: {
        summary: {
          totalFiles: fileCount,
          totalFunctions,
          avgComplexity: totalComplexity / totalFunctions,
          maxComplexity,
          highComplexityCount: expectedFiles.filter(f => f.complexity > 15).length
        },
        files: expectedFiles,
        performance: {
          maxExecutionTime: fileCount * 1000,
          maxMemoryUsage: fileCount * 10 * 1024 * 1024
        }
      },
      metadata: {
        version: '1.0.0',
        created: new Date().toISOString(),
        tags: ['generated', 'dynamic', fileType],
        difficulty,
        category,
        requirements: [`Node.js >= 18`, `Files: ${fileCount}`, `Complexity: ${complexityRange[0]}-${complexityRange[1]}`]
      }
    };
  }
  
  /**
   * 注册默认固件
   */
  private static registerDefaultFixtures(): void {
    // 基础复杂度固件
    this.registerFixture({
      name: 'basic-complexity',
      description: 'Basic complexity scenarios for unit testing',
      files: [
        {
          path: 'src/simple.ts',
          content: `
            function simple() {
              return true;
            }
            
            function withIf(condition: boolean) {
              if (condition) {
                return "yes";
              }
              return "no";
            }
          `,
          type: 'ts'
        },
        {
          path: 'src/loops.ts',
          content: `
            function withLoop() {
              for (let i = 0; i < 10; i++) {
                console.log(i);
              }
            }
            
            function withWhile(condition: boolean) {
              while (condition) {
                doSomething();
              }
            }
          `,
          type: 'ts'
        }
      ],
      expectedResults: {
        summary: {
          totalFiles: 2,
          totalFunctions: 4,
          avgComplexity: 0.75,
          maxComplexity: 1,
          highComplexityCount: 0
        }
      },
      metadata: {
        version: '1.0.0',
        created: '2024-01-01T00:00:00.000Z',
        tags: ['basic', 'unit-test'],
        difficulty: 'basic',
        category: 'unit'
      }
    });
    
    // 复杂嵌套固件
    this.registerFixture({
      name: 'nested-complexity',
      description: 'Complex nested structures for advanced testing',
      files: [
        {
          path: 'src/nested.ts',
          content: `
            function complexNested() {
              for (let i = 0; i < 10; i++) {
                if (condition1) {
                  while (condition2) {
                    if (condition3 && condition4 || condition5) {
                      return process();
                    }
                  }
                }
              }
            }
          `,
          type: 'ts'
        }
      ],
      expectedResults: {
        summary: {
          totalFiles: 1,
          totalFunctions: 1,
          avgComplexity: 9,
          maxComplexity: 9,
          highComplexityCount: 1
        }
      },
      metadata: {
        version: '1.0.0',
        created: '2024-01-01T00:00:00.000Z',
        tags: ['nested', 'complex', 'advanced'],
        difficulty: 'advanced',
        category: 'unit'
      }
    });
    
    // JSX/React 固件
    this.registerFixture({
      name: 'react-components',
      description: 'React components with JSX complexity',
      files: [
        {
          path: 'src/Component.tsx',
          content: `
            import React from 'react';
            
            export function SimpleComponent() {
              return <div>Hello World</div>;
            }
            
            export function ConditionalComponent({ show, items }: { show: boolean; items: string[] }) {
              if (!show) return null;
              
              return (
                <div>
                  {items.map(item => (
                    <span key={item}>
                      {item}
                    </span>
                  ))}
                </div>
              );
            }
          `,
          type: 'tsx'
        }
      ],
      expectedResults: {
        summary: {
          totalFiles: 1,
          totalFunctions: 2,
          avgComplexity: 1,
          maxComplexity: 2,
          highComplexityCount: 0
        }
      },
      metadata: {
        version: '1.0.0',
        created: '2024-01-01T00:00:00.000Z',
        tags: ['react', 'jsx', 'frontend'],
        difficulty: 'intermediate',
        category: 'integration'
      }
    });
    
    // 性能测试固件
    this.registerFixture({
      name: 'performance-stress',
      description: 'Large codebase for performance testing',
      files: Array.from({ length: 20 }, (_, index) => ({
        path: `src/module${index}.ts`,
        content: MockDataGenerator.generateTypeScriptCode(15),
        type: 'ts' as const
      })),
      expectedResults: {
        performance: {
          maxExecutionTime: 30000,
          maxMemoryUsage: 200 * 1024 * 1024
        }
      },
      metadata: {
        version: '1.0.0',
        created: '2024-01-01T00:00:00.000Z',
        tags: ['performance', 'stress', 'large'],
        difficulty: 'expert',
        category: 'performance'
      }
    });
  }
  
  /**
   * 批量生成测试固件
   */
  static generateFixtureBatch(
    prefix: string,
    count: number,
    baseOptions?: Parameters<typeof FixtureManager.generateDynamicFixture>[1]
  ): TestFixture[] {
    const fixtures: TestFixture[] = [];
    
    for (let i = 0; i < count; i++) {
      const fixture = this.generateDynamicFixture(
        `${prefix}-${i + 1}`,
        {
          ...baseOptions,
          fileCount: (baseOptions?.fileCount || 3) + Math.floor(Math.random() * 3)
        }
      );
      
      this.registerFixture(fixture);
      fixtures.push(fixture);
    }
    
    return fixtures;
  }
  
  /**
   * 清理所有生成的固件文件
   */
  static async cleanup(): Promise<void> {
    if (existsSync(this.fixtureDirectory)) {
      rmSync(this.fixtureDirectory, { recursive: true, force: true });
    }
  }
  
  /**
   * 获取固件统计信息
   */
  static getStatistics(): {
    totalFixtures: number;
    byCategory: Record<string, number>;
    byDifficulty: Record<string, number>;
    byTags: Record<string, number>;
  } {
    const fixtures = Array.from(this.fixtures.values());
    
    const byCategory: Record<string, number> = {};
    const byDifficulty: Record<string, number> = {};
    const byTags: Record<string, number> = {};
    
    fixtures.forEach(fixture => {
      const category = fixture.metadata?.category || 'unknown';
      const difficulty = fixture.metadata?.difficulty || 'unknown';
      const tags = fixture.metadata?.tags || [];
      
      byCategory[category] = (byCategory[category] || 0) + 1;
      byDifficulty[difficulty] = (byDifficulty[difficulty] || 0) + 1;
      
      tags.forEach(tag => {
        byTags[tag] = (byTags[tag] || 0) + 1;
      });
    });
    
    return {
      totalFixtures: fixtures.length,
      byCategory,
      byDifficulty,
      byTags
    };
  }
}

/**
 * 固件工厂类 - 快速创建特定类型的固件
 */
export class FixtureFactory {
  /**
   * 创建 CLI 集成测试固件
   */
  static createCLITestFixture(name: string, scenarios: TestScenario[]): TestFixture {
    return {
      name,
      description: `CLI integration test fixture with ${scenarios.length} scenarios`,
      files: [
        {
          path: 'test-scenarios.json',
          content: JSON.stringify(scenarios, null, 2),
          type: 'json'
        }
      ],
      config: {
        scenarios,
        cli: MockDataGenerator.generateCLITestConfig()
      },
      metadata: {
        version: '1.0.0',
        created: new Date().toISOString(),
        tags: ['cli', 'integration'],
        difficulty: 'intermediate',
        category: 'integration'
      }
    };
  }
  
  /**
   * 创建性能基准测试固件
   */
  static createPerformanceFixture(
    name: string,
    complexity: 'light' | 'moderate' | 'heavy'
  ): TestFixture {
    const stressData = MockDataGenerator.generateStressTestData(complexity);
    
    const files: FixtureFile[] = [];
    for (let i = 0; i < stressData.fileCount; i++) {
      files.push({
        path: `src/perf-test-${i}.ts`,
        content: MockDataGenerator.generateTypeScriptCode(10),
        type: 'ts'
      });
    }
    
    return {
      name,
      description: `Performance test fixture - ${complexity} load`,
      files,
      expectedResults: {
        performance: {
          maxExecutionTime: stressData.expectedDuration,
          maxMemoryUsage: stressData.expectedMemory
        }
      },
      metadata: {
        version: '1.0.0',
        created: new Date().toISOString(),
        tags: ['performance', complexity],
        difficulty: complexity === 'light' ? 'basic' : complexity === 'heavy' ? 'expert' : 'advanced',
        category: 'performance'
      }
    };
  }
  
  /**
   * 创建错误场景测试固件
   */
  static createErrorScenarioFixture(name: string, errorTypes: string[]): TestFixture {
    const files: FixtureFile[] = [];
    
    errorTypes.forEach(errorType => {
      switch (errorType) {
        case 'syntax-error':
          files.push({
            path: 'src/syntax-error.ts',
            content: `
              function syntaxError() {
                if (condition {  // Missing closing parenthesis
                  return true;
                }
              }
            `,
            type: 'ts'
          });
          break;
        case 'type-error':
          files.push({
            path: 'src/type-error.ts',
            content: `
              function typeError(): string {
                return 123;  // Type error: number is not assignable to string
              }
            `,
            type: 'ts'
          });
          break;
        case 'runtime-error':
          files.push({
            path: 'src/runtime-error.ts',
            content: `
              function runtimeError() {
                const obj: any = null;
                return obj.property;  // Runtime error: Cannot read property of null
              }
            `,
            type: 'ts'
          });
          break;
      }
    });
    
    return {
      name,
      description: `Error scenario fixture with ${errorTypes.join(', ')} cases`,
      files,
      metadata: {
        version: '1.0.0',
        created: new Date().toISOString(),
        tags: ['error', 'negative-test', ...errorTypes],
        difficulty: 'intermediate',
        category: 'integration'
      }
    };
  }
}

// 初始化固件管理器
FixtureManager.initialize();