import type { CLITestResult } from './cli-testing-utils';
import { InteractiveTestError, TestTimeoutError } from './cli-testing-utils';

/**
 * 交互式测试步骤接口
 * 定义单个交互式测试步骤的配置
 */
export interface InteractiveStep {
  /** 等待的文本或正则表达式 */
  readonly waitFor: string | RegExp;
  /** 要输入的内容 */
  readonly input: string;
  /** 输入前的延迟时间（毫秒） */
  readonly delay?: number;
  /** 步骤描述，用于错误报告 */
  readonly description?: string;
  /** 输入后是否需要按回车键 */
  readonly pressEnter?: boolean;
  /** 该步骤的超时时间（毫秒），覆盖全局超时 */
  readonly timeout?: number;
}

/**
 * 交互式测试配置接口
 */
export interface InteractiveTestConfig {
  /** 默认超时时间（毫秒） */
  readonly defaultTimeout: number;
  /** 步骤间默认延迟（毫秒） */
  readonly defaultDelay: number;
  /** 是否在每个步骤后自动按回车键 */
  readonly autoEnter: boolean;
  /** 是否启用调试输出 */
  readonly debug: boolean;
  /** 输入后等待输出稳定的时间（毫秒） */
  readonly stabilizeDelay: number;
}

/**
 * 交互式测试执行结果接口
 */
export interface InteractiveTestResult {
  /** 是否成功执行所有步骤 */
  readonly success: boolean;
  /** 已执行的步骤数 */
  readonly completedSteps: number;
  /** 总步骤数 */
  readonly totalSteps: number;
  /** 执行时长（毫秒） */
  readonly duration: number;
  /** 失败的步骤信息 */
  readonly failedStep?: {
    stepIndex: number;
    step: InteractiveStep;
    error: string;
  };
  /** 最终的输出内容 */
  readonly finalOutput: {
    stdout: string;
    stderr: string;
  };
}

/**
 * 键盘按键映射
 * 定义常用的键盘按键和控制序列
 */
export const KeyMap = {
  // 控制键
  ENTER: '\n',
  TAB: '\t',
  ESCAPE: '\u001b',
  BACKSPACE: '\u0008',
  DELETE: '\u007f',
  
  // 方向键 (ANSI 转义序列)
  ARROW_UP: '\u001b[A',
  ARROW_DOWN: '\u001b[B',
  ARROW_RIGHT: '\u001b[C',
  ARROW_LEFT: '\u001b[D',
  
  // 功能键
  HOME: '\u001b[H',
  END: '\u001b[F',
  PAGE_UP: '\u001b[5~',
  PAGE_DOWN: '\u001b[6~',
  
  // Ctrl 组合键
  CTRL_C: '\u0003',
  CTRL_D: '\u0004',
  CTRL_Z: '\u001a',
  CTRL_L: '\u000c',
  
  // 常用字符
  SPACE: ' ',
  Y: 'y',
  N: 'n',
  YES: 'yes',
  NO: 'no'
} as const;

/**
 * 交互式测试辅助类
 * 提供高级的交互式 CLI 测试功能
 */
export class InteractiveTestHelper {
  private static readonly DEFAULT_CONFIG: InteractiveTestConfig = {
    defaultTimeout: 10000,
    defaultDelay: 100,
    autoEnter: false,
    debug: false,
    stabilizeDelay: 200
  };

  /**
   * 模拟完整的用户交互流程
   * @param instance CLI 测试实例
   * @param flow 交互步骤数组
   * @param config 配置选项
   * @returns 交互测试结果
   */
  static async simulateUserFlow(
    instance: CLITestResult,
    flow: InteractiveStep[],
    config: Partial<InteractiveTestConfig> = {}
  ): Promise<InteractiveTestResult> {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config };
    const startTime = Date.now();
    let completedSteps = 0;

    if (finalConfig.debug) {
      console.log(`🎯 Starting interactive flow with ${flow.length} steps`);
    }

    try {
      for (let i = 0; i < flow.length; i++) {
        const step = flow[i];
        const stepTimeout = step.timeout || finalConfig.defaultTimeout;
        
        if (finalConfig.debug) {
          console.log(`📝 Step ${i + 1}/${flow.length}: ${step.description || 'Waiting for prompt'}`);
        }

        // 等待提示出现
        await this.waitForPrompt(instance, step.waitFor, stepTimeout);

        // 输入前延迟
        const delay = step.delay || finalConfig.defaultDelay;
        if (delay > 0) {
          await this._delay(delay);
        }

        // 发送输入
        await this._sendInput(instance, step.input, step.pressEnter ?? finalConfig.autoEnter);

        // 等待输出稳定
        if (finalConfig.stabilizeDelay > 0) {
          await this._delay(finalConfig.stabilizeDelay);
        }

        completedSteps++;

        if (finalConfig.debug) {
          console.log(`✅ Step ${i + 1} completed`);
        }
      }

      const duration = Date.now() - startTime;

      if (finalConfig.debug) {
        console.log(`🎉 Interactive flow completed successfully in ${duration}ms`);
      }

      return {
        success: true,
        completedSteps,
        totalSteps: flow.length,
        duration,
        finalOutput: {
          stdout: instance.stdout,
          stderr: instance.stderr
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const failedStepIndex = completedSteps;
      const failedStep = flow[failedStepIndex];

      if (finalConfig.debug) {
        console.error(`❌ Interactive flow failed at step ${failedStepIndex + 1}:`, error);
      }

      return {
        success: false,
        completedSteps,
        totalSteps: flow.length,
        duration,
        failedStep: failedStep ? {
          stepIndex: failedStepIndex,
          step: failedStep,
          error: error instanceof Error ? error.message : String(error)
        } : undefined,
        finalOutput: {
          stdout: instance.stdout,
          stderr: instance.stderr
        }
      };
    }
  }

  /**
   * 等待特定提示信息出现
   * @param instance CLI 测试实例
   * @param prompt 要等待的提示文本或正则表达式
   * @param timeout 超时时间（毫秒）
   */
  static async waitForPrompt(
    instance: CLITestResult,
    prompt: string | RegExp,
    timeout: number = 5000
  ): Promise<void> {
    const startTime = Date.now();
    const promptText = typeof prompt === 'string' ? prompt : prompt.source;

    try {
      await instance.waitForOutput(prompt, timeout);
    } catch (error) {
      const elapsed = Date.now() - startTime;
      throw new InteractiveTestError(
        `Timeout waiting for prompt "${promptText}" after ${elapsed}ms`,
        `interactive-test`,
        `waitForPrompt`
      );
    }
  }

  /**
   * 发送键盘按键序列
   * @param instance CLI 测试实例
   * @param keys 按键序列（可以是字符串或按键数组）
   */
  static async sendKeySequence(
    instance: CLITestResult,
    keys: string | string[]
  ): Promise<void> {
    const keySequence = Array.isArray(keys) ? keys : [keys];

    for (const key of keySequence) {
      await instance.userEvent.keyboard(key);
      // 短暂延迟模拟真实的按键间隔
      await this._delay(50);
    }
  }

  /**
   * 模拟确认对话框交互（y/n）
   * @param instance CLI 测试实例
   * @param prompt 确认提示文本
   * @param confirm 是否确认（true=y, false=n）
   * @param timeout 超时时间
   */
  static async confirmDialog(
    instance: CLITestResult,
    prompt: string | RegExp,
    confirm: boolean,
    timeout: number = 5000
  ): Promise<void> {
    await this.waitForPrompt(instance, prompt, timeout);
    const response = confirm ? KeyMap.Y : KeyMap.N;
    await instance.userEvent.type(response + KeyMap.ENTER);
  }

  /**
   * 模拟选择菜单交互
   * @param instance CLI 测试实例
   * @param menuPrompt 菜单提示文本
   * @param choice 选择项（数字或字符）
   * @param useArrowKeys 是否使用方向键导航
   * @param timeout 超时时间
   */
  static async selectFromMenu(
    instance: CLITestResult,
    menuPrompt: string | RegExp,
    choice: string | number,
    useArrowKeys: boolean = false,
    timeout: number = 5000
  ): Promise<void> {
    await this.waitForPrompt(instance, menuPrompt, timeout);

    if (useArrowKeys && typeof choice === 'number') {
      // 使用方向键导航到指定选项
      for (let i = 0; i < choice; i++) {
        await instance.userEvent.keyboard(KeyMap.ARROW_DOWN);
        await this._delay(100);
      }
      await instance.userEvent.keyboard(KeyMap.ENTER);
    } else {
      // 直接输入选择
      await instance.userEvent.type(String(choice) + KeyMap.ENTER);
    }
  }

  /**
   * 模拟文本输入对话框
   * @param instance CLI 测试实例
   * @param prompt 输入提示文本
   * @param input 要输入的文本
   * @param timeout 超时时间
   */
  static async inputText(
    instance: CLITestResult,
    prompt: string | RegExp,
    input: string,
    timeout: number = 5000
  ): Promise<void> {
    await this.waitForPrompt(instance, prompt, timeout);
    await instance.userEvent.type(input + KeyMap.ENTER);
  }

  /**
   * 模拟密码输入（不会在输出中显示）
   * @param instance CLI 测试实例
   * @param prompt 密码提示文本
   * @param password 密码内容
   * @param timeout 超时时间
   */
  static async inputPassword(
    instance: CLITestResult,
    prompt: string | RegExp,
    password: string,
    timeout: number = 5000
  ): Promise<void> {
    await this.waitForPrompt(instance, prompt, timeout);
    
    // 逐字符输入密码，模拟真实的密码输入体验
    for (const char of password) {
      await instance.userEvent.keyboard(char);
      await this._delay(50); // 模拟打字速度
    }
    
    await instance.userEvent.keyboard(KeyMap.ENTER);
  }

  /**
   * 模拟多行文本输入
   * @param instance CLI 测试实例
   * @param prompt 输入提示文本
   * @param lines 文本行数组
   * @param endSequence 结束输入的按键序列（默认为 Ctrl+D）
   * @param timeout 超时时间
   */
  static async inputMultilineText(
    instance: CLITestResult,
    prompt: string | RegExp,
    lines: string[],
    endSequence: string = KeyMap.CTRL_D,
    timeout: number = 5000
  ): Promise<void> {
    await this.waitForPrompt(instance, prompt, timeout);

    for (const line of lines) {
      await instance.userEvent.type(line + KeyMap.ENTER);
      await this._delay(100);
    }

    // 发送结束序列
    await instance.userEvent.keyboard(endSequence);
  }

  /**
   * 中断正在运行的程序（发送 Ctrl+C）
   * @param instance CLI 测试实例
   * @param waitForExit 是否等待程序退出
   * @param timeout 等待退出的超时时间
   */
  static async interruptProgram(
    instance: CLITestResult,
    waitForExit: boolean = true,
    timeout: number = 3000
  ): Promise<void> {
    await instance.userEvent.keyboard(KeyMap.CTRL_C);

    if (waitForExit) {
      try {
        await instance.waitForExit(timeout);
      } catch (error) {
        throw new InteractiveTestError(
          `Program did not exit after interrupt within ${timeout}ms`,
          'interactive-test',
          'interruptProgram'
        );
      }
    }
  }

  /**
   * 创建常用的交互步骤模板
   */
  static createSteps = {
    /**
     * 创建确认步骤
     */
    confirm(prompt: string | RegExp, answer: boolean, description?: string): InteractiveStep {
      return {
        waitFor: prompt,
        input: answer ? 'y' : 'n',
        pressEnter: true,
        description: description || `Confirm: ${answer ? 'Yes' : 'No'}`
      };
    },

    /**
     * 创建文本输入步骤
     */
    input(prompt: string | RegExp, text: string, description?: string): InteractiveStep {
      return {
        waitFor: prompt,
        input: text,
        pressEnter: true,
        description: description || `Input: ${text}`
      };
    },

    /**
     * 创建菜单选择步骤
     */
    select(prompt: string | RegExp, choice: string | number, description?: string): InteractiveStep {
      return {
        waitFor: prompt,
        input: String(choice),
        pressEnter: true,
        description: description || `Select: ${choice}`
      };
    },

    /**
     * 创建等待步骤（不输入任何内容）
     */
    wait(prompt: string | RegExp, delay?: number, description?: string): InteractiveStep {
      return {
        waitFor: prompt,
        input: '',
        delay: delay || 1000,
        description: description || 'Wait for output'
      };
    },

    /**
     * 创建按键步骤（发送特定按键）
     */
    key(prompt: string | RegExp, key: string, description?: string): InteractiveStep {
      return {
        waitFor: prompt,
        input: key,
        pressEnter: false,
        description: description || `Press key: ${key}`
      };
    }
  };

  /**
   * 调试辅助方法：显示当前输出内容
   * @param instance CLI 测试实例
   * @param label 标签
   */
  static debugOutput(instance: CLITestResult, label: string = 'Current Output'): void {
    console.log(`\n=== ${label} ===`);
    console.log('STDOUT:');
    console.log(instance.stdout || '(empty)');
    console.log('\nSTDERR:');
    console.log(instance.stderr || '(empty)');
    console.log('=================\n');
  }

  /**
   * 验证交互式测试结果
   * @param result 交互测试结果
   * @param expectedSteps 期望完成的步骤数
   */
  static validateResult(result: InteractiveTestResult, expectedSteps?: number): void {
    if (!result.success) {
      const failedStep = result.failedStep;
      const stepInfo = failedStep 
        ? ` at step ${failedStep.stepIndex + 1}/${result.totalSteps} (${failedStep.step.description || 'unnamed step'})` 
        : '';
      
      throw new InteractiveTestError(
        `Interactive test failed${stepInfo}: ${failedStep?.error || 'Unknown error'}`,
        'interactive-test',
        `step-${failedStep?.stepIndex || 'unknown'}`
      );
    }

    if (expectedSteps !== undefined && result.completedSteps !== expectedSteps) {
      throw new InteractiveTestError(
        `Expected to complete ${expectedSteps} steps, but completed ${result.completedSteps}`,
        'interactive-test',
        'validation'
      );
    }
  }

  // 私有辅助方法

  /**
   * 延迟指定毫秒数
   */
  private static async _delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 发送输入内容
   */
  private static async _sendInput(
    instance: CLITestResult,
    input: string,
    pressEnter: boolean
  ): Promise<void> {
    if (input) {
      await instance.userEvent.type(input);
    }
    
    if (pressEnter) {
      await instance.userEvent.keyboard(KeyMap.ENTER);
    }
  }
}

/**
 * 便捷函数导出
 */
export const InteractiveSteps = InteractiveTestHelper.createSteps;
export { KeyMap as Keys };

/**
 * 交互式测试的便捷装饰器
 * 用于简化测试代码的编写
 */
export function interactiveTest(
  config?: Partial<InteractiveTestConfig>
) {
  return function (
    target: any,
    propertyName: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const finalConfig = { ...InteractiveTestHelper['DEFAULT_CONFIG'], ...config };
      
      if (finalConfig.debug) {
        console.log(`🎭 Starting interactive test: ${propertyName}`);
      }
      
      try {
        const result = await originalMethod.apply(this, args);
        
        if (finalConfig.debug) {
          console.log(`✅ Interactive test completed: ${propertyName}`);
        }
        
        return result;
      } catch (error) {
        if (finalConfig.debug) {
          console.error(`❌ Interactive test failed: ${propertyName}`, error);
        }
        throw error;
      }
    };
    
    return descriptor;
  };
}