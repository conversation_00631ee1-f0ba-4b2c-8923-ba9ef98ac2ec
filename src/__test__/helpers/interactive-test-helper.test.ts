import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { InteractiveTestHelper, InteractiveSteps, Keys, type InteractiveStep } from '../helpers/interactive-test-helper';
import { CLITestingUtils, type CLITestResult } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';

describe('InteractiveTestHelper', () => {
  let cliInstance: CLITestResult;

  afterEach(async () => {
    if (cliInstance) {
      await CLITestingUtils.cleanup(cliInstance);
    }
  });

  describe('基础交互功能', () => {
    it('应该支持基本的确认对话框交互', async () => {
      // 创建一个简单的模拟确认脚本
      await TestUtils.withTempDir(async (tempDir) => {
        const script = `
          process.stdout.write("Are you sure you want to continue? (y/n): ");
          
          process.stdin.on('data', (data) => {
            const input = data.toString().trim().toLowerCase();
            if (input === 'y') {
              console.log("\\nConfirmed! Proceeding...");
              process.exit(0);
            } else if (input === 'n') {
              console.log("\\nCancelled!");
              process.exit(1);
            } else {
              process.stdout.write("Please enter y or n: ");
            }
          });
        `;

        const scriptPath = await TestUtils.createTempFile(script, '.js');
        cliInstance = await CLITestingUtils.renderCLI('node', [scriptPath]);

        // 等待确认提示并回答 'y'
        await InteractiveTestHelper.confirmDialog(
          cliInstance,
          /Are you sure.*\(y\/n\)/,
          true
        );

        // 验证结果
        const found = await cliInstance.findByText('Confirmed! Proceeding...');
        expect(found).toBe(true);

        const exitCode = await cliInstance.waitForExit(5000);
        expect(exitCode).toBe(0);

        await TestUtils.cleanupTempFile(scriptPath);
      });
    });

    it('应该支持文本输入交互', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const script = `
          process.stdout.write("Enter your name: ");
          
          process.stdin.on('data', (data) => {
            const name = data.toString().trim();
            console.log(\`\\nHello, \${name}!\`);
            process.exit(0);
          });
        `;

        const scriptPath = await TestUtils.createTempFile(script, '.js');
        cliInstance = await CLITestingUtils.renderCLI('node', [scriptPath]);

        // 输入名称
        await InteractiveTestHelper.inputText(
          cliInstance,
          'Enter your name:',
          'TestUser'
        );

        // 验证输出
        const found = await cliInstance.findByText('Hello, TestUser!');
        expect(found).toBe(true);

        await TestUtils.cleanupTempFile(scriptPath);
      });
    });
  });

  describe('复杂交互流程', () => {
    it('应该支持多步骤交互流程', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const script = `
          let step = 0;
          let name = '';
          let age = '';
          
          function nextQuestion() {
            switch(step) {
              case 0:
                process.stdout.write('What is your name? ');
                break;
              case 1:
                process.stdout.write('How old are you? ');
                break;
              case 2:
                process.stdout.write('Do you like programming? (y/n) ');
                break;
            }
          }
          
          process.stdin.on('data', (data) => {
            const input = data.toString().trim();
            
            switch(step) {
              case 0:
                name = input;
                console.log(\`\\nNice to meet you, \${name}!\`);
                step++;
                nextQuestion();
                break;
              case 1:
                age = input;
                console.log(\`\\nYou are \${age} years old.\`);
                step++;
                nextQuestion();
                break;
              case 2:
                if (input.toLowerCase() === 'y') {
                  console.log('\\nGreat! Keep coding!');
                } else {
                  console.log('\\nThat\\'s okay, there are many other interests!');
                }
                process.exit(0);
                break;
            }
          });
          
          nextQuestion();
        `;

        const scriptPath = await TestUtils.createTempFile(script, '.js');
        cliInstance = await CLITestingUtils.renderCLI('node', [scriptPath]);

        // 定义交互流程
        const interactiveFlow: InteractiveStep[] = [
          InteractiveSteps.input(/What is your name\?/, 'Alice', 'Enter name'),
          InteractiveSteps.input(/How old are you\?/, '25', 'Enter age'),
          InteractiveSteps.confirm(/Do you like programming\?/, true, 'Confirm programming interest')
        ];

        // 执行交互流程
        const result = await InteractiveTestHelper.simulateUserFlow(
          cliInstance,
          interactiveFlow,
          { debug: true }
        );

        // 验证结果
        expect(result.success).toBe(true);
        expect(result.completedSteps).toBe(3);
        expect(result.totalSteps).toBe(3);

        // 验证输出内容
        expect(await cliInstance.findByText('Nice to meet you, Alice!')).toBe(true);
        expect(await cliInstance.findByText('You are 25 years old.')).toBe(true);
        expect(await cliInstance.findByText('Great! Keep coding!')).toBe(true);

        await TestUtils.cleanupTempFile(scriptPath);
      });
    });

    it('应该正确处理交互流程中的错误', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const script = `
          console.log("Waiting for input...");
          setTimeout(() => {
            console.log("This prompt will not appear");
            process.exit(0);
          }, 3000);
        `;

        const scriptPath = await TestUtils.createTempFile(script, '.js');
        cliInstance = await CLITestingUtils.renderCLI('node', [scriptPath]);

        // 定义一个会失败的交互流程（等待不存在的提示）
        const interactiveFlow: InteractiveStep[] = [
          InteractiveSteps.input(/Non-existent prompt/, 'test', 'Wait for non-existent prompt')
        ];

        const result = await InteractiveTestHelper.simulateUserFlow(
          cliInstance,
          interactiveFlow,
          { defaultTimeout: 1000, debug: true }
        );

        // 验证失败结果
        expect(result.success).toBe(false);
        expect(result.completedSteps).toBe(0);
        expect(result.failedStep).toBeDefined();
        expect(result.failedStep?.stepIndex).toBe(0);

        await TestUtils.cleanupTempFile(scriptPath);
      });
    });
  });

  describe('键盘控制', () => {
    it('应该支持发送控制键序列', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建一个监听输入的脚本
        const script = `
          console.log("Press Ctrl+C to exit");
          
          process.on('SIGINT', () => {
            console.log("\\nReceived SIGINT, exiting gracefully...");
            process.exit(0);
          });
          
          // 监听标准输入的 Ctrl+C 字符 (\\u0003)
          process.stdin.on('data', (data) => {
            if (data.toString() === '\\u0003') {
              console.log("\\nReceived Ctrl+C input, exiting gracefully...");
              process.exit(0);
            }
          });
          
          // 保持进程运行
          const interval = setInterval(() => {}, 1000);
          
          // 3秒后自动退出以防止无限等待
          setTimeout(() => {
            clearInterval(interval);
            process.exit(1);
          }, 3000);
        `;

        const scriptPath = await TestUtils.createTempFile(script, '.js');
        cliInstance = await CLITestingUtils.renderCLI('node', [scriptPath]);

        // 等待提示消息
        await cliInstance.waitForOutput('Press Ctrl+C to exit');

        // 发送 Ctrl+C 字符
        await InteractiveTestHelper.sendKeySequence(cliInstance, Keys.CTRL_C);

        // 等待进程退出
        const exitCode = await cliInstance.waitForExit(5000);
        expect(exitCode).toBe(0);

        // 验证优雅退出消息
        const found = await cliInstance.findByText('Received Ctrl+C input, exiting gracefully...');
        expect(found).toBe(true);

        await TestUtils.cleanupTempFile(scriptPath);
      });
    });
  });

  describe('边界条件和错误处理', () => {
    it('应该处理超时情况', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const script = `
          console.log("This script will not prompt for input");
          setTimeout(() => process.exit(0), 2000);
        `;

        const scriptPath = await TestUtils.createTempFile(script, '.js');
        cliInstance = await CLITestingUtils.renderCLI('node', [scriptPath]);

        // 尝试等待一个不存在的提示，应该超时
        await expect(
          InteractiveTestHelper.waitForPrompt(cliInstance, /Enter something:/, 500)
        ).rejects.toThrow(/Timeout waiting for prompt/);

        await TestUtils.cleanupTempFile(scriptPath);
      });
    });

    it('应该支持调试输出', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const script = `
          console.log("Debug test output");
          console.error("Error output for testing");
          process.exit(0);
        `;

        const scriptPath = await TestUtils.createTempFile(script, '.js');
        cliInstance = await CLITestingUtils.renderCLI('node', [scriptPath]);

        await cliInstance.waitForExit();

        // 测试调试输出功能（应该不会抛出错误）
        expect(() => {
          InteractiveTestHelper.debugOutput(cliInstance, 'Test Debug');
        }).not.toThrow();

        await TestUtils.cleanupTempFile(scriptPath);
      });
    });
  });

  describe('高级功能', () => {
    it('应该支持多行文本输入', async () => {
      await TestUtils.withTempDir(async (tempDir) => {
        const script = `
          console.log("Enter multiple lines (send 'END' to finish):");
          
          let lines = [];
          
          process.stdin.on('data', (data) => {
            const input = data.toString().trim();
            if (input === 'END') {
              console.log("Lines entered:");
              lines.forEach((line, index) => {
                console.log(\`\${index + 1}: \${line}\`);
              });
              process.exit(0);
            } else {
              lines.push(input);
              console.log(\`Received: \${input}\`);
            }
          });
        `;

        const scriptPath = await TestUtils.createTempFile(script, '.js');
        cliInstance = await CLITestingUtils.renderCLI('node', [scriptPath]);

        // 等待提示
        await cliInstance.waitForOutput('Enter multiple lines');

        // 发送多行输入
        const lines = ['First line', 'Second line', 'Third line', 'END'];
        for (const line of lines) {
          await cliInstance.userEvent.type(line + '\\n');
          await TestUtils.wait(100); // 短暂等待
        }

        // 验证输出
        expect(await cliInstance.findByText('Lines entered:')).toBe(true);
        expect(await cliInstance.findByText('1: First line')).toBe(true);
        expect(await cliInstance.findByText('2: Second line')).toBe(true);
        expect(await cliInstance.findByText('3: Third line')).toBe(true);

        await TestUtils.cleanupTempFile(scriptPath);
      });
    });
  });
});

// 测试便捷步骤创建器
describe('InteractiveSteps 便捷方法', () => {
  it('应该正确创建各种类型的交互步骤', () => {
    // 测试确认步骤
    const confirmStep = InteractiveSteps.confirm(/Continue\?/, true, 'Test confirm');
    expect(confirmStep.waitFor).toEqual(/Continue\?/);
    expect(confirmStep.input).toBe('y');
    expect(confirmStep.pressEnter).toBe(true);
    expect(confirmStep.description).toBe('Test confirm');

    // 测试输入步骤
    const inputStep = InteractiveSteps.input(/Enter name:/, 'John');
    expect(inputStep.waitFor).toEqual(/Enter name:/);
    expect(inputStep.input).toBe('John');
    expect(inputStep.pressEnter).toBe(true);

    // 测试选择步骤
    const selectStep = InteractiveSteps.select(/Choose option:/, 2);
    expect(selectStep.waitFor).toEqual(/Choose option:/);
    expect(selectStep.input).toBe('2');
    expect(selectStep.pressEnter).toBe(true);

    // 测试等待步骤
    const waitStep = InteractiveSteps.wait(/Loading.../, 2000);
    expect(waitStep.waitFor).toEqual(/Loading.../);
    expect(waitStep.input).toBe('');
    expect(waitStep.delay).toBe(2000);

    // 测试按键步骤
    const keyStep = InteractiveSteps.key(/Press any key/, Keys.SPACE);
    expect(keyStep.waitFor).toEqual(/Press any key/);
    expect(keyStep.input).toBe(Keys.SPACE);
    expect(keyStep.pressEnter).toBe(false);
  });
});