/**
 * CLI 测试调试体验增强工具
 * 提供更好的测试失败诊断和调试能力
 */

import type { CLITestResult } from './cli-testing-utils';
import { CLITestingUtils } from './cli-testing-utils';

export interface DebugSession {
  sessionId: string;
  command: string;
  args: string[];
  startTime: number;
  endTime?: number;
  instance?: CLITestResult;
  snapshots: DebugSnapshot[];
  error?: Error;
  diagnostics: DiagnosticInfo[];
}

export interface DebugSnapshot {
  timestamp: number;
  stdout: string;
  stderr: string;
  memoryUsage: NodeJS.MemoryUsage;
  processInfo: {
    pid?: number;
    isRunning: boolean;
    runtime: number;
  };
}

export interface DiagnosticInfo {
  type: 'warning' | 'error' | 'info' | 'performance';
  message: string;
  timestamp: number;
  details?: Record<string, any>;
  suggestions?: string[];
}

export interface TestFailureDiagnosis {
  failureType: 'timeout' | 'crash' | 'output_mismatch' | 'unexpected_exit' | 'resource_limit' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  rootCause: string;
  evidence: string[];
  suggestions: string[];
  relatedIssues: string[];
  debugInfo: {
    lastStdout: string;
    lastStderr: string;
    executionTime: number;
    memoryUsage: number;
    exitCode?: number;
  };
}

/**
 * 调试会话管理器
 */
class DebugSessionManager {
  private sessions = new Map<string, DebugSession>();
  private activeSession?: string;

  createSession(command: string, args: string[]): string {
    const sessionId = `debug_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session: DebugSession = {
      sessionId,
      command,
      args,
      startTime: Date.now(),
      snapshots: [],
      diagnostics: []
    };
    
    this.sessions.set(sessionId, session);
    this.activeSession = sessionId;
    
    return sessionId;
  }

  getSession(sessionId: string): DebugSession | undefined {
    return this.sessions.get(sessionId);
  }

  addSnapshot(sessionId: string, instance: CLITestResult): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const snapshot: DebugSnapshot = {
      timestamp: Date.now(),
      stdout: instance.stdout,
      stderr: instance.stderr,
      memoryUsage: process.memoryUsage(),
      processInfo: {
        pid: (instance as any).getProcessId?.(),
        isRunning: instance.isRunning,
        runtime: (instance as any).getRuntime?.() || 0
      }
    };

    session.snapshots.push(snapshot);
  }

  addDiagnostic(sessionId: string, diagnostic: DiagnosticInfo): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.diagnostics.push(diagnostic);
  }

  completeSession(sessionId: string, instance?: CLITestResult, error?: Error): void {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    session.endTime = Date.now();
    session.instance = instance;
    session.error = error;
  }

  getAllSessions(): DebugSession[] {
    return Array.from(this.sessions.values());
  }

  clearSessions(): void {
    this.sessions.clear();
    this.activeSession = undefined;
  }
}

/**
 * 智能诊断引擎
 */
class DiagnosticEngine {
  private static readonly PATTERNS = {
    timeout: [
      /timeout/i,
      /time.*out/i,
      /timed.*out/i,
      /execution.*too.*long/i
    ],
    crash: [
      /segmentation.*fault/i,
      /core.*dumped/i,
      /illegal.*instruction/i,
      /bus.*error/i,
      /abort/i
    ],
    memory: [
      /out.*of.*memory/i,
      /memory.*limit/i,
      /heap.*overflow/i,
      /allocation.*failed/i
    ],
    permission: [
      /permission.*denied/i,
      /access.*denied/i,
      /eacces/i,
      /eperm/i
    ],
    notFound: [
      /command.*not.*found/i,
      /no.*such.*file/i,
      /enoent/i,
      /cannot.*find/i
    ]
  };

  static diagnoseFailure(
    error: Error,
    instance?: CLITestResult,
    session?: DebugSession
  ): TestFailureDiagnosis {
    const errorMessage = error.message.toLowerCase();
    const stderr = instance?.stderr || '';
    const stdout = instance?.stdout || '';
    const combinedOutput = `${errorMessage} ${stderr} ${stdout}`.toLowerCase();

    // 确定失败类型和根本原因
    let failureType: TestFailureDiagnosis['failureType'] = 'unknown';
    let rootCause = error.message;
    let severity: TestFailureDiagnosis['severity'] = 'medium';
    const evidence: string[] = [];
    const suggestions: string[] = [];
    const relatedIssues: string[] = [];

    // 超时检测
    if (this.matchesPatterns(combinedOutput, this.PATTERNS.timeout)) {
      failureType = 'timeout';
      rootCause = '命令执行超时';
      severity = 'medium';
      evidence.push('检测到超时相关错误信息');
      suggestions.push('增加超时时间配置');
      suggestions.push('检查命令是否陷入无限循环');
      suggestions.push('优化命令执行性能');
    }

    // 崩溃检测
    else if (this.matchesPatterns(combinedOutput, this.PATTERNS.crash)) {
      failureType = 'crash';
      rootCause = '进程异常崩溃';
      severity = 'critical';
      evidence.push('检测到进程崩溃信号');
      suggestions.push('检查代码中的内存访问错误');
      suggestions.push('启用调试符号重新编译');
      suggestions.push('使用 valgrind 等工具检查内存问题');
    }

    // 内存问题检测
    else if (this.matchesPatterns(combinedOutput, this.PATTERNS.memory)) {
      failureType = 'resource_limit';
      rootCause = '内存资源不足';
      severity = 'high';
      evidence.push('检测到内存相关错误');
      suggestions.push('增加系统可用内存');
      suggestions.push('优化程序内存使用');
      suggestions.push('检查是否存在内存泄漏');
    }

    // 权限问题检测
    else if (this.matchesPatterns(combinedOutput, this.PATTERNS.permission)) {
      failureType = 'crash';
      rootCause = '权限不足';
      severity = 'medium';
      evidence.push('检测到权限相关错误');
      suggestions.push('检查文件和目录权限');
      suggestions.push('确认用户具有必要的执行权限');
      suggestions.push('考虑使用 sudo 或调整权限设置');
    }

    // 命令未找到检测
    else if (this.matchesPatterns(combinedOutput, this.PATTERNS.notFound)) {
      failureType = 'crash';
      rootCause = '命令或文件未找到';
      severity = 'high';
      evidence.push('检测到文件或命令不存在');
      suggestions.push('确认命令路径正确');
      suggestions.push('检查命令是否已安装');
      suggestions.push('验证工作目录设置');
    }

    // 退出码分析
    if (instance?.exitCode !== undefined && instance.exitCode !== 0) {
      if (failureType === 'unknown') {
        failureType = 'unexpected_exit';
        rootCause = `命令异常退出 (退出码: ${instance.exitCode})`;
      }
      evidence.push(`进程异常退出，退出码: ${instance.exitCode}`);
    }

    // 输出分析
    if (stderr.trim()) {
      evidence.push('标准错误输出不为空');
      if (stderr.length > 1000) {
        evidence.push('错误输出内容较多，可能包含详细错误信息');
      }
    }

    // 执行时间分析
    const executionTime = session ? (session.endTime || Date.now()) - session.startTime : 0;
    if (executionTime > 30000) {
      evidence.push('执行时间超过30秒，可能存在性能问题');
      suggestions.push('分析命令执行性能瓶颈');
    }

    return {
      failureType,
      severity,
      rootCause,
      evidence,
      suggestions,
      relatedIssues,
      debugInfo: {
        lastStdout: stdout.slice(-500), // 最后500字符
        lastStderr: stderr.slice(-500),
        executionTime,
        memoryUsage: process.memoryUsage().heapUsed,
        exitCode: instance?.exitCode
      }
    };
  }

  private static matchesPatterns(text: string, patterns: RegExp[]): boolean {
    return patterns.some(pattern => pattern.test(text));
  }
}

/**
 * CLI 测试调试增强器主类
 */
export class CLITestDebugEnhancer {
  private static sessionManager = new DebugSessionManager();
  private static isEnabled = true;

  /**
   * 启用或禁用调试功能
   */
  static setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  /**
   * 增强的 CLI 执行，带调试功能
   */
  static async executeWithDebug(
    command: string,
    args: string[] = [],
    config: any = {}
  ): Promise<{
    result: CLITestResult;
    session: DebugSession;
  }> {
    const sessionId = this.sessionManager.createSession(command, args);
    
    try {
      // 添加初始诊断信息
      this.sessionManager.addDiagnostic(sessionId, {
        type: 'info',
        message: `开始执行命令: ${command} ${args.join(' ')}`,
        timestamp: Date.now()
      });

      const instance = await CLITestingUtils.renderCLI(command, args, config);
      
      // 定期创建快照
      const snapshotInterval = setInterval(() => {
        if (instance.isRunning) {
          this.sessionManager.addSnapshot(sessionId, instance);
        }
      }, 1000);

      try {
        // 监控执行
        const startTime = Date.now();
        const result = await instance.waitForExit();
        const executionTime = Date.now() - startTime;
        
        clearInterval(snapshotInterval);
        
        // 添加性能诊断
        if (executionTime > 5000) {
          this.sessionManager.addDiagnostic(sessionId, {
            type: 'performance',
            message: `执行时间较长: ${executionTime}ms`,
            timestamp: Date.now(),
            suggestions: ['考虑优化命令性能', '检查是否有阻塞操作']
          });
        }

        // 最终快照
        this.sessionManager.addSnapshot(sessionId, instance);
        this.sessionManager.completeSession(sessionId, instance);

        const session = this.sessionManager.getSession(sessionId)!;
        return { result: instance, session };
        
      } catch (error) {
        clearInterval(snapshotInterval);
        this.sessionManager.completeSession(sessionId, instance, error as Error);
        throw error;
      }
      
    } catch (error) {
      this.sessionManager.completeSession(sessionId, undefined, error as Error);
      throw error;
    }
  }

  /**
   * 诊断测试失败
   */
  static diagnoseTestFailure(
    error: Error,
    instance?: CLITestResult,
    sessionId?: string
  ): TestFailureDiagnosis {
    const session = sessionId ? this.sessionManager.getSession(sessionId) : undefined;
    return DiagnosticEngine.diagnoseFailure(error, instance, session);
  }

  /**
   * 生成调试报告
   */
  static generateDebugReport(sessionId: string): string {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) {
      return '调试会话未找到';
    }

    const report: string[] = [];
    report.push('=== CLI 测试调试报告 ===');
    report.push(`会话ID: ${session.sessionId}`);
    report.push(`命令: ${session.command} ${session.args.join(' ')}`);
    report.push(`开始时间: ${new Date(session.startTime).toISOString()}`);
    
    if (session.endTime) {
      report.push(`结束时间: ${new Date(session.endTime).toISOString()}`);
      report.push(`执行时长: ${session.endTime - session.startTime}ms`);
    }

    if (session.error) {
      report.push('\n--- 错误信息 ---');
      report.push(session.error.message);
      
      // 添加智能诊断
      const diagnosis = this.diagnoseTestFailure(session.error, session.instance, sessionId);
      report.push('\n--- 智能诊断 ---');
      report.push(`失败类型: ${diagnosis.failureType}`);
      report.push(`严重程度: ${diagnosis.severity}`);
      report.push(`根本原因: ${diagnosis.rootCause}`);
      
      if (diagnosis.evidence.length > 0) {
        report.push('\n证据:');
        diagnosis.evidence.forEach(e => report.push(`  - ${e}`));
      }
      
      if (diagnosis.suggestions.length > 0) {
        report.push('\n建议:');
        diagnosis.suggestions.forEach(s => report.push(`  - ${s}`));
      }
    }

    if (session.diagnostics.length > 0) {
      report.push('\n--- 诊断信息 ---');
      session.diagnostics.forEach(d => {
        report.push(`[${d.type.toUpperCase()}] ${d.message}`);
        if (d.suggestions) {
          d.suggestions.forEach(s => report.push(`  建议: ${s}`));
        }
      });
    }

    if (session.snapshots.length > 0) {
      report.push('\n--- 执行快照 ---');
      const lastSnapshot = session.snapshots[session.snapshots.length - 1];
      report.push(`最后输出 (stdout): ${lastSnapshot.stdout.slice(-200) || '(无)'}`);
      report.push(`最后错误 (stderr): ${lastSnapshot.stderr.slice(-200) || '(无)'}`);
      report.push(`内存使用: ${(lastSnapshot.memoryUsage.heapUsed / 1024 / 1024).toFixed(1)}MB`);
    }

    report.push('\n=== 报告结束 ===');
    return report.join('\n');
  }

  /**
   * 获取所有调试会话摘要
   */
  static getSessionsSummary(): Array<{
    sessionId: string;
    command: string;
    duration: number;
    status: 'running' | 'completed' | 'failed';
    errorCount: number;
  }> {
    return this.sessionManager.getAllSessions().map(session => ({
      sessionId: session.sessionId,
      command: `${session.command} ${session.args.join(' ')}`,
      duration: (session.endTime || Date.now()) - session.startTime,
      status: session.error ? 'failed' : session.endTime ? 'completed' : 'running',
      errorCount: session.diagnostics.filter(d => d.type === 'error').length
    }));
  }

  /**
   * 清理调试数据
   */
  static clearDebugData(): void {
    this.sessionManager.clearSessions();
  }

  /**
   * 创建测试失败的详细报告
   */
  static createFailureReport(
    testName: string,
    error: Error,
    instance?: CLITestResult
  ): string {
    const diagnosis = this.diagnoseTestFailure(error, instance);
    
    const report: string[] = [];
    report.push(`❌ 测试失败: ${testName}`);
    report.push(`🔍 失败类型: ${diagnosis.failureType}`);
    report.push(`⚠️  严重程度: ${diagnosis.severity}`);
    report.push(`💡 根本原因: ${diagnosis.rootCause}`);
    
    if (diagnosis.debugInfo.exitCode !== undefined) {
      report.push(`📤 退出码: ${diagnosis.debugInfo.exitCode}`);
    }
    
    report.push(`⏱️  执行时间: ${diagnosis.debugInfo.executionTime}ms`);
    report.push(`💾 内存使用: ${(diagnosis.debugInfo.memoryUsage / 1024 / 1024).toFixed(1)}MB`);
    
    if (diagnosis.debugInfo.lastStderr) {
      report.push(`\n🚨 错误输出:\n${diagnosis.debugInfo.lastStderr}`);
    }
    
    if (diagnosis.debugInfo.lastStdout) {
      report.push(`\n📋 标准输出:\n${diagnosis.debugInfo.lastStdout}`);
    }
    
    if (diagnosis.suggestions.length > 0) {
      report.push('\n💡 建议解决方案:');
      diagnosis.suggestions.forEach((suggestion, index) => {
        report.push(`${index + 1}. ${suggestion}`);
      });
    }
    
    return report.join('\n');
  }
}