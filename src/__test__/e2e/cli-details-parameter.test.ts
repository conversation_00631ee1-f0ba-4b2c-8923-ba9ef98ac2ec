import { test, expect } from 'vitest';
import { spawn } from 'child_process';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

// 创建临时测试文件
async function createTestFile(content: string, filePath: string): Promise<void> {
  const dir = join(filePath, '..');
  await mkdir(dir, { recursive: true });
  await writeFile(filePath, content, 'utf-8');
}

test('CLI端到端测试 - --details参数传递', async () => {
  const testDir = 'test-temp-' + Date.now();
  const testFilePath = join(testDir, 'test.ts');
  
  // 创建测试TypeScript文件
  const testCode = `
function simpleFunction() {
  if (condition) {
    return true;
  }
  return false;
}

function complexFunction() {
  if (a && b) {
    for (let i = 0; i < 10; i++) {
      if (c || d) {
        return i;
      }
    }
  }
  return 0;
}
  `;
  
  await createTestFile(testCode, testFilePath);
  
  // 测试不带--details的CLI命令
  const resultWithoutDetails = await new Promise<string>((resolve, reject) => {
    const child = spawn('bun', ['src/cli/index.ts', testFilePath, '--quiet', '--format=json'], {
      stdio: 'pipe'
    });
    
    let output = '';
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve(output);
      } else {
        reject(new Error(`CLI exited with code ${code}`));
      }
    });
    
    setTimeout(() => {
      child.kill();
      reject(new Error('CLI timeout'));
    }, 10000);
  });
  
  // 测试带--details的CLI命令
  const resultWithDetails = await new Promise<string>((resolve, reject) => {
    const child = spawn('bun', ['src/cli/index.ts', testFilePath, '--details', '--quiet', '--format=json'], {
      stdio: 'pipe'
    });
    
    let output = '';
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve(output);
      } else {
        reject(new Error(`CLI exited with code ${code}`));
      }
    });
    
    setTimeout(() => {
      child.kill();
      reject(new Error('CLI timeout'));
    }, 10000);
  });
  
  // 解析JSON输出
  const withoutDetailsJson = JSON.parse(resultWithoutDetails);
  const withDetailsJson = JSON.parse(resultWithDetails);
  
  // 验证不带--details时，函数没有details字段或details为空
  const funcWithoutDetails = withoutDetailsJson.results[0]?.functions?.[0];
  expect(funcWithoutDetails).toBeDefined();
  expect(funcWithoutDetails.details).toBeUndefined();
  
  // 验证带--details时，函数有details字段且包含详细步骤
  const funcWithDetails = withDetailsJson.results[0]?.functions?.[0];
  expect(funcWithDetails).toBeDefined();
  expect(funcWithDetails.details).toBeDefined();
  expect(Array.isArray(funcWithDetails.details)).toBe(true);
  
  // 如果函数有复杂度，应该有详细步骤
  if (funcWithDetails.complexity > 0) {
    expect(funcWithDetails.details.length).toBeGreaterThan(0);
    
    // 验证详细步骤的结构
    const firstStep = funcWithDetails.details[0];
    expect(firstStep).toHaveProperty('line');
    expect(firstStep).toHaveProperty('increment');
    expect(firstStep).toHaveProperty('cumulative');
    expect(firstStep).toHaveProperty('ruleId');
    expect(firstStep).toHaveProperty('description');
    expect(firstStep).toHaveProperty('nestingLevel');
  }
  
  // 验证JSON Schema元数据
  expect(withDetailsJson.metadata).toBeDefined();
  expect(withDetailsJson.metadata.detailsEnabled).toBe(true);
  expect(withoutDetailsJson.metadata.detailsEnabled).toBe(false);
  
}, 15000); // 15秒超时