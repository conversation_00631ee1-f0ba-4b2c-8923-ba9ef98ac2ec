import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { spawn } from "child_process";
import { writeFileSync, unlinkSync, existsSync, mkdirSync, rmSync } from "fs";
import { join } from "path";

describe("Scan Exclude - 端到端集成测试", () => {
  const testDir = join(process.cwd(), "test-e2e-scan-exclude");
  
  const runCLI = async (args: string[]): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
    return new Promise((resolve, reject) => {
      const proc = spawn("node", ["dist/cli/index.js", ...args], {
        cwd: process.cwd(),
        stdio: ["pipe", "pipe", "pipe"]
      });
      
      let stdout = "";
      let stderr = "";
      
      const timeout = setTimeout(() => {
        proc.kill();
        reject(new Error(`CLI process timed out after 15 seconds`));
      }, 15000);
      
      proc.stdout?.on("data", (data) => {
        stdout += data.toString();
      });
      
      proc.stderr?.on("data", (data) => {
        stderr += data.toString();
      });
      
      proc.on("close", (code) => {
        clearTimeout(timeout);
        resolve({
          stdout,
          stderr,
          exitCode: code || 0
        });
      });
      
      proc.on("error", (err) => {
        clearTimeout(timeout);
        reject(err);
      });
    });
  };

  beforeEach(() => {
    if (!existsSync(testDir)) {
      mkdirSync(testDir, { recursive: true });
    }
  });

  afterEach(() => {
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
  });

  describe("Node.js 项目智能排除", () => {
    beforeEach(() => {
      // 创建完整的Node.js项目结构
      const structure = {
        "package.json": {
          name: "test-nodejs-project",
          version: "1.0.0",
          scripts: { test: "vitest" },
          dependencies: { lodash: "^4.17.21" }
        },
        "src/index.ts": `
function mainFunction() {
  if (condition1 && condition2) {    // +2
    for (let i = 0; i < 10; i++) {   // +3 (嵌套)
      if (i % 2 === 0) {             // +4 (双重嵌套)
        console.log(i);
      }
    }
  }
  return result;
}

export function utilityFunction() {
  if (isValid) {                     // +1
    return true;
  }
  return false;
}
        `,
        "src/components/Button.tsx": `
export function Button() {
  if (props.disabled) {              // +1
    return null;
  }
  
  const handleClick = () => {
    if (props.onClick) {             // +1
      props.onClick();
    }
  };
  
  return <button onClick={handleClick}>{props.children}</button>;
}
        `,
        "src/utils/helpers.ts": `
export function complexHelper() {
  try {                              // +1
    if (condition1) {                // +2 (嵌套)
      switch (type) {                // +3 (双重嵌套)
        case 'A':                    // +4 (三重嵌套)
          return handleA();
        case 'B':                    // +4 (三重嵌套)
          return handleB();
        default:                     // +4 (三重嵌套)
          return handleDefault();
      }
    }
  } catch (error) {                  // +1
    console.error(error);
  }
}
        `,
        "node_modules/lodash/index.js": `
// 这是第三方库代码，应该被排除
function cloneDeep(obj) {
  if (typeof obj !== 'object') {    // +1
    return obj;
  }
  // 复杂的克隆逻辑
  for (let key in obj) {             // +2 (嵌套)
    if (obj.hasOwnProperty(key)) {   // +3 (双重嵌套)
      result[key] = cloneDeep(obj[key]);
    }
  }
  return result;
}
        `,
        "dist/index.js": `
// 编译后的文件，应该被排除
function compiledFunction() {
  if (condition) {                   // +1
    return compiled;
  }
}
        `,
        "build/output.js": `
// 构建输出文件，应该被排除
function buildOutput() {
  for (let i = 0; i < 100; i++) {   // +1
    if (i % 10 === 0) {              // +2 (嵌套)
      console.log(i);
    }
  }
}
        `,
        ".next/static/chunks/main.js": `
// Next.js 构建文件，应该被排除
function nextJsChunk() {
  if (window) {                      // +1
    return window.location;
  }
}
        `,
        "coverage/lcov-report/index.html": `
<!-- 测试覆盖率报告，应该被排除 -->
        `,
        "tests/unit.test.ts": `
function testFunction() {
  if (testCondition) {               // +1
    expect(result).toBe(expected);
  }
}
        `,
        "src/main.spec.ts": `
function specFunction() {
  describe('test', () => {
    it('should work', () => {
      if (condition) {               // +1
        expect(true).toBe(true);
      }
    });
  });
}
        `,
        "cognitive.config.json": {
          failOnComplexity: 10,
          exclude: ["**/tests/**"],
          excludeDefaults: true,
          disableSmartExclusion: false
        }
      };

      // 创建目录和文件
      Object.entries(structure).forEach(([path, content]) => {
        const fullPath = join(testDir, path);
        const dir = fullPath.substring(0, fullPath.lastIndexOf('/'));
        
        if (!existsSync(dir)) {
          mkdirSync(dir, { recursive: true });
        }
        
        const fileContent = typeof content === 'object' 
          ? JSON.stringify(content, null, 2)
          : content;
        writeFileSync(fullPath, fileContent, 'utf8');
      });
    });

    test("应该智能检测Node.js项目并排除依赖文件夹", async () => {
      const result = await runCLI([testDir, "--details"]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该包含源文件
      expect(result.stdout).toContain("src/index.ts");
      expect(result.stdout).toContain("src/components/Button.tsx");
      expect(result.stdout).toContain("src/utils/helpers.ts");
      
      // 应该排除Node.js依赖项目录（检查详细结果中没有这些路径）
      expect(result.stdout).not.toContain("node_modules/lodash");
      expect(result.stdout).not.toContain("dist/index.js");
      expect(result.stdout).not.toContain("build/output.js");
      expect(result.stdout).not.toContain(".next/static");
      expect(result.stdout).not.toContain("coverage/lcov");
      
      // 应该显示项目检测信息
      expect(
        result.stdout.includes("检测到项目类型: nodejs") ||
        result.stdout.includes("nodejs")
      ).toBe(true);
    });

    test("应该使用配置文件排除规则", async () => {
      const configPath = join(testDir, "cognitive.config.json");
      const result = await runCLI([testDir, "--config", configPath]);
      
      expect(result.exitCode).toBe(0);
      
      // 配置文件排除 tests/** 目录
      expect(result.stdout).not.toContain("tests/");
      expect(result.stdout).not.toContain("unit.test.ts");
      
      // 应该包含源文件，检查更具体的输出
      expect(result.stdout).toContain("分析文件数: 3"); // 根据实际输出调整
    });

    test("应该支持 --include-deps 包含依赖文件", async () => {
      const result = await runCLI([testDir, "--include-deps", "--details"]);
      
      expect(result.exitCode).toBe(0);
      
      // --include-deps 应该分析更多文件，包含一些通常被排除的文件
      expect(result.stdout).toContain("分析文件数:"); // 验证有文件被分析
      
      // 在详细模式下应该显示文件信息
      expect(
        result.stdout.includes("详细结果") ||
        result.stdout.includes("📄")
      ).toBe(true);
    });

    test("应该支持多个CLI排除参数", async () => {
      const result = await runCLI([
        testDir,
        "--exclude", "**/*.test.ts",
        "--exclude-pattern", "**/src/components/**",
        "--exclude-pattern", "**/utils/**"
      ]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该排除指定的模式
      expect(result.stdout).not.toContain("test.ts");
      expect(result.stdout).not.toContain("Button.tsx");
      expect(result.stdout).not.toContain("helpers.ts");
      
      // 应该包含未被排除的文件，检查分析了至少一个文件
      expect(result.stdout).toContain("分析文件数: 1"); // 只剩下 src/index.ts
    });
  });

  describe("多语言项目检测", () => {
    beforeEach(() => {
      // 创建混合项目结构
      const multiLangStructure = {
        // Python项目文件
        "requirements.txt": `
django==4.2.0
requests==2.28.0
        `,
        "src/main.py": `
def complex_python_function():
    if condition1:                   # +1
        for item in items:           # +2 (嵌套)
            if item.is_valid():      # +3 (双重嵌套)
                process_item(item)
    return result
        `,
        "venv/lib/python3.9/site-packages/django/__init__.py": `
# Django库文件，应该被排除
def django_function():
    if settings.DEBUG:               # +1
        return debug_response()
        `,
        "__pycache__/main.cpython-39.pyc": "# 编译的Python文件，应该被排除",
        
        // Java项目文件
        "pom.xml": `
<?xml version="1.0" encoding="UTF-8"?>
<project>
    <groupId>com.example</groupId>
    <artifactId>test-project</artifactId>
    <version>1.0.0</version>
</project>
        `,
        "src/main/java/Main.java": `
// Java源文件
public class Main {
    public void complexMethod() {
        if (condition1) {            // +1
            for (int i = 0; i < 10; i++) {  // +2 (嵌套)
                if (i % 2 == 0) {    // +3 (双重嵌套)
                    process(i);
                }
            }
        }
    }
}
        `,
        "target/classes/Main.class": "// 编译的Java类，应该被排除",
        "target/maven-archiver/pom.properties": "// Maven构建文件，应该被排除",
        
        // Rust项目文件
        "Cargo.toml": `
[package]
name = "test-project"
version = "0.1.0"
edition = "2021"

[dependencies]
serde = "1.0"
        `,
        "src/lib.rs": `
// Rust源文件
fn complex_rust_function() {
    if condition1 {                  // +1
        for item in items {          // +2 (嵌套)
            match item.kind() {      // +3 (双重嵌套)
                Kind::A => process_a(), // +4 (三重嵌套)
                Kind::B => process_b(), // +4 (三重嵌套)
                _ => process_default(), // +4 (三重嵌套)
            }
        }
    }
}
        `,
        "target/debug/test-project": "// Rust可执行文件，应该被排除",
        "target/release/deps/serde.rlib": "// Rust依赖库，应该被排除",
        
        // Go项目文件
        "go.mod": `
module test-project

go 1.19

require (
    github.com/gorilla/mux v1.8.0
)
        `,
        "main.go": `
// Go源文件
func complexGoFunction() {
    if condition1 {                  // +1
        for _, item := range items { // +2 (嵌套)
            switch item.Type() {     // +3 (双重嵌套)
            case TypeA:              // +4 (三重嵌套)
                processA(item)
            case TypeB:              // +4 (三重嵌套)
                processB(item)
            default:                 // +4 (三重嵌套)
                processDefault(item)
            }
        }
    }
}
        `,
        "vendor/github.com/gorilla/mux/mux.go": `
// Go vendor依赖，应该被排除
func VendorFunction() {
    if condition {                   // +1
        return result
    }
}
        `
      };

      Object.entries(multiLangStructure).forEach(([path, content]) => {
        const fullPath = join(testDir, path);
        const dir = fullPath.substring(0, fullPath.lastIndexOf('/'));
        
        if (!existsSync(dir)) {
          mkdirSync(dir, { recursive: true });
        }
        
        writeFileSync(fullPath, content, 'utf8');
      });
    });

    test("应该检测Python项目并排除Python依赖", async () => {
      // 添加一个TypeScript文件以便有内容可以分析
      writeFileSync(join(testDir, "src/main.ts"), `
function tsFunction() {
  if (condition) {  // +1
    return true;
  }
}
      `);
      
      const result = await runCLI([testDir, "--details", "--show-excluded"]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该排除Python依赖项目录（检查具体文件路径而不是目录名）
      expect(result.stdout).not.toContain("venv/lib");
      expect(result.stdout).not.toContain("__pycache__/main");
      expect(result.stdout).not.toContain("site-packages/django");
      
      // 应该分析TypeScript文件
      expect(result.stdout).toContain("分析文件数:");
    });

    test("应该检测Java项目并排除Java构建目录", async () => {
      // 添加一个TypeScript文件以便有内容可以分析
      writeFileSync(join(testDir, "src/app.ts"), `
function javaStyleFunction() {
  if (condition) {  // +1
    return true;
  }
}
      `);
      
      const result = await runCLI([testDir, "--details"]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该排除Java构建目录（检查具体文件路径）
      expect(result.stdout).not.toContain("target/classes");
      expect(result.stdout).not.toContain("maven-archiver/pom");
      
      // 应该分析TypeScript文件
      expect(result.stdout).toContain("分析文件数:");
    });

    test("应该检测Rust项目并排除Rust构建目录", async () => {
      // 添加一个TypeScript文件以便有内容可以分析
      writeFileSync(join(testDir, "src/rust-app.ts"), `
function rustStyleFunction() {
  if (condition) {  // +1
    return true;
  }
}
      `);
      
      const result = await runCLI([testDir, "--details"]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该排除Rust target目录（检查具体文件路径）
      expect(result.stdout).not.toContain("target/debug/test-project");
      expect(result.stdout).not.toContain("target/release/deps");
      
      // 应该分析TypeScript文件
      expect(result.stdout).toContain("分析文件数:");
    });

    test("应该检测Go项目并排除Go vendor目录", async () => {
      // 添加一个TypeScript文件以便有内容可以分析
      writeFileSync(join(testDir, "src/go-app.ts"), `
function goStyleFunction() {
  if (condition) {  // +1
    return true;
  }
}
      `);
      
      const result = await runCLI([testDir, "--details"]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该排除Go vendor目录（检查具体文件路径）
      expect(result.stdout).not.toContain("vendor/github.com");
      expect(result.stdout).not.toContain("github.com/gorilla");
      
      // 应该分析TypeScript文件
      expect(result.stdout).toContain("分析文件数:");
    });
  });

  describe("复杂工作流程场景", () => {
    beforeEach(() => {
      // 创建大型项目结构
      const largeProjectStructure = {
        "package.json": {
          name: "large-project",
          version: "2.1.0",
          workspaces: ["packages/*"]
        },
        "lerna.json": {
          version: "independent",
          packages: ["packages/*"]
        },
        "packages/core/package.json": {
          name: "@project/core",
          version: "1.0.0"
        },
        "packages/core/src/index.ts": `
export function coreFunction() {
  if (process.env.NODE_ENV === 'production') {  // +1
    return optimizedVersion();
  } else if (process.env.NODE_ENV === 'development') {  // +1
    if (debugMode) {                             // +2 (嵌套)
      return debugVersion();
    }
    return developmentVersion();
  } else {                                       // +1
    return defaultVersion();
  }
}
        `,
        "packages/ui/src/components/Complex.tsx": `
interface Props {
  variant: 'primary' | 'secondary' | 'danger';
  size: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

export function Complex({ variant, size, disabled }: Props) {
  const getClassName = () => {
    let classes = ['button'];
    
    if (variant === 'primary') {                 // +1
      classes.push('btn-primary');
    } else if (variant === 'secondary') {        // +1
      classes.push('btn-secondary');
    } else if (variant === 'danger') {           // +1
      classes.push('btn-danger');
    }
    
    if (size === 'small') {                      // +1
      classes.push('btn-sm');
    } else if (size === 'large') {               // +1
      classes.push('btn-lg');
    }
    
    if (disabled) {                              // +1
      classes.push('btn-disabled');
    }
    
    return classes.join(' ');
  };
  
  return <button className={getClassName()}>{children}</button>;
}
        `,
        "apps/web/src/pages/dashboard.tsx": `
export function Dashboard() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const fetchData = async () => {
    try {                                        // +1
      setLoading(true);
      const response = await api.fetchData();
      
      if (response.status === 200) {             // +2 (嵌套)
        if (response.data) {                     // +3 (双重嵌套)
          setData(response.data);
        } else {                                 // +3 (双重嵌套)
          setError('No data received');
        }
      } else if (response.status === 401) {      // +2 (嵌套)
        redirectToLogin();
      } else {                                   // +2 (嵌套)
        setError('Failed to fetch data');
      }
    } catch (err) {                              // +1
      setError(err.message);
    } finally {                                  // +1
      setLoading(false);
    }
  };
  
  return <div>{/* Dashboard content */}</div>;
}
        `,
        ".cognitive-complexity.config.js": `
module.exports = {
  failOnComplexity: 15,
  exclude: [
    "**/node_modules/**",
    "**/dist/**",
    "**/*.test.*",
    "**/*.spec.*",
    "**/coverage/**"
  ],
  excludeDefaults: true,
  includeOverrides: [
    "packages/*/src/**"
  ],
  disableSmartExclusion: false,
  report: {
    json: "reports/complexity.json",
    html: "reports/complexity.html"
  }
};
        `,
        "node_modules/@types/react/index.d.ts": "// React类型定义，应该被排除",
        "apps/web/node_modules/next/package.json": "// Next.js包，应该被排除",
        "packages/core/dist/index.js": "// 构建输出，应该被排除",
        "coverage/lcov.info": "// 测试覆盖率，应该被排除"
      };

      Object.entries(largeProjectStructure).forEach(([path, content]) => {
        const fullPath = join(testDir, path);
        const dir = fullPath.substring(0, fullPath.lastIndexOf('/'));
        
        if (!existsSync(dir)) {
          mkdirSync(dir, { recursive: true });
        }
        
        const fileContent = typeof content === 'object' 
          ? JSON.stringify(content, null, 2)
          : content;
        writeFileSync(fullPath, fileContent, 'utf8');
      });
    });

    test("应该分析monorepo项目结构", async () => {
      const configPath = join(testDir, ".cognitive-complexity.config.js");
      const result = await runCLI([testDir, "--config", configPath, "--details"]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该包含packages中的源文件
      expect(result.stdout).toContain("packages/core/src/");
      expect(result.stdout).toContain("packages/ui/src/");
      expect(result.stdout).toContain("apps/web/src/");
      
      // 应该排除node_modules和构建文件（检查具体文件路径）
      expect(result.stdout).not.toContain("node_modules/@types");
      expect(result.stdout).not.toContain("dist/index.js");
      expect(result.stdout).not.toContain("coverage/lcov");
    });

    test("应该支持复杂的排除和包含覆盖规则", async () => {
      // 确保src目录存在并添加一个TypeScript文件以便有内容可以分析
      mkdirSync(join(testDir, "src"), { recursive: true });
      writeFileSync(join(testDir, "src/app.ts"), `
function complexAppFunction() {
  if (condition) {  // +1
    return true;
  }
}
      `);
      
      const result = await runCLI([
        testDir,
        "--exclude", "**/packages/**",          // 排除所有packages
        "--exclude-pattern", "**/apps/**",      // 排除所有apps
        "--exclude-defaults=false"              // 禁用默认排除
      ]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该排除packages和apps目录（检查具体文件）
      expect(result.stdout).not.toContain("packages/core/src");
      expect(result.stdout).not.toContain("apps/web/src");
      
      // 应该分析了我们添加的文件
      expect(result.stdout).toContain("分析文件数:");
    });

    test("应该处理质量门禁和复杂度阈值", async () => {
      const result = await runCLI([testDir, "--fail-on", "5"]);
      
      // 由于项目中有高复杂度函数，应该触发质量门禁失败
      expect(result.exitCode).toBe(1);
      expect(
        result.stderr.includes("Quality gate failed") || 
        result.stdout.includes("Quality gate failed")
      ).toBe(true);
    });

    test("应该生成详细的分析报告", async () => {
      const outputDir = join(testDir, "reports");
      mkdirSync(outputDir, { recursive: true });
      
      const result = await runCLI([
        testDir,
        "--format", "json",
        "--output-dir", outputDir,
        "--details",
        "--show-excluded"
      ]);
      
      expect(result.exitCode).toBe(0);
      
      // 检查报告文件是否生成
      expect(existsSync(join(outputDir, "complexity-report.json"))).toBe(true);
      
      // 验证输出包含详细信息
      expect(
        result.stdout.includes("检测到项目类型") ||
        result.stdout.includes("应用的排除模式")
      ).toBe(true);
    });
  });

  describe("性能和扩展性测试", () => {
    beforeEach(() => {
      // 创建包含大量文件的项目结构
      const baseDir = testDir;
      
      // 创建package.json
      writeFileSync(join(baseDir, "package.json"), JSON.stringify({
        name: "performance-test-project",
        version: "1.0.0"
      }, null, 2));
      
      // 创建大量源文件 (但数量控制在合理范围内以避免测试过慢)
      for (let i = 0; i < 20; i++) {
        const moduleDir = join(baseDir, `src/module${i}`);
        mkdirSync(moduleDir, { recursive: true });
        
        writeFileSync(join(moduleDir, "index.ts"), `
export function module${i}Function() {
  if (condition1) {                    // +1
    for (let j = 0; j < 10; j++) {     // +2 (嵌套)
      if (j % 2 === 0) {               // +3 (双重嵌套)
        process(j);
      }
    }
  }
  return result${i};
}
        `);
        
        writeFileSync(join(moduleDir, "utils.ts"), `
export function util${i}Function() {
  try {                                // +1
    if (validate${i}()) {              // +2 (嵌套)
      return execute${i}();
    }
  } catch (error) {                    // +1
    console.error(error);
  }
}
        `);
      }
      
      // 创建大量node_modules文件（应该被排除）
      const nodeModulesDir = join(baseDir, "node_modules");
      mkdirSync(nodeModulesDir, { recursive: true });
      
      for (let i = 0; i < 10; i++) {
        const packageDir = join(nodeModulesDir, `package${i}`);
        mkdirSync(packageDir, { recursive: true });
        
        writeFileSync(join(packageDir, "index.js"), `
// 第三方包文件，应该被智能排除
function thirdPartyFunction${i}() {
  if (condition) {                     // +1
    for (let k = 0; k < 100; k++) {    // +2 (嵌套)
      if (k % 3 === 0) {               // +3 (双重嵌套)
        complexOperation(k);
      }
    }
  }
}
        `);
      }
    });

    test("应该高效处理大量文件的项目", async () => {
      const startTime = Date.now();
      
      const result = await runCLI([testDir, "--details"]);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(result.exitCode).toBe(0);
      
      // 性能要求：处理应该在合理时间内完成 (30秒内)
      expect(duration).toBeLessThan(30000);
      
      // 应该分析一定数量的源文件（智能排除可能会影响具体数量）
      expect(result.stdout).toContain("分析文件数:");
      
      // 智能排除规则应该被应用（在输出中会显示应用信息）
      expect(
        result.stdout.includes("应用智能排除规则") ||
        result.stdout.includes("检测到项目类型")
      ).toBe(true);
      
      // 应该显示合理数量的分析文件（至少10个）
      const filesMatch = result.stdout.match(/分析文件数: (\d+)/);
      if (filesMatch && filesMatch[1]) {
        const fileCount = parseInt(filesMatch[1]);
        expect(fileCount).toBeGreaterThanOrEqual(10);
      }
    });

    test("应该处理大量排除规则而不显著影响性能", async () => {
      // 创建包含大量排除规则的配置
      const manyExcludes = [];
      for (let i = 0; i < 25; i++) {
        manyExcludes.push(`**/exclude${i}/**`);
      }
      
      const startTime = Date.now();
      
      const result = await runCLI([
        testDir,
        ...manyExcludes.flatMap(pattern => ["--exclude-pattern", pattern])
      ]);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(result.exitCode).toBe(0);
      
      // 即使有大量排除规则，性能也应该可接受
      expect(duration).toBeLessThan(20000);
    });

    test("应该在详细模式下报告排除操作的性能影响", async () => {
      const result = await runCLI([
        testDir, 
        "--details", 
        "--show-excluded",
        "--exclude-pattern", "**/temp/**",
        "--exclude-pattern", "**/cache/**"
      ]);
      
      expect(result.exitCode).toBe(0);
      
      // 应该显示排除相关的信息
      expect(
        result.stdout.includes("检测到项目类型") ||
        result.stdout.includes("应用智能排除规则") ||
        result.stdout.includes("应用的排除模式")
      ).toBe(true);
    });
  });

  describe("错误处理和边界条件", () => {
    test("应该处理不存在的项目目录", async () => {
      const nonExistentDir = join(testDir, "non-existent");
      const result = await runCLI([nonExistentDir]);
      
      expect(result.exitCode).toBe(1);
      expect(result.stderr.length > 0).toBe(true);
    });

    test("应该处理空项目目录", async () => {
      // testDir在beforeEach中创建，但是空的
      const result = await runCLI([testDir]);
      
      expect(result.exitCode).toBe(1);
      expect(result.stderr.includes("No TypeScript/JavaScript files found") ||
             result.stdout.includes("No TypeScript/JavaScript files found")).toBe(true);
    });

    test("应该处理无效的配置文件", async () => {
      const invalidConfigPath = join(testDir, "invalid-config.json");
      writeFileSync(invalidConfigPath, "{ invalid json");
      
      const result = await runCLI([testDir, "--config", invalidConfigPath]);
      
      // 应该回退到默认配置而不是失败
      expect(result.exitCode).toBe(1); // 因为目录为空
      expect(result.stderr.includes("Failed to load config") ||
             result.stdout.includes("Using default configuration")).toBe(true);
    });

    test("应该处理极其复杂的glob模式", async () => {
      // 创建一些测试文件，使用不会被排除的文件名
      writeFileSync(join(testDir, "main.ts"), "function main() { return 1; }");
      writeFileSync(join(testDir, "helper.ts"), "function helper() { return 2; }");
      
      const result = await runCLI([
        testDir,
        "--exclude-pattern", "**/{node_modules,dist,build,coverage,target}/**/*",
        "--exclude-pattern", "**/*.{test,spec}.{js,ts,jsx,tsx}",
        "--exclude-pattern", "**/{__tests__,__mocks__,fixtures,e2e}/**"
      ]);
      
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain("分析文件数: 2"); // 验证分析了正确数量的文件
    });

    test("应该处理混合的有效和无效排除模式", async () => {
      writeFileSync(join(testDir, "valid.ts"), "function valid() { return true; }");
      writeFileSync(join(testDir, "main.valid.ts"), "function mainValid() { return true; }");
      
      const result = await runCLI([
        testDir,
        "--exclude", "../dangerous-path",      // 无效
        "--exclude-pattern", "**/*.valid.ts",  // 有效
        "--exclude", "[unclosed-bracket",      // 无效
        "--exclude-pattern", "**/node_modules/**" // 有效
      ]);
      
      expect(result.exitCode).toBe(0);
      
      // 有效的排除模式应该生效，验证分析了正确数量的文件
      expect(result.stdout).toContain("分析文件数: 1"); // 只有 valid.ts 应该被包含
    });
  });
});