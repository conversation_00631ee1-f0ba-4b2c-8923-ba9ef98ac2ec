import { test, expect, describe, beforeEach, afterEach } from 'vitest';
import { exec } from 'child_process';
import { promisify } from 'util';
import { writeFileSync, mkdirSync, rmSync, existsSync } from 'fs';
import { join } from 'path';

const execAsync = promisify(exec);

describe('Context Lines E2E Tests', () => {
  const testDir = join(__dirname, '../../../test-temp-context-lines');
  const testFilePath = join(testDir, 'test.ts');

  beforeEach(() => {
    // 创建测试目录和文件
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true });
    }
    mkdirSync(testDir, { recursive: true });

    // 创建一个简单的测试文件
    const testCode = `
function complexFunction(a: number, b: number): number {
  if (a > 0) {
    for (let i = 0; i < b; i++) {
      if (i % 2 === 0) {
        console.log('even', i);
      } else {
        console.log('odd', i);
      }
    }
  }
  return a + b;
}
`;
    writeFileSync(testFilePath, testCode);
  });

  afterEach(() => {
    // 清理测试文件
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true });
    }
  });

  test('应该支持 --context-lines 参数', async () => {
    const cmd = `bun run src/cli/index.ts ${testDir} --details --show-context --context-lines 1`;
    
    try {
      const { stdout, stderr } = await execAsync(cmd, { 
        cwd: process.cwd(),
        timeout: 10000
      });
      
      // 检查命令执行成功
      expect(stderr).toBe('');
      expect(stdout).toContain('complexFunction');
      expect(stdout).toContain('test.ts');
      
    } catch (error) {
      // 如果因为上下文生成错误而失败，至少验证参数被接受
      const errorOutput = (error as any).stdout || (error as any).stderr || '';
      expect(errorOutput).not.toContain('unknown option');
      expect(errorOutput).not.toContain('--context-lines 必须为非负整数');
    }
  }, 15000);

  test('应该拒绝无效的 --context-lines 值', async () => {
    const cmd = `bun run src/cli/index.ts ${testDir} --details --show-context --context-lines -1`;
    
    try {
      await execAsync(cmd, { 
        cwd: process.cwd(),
        timeout: 5000
      });
      // 如果没有抛出错误，测试失败
      expect(true).toBe(false);
    } catch (error) {
      const errorOutput = (error as any).stderr || (error as any).stdout || '';
      expect(errorOutput).toContain('--context-lines 必须为非负整数');
    }
  });

  test('应该在大值时发出警告', async () => {
    const cmd = `bun run src/cli/index.ts ${testDir} --details --show-context --context-lines 25`;
    
    try {
      const { stdout, stderr } = await execAsync(cmd, { 
        cwd: process.cwd(),
        timeout: 10000
      });
      
      const output = stdout + stderr;
      expect(output).toContain('警告: 过大的上下文行数可能影响性能和可读性');
      
    } catch (error) {
      // 即使执行失败，也应该看到警告信息
      const errorOutput = (error as any).stdout || (error as any).stderr || '';
      expect(errorOutput).toContain('警告: 过大的上下文行数可能影响性能和可读性');
    }
  });

  test('应该在没有上下文标志时给出警告', async () => {
    const cmd = `bun run src/cli/index.ts ${testDir} --details --context-lines 5`;
    
    try {
      const { stdout, stderr } = await execAsync(cmd, { 
        cwd: process.cwd(),
        timeout: 10000
      });
      
      const output = stdout + stderr;
      // 应该包含参数验证警告
      expect(output).toContain('--context-lines 参数仅在使用 --show-context 或 --show-all-context 时有效');
      
    } catch (error) {
      const errorOutput = (error as any).stdout || (error as any).stderr || '';
      expect(errorOutput).toContain('--context-lines 参数仅在使用 --show-context 或 --show-all-context 时有效');
    }
  });

  test('应该支持 --context-lines 0', async () => {
    const cmd = `bun run src/cli/index.ts ${testDir} --details --show-context --context-lines 0`;
    
    try {
      const { stdout, stderr } = await execAsync(cmd, { 
        cwd: process.cwd(),
        timeout: 10000
      });
      
      // 检查命令执行成功
      expect(stderr).not.toContain('--context-lines 必须为非负整数');
      expect(stdout).toContain('complexFunction');
      
    } catch (error) {
      // 至少验证参数被正确解析
      const errorOutput = (error as any).stderr || '';
      expect(errorOutput).not.toContain('--context-lines 必须为非负整数');
      expect(errorOutput).not.toContain('unknown option');
    }
  });
});