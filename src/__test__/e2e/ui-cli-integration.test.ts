import { test, expect, describe, beforeEach, afterEach } from "vitest";
import { UIServer } from "../../ui/server";
import type { CognitiveConfig } from "../../config/types";
import { TestUtils, PerformanceTestUtils } from "../helpers/test-utils";
import { CLITestingUtils } from "../helpers/cli-testing-utils";
import { OutputValidator } from "../helpers/output-validator";
import { existsSync, unlinkSync, mkdirSync, writeFileSync, rmSync, mkdtempSync } from "fs";
import { join } from "path";
import { tmpdir } from "os";

describe("UIServer - CLI 端到端集成测试", () => {
  let server: UIServer;
  let config: CognitiveConfig;
  let testDir: string;
  let testFiles: string[];
  const testResultPath = join(process.cwd(), '.cognitive-complexity-result.json');

  beforeEach(async () => {
    // 创建临时测试目录
    testDir = mkdtempSync(join(tmpdir(), "cognitive-"));
    
    // 创建测试文件
    testFiles = [
      join(testDir, 'simple.ts'),
      join(testDir, 'complex.ts')
    ];

    // 简单函数
    writeFileSync(testFiles[0], `
function simpleFunction() {
  return true;
}

export function anotherSimple() {
  const x = 1;
  return x + 1;
}
    `);

    // 复杂函数
    writeFileSync(testFiles[1], `
function complexFunction() {
  for (let i = 0; i < 10; i++) {      // +1
    if (i % 2 === 0) {               // +1 + 1(嵌套) = +2
      while (true) {                 // +1 + 2(嵌套) = +3
        if (condition) {             // +1 + 3(嵌套) = +4
          break;
        }
      }
    } else if (i % 3 === 0) {        // +1 + 1(嵌套) = +2
      doSomething();
    }
  }
}

export function mixedLogical() {
  if (a && b || c && d) {            // +1 + 1(&&) + 1(||) + 1(&&) = +4
    return true;
  }
  return false;
}
    `);

    // 创建测试配置
    config = {
      failOnComplexity: 15,
      exclude: [],
      report: {},
      severityMapping: [
        { level: 'Critical', threshold: 20 },
        { level: 'Warning', threshold: 10 }
      ],
      ui: {
        port: 0,
        host: 'localhost',
        openBrowser: false
      }
    } as CognitiveConfig;

    // 清理之前的结果文件
    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }
  });

  afterEach(async () => {
    // 停止服务器
    if (server) {
      try {
        await server.stop();
      } catch (error) {
        // 忽略停止错误
      }
    }
    
    // 清理结果文件
    if (existsSync(testResultPath)) {
      unlinkSync(testResultPath);
    }

    // 清理测试目录
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }

    // 清理所有活动的 CLI 进程
    try {
      await CLITestingUtils.cleanupAll();
    } catch (error) {
      console.warn('Warning during CLI cleanup:', error);
    }
  });

  describe("增强的 CLI 集成测试 (使用 cli-testing-library)", () => {
    test("应该使用新的 CLI 测试框架运行分析并启动 Web UI", async () => {
      // 创建并启动 UIServer
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url, port } = await server.start();

      // 验证服务器已启动
      expect(url).toMatch(/^http:\/\/localhost:\d+$/);
      expect(port).toBeGreaterThan(0);

      // 验证初始状态：没有结果
      const statusResponse = await fetch(`${url}/api/status`);
      const statusData = await statusResponse.json();
      expect(statusData.hasResults).toBe(false);

      // 使用新的 CLI 测试框架运行分析命令
      await TestUtils.withTempDir(async (tempDir) => {
        // 创建测试文件
        const testFile = join(tempDir, 'test.ts');
        writeFileSync(testFile, `
function complexFunction() {
  for (let i = 0; i < 10; i++) {      // +1
    if (i % 2 === 0) {               // +1 + 1(嵌套) = +2
      while (true) {                 // +1 + 2(嵌套) = +3
        if (condition) {             // +1 + 3(嵌套) = +4
          break;
        }
      }
    } else if (i % 3 === 0) {        // +1 + 1(嵌套) = +2
      doSomething();
    }
  }
}

export function simpleFunction() {
  const x = 1;
  return x + 1;
}
        `.trim());

        // 使用新的 CLI 测试框架执行命令
        const cliResult = await TestUtils.executeCLITest('bun', [
          'run', 'src/cli/index.ts',
          testFile,
          '--format', 'json'
        ]);

        // 验证 CLI 成功执行
        await TestUtils.expectCLISuccess(cliResult);

        // 验证输出包含预期内容
        await TestUtils.expectCLIOutput(cliResult, [
          'complexFunction',
          'simpleFunction'
        ]);

        // 清理 CLI 实例
        await CLITestingUtils.cleanup(cliResult);
      });

      // 注意：当前的 CLI 工具不会自动向 UIServer 写入结果
      // 这是一个设计决定，CLI 和 Web UI 是独立的工具
      // 所以这里不验证 Web UI 状态的自动更新

      // 验证 Web UI 的基本状态（无结果时）
      const finalStatusResponse = await fetch(`${url}/api/status`);
      const finalStatusData = await finalStatusResponse.json();
      expect(finalStatusData.hasResults).toBe(false);
    });

    test("应该支持不同的 CLI 参数和配置选项", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      await TestUtils.withTempDir(async (tempDir) => {
        // 创建测试配置文件
        const configFile = join(tempDir, 'cognitive.config.js');
        writeFileSync(configFile, `
export default {
  failOnComplexity: 10,
};
        `.trim());

        // 创建简单测试文件
        const testFile = join(tempDir, 'simple.ts');
        writeFileSync(testFile, `
export function add(a: number, b: number): number {
  return a + b;
}
        `.trim());

        // 测试带配置文件的 CLI 执行
        const cliResult = await TestUtils.executeCLITest('bun', [
          'run', 'src/cli/index.ts',
          testFile,
          '--config', configFile,
          '--format', 'json'
        ]);

        // 验证成功执行
        await TestUtils.expectCLISuccess(cliResult);

        // 验证输出包含函数信息
        await TestUtils.expectCLIOutput(cliResult, ['add']);

        // 清理
        await CLITestingUtils.cleanup(cliResult);
      });
    });

    test("应该处理 CLI 错误情况和异常输出", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      await server.start();

      // 测试无效配置文件错误处理
      await TestUtils.withTempDir(async (tempDir) => {
        const invalidConfigFile = join(tempDir, 'invalid.config.js');
        writeFileSync(invalidConfigFile, 'invalid javascript syntax {{{');

        const testFile = join(tempDir, 'test.ts');
        writeFileSync(testFile, 'export function test() { return true; }');

        const invalidConfigResult = await TestUtils.executeCLITest('bun', [
          'run', 'src/cli/index.ts',
          testFile,
          '--config', invalidConfigFile
        ]);

        // 验证配置错误处理（可能成功或失败，取决于配置解析实现）
        // 这里我们只验证命令能正常完成，不做严格的成功/失败断言
        const exitCode = await invalidConfigResult.waitForExit(10000);
        expect(typeof exitCode).toBe('number');

        await CLITestingUtils.cleanup(invalidConfigResult);
      });
    });

    test("应该验证多种输出格式的 CLI 集成", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      await server.start();

      await TestUtils.withTempDir(async (tempDir) => {
        // 创建多个测试文件
        const files = [
          { name: 'utils.ts', content: 'export const helper = () => true;' },
          { name: 'main.ts', content: 'function main() { if (true) { return "hello"; } }' },
          { name: 'complex.ts', content: 'function complex() { for(let i=0;i<10;i++) { if(i%2) { while(true) break; } } }' }
        ];

        files.forEach(file => {
          writeFileSync(join(tempDir, file.name), file.content);
        });

        // 测试 JSON 输出格式
        const jsonResult = await TestUtils.executeCLITest('bun', [
          'run', 'src/cli/index.ts',
          tempDir,
          '--format', 'json'
        ]);

        await TestUtils.expectCLISuccess(jsonResult);
        await TestUtils.expectCLIOutput(jsonResult, ['helper', 'main', 'complex']);

        await CLITestingUtils.cleanup(jsonResult);

        // 测试文本输出格式
        const textResult = await TestUtils.executeCLITest('bun', [
          'run', 'src/cli/index.ts',
          tempDir,
          '--format', 'text'
        ]);

        await TestUtils.expectCLISuccess(textResult);
        await TestUtils.expectCLIOutput(textResult, ['复杂度', '分析']);

        await CLITestingUtils.cleanup(textResult);

        // 测试 HTML 报告输出
        const htmlResult = await TestUtils.executeCLITest('bun', [
          'run', 'src/cli/index.ts',
          tempDir,
          '--format', 'html',
          '--output-dir', tempDir
        ]);

        await TestUtils.expectCLISuccess(htmlResult);

        await CLITestingUtils.cleanup(htmlResult);
      });
    });

    test("应该支持并发 CLI 命令执行", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      await server.start();

      await TestUtils.withTempDir(async (tempDir) => {
        // 创建多个测试文件
        const testFiles = Array.from({ length: 3 }, (_, i) => {
          const fileName = `test${i}.ts`;
          const filePath = join(tempDir, fileName);
          writeFileSync(filePath, `
export function func${i}() {
  if (Math.random() > 0.5) {
    return ${i};
  }
  return 0;
}
          `.trim());
          return filePath;
        });

        // 并发执行多个 CLI 命令
        const cliPromises = testFiles.map(async (file, index) => {
          const result = await TestUtils.executeCLITest('bun', [
            'run', 'src/cli/index.ts',
            file,
            '--format', 'json'
          ]);

          await TestUtils.expectCLISuccess(result);
          await TestUtils.expectCLIOutput(result, [`func${index}`]);

          return result;
        });

        const results = await Promise.all(cliPromises);

        // 验证所有命令都成功完成
        expect(results).toHaveLength(3);

        // 清理所有实例
        await Promise.all(results.map(result => CLITestingUtils.cleanup(result)));
      });
    });

    test("应该监控 CLI 性能指标和资源使用", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      await server.start();

      await TestUtils.withTempDir(async (tempDir) => {
        // 创建较大的测试文件以测试性能
        const largeFile = join(tempDir, 'large.ts');
        const content = Array.from({ length: 10 }, (_, i) => `
function func${i}() {
  for (let j = 0; j < 10; j++) {
    if (j % 2 === 0) {
      while (j < 5) {
        if (Math.random() > 0.5) {
          break;
        }
        j++;
      }
    }
  }
}
        `).join('\n');

        writeFileSync(largeFile, content);

        // 使用性能监控执行 CLI
        const { result: cliResult, metrics } = await PerformanceTestUtils.measureCLIPerformance(
          async () => await TestUtils.executeCLITest('bun', [
            'run', 'src/cli/index.ts',
            largeFile,
            '--format', 'json'
          ])
        );

        // 验证性能指标
        expect(metrics.executionTime).toBeLessThan(30000); // 30 秒内完成
        expect(metrics.memoryUsage.used).toBeLessThan(500 * 1024 * 1024); // 500MB 内
        expect(metrics.testDetails.scenario).toBe('CLI Test');

        // 验证功能正确性
        await TestUtils.expectCLISuccess(cliResult);
        await TestUtils.expectCLIOutput(cliResult, ['func0', 'func9']); // 第一个和最后一个函数

        await CLITestingUtils.cleanup(cliResult);
      });
    });
  });

  describe("CLI 与 UIServer 完整工作流", () => {
    test("应该能够运行分析并启动Web UI", async () => {
      // 创建并启动UIServer
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url, port } = await server.start();

      // 验证服务器已启动
      expect(url).toMatch(/^http:\/\/localhost:\d+$/);
      expect(port).toBeGreaterThan(0);

      // 验证初始状态：没有结果
      const statusResponse = await fetch(`${url}/api/status`);
      const statusData = await statusResponse.json();
      expect(statusData.hasResults).toBe(false);

      // 模拟CLI分析并存储结果
      const mockResult = TestUtils.createMockAnalysisResult({
        summary: {
          totalComplexity: 20,
          averageComplexity: 10,
          filesAnalyzed: 2,
          functionsAnalyzed: 4,
          highComplexityFunctions: 1
        },
        results: [
          TestUtils.createMockFileResult({
            filePath: testFiles[0],
            complexity: 2,
            functions: [
              TestUtils.createMockFunctionResult({
                name: 'simpleFunction',
                complexity: 0,
                filePath: testFiles[0]
              }),
              TestUtils.createMockFunctionResult({
                name: 'anotherSimple',
                complexity: 2,
                filePath: testFiles[0]
              })
            ]
          }),
          TestUtils.createMockFileResult({
            filePath: testFiles[1],
            complexity: 18,
            functions: [
              TestUtils.createMockFunctionResult({
                name: 'complexFunction',
                complexity: 14,
                filePath: testFiles[1]
              }),
              TestUtils.createMockFunctionResult({
                name: 'mixedLogical',
                complexity: 4,
                filePath: testFiles[1]
              })
            ]
          })
        ]
      });

      await server.storeResult(mockResult);

      // 验证结果已存储
      const updatedStatusResponse = await fetch(`${url}/api/status`);
      const updatedStatusData = await updatedStatusResponse.json();
      expect(updatedStatusData.hasResults).toBe(true);

      // 验证可以获取结果
      const resultResponse = await fetch(`${url}/api/result`);
      expect(resultResponse.status).toBe(200);
      const resultData = await resultResponse.json();
      expect(resultData.summary.totalComplexity).toBe(20);

      // 验证报告页面可用
      const reportResponse = await fetch(`${url}/report`);
      expect(reportResponse.status).toBe(200);
      const reportHtml = await reportResponse.text();
      expect(reportHtml).toContain('认知复杂度分析报告');
    });

    test("Web UI 应该实时反映分析状态变化", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 初始状态：无结果
      let statusResponse = await fetch(`${url}/api/status`);
      let statusData = await statusResponse.json();
      expect(statusData.hasResults).toBe(false);

      // 存储第一个结果
      const result1 = TestUtils.createMockAnalysisResult({
        summary: { totalComplexity: 10, averageComplexity: 5, filesAnalyzed: 1, functionsAnalyzed: 2, highComplexityFunctions: 0 }
      });
      await server.storeResult(result1);

      // 验证状态更新
      statusResponse = await fetch(`${url}/api/status`);
      statusData = await statusResponse.json();
      expect(statusData.hasResults).toBe(true);

      // 清理结果
      await server.cleanupResult();

      // 验证状态重置
      statusResponse = await fetch(`${url}/api/status`);
      statusData = await statusResponse.json();
      expect(statusData.hasResults).toBe(false);

      // 存储新结果
      const result2 = TestUtils.createMockAnalysisResult({
        summary: { totalComplexity: 25, averageComplexity: 12.5, filesAnalyzed: 2, functionsAnalyzed: 2, highComplexityFunctions: 1 }
      });
      await server.storeResult(result2);

      // 验证新结果
      const resultResponse = await fetch(`${url}/api/result`);
      const resultData = await resultResponse.json();
      expect(resultData.summary.totalComplexity).toBe(25);
    });
  });

  describe("多文件分析集成", () => {
    test("应该正确处理多文件分析结果", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 创建多个文件的复杂分析结果
      const multiFileResult = TestUtils.createMockAnalysisResult({
        summary: {
          totalComplexity: 45,
          averageComplexity: 15,
          filesAnalyzed: 3,
          functionsAnalyzed: 6,
          highComplexityFunctions: 2
        },
        results: [
          TestUtils.createMockFileResult({
            filePath: 'src/utils.ts',
            complexity: 8,
            functions: [
              TestUtils.createMockFunctionResult({ name: 'helper1', complexity: 3 }),
              TestUtils.createMockFunctionResult({ name: 'helper2', complexity: 5 })
            ]
          }),
          TestUtils.createMockFileResult({
            filePath: 'src/main.ts',
            complexity: 22,
            functions: [
              TestUtils.createMockFunctionResult({ name: 'mainFunction', complexity: 18 }),
              TestUtils.createMockFunctionResult({ name: 'initFunction', complexity: 4 })
            ]
          }),
          TestUtils.createMockFileResult({
            filePath: 'src/config.ts',
            complexity: 15,
            functions: [
              TestUtils.createMockFunctionResult({ name: 'loadConfig', complexity: 8 }),
              TestUtils.createMockFunctionResult({ name: 'validateConfig', complexity: 7 })
            ]
          })
        ]
      });

      await server.storeResult(multiFileResult);

      // 验证API返回完整数据
      const resultResponse = await fetch(`${url}/api/result`);
      const resultData = await resultResponse.json();
      
      expect(resultData.summary.filesAnalyzed).toBe(3);
      expect(resultData.summary.functionsAnalyzed).toBe(6);
      expect(resultData.results).toHaveLength(3);
      
      // 验证每个文件的数据
      const fileResults = resultData.results;
      expect(fileResults[0].filePath).toBe('src/utils.ts');
      expect(fileResults[1].filePath).toBe('src/main.ts');
      expect(fileResults[2].filePath).toBe('src/config.ts');

      // 验证HTML报告包含所有文件
      const reportResponse = await fetch(`${url}/report`);
      const reportHtml = await reportResponse.text();
      expect(reportHtml).toContain('src/utils.ts');
      expect(reportHtml).toContain('src/main.ts');
      expect(reportHtml).toContain('src/config.ts');
    });
  });

  describe("配置集成测试", () => {
    test("应该使用正确的配置设置", async () => {
      const customConfig = {
        ...config,
        failOnComplexity: 8,
        severityMapping: [
          { level: 'Critical', threshold: 12 },
          { level: 'Warning', threshold: 6 },
          { level: 'Info', threshold: 3 }
        ],
        ui: {
          port: 0,
          host: '127.0.0.1',
          openBrowser: false
        }
      } as CognitiveConfig;

      server = new UIServer(customConfig, { port: 0, host: '127.0.0.1', openBrowser: false });
      const { url } = await server.start();

      // 验证服务器使用了正确的主机
      expect(url).toContain('127.0.0.1');

      // 创建测试结果并验证配置被正确使用
      const mockResult = TestUtils.createMockAnalysisResult();
      await server.storeResult(mockResult);

      const resultResponse = await fetch(`${url}/api/result`);
      expect(resultResponse.status).toBe(200);
    });
  });

  describe("错误恢复和边界情况", () => {
    test("应该处理损坏的结果文件", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();

      // 写入无效的JSON数据
      writeFileSync(testResultPath, '{ invalid json', 'utf-8');

      // 状态检查：在Hono实现中，只要文件存在就认为有结果
      // 但是实际获取结果时会失败
      const statusResponse = await fetch(`${url}/api/status`);
      const statusData = await statusResponse.json();
      expect(statusData.hasResults).toBe(true); // 文件存在，所以hasResults为true

      // 但是实际结果API应该处理JSON解析错误并返回404
      const resultResponse = await fetch(`${url}/api/result`);
      expect(resultResponse.status).toBe(404);
    });

    test("应该处理文件系统权限错误", async () => {
      server = new UIServer(config, { port: 0, openBrowser: false });
      await server.start();

      // 尝试存储结果到无效路径（这应该不会崩溃）
      const mockResult = TestUtils.createMockAnalysisResult();
      await expect(server.storeResult(mockResult)).resolves.toBeUndefined();
    });

    test("应该处理服务器重启场景", async () => {
      // 第一次启动
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url: url1, port: port1 } = await server.start();
      
      const mockResult = TestUtils.createMockAnalysisResult();
      await server.storeResult(mockResult);
      
      // 验证结果存在
      let statusResponse = await fetch(`${url1}/api/status`);
      let statusData = await statusResponse.json();
      expect(statusData.hasResults).toBe(true);
      
      // 停止服务器
      await server.stop();
      
      // 短暂等待确保端口释放
      await TestUtils.wait(100);
      
      // 重新启动服务器
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url: url2, port: port2 } = await server.start();
      
      // 验证新的服务器实例能够访问存储的结果
      statusResponse = await fetch(`${url2}/api/status`);
      statusData = await statusResponse.json();
      expect(statusData.hasResults).toBe(true);
      
      // 验证端口可能相同（因为前一个服务器已经停止）
      expect(typeof port2).toBe('number');
      expect(port2).toBeGreaterThan(0);
    });
  });

  describe("性能和并发测试", () => {
    test("应该在合理时间内完成完整工作流", async () => {
      const startTime = performance.now();
      
      server = new UIServer(config, { port: 0, openBrowser: false });
      const { url } = await server.start();
      
      const mockResult = TestUtils.createMockAnalysisResult({
        results: Array.from({ length: 10 }, (_, i) => 
          TestUtils.createMockFileResult({
            filePath: `src/file${i}.ts`,
            functions: Array.from({ length: 5 }, (_, j) =>
              TestUtils.createMockFunctionResult({ name: `func${j}` })
            )
          })
        )
      });
      
      await server.storeResult(mockResult);
      
      // 执行多个并发请求
      const promises = [
        fetch(`${url}/api/status`),
        fetch(`${url}/api/result`),
        fetch(`${url}/report`),
        fetch(`${url}/health`)
      ];
      
      const responses = await Promise.all(promises);
      const endTime = performance.now();
      
      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.status).toBeLessThan(500);
      });
      
      // 整个流程应该在合理时间内完成
      expect(endTime - startTime).toBeLessThan(2000); // 2秒内
    });
  });
});