/**
 * 并发安全和压力测试
 * 验证系统在高负载和并发环境下的稳定性
 */

import { describe, test, expect, beforeEach, afterEach } from 'vitest';
import { promises as fs } from 'fs';
import { join } from 'path';
import { ComplexityCalculator } from '../../core/calculator';
import { AdvancedDebugSystem } from '../../engine/debug-system';
import { AsyncRuleEngineImpl } from '../../engine/async-engine';
import { IntelligentCacheManager } from '../../cache/manager';

describe('并发安全和压力测试', () => {
  let tempDir: string;
  let testFiles: string[];
  let calculator: ComplexityCalculator;

  beforeEach(async () => {
    // 创建临时测试目录
    tempDir = join(process.cwd(), 'stress-test-' + Date.now());
    await fs.mkdir(tempDir, { recursive: true });
    
    // 创建测试文件
    testFiles = await createStressTestFiles(tempDir);
    
    // 初始化计算器
    calculator = new ComplexityCalculator({
      enableMixedLogicOperatorPenalty: true,
      recursionChainThreshold: 10,
    });
  });

  afterEach(async () => {
    // 清理临时文件
    try {
      await fs.rm(tempDir, { recursive: true, force: true });
    } catch (error) {
      // 忽略清理错误
    }
  });

  describe('并发安全测试', () => {
    test('多线程同时分析同一文件', async () => {
      const testFile = testFiles[0]!;
      const concurrency = 10;
      
      // 并发分析同一个文件
      const promises = Array(concurrency).fill(null).map(() => 
        calculator.calculateFile(testFile)
      );
      
      const results = await Promise.all(promises);
      
      // 所有结果应该一致
      expect(results).toHaveLength(concurrency);
      
      const firstResult = results[0]!;
      results.forEach((result, index) => {
        expect(result).toHaveLength(firstResult.length);
        expect(result[0]?.complexity).toBe(firstResult[0]?.complexity);
      });
      
      console.log(`并发安全测试: ${concurrency}个并发请求，所有结果一致`);
    });

    test('多文件并发分析', async () => {
      const concurrency = 8;
      const filesToAnalyze = testFiles.slice(0, Math.min(testFiles.length, concurrency));
      
      // 并发分析多个不同文件
      const promises = filesToAnalyze.map(file => calculator.calculateFile(file));
      
      const startTime = performance.now();
      const results = await Promise.all(promises);
      const endTime = performance.now();
      
      expect(results).toHaveLength(filesToAnalyze.length);
      results.forEach(result => {
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBeGreaterThan(0);
      });
      
      const processingTime = endTime - startTime;
      console.log(`多文件并发分析: ${filesToAnalyze.length}个文件，耗时 ${processingTime.toFixed(2)}ms`);
    });

    test('缓存并发访问安全', async () => {
      const cacheManager = new IntelligentCacheManager();
      const testData = Array(20).fill(null).map((_, i) => ({
        nodeType: `test-type-${i}`,
        info: { 
          complexity: i * 2, 
          baseComplexity: i,
          patterns: [`pattern-${i}`],
          optimizations: [`opt-${i}`],
          metadata: { index: i }
        }
      }));
      
      // 并发写入缓存 - 使用setCachedTypeInfo
      const writePromises = testData.map(({ nodeType, info }) => 
        Promise.resolve(cacheManager.setCachedTypeInfo(nodeType, info))
      );
      
      await Promise.all(writePromises);
      
      // 并发读取缓存 - 使用getCachedTypeInfo
      const readPromises = testData.map(({ nodeType }) => 
        Promise.resolve(cacheManager.getCachedTypeInfo(nodeType))
      );
      
      const readResults = await Promise.all(readPromises);
      
      // 验证所有数据都正确读取
      readResults.forEach((result, index) => {
        expect(result).toBeDefined();
        expect(result!.complexity).toBe(testData[index]!.info.complexity);
        expect(result!.baseComplexity).toBe(testData[index]!.info.baseComplexity);
      });
      
      console.log(`缓存并发测试: 写入/读取 ${testData.length} 个项目成功`);
    });

    test('调试系统并发安全', async () => {
      const debugSystem = new AdvancedDebugSystem({
        enabled: true,
        level: 'info',
        debugging: {
          enableBreakpoints: true,
          enableStepByStep: false,
          enableSnapshotCapture: true,
          maxSnapshots: 100,
        },
      });
      
      debugSystem.startSession();
      
      // 并发记录事件
      const concurrency = 15;
      const eventPromises = Array(concurrency).fill(null).map((_, i) => 
        Promise.resolve().then(() => {
          debugSystem.recordError(`Concurrent error ${i}`, new Error(`Test error ${i}`));
          debugSystem.recordWarning(`Concurrent warning ${i}`, { index: i });
        })
      );
      
      await Promise.all(eventPromises);
      
      const events = debugSystem.getEvents();
      expect(events.length).toBeGreaterThanOrEqual(concurrency * 2); // 每个循环产生2个事件
      
      debugSystem.endSession();
      
      console.log(`调试系统并发测试: 记录了 ${events.length} 个事件`);
    });
  });

  describe('压力测试', () => {
    test('大量文件批量处理', async () => {
      // 创建更多测试文件
      const largeFileSet = await createLargeFileSet(tempDir, 15);
      
      const startTime = performance.now();
      const memoryBefore = process.memoryUsage();
      
      // 批量处理
      const batchSize = 5;
      const results = [];
      
      for (let i = 0; i < largeFileSet.length; i += batchSize) {
        const batch = largeFileSet.slice(i, i + batchSize);
        const batchResults = await Promise.all(
          batch.map(file => calculator.calculateFile(file))
        );
        results.push(...batchResults);
      }
      
      const endTime = performance.now();
      const memoryAfter = process.memoryUsage();
      
      const processingTime = endTime - startTime;
      const memoryIncrease = (memoryAfter.heapUsed - memoryBefore.heapUsed) / 1024 / 1024;
      
      expect(results).toHaveLength(largeFileSet.length);
      expect(processingTime).toBeLessThan(10000); // 10秒内完成
      expect(Math.abs(memoryIncrease)).toBeLessThan(100); // 内存增长小于100MB
      
      console.log(`大量文件处理: ${largeFileSet.length}个文件，耗时 ${processingTime.toFixed(2)}ms，内存增长 ${memoryIncrease.toFixed(2)}MB`);
    });

    test('长时间运行稳定性', async () => {
      const iterations = 50;
      const testFile = testFiles[0]!;
      const times: number[] = [];
      
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        const result = await calculator.calculateFile(testFile);
        const endTime = performance.now();
        
        times.push(endTime - startTime);
        
        expect(result.length).toBeGreaterThan(0);
        
        // 每10次迭代检查一次性能稳定性
        if ((i + 1) % 10 === 0) {
          const recentTimes = times.slice(-10);
          const avgTime = recentTimes.reduce((a, b) => a + b, 0) / recentTimes.length;
          expect(avgTime).toBeLessThan(100); // 平均时间应该保持在合理范围内
        }
      }
      
      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      console.log(`长时间运行稳定性: ${iterations}次迭代，平均 ${avgTime.toFixed(2)}ms，最大 ${maxTime.toFixed(2)}ms，最小 ${minTime.toFixed(2)}ms`);
      
      // 验证性能稳定性 - 最大时间不应该超过平均时间的3倍
      expect(maxTime).toBeLessThan(avgTime * 3);
    });

    test('高并发压力测试', async () => {
      const concurrency = 20;
      const testFile = testFiles[0]!;
      
      const startTime = performance.now();
      
      // 创建大量并发任务
      const tasks = Array(concurrency).fill(null).map(async (_, index) => {
        const results = [];
        
        // 每个任务执行多次分析
        for (let i = 0; i < 5; i++) {
          const result = await calculator.calculateFile(testFile);
          results.push(result);
        }
        
        return {
          taskId: index,
          results,
          completedAt: performance.now()
        };
      });
      
      const taskResults = await Promise.all(tasks);
      const endTime = performance.now();
      
      const totalProcessingTime = endTime - startTime;
      const totalAnalyses = taskResults.reduce((sum, task) => sum + task.results.length, 0);
      
      expect(taskResults).toHaveLength(concurrency);
      expect(totalAnalyses).toBe(concurrency * 5);
      expect(totalProcessingTime).toBeLessThan(15000); // 15秒内完成
      
      // 验证所有结果都是一致的
      const firstResult = taskResults[0]!.results[0]!;
      taskResults.forEach(task => {
        task.results.forEach(result => {
          expect(result).toHaveLength(firstResult.length);
          expect(result[0]?.complexity).toBe(firstResult[0]?.complexity);
        });
      });
      
      console.log(`高并发压力测试: ${concurrency}个并发任务，总计${totalAnalyses}次分析，耗时 ${totalProcessingTime.toFixed(2)}ms`);
    });

    test('内存压力测试', async () => {
      const largeFiles = await createLargeFileSet(tempDir, 8);
      const memorySnapshots: number[] = [];
      
      // 记录初始内存
      if (global.gc) global.gc();
      memorySnapshots.push(process.memoryUsage().heapUsed);
      
      // 连续处理大量文件
      for (let round = 0; round < 3; round++) {
        const results = await Promise.all(
          largeFiles.map(file => calculator.calculateFile(file))
        );
        
        expect(results).toHaveLength(largeFiles.length);
        
        // 记录每轮后的内存使用
        memorySnapshots.push(process.memoryUsage().heapUsed);
      }
      
      // 分析内存增长趋势
      const initialMemory = memorySnapshots[0]!;
      const finalMemory = memorySnapshots[memorySnapshots.length - 1]!;
      const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024;
      
      expect(Math.abs(memoryIncrease)).toBeLessThan(200); // 内存增长小于200MB
      
      console.log(`内存压力测试: 初始内存 ${(initialMemory / 1024 / 1024).toFixed(2)}MB，最终内存 ${(finalMemory / 1024 / 1024).toFixed(2)}MB，增长 ${memoryIncrease.toFixed(2)}MB`);
    });

    test('异步引擎压力测试', async () => {
      const engine = new AsyncRuleEngineImpl({}, {
        enabled: true,
        level: 'warn', // 降低日志级别以减少输出
      });
      
      const testFilesForEngine = testFiles.slice(0, 6);
      const rounds = 3;
      
      const allResults = [];
      
      for (let round = 0; round < rounds; round++) {
        const startTime = performance.now();
        const results = await engine.analyzeFiles(testFilesForEngine);
        const endTime = performance.now();
        
        expect(results.size).toBe(testFilesForEngine.length);
        allResults.push({
          round,
          processingTime: endTime - startTime,
          fileCount: results.size
        });
      }
      
      // 验证性能一致性
      const avgTime = allResults.reduce((sum, r) => sum + r.processingTime, 0) / allResults.length;
      allResults.forEach(result => {
        expect(result.processingTime).toBeLessThan(avgTime * 2); // 性能不应该有大幅波动
      });
      
      console.log(`异步引擎压力测试: ${rounds}轮处理，平均耗时 ${avgTime.toFixed(2)}ms`);
    });
  });

  describe('错误恢复测试', () => {
    test('文件访问错误恢复', async () => {
      const validFiles = testFiles.slice(0, 2);
      const invalidFiles = ['/nonexistent/file1.ts', '/nonexistent/file2.ts'];
      const mixedFiles = [...validFiles, ...invalidFiles];
      
      // 分析混合的有效和无效文件
      const results = await Promise.allSettled(
        mixedFiles.map(file => calculator.calculateFile(file))
      );
      
      const fulfilled = results.filter(r => r.status === 'fulfilled');
      const rejected = results.filter(r => r.status === 'rejected');
      
      expect(fulfilled).toHaveLength(validFiles.length);
      expect(rejected).toHaveLength(invalidFiles.length);
      
      console.log(`错误恢复测试: ${fulfilled.length}个成功，${rejected.length}个失败`);
    });

    test('部分失败场景下的系统稳定性', async () => {
      const debugSystem = new AdvancedDebugSystem({
        enabled: true,
        level: 'error', // 只记录错误
      });
      
      debugSystem.startSession();
      
      // 模拟各种错误情况
      const errorScenarios = [
        () => debugSystem.recordError('Parse error', new SyntaxError('Invalid syntax')),
        () => debugSystem.recordError('File not found', new Error('ENOENT')),
        () => debugSystem.recordError('Memory error', new Error('Out of memory')),
        () => debugSystem.recordWarning('Performance warning', { threshold: 'exceeded' }),
      ];
      
      // 并发执行错误场景
      await Promise.all(
        errorScenarios.map(scenario => Promise.resolve(scenario()))
      );
      
      const events = debugSystem.getEvents();
      const errorEvents = events.filter(e => e.type === 'error');
      const warningEvents = events.filter(e => e.type === 'warning');
      
      expect(errorEvents.length).toBe(3);
      expect(warningEvents.length).toBe(1);
      
      // 系统应该仍然能正常运行
      const diagnostics = debugSystem.runDiagnostics();
      expect(Array.isArray(diagnostics)).toBe(true);
      
      debugSystem.endSession();
      
      console.log(`错误恢复测试: 处理了 ${errorEvents.length} 个错误和 ${warningEvents.length} 个警告`);
    });
  });
});

/**
 * 创建压力测试文件
 */
async function createStressTestFiles(tempDir: string): Promise<string[]> {
  const files: string[] = [];
  
  // 创建几个不同类型的测试文件
  for (let i = 0; i < 5; i++) {
    const fileName = join(tempDir, `stress-test-${i}.ts`);
    await fs.writeFile(fileName, generateStressTestContent(i));
    files.push(fileName);
  }
  
  return files;
}

/**
 * 创建大型文件集
 */
async function createLargeFileSet(tempDir: string, count: number): Promise<string[]> {
  const files: string[] = [];
  
  for (let i = 0; i < count; i++) {
    const fileName = join(tempDir, `large-file-${i}.ts`);
    await fs.writeFile(fileName, generateLargeFileContent(i));
    files.push(fileName);
  }
  
  return files;
}

/**
 * 生成压力测试文件内容
 */
function generateStressTestContent(index: number): string {
  return `
// 压力测试文件 ${index}
export class StressTest${index} {
  private data: any[] = [];
  
  public processData(input: any[]): any[] {
    const results = [];
    
    for (let i = 0; i < input.length; i++) {
      const item = input[i];
      
      if (item) {
        if (typeof item === 'object') {
          if (item.type === 'process') {
            if (item.priority > ${index}) {
              if (item.status === 'active') {
                results.push(this.processItem(item));
              } else {
                results.push(this.skipItem(item));
              }
            } else {
              results.push(this.defaultProcess(item));
            }
          } else {
            results.push(this.unknownProcess(item));
          }
        } else {
          results.push(this.primitiveProcess(item));
        }
      }
    }
    
    return results;
  }
  
  private processItem(item: any): any {
    return { ...item, processed: true, timestamp: Date.now() };
  }
  
  private skipItem(item: any): any {
    return { ...item, skipped: true, reason: 'inactive' };
  }
  
  private defaultProcess(item: any): any {
    return { ...item, default: true };
  }
  
  private unknownProcess(item: any): any {
    return { ...item, unknown: true };
  }
  
  private primitiveProcess(item: any): any {
    return { value: item, primitive: true };
  }
}
  `;
}

/**
 * 生成大型文件内容
 */
function generateLargeFileContent(index: number): string {
  let content = `
// 大型测试文件 ${index}
export class LargeClass${index} {
  private config: any = {
    id: ${index},
    name: 'large-class-${index}',
    settings: {}
  };
  
`;

  // 生成多个方法
  for (let i = 0; i < 10; i++) {
    content += `
  public method${i}(param1: number, param2: string, param3?: boolean): any {
    if (param1 > 0) {
      if (param2.length > 5) {
        if (param3) {
          if (param1 % 2 === 0) {
            return { result: param1 * 2, message: param2.toUpperCase() };
          } else {
            return { result: param1 * 3, message: param2.toLowerCase() };
          }
        } else {
          if (param2.includes('test')) {
            return { result: param1 + param2.length, message: 'test-mode' };
          } else {
            return { result: param1 - param2.length, message: 'normal-mode' };
          }
        }
      } else {
        if (param1 > 10) {
          return { result: param1 / 2, message: 'large-number' };
        } else {
          return { result: param1 * 1.5, message: 'small-number' };
        }
      }
    } else {
      if (param2 === 'default') {
        return { result: 0, message: 'default-case' };
      } else if (param2 === 'special') {
        return { result: -1, message: 'special-case' };
      } else {
        return { result: null, message: 'unknown-case' };
      }
    }
  }
`;
  }

  content += `
}
`;

  return content;
}