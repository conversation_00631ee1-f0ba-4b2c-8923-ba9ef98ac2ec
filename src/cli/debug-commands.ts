/**
 * 调试命令处理器
 * 集成调试系统到CLI，提供调试相关的命令和功能
 */

import { join } from 'path';
import { promises as fs } from 'fs';
import type { CLIOptions } from '../config/types';
import { AdvancedDebugSystem } from '../engine/debug-system';
import type { DebugSystemConfig } from '../engine/debug-system';
import { DebugVisualizer } from '../engine/debug-visualizer';
import type { VisualizationConfig } from '../engine/debug-visualizer';
import { AsyncRuleEngineImpl } from '../engine/async-engine';

// 调试CLI选项扩展
export interface DebugCLIOptions extends CLIOptions {
  // 调试模式
  debug?: boolean;
  debugLevel?: 'trace' | 'debug' | 'info' | 'warn' | 'error';
  
  // 断点和步进
  enableBreakpoints?: boolean;
  breakOnRule?: string[];
  breakOnComplexity?: number;
  stepByStep?: boolean;
  
  // 输出控制
  debugReport?: boolean;
  debugOutput?: string;
  visualReport?: boolean;
  
  // 性能分析
  enableProfiling?: boolean;
  enableTracing?: boolean;
  enableDiagnostics?: boolean;
  
  // 实时监控
  enableRealtimeMonitoring?: boolean;
  showPerformanceMetrics?: boolean;
}

/**
 * 调试增强的命令处理器
 */
export class EnhancedCommandProcessor {
  private debugSystem?: AdvancedDebugSystem;
  private debugVisualizer?: DebugVisualizer;
  private engine?: AsyncRuleEngineImpl;

  /**
   * 初始化调试系统
   */
  private initializeDebugSystem(options: DebugCLIOptions): void {
    if (!options.debug) return;

    // 调试系统配置
    const debugConfig: Partial<DebugSystemConfig> = {
      enabled: true,
      level: options.debugLevel || 'info',
      
      tracing: {
        enableRuleTracing: options.enableTracing !== false,
        enableNodeTracing: options.enableTracing !== false,
        enableFunctionTracing: options.enableTracing !== false,
        enableFileTracing: options.enableTracing !== false,
        enableCacheTracing: options.enableTracing !== false,
        enablePerformanceTracing: options.enableProfiling !== false,
      },
      
      debugging: {
        enableBreakpoints: options.enableBreakpoints !== false,
        enableStepByStep: options.stepByStep === true,
        enableSnapshotCapture: true,
        maxSnapshots: 100,
      },
      
      diagnostics: {
        enableProblemDetection: options.enableDiagnostics !== false,
        enableSolutionRecommendation: true,
        enableAutoFix: false,
        severityThreshold: 'warning',
      },
      
      output: {
        logToConsole: !options.quiet,
        logToFile: !!options.debugOutput,
        logFilePath: options.debugOutput,
        generateReports: options.debugReport !== false,
        includeStackTraces: options.debugLevel === 'trace',
      },
    };

    this.debugSystem = new AdvancedDebugSystem(debugConfig);

    // 可视化配置
    const visualConfig: Partial<VisualizationConfig> = {
      charts: {
        enableTimeline: true,
        enableFlowGraph: true,
        enablePerformanceBars: options.enableProfiling !== false,
        enableHeatMap: true,
      },
      output: {
        includeRawData: options.debugLevel === 'trace',
        includeSourceCode: options.debugLevel === 'trace',
        enableInteractivity: options.visualReport !== false,
        compressionLevel: 'basic',
      },
    };

    this.debugVisualizer = new DebugVisualizer(visualConfig);

    console.log(`🔍 Debug system initialized (level: ${debugConfig.level})`);
  }

  /**
   * 设置断点
   */
  private setupBreakpoints(options: DebugCLIOptions): void {
    if (!this.debugSystem || !options.enableBreakpoints) return;

    // 规则断点
    if (options.breakOnRule) {
      options.breakOnRule.forEach(ruleId => {
        const breakpointId = this.debugSystem!.addBreakpoint({
          enabled: true,
          type: 'rule',
          conditions: { ruleId },
          actions: {
            pauseExecution: options.stepByStep === true,
            logDetails: true,
            captureSnapshot: true,
          },
        });
        
        console.log(`🔴 Breakpoint set on rule: ${ruleId} (ID: ${breakpointId})`);
      });
    }

    // 复杂度断点
    if (options.breakOnComplexity !== undefined) {
      const breakpointId = this.debugSystem.addBreakpoint({
        enabled: true,
        type: 'complexity',
        conditions: {
          complexity: {
            operator: '>=',
            value: options.breakOnComplexity,
          },
        },
        actions: {
          pauseExecution: options.stepByStep === true,
          logDetails: true,
          captureSnapshot: true,
        },
      });
      
      console.log(`🔴 Breakpoint set on complexity >= ${options.breakOnComplexity} (ID: ${breakpointId})`);
    }
  }

  /**
   * 执行带调试的分析
   */
  public async executeWithDebug(options: DebugCLIOptions): Promise<void> {
    const startTime = performance.now();

    try {
      // 初始化调试系统
      this.initializeDebugSystem(options);

      // 开始调试会话
      if (this.debugSystem) {
        this.debugSystem.startSession();
        this.setupBreakpoints(options);
        
        // 设置事件监听器
        this.setupEventListeners(options);
      }

      // 初始化规则引擎（如果启用调试）
      if (options.debug) {
        this.engine = new AsyncRuleEngineImpl({}, {
          enabled: true,
        });
        
        // 注册默认规则（这里需要根据实际规则实现来调整）
        await this.registerDefaultRules();
      }

      // 执行分析
      await this.performAnalysisWithDebug(options);

      // 结束调试会话并生成报告
      if (this.debugSystem) {
        const visualTrace = this.debugSystem.endSession();
        await this.generateDebugReports(options, visualTrace);
      }

      const processingTime = (performance.now() - startTime) / 1000;
      
      if (!options.quiet) {
        console.log(`✅ Analysis completed in ${processingTime.toFixed(2)}s`);
        
        if (options.showPerformanceMetrics && this.engine) {
          this.showPerformanceMetrics();
        }
      }

    } catch (error) {
      if (this.debugSystem) {
        this.debugSystem.recordError('Analysis failed', error as Error);
      }
      
      console.error(`❌ Analysis failed: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }

  /**
   * 设置调试事件监听器
   */
  private setupEventListeners(options: DebugCLIOptions): void {
    if (!this.debugSystem) return;

    // 断点命中监听器
    this.debugSystem.addEventListener('breakpoint_hit', (event) => {
      console.log(`\n🔴 Breakpoint hit: ${JSON.stringify(event.data.breakpoint.conditions)}`);
      
      if (options.stepByStep) {
        this.handleStepByStepDebugging();
      }
    });

    // 错误监听器
    this.debugSystem.addEventListener('error', (event) => {
      console.error(`🚨 Debug Error: ${event.data.message}`);
      if (event.data.error?.stack && options.debugLevel === 'trace') {
        console.error(event.data.error.stack);
      }
    });

    // 警告监听器
    this.debugSystem.addEventListener('warning', (event) => {
      if (!options.quiet) {
        console.warn(`⚠️  Debug Warning: ${event.data.message}`);
      }
    });

    // 性能热点监听器
    this.debugSystem.addEventListener('hotspot_detected', (event) => {
      const hotspot = event.data;
      if (hotspot.severity === 'critical' || hotspot.severity === 'high') {
        console.warn(`🔥 Performance hotspot detected: ${hotspot.identifier} (${hotspot.metric}: ${hotspot.value.toFixed(2)})`);
      }
    });
  }

  /**
   * 处理步进调试
   */
  private handleStepByStepDebugging(): void {
    if (!this.debugSystem) return;

    console.log('\n--- Step-by-step debugging ---');
    console.log('Commands: (c)ontinue, (s)tep into, (n)ext, (o)ut, (i)nspect');
    
    // 这里在实际实现中应该使用readline或类似的库来处理用户输入
    // 为了演示，这里简化处理
    process.stdin.once('data', (input) => {
      const command = input.toString().trim().toLowerCase();
      
      switch (command) {
        case 'c':
        case 'continue':
          this.debugSystem!.continue();
          break;
        case 's':
        case 'step':
          this.debugSystem!.stepInto();
          break;
        case 'n':
        case 'next':
          this.debugSystem!.stepOver();
          break;
        case 'o':
        case 'out':
          this.debugSystem!.stepOut();
          break;
        case 'i':
        case 'inspect':
          this.showCurrentState();
          break;
        default:
          console.log('Unknown command. Use (c)ontinue, (s)tep, (n)ext, (o)ut, or (i)nspect');
          this.handleStepByStepDebugging();
      }
    });
  }

  /**
   * 显示当前调试状态
   */
  private showCurrentState(): void {
    if (!this.debugSystem) return;

    const state = this.debugSystem.getExecutionState();
    console.log('\n--- Current State ---');
    console.log(`Paused: ${state.isPaused}`);
    console.log(`Step Mode: ${state.stepMode}`);
    console.log(`Event Stack Depth: ${state.eventStack.length}`);
    
    if (state.currentSnapshot) {
      console.log(`Current Function: ${state.currentSnapshot.state.currentFunction || 'N/A'}`);
      console.log(`Current Rule: ${state.currentSnapshot.state.currentRule?.id || 'N/A'}`);
      console.log(`Nesting Level: ${state.currentSnapshot.state.nestingLevel}`);
      console.log(`Complexity: ${state.currentSnapshot.state.complexity}`);
    }
    
    if (state.activeBreakpoint) {
      console.log(`Active Breakpoint: ${state.activeBreakpoint.id} (${state.activeBreakpoint.type})`);
    }
    
    console.log('-------------------\n');
  }

  /**
   * 执行带调试的分析
   */
  private async performAnalysisWithDebug(options: DebugCLIOptions): Promise<void> {
    // 这里应该集成实际的分析逻辑
    // 由于当前的ComplexityCalculator不支持调试，这里提供一个简化的示例
    
    if (!options.paths || options.paths.length === 0) {
      throw new Error('No files specified for analysis');
    }

    console.log(`🔍 Analyzing ${options.paths.length} path(s) with debug enabled...`);

    for (const path of options.paths) {
      if (this.debugSystem) {
        this.debugSystem.recordNodeStart(
          { type: 'File', span: { start: { line: 1, column: 1 }, end: { line: 1, column: 1 } } } as any,
          {
            filePath: path,
            fileContent: '',
            ast: {} as any,
            functionName: '',
            nestingLevel: 0,
            config: {} as any,
            jsxMode: 'standard',
            rules: { core: [], jsx: [], plugins: [] },
            cache: {} as any,
            metrics: {
              totalNodes: 0,
              processedNodes: 0,
              cacheHits: 0,
              cacheMisses: 0,
              ruleExecutions: 0,
              parallelExecutions: 0,
              errors: 0,
            },
            plugins: [],
            customData: new Map(),
          }
        );
      }

      // 这里应该调用实际的分析逻辑
      await this.simulateFileAnalysis(path);

      if (this.debugSystem) {
        this.debugSystem.recordNodeEnd(
          { type: 'File' } as any,
          {
            node: { type: 'File' } as any,
            complexity: Math.floor(Math.random() * 20) + 1, // 模拟复杂度
            appliedRules: [],
            exemptions: [],
            children: [],
            aggregatedComplexity: 0,
            analysisTime: Math.random() * 100,
            cacheUtilization: Math.random(),
          }
        );
      }
    }
  }

  /**
   * 模拟文件分析（演示用）
   */
  private async simulateFileAnalysis(filePath: string): Promise<void> {
    // 模拟分析延迟
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    
    // 模拟一些规则执行
    const mockRules = ['complexity-rule', 'jsx-rule', 'logical-rule'];
    
    for (const ruleId of mockRules) {
      if (this.debugSystem) {
        const mockRule = { 
          id: ruleId, 
          name: ruleId, 
          priority: 1,
          evaluate: async () => ({
            ruleId,
            complexity: 1,
            isExempted: false,
            shouldIncreaseNesting: false,
            reason: `Mock rule ${ruleId} executed`,
            suggestions: [],
            metadata: { nodeType: 'MockNode' },
            executionTime: Math.random() * 50,
            cacheHit: false,
          }),
          canHandle: () => true,
          getDependencies: () => []
        };
        const mockNode = { type: 'MockNode', span: { start: { line: 1, column: 1 }, end: { line: 1, column: 1 } } };
        const mockContext = {
          filePath,
          fileContent: '',
          ast: {} as any,
          functionName: 'mockFunction',
          nestingLevel: 1,
          config: {} as any,
          jsxMode: 'standard' as const,
          rules: { core: [], jsx: [], plugins: [] },
          cache: {} as any,  
          metrics: {
            totalNodes: 0,
            processedNodes: 0,
            cacheHits: 0,
            cacheMisses: 0,
            ruleExecutions: 0,
            parallelExecutions: 0,
            errors: 0,
          },
          plugins: [],
          customData: new Map(),
        };
        
        this.debugSystem.recordRuleStart(mockRule, mockNode, mockContext);
        
        // 模拟规则执行
        await new Promise(resolve => setTimeout(resolve, Math.random() * 50));
        
        const mockResult = {
          ruleId,
          complexity: Math.floor(Math.random() * 5) + 1,
          isExempted: Math.random() < 0.2,
          shouldIncreaseNesting: false,
          reason: `Mock rule ${ruleId} executed`,
          suggestions: [],
          metadata: { nodeType: 'MockNode' },
          executionTime: Math.random() * 50,
          cacheHit: Math.random() < 0.3,
        };
        
        this.debugSystem.recordRuleEnd(mockRule, mockResult);
      }
    }
  }

  /**
   * 注册默认规则
   */
  private async registerDefaultRules(): Promise<void> {
    // 这里需要根据实际的规则实现来注册规则
    // 当前是模拟实现
    console.log('📋 Registering default rules...');
  }

  /**
   * 生成调试报告
   */
  private async generateDebugReports(options: DebugCLIOptions, visualTrace: any): Promise<void> {
    if (!this.debugSystem || !this.debugVisualizer) return;

    const events = this.debugSystem.getEvents();
    const diagnostics = this.debugSystem.getDiagnostics();
    const snapshots: any[] = []; // 简化处理

    // 生成文本报告
    if (!options.quiet) {
      const textReport = this.debugVisualizer.generateTextReport(events, visualTrace, diagnostics);
      console.log('\n' + '='.repeat(80));
      console.log('DEBUG REPORT SUMMARY');
      console.log('='.repeat(80));
      console.log(textReport.split('\n').slice(0, 20).join('\n')); // 显示前20行
      
      if (diagnostics.length > 0) {
        console.log(`\n🔍 Found ${diagnostics.length} diagnostic issues`);
        diagnostics.slice(0, 3).forEach((diagnostic, index) => {
          const icon = { info: 'ℹ️', warning: '⚠️', error: '❌', critical: '🚨' }[diagnostic.severity];
          console.log(`${index + 1}. ${icon} ${diagnostic.title}`);
        });
      }
    }

    // 生成HTML报告
    if (options.visualReport && options.debugOutput) {
      const htmlReport = this.debugVisualizer.generateHTMLReport(events, visualTrace, diagnostics, snapshots);
      const htmlPath = options.debugOutput.replace(/\.[^.]+$/, '.html');
      
      try {
        await fs.writeFile(htmlPath, htmlReport, 'utf8');
        console.log(`📊 Visual debug report saved to: ${htmlPath}`);
      } catch (error) {
        console.error(`❌ Failed to save HTML report: ${error}`);
      }
    }

    // 生成JSON报告
    if (options.debugOutput) {
      const jsonReport = this.debugVisualizer.generateJSONReport(events, visualTrace, diagnostics, snapshots);
      const jsonPath = options.debugOutput.endsWith('.json') ? options.debugOutput : `${options.debugOutput}.json`;
      
      try {
        await fs.writeFile(jsonPath, jsonReport, 'utf8');
        console.log(`💾 Debug data saved to: ${jsonPath}`);
      } catch (error) {
        console.error(`❌ Failed to save debug data: ${error}`);
      }
    }
  }

  /**
   * 显示性能指标
   */
  private showPerformanceMetrics(): void {
    if (!this.engine) return;

    const metrics = this.engine.getMetrics();
    const performanceReport = this.engine.generatePerformanceReport();

    console.log('\n📊 Performance Metrics:');
    console.log(`   Files Processed: ${metrics.filesProcessed}`);
    console.log(`   Functions Analyzed: ${metrics.functionsAnalyzed}`);
    console.log(`   Rules Executed: ${metrics.rulesExecuted}`);
    console.log(`   Cache Hit Rate: ${(metrics.cacheHitRate * 100).toFixed(1)}%`);
    console.log(`   Parallel Efficiency: ${(metrics.parallelEfficiency * 100).toFixed(1)}%`);
    console.log(`   Memory Usage: ${(metrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`);

    // 显示性能热点
    if (performanceReport.hotspots.length > 0) {
      console.log('\n🔥 Performance Hotspots:');
      performanceReport.hotspots.slice(0, 5).forEach((hotspot, index) => {
        console.log(`   ${index + 1}. ${hotspot.identifier} (${hotspot.type}): ${hotspot.value.toFixed(2)}ms`);
      });
    }

    // 显示建议
    if (performanceReport.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      performanceReport.recommendations.slice(0, 3).forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec.title}: ${rec.description}`);
      });
    }
  }
}