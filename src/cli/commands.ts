import fg from 'fast-glob';
import { promises as fs } from 'fs';
import { join, dirname } from 'path';
import chalk from 'chalk';
import type { CLIOptions, CognitiveConfig, ProjectDetectionResult } from '../config/types';
import type { AnalysisResult, FileResult } from '../core/types';
import { ConfigManager } from '../config/manager';
import { BaselineManager } from '../baseline/manager';
import { analyzeFile } from '../index'; // 使用新的analyzeFile函数
import { TextFormatter } from '../formatters/text';
import { JsonFormatter } from '../formatters/json';
import { HtmlFormatter } from '../formatters/html';
import { UIServer } from '../ui/server';
import { CLIUIHelper } from './ui-helper';
import { getCompatibilityService } from '../utils/compatibility-service';
import { getDefaultValidationService } from '../utils/concurrent-validation-service';

export class CommandProcessor {
  private config?: CognitiveConfig;
  private baselineManager: BaselineManager;
  private ui: CLIUIHelper;

  constructor() {
    this.baselineManager = new BaselineManager();
    this.ui = new CLIUIHelper();
  }

  /**
   * 验证CLI参数的有效性 - 使用并发验证服务
   */
  private async validateOptions(options: CLIOptions): Promise<void> {
    // 获取并发验证服务
    const validationService = getDefaultValidationService();
    
    if (!options.quiet) {
      this.ui.startSpinner('验证参数...');
    }
    
    try {
      // 执行并发验证
      const summary = await validationService.validateConcurrently(options);
      
      if (!options.quiet) {
        if (summary.valid) {
          this.ui.succeedSpinner(`参数验证通过 (${summary.passedRules}/${summary.totalRules} 规则)`);
        } else {
          this.ui.failSpinner(`参数验证失败 (${summary.failedRules}/${summary.totalRules} 规则)`);
        }
      }
      
      // 显示警告信息
      if (summary.warnings.length > 0 && !options.quiet) {
        summary.warnings.forEach(warning => {
          this.ui.warning(warning);
        });
      }
      
      // 如果有错误，抛出第一个错误
      if (!summary.valid && summary.errors.length > 0) {
        throw new Error(summary.errors[0]);
      }
      
      // 如果启用了性能分析，显示验证性能信息
      if (options.details && summary.executionTime > 100) {
        this.ui.info(`参数验证耗时: ${Math.round(summary.executionTime)}ms`);
      }
      
    } catch (error) {
      if (!options.quiet) {
        this.ui.failSpinner('参数验证失败');
      }
      throw error;
    }
  }

  public async execute(options: CLIOptions): Promise<boolean> {
    const startTime = performance.now();
    let compatibleOptions = options; // 默认使用原始选项
    
    try {
      // JSON输出模式应该静默运行，避免非JSON内容混入输出
      if (options.format === 'json') {
        compatibleOptions = { 
          ...options, 
          quiet: true,
          // 确保所有可能产生输出的选项都被抑制
          details: options.details, // 保持原始 details 设置，但输出会被静默处理
        };
      }
      
      // 兼容性检查 - 在其他操作之前执行
      const compatibilityService = getCompatibilityService();
      
      if (!compatibleOptions.quiet) {
        this.ui.startSpinner('检查系统兼容性...');
      }
      
      const compatibilityResult = await compatibilityService.checkBabelCodeFrameCompatibility();
      
      if (!compatibleOptions.quiet) {
        if (compatibilityResult.compatible) {
          this.ui.succeedSpinner('兼容性检查通过');
        } else {
          this.ui.succeedSpinner('兼容性检查完成（发现问题）');
          
          // 显示兼容性问题但不中断执行
          if (compatibilityResult.warnings.length > 0) {
            compatibilityResult.warnings.forEach(warning => {
              this.ui.warning(warning);
            });
          }
          
          if (compatibilityResult.errors.length > 0) {
            compatibilityResult.errors.forEach(error => {
              this.ui.warning(`兼容性错误: ${error}`);
            });
            this.ui.info('将使用降级模式继续执行');
          }
        }
      }
      
      // 应用兼容性设置到选项
      compatibleOptions = compatibilityService.applyCompatibilitySettings(options);
      
      // 参数验证 - 使用异步并发验证
      await this.validateOptions(compatibleOptions);
      
      // 加载配置文件 - 如果没有指定配置文件，从第一个文件路径开始搜索
      if (!compatibleOptions.quiet) {
        this.ui.startSpinner('加载配置文件...');
      }
      
      let searchFrom: string | undefined;
      if (!compatibleOptions.config && compatibleOptions.paths.length > 0) {
        // 从第一个路径的父目录开始搜索配置文件
        const firstPath = compatibleOptions.paths[0]!; // 使用非空断言，因为length > 0
        searchFrom = dirname(firstPath) || process.cwd();
      }
      this.config = await ConfigManager.loadConfig(compatibleOptions.config, searchFrom, compatibleOptions.quiet);

      if (!compatibleOptions.quiet) {
        this.ui.succeedSpinner('配置加载完成');
      }

      // 处理UI模式
      if (compatibleOptions.ui) {
        await this.handleUIMode(compatibleOptions);
        return true;
      }

      // 处理基线相关命令
      if (compatibleOptions.createBaseline) {
        await this.handleCreateBaseline(compatibleOptions.paths, compatibleOptions);
        return true;
      }

      if (compatibleOptions.updateBaseline) {
        await this.handleUpdateBaseline(compatibleOptions.paths, compatibleOptions);
        return true;
      }

      // 执行正常分析
      const result = await this.analyzeFiles(compatibleOptions.paths, compatibleOptions);

      // 应用过滤器
      const filteredResult = this.applyFilters(result, compatibleOptions);

      // 检查质量门禁 - 优先级：CLI显式参数 > 配置文件 > 系统默认值15
      const threshold =
        compatibleOptions.failOn !== undefined ? compatibleOptions.failOn : this.config.failOnComplexity ?? 15;
      
      // 添加调试信息
      if (!compatibleOptions.quiet) {
        console.log(`\n🔍 质量门禁检查 - 阈值: ${threshold}`);
      }
      
      // JSON 模式下，质量门禁失败不应该导致进程异常退出
      // 而应该将错误信息包含在 JSON 输出中，让调用者处理
      const qualityGatePassed = this.applyQualityGate(filteredResult, threshold, compatibleOptions);

      // 输出结果（质量门禁信息已经添加到结果中）
      await this.outputResults(filteredResult, compatibleOptions);

      // 添加更多调试信息
      if (!compatibleOptions.quiet) {
        console.log(`\n🔍 质量门禁结果: ${qualityGatePassed ? '✅ 通过' : '❌ 失败'}`);
        console.log(`🔍 输出格式: ${compatibleOptions.format}`);
        console.log(`🔍 即将执行的退出逻辑...`);
      }

      // 计算总耗时
      const processingTime = (performance.now() - startTime) / 1000;
      
      // 显示处理统计
      if (!compatibleOptions.quiet) {
        this.ui.showSummary({
          ...filteredResult.summary,
          processingTime
        });
        
        // 显示性能优化建议
        this.ui.showPerformanceTips({
          filesAnalyzed: filteredResult.summary.filesAnalyzed,
          processingTime,
          highComplexityFunctions: filteredResult.summary.highComplexityFunctions
        });
        
        // 如果有高复杂度函数，显示相关帮助
        if (filteredResult.summary.highComplexityFunctions > 0) {
          this.ui.showContextualHelp('high-complexity');
        }
      }

      if (!qualityGatePassed && compatibleOptions.format !== 'json') {
        return false; // 返回失败状态而不是直接退出
      }

      // 成功完成，返回成功状态
      return true;
    } catch (error) {
      if (!compatibleOptions.quiet) {
        this.ui.stopSpinner();
        const errorMessage = error instanceof Error ? error.message : String(error);
        this.ui.errorWithSuggestion(
          `处理命令时出错: ${errorMessage}`,
          '使用 --help 查看使用说明，或检查输入参数是否正确'
        );
        
        // 根据错误类型显示相关帮助
        if (errorMessage.includes('配置')) {
          this.ui.showContextualHelp('config-error');
        }
      }
      return false; // 返回错误状态而不是直接退出
    }
  }

  public async analyzeFiles(paths: string[], options: CLIOptions): Promise<AnalysisResult> {
    // 发现所有需要分析的文件
    if (!options.quiet) {
      this.ui.updateSpinner('发现分析文件...');
    }
    
    const files = await this.discoverFiles(paths, options);

    if (files.length === 0) {
      if (!options.quiet) {
        this.ui.errorWithSuggestion(
          '未找到需要分析的 TypeScript/JavaScript 文件',
          '检查文件路径是否正确，或使用 --include-deps 包含更多目录'
        );
        this.ui.showContextualHelp('no-files');
      }
      throw new Error('未找到需要分析的 TypeScript/JavaScript 文件');
    }

    if (!options.quiet) {
      this.ui.succeedSpinner(`发现 ${files.length} 个文件需要分析`);
    }

    // 映射配置到分析选项
    // 优先级：CLI参数 > 配置文件 > 默认值
    const analysisOptions = {
      enableMixedLogicOperatorPenalty: this.config?.rules?.enableMixedLogicOperatorPenalty ?? false,
      recursionChainThreshold: this.config?.rules?.recursionChainThreshold ?? 10,
      enableDetails: options.details !== undefined ? Boolean(options.details) : Boolean(this.config?.enableDetails ?? false), // 支持配置文件中的详细模式设置
      quiet: options.quiet, // 传递静默模式标志
    };

    // 分析所有文件
    const fileResults: FileResult[] = [];
    let totalComplexity = 0;
    let totalFunctions = 0;
    let failedFiles = 0;
    let timeoutFiles = 0;

    // 显示进度条（如果文件数量较多且非静默模式）
    const showProgress = !options.quiet && files.length > 3;
    // 显示文件进度（详细模式或文件数量适中时，但不与进度条冲突）
    const showFileProgress = !options.quiet && !showProgress && 
      (options.details || (files.length <= 10 && files.length > 1));
    
    if (showProgress) {
      this.ui.createProgressBar(files.length, '分析进度');
    }

    for (let i = 0; i < files.length; i++) {
      const filePath = files[i]!; // 使用非空断言，因为i < files.length

      // 显示当前处理文件（仅在非进度条模式时）
      if (showFileProgress) {
        this.ui.showFileProgress(i + 1, files.length, filePath);
      }

      try {
        // 为每个文件添加超时保护
        const timeoutMs = this.config?.fileProcessingTimeout ?? 30000; // 30秒默认超时
        const timeoutPromise = new Promise<FileResult>((_, reject) => {
          setTimeout(() => reject(new Error('File processing timeout')), timeoutMs);
        });

        const analyzePromise = analyzeFile(filePath, analysisOptions);
        const fileResult = await Promise.race([analyzePromise, timeoutPromise]);

        fileResults.push(fileResult);
        totalComplexity += fileResult.complexity;
        totalFunctions += fileResult.functions.length;
      } catch (error) {
        if (error instanceof Error && error.message === 'File processing timeout') {
          timeoutFiles++;
          if (!options.quiet) {
            this.ui.warning(`${filePath}: 处理超时，跳过该文件`);
          }
        } else {
          failedFiles++;
          if (!options.quiet) {
            this.ui.warning(`分析失败 ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
      }

      // 更新进度条
      if (showProgress) {
        this.ui.updateProgress(i + 1, filePath);
      }
    }

    // 停止进度条
    if (showProgress) {
      this.ui.stopProgress();
    }

    // 显示错误统计
    if (!options.quiet) {
      this.ui.showErrorStats(fileResults.length, failedFiles, timeoutFiles);
      
      // 显示上下文帮助
      if (timeoutFiles > 0) {
        this.ui.showContextualHelp('timeout');
      }
    }

    const averageComplexity = totalFunctions > 0 ? totalComplexity / totalFunctions : 0;
    const highComplexityFunctions = fileResults
      .flatMap((file) => file.functions)
      .filter((fn) => fn.complexity > (this.config?.failOnComplexity ?? 15)).length;

    // 检查基线
    const baseline = await this.baselineManager.loadBaseline();
    const result: AnalysisResult = {
      summary: {
        totalComplexity,
        averageComplexity,
        filesAnalyzed: fileResults.length,
        functionsAnalyzed: totalFunctions,
        highComplexityFunctions,
      },
      results: fileResults,
      baseline: baseline || undefined,
    };

    // 如果有基线，进行比较
    if (baseline) {
      return this.baselineManager.compareWithBaseline(result, baseline);
    }

    return result;
  }

  private async discoverFiles(paths: string[], options: CLIOptions): Promise<string[]> {
    const allFiles: string[] = [];

    for (const path of paths) {
      try {
        // 1. 检查是否为glob模式
        if (this.isGlobPattern(path)) {
          const files = await fg(path, { ignore: await this.getIgnorePatterns(options, path) });
          allFiles.push(...files);
          continue;
        }

        // 2. 检查文件系统状态
        try {
          // 特殊处理当前目录
          if (path === '.') {
            const files = await fg(`${path}/**/*.{ts,tsx,js,jsx}`, {
              ignore: await this.getIgnorePatterns(options, path),
            });
            allFiles.push(...files);
            continue;
          }

          // 使用Node.js fs API检查文件状态
          const stats = await fs.stat(path);

          if (stats.isDirectory()) {
            // 目录：添加递归模式
            const files = await fg(`${path}/**/*.{ts,tsx,js,jsx}`, {
              ignore: await this.getIgnorePatterns(options, path),
            });
            allFiles.push(...files);
          } else if (stats.isFile()) {
            // 文件：直接添加
            allFiles.push(path);
          }
        } catch {
          // 无法stat时，尝试作为glob模式处理
          const files = await fg(path, { ignore: await this.getIgnorePatterns(options, path) });
          allFiles.push(...files);
        }
      } catch (error) {
        console.warn(
          `Warning: Could not process path ${path}: ${error instanceof Error ? error.message : String(error)}`
        );
      }
    }

    // 去重并排序
    return [...new Set(allFiles)].sort();
  }

  /**
   * 检查路径是否包含glob模式
   */
  private isGlobPattern(path: string): boolean {
    return /[*?[\]{}]/.test(path);
  }

  /**
   * 检测项目类型
   */
  private async detectProjectType(basePath: string): Promise<ProjectDetectionResult> {
    const result: ProjectDetectionResult = {
      type: 'unknown',
      configFiles: [],
      excludePatterns: [],
    };

    try {
      // 检测配置文件存在性
      const configFiles = [
        'package.json',
        'requirements.txt',
        'pyproject.toml',
        'pom.xml',
        'build.gradle',
        'Cargo.toml',
        'go.mod',
      ];

      for (const configFile of configFiles) {
        try {
          const configPath = join(basePath, configFile);
          await fs.access(configPath);
          result.configFiles.push(configFile);
        } catch {
          // 文件不存在，继续检查下一个
        }
      }

      // 根据检测到的配置文件确定项目类型和排除模式
      if (result.configFiles.includes('package.json')) {
        result.type = 'nodejs';
        result.excludePatterns = ['**/node_modules/**', '**/.next/**', '**/dist/**', '**/build/**', '**/coverage/**'];
      } else if (result.configFiles.includes('requirements.txt') || result.configFiles.includes('pyproject.toml')) {
        result.type = 'python';
        result.excludePatterns = [
          '**/venv/**',
          '**/__pycache__/**',
          '**/.venv/**',
          '**/site-packages/**',
          '**/.pytest_cache/**',
        ];
      } else if (result.configFiles.includes('pom.xml') || result.configFiles.includes('build.gradle')) {
        result.type = 'java';
        result.excludePatterns = ['**/target/**', '**/build/**', '**/.gradle/**', '**/.m2/**'];
      } else if (result.configFiles.includes('Cargo.toml')) {
        result.type = 'rust';
        result.excludePatterns = ['**/target/**'];
      } else if (result.configFiles.includes('go.mod')) {
        result.type = 'go';
        result.excludePatterns = ['**/vendor/**'];
      }
    } catch {
      // 项目检测失败，保持unknown类型
    }

    return result;
  }

  /**
   * 获取忽略模式列表 - 支持智能排除和优先级
   */
  private async getIgnorePatterns(options: CLIOptions, basePath: string): Promise<string[]> {
    const patterns: string[] = [];

    // 1. 基本排除模式（总是生效）
    const basePatterns = ['**/*.d.ts'];
    patterns.push(...basePatterns);

    // 2. 智能项目类型检测排除（除非被禁用）
    if (!this.config?.disableSmartExclusion && !options.includeDeps) {
      const projectDetection = await this.detectProjectType(basePath);

      if (options.details && !options.quiet) {
        console.log(`检测到项目类型: ${projectDetection.type}`);
        if (projectDetection.excludePatterns.length > 0) {
          console.log(`应用智能排除规则: ${projectDetection.excludePatterns.join(', ')}`);
        }
      }

      patterns.push(...projectDetection.excludePatterns);
    }

    // 3. 配置文件排除规则（如果启用默认规则）
    const useDefaults = options.excludeDefaults !== false && this.config?.excludeDefaults !== false;
    if (useDefaults && this.config?.exclude) {
      patterns.push(...this.config.exclude);
    }

    // 4. CLI 排除参数（优先级最高）
    if (options.exclude) {
      patterns.push(...options.exclude);
    }

    // 5. CLI excludePattern 参数
    if (options.excludePattern) {
      patterns.push(...options.excludePattern);
    }

    // 6. 应用包含覆盖 - 从排除列表中移除匹配 includeOverrides 的模式
    if (this.config?.includeOverrides) {
      // 这里需要更复杂的逻辑来处理包含覆盖
      // 暂时跳过，因为 fast-glob 的 ignore 选项不支持"反向"模式
    }

    // 去重并排序
    const uniquePatterns = [...new Set(patterns)];

    if (options.showExcluded && options.details && !options.quiet) {
      console.log('应用的排除模式:');
      uniquePatterns.forEach((pattern) => console.log(`  - ${pattern}`));
    }

    return uniquePatterns;
  }

  private applyFilters(result: AnalysisResult, options: CLIOptions): AnalysisResult {
    let filteredResults = [...result.results];

    // 应用最小复杂度过滤器
    if (options.min !== undefined) {
      filteredResults = filteredResults
        .map((fileResult) => ({
          ...fileResult,
          functions: fileResult.functions.filter((fn) => fn.complexity >= options.min!),
        }))
        .filter((fileResult) => fileResult.functions.length > 0);
    }

    // 应用排序
    if (options.sort === 'complexity') {
      filteredResults.forEach((fileResult) => {
        fileResult.functions.sort((a, b) => b.complexity - a.complexity);
      });
      filteredResults.sort((a, b) => b.complexity - a.complexity);
    } else if (options.sort === 'name') {
      filteredResults.forEach((fileResult) => {
        fileResult.functions.sort((a, b) => a.name.localeCompare(b.name));
      });
      filteredResults.sort((a, b) => a.filePath.localeCompare(b.filePath));
    }

    return {
      ...result,
      results: filteredResults,
    };
  }

  private async outputResults(result: AnalysisResult, options: CLIOptions): Promise<void> {
    // 显示控制台输出
    let formatter;

    if (options.format === 'json') {
      formatter = new JsonFormatter(this.config!);
    } else if (options.format === 'html') {
      formatter = new HtmlFormatter(this.config!);
    } else {
      formatter = new TextFormatter(this.config!);
    }

    const output = await formatter.format(result, options.details, options);
    console.log(output);

    // 处理文件输出 - 优先级：CLI选项 > 配置文件
    const outputPaths: string[] = [];

    // 1. 如果指定了--output-dir，写入文件
    if (options.outputDir) {
      let extension = 'txt';
      if (options.format === 'json') extension = 'json';
      else if (options.format === 'html') extension = 'html';

      const outputPath = `${options.outputDir}/complexity-report.${extension}`;
      outputPaths.push(outputPath);

      // 确保输出目录存在
      await this.ensureDirectoryExists(options.outputDir);
    }

    // 2. 如果配置文件中有report设置，也写入相应文件
    if (this.config?.report) {
      if (this.config.report.json) {
        outputPaths.push(this.config.report.json);
      }
      if (this.config.report.html) {
        outputPaths.push(this.config.report.html);
      }
    }

    // 写入所有指定的输出文件
    for (const outputPath of outputPaths) {
      try {
        // 确保输出目录存在
        const outputDir = outputPath.substring(0, outputPath.lastIndexOf('/'));
        if (outputDir) {
          await this.ensureDirectoryExists(outputDir);
        }

        // 根据文件扩展名选择格式化器
        let outputFormatter;
        if (outputPath.endsWith('.json')) {
          outputFormatter = new JsonFormatter(this.config!);
        } else if (outputPath.endsWith('.html')) {
          outputFormatter = new HtmlFormatter(this.config!);
        } else {
          outputFormatter = new TextFormatter(this.config!);
        }

        await outputFormatter.writeToFile?.(result, outputPath);
        if (!options.quiet) {
          this.ui.success(`报告已保存到: ${outputPath}`);
        }
      } catch (error) {
        if (!options.quiet) {
          this.ui.warning(
            `无法写入报告到 ${outputPath}: ${error instanceof Error ? error.message : String(error)}`
          );
        }
      }
    }
  }

  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      // 使用Node.js fs API创建目录
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      throw new Error(
        `无法创建输出目录 ${dirPath}: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private applyQualityGate(result: AnalysisResult, threshold: number, options: CLIOptions): boolean {
    const failingFunctions = result.results.flatMap((file) => file.functions).filter((fn) => fn.complexity > threshold);

    if (failingFunctions.length > 0) {
      // JSON 模式下，将质量门禁信息添加到结果中，而不是直接输出
      if (options.format === 'json') {
        // 在 JSON 模式下，将质量门禁失败信息添加到一个特殊属性中
        // 这样 JSON 格式化器可以读取并包含在 metadata 中
        (result as any).qualityGateInfo = {
          passed: false,
          threshold,
          failingFunctions: failingFunctions.length,
          failingItems: failingFunctions.map(fn => ({
            name: fn.name,
            filePath: fn.filePath,
            line: fn.line,
            complexity: fn.complexity
          }))
        };
      } else {
        // 非 JSON 模式下，显示质量门禁失败信息
        if (!options.quiet) {
          this.ui.showQualityGate(false, threshold, failingFunctions.length);
          
          // 显示失败的函数详情
          console.log(''); // 空行
          failingFunctions.forEach((fn) => {
            const complexityStr = this.ui.formatComplexity(fn.complexity, threshold);
            this.ui.error(`  • ${fn.name} 在 ${fn.filePath}:${fn.line} (复杂度: ${complexityStr})`);
          });
        }
      }

      return false;
    }

    // 质量门禁通过
    if (options.format === 'json') {
      // JSON 模式下，添加成功的质量门禁信息
      (result as any).qualityGateInfo = {
        passed: true,
        threshold,
        failingFunctions: 0
      };
    } else {
      // 非 JSON 模式下，显示成功信息
      if (!options.quiet) {
        this.ui.showQualityGate(true, threshold, 0);
      }
    }
    
    return true;
  }

  private async handleCreateBaseline(paths: string[], options: CLIOptions): Promise<void> {
    if (!options.quiet) {
      this.ui.info('创建基线文件...');
    }
    const result = await this.analyzeFiles(paths, { ...options, paths: [], exclude: [] });
    const threshold = this.config?.failOnComplexity;
    await this.baselineManager.createBaseline(result, threshold);
    if (!options.quiet) {
      this.ui.success('基线文件创建完成');
    }
  }

  private async handleUpdateBaseline(paths: string[], options: CLIOptions): Promise<void> {
    if (!options.quiet) {
      this.ui.info('更新基线文件...');
    }
    const result = await this.analyzeFiles(paths, { ...options, paths: [], exclude: [] });
    const threshold = this.config?.failOnComplexity;
    await this.baselineManager.updateBaseline(result, threshold);
    if (!options.quiet) {
      this.ui.success('基线文件更新完成');
    }
  }

  private async handleUIMode(options: CLIOptions): Promise<void> {
    console.log('🚀 Starting Web UI mode...');

    // 合并配置文件和默认值
    const uiConfig = {
      port: this.config?.ui?.port ?? 3000,
      host: this.config?.ui?.host ?? 'localhost',
      openBrowser: options.open ?? this.config?.ui?.openBrowser ?? false,
      autoShutdown: this.config?.ui?.autoShutdown ?? false,
    };

    // 创建UI服务器
    const uiServer = new UIServer(this.config!, uiConfig);

    // 启动服务器
    const { url } = await uiServer.start();

    // 执行分析
    console.log('\n📊 Analyzing files...');
    const result = await this.analyzeFiles(options.paths, options);

    // 应用过滤器
    const filteredResult = this.applyFilters(result, options);

    // 存储结果供Web UI使用
    await uiServer.storeResult(filteredResult);

    console.log(`\n✅ Analysis complete! Results available at: ${url}/report`);

    // 如果配置了自动关闭，则关闭服务器
    if (uiConfig.autoShutdown) {
      console.log('\n🕒 Auto-shutdown enabled, server will close in 5 seconds...');
      setTimeout(async () => {
        await uiServer.cleanupResult();
        await uiServer.stop();
        process.exit(0);
      }, 5000);
      return;
    }

    // 设置优雅关闭
    const gracefulShutdown = async () => {
      console.log('\n\n👋 Shutting down Web UI...');
      await uiServer.cleanupResult();
      await uiServer.stop();
      process.exit(0);
    };

    // 监听退出信号
    process.on('SIGINT', gracefulShutdown);
    process.on('SIGTERM', gracefulShutdown);

    // 保持进程运行
    console.log('\n💡 Press Ctrl+C to stop the server');

    // 阻止进程退出
    await new Promise(() => {});
  }
}
