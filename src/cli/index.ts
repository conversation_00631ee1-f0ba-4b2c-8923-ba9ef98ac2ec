#!/usr/bin/env node

import { Command } from 'commander';
import { CommandProcessor } from './commands';
import { EnhancedCommandProcessor } from './debug-commands';
import { CLIUIHelper } from './ui-helper';
import { validateMinFileComplexity } from '../utils/concurrent-validation-service';

const program = new Command();
const ui = new CLIUIHelper();

program
  .name('cognitive-complexity')
  .description('🧠 智能分析 TypeScript/JavaScript 代码的认知复杂度')
  .version('1.0.0')
  .argument('[paths...]', '待分析的文件或目录路径 (默认: 当前目录)', ['.'])
  .option('--fail-on <threshold>', '复杂度超过阈值时退出并返回错误码 (默认: 15)')
  .option('--create-baseline', '为当前项目创建复杂度基线文件')
  .option('--update-baseline', '更新现有的复杂度基线文件')
  .option('-c, --config <path>', '指定配置文件路径')
  .option('-o, --output-dir <path>', '报告输出目录')
  .option('-d, --details', '显示详细的复杂度分析报告')
  .option('--show-context', '在详细模式下显示代码上下文信息')
  .option('--show-all-context', '显示所有步骤的代码上下文（包括低复杂度）')
  .option('-f, --format <format>', '输出格式: text(文本) | json | html (默认: text)', 'text')
  .option('-m, --min <threshold>', '仅显示复杂度高于指定阈值的结果')
  .option('--min-file-complexity <threshold>', '仅显示文件总复杂度高于指定阈值的文件。设为0显示所有文件，默认为1自动隐藏零复杂度文件 (默认: 1)', validateMinFileComplexity, 1)
  .option('--min-function-complexity <threshold>', '仅显示函数复杂度高于指定阈值的函数。设为0显示所有函数，默认为1自动隐藏零复杂度函数 (默认: 1)', (value: string) => {
    const num = parseInt(value, 10);
    if (isNaN(num) || num < 0) {
      throw new Error('--min-function-complexity 必须为非负整数');
    }
    return num;
  }, 1)
  .option('--show-zero-complexity', '显示复杂度为0的函数（覆盖 --min-function-complexity）')
  .option('-s, --sort <key>', '结果排序方式: complexity(复杂度) | name(名称) (默认: complexity)', 'complexity')
  .option('--exclude <pattern>', '排除匹配指定模式的文件/目录 (可多次使用)', (value: string, previous: string[]) => {
    return previous ? previous.concat([value]) : [value];
  }, [] as string[])
  .option('--exclude-pattern <pattern>', '排除匹配指定模式的文件/目录 (可多次使用)', (value: string, previous: string[]) => {
    return previous ? previous.concat([value]) : [value];
  }, [] as string[])
  .option('--include-deps', '包含依赖目录 (如 node_modules 等)')
  .option('--exclude-defaults <value>', '使用默认排除规则 (true/false)', 'true')
  .option('--show-excluded', '显示被排除的路径和模式')
  .option('--ui', '启动 Web UI 进行可视化分析')
  .option('--open', '启动 UI 时自动打开浏览器 (需与 --ui 配合使用)')
  .option('--quiet', '静默模式，减少输出信息')
  .option('--no-colors', '禁用彩色输出')
  
  // 智能上下文过滤选项
  .option('--max-context-items <number>', '在详细模式下显示的最大上下文项目数 (默认: 10)', (value) => parseInt(value, 10))
  .option('--min-complexity-increment <number>', '显示上下文的最小复杂度增量 (默认: 1)', (value) => parseInt(value, 10))
  .option('--context-lines <number>', '上下文显示的行数 (默认: 2)', (value) => {
    // 检查是否包含小数点
    if (value.includes('.')) {
      throw new Error('--context-lines 必须为非负整数');
    }
    const num = parseInt(value, 10);
    if (isNaN(num) || num < 0) {
      throw new Error('--context-lines 必须为非负整数');
    }
    if (num > 20) {
      console.warn('⚠️  警告: 过大的上下文行数可能影响性能和可读性');
    }
    return num;
  }, 2)
  
  // 调试和诊断选项
  .option('--debug', '启用调试模式，提供详细的执行追踪')
  .option('--debug-level <level>', '调试级别: trace | debug | info | warn | error (默认: info)', 'info')
  .option('--debug-output <path>', '调试数据输出文件路径')
  .option('--visual-report', '生成可视化调试报告 (HTML格式)')
  
  // 断点和步进调试
  .option('--enable-breakpoints', '启用断点调试功能')
  .option('--break-on-rule <rules...>', '在指定规则执行时设置断点')
  .option('--break-on-complexity <threshold>', '在复杂度达到阈值时设置断点')
  .option('--step-by-step', '启用步进调试模式')
  
  // 性能分析和追踪
  .option('--enable-profiling', '启用性能分析')
  .option('--enable-tracing', '启用执行追踪')
  .option('--enable-diagnostics', '启用问题诊断')
  .option('--show-performance-metrics', '显示详细的性能指标')
  .option('--realtime-monitoring', '启用实时监控')
  .addHelpText('after', `
示例:
  $ cognitive-complexity                    # 分析当前目录
  $ cognitive-complexity src/              # 分析 src 目录
  $ cognitive-complexity --details         # 显示详细报告
  $ cognitive-complexity --fail-on 10      # 设置复杂度阈值为 10
  $ cognitive-complexity --format json     # 输出 JSON 格式
  $ cognitive-complexity --ui              # 启动 Web UI
  $ cognitive-complexity --ui --open      # 启动 Web UI 并自动打开浏览器
  
详细模式示例:
  $ cognitive-complexity --details --show-context           # 显示代码上下文
  $ cognitive-complexity --details --show-all-context       # 显示所有上下文
  $ cognitive-complexity --details --show-context --context-lines 5  # 显示5行上下文
  $ cognitive-complexity --details --max-context-items 5    # 最多显示5个上下文
  $ cognitive-complexity --details --min-complexity-increment 2  # 仅显示增量≥2的上下文
  
文件级过滤示例:  
  $ cognitive-complexity --min-file-complexity 0    # 显示所有文件（包括复杂度为0的文件）
  $ cognitive-complexity --min-file-complexity 5    # 仅显示文件总复杂度≥5的文件
  $ cognitive-complexity --min-file-complexity 1    # 默认值，隐藏零复杂度文件  
  $ cognitive-complexity --min 3 --min-file-complexity 10  # 函数级过滤+文件级过滤
  
调试示例:
  $ cognitive-complexity --debug           # 启用调试模式
  $ cognitive-complexity --debug --debug-level trace    # 详细追踪
  $ cognitive-complexity --debug --visual-report        # 生成可视化报告
  $ cognitive-complexity --debug --break-on-complexity 15  # 复杂度断点
  $ cognitive-complexity --debug --enable-profiling        # 性能分析

更多信息: https://github.com/your-repo/cognitive-complexity
`)
  .action(async (paths, options) => {
    try {
      // JSON输出模式应该静默运行，避免非JSON内容混入输出
      if (options.format === 'json') {
        options.quiet = true;
      }
      
      // 禁用颜色输出时的处理
      if (options.noColors) {
        // 这里可以设置chalk的全局配置禁用颜色
        process.env.FORCE_COLOR = '0';
      }

      // 显示欢迎信息（非静默模式且非JSON格式）
      if (!options.quiet && options.format !== 'json') {
        ui.showWelcome();
      }

      // 根据是否启用调试模式选择处理器
      let success = false;
      if (options.debug) {
        // 使用增强的调试处理器
        const enhancedProcessor = new EnhancedCommandProcessor();
        await enhancedProcessor.executeWithDebug({ paths, ...options });
        success = true; // 调试处理器目前没有返回值，假设成功
      } else {
        // 使用标准处理器
        const processor = new CommandProcessor();
        success = await processor.execute({ paths, ...options });
      }
      
      // 统一处理退出码
      process.exit(success ? 0 : 1);
    } catch (error) {
      ui.error(error instanceof Error ? error.message : String(error));
      if (!options.quiet) {
        ui.showTip('使用 --help 查看使用说明，或检查输入参数是否正确');
      }
      process.exit(1);
    }
  });

// 自定义帮助显示
program.configureHelp({
  helpWidth: 100,
  sortSubcommands: true,
});

// 处理未知命令
program.on('command:*', () => {
  ui.error(`未知命令: ${program.args.join(' ')}`);
  ui.showTip('使用 --help 查看可用命令');
  process.exit(1);
});

program.parse();