import chalk from 'chalk';
import ora from 'ora';
import * as cliProgress from 'cli-progress';

export class CL<PERSON>UIHelper {
  private spinner?: ReturnType<typeof ora>;
  private progressBar?: cliProgress.SingleBar;
  private messageBuffer: string[] = [];
  private isProgressBarActive = false;

  /**
   * 显示成功消息
   */
  success(message: string): void {
    console.log(chalk.green('✅ ' + message));
  }

  /**
   * 显示错误消息
   */
  error(message: string): void {
    console.error(chalk.red('❌ ' + message));
  }

  /**
   * 显示详细错误消息，包含建议的解决方案
   */
  errorWithSuggestion(message: string, suggestion?: string): void {
    console.error(chalk.red('❌ ' + message));
    if (suggestion) {
      console.error(chalk.gray('   💡 建议: ' + suggestion));
    }
  }

  /**
   * 显示警告消息
   */
  warning(message: string): void {
    if (this.isProgressBarActive) {
      this.bufferMessage(chalk.yellow('⚠️  ' + message));
    } else {
      console.warn(chalk.yellow('⚠️  ' + message));
    }
  }

  /**
   * 显示信息消息
   */
  info(message: string): void {
    console.log(chalk.blue('ℹ️  ' + message));
  }

  /**
   * 显示带标题的章节
   */
  section(title: string): void {
    console.log(chalk.bold.cyan(`\n🔍 ${title}`));
    console.log(chalk.gray('='.repeat(title.length + 3)));
  }

  /**
   * 启动加载旋转器
   */
  startSpinner(text: string): void {
    this.spinner = ora({
      text: chalk.blue(text),
      color: 'blue',
      spinner: 'dots'
    }).start();
  }

  /**
   * 更新旋转器文本
   */
  updateSpinner(text: string): void {
    if (this.spinner) {
      this.spinner.text = chalk.blue(text);
    }
  }

  /**
   * 成功停止旋转器
   */
  succeedSpinner(text?: string): void {
    if (this.spinner) {
      this.spinner.succeed(text ? chalk.green(text) : undefined);
      this.spinner = undefined;
    }
  }

  /**
   * 失败停止旋转器
   */
  failSpinner(text?: string): void {
    if (this.spinner) {
      this.spinner.fail(text ? chalk.red(text) : undefined);
      this.spinner = undefined;
    }
  }

  /**
   * 停止旋转器
   */
  stopSpinner(): void {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = undefined;
    }
  }

  /**
   * 创建进度条
   */
  createProgressBar(total: number, title: string): void {
    this.isProgressBarActive = true;
    this.messageBuffer = []; // 清空缓冲区
    this.progressBar = new cliProgress.SingleBar({
      format: chalk.cyan(title) + ' |' + chalk.green('{bar}') + '| {percentage}% | {value}/{total} | ETA: {eta}s | {currentFile}',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    this.progressBar.start(total, 0, {
      currentFile: '准备中...'
    });
  }

  /**
   * 更新进度条
   */
  updateProgress(current: number, fileName?: string): void {
    if (this.progressBar) {
      const payload: any = {};
      if (fileName) {
        // 截取文件名，避免进度条过长
        const shortFileName = fileName.length > 50 ? '...' + fileName.slice(-47) : fileName;
        payload.currentFile = chalk.gray(shortFileName);
      }
      this.progressBar.update(current, payload);
    }
  }

  /**
   * 停止进度条
   */
  stopProgress(): void {
    if (this.progressBar) {
      this.progressBar.stop();
      this.progressBar = undefined;
      this.isProgressBarActive = false;
      
      // 显示缓冲的消息
      this.flushMessages();
    }
  }

  /**
   * 格式化复杂度值（带颜色）
   */
  formatComplexity(complexity: number, threshold = 15): string {
    if (complexity > threshold * 1.5) {
      return chalk.red.bold(`${complexity}`);
    } else if (complexity > threshold) {
      return chalk.yellow(`${complexity}`);
    } else if (complexity > threshold * 0.7) {
      return chalk.blue(`${complexity}`);
    } else {
      return chalk.green(`${complexity}`);
    }
  }

  /**
   * 格式化严重级别
   */
  formatSeverity(severity: string): string {
    switch (severity) {
      case 'Critical':
        return chalk.red.bold('[严重]');
      case 'Warning':
        return chalk.yellow('[警告]');
      case 'Info':
        return chalk.blue('[信息]');
      default:
        return chalk.gray('[一般]');
    }
  }

  /**
   * 显示帮助提示
   */
  showTip(message: string): void {
    console.log(chalk.gray('💡 提示: ' + message));
  }

  /**
   * 显示分隔线
   */
  separator(): void {
    console.log(chalk.gray('─'.repeat(60)));
  }

  /**
   * 显示质量门禁结果
   */
  showQualityGate(passed: boolean, threshold: number, failingCount: number): void {
    if (passed) {
      this.success(`✅ 质量门禁通过！所有函数复杂度都在 ${threshold} 的阈值内`);
    } else {
      this.section('❌ 质量门禁失败');
      this.error(`发现 ${failingCount} 个函数超过复杂度阈值 ${threshold}`);
      
      // 显示更详细的修复建议
      console.log(chalk.cyan('\n💡 解决方案:'));
      console.log(chalk.gray('   1. 重构高复杂度函数，拆分为更小的函数'));
      console.log(chalk.gray('   2. 使用 --details 查看具体问题函数'));
      console.log(chalk.gray(`   3. 临时调整阈值: --fail-on ${threshold + 5}`));
      console.log(chalk.gray('   4. 在 CI/CD 中使用适当的阈值确保代码质量'));
    }
  }

  /**
   * 显示文件分析进度
   */
  showFileProgress(current: number, total: number, fileName: string): void {
    const percentage = Math.round((current / total) * 100);
    const progressStr = chalk.cyan(`[${current}/${total} ${percentage}%]`);
    console.log(`${progressStr} ${chalk.gray('分析:')} ${fileName}`);
  }

  /**
   * 显示汇总统计
   */
  showSummary(stats: {
    filesAnalyzed: number;
    functionsAnalyzed: number;
    averageComplexity: number;
    totalComplexity: number;
    highComplexityFunctions: number;
    processingTime?: number;
  }): void {
    this.section('分析汇总');
    
    console.log(chalk.cyan('📁 文件数量: ') + chalk.white(stats.filesAnalyzed.toString()));
    console.log(chalk.cyan('🔍 函数数量: ') + chalk.white(stats.functionsAnalyzed.toString()));
    console.log(chalk.cyan('📊 平均复杂度: ') + this.formatComplexity(stats.averageComplexity));
    console.log(chalk.cyan('📈 总复杂度: ') + chalk.white(stats.totalComplexity.toString()));
    
    if (stats.highComplexityFunctions > 0) {
      console.log(chalk.cyan('⚠️  高复杂度函数: ') + chalk.yellow(stats.highComplexityFunctions.toString()));
    } else {
      console.log(chalk.cyan('✅ 高复杂度函数: ') + chalk.green('0'));
    }

    if (stats.processingTime) {
      console.log(chalk.cyan('⏱️  处理时间: ') + chalk.gray(`${stats.processingTime.toFixed(2)}s`));
    }
  }

  /**
   * 显示错误统计
   */
  showErrorStats(successful: number, failed: number, timeout: number): void {
    if (failed > 0 || timeout > 0) {
      this.separator();
      console.log(chalk.cyan('📋 处理统计:'));
      console.log(`  ${chalk.green('✅ 成功:')} ${successful}`);
      if (failed > 0) {
        console.log(`  ${chalk.red('❌ 失败:')} ${failed}`);
      }
      if (timeout > 0) {
        console.log(`  ${chalk.yellow('⏰ 超时:')} ${timeout}`);
      }
    }
  }

  /**
   * 清屏
   */
  clear(): void {
    console.clear();
  }

  /**
   * 显示欢迎信息
   */
  showWelcome(): void {
    console.log(chalk.cyan.bold('🧠 认知复杂度分析工具'));
    console.log(chalk.gray('='.repeat(25)));
  }

  /**
   * 显示交互式配置向导
   */
  async showConfigWizard(): Promise<{
    format: string;
    threshold: number;
    includeDetails: boolean;
    outputPath?: string;
  }> {
    console.log(chalk.cyan.bold('\n🔧 配置向导'));
    console.log(chalk.gray('请回答以下问题来配置分析选项：\n'));

    // 这是一个简化的配置向导示例
    // 在真实实现中，可以使用 inquirer 等库来提供更好的交互体验
    console.log(chalk.yellow('提示：这是一个简化的配置向导示例'));
    console.log(chalk.gray('实际实现建议使用 inquirer 库来提供更好的交互体验\n'));

    return {
      format: 'text',
      threshold: 15,
      includeDetails: false
    };
  }

  /**
   * 显示性能优化建议
   */
  showPerformanceTips(stats: {
    filesAnalyzed: number;
    processingTime?: number;
    highComplexityFunctions: number;
  }): void {
    if (!stats.processingTime) return;

    console.log(chalk.cyan('\n💡 性能优化建议:'));
    
    if (stats.processingTime > 10) {
      console.log(chalk.yellow('  • 分析时间较长，考虑使用 --exclude 排除不必要的文件'));
    }
    
    if (stats.highComplexityFunctions > stats.filesAnalyzed * 0.3) {
      console.log(chalk.yellow('  • 高复杂度函数较多，建议重构复杂的函数'));
    }
    
    if (stats.filesAnalyzed > 1000) {
      console.log(chalk.blue('  • 大型项目，建议设置基线文件追踪复杂度变化'));
    }
  }

  /**
   * 缓冲消息（在进度条活动时使用）
   */
  private bufferMessage(message: string): void {
    this.messageBuffer.push(message);
  }

  /**
   * 刷新缓冲的消息
   */
  private flushMessages(): void {
    if (this.messageBuffer.length > 0) {
      console.log(''); // 空行分隔
      this.messageBuffer.forEach(message => {
        console.log(message);
      });
      this.messageBuffer = [];
    }
  }

  /**
   * 显示上下文相关的帮助信息
   */
  showContextualHelp(context: 'no-files' | 'high-complexity' | 'timeout' | 'config-error'): void {
    console.log(chalk.cyan('\n🆘 相关帮助:'));
    
    switch (context) {
      case 'no-files':
        console.log(chalk.gray('  • 检查文件路径是否正确'));
        console.log(chalk.gray('  • 使用 --include-deps 包含依赖目录'));
        console.log(chalk.gray('  • 检查 --exclude 模式是否过于宽泛'));
        break;
      case 'high-complexity':
        console.log(chalk.gray('  • 使用 --details 查看函数详细信息'));
        console.log(chalk.gray('  • 考虑重构复杂度超过15的函数'));
        console.log(chalk.gray('  • 使用 --fail-on 调整质量门禁阈值'));
        break;
      case 'timeout':
        console.log(chalk.gray('  • 某些文件处理超时，可能包含非常复杂的代码'));
        console.log(chalk.gray('  • 考虑使用 --exclude 排除有问题的文件'));
        console.log(chalk.gray('  • 检查是否有语法错误导致解析失败'));
        break;
      case 'config-error':
        console.log(chalk.gray('  • 检查配置文件语法是否正确'));
        console.log(chalk.gray('  • 使用 cognitive-complexity --help 查看选项'));
        console.log(chalk.gray('  • 尝试删除配置文件使用默认设置'));
        break;
    }
  }
}