/**
 * 调试和诊断工具系统
 * 提供详细的规则执行追踪、可视化分析过程展示、逐步调试和断点分析
 * 建立问题诊断和解决方案推荐机制
 */

import type { Node, Module } from '@swc/core';
import type { 
  Rule, 
  RuleResult, 
  AnalysisContext, 
  NodeAnalysis, 
  FunctionAnalysis, 
  FileAnalysis 
} from './types';

// 调试事件类型
export type DebugEventType = 
  | 'analysis_start'
  | 'analysis_end'
  | 'rule_start'
  | 'rule_end'
  | 'node_start'
  | 'node_end'
  | 'function_start'
  | 'function_end'
  | 'file_start'
  | 'file_end'
  | 'cache_hit'
  | 'cache_miss'
  | 'error'
  | 'warning'
  | 'breakpoint_hit'
  | 'step_complete'
  | 'hotspot_detected';

// 调试级别
export type DebugLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error';

// 调试事件
export interface DebugEvent {
  id: string;
  type: DebugEventType;
  level: DebugLevel;
  timestamp: number;
  sessionId: string;
  
  // 上下文信息
  context: {
    filePath?: string;
    functionName?: string;
    nodeType?: string;
    ruleId?: string;
    line?: number;
    column?: number;
  };
  
  // 事件数据
  data: any;
  
  // 性能信息
  performance?: {
    startTime: number;
    endTime?: number;
    duration?: number;
    memoryUsage?: NodeJS.MemoryUsage;
  };
  
  // 调用堆栈
  stackTrace?: string[];
  
  // 相关事件ID
  parentEventId?: string;
  childEventIds?: string[];
}

// 断点配置
export interface Breakpoint {
  id: string;
  enabled: boolean;
  
  // 断点类型
  type: 'rule' | 'node' | 'function' | 'file' | 'complexity' | 'condition';
  
  // 匹配条件
  conditions: {
    ruleId?: string;
    nodeType?: string;
    functionName?: string;
    filePath?: string;
    complexity?: {
      operator: '>' | '<' | '=' | '>=' | '<=';
      value: number;
    };
    customCondition?: (context: any) => boolean;
  };
  
  // 断点动作
  actions: {
    pauseExecution: boolean;
    logDetails: boolean;
    captureSnapshot: boolean;
    executeCallback?: (event: DebugEvent) => void;
  };
  
  // 统计信息
  hitCount: number;
  lastHit?: number;
}

// 执行快照
export interface ExecutionSnapshot {
  id: string;
  timestamp: number;
  
  // 执行状态
  state: {
    currentRule?: Rule;
    currentNode?: Node;
    currentFunction?: string;
    currentFilePath?: string;
    nestingLevel: number;
    complexity: number;
  };
  
  // 变量状态
  variables: {
    context: AnalysisContext;
    ruleResults: RuleResult[];
    nodeAnalysis?: NodeAnalysis;
  };
  
  // 调用栈
  callStack: {
    type: 'rule' | 'function' | 'file';
    name: string;
    startTime: number;
    complexity: number;
  }[];
  
  // 缓存状态
  cacheState: {
    hitRate: number;
    totalHits: number;
    totalMisses: number;
    recentAccesses: Array<{
      key: string;
      hit: boolean;
      timestamp: number;
    }>;
  };
}

// 问题诊断结果
export interface DiagnosticResult {
  id: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  category: 'performance' | 'correctness' | 'configuration' | 'usage';
  
  // 问题描述
  title: string;
  description: string;
  location?: {
    filePath?: string;
    functionName?: string;
    line?: number;
    column?: number;
  };
  
  // 根本原因分析
  rootCause: {
    type: 'code_issue' | 'config_issue' | 'rule_issue' | 'data_issue';
    description: string;
    evidence: string[];
  };
  
  // 影响评估
  impact: {
    performance?: number; // 0-100
    accuracy?: number;    // 0-100
    usability?: number;   // 0-100
    description: string;
  };
  
  // 解决方案
  solutions: Array<{
    type: 'quick_fix' | 'refactor' | 'configuration' | 'upgrade';
    title: string;
    description: string;
    effort: 'low' | 'medium' | 'high';
    risk: 'low' | 'medium' | 'high';
    steps: string[];
    expectedImprovement: string;
    autoFixAvailable?: boolean;
    autoFixCode?: string;
  }>;
  
  // 相关资源
  resources: Array<{
    type: 'documentation' | 'example' | 'tool' | 'article';
    title: string;
    url: string;
    description: string;
  }>;
}

// 可视化追踪数据
export interface VisualTrace {
  sessionId: string;
  startTime: number;
  endTime?: number;
  
  // 文件级别追踪
  files: Array<{
    path: string;
    startTime: number;
    endTime: number;
    complexity: number;
    functions: Array<{
      name: string;
      startTime: number;
      endTime: number;
      complexity: number;
      nodes: Array<{
        type: string;
        startTime: number;
        endTime: number;
        complexity: number;
        rules: Array<{
          id: string;
          startTime: number;
          endTime: number;
          complexity: number;
          cached: boolean;
          success: boolean;
        }>;
      }>;
    }>;
  }>;
  
  // 性能热点
  hotspots: Array<{
    type: 'rule' | 'function' | 'file';
    identifier: string;
    totalTime: number;
    frequency: number;
    averageTime: number;
  }>;
  
  // 执行流程图
  flowGraph: {
    nodes: Array<{
      id: string;
      type: 'file' | 'function' | 'node' | 'rule';
      label: string;
      complexity: number;
      duration: number;
      status: 'pending' | 'running' | 'completed' | 'error' | 'cached';
    }>;
    edges: Array<{
      from: string;
      to: string;
      type: 'calls' | 'contains' | 'depends';
      weight: number;
    }>;
  };
}

// 调试系统配置
export interface DebugSystemConfig {
  enabled: boolean;
  level: DebugLevel;
  
  // 追踪配置
  tracing: {
    enableRuleTracing: boolean;
    enableNodeTracing: boolean;
    enableFunctionTracing: boolean;
    enableFileTracing: boolean;
    enableCacheTracing: boolean;
    enablePerformanceTracing: boolean;
  };
  
  // 可视化配置
  visualization: {
    enableFlowGraph: boolean;
    enableHotspotAnalysis: boolean;
    enableTimelineView: boolean;
    maxTraceDepth: number;
    maxTraceNodes: number;
  };
  
  // 断点调试配置
  debugging: {
    enableBreakpoints: boolean;
    enableStepByStep: boolean;
    enableSnapshotCapture: boolean;
    maxSnapshots: number;
  };
  
  // 诊断配置
  diagnostics: {
    enableProblemDetection: boolean;
    enableSolutionRecommendation: boolean;
    enableAutoFix: boolean;
    severityThreshold: 'info' | 'warning' | 'error';
  };
  
  // 输出配置
  output: {
    logToConsole: boolean;
    logToFile: boolean;
    logFilePath?: string;
    generateReports: boolean;
    includeStackTraces: boolean;
  };
}

// 默认调试配置
const DEFAULT_DEBUG_CONFIG: DebugSystemConfig = {
  enabled: true,
  level: 'info',
  tracing: {
    enableRuleTracing: true,
    enableNodeTracing: true,
    enableFunctionTracing: true,
    enableFileTracing: true,
    enableCacheTracing: true,
    enablePerformanceTracing: true,
  },
  visualization: {
    enableFlowGraph: true,
    enableHotspotAnalysis: true,
    enableTimelineView: true,
    maxTraceDepth: 10,
    maxTraceNodes: 1000,
  },
  debugging: {
    enableBreakpoints: true,
    enableStepByStep: true,
    enableSnapshotCapture: true,
    maxSnapshots: 50,
  },
  diagnostics: {
    enableProblemDetection: true,
    enableSolutionRecommendation: true,
    enableAutoFix: false,
    severityThreshold: 'warning',
  },
  output: {
    logToConsole: true,
    logToFile: false,
    generateReports: true,
    includeStackTraces: true,
  },
};

/**
 * 高级调试和诊断系统
 * 提供全面的调试、追踪和问题诊断功能
 */
export class AdvancedDebugSystem {
  private config: DebugSystemConfig;
  private sessionId: string;
  private isEnabled: boolean;
  private isRunning: boolean = false;
  
  // 事件存储和管理
  private events: DebugEvent[] = [];
  private eventIdCounter: number = 0;
  private currentEventStack: string[] = [];
  
  // 断点管理
  private breakpoints = new Map<string, Breakpoint>();
  private activeBreakpoint?: string;
  private isPaused: boolean = false;
  
  // 快照管理
  private snapshots: ExecutionSnapshot[] = [];
  private currentSnapshot?: ExecutionSnapshot;
  
  // 可视化追踪
  private visualTrace: VisualTrace;
  
  // 诊断系统
  private diagnosticResults: DiagnosticResult[] = [];
  private problemDetectors: Array<(events: DebugEvent[]) => DiagnosticResult[]> = [];
  
  // 事件监听器
  private eventListeners = new Map<DebugEventType, Array<(event: DebugEvent) => void>>();
  
  // 步进调试状态
  private stepMode: 'none' | 'into' | 'over' | 'out' = 'none';
  private stepDepth: number = 0;

  constructor(config: Partial<DebugSystemConfig> = {}) {
    this.config = { ...DEFAULT_DEBUG_CONFIG, ...config };
    this.isEnabled = this.config.enabled;
    this.sessionId = this.generateSessionId();
    this.visualTrace = this.createEmptyVisualTrace();
    
    this.initializeProblemDetectors();
    
    if (this.isEnabled) {
      this.logInfo('Debug system initialized', { sessionId: this.sessionId });
    }
  }

  // ============ 公共API ============

  /**
   * 开始调试会话
   */
  startSession(): void {
    if (!this.isEnabled) return;
    
    this.sessionId = this.generateSessionId();
    this.isRunning = true;
    this.events = [];
    this.snapshots = [];
    this.diagnosticResults = [];
    this.visualTrace = this.createEmptyVisualTrace();
    this.currentEventStack = [];
    
    this.emitEvent({
      type: 'analysis_start',
      level: 'info',
      context: {},
      data: { sessionId: this.sessionId },
    });
    
    this.logInfo('Debug session started', { sessionId: this.sessionId });
  }

  /**
   * 结束调试会话
   */
  endSession(): VisualTrace {
    if (!this.isEnabled || !this.isRunning) return this.visualTrace;
    
    this.visualTrace.endTime = performance.now();
    this.isRunning = false;
    
    this.emitEvent({
      type: 'analysis_end',
      level: 'info',
      context: {},
      data: { 
        sessionId: this.sessionId,
        totalEvents: this.events.length,
        duration: this.visualTrace.endTime - this.visualTrace.startTime,
      },
    });
    
    // 执行问题诊断
    this.runDiagnostics();
    
    // 生成报告
    if (this.config.output.generateReports) {
      this.generateDebugReport();
    }
    
    this.logInfo('Debug session ended', { 
      sessionId: this.sessionId,
      events: this.events.length,
      diagnostics: this.diagnosticResults.length 
    });
    
    return this.visualTrace;
  }

  /**
   * 记录规则执行开始
   */
  recordRuleStart(rule: Rule, node: Node, context: AnalysisContext): void {
    if (!this.shouldTrace('enableRuleTracing')) return;
    
    const event = this.emitEvent({
      type: 'rule_start',
      level: 'trace',
      context: {
        ruleId: rule.id,
        nodeType: node.type,
        functionName: context.functionName,
        filePath: context.filePath,
        line: (node as any).span?.start.line,
        column: (node as any).span?.start.column,
      },
      data: {
        rule: {
          id: rule.id,
          name: rule.name,
          priority: rule.priority,
        },
        node: {
          type: node.type,
          span: (node as any).span,
        },
        context: {
          nestingLevel: context.nestingLevel,
          functionName: context.functionName,
        },
      },
      performance: {
        startTime: performance.now(),
        memoryUsage: process.memoryUsage(),
      },
    });
    
    // 检查断点
    this.checkBreakpoints(event);
    
    // 更新可视化追踪
    this.updateVisualTrace('rule_start', event);
  }

  /**
   * 记录规则执行结束
   */
  recordRuleEnd(rule: Rule, result: RuleResult): void {
    if (!this.shouldTrace('enableRuleTracing')) return;
    
    const event = this.emitEvent({
      type: 'rule_end',
      level: 'trace',
      context: {
        ruleId: rule.id,
      },
      data: {
        result: {
          ruleId: result.ruleId,
          complexity: result.complexity,
          isExempted: result.isExempted,
          cacheHit: result.cacheHit,
          executionTime: result.executionTime,
          reason: result.reason,
        },
      },
      performance: {
        startTime: performance.now() - result.executionTime,
        endTime: performance.now(),
        duration: result.executionTime,
      },
    });
    
    // 更新可视化追踪
    this.updateVisualTrace('rule_end', event);
    
    // 检查性能问题
    if (result.executionTime > 100) { // 100ms阈值
      this.recordWarning('Slow rule execution detected', {
        ruleId: rule.id,
        executionTime: result.executionTime,
        suggestion: 'Consider optimizing rule logic or adding caching',
      });
    }
  }

  /**
   * 记录节点分析开始
   */
  recordNodeStart(node: Node, context: AnalysisContext): void {
    if (!this.shouldTrace('enableNodeTracing')) return;
    
    const event = this.emitEvent({
      type: 'node_start',
      level: 'debug',
      context: {
        nodeType: node.type,
        functionName: context.functionName,
        filePath: context.filePath,
        line: (node as any).span?.start.line,
        column: (node as any).span?.start.column,
      },
      data: {
        node: {
          type: node.type,
          span: (node as any).span,
        },
        context: {
          nestingLevel: context.nestingLevel,
          functionName: context.functionName,
        },
      },
      performance: {
        startTime: performance.now(),
      },
    });
    
    // 检查断点
    this.checkBreakpoints(event);
    
    // 更新快照
    if (this.config.debugging.enableSnapshotCapture) {
      this.captureSnapshot(event);
    }
  }

  /**
   * 记录节点分析结束
   */
  recordNodeEnd(node: Node, analysis: NodeAnalysis): void {
    if (!this.shouldTrace('enableNodeTracing')) return;
    
    this.emitEvent({
      type: 'node_end',
      level: 'debug',
      context: {
        nodeType: node.type,
      },
      data: {
        analysis: {
          complexity: analysis.complexity,
          appliedRules: analysis.appliedRules.map(r => ({
            ruleId: r.ruleId,
            complexity: r.complexity,
            executionTime: r.executionTime,
          })),
          exemptions: analysis.exemptions.map(e => ({
            type: e.type,
            reason: e.reason,
          })),
          cacheUtilization: analysis.cacheUtilization,
        },
      },
      performance: {
        startTime: 0, // 添加默认值
        endTime: performance.now(),
        duration: analysis.analysisTime,
      },
    });
  }

  /**
   * 记录缓存访问
   */
  recordCacheAccess(key: string, hit: boolean, accessTime: number): void {
    if (!this.shouldTrace('enableCacheTracing')) return;
    
    this.emitEvent({
      type: hit ? 'cache_hit' : 'cache_miss',
      level: 'trace',
      context: {},
      data: {
        key,
        hit,
        accessTime,
      },
    });
    
    // 更新可视化追踪中的缓存状态
    if (this.currentSnapshot) {
      this.currentSnapshot.cacheState.recentAccesses.push({
        key,
        hit,
        timestamp: performance.now(),
      });
      
      if (hit) {
        this.currentSnapshot.cacheState.totalHits++;
      } else {
        this.currentSnapshot.cacheState.totalMisses++;
      }
      
      const total = this.currentSnapshot.cacheState.totalHits + this.currentSnapshot.cacheState.totalMisses;
      this.currentSnapshot.cacheState.hitRate = this.currentSnapshot.cacheState.totalHits / total;
    }
  }

  /**
   * 记录错误
   */
  recordError(message: string, error: Error, context?: any): void {
    this.emitEvent({
      type: 'error',
      level: 'error',
      context: context || {},
      data: {
        message,
        error: {
          name: error.name,
          message: error.message,
          stack: error.stack,
        },
        context,
      },
      stackTrace: error.stack?.split('\n'),
    });
  }

  /**
   * 记录警告
   */
  recordWarning(message: string, data?: any): void {
    this.emitEvent({
      type: 'warning',
      level: 'warn',
      context: {},
      data: { message, ...data },
    });
  }

  /**
   * 添加断点
   */
  addBreakpoint(breakpoint: Omit<Breakpoint, 'id' | 'hitCount'>): string {
    const id = this.generateId();
    const fullBreakpoint: Breakpoint = {
      id,
      hitCount: 0,
      ...breakpoint,
    };
    
    this.breakpoints.set(id, fullBreakpoint);
    this.logInfo('Breakpoint added', { id, type: breakpoint.type });
    
    return id;
  }

  /**
   * 移除断点
   */
  removeBreakpoint(id: string): boolean {
    const removed = this.breakpoints.delete(id);
    if (removed) {
      this.logInfo('Breakpoint removed', { id });
    }
    return removed;
  }

  /**
   * 启用/禁用断点
   */
  toggleBreakpoint(id: string, enabled?: boolean): boolean {
    const breakpoint = this.breakpoints.get(id);
    if (!breakpoint) return false;
    
    breakpoint.enabled = enabled !== undefined ? enabled : !breakpoint.enabled;
    this.logInfo('Breakpoint toggled', { id, enabled: breakpoint.enabled });
    return true;
  }

  /**
   * 继续执行
   */
  continue(): void {
    if (!this.isPaused) return;
    
    this.isPaused = false;
    this.activeBreakpoint = undefined;
    this.stepMode = 'none';
    
    this.logInfo('Execution continued');
  }

  /**
   * 单步执行 - 步入
   */
  stepInto(): void {
    this.stepMode = 'into';
    this.stepDepth = this.currentEventStack.length;
    this.isPaused = false;
    
    this.logInfo('Step into');
  }

  /**
   * 单步执行 - 步过
   */
  stepOver(): void {
    this.stepMode = 'over';
    this.stepDepth = this.currentEventStack.length;
    this.isPaused = false;
    
    this.logInfo('Step over');
  }

  /**
   * 单步执行 - 步出
   */
  stepOut(): void {
    this.stepMode = 'out';
    this.stepDepth = this.currentEventStack.length - 1;
    this.isPaused = false;
    
    this.logInfo('Step out');
  }

  /**
   * 获取当前执行状态
   */
  getExecutionState(): {
    isPaused: boolean;
    activeBreakpoint?: Breakpoint;
    currentSnapshot?: ExecutionSnapshot;
    stepMode: string;
    eventStack: string[];
  } {
    return {
      isPaused: this.isPaused,
      activeBreakpoint: this.activeBreakpoint ? this.breakpoints.get(this.activeBreakpoint) : undefined,
      currentSnapshot: this.currentSnapshot,
      stepMode: this.stepMode,
      eventStack: [...this.currentEventStack],
    };
  }

  /**
   * 获取所有事件
   */
  getEvents(filter?: {
    type?: DebugEventType;
    level?: DebugLevel;
    startTime?: number;
    endTime?: number;
  }): DebugEvent[] {
    let filteredEvents = [...this.events];
    
    if (filter) {
      if (filter.type) {
        filteredEvents = filteredEvents.filter(e => e.type === filter.type);
      }
      if (filter.level) {
        const levelOrder = { trace: 0, debug: 1, info: 2, warn: 3, error: 4 };
        const minLevel = levelOrder[filter.level];
        filteredEvents = filteredEvents.filter(e => levelOrder[e.level] >= minLevel);
      }
      if (filter.startTime) {
        filteredEvents = filteredEvents.filter(e => e.timestamp >= filter.startTime!);
      }
      if (filter.endTime) {
        filteredEvents = filteredEvents.filter(e => e.timestamp <= filter.endTime!);
      }
    }
    
    return filteredEvents;
  }

  /**
   * 获取诊断结果
   */
  getDiagnostics(): DiagnosticResult[] {
    return [...this.diagnosticResults];
  }

  /**
   * 获取可视化追踪数据
   */
  getVisualTrace(): VisualTrace {
    return { ...this.visualTrace };
  }

  /**
   * 添加事件监听器
   */
  addEventListener(type: DebugEventType, listener: (event: DebugEvent) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, []);
    }
    this.eventListeners.get(type)!.push(listener);
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(type: DebugEventType, listener: (event: DebugEvent) => void): void {
    const listeners = this.eventListeners.get(type);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index >= 0) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 运行自动诊断
   */
  runDiagnostics(): DiagnosticResult[] {
    if (!this.config.diagnostics.enableProblemDetection) {
      return [];
    }
    
    this.diagnosticResults = [];
    
    // 运行所有问题检测器
    for (const detector of this.problemDetectors) {
      try {
        const results = detector(this.events);
        this.diagnosticResults.push(...results);
      } catch (error) {
        this.recordError('Problem detector failed', error as Error);
      }
    }
    
    // 过滤结果按严重程度
    const severityOrder = { info: 0, warning: 1, error: 2, critical: 3 };
    const threshold = severityOrder[this.config.diagnostics.severityThreshold];
    
    this.diagnosticResults = this.diagnosticResults.filter(
      result => severityOrder[result.severity] >= threshold
    );
    
    this.logInfo('Diagnostics completed', { 
      results: this.diagnosticResults.length,
      threshold: this.config.diagnostics.severityThreshold 
    });
    
    return this.diagnosticResults;
  }

  // ============ 私有方法 ============

  private shouldTrace(type: keyof DebugSystemConfig['tracing']): boolean {
    if (!this.isEnabled || !this.isRunning) return false;
    
    const traceConfig = this.config.tracing;
    return traceConfig[type] || false;
  }

  private emitEvent(eventData: Omit<DebugEvent, 'id' | 'timestamp' | 'sessionId'>): DebugEvent {
    const event: DebugEvent = {
      id: this.generateId(),
      timestamp: performance.now(),
      sessionId: this.sessionId,
      ...eventData,
    };
    
    // 处理事件层次结构
    if (this.currentEventStack.length > 0) {
      event.parentEventId = this.currentEventStack[this.currentEventStack.length - 1];
    }
    
    if (eventData.type.endsWith('_start')) {
      this.currentEventStack.push(event.id);
    } else if (eventData.type.endsWith('_end')) {
      this.currentEventStack.pop();
    }
    
    this.events.push(event);
    
    // 触发事件监听器
    const listeners = this.eventListeners.get(event.type);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Event listener error:', error);
        }
      });
    }
    
    // 输出日志
    if (this.config.output.logToConsole) {
      this.logEvent(event);
    }
    
    return event;
  }

  private checkBreakpoints(event: DebugEvent): void {
    if (!this.config.debugging.enableBreakpoints || this.isPaused) return;
    
    for (const [id, breakpoint] of this.breakpoints) {
      if (!breakpoint.enabled) continue;
      
      if (this.matchesBreakpoint(event, breakpoint)) {
        breakpoint.hitCount++;
        breakpoint.lastHit = event.timestamp;
        
        if (breakpoint.actions.pauseExecution) {
          this.isPaused = true;
          this.activeBreakpoint = id;
          
          this.emitEvent({
            type: 'breakpoint_hit',
            level: 'info',
            context: event.context,
            data: { breakpointId: id, breakpoint },
          });
        }
        
        if (breakpoint.actions.logDetails) {
          this.logInfo('Breakpoint hit', { 
            breakpointId: id, 
            hitCount: breakpoint.hitCount,
            event: event.data 
          });
        }
        
        if (breakpoint.actions.captureSnapshot) {
          this.captureSnapshot(event);
        }
        
        if (breakpoint.actions.executeCallback) {
          try {
            breakpoint.actions.executeCallback(event);
          } catch (error) {
            this.recordError('Breakpoint callback failed', error as Error);
          }
        }
        
        // 如果暂停了执行，就不要继续检查其他断点
        if (this.isPaused) break;
      }
    }
    
    // 检查步进模式
    this.checkStepMode(event);
  }

  private matchesBreakpoint(event: DebugEvent, breakpoint: Breakpoint): boolean {
    const conditions = breakpoint.conditions;
    
    if (breakpoint.type === 'rule' && conditions.ruleId) {
      return event.context.ruleId === conditions.ruleId;
    }
    
    if (breakpoint.type === 'node' && conditions.nodeType) {
      return event.context.nodeType === conditions.nodeType;
    }
    
    if (breakpoint.type === 'function' && conditions.functionName) {
      return event.context.functionName === conditions.functionName;
    }
    
    if (breakpoint.type === 'file' && conditions.filePath) {
      return event.context.filePath?.includes(conditions.filePath) || false;
    }
    
    if (breakpoint.type === 'complexity' && conditions.complexity) {
      const complexity = event.data?.result?.complexity || event.data?.analysis?.complexity || 0;
      return this.evaluateComplexityCondition(complexity, conditions.complexity);
    }
    
    if (breakpoint.type === 'condition' && conditions.customCondition) {
      try {
        return conditions.customCondition(event);
      } catch (error) {
        this.recordError('Custom breakpoint condition failed', error as Error);
        return false;
      }
    }
    
    return false;
  }

  private evaluateComplexityCondition(
    complexity: number, 
    condition: { operator: string; value: number }
  ): boolean {
    switch (condition.operator) {
      case '>': return complexity > condition.value;
      case '<': return complexity < condition.value;
      case '=': return complexity === condition.value;
      case '>=': return complexity >= condition.value;
      case '<=': return complexity <= condition.value;
      default: return false;
    }
  }

  private checkStepMode(event: DebugEvent): void {
    if (this.stepMode === 'none') return;
    
    const currentDepth = this.currentEventStack.length;
    
    switch (this.stepMode) {
      case 'into':
        // 任何新事件都暂停
        this.isPaused = true;
        this.stepMode = 'none';
        break;
        
      case 'over':
        // 只有在相同或更浅层级时暂停
        if (currentDepth <= this.stepDepth) {
          this.isPaused = true;
          this.stepMode = 'none';
        }
        break;
        
      case 'out':
        // 只有在更浅层级时暂停
        if (currentDepth < this.stepDepth) {
          this.isPaused = true;
          this.stepMode = 'none';
        }
        break;
    }
    
    if (this.isPaused) {
      this.emitEvent({
        type: 'step_complete',
        level: 'info',
        context: event.context,
        data: { stepMode: this.stepMode, depth: currentDepth },
      });
    }
  }

  private captureSnapshot(event: DebugEvent): void {
    if (this.snapshots.length >= this.config.debugging.maxSnapshots) {
      this.snapshots.shift(); // 移除最旧的快照
    }
    
    const snapshot: ExecutionSnapshot = {
      id: this.generateId(),
      timestamp: event.timestamp,
      state: {
        currentRule: event.data?.rule,
        currentNode: event.data?.node,
        currentFunction: event.context.functionName,
        currentFilePath: event.context.filePath,
        nestingLevel: event.data?.context?.nestingLevel || 0,
        complexity: event.data?.analysis?.complexity || event.data?.result?.complexity || 0,
      },
      variables: {
        context: event.data?.context || {},
        ruleResults: [],
        nodeAnalysis: event.data?.analysis,
      },
      callStack: this.buildCallStack(),
      cacheState: {
        hitRate: 0,
        totalHits: 0,
        totalMisses: 0,
        recentAccesses: [],
      },
    };
    
    this.snapshots.push(snapshot);
    this.currentSnapshot = snapshot;
  }

  private buildCallStack(): ExecutionSnapshot['callStack'] {
    const stack: ExecutionSnapshot['callStack'] = [];
    
    // 基于当前事件栈构建调用栈
    for (const eventId of this.currentEventStack) {
      const event = this.events.find(e => e.id === eventId);
      if (event) {
        let type: 'rule' | 'function' | 'file' = 'rule';
        let name = 'unknown';
        
        if (event.type.includes('rule')) {
          type = 'rule';
          name = event.context.ruleId || 'unknown-rule';
        } else if (event.type.includes('function')) {
          type = 'function';
          name = event.context.functionName || 'anonymous';
        } else if (event.type.includes('file')) {
          type = 'file';
          name = event.context.filePath || 'unknown-file';
        }
        
        stack.push({
          type,
          name,
          startTime: event.timestamp,
          complexity: event.data?.complexity || 0,
        });
      }
    }
    
    return stack;
  }

  private updateVisualTrace(eventType: string, event: DebugEvent): void {
    if (!this.config.visualization.enableFlowGraph) return;
    
    // 实现可视化追踪数据的更新逻辑
    // 这里是简化版本，完整实现会更复杂
    
    if (eventType === 'rule_start') {
      // 添加节点到流程图
      this.visualTrace.flowGraph.nodes.push({
        id: event.id,
        type: 'rule',
        label: event.context.ruleId || 'unknown',
        complexity: 0,
        duration: 0,
        status: 'running',
      });
    } else if (eventType === 'rule_end') {
      // 更新节点状态
      const node = this.visualTrace.flowGraph.nodes.find(n => 
        n.type === 'rule' && n.label === event.context.ruleId
      );
      if (node) {
        node.status = 'completed';
        node.complexity = event.data?.result?.complexity || 0;
        node.duration = event.performance?.duration || 0;
      }
    }
  }

  private createEmptyVisualTrace(): VisualTrace {
    return {
      sessionId: this.sessionId,
      startTime: performance.now(),
      files: [],
      hotspots: [],
      flowGraph: {
        nodes: [],
        edges: [],
      },
    };
  }

  private initializeProblemDetectors(): void {
    // 性能问题检测器
    this.problemDetectors.push(this.detectPerformanceIssues.bind(this));
    
    // 正确性问题检测器
    this.problemDetectors.push(this.detectCorrectnessIssues.bind(this));
    
    // 配置问题检测器
    this.problemDetectors.push(this.detectConfigurationIssues.bind(this));
    
    // 使用问题检测器
    this.problemDetectors.push(this.detectUsageIssues.bind(this));
  }

  private detectPerformanceIssues(events: DebugEvent[]): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];
    
    // 检测慢规则
    const rulePerformance = new Map<string, number[]>();
    events.filter(e => e.type === 'rule_end').forEach(event => {
      const ruleId = event.context.ruleId!;
      const duration = event.performance?.duration || 0;
      
      if (!rulePerformance.has(ruleId)) {
        rulePerformance.set(ruleId, []);
      }
      rulePerformance.get(ruleId)!.push(duration);
    });
    
    for (const [ruleId, durations] of rulePerformance) {
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
      
      if (avgDuration > 50) { // 50ms阈值
        results.push({
          id: this.generateId(),
          severity: avgDuration > 200 ? 'error' : 'warning',
          category: 'performance',
          title: `Slow rule execution: ${ruleId}`,
          description: `Rule '${ruleId}' has an average execution time of ${avgDuration.toFixed(2)}ms, which may impact overall performance.`,
          rootCause: {
            type: 'rule_issue',
            description: 'Rule implementation may be inefficient',
            evidence: [
              `Average execution time: ${avgDuration.toFixed(2)}ms`,
              `Total executions: ${durations.length}`,
              `Maximum execution time: ${Math.max(...durations).toFixed(2)}ms`,
            ],
          },
          impact: {
            performance: Math.min((avgDuration / 50) * 30, 100),
            description: 'Slow rule execution increases total analysis time',
          },
          solutions: [
            {
              type: 'refactor',
              title: 'Optimize rule logic',
              description: 'Review and optimize the rule implementation for better performance',
              effort: 'medium',
              risk: 'low',
              steps: [
                'Profile the rule execution to identify bottlenecks',
                'Optimize AST traversal patterns',
                'Add early exit conditions where possible',
                'Consider caching intermediate results',
              ],
              expectedImprovement: `Potential 30-50% performance improvement`,
            },
            {
              type: 'configuration',
              title: 'Enable caching',
              description: 'Enable caching for this rule to avoid redundant calculations',
              effort: 'low',
              risk: 'low',
              steps: [
                'Enable rule-level caching in configuration',
                'Verify cache hit rates improve performance',
              ],
              expectedImprovement: `20-40% improvement for repeated patterns`,
            },
          ],
          resources: [
            {
              type: 'documentation',
              title: 'Performance Optimization Guide',
              url: '/docs/performance',
              description: 'Best practices for optimizing rule performance',
            },
          ],
        });
      }
    }
    
    return results;
  }

  private detectCorrectnessIssues(events: DebugEvent[]): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];
    
    // 检测错误
    const errors = events.filter(e => e.type === 'error');
    if (errors.length > 0) {
      results.push({
        id: this.generateId(),
        severity: 'error',
        category: 'correctness',
        title: `${errors.length} errors detected during analysis`,
        description: 'Errors occurred during the analysis process, which may affect result accuracy.',
        rootCause: {
          type: 'code_issue',
          description: 'Analysis errors indicate potential issues with input code or rule logic',
          evidence: errors.map(e => e.data?.message || 'Unknown error'),
        },
        impact: {
          accuracy: Math.min(errors.length * 20, 100),
          description: 'Errors may lead to incorrect complexity calculations',
        },
        solutions: [
          {
            type: 'quick_fix',
            title: 'Review error details',
            description: 'Examine the error messages and stack traces to identify root causes',
            effort: 'low',
            risk: 'low',
            steps: [
              'Check the error messages in the debug log',
              'Verify input code syntax is valid',
              'Update rules if they contain bugs',
            ],
            expectedImprovement: 'Elimination of analysis errors',
          },
        ],
        resources: [],
      });
    }
    
    return results;
  }

  private detectConfigurationIssues(events: DebugEvent[]): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];
    
    // 检测缓存命中率
    const cacheEvents = events.filter(e => e.type === 'cache_hit' || e.type === 'cache_miss');
    if (cacheEvents.length > 0) {
      const hits = cacheEvents.filter(e => e.type === 'cache_hit').length;
      const hitRate = hits / cacheEvents.length;
      
      if (hitRate < 0.3) { // 30%阈值
        results.push({
          id: this.generateId(),
          severity: 'warning',
          category: 'configuration',
          title: 'Low cache hit rate detected',
          description: `Cache hit rate is ${(hitRate * 100).toFixed(1)}%, which is below optimal range.`,
          rootCause: {
            type: 'config_issue',
            description: 'Cache configuration may not be optimal for this workload',
            evidence: [
              `Cache hit rate: ${(hitRate * 100).toFixed(1)}%`,
              `Total cache accesses: ${cacheEvents.length}`,
              `Cache hits: ${hits}`,
              `Cache misses: ${cacheEvents.length - hits}`,
            ],
          },
          impact: {
            performance: (1 - hitRate) * 40,
            description: 'Low cache hit rate leads to redundant calculations',
          },
          solutions: [
            {
              type: 'configuration',
              title: 'Adjust cache settings',
              description: 'Tune cache size and TTL settings for better performance',
              effort: 'low',
              risk: 'low',
              steps: [
                'Increase cache size if memory allows',
                'Adjust cache TTL based on usage patterns',
                'Enable cache prewarming for common patterns',
              ],
              expectedImprovement: `Potential ${((0.7 - hitRate) * 30).toFixed(0)}% performance improvement`,
            },
          ],
          resources: [
            {
              type: 'documentation',
              title: 'Caching Configuration Guide',
              url: '/docs/caching',
              description: 'How to optimize cache settings for your workload',
            },
          ],
        });
      }
    }
    
    return results;
  }

  private detectUsageIssues(events: DebugEvent[]): DiagnosticResult[] {
    const results: DiagnosticResult[] = [];
    
    // 检测未使用的规则
    const allRuleIds = new Set<string>();
    const usedRuleIds = new Set<string>();
    
    events.forEach(event => {
      if (event.context.ruleId) {
        allRuleIds.add(event.context.ruleId);
        if (event.type === 'rule_end') {
          usedRuleIds.add(event.context.ruleId);
        }
      }
    });
    
    const unusedRules = Array.from(allRuleIds).filter(id => !usedRuleIds.has(id));
    
    if (unusedRules.length > 0) {
      results.push({
        id: this.generateId(),
        severity: 'info',
        category: 'usage',
        title: `${unusedRules.length} unused rules detected`,
        description: 'Some registered rules were not executed during analysis.',
        rootCause: {
          type: 'config_issue',
          description: 'Rules may be unnecessary or incorrectly configured',
          evidence: unusedRules.map(id => `Unused rule: ${id}`),
        },
        impact: {
          performance: unusedRules.length * 2,
          description: 'Unused rules add overhead without providing value',
        },
        solutions: [
          {
            type: 'configuration',
            title: 'Review rule configuration',
            description: 'Remove unnecessary rules or adjust their applicability conditions',
            effort: 'low',
            risk: 'low',
            steps: [
              'Review which rules are actually needed for your use case',
              'Remove or disable unused rules',
              'Verify rule applicability conditions are correct',
            ],
            expectedImprovement: 'Reduced configuration complexity and minor performance gain',
          },
        ],
        resources: [],
      });
    }
    
    return results;
  }

  private generateDebugReport(): void {
    const report = {
      session: {
        id: this.sessionId,
        startTime: this.visualTrace.startTime,
        endTime: this.visualTrace.endTime,
        duration: (this.visualTrace.endTime || performance.now()) - this.visualTrace.startTime,
      },
      statistics: {
        totalEvents: this.events.length,
        eventsByType: this.getEventStatistics(),
        breakpointsHit: Array.from(this.breakpoints.values()).reduce((sum, bp) => sum + bp.hitCount, 0),
        snapshotsCaptured: this.snapshots.length,
        diagnosticsFound: this.diagnosticResults.length,
      },
      diagnostics: this.diagnosticResults,
      hotspots: this.visualTrace.hotspots,
      performance: {
        averageRuleTime: this.calculateAverageRuleTime(),
        cacheHitRate: this.calculateOverallCacheHitRate(),
        memoryUsage: this.getMemoryUsageStats(),
      },
    };
    
    if (this.config.output.logToFile && this.config.output.logFilePath) {
      // 将报告写入文件（简化实现）
      console.log('Debug report generated:', JSON.stringify(report, null, 2));
    }
    
    this.logInfo('Debug report generated', {
      events: report.statistics.totalEvents,
      diagnostics: report.diagnostics.length,
      duration: report.session.duration,
    });
  }

  private getEventStatistics(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    this.events.forEach(event => {
      stats[event.type] = (stats[event.type] || 0) + 1;
    });
    
    return stats;
  }

  private calculateAverageRuleTime(): number {
    const ruleTimes = this.events
      .filter(e => e.type === 'rule_end' && e.performance?.duration)
      .map(e => e.performance!.duration!);
    
    return ruleTimes.length > 0 
      ? ruleTimes.reduce((a, b) => a + b, 0) / ruleTimes.length 
      : 0;
  }

  private calculateOverallCacheHitRate(): number {
    const cacheEvents = this.events.filter(e => e.type === 'cache_hit' || e.type === 'cache_miss');
    const hits = this.events.filter(e => e.type === 'cache_hit').length;
    
    return cacheEvents.length > 0 ? hits / cacheEvents.length : 0;
  }

  private getMemoryUsageStats(): any {
    const memoryEvents = this.events
      .filter(e => e.performance?.memoryUsage)
      .map(e => e.performance!.memoryUsage!);
    
    if (memoryEvents.length === 0) return null;
    
    const heapUsed = memoryEvents.map(m => m.heapUsed);
    
    return {
      min: Math.min(...heapUsed),
      max: Math.max(...heapUsed),
      average: heapUsed.reduce((a, b) => a + b, 0) / heapUsed.length,
    };
  }

  private generateSessionId(): string {
    return `debug_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateId(): string {
    return `${this.eventIdCounter++}`;
  }

  private logEvent(event: DebugEvent): void {
    const levelColors = {
      trace: '\x1b[90m',  // gray
      debug: '\x1b[36m',  // cyan
      info: '\x1b[32m',   // green
      warn: '\x1b[33m',   // yellow
      error: '\x1b[31m',  // red
    };
    
    const reset = '\x1b[0m';
    const color = levelColors[event.level];
    
    const timestamp = new Date(this.visualTrace.startTime + event.timestamp).toISOString();
    const prefix = `${color}[${event.level.toUpperCase()}]${reset}`;
    const context = event.context.ruleId || event.context.nodeType || event.context.functionName || '';
    
    console.log(`${prefix} ${timestamp} ${event.type} ${context}`, 
      this.config.output.includeStackTraces && event.stackTrace ? 
        { data: event.data, stack: event.stackTrace } : 
        event.data
    );
  }

  private logInfo(message: string, data?: any): void {
    if (this.config.output.logToConsole) {
      console.log(`\x1b[32m[DEBUG-INFO]\x1b[0m ${message}`, data || '');
    }
  }
}