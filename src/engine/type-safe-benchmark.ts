/**
 * 类型安全的性能基准测试系统
 * 基于现有 BenchmarkSuite 和 RegressionTester 类，增强类型安全和错误处理
 */

import type { AsyncRuleEngine, AnalysisContext, FileAnalysis } from './types';
import { performance } from 'perf_hooks';
import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';

// 基准测试错误类型
export type BenchmarkError = 
  | { type: 'timeout'; duration: number }
  | { type: 'memory'; usage: number; limit: number }
  | { type: 'execution'; error: Error }
  | { type: 'validation'; message: string };

// 类型安全的性能指标接口
export interface SafePerformanceMetrics {
  readonly executionTime: number;
  readonly memoryUsage: Readonly<NodeJS.MemoryUsage>;
  readonly cpuUsage: Readonly<NodeJS.CpuUsage>;
  readonly throughput: number;
  readonly cacheHitRate: number;
  readonly errorRate: number;
  readonly customMetrics: Readonly<Record<string, number>>;
  readonly errors: readonly BenchmarkError[];
}

// 验证后的基准配置
export interface ValidatedBenchmarkConfig {
  readonly warmupRuns: number;
  readonly benchmarkRuns: number;
  readonly memoryMonitoring: boolean;
  readonly gcBetweenRuns: boolean;
  readonly timeoutMs: number;
  readonly enableProfiling: boolean;
  readonly outputFile: string | null;
}

// 输入基准配置
export interface BenchmarkConfig {
  warmupRuns?: number;
  benchmarkRuns?: number;
  memoryMonitoring?: boolean;
  gcBetweenRuns?: boolean;
  timeoutMs?: number;
  enableProfiling?: boolean;
  outputFile?: string;
}

export const DEFAULT_BENCHMARK_CONFIG: ValidatedBenchmarkConfig = {
  warmupRuns: 3,
  benchmarkRuns: 10,
  memoryMonitoring: true,
  gcBetweenRuns: true,
  timeoutMs: 60000,
  enableProfiling: false,
  outputFile: null,
};

// 类型安全的基准测试结果
export interface SafeBenchmarkResult {
  readonly testName: string;
  readonly config: ValidatedBenchmarkConfig;
  readonly timestamp: Date;
  readonly metrics: {
    readonly min: SafePerformanceMetrics;
    readonly max: SafePerformanceMetrics;
    readonly avg: SafePerformanceMetrics;
    readonly median: SafePerformanceMetrics;
    readonly p95: SafePerformanceMetrics;
    readonly p99: SafePerformanceMetrics;
    readonly stdDev: Readonly<Partial<SafePerformanceMetrics>>;
  };
  readonly rawData: readonly SafePerformanceMetrics[];
  readonly environment: {
    readonly nodeVersion: string;
    readonly platform: string;
    readonly arch: string;
    readonly cpuCount: number;
    readonly totalMemory: number;
  };
}

/**
 * 类型安全的性能监控器
 */
class SafePerformanceProfiler {
  private startTime: number = 0;
  private startCpuUsage: NodeJS.CpuUsage = { user: 0, system: 0 };
  private startMemoryUsage: NodeJS.MemoryUsage;
  private customCounters = new Map<string, number>();
  private errors: BenchmarkError[] = [];

  constructor() {
    this.startMemoryUsage = process.memoryUsage();
  }

  start(): void {
    this.startTime = performance.now();
    this.startCpuUsage = process.cpuUsage();
    this.startMemoryUsage = process.memoryUsage();
    this.customCounters.clear();
    this.errors = [];
  }

  addCustomMetric(name: string, value: number | undefined | null): void {
    if (typeof value === 'number' && !isNaN(value)) {
      this.customCounters.set(name, value);
    }
  }

  incrementCounter(name: string, increment: number | undefined | null = 1): void {
    const safeIncrement = typeof increment === 'number' && !isNaN(increment) ? increment : 1;
    const current = this.customCounters.get(name) ?? 0;
    this.customCounters.set(name, current + safeIncrement);
  }

  addError(error: BenchmarkError): void {
    this.errors.push(error);
  }

  end(): SafePerformanceMetrics {
    const endTime = performance.now();
    const endCpuUsage = process.cpuUsage(this.startCpuUsage);
    const endMemoryUsage = process.memoryUsage();

    const executionTime = Math.max(0, endTime - this.startTime);
    const throughput = executionTime > 0 ? 1000 / executionTime : 0;

    const customMetrics: Record<string, number> = {};
    this.customCounters.forEach((value, key) => {
      if (typeof value === 'number' && !isNaN(value)) {
        customMetrics[key] = value;
      }
    });

    return {
      executionTime,
      memoryUsage: Object.freeze({ ...endMemoryUsage }),
      cpuUsage: Object.freeze({ ...endCpuUsage }),
      throughput,
      cacheHitRate: this.safeGetMetric(customMetrics, 'cacheHitRate', 0),
      errorRate: this.safeGetMetric(customMetrics, 'errorRate', 0),
      customMetrics: Object.freeze(customMetrics),
      errors: Object.freeze([...this.errors]),
    };
  }

  private safeGetMetric(metrics: Record<string, number>, key: string, defaultValue: number): number {
    const value = metrics[key];
    return typeof value === 'number' && !isNaN(value) ? value : defaultValue;
  }
}

/**
 * 类型安全的基准测试套件
 */
export class TypeSafeBenchmarkSuite extends EventEmitter {
  private readonly config: ValidatedBenchmarkConfig;
  private readonly results = new Map<string, SafeBenchmarkResult>();

  constructor(config: BenchmarkConfig = {}) {
    super();
    this.config = this.validateConfig(config);
  }

  /**
   * 运行单个基准测试
   */
  async runBenchmark(
    testName: string,
    testFunction: () => Promise<void>
  ): Promise<SafeBenchmarkResult> {
    if (typeof testName !== 'string' || testName.trim() === '') {
      throw new TypeError('Test name must be a non-empty string');
    }

    if (typeof testFunction !== 'function') {
      throw new TypeError('Test function must be a function');
    }

    this.emit('benchmark-started', { testName, config: this.config });
    
    const profiler = new SafePerformanceProfiler();
    const rawData: SafePerformanceMetrics[] = [];

    try {
      // 预热阶段
      await this.runWarmupPhase(testName, testFunction);

      // 基准测试阶段
      for (let i = 0; i < this.config.benchmarkRuns; i++) {
        const metrics = await this.runSingleBenchmark(profiler, testFunction);
        rawData.push(metrics);
        
        this.emit('benchmark-run-completed', { 
          testName, 
          runIndex: i, 
          metrics 
        });
      }

      const result = this.calculateSafeStatistics(testName, rawData);
      this.results.set(testName, result);
      
      if (this.config.outputFile) {
        await this.saveResults(this.config.outputFile);
      }
      
      this.emit('benchmark-completed', { testName, result });
      return result;
      
    } catch (error: unknown) {
      const benchmarkError = this.createBenchmarkError(testName, error);
      this.emit('benchmark-error', { testName, error: benchmarkError });
      throw benchmarkError;
    }
  }

  /**
   * 运行内存基准测试
   */
  async runMemoryBenchmark(
    testName: string,
    testFunction: (profiler: SafePerformanceProfiler) => Promise<void>
  ): Promise<SafeBenchmarkResult> {
    return this.runBenchmark(testName, async () => {
      const profiler = new SafePerformanceProfiler();
      await testFunction(profiler);
    });
  }

  /**
   * 批量运行基准测试
   */
  async runBatchBenchmarks(
    tests: Array<{ name: string; fn: () => Promise<void> }>
  ): Promise<Map<string, SafeBenchmarkResult>> {
    const results = new Map<string, SafeBenchmarkResult>();
    
    for (const { name, fn } of tests) {
      try {
        const result = await this.runBenchmark(name, fn);
        results.set(name, result);
      } catch (error: unknown) {
        // 记录错误但继续执行其他测试
        console.error(`Benchmark failed for ${name}:`, error);
      }
    }
    
    return results;
  }

  /**
   * 获取所有基准测试结果
   */
  getAllResults(): ReadonlyMap<string, SafeBenchmarkResult> {
    return new Map(this.results);
  }

  /**
   * 生成类型安全的性能报告
   */
  generateReport(): {
    readonly summary: {
      readonly totalTests: number;
      readonly avgExecutionTime: number;
      readonly avgMemoryUsage: number;
      readonly avgThroughput: number;
      readonly totalErrors: number;
    };
    readonly detailed: readonly SafeBenchmarkResult[];
  } {
    const results = Array.from(this.results.values());
    
    if (results.length === 0) {
      return {
        summary: {
          totalTests: 0,
          avgExecutionTime: 0,
          avgMemoryUsage: 0,
          avgThroughput: 0,
          totalErrors: 0,
        },
        detailed: [],
      };
    }

    const validResults = results.filter(r => 
      typeof r.metrics.avg.executionTime === 'number' && 
      !isNaN(r.metrics.avg.executionTime)
    );

    const totalErrors = results.reduce((sum, r) => 
      sum + r.metrics.avg.errors.length, 0
    );

    const summary = {
      totalTests: results.length,
      avgExecutionTime: this.calculateSafeAverage(
        validResults.map(r => r.metrics.avg.executionTime)
      ),
      avgMemoryUsage: this.calculateSafeAverage(
        validResults.map(r => r.metrics.avg.memoryUsage.heapUsed)
      ),
      avgThroughput: this.calculateSafeAverage(
        validResults.map(r => r.metrics.avg.throughput)
      ),
      totalErrors,
    };
    
    return {
      summary: Object.freeze(summary),
      detailed: Object.freeze([...results]),
    };
  }

  private async runWarmupPhase(testName: string, testFunction: () => Promise<void>): Promise<void> {
    this.emit('warmup-started', { testName, runs: this.config.warmupRuns });
    
    for (let i = 0; i < this.config.warmupRuns; i++) {
      if (this.config.gcBetweenRuns && global.gc) {
        global.gc();
      }
      await testFunction();
    }
    
    this.emit('warmup-completed', { testName });
  }

  private async runSingleBenchmark(
    profiler: SafePerformanceProfiler,
    testFunction: () => Promise<void>
  ): Promise<SafePerformanceMetrics> {
    if (this.config.gcBetweenRuns && global.gc) {
      global.gc();
      await this.sleep(100); // 给GC时间
    }

    profiler.start();
    
    try {
      await Promise.race([
        testFunction(),
        this.createTimeoutPromise(this.config.timeoutMs)
      ]);
      
      return profiler.end();
    } catch (error: unknown) {
      const metrics = profiler.end();
      
      if (error instanceof Error && error.message === 'BENCHMARK_TIMEOUT') {
        profiler.addError({ 
          type: 'timeout', 
          duration: this.config.timeoutMs 
        });
      } else {
        profiler.addError({ 
          type: 'execution', 
          error: error instanceof Error ? error : new Error('Unknown error')
        });
      }
      
      return profiler.end();
    }
  }

  private calculateSafeStatistics(
    testName: string,
    rawData: SafePerformanceMetrics[]
  ): SafeBenchmarkResult {
    if (rawData.length === 0) {
      throw new Error(`No valid data for benchmark: ${testName}`);
    }

    const validData = rawData.filter(d => 
      typeof d.executionTime === 'number' && 
      !isNaN(d.executionTime) &&
      d.executionTime >= 0
    );

    if (validData.length === 0) {
      throw new Error(`No valid execution times for benchmark: ${testName}`);
    }

    const sortedByExecutionTime = [...validData].sort((a, b) => a.executionTime - b.executionTime);
    
    const calculateAverageMetrics = (data: SafePerformanceMetrics[]): SafePerformanceMetrics => {
      const len = data.length;
      
      return {
        executionTime: this.calculateSafeAverage(data.map(d => d.executionTime)),
        memoryUsage: Object.freeze({
          rss: this.calculateSafeAverage(data.map(d => d.memoryUsage.rss)),
          heapTotal: this.calculateSafeAverage(data.map(d => d.memoryUsage.heapTotal)),
          heapUsed: this.calculateSafeAverage(data.map(d => d.memoryUsage.heapUsed)),
          external: this.calculateSafeAverage(data.map(d => d.memoryUsage.external)),
          arrayBuffers: this.calculateSafeAverage(data.map(d => d.memoryUsage.arrayBuffers)),
        }),
        cpuUsage: Object.freeze({
          user: this.calculateSafeAverage(data.map(d => d.cpuUsage.user)),
          system: this.calculateSafeAverage(data.map(d => d.cpuUsage.system)),
        }),
        throughput: this.calculateSafeAverage(data.map(d => d.throughput)),
        cacheHitRate: this.calculateSafeAverage(data.map(d => d.cacheHitRate)),
        errorRate: this.calculateSafeAverage(data.map(d => d.errorRate)),
        customMetrics: Object.freeze({}),
        errors: Object.freeze([]),
      };
    };

    const getPercentile = (sorted: SafePerformanceMetrics[], percentile: number): SafePerformanceMetrics => {
      const index = Math.floor((percentile / 100) * sorted.length);
      return sorted[Math.min(index, sorted.length - 1)];
    };

    return {
      testName,
      config: this.config,
      timestamp: new Date(),
      metrics: {
        min: sortedByExecutionTime[0],
        max: sortedByExecutionTime[sortedByExecutionTime.length - 1],
        avg: calculateAverageMetrics(validData),
        median: getPercentile(sortedByExecutionTime, 50),
        p95: getPercentile(sortedByExecutionTime, 95),
        p99: getPercentile(sortedByExecutionTime, 99),
        stdDev: Object.freeze(this.calculateStandardDeviation(validData)),
      },
      rawData: Object.freeze([...rawData]),
      environment: Object.freeze({
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpuCount: require('os').cpus().length,
        totalMemory: require('os').totalmem(),
      }),
    };
  }

  private calculateSafeAverage(values: number[]): number {
    const validValues = values.filter(v => typeof v === 'number' && !isNaN(v));
    return validValues.length > 0 ? validValues.reduce((sum, v) => sum + v, 0) / validValues.length : 0;
  }

  private calculateStandardDeviation(data: SafePerformanceMetrics[]): Partial<SafePerformanceMetrics> {
    const executionTimes = data.map(d => d.executionTime).filter(t => typeof t === 'number' && !isNaN(t));
    
    if (executionTimes.length === 0) {
      return { executionTime: 0 };
    }

    const avg = executionTimes.reduce((sum, t) => sum + t, 0) / executionTimes.length;
    const variance = executionTimes.reduce((sum, t) => sum + Math.pow(t - avg, 2), 0) / executionTimes.length;
    
    return {
      executionTime: Math.sqrt(variance),
    };
  }

  private validateConfig(config: BenchmarkConfig): ValidatedBenchmarkConfig {
    const warmupRuns = config.warmupRuns ?? DEFAULT_BENCHMARK_CONFIG.warmupRuns;
    const benchmarkRuns = config.benchmarkRuns ?? DEFAULT_BENCHMARK_CONFIG.benchmarkRuns;
    const timeoutMs = config.timeoutMs ?? DEFAULT_BENCHMARK_CONFIG.timeoutMs;
    
    if (!Number.isInteger(warmupRuns) || warmupRuns < 0) {
      throw new TypeError('warmupRuns must be a non-negative integer');
    }
    
    if (!Number.isInteger(benchmarkRuns) || benchmarkRuns <= 0) {
      throw new TypeError('benchmarkRuns must be a positive integer');
    }
    
    if (!Number.isInteger(timeoutMs) || timeoutMs <= 0) {
      throw new TypeError('timeoutMs must be a positive integer');
    }
    
    return {
      warmupRuns,
      benchmarkRuns,
      memoryMonitoring: config.memoryMonitoring ?? DEFAULT_BENCHMARK_CONFIG.memoryMonitoring,
      gcBetweenRuns: config.gcBetweenRuns ?? DEFAULT_BENCHMARK_CONFIG.gcBetweenRuns,
      timeoutMs,
      enableProfiling: config.enableProfiling ?? DEFAULT_BENCHMARK_CONFIG.enableProfiling,
      outputFile: (typeof config.outputFile === 'string' && config.outputFile.trim() !== '') 
        ? config.outputFile.trim() 
        : null,
    };
  }

  private createBenchmarkError(testName: string, error: unknown): Error {
    const message = error instanceof Error 
      ? error.message 
      : (typeof error === 'string' ? error : 'Unknown benchmark error');
    
    return new Error(`Benchmark '${testName}' failed: ${message}`);
  }

  private createTimeoutPromise(timeoutMs: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('BENCHMARK_TIMEOUT'));
      }, timeoutMs);
    });
  }

  private async saveResults(filename: string): Promise<void> {
    try {
      const results = Object.fromEntries(this.results);
      const json = JSON.stringify(results, null, 2);
      await fs.promises.writeFile(filename, json, 'utf-8');
    } catch (error: unknown) {
      console.error('Failed to save benchmark results:', error);
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 类型安全的回归测试器
 */
export class TypeSafeRegressionTester {
  private readonly config: RegressionConfig;

  constructor(config: Partial<RegressionConfig> = {}) {
    this.config = this.validateRegressionConfig(config);
  }

  /**
   * 运行回归测试
   */
  async runRegressionTest(
    testName: string,
    currentResult: SafeBenchmarkResult
  ): Promise<RegressionResult> {
    if (typeof testName !== 'string' || testName.trim() === '') {
      throw new TypeError('Test name must be a non-empty string');
    }

    const baseline = await this.loadBaseline(testName);
    
    if (!baseline) {
      if (this.config.autoSave) {
        await this.saveBaseline(testName, currentResult);
      }
      
      return this.createEmptyRegressionResult(testName);
    }
    
    return this.compareWithBaseline(testName, baseline, currentResult);
  }

  private validateRegressionConfig(config: Partial<RegressionConfig>): RegressionConfig {
    return {
      baselineFile: config.baselineFile ?? 'performance-baseline.json',
      tolerancePercent: config.tolerancePercent ?? 10,
      criticalMetrics: config.criticalMetrics ?? ['executionTime', 'memoryUsage.heapUsed', 'throughput'],
      autoSave: config.autoSave ?? false,
      failOnRegression: config.failOnRegression ?? true,
    };
  }

  private createEmptyRegressionResult(testName: string): RegressionResult {
    return {
      testName,
      passed: true,
      regressions: [],
      improvements: [],
      summary: {
        totalRegressions: 0,
        criticalRegressions: 0,
        totalImprovements: 0,
        overallChangePercent: 0,
      },
    };
  }

  private async loadBaseline(testName: string): Promise<SafeBenchmarkResult | null> {
    try {
      const baselineData = await fs.promises.readFile(this.config.baselineFile, 'utf-8');
      const baselines = JSON.parse(baselineData);
      return baselines[testName] ?? null;
    } catch (error: unknown) {
      return null;
    }
  }

  private async saveBaseline(testName: string, result: SafeBenchmarkResult): Promise<void> {
    try {
      let baselines: Record<string, SafeBenchmarkResult> = {};
      
      try {
        const existingData = await fs.promises.readFile(this.config.baselineFile, 'utf-8');
        baselines = JSON.parse(existingData);
      } catch {
        // 文件不存在或无效，使用空对象
      }
      
      baselines[testName] = result;
      
      const json = JSON.stringify(baselines, null, 2);
      await fs.promises.writeFile(this.config.baselineFile, json, 'utf-8');
    } catch (error: unknown) {
      console.error('Failed to save baseline:', error);
    }
  }

  private compareWithBaseline(
    testName: string,
    baseline: SafeBenchmarkResult,
    current: SafeBenchmarkResult
  ): RegressionResult {
    const regressions: RegressionResult['regressions'] = [];
    const improvements: RegressionResult['improvements'] = [];
    
    for (const metricPath of this.config.criticalMetrics) {
      const baselineValue = this.getNestedValue(baseline.metrics.avg, metricPath);
      const currentValue = this.getNestedValue(current.metrics.avg, metricPath);
      
      if (typeof baselineValue === 'number' && typeof currentValue === 'number' && baselineValue > 0) {
        const change = currentValue - baselineValue;
        const changePercent = (change / baselineValue) * 100;
        
        if (Math.abs(changePercent) > this.config.tolerancePercent) {
          if (changePercent > 0) {
            regressions.push({
              metric: metricPath,
              baseline: baselineValue,
              current: currentValue,
              change,
              changePercent,
              isCritical: true,
            });
          } else {
            improvements.push({
              metric: metricPath,
              baseline: baselineValue,
              current: currentValue,
              improvement: Math.abs(change),
              improvementPercent: Math.abs(changePercent),
            });
          }
        }
      }
    }
    
    const totalRegressions = regressions.length;
    const criticalRegressions = regressions.filter(r => r.isCritical).length;
    const totalImprovements = improvements.length;
    
    const overallChangePercent = regressions.length > 0 
      ? regressions.reduce((sum, r) => sum + r.changePercent, 0) / regressions.length
      : 0;
    
    const passed = this.config.failOnRegression ? criticalRegressions === 0 : true;
    
    return {
      testName,
      passed,
      regressions,
      improvements,
      summary: {
        totalRegressions,
        criticalRegressions,
        totalImprovements,
        overallChangePercent,
      },
    };
  }

  private getNestedValue(obj: unknown, path: string): number | null {
    if (!obj || typeof obj !== 'object') {
      return null;
    }

    const keys = path.split('.');
    let current: any = obj;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return null;
      }
    }
    
    return typeof current === 'number' && !isNaN(current) ? current : null;
  }
}

// 回归测试相关接口（重用现有定义）
interface RegressionConfig {
  baselineFile: string;
  tolerancePercent: number;
  criticalMetrics: string[];
  autoSave: boolean;
  failOnRegression: boolean;
}

interface RegressionResult {
  testName: string;
  passed: boolean;
  regressions: Array<{
    metric: string;
    baseline: number;
    current: number;
    change: number;
    changePercent: number;
    isCritical: boolean;
  }>;
  improvements: Array<{
    metric: string;
    baseline: number;
    current: number;
    improvement: number;
    improvementPercent: number;
  }>;
  summary: {
    totalRegressions: number;
    criticalRegressions: number;
    totalImprovements: number;
    overallChangePercent: number;
  };
}

// 便捷函数
export function createTypeSafeBenchmarkSuite(config?: BenchmarkConfig): TypeSafeBenchmarkSuite {
  return new TypeSafeBenchmarkSuite(config);
}

export function createTypeSafeRegressionTester(config?: Partial<RegressionConfig>): TypeSafeRegressionTester {
  return new TypeSafeRegressionTester(config);
}