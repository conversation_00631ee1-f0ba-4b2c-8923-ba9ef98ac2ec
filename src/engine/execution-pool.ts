/**
 * 并行执行池实现
 * 管理规则并行执行、负载均衡和错误隔离
 */

import type { Node } from '@swc/core';
import * as os from 'os';
import type { 
  Rule, 
  RuleResult, 
  AnalysisContext, 
  ExecutionOptions, 
  ExecutionLoad, 
  RuleError 
} from '../engine/types';

// 执行计划
export interface ExecutionPlan {
  phases: Rule[][];
  estimatedTime: number;
  parallelism: number;
}

// 执行任务
export interface ExecutionTask {
  id: string;
  rule: Rule;
  node: Node;
  context: AnalysisContext;
  priority: number;
  timeout: number;
  retryCount: number;
  maxRetries: number;
  startTime?: number;
  endTime?: number;
}

// 执行结果
export interface ExecutionResult {
  taskId: string;
  ruleId: string;
  success: boolean;
  result?: RuleResult;
  error?: RuleError;
  executionTime: number;
  retryCount: number;
}

// 工作器接口
export interface Worker {
  id: string;
  isActive: boolean;
  currentTask?: ExecutionTask;
  completedTasks: number;
  failedTasks: number;
  totalExecutionTime: number;
  averageExecutionTime: number;
}

// 执行池统计
export interface ExecutionPoolStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  activeTasks: number;
  queuedTasks: number;
  averageExecutionTime: number;
  throughput: number; // 每秒处理的任务数
  errorRate: number;
  workers: Worker[];
  // Phase 7.2: 补充缺失的属性
  tasksExecuted: number;
  currentConcurrency: number;
  maxConcurrency: number;
  queueSize: number;
  errorCount: number;
  timeoutCount: number;
}

/**
 * 优先队列实现
 */
class PriorityQueue<T> {
  private items: Array<{ item: T; priority: number }> = [];

  enqueue(item: T, priority: number): void {
    const element = { item, priority };
    let added = false;

    for (let i = 0; i < this.items.length; i++) {
      const currentItem = this.items[i];
      if (currentItem && element.priority > currentItem.priority) {
        this.items.splice(i, 0, element);
        added = true;
        break;
      }
    }

    if (!added) {
      this.items.push(element);
    }
  }

  dequeue(): T | undefined {
    const element = this.items.shift();
    return element?.item;
  }

  isEmpty(): boolean {
    return this.items.length === 0;
  }

  size(): number {
    return this.items.length;
  }

  peek(): T | undefined {
    return this.items[0]?.item;
  }

  clear(): void {
    this.items = [];
  }
}

/**
 * 工作器实现
 */
class WorkerImpl implements Worker {
  public readonly id: string;
  public isActive: boolean = false;
  public currentTask?: ExecutionTask;
  public completedTasks: number = 0;
  public failedTasks: number = 0;
  public totalExecutionTime: number = 0;
  public averageExecutionTime: number = 0;

  constructor(id: string) {
    this.id = id;
  }

  async executeTask(task: ExecutionTask): Promise<ExecutionResult> {
    this.isActive = true;
    this.currentTask = task;
    task.startTime = performance.now();

    try {
      const result = await this.executeWithTimeout(task);
      task.endTime = performance.now();
      
      const executionTime = task.endTime - task.startTime;
      this.updateStats(executionTime, true);

      return {
        taskId: task.id,
        ruleId: task.rule.id,
        success: true,
        result,
        executionTime,
        retryCount: task.retryCount,
      };
    } catch (error) {
      task.endTime = performance.now();
      const executionTime = task.endTime - task.startTime;
      this.updateStats(executionTime, false);

      const ruleError: RuleError = {
        name: 'RuleExecutionError',
        message: error instanceof Error ? error.message : 'Unknown error',
        ruleId: task.rule.id,
        nodeType: task.node.type,
        isRecoverable: this.isRecoverableError(error),
        context: task.context,
      };

      return {
        taskId: task.id,
        ruleId: task.rule.id,
        success: false,
        error: ruleError,
        executionTime,
        retryCount: task.retryCount,
      };
    } finally {
      this.isActive = false;
      this.currentTask = undefined;
    }
  }

  private async executeWithTimeout(task: ExecutionTask): Promise<RuleResult> {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Rule '${task.rule.id}' execution timed out after ${task.timeout}ms`));
      }, task.timeout);

      try {
        const result = await task.rule.evaluate(task.node, task.context);
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  private updateStats(executionTime: number, success: boolean): void {
    if (success) {
      this.completedTasks++;
    } else {
      this.failedTasks++;
    }

    this.totalExecutionTime += executionTime;
    const totalTasks = this.completedTasks + this.failedTasks;
    this.averageExecutionTime = this.totalExecutionTime / totalTasks;
  }

  private isRecoverableError(error: any): boolean {
    // 判断错误是否可恢复
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      return !message.includes('timeout') && 
             !message.includes('out of memory') &&
             !message.includes('stack overflow');
    }
    return true;
  }
}

/**
 * 并行执行池实现
 */
export class ParallelExecutionPool {
  private options: ExecutionOptions;
  private workers: WorkerImpl[] = [];
  private taskQueue = new PriorityQueue<ExecutionTask>();
  private activeTasks = new Map<string, ExecutionTask>();
  private completedResults = new Map<string, ExecutionResult>();
  private errorHandlers: Array<(error: RuleError) => void> = [];
  private isRunning: boolean = false;
  private stats: ExecutionPoolStats = {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    activeTasks: 0,
    queuedTasks: 0,
    averageExecutionTime: 0,
    throughput: 0,
    errorRate: 0,
    workers: [],
    // Phase 7.2: 补充缺失的属性
    tasksExecuted: 0,
    currentConcurrency: 0,
    maxConcurrency: 0,
    queueSize: 0,
    errorCount: 0,
    timeoutCount: 0,
  };
  private taskIdCounter: number = 0;
  private throughputWindow: number[] = [];
  private lastThroughputUpdate: number = Date.now();
  private throughputTimer?: NodeJS.Timeout;

  constructor(options: ExecutionOptions) {
    this.options = {
      ...options,
      maxConcurrency: Math.max(1, options.maxConcurrency || this.getOptimalConcurrency()),
    };
    this.initializeWorkers();
    this.initializeStats();
    this.startThroughputMonitoring();
  }

  async executeRules(
    rules: Rule[], 
    node: Node, 
    context: AnalysisContext
  ): Promise<RuleResult[]> {
    const results: RuleResult[] = [];
    
    // 简化实现：顺序执行所有规则
    for (const rule of rules) {
      if (rule.canHandle(node)) {
        try {
          const result = await rule.evaluate(node, context);
          results.push(result);
        } catch (error) {
          console.warn(`Rule ${rule.id} failed:`, error);
        }
      }
    }
    
    return results;
  }

  private getOptimalConcurrency(): number {
    // 基于系统CPU核心数和内存情况动态计算
    const cpuCount = os.cpus().length;
    const memoryGB = os.totalmem() / (1024 * 1024 * 1024);
    
    // 启发式计算：CPU密集型任务通常使用CPU核心数
    // 内存充足时可以增加并发数
    let optimal = cpuCount;
    
    if (memoryGB > 8) {
      optimal = Math.min(cpuCount * 1.5, 16); // 最多16个并发
    }
    
    return Math.floor(optimal);
  }

  private startThroughputMonitoring(): void {
    this.throughputTimer = setInterval(() => {
      this.updateThroughput();
    }, 1000); // 每秒更新一次吞吐量
    
    // 使用 unref() 防止定时器阻止进程退出
    this.throughputTimer.unref();
  }

  private updateThroughput(): void {
    const now = Date.now();
    const windowSize = 10000; // 10秒窗口
    
    // 清理过期的数据点
    this.throughputWindow = this.throughputWindow.filter(
      timestamp => now - timestamp < windowSize
    );
    
    // 计算当前吞吐量（每秒完成的任务数）
    this.stats.throughput = this.throughputWindow.length / (windowSize / 1000);
    
    this.lastThroughputUpdate = now;
  }

  getCurrentLoad(): ExecutionLoad {
    const activeWorkers = this.workers.filter(w => w.isActive).length;
    const queuedTasks = this.taskQueue.size();
    const totalCapacity = this.workers.length;
    const utilizationRate = totalCapacity > 0 ? activeWorkers / totalCapacity : 0;

    return {
      activeWorkers,
      queuedTasks,
      totalCapacity,
      utilizationRate,
    };
  }

  onError(handler: (error: RuleError) => void): void {
    this.errorHandlers.push(handler);
  }

  onRuleError(handler: (error: RuleError) => void): void {
    this.errorHandlers.push(handler);
  }

  cancelAll(): void {
    this.taskQueue.clear();
    this.activeTasks.clear();
  }

  getStats(): ExecutionPoolStats {
    return {
      ...this.stats,
      workers: this.workers.map(w => ({ ...w })),
    };
  }

  getActiveTaskCount(): number {
    return this.activeTasks.size;
  }

  getMetrics(): any {
    return {
      executedTasks: this.stats.completedTasks,
      averageExecutionTime: this.stats.averageExecutionTime,
      errorRate: this.stats.errorRate,
      throughput: this.stats.throughput
    };
  }

  shutdown(): void {
    this.isRunning = false;
    this.taskQueue.clear();
    this.activeTasks.clear();
    this.completedResults.clear();
    
    // 清理定时器
    if (this.throughputTimer) {
      clearInterval(this.throughputTimer);
      this.throughputTimer = undefined;
    }
  }

  private createTask(rule: Rule, node: Node, context: AnalysisContext): ExecutionTask {
    return {
      id: `task-${++this.taskIdCounter}`,
      rule,
      node,
      context,
      priority: rule.priority,
      timeout: this.options.timeout,
      retryCount: 0,
      maxRetries: 3,
    };
  }

  private initializeWorkers(): void {
    for (let i = 0; i < this.options.maxConcurrency; i++) {
      const worker = new WorkerImpl(`worker-${i}`);
      this.workers.push(worker);
    }
  }

  private initializeStats(): void {
    this.stats = {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      queuedTasks: 0,
      averageExecutionTime: 0,
      throughput: 0,
      errorRate: 0,
      workers: [],
      // 补充缺失的属性
      tasksExecuted: 0,
      currentConcurrency: 0,
      maxConcurrency: this.options.maxConcurrency,
      queueSize: 0,
      errorCount: 0,
      timeoutCount: 0,
    };
  }
}

/**
 * 轻量级执行池实现（Null Object模式）
 * 提供与ParallelExecutionPool相同的接口，但所有方法都是空操作
 * 用于在禁用监控功能时提供无副作用的替代品
 */
export class LightweightExecutionPool {
  public stats: ExecutionPoolStats;
  public options: ExecutionOptions;

  constructor(options?: ExecutionOptions) {
    // 空构造函数：不创建任何工作器或定时器
    this.options = {
      maxConcurrency: 1,
      enableTimeout: false,
      enableMonitoring: false,
      enableCaching: false,
      enableCircuitBreaker: false,
      ...options
    };
    
    this.stats = {
      tasksExecuted: 0,
      averageExecutionTime: 0,
      currentConcurrency: 0,
      maxConcurrency: this.options.maxConcurrency,
      queueSize: 0,
      errorCount: 0,
      timeoutCount: 0,
      throughput: 0,
      // 兼容原有接口
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      queuedTasks: 0,
      errorRate: 0,
      workers: [],
    };
  }

  /**
   * 初始化工作器（轻量级版本为空实现）
   */
  initializeWorkers(): void {
    // 轻量级版本不需要初始化工作器
  }

  /**
   * 直接执行规则，无并行处理和监控开销
   */
  async executeRules(
    rules: Rule[], 
    node: Node, 
    context: AnalysisContext
  ): Promise<RuleResult[]> {
    const results: RuleResult[] = [];
    
    // 顺序执行所有规则，无并行优化
    for (const rule of rules) {
      if (rule.canHandle(node)) {
        try {
          const result = await rule.evaluate(node, context);
          results.push(result);
        } catch (error) {
          // 简单错误处理，继续执行其他规则
          console.warn(`Rule ${rule.id} failed:`, error);
        }
      }
    }
    
    return results;
  }

  /**
   * 无操作：不需要等待任何任务
   */
  async execute<T>(task: () => Promise<T>): Promise<T> {
    return await task();
  }

  /**
   * 获取空统计信息
   */
  getStats(): ExecutionPoolStats {
    return {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      queuedTasks: 0,
      averageExecutionTime: 0,
      throughput: 0,
      errorRate: 0,
      workers: [],
      // 补充缺失的属性
      tasksExecuted: 0,
      currentConcurrency: 0,
      maxConcurrency: this.options.maxConcurrency,
      queueSize: 0,
      errorCount: 0,
      timeoutCount: 0,
    };
  }

  /**
   * 获取空负载信息
   */
  getCurrentLoad(): ExecutionLoad {
    return {
      activeWorkers: 0,
      queuedTasks: 0,
      totalCapacity: 1,
      utilizationRate: 0,
    };
  }

  /**
   * 无操作：没有错误处理器需要注册
   */
  onError(_handler: (error: RuleError) => void): void {
    // 空方法：不注册错误处理器
  }

  /**
   * 无操作：没有规则错误处理器需要注册
   */
  onRuleError(_handler: (error: RuleError) => void): void {
    // 空方法：不注册规则错误处理器
  }

  /**
   * 无操作：没有活跃任务需要取消
   */
  cancelAll(): void {
    // 空方法：没有任务需要取消
  }

  /**
   * 无操作：没有资源需要清理
   */
  shutdown(): void {
    // 空方法：没有定时器或工作器需要清理
  }

  /**
   * 无操作：没有活跃任务数量
   */
  getActiveTaskCount(): number {
    return 0;
  }

  /**
   * 无操作：没有指标需要返回
   */
  getMetrics(): any {
    return {
      executedTasks: 0,
      averageExecutionTime: 0,
      errorRate: 0,
      throughput: 0
    };
  }

  /**
   * 获取执行池统计信息
   */
  getStatistics(): ExecutionPoolStats {
    return { ...this.stats };
  }

  /**
   * 设置最大并发数
   */
  setMaxConcurrency(maxConcurrency: number): void {
    this.options.maxConcurrency = Math.max(1, maxConcurrency);
    // 重新初始化workers以适应新的并发数
    this.initializeWorkers();
  }
}