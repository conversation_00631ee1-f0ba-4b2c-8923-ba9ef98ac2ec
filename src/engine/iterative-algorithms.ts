/**
 * 迭代算法优化器
 * 将递归算法转换为迭代实现，避免栈溢出和提升性能
 */

import type { Node, Module } from '@swc/core';
import type { AnalysisContext, NodeAnalysis } from './types';

// 迭代状态
export interface IterativeState<T> {
  currentNode: Node;
  context: T;
  depth: number;
  parent?: Node;
  index: number;
  isVisited: boolean;
}

// 遍历配置
export interface TraversalConfig {
  maxDepth: number;           // 最大遍历深度
  skipTypes: Set<string>;     // 跳过的节点类型
  earlyExit: boolean;         // 启用早期退出
  batchSize: number;          // 批处理大小
  enablePruning: boolean;     // 启用剪枝优化
}

export const DEFAULT_TRAVERSAL_CONFIG: TraversalConfig = {
  maxDepth: 100,
  skipTypes: new Set(['Identifier', 'Literal', 'Comment']),
  earlyExit: true,
  batchSize: 50,
  enablePruning: true,
};

// 工作项接口
export interface WorkItem<T = any> {
  node: Node;
  context: T;
  depth: number;
  parent?: Node;
  metadata?: Record<string, any>;
}

/**
 * 迭代式AST遍历器
 */
export class IterativeASTTraverser {
  private config: TraversalConfig;
  private visitedNodes = new WeakSet<Node>();
  private nodeDepthMap = new WeakMap<Node, number>();

  constructor(config: Partial<TraversalConfig> = {}) {
    this.config = { ...DEFAULT_TRAVERSAL_CONFIG, ...config };
  }

  /**
   * 迭代式深度优先遍历
   */
  *traverseDepthFirst<T>(
    root: Node,
    initialContext: T,
    visitor: (node: Node, context: T, depth: number) => T | null
  ): Generator<{ node: Node; context: T; depth: number }, void, unknown> {
    const stack: WorkItem<T>[] = [{ node: root, context: initialContext, depth: 0 }];
    
    while (stack.length > 0) {
      const current = stack.pop()!;
      
      // 检查深度限制
      if (current.depth > this.config.maxDepth) {
        continue;
      }
      
      // 检查是否应该跳过
      if (this.shouldSkipNode(current.node)) {
        continue;
      }
      
      // 检查是否已访问（避免循环引用）
      if (this.visitedNodes.has(current.node)) {
        continue;
      }
      
      this.visitedNodes.add(current.node);
      this.nodeDepthMap.set(current.node, current.depth);
      
      // 访问当前节点
      const newContext = visitor(current.node, current.context, current.depth);
      
      if (newContext === null && this.config.earlyExit) {
        continue; // 跳过子节点
      }
      
      const contextToUse = newContext || current.context;
      
      yield {
        node: current.node,
        context: contextToUse,
        depth: current.depth,
      };
      
      // 添加子节点到栈（逆序添加以保持正确的遍历顺序）
      const children = this.getChildNodes(current.node);
      for (let i = children.length - 1; i >= 0; i--) {
        const child = children[i];
        if (child) {
          stack.push({
            node: child,
            context: contextToUse,
            depth: current.depth + 1,
            parent: current.node,
          });
        }
      }
    }
  }

  /**
   * 迭代式广度优先遍历
   */
  *traverseBreadthFirst<T>(
    root: Node,
    initialContext: T,
    visitor: (node: Node, context: T, depth: number) => T | null
  ): Generator<{ node: Node; context: T; depth: number }, void, unknown> {
    const queue: WorkItem<T>[] = [{ node: root, context: initialContext, depth: 0 }];
    
    while (queue.length > 0) {
      const current = queue.shift()!;
      
      // 检查深度限制
      if (current.depth > this.config.maxDepth) {
        continue;
      }
      
      // 检查是否应该跳过
      if (this.shouldSkipNode(current.node)) {
        continue;
      }
      
      // 检查是否已访问
      if (this.visitedNodes.has(current.node)) {
        continue;
      }
      
      this.visitedNodes.add(current.node);
      this.nodeDepthMap.set(current.node, current.depth);
      
      // 访问当前节点
      const newContext = visitor(current.node, current.context, current.depth);
      
      if (newContext === null && this.config.earlyExit) {
        continue;
      }
      
      const contextToUse = newContext || current.context;
      
      yield {
        node: current.node,
        context: contextToUse,
        depth: current.depth,
      };
      
      // 添加子节点到队列
      const children = this.getChildNodes(current.node);
      for (const child of children) {
        queue.push({
          node: child,
          context: contextToUse,
          depth: current.depth + 1,
          parent: current.node,
        });
      }
    }
  }

  /**
   * 批量迭代遍历
   */
  async *traverseInBatches<T>(
    root: Node,
    initialContext: T,
    visitor: (batch: Array<{ node: Node; context: T; depth: number }>) => Promise<T[]>
  ): AsyncGenerator<T[], void, unknown> {
    const generator = this.traverseDepthFirst(root, initialContext, (node, context, depth) => context);
    let batch: Array<{ node: Node; context: T; depth: number }> = [];
    
    for (const item of generator) {
      batch.push(item);
      
      if (batch.length >= this.config.batchSize) {
        const results = await visitor(batch);
        yield results;
        batch = [];
      }
    }
    
    // 处理剩余的批次
    if (batch.length > 0) {
      const results = await visitor(batch);
      yield results;
    }
  }

  /**
   * 清理遍历状态
   */
  reset(): void {
    this.visitedNodes = new WeakSet<Node>();
    this.nodeDepthMap = new WeakMap<Node, number>();
  }

  /**
   * 获取节点深度
   */
  getNodeDepth(node: Node): number {
    return this.nodeDepthMap.get(node) || 0;
  }

  private shouldSkipNode(node: Node): boolean {
    return this.config.skipTypes.has(node.type);
  }

  private getChildNodes(node: Node): Node[] {
    const children: Node[] = [];
    
    // 递归查找子节点的迭代实现
    const stack = [node];
    const processed = new Set();
    
    while (stack.length > 0) {
      const current = stack.pop()!;
      
      if (processed.has(current)) {
        continue;
      }
      processed.add(current);
      
      // 检查所有属性
      for (const [key, value] of Object.entries(current)) {
        if (key === 'parent' || key === 'span') {
          continue; // 跳过父节点引用和位置信息
        }
        
        if (value && typeof value === 'object') {
          if ('type' in value && typeof value.type === 'string') {
            // 单个子节点
            if (current !== node) { // 只有当前节点不是根节点时才添加
              children.push(value as Node);
            } else {
              stack.push(value as Node);
            }
          } else if (Array.isArray(value)) {
            // 子节点数组
            for (const item of value) {
              if (item && typeof item === 'object' && 'type' in item) {
                if (current !== node) {
                  children.push(item as Node);
                } else {
                  stack.push(item as Node);
                }
              }
            }
          }
        }
      }
    }
    
    return children;
  }
}

/**
 * 优化的复杂度计算器（迭代版本）
 */
export class IterativeComplexityCalculator {
  private traverser: IterativeASTTraverser;
  private complexityRules: Map<string, (node: Node, context: AnalysisContext) => number> = new Map();

  constructor(config?: Partial<TraversalConfig>) {
    this.traverser = new IterativeASTTraverser(config);
    this.initializeComplexityRules();
  }

  /**
   * 迭代式函数复杂度计算
   */
  async calculateFunctionComplexity(func: Node, context: AnalysisContext): Promise<number> {
    let totalComplexity = 0;
    const nodeComplexities = new Map<Node, number>();
    
    // 迭代遍历函数体
    for (const { node, depth } of this.traverser.traverseDepthFirst(
      func,
      context,
      (node, context, depth) => {
        // 计算当前节点的复杂度
        const complexity = this.calculateNodeComplexity(node, context, depth);
        nodeComplexities.set(node, complexity);
        
        return context; // 继续遍历
      }
    )) {
      const nodeComplexity = nodeComplexities.get(node) || 0;
      totalComplexity += nodeComplexity;
    }
    
    return totalComplexity;
  }

  /**
   * 迭代式文件复杂度分析
   */
  async *analyzeFileIteratively(
    ast: Module,
    context: AnalysisContext
  ): AsyncGenerator<{ functionName: string; complexity: number; node: Node }, void, unknown> {
    const functions: Node[] = [];
    
    // 首先收集所有函数
    for (const { node } of this.traverser.traverseDepthFirst(
      ast,
      context,
      (node, context) => {
        if (this.isFunctionNode(node)) {
          functions.push(node);
          return null; // 不遍历函数内部（避免重复计算）
        }
        return context;
      }
    )) {
      // 遍历过程中已经收集了函数
    }
    
    // 逐个分析函数
    for (const func of functions) {
      const functionName = this.extractFunctionName(func);
      const complexity = await this.calculateFunctionComplexity(func, context);
      
      yield {
        functionName,
        complexity,
        node: func,
      };
    }
  }

  /**
   * 批量函数分析
   */
  async *analyzeFunctionsInBatches(
    functions: Node[],
    context: AnalysisContext,
    batchSize: number = 10
  ): AsyncGenerator<Array<{ functionName: string; complexity: number; node: Node }>, void, unknown> {
    for (let i = 0; i < functions.length; i += batchSize) {
      const batch = functions.slice(i, i + batchSize);
      const results: Array<{ functionName: string; complexity: number; node: Node }> = [];
      
      // 并行处理批次中的函数
      const promises = batch.map(async (func) => {
        const functionName = this.extractFunctionName(func);
        const complexity = await this.calculateFunctionComplexity(func, context);
        return { functionName, complexity, node: func };
      });
      
      const batchResults = await Promise.all(promises);
      results.push(...batchResults);
      
      yield results;
    }
  }

  /**
   * 查找特定模式的节点（迭代实现）
   */
  findNodesIteratively(
    root: Node,
    predicate: (node: Node) => boolean,
    maxResults?: number
  ): Node[] {
    const results: Node[] = [];
    let count = 0;
    
    for (const { node } of this.traverser.traverseDepthFirst(
      root,
      null,
      (node) => {
        if (predicate(node)) {
          results.push(node);
          count++;
          
          if (maxResults && count >= maxResults) {
            return null; // 早期退出
          }
        }
        return null; // 继续遍历
      }
    )) {
      if (maxResults && count >= maxResults) {
        break;
      }
    }
    
    return results;
  }

  /**
   * 计算嵌套深度（迭代实现）
   */
  calculateNestingDepth(root: Node): number {
    let maxDepth = 0;
    
    for (const { depth } of this.traverser.traverseDepthFirst(
      root,
      null,
      (node, context, depth) => {
        if (this.isNestingNode(node)) {
          maxDepth = Math.max(maxDepth, depth);
        }
        return null;
      }
    )) {
      // 遍历过程中已经更新了maxDepth
    }
    
    return maxDepth;
  }

  /**
   * 计算圈复杂度（迭代实现）
   */
  calculateCyclomaticComplexity(func: Node): number {
    let complexity = 1; // 基础复杂度
    
    for (const { node } of this.traverser.traverseDepthFirst(
      func,
      null,
      (node) => {
        if (this.isDecisionNode(node)) {
          complexity++;
        }
        return null;
      }
    )) {
      // 遍历过程中已经累加了复杂度
    }
    
    return complexity;
  }

  /**
   * 重置计算器状态
   */
  reset(): void {
    this.traverser.reset();
  }

  private calculateNodeComplexity(node: Node, context: AnalysisContext, depth: number): number {
    const rule = this.complexityRules.get(node.type);
    if (rule) {
      return rule(node, context);
    }
    return 0;
  }

  private initializeComplexityRules(): void {
    this.complexityRules = new Map([
      ['IfStatement', (node: any) => 1],
      ['WhileStatement', (node: any) => 1],
      ['ForStatement', (node: any) => 1],
      ['DoWhileStatement', (node: any) => 1],
      ['SwitchStatement', (node: any) => {
        // 计算switch语句的复杂度
        const cases = node.cases || [];
        return cases.length;
      }],
      ['ConditionalExpression', (node: any) => 1],
      ['LogicalExpression', (node: any) => {
        // 逻辑表达式的复杂度
        return node.operator === '&&' || node.operator === '||' ? 1 : 0;
      }],
      ['CatchClause', (node: any) => 1],
      ['CallExpression', (node: any) => {
        // 函数调用的复杂度（简化版本）
        return 0.5; // 函数调用增加0.5复杂度
      }],
      // JSX相关规则
      ['JSXElement', (node: any) => 0], // JSX元素默认不增加复杂度
      ['JSXFragment', (node: any) => 0],
      ['JSXConditionalExpression', (node: any) => 1], // JSX中的条件表达式
    ]);
  }

  private isFunctionNode(node: Node): boolean {
    return [
      'FunctionDeclaration',
      'FunctionExpression',
      'ArrowFunctionExpression',
      'MethodDefinition',
    ].includes(node.type);
  }

  private isNestingNode(node: Node): boolean {
    return [
      'IfStatement',
      'WhileStatement',
      'ForStatement',
      'DoWhileStatement',
      'SwitchStatement',
      'TryStatement',
      'BlockStatement',
    ].includes(node.type);
  }

  private isDecisionNode(node: Node): boolean {
    return [
      'IfStatement',
      'WhileStatement',
      'ForStatement',
      'DoWhileStatement',
      'ConditionalExpression',
      'LogicalExpression',
      'SwitchCase',
      'CatchClause',
    ].includes(node.type);
  }

  private extractFunctionName(func: Node): string {
    // 迭代方式提取函数名
    const stack = [func];
    
    while (stack.length > 0) {
      const current = stack.pop()!;
      
      // 检查各种函数名位置
      if (current && typeof current === 'object' && 'id' in current && current.id && typeof current.id === 'object' && 'name' in current.id) {
        return (current.id as any).name;
      }
      
      if (current && typeof current === 'object' && 'key' in current && current.key && typeof current.key === 'object' && 'name' in current.key) {
        return (current.key as any).name;
      }
      
      // 对于箭头函数或匿名函数
      if (current.type === 'VariableDeclarator' && 'id' in current && current.id) {
        return (current.id as any).name || 'anonymous';
      }
    }
    
    return 'anonymous';
  }
}

/**
 * 树形结构扁平化器
 */
export class TreeFlattener {
  /**
   * 将树形结构扁平化为数组（迭代实现）
   */
  static flatten<T extends { children?: T[] }>(
    root: T,
    includeRoot: boolean = true
  ): T[] {
    const result: T[] = [];
    const stack: T[] = [root];
    
    while (stack.length > 0) {
      const current = stack.pop()!;
      
      if (includeRoot || current !== root) {
        result.push(current);
      }
      
      if (current.children && current.children.length > 0) {
        // 逆序添加子节点以保持正确的遍历顺序
        for (let i = current.children.length - 1; i >= 0; i--) {
          const child = current.children[i];
          if (child) {
            stack.push(child);
          }
        }
      }
    }
    
    return result;
  }

  /**
   * 构建节点路径映射（迭代实现）
   */
  static buildPathMap<T extends { children?: T[] }>(
    root: T,
    getKey: (item: T) => string
  ): Map<string, string[]> {
    const pathMap = new Map<string, string[]>();
    const stack: Array<{ node: T; path: string[] }> = [{ node: root, path: [] }];
    
    while (stack.length > 0) {
      const { node, path } = stack.pop()!;
      const key = getKey(node);
      const currentPath = [...path, key];
      
      pathMap.set(key, currentPath);
      
      if (node.children && node.children.length > 0) {
        for (let i = node.children.length - 1; i >= 0; i--) {
          const child = node.children[i];
          if (child) {
            stack.push({
              node: child,
              path: currentPath,
            });
          }
        }
      }
    }
    
    return pathMap;
  }

  /**
   * 查找最深的叶子节点（迭代实现）
   */
  static findDeepestLeaf<T extends { children?: T[] }>(
    root: T,
    getValue: (item: T) => number
  ): { node: T; depth: number; value: number } | null {
    let deepest: { node: T; depth: number; value: number } | null = null;
    const stack: Array<{ node: T; depth: number }> = [{ node: root, depth: 0 }];
    
    while (stack.length > 0) {
      const { node, depth } = stack.pop()!;
      const value = getValue(node);
      
      // 如果是叶子节点
      if (!node.children || node.children.length === 0) {
        if (!deepest || depth > deepest.depth || (depth === deepest.depth && value > deepest.value)) {
          deepest = { node, depth, value };
        }
      } else {
        // 添加子节点到栈
        if (node.children) {
          for (let i = node.children.length - 1; i >= 0; i--) {
            const child = node.children[i];
            if (child !== undefined) {
              stack.push({
                node: child,
                depth: depth + 1,
              });
            }
          }
        }
      }
    }
    
    return deepest;
  }
}

/**
 * 性能优化的搜索算法
 */
export class OptimizedSearch {
  /**
   * 二分搜索（迭代实现）
   */
  static binarySearch<T>(
    sortedArray: T[],
    target: T,
    compareFn: (a: T, b: T) => number
  ): number {
    let left = 0;
    let right = sortedArray.length - 1;
    
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const midElement = sortedArray[mid];
      
      if (midElement === undefined) {
        break; // 数组访问越界，退出循环
      }
      
      const comparison = compareFn(midElement, target);
      
      if (comparison === 0) {
        return mid;
      } else if (comparison < 0) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }
    
    return -1; // 未找到
  }

  /**
   * 滑动窗口搜索
   */
  static slidingWindowSearch<T>(
    array: T[],
    windowSize: number,
    predicate: (window: T[]) => boolean
  ): number[] {
    const results: number[] = [];
    
    for (let i = 0; i <= array.length - windowSize; i++) {
      const window = array.slice(i, i + windowSize);
      if (predicate(window)) {
        results.push(i);
      }
    }
    
    return results;
  }

  /**
   * 快速模式匹配
   */
  static findPattern<T>(
    array: T[],
    pattern: T[],
    equals: (a: T, b: T) => boolean
  ): number[] {
    const results: number[] = [];
    
    if (pattern.length === 0 || pattern.length > array.length) {
      return results;
    }
    
    for (let i = 0; i <= array.length - pattern.length; i++) {
      let match = true;
      
      for (let j = 0; j < pattern.length; j++) {
        const arrayElement = array[i + j];
        const patternElement = pattern[j];
        
        if (arrayElement === undefined || patternElement === undefined) {
          match = false;
          break;
        }
        
        if (!equals(arrayElement, patternElement)) {
          match = false;
          break;
        }
      }
      
      if (match) {
        results.push(i);
      }
    }
    
    return results;
  }
}

// 导出便捷函数
export function createIterativeCalculator(config?: Partial<TraversalConfig>): IterativeComplexityCalculator {
  return new IterativeComplexityCalculator(config);
}

export function createIterativeTraverser(config?: Partial<TraversalConfig>): IterativeASTTraverser {
  return new IterativeASTTraverser(config);
}