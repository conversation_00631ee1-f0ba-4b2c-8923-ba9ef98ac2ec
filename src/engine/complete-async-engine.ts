/**
 * 完整的异步规则引擎实现
 * 实现 AsyncRuleEngine 和 RuleRegistry 的所有接口方法
 */

import type { Node, Module } from '@swc/core';
import type { 
  AsyncRuleEngine,
  Rule, 
  AnalysisContext, 
  NodeAnalysis, 
  FunctionAnalysis, 
  FileAnalysis,
  EngineMetrics,
  ResolvedEngineConfig
} from './types';
import { AsyncRuleEngineImpl } from './async-engine';
import type { RuleRegistry } from './registry';

/**
 * 完整的异步规则引擎实现
 * 继承现有的 AsyncRuleEngineImpl 并补全 RuleRegistry 接口
 */
export class CompleteAsyncRuleEngineImpl 
  extends AsyncRuleEngineImpl 
  implements AsyncRuleEngine, RuleRegistry {
  
  constructor(config: Partial<ResolvedEngineConfig> = {}) {
    super(config);
  }

  // === 获取内部 ruleRegistry 的访问器 ===
  private get internalRuleRegistry(): import('./registry').RuleRegistryImpl {
    return (this as any).ruleRegistry;
  }

  // === RuleRegistry 接口实现 ===

  /**
   * 获取适用于指定节点的所有规则
   */
  getRulesForNode(node: Node): Rule[] {
    return this.internalRuleRegistry.getRulesForNode(node);
  }

  /**
   * 根据优先级获取规则
   */
  getRulesByPriority(priority?: number): Rule[] {
    const allRules = this.getAllRules();
    
    if (priority !== undefined) {
      return allRules.filter(rule => rule.priority === priority);
    }
    
    // 如果未指定优先级，返回按优先级排序的所有规则
    return allRules.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 根据类别获取规则
   */
  getRulesByCategory(category: string): Rule[] {
    return this.internalRuleRegistry.getRulesByCategory(category);
  }

  /**
   * 获取所有已注册的规则
   */
  override getAllRules(): Rule[] {
    return this.internalRuleRegistry.getAllRules();
  }

  /**
   * 检查是否存在指定 ID 的规则
   */
  hasRule(ruleId: string): boolean {
    return this.internalRuleRegistry.hasRule(ruleId);
  }

  /**
   * 根据 ID 获取规则
   */
  getRule(ruleId: string): Rule | null {
    return this.internalRuleRegistry.getRule(ruleId);
  }

  /**
   * 注册规则（重写以添加类型验证）
   */
  override registerRule(rule: Rule, quiet: boolean = false): void {
    // 类型验证
    if (!this.validateRuleStructure(rule)) {
      const ruleId = this.extractRuleId(rule);
      throw new TypeError(`Invalid rule structure for rule '${ruleId}': rule must implement all required methods and properties`);
    }

    // 检查规则是否已存在
    if (this.hasRule(rule.id)) {
      throw new Error(`Cannot register rule '${rule.id}': a rule with this ID already exists`);
    }

    // 调用父类实现
    super.registerRule(rule, quiet);
  }

  /**
   * 安全地提取规则ID
   */
  private extractRuleId(rule: any): string {
    if (rule && typeof rule === 'object' && rule !== null && 'id' in rule && typeof rule.id === 'string') {
      return rule.id;
    }
    return 'unknown';
  }

  /**
   * 取消注册规则（重写以添加存在性检查）
   */
  override unregisterRule(ruleId: string): void {
    if (!this.hasRule(ruleId)) {
      throw new Error(`Cannot unregister non-existent rule: '${ruleId}'`);
    }

    // 调用父类实现
    super.unregisterRule(ruleId);
  }

  // === RuleRegistry 接口的其他方法（委托实现） ===

  /**
   * 解析规则依赖关系
   */
  resolveDependencies(ruleId: string): Rule[] {
    return this.internalRuleRegistry.resolveDependencies(ruleId);
  }

  /**
   * 验证规则依赖关系
   */
  validateDependencies(ruleId: string): import('./registry').DependencyValidationResult {
    return this.internalRuleRegistry.validateDependencies(ruleId);
  }

  /**
   * 加载插件
   */
  override async loadPlugin(pluginPath: string): Promise<void> {
    return this.internalRuleRegistry.loadPlugin(pluginPath);
  }

  /**
   * 卸载插件
   */
  unloadPlugin(pluginId: string): void {
    return this.internalRuleRegistry.unloadPlugin(pluginId);
  }

  /**
   * 获取已加载的插件
   */
  getLoadedPlugins(): import('./registry').LoadedPlugin[] {
    return this.internalRuleRegistry.getLoadedPlugins();
  }

  /**
   * 检查规则是否启用
   */
  isRuleEnabled(ruleId: string): boolean {
    return this.internalRuleRegistry.isRuleEnabled(ruleId);
  }

  /**
   * 启用规则
   */
  override enableRule(ruleId: string): void {
    return this.internalRuleRegistry.enableRule(ruleId);
  }

  /**
   * 禁用规则
   */
  override disableRule(ruleId: string): void {
    return this.internalRuleRegistry.disableRule(ruleId);
  }

  /**
   * 检测规则冲突
   */
  detectConflicts(): import('./registry').RuleConflict[] {
    return this.internalRuleRegistry.detectConflicts();
  }

  /**
   * 解决规则冲突
   */
  resolveConflict(conflictId: string, resolution: import('./registry').ConflictResolution): void {
    return this.internalRuleRegistry.resolveConflict(conflictId, resolution);
  }

  // === 增强的查询方法 ===

  /**
   * 根据多个条件查询规则
   */
  queryRules(options: {
    category?: string;
    priority?: number;
    enabled?: boolean;
    node?: Node;
    namePattern?: RegExp;
  }): Rule[] {
    let rules = this.getAllRules();

    // 按类别过滤
    if (options.category) {
      rules = this.getRulesByCategory(options.category);
    }

    // 按优先级过滤
    if (options.priority !== undefined) {
      rules = rules.filter(rule => rule.priority === options.priority);
    }

    // 按启用状态过滤
    if (options.enabled !== undefined) {
      rules = rules.filter(rule => this.isRuleEnabled(rule.id) === options.enabled);
    }

    // 按节点适用性过滤
    if (options.node) {
      const node = options.node; // 类型收窄
      rules = rules.filter(rule => rule.canHandle(node));
    }

    // 按名称模式过滤
    if (options.namePattern) {
      rules = rules.filter(rule => options.namePattern!.test(rule.name));
    }

    return rules;
  }

  /**
   * 获取规则详细信息
   */
  getRuleInfo(ruleId: string): {
    rule: Rule | null;
    isEnabled: boolean;
    dependencies: Rule[];
    dependents: string[];
    category: string | null;
    conflicts: import('./registry').RuleConflict[];
  } | null {
    const rule = this.getRule(ruleId);
    if (!rule) {
      return null;
    }

    const dependencies = this.resolveDependencies(ruleId);
    const allConflicts = this.detectConflicts();
    const conflicts = allConflicts.filter(conflict => 
      conflict.involvedRules.includes(ruleId)
    );

    // 查找依赖此规则的规则
    const dependents: string[] = [];
    for (const otherRule of this.getAllRules()) {
      if (otherRule.id !== ruleId) {
        const otherDeps = this.resolveDependencies(otherRule.id);
        if (otherDeps.some(dep => dep.id === ruleId)) {
          dependents.push(otherRule.id);
        }
      }
    }

    // 查找规则所属类别
    let category: string | null = null;
    const stats = this.getRuleStatistics();
    if (stats && typeof stats.byCategory === 'object') {
      for (const [cat, rules] of Object.entries(stats.byCategory)) {
        if (Array.isArray(rules) && rules.includes(ruleId)) {
          category = cat;
          break;
        }
      }
    }

    return {
      rule,
      isEnabled: this.isRuleEnabled(ruleId),
      dependencies,
      dependents,
      category,
      conflicts,
    };
  }

  /**
   * 批量操作规则
   */
  batchOperation(operations: Array<{
    type: 'register' | 'unregister' | 'enable' | 'disable';
    ruleId?: string;
    rule?: Rule;
  }>): { success: number; failed: Array<{ operation: any; error: string }> } {
    let success = 0;
    const failed: Array<{ operation: any; error: string }> = [];

    for (const operation of operations) {
      try {
        switch (operation.type) {
          case 'register':
            if (!operation.rule) {
              throw new Error('Rule object is required for register operation');
            }
            this.registerRule(operation.rule);
            break;
          case 'unregister':
            if (!operation.ruleId) {
              throw new Error('Rule ID is required for unregister operation');
            }
            this.unregisterRule(operation.ruleId);
            break;
          case 'enable':
            if (!operation.ruleId) {
              throw new Error('Rule ID is required for enable operation');
            }
            this.enableRule(operation.ruleId);
            break;
          case 'disable':
            if (!operation.ruleId) {
              throw new Error('Rule ID is required for disable operation');
            }
            this.disableRule(operation.ruleId);
            break;
          default:
            throw new Error(`Unknown operation type: ${(operation as any).type}`);
        }
        success++;
      } catch (error: unknown) {
        failed.push({
          operation,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return { success, failed };
  }

  // === 私有验证方法 ===

  /**
   * 验证规则结构的完整性
   */
  private validateRuleStructure(rule: unknown): rule is Rule {
    return (
      typeof rule === 'object' &&
      rule !== null &&
      typeof (rule as any).id === 'string' &&
      typeof (rule as any).name === 'string' &&
      typeof (rule as any).priority === 'number' &&
      typeof (rule as any).evaluate === 'function' &&
      typeof (rule as any).canHandle === 'function' &&
      typeof (rule as any).getDependencies === 'function'
    );
  }

  // === 增强的性能监控 ===

  /**
   * 获取增强的规则统计信息
   */
  getEnhancedRuleStatistics(): {
    basic: ReturnType<AsyncRuleEngineImpl['getRuleStatistics']>;
    detailed: {
      rulesByPriority: Map<number, Rule[]>;
      rulesByCategory: Map<string, Rule[]>;
      enabledRules: Rule[];
      disabledRules: Rule[];
      conflictingRules: string[];
      dependencyGraph: Map<string, string[]>;
    };
  } {
    const basicStats = this.getRuleStatistics();
    const allRules = this.getAllRules();
    
    // 按优先级分组
    const rulesByPriority = new Map<number, Rule[]>();
    for (const rule of allRules) {
      const existing = rulesByPriority.get(rule.priority) || [];
      existing.push(rule);
      rulesByPriority.set(rule.priority, existing);
    }

    // 按类别分组
    const rulesByCategory = new Map<string, Rule[]>();
    const categories = ['core', 'jsx', 'logical', 'plugin']; // 从 registry.ts 中获取
    for (const category of categories) {
      rulesByCategory.set(category, this.getRulesByCategory(category));
    }

    // 分离启用和禁用的规则
    const enabledRules = allRules.filter(rule => this.isRuleEnabled(rule.id));
    const disabledRules = allRules.filter(rule => !this.isRuleEnabled(rule.id));

    // 获取有冲突的规则
    const conflicts = this.detectConflicts();
    const conflictingRules = Array.from(new Set(
      conflicts.flatMap(conflict => conflict.involvedRules)
    ));

    // 构建依赖图
    const dependencyGraph = new Map<string, string[]>();
    for (const rule of allRules) {
      const deps = this.resolveDependencies(rule.id);
      dependencyGraph.set(rule.id, deps.map(dep => dep.id));
    }

    return {
      basic: basicStats,
      detailed: {
        rulesByPriority,
        rulesByCategory,
        enabledRules,
        disabledRules,
        conflictingRules,
        dependencyGraph,
      },
    };
  }

  // === 生命周期管理 ===

  /**
   * 释放资源，清理事件监听器和异步任务
   * 确保 Node.js 进程能够正常退出
   */
  async dispose(): Promise<void> {
    try {
      // 1. 停止所有异步操作
      this.shutdown();
      
      // 2. 清理缓存和内存
      this.clearCache();
      
      // 3. 卸载所有插件
      const loadedPlugins = this.getLoadedPlugins();
      for (const plugin of loadedPlugins) {
        try {
          this.unloadPlugin(plugin.id);
        } catch (error) {
          console.warn(`Failed to unload plugin ${plugin.id}:`, error);
        }
      }
      
      // 4. 调用所有规则的 onUnload 钩子
      const allRules = this.getAllRules();
      for (const rule of allRules) {
        try {
          if (rule.onUnload) {
            await rule.onUnload();
          }
        } catch (error) {
          console.warn(`Failed to call onUnload for rule ${rule.id}:`, error);
        }
      }
      
      // 5. 清理内部状态
      if (this.internalRuleRegistry && typeof (this.internalRuleRegistry as any).dispose === 'function') {
        await (this.internalRuleRegistry as any).dispose();
      }
      
      console.debug('AsyncRuleEngine disposed successfully');
    } catch (error) {
      console.error('Error during AsyncRuleEngine disposal:', error);
      throw error;
    }
  }
}