/**
 * 并行执行池实现
 * 管理规则并行执行、负载均衡和错误隔离
 */

import type { Node } from '@swc/core';
import * as os from 'os';
import type { 
  Rule, 
  RuleResult, 
  AnalysisContext, 
  ExecutionOptions, 
  ExecutionLoad, 
  RuleError 
} from '../engine/types';

// 执行计划
export interface ExecutionPlan {
  phases: Rule[][];
  estimatedTime: number;
  parallelism: number;
}

// 执行任务
export interface ExecutionTask {
  id: string;
  rule: Rule;
  node: Node;
  context: AnalysisContext;
  priority: number;
  timeout: number;
  retryCount: number;
  maxRetries: number;
  startTime?: number;
  endTime?: number;
}

// 执行结果
export interface ExecutionResult {
  taskId: string;
  ruleId: string;
  success: boolean;
  result?: RuleResult;
  error?: RuleError;
  executionTime: number;
  retryCount: number;
}

// 工作器接口
export interface Worker {
  id: string;
  isActive: boolean;
  currentTask?: ExecutionTask;
  completedTasks: number;
  failedTasks: number;
  totalExecutionTime: number;
  averageExecutionTime: number;
}

// 执行池统计
export interface ExecutionPoolStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  activeTasks: number;
  queuedTasks: number;
  averageExecutionTime: number;
  throughput: number; // 每秒处理的任务数
  errorRate: number;
  workers: Worker[];
}

/**
 * 优先队列实现
 */
class PriorityQueue<T> {
  private items: Array<{ item: T; priority: number }> = [];

  enqueue(item: T, priority: number): void {
    const element = { item, priority };
    let added = false;

    for (let i = 0; i < this.items.length; i++) {
      const currentItem = this.items[i];
      if (currentItem && element.priority > currentItem.priority) {
        this.items.splice(i, 0, element);
        added = true;
        break;
      }
    }

    if (!added) {
      this.items.push(element);
    }
  }

  dequeue(): T | undefined {
    const element = this.items.shift();
    return element?.item;
  }

  isEmpty(): boolean {
    return this.items.length === 0;
  }

  size(): number {
    return this.items.length;
  }

  peek(): T | undefined {
    return this.items[0]?.item;
  }

  clear(): void {
    this.items = [];
  }
}

/**
 * 工作器实现
 */
class WorkerImpl implements Worker {
  public readonly id: string;
  public isActive: boolean = false;
  public currentTask?: ExecutionTask;
  public completedTasks: number = 0;
  public failedTasks: number = 0;
  public totalExecutionTime: number = 0;
  public averageExecutionTime: number = 0;

  constructor(id: string) {
    this.id = id;
  }

  async executeTask(task: ExecutionTask): Promise<ExecutionResult> {
    this.isActive = true;
    this.currentTask = task;
    task.startTime = performance.now();

    try {
      const result = await this.executeWithTimeout(task);
      task.endTime = performance.now();
      
      const executionTime = task.endTime - task.startTime;
      this.updateStats(executionTime, true);

      return {
        taskId: task.id,
        ruleId: task.rule.id,
        success: true,
        result,
        executionTime,
        retryCount: task.retryCount,
      };
    } catch (error) {
      task.endTime = performance.now();
      const executionTime = task.endTime - task.startTime;
      this.updateStats(executionTime, false);

      const ruleError: RuleError = {
        name: 'RuleExecutionError',
        message: error instanceof Error ? error.message : 'Unknown error',
        ruleId: task.rule.id,
        nodeType: task.node.type,
        isRecoverable: this.isRecoverableError(error),
        context: task.context,
      };

      return {
        taskId: task.id,
        ruleId: task.rule.id,
        success: false,
        error: ruleError,
        executionTime,
        retryCount: task.retryCount,
      };
    } finally {
      this.isActive = false;
      this.currentTask = undefined;
    }
  }

  private async executeWithTimeout(task: ExecutionTask): Promise<RuleResult> {
    return new Promise(async (resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Rule '${task.rule.id}' execution timed out after ${task.timeout}ms`));
      }, task.timeout);

      try {
        const result = await task.rule.evaluate(task.node, task.context);
        clearTimeout(timeoutId);
        resolve(result);
      } catch (error) {
        clearTimeout(timeoutId);
        reject(error);
      }
    });
  }

  private updateStats(executionTime: number, success: boolean): void {
    if (success) {
      this.completedTasks++;
    } else {
      this.failedTasks++;
    }

    this.totalExecutionTime += executionTime;
    const totalTasks = this.completedTasks + this.failedTasks;
    this.averageExecutionTime = this.totalExecutionTime / totalTasks;
  }

  private isRecoverableError(error: any): boolean {
    // 判断错误是否可恢复
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      return !message.includes('timeout') && 
             !message.includes('out of memory') &&
             !message.includes('stack overflow');
    }
    return true;
  }
}

/**
 * 并行执行池实现
 */
export class ParallelExecutionPool {
  private options: ExecutionOptions;
  private workers: WorkerImpl[] = [];
  private taskQueue = new PriorityQueue<ExecutionTask>();
  private activeTasks = new Map<string, ExecutionTask>();
  private completedResults = new Map<string, ExecutionResult>();
  private errorHandlers: Array<(error: RuleError) => void> = [];
  private isRunning: boolean = false;
  private stats: ExecutionPoolStats = {
    totalTasks: 0,
    completedTasks: 0,
    failedTasks: 0,
    activeTasks: 0,
    queuedTasks: 0,
    averageExecutionTime: 0,
    throughput: 0,
    errorRate: 0,
    workers: [],
  };
  private taskIdCounter: number = 0;
  private dependencyGraph = new Map<string, string[]>();
  private scheduledTasks = new Set<string>();
  private throughputWindow: number[] = [];
  private lastThroughputUpdate: number = Date.now();
  private throughputTimer?: NodeJS.Timeout;

  constructor(options: ExecutionOptions) {
    this.options = {
      ...options,
      maxConcurrency: Math.max(1, options.maxConcurrency || this.getOptimalConcurrency()),
    };
    this.initializeWorkers();
    this.initializeStats();
    this.startThroughputMonitoring();
  }

  async executeRules(
    rules: Rule[], 
    node: Node, 
    context: AnalysisContext
  ): Promise<RuleResult[]> {
    const executionPlan = this.buildExecutionPlan(rules, node);
    const taskIds: string[] = [];
    
    // 按阶段创建和调度任务
    for (const phase of executionPlan.phases) {
      for (const rule of phase) {
        const task = this.createTask(rule, node, context);
        this.scheduledTasks.add(task.id);
        taskIds.push(task.id);
        
        // 优化任务优先级
        const adjustedPriority = this.calculateDynamicPriority(task, rule);
        this.taskQueue.enqueue(task, adjustedPriority);
      }
    }

    // 启动执行
    if (!this.isRunning) {
      this.startExecution();
    }

    // 等待所有任务完成
    return this.waitForTasks(taskIds);
  }

  /**
   * 智能执行计划构建
   */
  private buildExecutionPlan(rules: Rule[], node: Node): ExecutionPlan {
    // 构建依赖图
    const dependencyMap = new Map<string, string[]>();
    const inDegree = new Map<string, number>();
    
    rules.forEach(rule => {
      const deps = rule.getDependencies();
      dependencyMap.set(rule.id, deps);
      inDegree.set(rule.id, deps.length);
    });

    // 拓扑排序生成执行阶段
    const phases: Rule[][] = [];
    const remaining = new Set(rules);
    
    while (remaining.size > 0) {
      const currentPhase: Rule[] = [];
      
      // 找到当前阶段可执行的规则（无依赖或依赖已满足）
      for (const rule of remaining) {
        const deps = dependencyMap.get(rule.id) || [];
        const canExecute = deps.every(depId => 
          !remaining.has(rules.find(r => r.id === depId)!)
        );
        
        if (canExecute) {
          currentPhase.push(rule);
        }
      }
      
      // 移除已调度的规则
      currentPhase.forEach(rule => remaining.delete(rule));
      
      if (currentPhase.length > 0) {
        phases.push(currentPhase);
      } else {
        // 检测循环依赖
        console.warn('Possible circular dependency detected in rules');
        phases.push(Array.from(remaining));
        break;
      }
    }
    
    return {
      phases,
      estimatedTime: this.estimateExecutionTime(rules),
      parallelism: this.calculateOptimalParallelism(phases),
    };
  }

  /**
   * 动态优先级计算
   */
  private calculateDynamicPriority(task: ExecutionTask, rule: Rule): number {
    let priority = rule.priority;
    
    // 考虑执行历史
    const worker = this.findOptimalWorker(rule);
    if (worker) {
      // 根据工作器的历史性能调整优先级
      const avgTime = worker.averageExecutionTime;
      if (avgTime > 0 && avgTime < 100) {
        priority += 10; // 快速规则优先
      }
    }
    
    // 考虑当前负载
    const load = this.getCurrentLoad();
    if (load.utilizationRate > 0.8) {
      // 高负载时优先执行轻量级规则
      priority += rule.id.includes('exemption') ? 20 : 0;
    }
    
    return priority;
  }

  /**
   * 最优工作器选择
   */
  private findOptimalWorker(rule: Rule): WorkerImpl | null {
    const availableWorkers = this.workers.filter(w => !w.isActive);
    
    if (availableWorkers.length === 0) {
      return null;
    }
    
    // 选择最适合的工作器（基于历史性能）
    return availableWorkers.reduce((best, current) => {
      // 简单的负载均衡策略
      const bestLoad = best.completedTasks + best.failedTasks;
      const currentLoad = current.completedTasks + current.failedTasks;
      
      return currentLoad < bestLoad ? current : best;
    });
  }

  /**
   * 获取系统最优并发数
   */
  private getOptimalConcurrency(): number {
    // 基于系统CPU核心数和内存情况动态计算
    const cpuCount = os.cpus().length;
    const memoryGB = os.totalmem() / (1024 * 1024 * 1024);
    
    // 启发式计算：CPU密集型任务通常使用CPU核心数
    // 内存充足时可以增加并发数
    let optimal = cpuCount;
    
    if (memoryGB > 8) {
      optimal = Math.min(cpuCount * 1.5, 16); // 最多16个并发
    }
    
    return Math.floor(optimal);
  }

  /**
   * 智能负载均衡
   */
  private async processQueueWithLoadBalancing(): Promise<void> {
    while (this.isRunning) {
      // 动态调整工作器数量
      await this.adjustWorkerPool();
      
      // 查找最优工作器
      const availableWorker = this.findOptimalWorker({ id: 'temp' } as Rule);
      
      if (!availableWorker || this.taskQueue.isEmpty()) {
        await this.sleep(5); // 减少轮询间隔
        continue;
      }

      // 获取下一个任务
      const task = this.taskQueue.dequeue();
      if (!task) continue;

      // 检测任务依赖是否满足
      if (!this.areDependenciesSatisfied(task)) {
        // 重新排队等待依赖满足
        this.taskQueue.enqueue(task, task.priority - 1);
        continue;
      }

      // 分配任务给工作器
      this.activeTasks.set(task.id, task);
      this.updateRealTimeStats();

      // 异步执行任务
      this.executeTaskWithWorker(availableWorker, task);
    }
  }

  /**
   * 动态工作器池调整
   */
  private async adjustWorkerPool(): Promise<void> {
    const load = this.getCurrentLoad();
    const queueLength = this.taskQueue.size();
    
    // 自适应扩展策略
    if (load.utilizationRate > 0.9 && queueLength > 5 && this.workers.length < 16) {
      // 高负载且队列积压，增加工作器
      const newWorker = new WorkerImpl(`worker-${this.workers.length}`);
      this.workers.push(newWorker);
      console.debug(`Scaled up to ${this.workers.length} workers`);
    } else if (load.utilizationRate < 0.2 && this.workers.length > 2) {
      // 低负载，减少工作器（保留最少2个）
      const workerToRemove = this.workers.find(w => !w.isActive);
      if (workerToRemove) {
        this.workers = this.workers.filter(w => w.id !== workerToRemove.id);
        console.debug(`Scaled down to ${this.workers.length} workers`);
      }
    }
  }

  /**
   * 检查任务依赖是否满足
   */
  private areDependenciesSatisfied(task: ExecutionTask): boolean {
    const dependencies = task.rule.getDependencies();
    
    return dependencies.every(depId => {
      // 检查依赖的规则是否已完成
      const completedTaskIds = Array.from(this.completedResults.keys());
      return completedTaskIds.some(taskId => {
        const result = this.completedResults.get(taskId);
        return result?.ruleId === depId && result.success;
      });
    });
  }

  /**
   * 实时统计更新
   */
  private updateRealTimeStats(): void {
    this.stats.activeTasks = this.activeTasks.size;
    this.stats.queuedTasks = this.taskQueue.size();
    
    // 更新吞吐量
    this.updateThroughput();
  }

  /**
   * 吞吐量监控
   */
  private startThroughputMonitoring(): void {
    this.throughputTimer = setInterval(() => {
      this.updateThroughput();
    }, 1000); // 每秒更新一次吞吐量
  }

  private updateThroughput(): void {
    const now = Date.now();
    const windowSize = 10000; // 10秒窗口
    
    // 清理过期的数据点
    this.throughputWindow = this.throughputWindow.filter(
      timestamp => now - timestamp < windowSize
    );
    
    // 计算当前吞吐量（每秒完成的任务数）
    this.stats.throughput = this.throughputWindow.length / (windowSize / 1000);
    
    this.lastThroughputUpdate = now;
  }

  /**
   * 估算执行时间
   */
  private estimateExecutionTime(rules: Rule[]): number {
    // 基于历史数据估算
    let totalTime = 0;
    
    for (const rule of rules) {
      // 查找工作器的历史数据
      const avgTime = this.workers.reduce((sum, worker) => {
        return sum + worker.averageExecutionTime;
      }, 0) / this.workers.length;
      
      totalTime += avgTime || 50; // 默认50ms每个规则
    }
    
    // 考虑并行度
    const parallelism = Math.min(rules.length, this.options.maxConcurrency);
    return totalTime / parallelism;
  }

  /**
   * 计算最优并行度
   */
  private calculateOptimalParallelism(phases: Rule[][]): number {
    return phases.reduce((max, phase) => Math.max(max, phase.length), 1);
  }

  setMaxConcurrency(count: number): void {
    if (count < 1) {
      throw new Error('Max concurrency must be at least 1');
    }

    const currentCount = this.workers.length;
    
    if (count > currentCount) {
      // 增加工作器
      for (let i = currentCount; i < count; i++) {
        const worker = new WorkerImpl(`worker-${i}`);
        this.workers.push(worker);
      }
    } else if (count < currentCount) {
      // 减少工作器
      const workersToRemove = this.workers.slice(count);
      this.workers = this.workers.slice(0, count);
      
      // 等待被移除的工作器完成当前任务
      for (const worker of workersToRemove) {
        if (worker.isActive && worker.currentTask) {
          // 将任务重新放入队列
          this.taskQueue.enqueue(worker.currentTask, worker.currentTask.priority);
        }
      }
    }

    this.options.maxConcurrency = count;
  }

  getCurrentLoad(): ExecutionLoad {
    const activeWorkers = this.workers.filter(w => w.isActive).length;
    const queuedTasks = this.taskQueue.size();
    const totalCapacity = this.workers.length;
    const utilizationRate = totalCapacity > 0 ? activeWorkers / totalCapacity : 0;

    return {
      activeWorkers,
      queuedTasks,
      totalCapacity,
      utilizationRate,
    };
  }

  onRuleError(handler: (error: RuleError) => void): void {
    this.errorHandlers.push(handler);
  }

  getStatistics(): ExecutionPoolStats {
    return {
      ...this.stats,
      workers: this.workers.map(w => ({ ...w })),
    };
  }

  shutdown(): void {
    this.isRunning = false;
    this.taskQueue.clear();
    this.activeTasks.clear();
    this.completedResults.clear();
    
    // 清理定时器
    if (this.throughputTimer) {
      clearInterval(this.throughputTimer);
      this.throughputTimer = undefined;
    }
  }

  private createTask(rule: Rule, node: Node, context: AnalysisContext): ExecutionTask {
    return {
      id: `task-${++this.taskIdCounter}`,
      rule,
      node,
      context,
      priority: rule.priority,
      timeout: this.options.timeout,
      retryCount: 0,
      maxRetries: 3,
    };
  }

  private initializeWorkers(): void {
    for (let i = 0; i < this.options.maxConcurrency; i++) {
      const worker = new WorkerImpl(`worker-${i}`);
      this.workers.push(worker);
    }
  }

  private initializeStats(): void {
    this.stats = {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      queuedTasks: 0,
      averageExecutionTime: 0,
      throughput: 0,
      errorRate: 0,
      workers: [],
    };
  }
}

/**
 * 轻量级执行池实现（Null Object模式）
 * 提供与ParallelExecutionPool相同的接口，但所有方法都是空操作
 * 用于在禁用监控功能时提供无副作用的替代品
 */
export class LightweightExecutionPool {
  constructor(_options?: ExecutionOptions) {
    // 空构造函数：不创建任何工作器或定时器
  }

  /**
   * 直接执行规则，无并行处理和监控开销
   */
  async executeRules(
    rules: Rule[], 
    node: Node, 
    context: AnalysisContext
  ): Promise<RuleResult[]> {
    const results: RuleResult[] = [];
    
    // 顺序执行所有规则，无并行优化
    for (const rule of rules) {
      if (rule.canHandle(node)) {
        try {
          const result = await rule.evaluate(node, context);
          results.push(result);
        } catch (error) {
          // 简单错误处理，继续执行其他规则
          console.warn(`Rule ${rule.id} failed:`, error);
        }
      }
    }
    
    return results;
  }

  /**
   * 无操作：不需要等待任何任务
   */
  async execute<T>(task: () => Promise<T>): Promise<T> {
    return await task();
  }

  /**
   * 获取空统计信息
   */
  getStats(): ExecutionPoolStats {
    return {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      queuedTasks: 0,
      averageExecutionTime: 0,
      throughput: 0,
      errorRate: 0,
      workers: [],
    };
  }

  /**
   * 获取空负载信息
   */
  getCurrentLoad(): ExecutionLoad {
    return {
      activeWorkers: 0,
      queuedTasks: 0,
      totalCapacity: 1,
      utilizationRate: 0,
    };
  }

  /**
   * 无操作：没有错误处理器需要注册
   */
  onError(_handler: (error: RuleError) => void): void {
    // 空方法：不注册错误处理器
  }

  /**
   * 无操作：没有活跃任务需要取消
   */
  cancelAll(): void {
    // 空方法：没有任务需要取消
  }

  /**
   * 无操作：没有资源需要清理
   */
  shutdown(): void {
    // 空方法：没有定时器或工作器需要清理
  }

  /**
   * 无操作：没有活跃任务数量
   */
  getActiveTaskCount(): number {
    return 0;
  }

  /**
   * 无操作：没有指标需要返回
   */
  getMetrics(): any {
    return {
      executedTasks: 0,
      averageExecutionTime: 0,
      errorRate: 0,
      throughput: 0
  }
}

/**
 * 轻量级执行池实现（Null Object模式）
 * 提供与ParallelExecutionPool相同的接口，但所有方法都是空操作
 * 用于在禁用监控功能时提供无副作用的替代品
 */
export class LightweightExecutionPool {
  constructor(_options?: ExecutionOptions) {
    // 空构造函数：不创建任何工作器或定时器
  }

  /**
   * 直接执行规则，无并行处理和监控开销
   */
  async executeRules(
    rules: Rule[], 
    node: Node, 
    context: AnalysisContext
  ): Promise<RuleResult[]> {
    const results: RuleResult[] = [];
    
    // 顺序执行所有规则，无并行优化
    for (const rule of rules) {
      if (rule.canHandle(node)) {
        try {
          const result = await rule.evaluate(node, context);
          results.push(result);
        } catch (error) {
          // 简单错误处理，继续执行其他规则
          console.warn(`Rule ${rule.id} failed:`, error);
        }
      }
    }
    
    return results;
  }

  /**
   * 无操作：不需要等待任何任务
   */
  async execute<T>(task: () => Promise<T>): Promise<T> {
    return await task();
  }

  /**
   * 获取空统计信息
   */
  getStats(): ExecutionPoolStats {
    return {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      queuedTasks: 0,
      averageExecutionTime: 0,
      throughput: 0,
      errorRate: 0,
      workers: [],
    };
  }

  /**
   * 获取空负载信息
   */
  getCurrentLoad(): ExecutionLoad {
    return {
      activeWorkers: 0,
      queuedTasks: 0,
      totalCapacity: 1,
      utilizationRate: 0,
    };
  }

  /**
   * 无操作：没有错误处理器需要注册
   */
  onError(_handler: (error: RuleError) => void): void {
    // 空方法：不注册错误处理器
  }

  /**
   * 无操作：没有活跃任务需要取消
   */
  cancelAll(): void {
    // 空方法：没有任务需要取消
  }

  /**
   * 无操作：没有资源需要清理
   */
  shutdown(): void {
    // 空方法：没有定时器或工作器需要清理
  }

  /**
   * 无操作：没有活跃任务数量
   */
  getActiveTaskCount(): number {
    return 0;
  }

  /**
   * 无操作：没有指标需要返回
   */
  getMetrics(): any {
    return {
      executedTasks: 0,
      averageExecutionTime: 0,
      errorRate: 0,
      throughput: 0
    };
  }
}

  private async executeTaskWithWorker(worker: WorkerImpl, task: ExecutionTask): Promise<void> {
    try {
      // 增加总任务计数
      this.stats.totalTasks++;
      
      const result = await worker.executeTask(task);
      
      // 处理执行结果
      this.activeTasks.delete(task.id);
      this.completedResults.set(task.id, result);
      this.scheduledTasks.delete(task.id);
      
      // 记录完成的任务时间点用于吞吐量计算
      this.throughputWindow.push(Date.now());
      
      if (result.success) {
        this.stats.completedTasks++;
      } else {
        this.stats.failedTasks++;
        
        // 智能重试机制
        if (this.shouldRetryTask(task, result)) {
          task.retryCount++;
          // 降低重试任务的优先级
          this.taskQueue.enqueue(task, task.priority - 10);
          console.debug(`Retrying task ${task.id}, attempt ${task.retryCount}`);
        } else {
          // 触发错误处理器
          if (result.error) {
            this.notifyErrorHandlers(result.error);
          }
        }
      }
      
      // 更新统计信息
      this.updateStats();
    } catch (error) {
      console.error(`Unexpected error in worker ${worker.id}:`, error);
      this.activeTasks.delete(task.id);
      this.scheduledTasks.delete(task.id);
      this.stats.failedTasks++;
      
      // 记录系统级错误
      const systemError: RuleError = {
        name: 'SystemError',
        message: error instanceof Error ? error.message : 'Unknown system error',
        ruleId: task.rule.id,
        nodeType: task.node.type,
        isRecoverable: false,
        context: task.context,
      };
      
      this.notifyErrorHandlers(systemError);
    }
  }

  /**
   * 智能重试决策
   */
  private shouldRetryTask(task: ExecutionTask, result: ExecutionResult): boolean {
    // 检查是否达到最大重试次数
    if (task.retryCount >= task.maxRetries) {
      return false;
    }
    
    // 检查错误是否可恢复
    if (!result.error?.isRecoverable) {
      return false;
    }
    
    // 检查是否是瞬时错误（如内存压力、网络问题等）
    const isTransientError = result.error.message.includes('timeout') ||
                           result.error.message.includes('memory') ||
                           result.error.message.includes('busy');
    
    return isTransientError;
  }

  private async waitForTasks(taskIds: string[]): Promise<RuleResult[]> {
    const results: RuleResult[] = [];
    const maxWaitTime = 30000; // 30秒超时
    const startTime = Date.now();

    while (results.length < taskIds.length) {
      // 检查超时
      if (Date.now() - startTime > maxWaitTime) {
        throw new Error('Task execution timeout');
      }

      // 检查已完成的任务
      for (const taskId of taskIds) {
        const result = this.completedResults.get(taskId);
        if (result && !results.find(r => r.ruleId === result.ruleId)) {
          if (result.success && result.result) {
            results.push(result.result);
          } else if (result.error) {
            // 对于失败的任务，创建一个默认结果
            const defaultResult: RuleResult = {
              ruleId: result.ruleId,
              complexity: 0,
              isExempted: false,
              shouldIncreaseNesting: false,
              reason: `Rule execution failed: ${result.error.message}`,
              suggestions: [],
              metadata: { nodeType: result.error.nodeType, error: true },
              executionTime: result.executionTime,
              cacheHit: false,
            };
            results.push(defaultResult);
          }
        }
      }

      // 短暂等待
      await this.sleep(10);
    }

    // 清理完成的结果
    for (const taskId of taskIds) {
      this.completedResults.delete(taskId);
    }

    return results;
  }

  private updateStats(): void {
    const totalTasks = this.stats.completedTasks + this.stats.failedTasks;
    this.stats.errorRate = totalTasks > 0 ? this.stats.failedTasks / totalTasks : 0;
    
    // 计算平均执行时间
    const totalExecutionTime = this.workers.reduce((sum, w) => sum + w.totalExecutionTime, 0);
    this.stats.averageExecutionTime = totalTasks > 0 ? totalExecutionTime / totalTasks : 0;
    
    // 计算吞吐量（简化版本）
    this.stats.throughput = this.stats.completedTasks; // 实际应该考虑时间窗口
    
    this.stats.activeTasks = this.activeTasks.size;
    this.stats.queuedTasks = this.taskQueue.size();
  }

  private notifyErrorHandlers(error: RuleError): void {
    for (const handler of this.errorHandlers) {
      try {
        handler(error);
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError);
      }
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}