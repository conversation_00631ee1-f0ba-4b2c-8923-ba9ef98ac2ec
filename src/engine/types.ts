/**
 * 现代化规则引擎核心类型定义
 * 支持异步处理、智能缓存、并行执行的下一代复杂度分析引擎
 */

import type { Node, Module } from '@swc/core';

// 可变分析上下文（用于对象池）
export interface MutableAnalysisContext {
  // 文件上下文
  filePath: string;
  fileContent: string;
  ast: Module;
  
  // 函数上下文
  currentFunction?: Node;
  functionName: string;
  nestingLevel: number;
  
  // 配置上下文  
  config: ResolvedEngineConfig;
  jsxMode: JSXAnalysisMode;
  rules: EnabledRules;
  
  // 缓存上下文
  cache: import('../cache/types').CacheManager;
  metrics: AnalysisMetrics;
  
  // 扩展上下文
  plugins: LoadedPlugin[];
  customData: Map<string, any>;
}

// 分析上下文（只读版本，用于规则执行）
export interface AnalysisContext {
  // 文件上下文
  readonly filePath: string;
  readonly fileContent: string;
  readonly ast: Module;
  
  // 函数上下文
  readonly currentFunction?: Node;
  readonly functionName: string;
  readonly nestingLevel: number;
  
  // 配置上下文  
  readonly config: ResolvedEngineConfig;
  readonly jsxMode: JSXAnalysisMode;
  readonly rules: EnabledRules;
  
  // 缓存上下文
  readonly cache: import('../cache/types').CacheManager;
  readonly metrics: AnalysisMetrics;
  
  // 扩展上下文
  readonly plugins: LoadedPlugin[];
  readonly customData: Map<string, any>;
}

// 规则接口
export interface Rule {
  readonly id: string;
  readonly name: string;
  readonly priority: number;
  
  // 规则评估
  evaluate(node: Node, context: AnalysisContext): Promise<RuleResult>;
  canHandle(node: Node): boolean;
  getDependencies(): string[];
  
  // 可选的生命周期钩子
  onLoad?(engine: AsyncRuleEngine): Promise<void>;
  onUnload?(): Promise<void>;
}

// 规则执行结果
export interface RuleResult {
  readonly ruleId: string;
  readonly complexity: number;
  readonly isExempted: boolean;
  readonly shouldIncreaseNesting: boolean;
  
  // 详细信息
  readonly reason: string;
  readonly suggestions: Suggestion[];
  readonly metadata: RuleMetadata;
  
  // 性能信息
  readonly executionTime: number;
  readonly cacheHit: boolean;
}

// 可变规则执行结果（用于对象池）
export interface MutableRuleResult {
  ruleId: string;
  complexity: number;
  isExempted: boolean;
  shouldIncreaseNesting: boolean;
  
  // 详细信息
  reason: string;
  suggestions: Suggestion[];
  metadata: RuleMetadata;
  
  // 性能信息
  executionTime: number;
  cacheHit: boolean;
}

// 节点分析结果
export interface NodeAnalysis {
  readonly node: Node;
  readonly complexity: number;
  readonly appliedRules: RuleApplication[];
  readonly exemptions: Exemption[];
  
  // 子节点分析
  readonly children: NodeAnalysis[];
  readonly aggregatedComplexity: number;
  
  // 性能统计
  readonly analysisTime: number;
  readonly cacheUtilization: number;
}

// 可变节点分析结果（用于对象池）
export interface MutableNodeAnalysis {
  node: Node;
  complexity: number;
  appliedRules: RuleApplication[];
  exemptions: Exemption[];
  
  // 子节点分析
  children: NodeAnalysis[];
  aggregatedComplexity: number;
  
  // 性能统计
  analysisTime: number;
  cacheUtilization: number;
}

// 函数分析结果
export interface FunctionAnalysis {
  readonly functionName: string;
  readonly totalComplexity: number;
  readonly nodeAnalyses: NodeAnalysis[];
  readonly location: CodeLocation;
  readonly metrics: FunctionMetrics;
}

// 文件分析结果
export interface FileAnalysis {
  readonly filePath: string;
  readonly functions: FunctionAnalysis[];
  readonly totalComplexity: number;
  readonly averageComplexity: number;
  readonly analysisTime: number;
  readonly cacheHitRate: number;
}

// 引擎配置
export interface EngineConfig {
  // 规则配置
  rules?: {
    jsx?: JSXRuleConfig;
    logical?: LogicalRuleConfig;
    nesting?: NestingRuleConfig;
    custom?: Map<string, any>;
  };
  
  // 性能配置
  performance?: {
    maxConcurrency?: number;
    cacheSize?: number;
    streamingThreshold?: number;
    enableParallelExecution?: boolean;
  };
  
  // 输出配置
  output?: {
    includeMetrics?: boolean;
    detailLevel?: 'minimal' | 'standard' | 'verbose';
    enableDebugInfo?: boolean;
  };
  
  // 插件配置
  plugins?: {
    enabledPlugins?: string[];
    pluginPaths?: string[];
  };
}

// 解析后的配置
export interface ResolvedEngineConfig {
  readonly rules: {
    jsx: JSXRuleConfig;
    logical: LogicalRuleConfig;
    nesting: NestingRuleConfig;
    custom: Map<string, any>;
  };
  
  readonly performance: {
    maxConcurrency: number;
    cacheSize: number;
    streamingThreshold: number;
    enableParallelExecution: boolean;
  };
  
  readonly output: {
    includeMetrics: boolean;
    detailLevel: 'minimal' | 'standard' | 'verbose';
    enableDebugInfo: boolean;
  };
  
  readonly plugins: {
    enabledPlugins: string[];
    pluginPaths: string[];
  };
}

// JSX 规则配置
export interface JSXRuleConfig {
  enabled: boolean;
  
  // 豁免配置
  exemptions: {
    structuralNodes: boolean;          // JSX结构节点豁免
    attributeExpressions: boolean;     // 属性表达式豁免
    nullishCoalescing: boolean;        // 空值合并豁免
    simpleConditionals: boolean;       // 简单条件豁免
  };
  
  // 计分配置
  scoring: {
    conditionalRendering: boolean;     // 条件渲染计分
    eventHandlers: boolean;           // 事件处理器计分
    loopRendering: boolean;           // 循环渲染计分
    nestedComponents: boolean;        // 嵌套组件计分
  };
  
  // 高级配置
  advanced: {
    detectHookComplexity: boolean;    // Hook复杂度检测
    analyzeProps: boolean;            // Props分析
    trackContextUsage: boolean;       // Context使用跟踪
  };
}

// 逻辑规则配置
export interface LogicalRuleConfig {
  enabled: boolean;
  enableMixedLogicOperatorPenalty: boolean;
  recursionChainThreshold: number;
}

// 嵌套规则配置
export interface NestingRuleConfig {
  enabled: boolean;
  maxNestingLevel: number;
  penaltyPerLevel: number;
}

// JSX 分析模式
export type JSXAnalysisMode = 'strict' | 'standard' | 'lenient';

// 启用的规则集
export interface EnabledRules {
  core: string[];
  jsx: string[];
  plugins: string[];
}

// 分析指标
export interface AnalysisMetrics {
  totalNodes: number;
  processedNodes: number;
  cacheHits: number;
  cacheMisses: number;
  ruleExecutions: number;
  parallelExecutions: number;
  errors: number;
}

// 函数指标
export interface FunctionMetrics {
  nodeCount: number;
  nestingDepth: number;
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  executionTime: number;
}

// 建议
export interface Suggestion {
  type: 'refactor' | 'optimize' | 'warning' | 'info';
  message: string;
  codeExample?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

// 规则元数据
export interface RuleMetadata {
  nodeType: string;
  exemptionType?: string;
  pattern?: string;
  depth?: number;
  [key: string]: any;
}

// 规则应用记录
export interface RuleApplication {
  ruleId: string;
  ruleName: string;
  complexity: number;
  isExempted: boolean;
  reason: string;
  executionTime: number;
}

// 豁免记录
export interface Exemption {
  type: 'structural' | 'configuration' | 'annotation';
  reason: string;
  complexityReduced: number;
  appliedRule: string;
}

// 代码位置
export interface CodeLocation {
  line: number;
  column: number;
  endLine?: number;
  endColumn?: number;
}

// 插件相关
export interface LoadedPlugin {
  id: string;
  name: string;
  version: string;
  rules: Rule[];
  isActive: boolean;
}

// 引擎指标
export interface EngineMetrics {
  totalAnalysisTime: number;
  filesProcessed: number;
  functionsAnalyzed: number;
  rulesExecuted: number;
  cacheHitRate: number;
  parallelEfficiency: number;
  memoryUsage: NodeJS.MemoryUsage;
}

// 执行选项
export interface ExecutionOptions {
  maxConcurrency: number;
  timeout: number;
  enableProfiling: boolean;
  isolateErrors: boolean;
}

// 执行负载
export interface ExecutionLoad {
  activeWorkers: number;
  queuedTasks: number;
  totalCapacity: number;
  utilizationRate: number;
}

// 规则错误
export interface RuleError extends Error {
  ruleId: string;
  nodeType: string;
  isRecoverable: boolean;
  context?: AnalysisContext;
}

// 缓存失效原因
export type CacheInvalidationReason = 'config' | 'rule' | 'node' | 'manual';

// 变更类型
export type ChangeType = 'config' | 'rule' | 'node';

// 监控器配置选项
export interface MonitorOptions {
  /** 是否收集性能指标 */
  collectMetrics?: boolean;
  /** 性能阈值（毫秒） */
  performanceThreshold?: number;
  /** 指标收集间隔（毫秒） */
  metricsInterval?: number;
  /** 是否启用性能日志 */
  enablePerformanceLogging?: boolean;
}

// 插件配置
export interface PluginConfig {
  /** 插件路径 */
  path: string;
  /** 插件选项 */
  options?: Record<string, any>;
}

// 计算器配置选项（IoC重构的核心配置接口）
export interface CalculatorOptions {
  /**
   * 是否开启性能和吞吐量监控
   * @default false
   */
  enableMonitoring?: boolean;

  /**
   * 监控器的详细配置选项
   */
  monitorConfig?: MonitorOptions;

  /**
   * 要加载的插件列表
   */
  plugins?: PluginConfig[];

  /**
   * 要启用的规则ID列表，如果未提供则全部启用
   */
  enabledRuleIds?: string[];

  /**
   * 最大并发数
   * @default 4
   */
  maxConcurrency?: number;

  /**
   * 是否启用缓存
   * @default true
   */
  enableCaching?: boolean;

  /**
   * 调试模式
   * @default false
   */
  debugMode?: boolean;

  /**
   * 静默模式，不输出日志
   * @default false
   */
  quiet?: boolean;

  /**
   * 是否启用详细模式
   * @default false
   */
  enableDetails?: boolean;

  /**
   * 流式处理阈值
   * @default 1000
   */
  streamingThreshold?: number;

  /**
   * 缓存大小
   * @default 10000
   */
  cacheSize?: number;

  /**
   * 输出详细级别
   * @default 'standard'
   */
  detailLevel?: 'minimal' | 'standard' | 'verbose';

  /**
   * 是否包含性能指标
   * @default false
   */
  includeMetrics?: boolean;
  
  /**
   * 规则引擎配置选项
   */
  ruleEngineConfig?: {
    /**
     * 异步规则引擎的最大并发数
     * @default 10
     */
    maxRuleConcurrency?: number;
    /**
     * 是否启用规则缓存
     * @default true
     */
    enableRuleCaching?: boolean;
    /**
     * 规则调试模式
     * @default false
     */
    ruleDebugMode?: boolean;
  };
}

// 异步规则引擎接口声明（避免循环依赖）
export interface AsyncRuleEngine {
  analyzeNode(node: Node, context: AnalysisContext): Promise<NodeAnalysis>;
  analyzeFunction(func: Node): Promise<FunctionAnalysis>;
  analyzeFile(ast: Module): Promise<FileAnalysis>;
  analyzeFiles(filePaths: string[]): Promise<Map<string, FileAnalysis>>;
  analyzeCode(code: string, filePath?: string): Promise<FileAnalysis>;
  registerRule(rule: Rule, quiet?: boolean): void;
  unregisterRule(ruleId: string): void;
  getMetrics(): EngineMetrics;
  clearCache(): void;
  
  // 配置管理
  updateConfig(newConfig: Partial<ResolvedEngineConfig>): void;
  getConfig(): ResolvedEngineConfig;
  
  // 状态查询
  getRuleStatistics(): any;
  getExecutionLoad(): any;
  
  // 性能优化
  preWarmCache(nodes: Node[]): Promise<void>;
  
  // 插件管理
  loadPlugin(pluginPath: string, options?: any): Promise<void>;
  getAllRules(): Rule[];
  enableRule(ruleId: string): void;
  disableRule(ruleId: string): void;
  
  // 生命周期
  shutdown(): void;
  dispose?(): Promise<void>;
}

// 默认配置
export const DEFAULT_ENGINE_CONFIG: ResolvedEngineConfig = {
  rules: {
    jsx: {
      enabled: true,
      exemptions: {
        structuralNodes: true,
        attributeExpressions: true,
        nullishCoalescing: true,
        simpleConditionals: true,
      },
      scoring: {
        conditionalRendering: true,
        eventHandlers: true,
        loopRendering: true,
        nestedComponents: true,
      },
      advanced: {
        detectHookComplexity: false,
        analyzeProps: false,
        trackContextUsage: false,
      },
    },
    logical: {
      enabled: true,
      enableMixedLogicOperatorPenalty: false,
      recursionChainThreshold: 10,
    },
    nesting: {
      enabled: true,
      maxNestingLevel: 10,
      penaltyPerLevel: 1,
    },
    custom: new Map(),
  },
  performance: {
    maxConcurrency: 4,
    cacheSize: 10000,
    streamingThreshold: 1000,
    enableParallelExecution: true,
  },
  output: {
    includeMetrics: false,
    detailLevel: 'standard',
    enableDebugInfo: false,
  },
  plugins: {
    enabledPlugins: [],
    pluginPaths: [],
  },
};

// 重新导出其他模块的类型
export type { PerformanceMonitorConfig } from './performance-monitor';