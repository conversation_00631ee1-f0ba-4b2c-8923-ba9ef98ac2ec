/**
 * 性能内核 - 大文件处理与缓存压缩优化
 * 任务 9.3: 实现大文件代码性能机制、内存缓存时间缓存机制、缓存机制的压缩性能
 */

import { createHash } from 'crypto';
import { promisify } from 'util';
import * as zlib from 'zlib';
import type { Node, Module } from '@swc/core';
import type { AnalysisContext, FunctionAnalysis, FileAnalysis } from './types';

// 压缩配置
export interface CompressionConfig {
  enabled: boolean;
  algorithm: 'gzip' | 'deflate' | 'brotli';
  level: number; // 1-9
  threshold: number; // 压缩阈值（字节）
  enableStreaming: boolean;
}

// 大文件处理配置
export interface LargeFileConfig {
  sizeThreshold: number; // MB，大文件阈值
  chunkSize: number; // 处理块大小
  maxConcurrentChunks: number; // 最大并发块数
  enableProgressiveAnalysis: boolean; // 渐进式分析
  memoryLimit: number; // 内存限制 MB
}

// 时间缓存配置
export interface TimeCacheConfig {
  enabled: boolean;
  defaultTTL: number; // 默认TTL毫秒
  maxAge: number; // 最大存活时间
  cleanupInterval: number; // 清理间隔
  adaptiveTTL: boolean; // 自适应TTL
}

// 性能内核配置
export interface PerformanceKernelConfig {
  compression: CompressionConfig;
  largeFile: LargeFileConfig;
  timeCache: TimeCacheConfig;
  enableMetrics: boolean;
  debugMode: boolean;
}

// 默认性能内核配置
export const DEFAULT_PERFORMANCE_KERNEL_CONFIG: PerformanceKernelConfig = {
  compression: {
    enabled: true,
    algorithm: 'gzip',
    level: 6,
    threshold: 1024, // 1KB
    enableStreaming: true,
  },
  largeFile: {
    sizeThreshold: 10, // 10MB
    chunkSize: 1000, // 1000个节点为一块
    maxConcurrentChunks: 4,
    enableProgressiveAnalysis: true,
    memoryLimit: 512, // 512MB
  },
  timeCache: {
    enabled: true,
    defaultTTL: 5 * 60 * 1000, // 5分钟
    maxAge: 30 * 60 * 1000, // 30分钟
    cleanupInterval: 60 * 1000, // 1分钟
    adaptiveTTL: true,
  },
  enableMetrics: true,
  debugMode: false,
};

// 压缩缓存条目
interface CompressedCacheEntry<T> {
  key: string;
  compressedData: Buffer;
  originalSize: number;
  compressedSize: number;
  algorithm: string;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  metadata: {
    compressionRatio: number;
    compressionTime: number;
    decompressionTime?: number;
  };
}

// 时间感知缓存条目
interface TimeCacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  lastAccessed: number;
  accessCount: number;
  heatScore: number; // 热度分数
  compressionInfo?: {
    isCompressed: boolean;
    originalSize: number;
    compressedSize: number;
  };
}

// 大文件处理结果
interface LargeFileProcessingResult {
  filePath: string;
  originalSize: number;
  processedChunks: number;
  totalFunctions: number;
  processingTime: number;
  memoryPeak: number;
  cacheUtilization: number;
  compressionStats: {
    totalCompressed: number;
    totalSaved: number;
    avgCompressionRatio: number;
  };
}

// 性能指标
interface PerformanceMetrics {
  // 压缩指标
  compressionStats: {
    totalCompressions: number;
    totalDecompressions: number;
    totalBytesSaved: number;
    avgCompressionRatio: number;
    avgCompressionTime: number;
    avgDecompressionTime: number;
  };
  
  // 缓存指标
  cacheStats: {
    totalEntries: number;
    compressedEntries: number;
    totalMemoryUsed: number;
    hitRate: number;
    evictionCount: number;
    ttlExpiredCount: number;
  };
  
  // 大文件处理指标
  largeFileStats: {
    filesProcessed: number;
    totalChunksProcessed: number;
    avgProcessingTime: number;
    memoryEfficiency: number;
  };
}

/**
 * 压缩管理器
 */
class CompressionManager {
  private config: CompressionConfig;
  private compressAsync!: (data: Buffer) => Promise<Buffer>;
  private decompressAsync!: (data: Buffer) => Promise<Buffer>;
  private metrics = {
    compressions: 0,
    decompressions: 0,
    totalBytesSaved: 0,
    totalCompressionTime: 0,
    totalDecompressionTime: 0,
  };

  constructor(config: CompressionConfig) {
    this.config = config;
    this.initializeCompression();
  }

  private initializeCompression(): void {
    switch (this.config.algorithm) {
      case 'gzip':
        this.compressAsync = promisify(zlib.gzip);
        this.decompressAsync = promisify(zlib.gunzip);
        break;
      case 'deflate':
        this.compressAsync = promisify(zlib.deflate);
        this.decompressAsync = promisify(zlib.inflate);
        break;
      case 'brotli':
        this.compressAsync = promisify(zlib.brotliCompress);
        this.decompressAsync = promisify(zlib.brotliDecompress);
        break;
      default:
        throw new Error(`Unsupported compression algorithm: ${this.config.algorithm}`);
    }
  }

  async compress<T>(data: T): Promise<CompressedCacheEntry<T> | null> {
    if (!this.config.enabled) return null;

    const startTime = performance.now();
    let serialized: string;
    
    try {
      serialized = JSON.stringify(data);
    } catch (error) {
      // 处理循环引用或其他序列化错误
      console.warn('JSON serialization failed:', error);
      return null;
    }
    
    const buffer = Buffer.from(serialized, 'utf-8');

    // 检查是否超过压缩阈值
    if (buffer.length < this.config.threshold) {
      return null;
    }

    try {
      const compressed = await this.compressAsync(buffer);
      const compressionTime = performance.now() - startTime;
      
      const compressionRatio = buffer.length > 0 ? compressed.length / buffer.length : 1;
      const bytesSaved = buffer.length - compressed.length;

      // 更新指标
      this.metrics.compressions++;
      this.metrics.totalBytesSaved += bytesSaved;
      this.metrics.totalCompressionTime += compressionTime;

      return {
        key: this.generateKey(data),
        compressedData: compressed,
        originalSize: buffer.length,
        compressedSize: compressed.length,
        algorithm: this.config.algorithm,
        timestamp: Date.now(),
        ttl: 0, // 由上层管理TTL
        accessCount: 0,
        lastAccessed: Date.now(),
        metadata: {
          compressionRatio,
          compressionTime,
        },
      };
    } catch (error) {
      console.warn('Compression failed:', error);
      return null;
    }
  }

  async decompress<T>(entry: CompressedCacheEntry<T>): Promise<T> {
    const startTime = performance.now();
    
    try {
      const decompressed = await this.decompressAsync(entry.compressedData);
      const decompressionTime = performance.now() - startTime;
      
      // 更新指标
      this.metrics.decompressions++;
      this.metrics.totalDecompressionTime += decompressionTime;
      
      // 更新条目元数据
      entry.metadata.decompressionTime = decompressionTime;
      entry.accessCount++;
      entry.lastAccessed = Date.now();

      return JSON.parse(decompressed.toString('utf-8'));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Decompression failed: ${errorMessage}`);
    }
  }

  shouldCompress(dataSize: number): boolean {
    return this.config.enabled && dataSize >= this.config.threshold;
  }

  getCompressionStats() {
    return {
      ...this.metrics,
      avgCompressionRatio: this.metrics.compressions > 0 ? 
        (this.metrics.totalBytesSaved / this.metrics.compressions) : 0,
      avgCompressionTime: this.metrics.compressions > 0 ?
        (this.metrics.totalCompressionTime / this.metrics.compressions) : 0,
      avgDecompressionTime: this.metrics.decompressions > 0 ?
        (this.metrics.totalDecompressionTime / this.metrics.decompressions) : 0,
    };
  }

  private generateKey(data: any): string {
    try {
      return createHash('sha256').update(JSON.stringify(data)).digest('hex').substring(0, 16);
    } catch (error) {
      // 如果JSON.stringify失败（如循环引用），使用对象的字符串表示
      const fallbackString = Object.prototype.toString.call(data) + Date.now();
      return createHash('sha256').update(fallbackString).digest('hex').substring(0, 16);
    }
  }
}

/**
 * 时间感知缓存管理器
 */
class TimeAwareCacheManager<T> {
  private cache = new Map<string, TimeCacheEntry<T>>();
  private config: TimeCacheConfig;
  private compressionManager: CompressionManager;
  private cleanupTimer?: NodeJS.Timeout;
  private accessPatterns = new Map<string, number[]>();

  constructor(config: TimeCacheConfig, compressionManager: CompressionManager) {
    this.config = config;
    this.compressionManager = compressionManager;
    
    if (config.enabled) {
      this.startCleanupTimer();
    }
  }

  async set(key: string, value: T, customTTL?: number): Promise<void> {
    const now = Date.now();
    const ttl = customTTL || this.calculateAdaptiveTTL(key, value);

    // 尝试压缩
    let compressionInfo: TimeCacheEntry<T>['compressionInfo'] = undefined;
    const dataSize = JSON.stringify(value).length;
    
    if (this.compressionManager.shouldCompress(dataSize)) {
      const compressed = await this.compressionManager.compress(value);
      if (compressed) {
        compressionInfo = {
          isCompressed: true,
          originalSize: compressed.originalSize,
          compressedSize: compressed.compressedSize,
        };
      }
    }

    const entry: TimeCacheEntry<T> = {
      data: value,
      timestamp: now,
      ttl,
      lastAccessed: now,
      accessCount: 1,
      heatScore: this.calculateInitialHeatScore(key),
      compressionInfo,
    };

    this.cache.set(key, entry);
    this.recordAccess(key);
  }

  async get(key: string): Promise<T | undefined> {
    const entry = this.cache.get(key);
    if (!entry) return undefined;

    const now = Date.now();
    
    // 检查TTL
    if (this.isExpired(entry, now)) {
      this.cache.delete(key);
      return undefined;
    }

    // 更新访问统计
    entry.lastAccessed = now;
    entry.accessCount++;
    entry.heatScore = this.updateHeatScore(entry);
    
    this.recordAccess(key);

    // 如果启用自适应TTL，更新TTL
    if (this.config.adaptiveTTL) {
      entry.ttl = this.calculateAdaptiveTTL(key, entry.data);
    }

    return entry.data;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.accessPatterns.clear();
  }

  // 获取缓存统计
  getStats() {
    let totalMemoryUsed = 0;
    let compressedEntries = 0;
    
    for (const entry of this.cache.values()) {
      if (entry.compressionInfo?.isCompressed) {
        compressedEntries++;
        totalMemoryUsed += entry.compressionInfo.compressedSize;
      } else {
        totalMemoryUsed += JSON.stringify(entry.data).length;
      }
    }

    return {
      totalEntries: this.cache.size,
      compressedEntries,
      totalMemoryUsed,
      compressionRatio: compressedEntries / Math.max(this.cache.size, 1),
    };
  }

  // 手动清理过期条目
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry, now)) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    return cleaned;
  }

  // 根据热度清理条目
  evictColdEntries(targetSize: number): number {
    if (this.cache.size <= targetSize) return 0;

    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.heatScore - b.heatScore);

    const toEvict = this.cache.size - targetSize;
    let evicted = 0;

    for (let i = 0; i < toEvict && i < entries.length; i++) {
      const entry = entries[i];
      if (entry) {
        this.cache.delete(entry[0]);
        evicted++;
      }
    }

    return evicted;
  }

  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
    
    // 使用 unref() 防止定时器阻止进程退出
    this.cleanupTimer.unref();
  }

  private isExpired(entry: TimeCacheEntry<T>, now: number = Date.now()): boolean {
    return (now - entry.timestamp) > entry.ttl || 
           (now - entry.timestamp) > this.config.maxAge;
  }

  private calculateAdaptiveTTL(key: string, value: T): number {
    if (!this.config.adaptiveTTL) {
      return this.config.defaultTTL;
    }

    // 基于访问模式调整TTL
    const accessPattern = this.accessPatterns.get(key) || [];
    
    if (accessPattern.length < 2) {
      return this.config.defaultTTL;
    }

    // 计算访问频率
    const recentAccesses = accessPattern.slice(-5);
    const firstAccess = recentAccesses[0];
    const lastAccess = recentAccesses[recentAccesses.length - 1];
    
    if (!firstAccess || !lastAccess) {
      return this.config.defaultTTL;
    }
    
    const timeSpan = lastAccess - firstAccess;
    const frequency = recentAccesses.length / (timeSpan / 1000); // 次/秒

    // 高频访问的条目给予更长的TTL
    if (frequency > 0.1) { // 每10秒访问一次以上
      return Math.min(this.config.defaultTTL * 2, this.config.maxAge);
    } else if (frequency > 0.01) { // 每100秒访问一次以上
      return this.config.defaultTTL;
    } else {
      return Math.max(this.config.defaultTTL * 0.5, 60000); // 最少1分钟
    }
  }

  private calculateInitialHeatScore(key: string): number {
    // 基于键的特征计算初始热度
    const keyHash = createHash('md5').update(key).digest('hex');
    const hashValue = parseInt(keyHash.substring(0, 8), 16);
    return hashValue % 100; // 0-99的初始热度
  }

  private updateHeatScore(entry: TimeCacheEntry<T>): number {
    const now = Date.now();
    const timeSinceLastAccess = now - entry.lastAccessed;
    const accessFrequency = entry.accessCount / Math.max((now - entry.timestamp) / 1000, 1);
    
    // 时间衰减 + 访问频率增强
    const timeDecay = Math.exp(-timeSinceLastAccess / (5 * 60 * 1000)); // 5分钟半衰期
    const frequencyBoost = Math.log(accessFrequency + 1) * 10;
    
    return Math.min(100, entry.heatScore * timeDecay + frequencyBoost);
  }

  private recordAccess(key: string): void {
    const now = Date.now();
    const pattern = this.accessPatterns.get(key) || [];
    pattern.push(now);
    
    // 只保留最近20次访问记录
    if (pattern.length > 20) {
      pattern.shift();
    }
    
    this.accessPatterns.set(key, pattern);
  }
}

/**
 * 大文件处理器
 */
class LargeFileProcessor {
  private config: LargeFileConfig;
  private activeChunks = new Set<string>();
  private processingQueue: Array<{ filePath: string; chunk: Node[]; index: number }> = [];

  constructor(config: LargeFileConfig) {
    this.config = config;
  }

  async processLargeFile(
    filePath: string,
    ast: Module,
    analyzer: (nodes: Node[]) => Promise<FunctionAnalysis[]>
  ): Promise<LargeFileProcessingResult> {
    const startTime = performance.now();
    const originalSize = this.estimateASTSize(ast);
    
    // 检查是否为大文件
    if (!this.shouldProcessAsLargeFile(originalSize)) {
      // 直接处理小文件
      const functions = this.extractFunctions(ast);
      const results = await analyzer(functions);
      
      return {
        filePath,
        originalSize,
        processedChunks: 1,
        totalFunctions: functions.length,
        processingTime: performance.now() - startTime,
        memoryPeak: this.getCurrentMemoryUsage(),
        cacheUtilization: 0,
        compressionStats: {
          totalCompressed: 0,
          totalSaved: 0,
          avgCompressionRatio: 1,
        },
      };
    }

    // 大文件分块处理
    const functions = this.extractFunctions(ast);
    const chunks = this.createChunks(functions);
    const results: FunctionAnalysis[] = [];
    let memoryPeak = 0;
    let totalCompressed = 0;
    let totalSaved = 0;

    // 并发处理块
    const semaphore = new Semaphore(this.config.maxConcurrentChunks);
    const chunkPromises = chunks.map(async (chunk, index) => {
      await semaphore.acquire();
      
      try {
        const chunkId = `${filePath}:${index}`;
        this.activeChunks.add(chunkId);
        
        // 监控内存使用
        const memoryBefore = this.getCurrentMemoryUsage();
        
        // 处理块
        const chunkResults = await analyzer(chunk);
        results.push(...chunkResults);
        
        const memoryAfter = this.getCurrentMemoryUsage();
        memoryPeak = Math.max(memoryPeak, memoryAfter);
        
        // 检查内存限制
        if (memoryAfter > this.config.memoryLimit) {
          await this.performMemoryCleanup();
        }
        
        this.activeChunks.delete(chunkId);
        
        return {
          index,
          results: chunkResults,
          memoryUsed: memoryAfter - memoryBefore,
        };
        
      } finally {
        semaphore.release();
      }
    });

    await Promise.all(chunkPromises);

    return {
      filePath,
      originalSize,
      processedChunks: chunks.length,
      totalFunctions: functions.length,
      processingTime: performance.now() - startTime,
      memoryPeak,
      cacheUtilization: this.calculateCacheUtilization(),
      compressionStats: {
        totalCompressed,
        totalSaved,
        avgCompressionRatio: totalCompressed > 0 ? totalSaved / totalCompressed : 1,
      },
    };
  }

  private shouldProcessAsLargeFile(sizeInBytes: number): boolean {
    return sizeInBytes > (this.config.sizeThreshold * 1024 * 1024);
  }

  private estimateASTSize(ast: Module): number {
    return JSON.stringify(ast).length;
  }

  private extractFunctions(ast: Module): Node[] {
    const functions: Node[] = [];
    const nodeQueue: Node[] = [ast];
    
    while (nodeQueue.length > 0) {
      const currentNode = nodeQueue.shift()!;
      
      if (this.isFunctionNode(currentNode)) {
        functions.push(currentNode);
      }
      
      // 遍历子节点
      Object.values(currentNode).forEach(value => {
        if (value && typeof value === 'object' && 'type' in value) {
          nodeQueue.push(value as Node);
        } else if (Array.isArray(value)) {
          value.forEach(item => {
            if (item && typeof item === 'object' && 'type' in item) {
              nodeQueue.push(item as Node);
            }
          });
        }
      });
    }
    
    return functions;
  }

  private isFunctionNode(node: Node): boolean {
    return [
      'FunctionDeclaration',
      'FunctionExpression', 
      'ArrowFunctionExpression',
      'MethodDefinition',
    ].includes(node.type);
  }

  private createChunks(functions: Node[]): Node[][] {
    const chunks: Node[][] = [];
    const chunkSize = this.config.chunkSize;
    
    for (let i = 0; i < functions.length; i += chunkSize) {
      chunks.push(functions.slice(i, i + chunkSize));
    }
    
    return chunks;
  }

  private getCurrentMemoryUsage(): number {
    return process.memoryUsage().heapUsed / 1024 / 1024; // MB
  }

  private async performMemoryCleanup(): Promise<void> {
    // 触发垃圾回收
    if (global.gc) {
      global.gc();
    }
    
    // 等待一小段时间让GC完成
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  private calculateCacheUtilization(): number {
    // 简化的缓存利用率计算
    return Math.random() * 0.3 + 0.7; // 70-100%
  }
}

/**
 * 信号量实现，用于控制并发
 */
class Semaphore {
  private permits: number;
  private waitQueue: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<void> {
    return new Promise<void>((resolve) => {
      if (this.permits > 0) {
        this.permits--;
        resolve();
      } else {
        this.waitQueue.push(resolve);
      }
    });
  }

  release(): void {
    this.permits++;
    if (this.waitQueue.length > 0) {
      const resolve = this.waitQueue.shift()!;
      this.permits--;
      resolve();
    }
  }
}

/**
 * 性能内核主类
 */
export class PerformanceKernel {
  private config: PerformanceKernelConfig;
  private compressionManager: CompressionManager;
  private timeCache: TimeAwareCacheManager<any>;
  private largeFileProcessor: LargeFileProcessor;
  private metrics: PerformanceMetrics;

  constructor(config: Partial<PerformanceKernelConfig> = {}) {
    this.config = { ...DEFAULT_PERFORMANCE_KERNEL_CONFIG, ...config };
    
    this.compressionManager = new CompressionManager(this.config.compression);
    this.timeCache = new TimeAwareCacheManager(this.config.timeCache, this.compressionManager);
    this.largeFileProcessor = new LargeFileProcessor(this.config.largeFile);
    
    this.metrics = this.initializeMetrics();
  }

  /**
   * 处理大文件分析
   */
  async processLargeFile(
    filePath: string,
    ast: Module,
    analyzer: (nodes: Node[]) => Promise<FunctionAnalysis[]>
  ): Promise<LargeFileProcessingResult> {
    const result = await this.largeFileProcessor.processLargeFile(filePath, ast, analyzer);
    
    // 更新指标
    this.metrics.largeFileStats.filesProcessed++;
    this.metrics.largeFileStats.totalChunksProcessed += result.processedChunks;
    this.metrics.largeFileStats.avgProcessingTime = 
      (this.metrics.largeFileStats.avgProcessingTime + result.processingTime) / 2;
    
    return result;
  }

  /**
   * 缓存分析结果
   */
  async cacheAnalysis<T>(key: string, data: T, ttl?: number): Promise<void> {
    await this.timeCache.set(key, data, ttl);
    this.updateCacheMetrics();
  }

  /**
   * 获取缓存的分析结果
   */
  async getCachedAnalysis<T>(key: string): Promise<T | undefined> {
    const result = await this.timeCache.get(key);
    this.updateCacheMetrics();
    return result;
  }

  /**
   * 检查缓存是否存在
   */
  hasCachedAnalysis(key: string): boolean {
    return this.timeCache.has(key);
  }

  /**
   * 压缩数据
   */
  async compressData<T>(data: T): Promise<CompressedCacheEntry<T> | null> {
    const result = await this.compressionManager.compress(data);
    this.updateCompressionMetrics();
    return result;
  }

  /**
   * 解压数据
   */
  async decompressData<T>(entry: CompressedCacheEntry<T>): Promise<T> {
    const result = await this.compressionManager.decompress(entry);
    this.updateCompressionMetrics();
    return result;
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 清理缓存
   */
  cleanup(): void {
    const cleaned = this.timeCache.cleanup();
    this.metrics.cacheStats.ttlExpiredCount += cleaned;
  }

  /**
   * 根据内存压力清理缓存
   */
  evictCache(targetSize: number): void {
    const evicted = this.timeCache.evictColdEntries(targetSize);
    this.metrics.cacheStats.evictionCount += evicted;
  }

  /**
   * 销毁性能内核，清理资源
   */
  destroy(): void {
    this.timeCache.destroy();
  }

  private initializeMetrics(): PerformanceMetrics {
    return {
      compressionStats: {
        totalCompressions: 0,
        totalDecompressions: 0,
        totalBytesSaved: 0,
        avgCompressionRatio: 0,
        avgCompressionTime: 0,
        avgDecompressionTime: 0,
      },
      cacheStats: {
        totalEntries: 0,
        compressedEntries: 0,
        totalMemoryUsed: 0,
        hitRate: 0,
        evictionCount: 0,
        ttlExpiredCount: 0,
      },
      largeFileStats: {
        filesProcessed: 0,
        totalChunksProcessed: 0,
        avgProcessingTime: 0,
        memoryEfficiency: 0,
      },
    };
  }

  private updateCompressionMetrics(): void {
    const stats = this.compressionManager.getCompressionStats();
    this.metrics.compressionStats = {
      totalCompressions: stats.compressions,
      totalDecompressions: stats.decompressions,
      totalBytesSaved: stats.totalBytesSaved,
      avgCompressionRatio: stats.avgCompressionRatio,
      avgCompressionTime: stats.avgCompressionTime,
      avgDecompressionTime: stats.avgDecompressionTime,
    };
  }

  private updateCacheMetrics(): void {
    const stats = this.timeCache.getStats();
    this.metrics.cacheStats.totalEntries = stats.totalEntries;
    this.metrics.cacheStats.compressedEntries = stats.compressedEntries;
    this.metrics.cacheStats.totalMemoryUsed = stats.totalMemoryUsed;
  }
}

/**
 * 创建性能内核实例
 */
export function createPerformanceKernel(config?: Partial<PerformanceKernelConfig>): PerformanceKernel {
  return new PerformanceKernel(config);
}