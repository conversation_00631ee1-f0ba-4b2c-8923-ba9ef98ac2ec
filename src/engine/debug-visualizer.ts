/**
 * 调试可视化输出工具
 * 生成HTML格式的调试报告，包含交互式图表和详细分析
 */

import type { 
  DebugEvent, 
  VisualTrace, 
  DiagnosticResult, 
  ExecutionSnapshot,
  DebugEventType 
} from './debug-system';

// 可视化配置
export interface VisualizationConfig {
  // 图表配置
  charts: {
    enableTimeline: boolean;
    enableFlowGraph: boolean;
    enablePerformanceBars: boolean;
    enableHeatMap: boolean;
  };
  
  // 颜色主题
  theme: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    background: string;
    text: string;
  };
  
  // 输出配置
  output: {
    includeRawData: boolean;
    includeSourceCode: boolean;
    enableInteractivity: boolean;
    compressionLevel: 'none' | 'basic' | 'aggressive';
  };
}

// 默认可视化配置
const DEFAULT_VIS_CONFIG: VisualizationConfig = {
  charts: {
    enableTimeline: true,
    enableFlowGraph: true,
    enablePerformanceBars: true,
    enableHeatMap: true,
  },
  theme: {
    primary: '#3498db',
    secondary: '#2ecc71',
    success: '#27ae60',
    warning: '#f39c12',
    error: '#e74c3c',
    background: '#ffffff',
    text: '#2c3e50',
  },
  output: {
    includeRawData: true,
    includeSourceCode: false,
    enableInteractivity: true,
    compressionLevel: 'basic',
  },
};

/**
 * 调试可视化生成器
 */
export class DebugVisualizer {
  private config: VisualizationConfig;

  constructor(config: Partial<VisualizationConfig> = {}) {
    this.config = { 
      ...DEFAULT_VIS_CONFIG, 
      ...config,
      charts: { ...DEFAULT_VIS_CONFIG.charts, ...config.charts },
      theme: { ...DEFAULT_VIS_CONFIG.theme, ...config.theme },
      output: { ...DEFAULT_VIS_CONFIG.output, ...config.output },
    };
  }

  /**
   * 生成完整的HTML调试报告
   */
  generateHTMLReport(
    events: DebugEvent[],
    visualTrace: VisualTrace,
    diagnostics: DiagnosticResult[],
    snapshots: ExecutionSnapshot[]
  ): string {
    const reportData = this.processReportData(events, visualTrace, diagnostics, snapshots);
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cognitive Complexity Debug Report - ${visualTrace.sessionId}</title>
    <style>${this.generateCSS()}</style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
</head>
<body>
    <div class="container">
        ${this.generateHeader(visualTrace)}
        ${this.generateSummary(reportData)}
        ${this.generateDiagnosticsSection(diagnostics)}
        ${this.generateTimelineSection(events)}
        ${this.generatePerformanceSection(reportData)}
        ${this.generateFlowGraphSection(visualTrace)}
        ${this.generateEventsSection(events)}
        ${this.config.output.includeRawData ? this.generateRawDataSection(events, visualTrace, diagnostics) : ''}
    </div>
    <script>${this.generateJavaScript(reportData)}</script>
</body>
</html>`;
  }

  /**
   * 生成简化的文本报告
   */
  generateTextReport(
    events: DebugEvent[],
    visualTrace: VisualTrace,
    diagnostics: DiagnosticResult[]
  ): string {
    const report: string[] = [];
    
    // 报告头部
    report.push('='.repeat(80));
    report.push('COGNITIVE COMPLEXITY DEBUG REPORT');
    report.push('='.repeat(80));
    report.push(`Session ID: ${visualTrace.sessionId}`);
    report.push(`Duration: ${((visualTrace.endTime || performance.now()) - visualTrace.startTime).toFixed(2)}ms`);
    report.push(`Total Events: ${events.length}`);
    report.push('');
    
    // 事件统计
    report.push('EVENT STATISTICS');
    report.push('-'.repeat(40));
    const eventStats = this.getEventStatistics(events);
    Object.entries(eventStats).forEach(([type, count]) => {
      report.push(`${type.padEnd(20)}: ${count}`);
    });
    report.push('');
    
    // 性能热点
    if (visualTrace.hotspots.length > 0) {
      report.push('PERFORMANCE HOTSPOTS');
      report.push('-'.repeat(40));
      visualTrace.hotspots.slice(0, 10).forEach((hotspot, index) => {
        report.push(`${index + 1}. ${hotspot.identifier} (${hotspot.type})`);
        report.push(`   Total Time: ${hotspot.totalTime.toFixed(2)}ms`);
        report.push(`   Frequency: ${hotspot.frequency.toFixed(2)} calls/s`);
        report.push(`   Avg Time: ${hotspot.averageTime.toFixed(2)}ms`);
        report.push('');
      });
    }
    
    // 诊断结果
    if (diagnostics.length > 0) {
      report.push('DIAGNOSTIC RESULTS');
      report.push('-'.repeat(40));
      diagnostics.forEach((diagnostic, index) => {
        const severityIcon = {
          info: 'ℹ',
          warning: '⚠',
          error: '❌',
          critical: '🚨'
        }[diagnostic.severity];
        
        report.push(`${index + 1}. ${severityIcon} ${diagnostic.title}`);
        report.push(`   Severity: ${diagnostic.severity.toUpperCase()}`);
        report.push(`   Category: ${diagnostic.category}`);
        report.push(`   Description: ${diagnostic.description}`);
        
        if (diagnostic.solutions.length > 0) {
          report.push('   Solutions:');
          diagnostic.solutions.slice(0, 2).forEach((solution, sIndex) => {
            report.push(`     ${sIndex + 1}. ${solution.title} (${solution.effort} effort, ${solution.risk} risk)`);
          });
        }
        report.push('');
      });
    }
    
    // 推荐的下一步行动
    report.push('RECOMMENDED ACTIONS');
    report.push('-'.repeat(40));
    const actions = this.generateRecommendedActions(diagnostics);
    actions.forEach((action, index) => {
      report.push(`${index + 1}. ${action}`);
    });
    
    report.push('');
    report.push('='.repeat(80));
    report.push('End of Report');
    report.push('='.repeat(80));
    
    return report.join('\n');
  }

  /**
   * 生成JSON格式的调试数据
   */
  generateJSONReport(
    events: DebugEvent[],
    visualTrace: VisualTrace,
    diagnostics: DiagnosticResult[],
    snapshots: ExecutionSnapshot[]
  ): string {
    const reportData = {
      metadata: {
        sessionId: visualTrace.sessionId,
        generatedAt: new Date().toISOString(),
        duration: (visualTrace.endTime || performance.now()) - visualTrace.startTime,
        totalEvents: events.length,
        version: '1.0.0',
      },
      summary: this.processReportData(events, visualTrace, diagnostics, snapshots),
      events: this.config.output.includeRawData ? events : [],
      visualTrace,
      diagnostics,
      snapshots: this.config.output.includeRawData ? snapshots : [],
    };
    
    return JSON.stringify(reportData, null, 2);
  }

  private processReportData(
    events: DebugEvent[],
    visualTrace: VisualTrace,
    diagnostics: DiagnosticResult[],
    snapshots: ExecutionSnapshot[]
  ) {
    return {
      overview: {
        duration: (visualTrace.endTime || performance.now()) - visualTrace.startTime,
        totalEvents: events.length,
        totalDiagnostics: diagnostics.length,
        totalSnapshots: snapshots.length,
        filesProcessed: visualTrace.files.length,
      },
      eventStatistics: this.getEventStatistics(events),
      performanceMetrics: this.getPerformanceMetrics(events),
      errorSummary: this.getErrorSummary(events),
      cacheStatistics: this.getCacheStatistics(events),
      ruleStatistics: this.getRuleStatistics(events),
      diagnosticsSummary: this.getDiagnosticsSummary(diagnostics),
    };
  }

  private getEventStatistics(events: DebugEvent[]): Record<string, number> {
    const stats: Record<string, number> = {};
    events.forEach(event => {
      stats[event.type] = (stats[event.type] || 0) + 1;
    });
    return stats;
  }

  private getPerformanceMetrics(events: DebugEvent[]) {
    const ruleEvents = events.filter(e => e.type === 'rule_end' && e.performance?.duration);
    const ruleTimes = ruleEvents.map(e => e.performance!.duration!);
    
    return {
      totalRuleExecutions: ruleEvents.length,
      averageRuleTime: ruleTimes.length > 0 ? ruleTimes.reduce((a, b) => a + b, 0) / ruleTimes.length : 0,
      minRuleTime: ruleTimes.length > 0 ? Math.min(...ruleTimes) : 0,
      maxRuleTime: ruleTimes.length > 0 ? Math.max(...ruleTimes) : 0,
      totalExecutionTime: ruleTimes.reduce((a, b) => a + b, 0),
    };
  }

  private getErrorSummary(events: DebugEvent[]) {
    const errors = events.filter(e => e.type === 'error');
    const warnings = events.filter(e => e.type === 'warning');
    
    return {
      errorCount: errors.length,
      warningCount: warnings.length,
      errorTypes: this.groupBy(errors, e => e.data?.error?.name || 'Unknown'),
      commonErrors: this.getTopErrors(errors, 5),
    };
  }

  private getCacheStatistics(events: DebugEvent[]) {
    const cacheEvents = events.filter(e => e.type === 'cache_hit' || e.type === 'cache_miss');
    const hits = events.filter(e => e.type === 'cache_hit').length;
    const misses = events.filter(e => e.type === 'cache_miss').length;
    
    return {
      totalAccesses: cacheEvents.length,
      hits,
      misses,
      hitRate: cacheEvents.length > 0 ? hits / cacheEvents.length : 0,
    };
  }

  private getRuleStatistics(events: DebugEvent[]) {
    const ruleEvents = events.filter(e => e.context.ruleId);
    const ruleStats: Record<string, any> = {};
    
    ruleEvents.forEach(event => {
      const ruleId = event.context.ruleId!;
      if (!ruleStats[ruleId]) {
        ruleStats[ruleId] = {
          executions: 0,
          totalTime: 0,
          errors: 0,
        };
      }
      
      ruleStats[ruleId].executions++;
      
      if (event.type === 'rule_end' && event.performance?.duration) {
        ruleStats[ruleId].totalTime += event.performance.duration;
      }
      
      if (event.type === 'error') {
        ruleStats[ruleId].errors++;
      }
    });
    
    // 计算平均时间
    Object.values(ruleStats).forEach((stats: any) => {
      stats.averageTime = stats.executions > 0 ? stats.totalTime / stats.executions : 0;
    });
    
    return ruleStats;
  }

  private getDiagnosticsSummary(diagnostics: DiagnosticResult[]) {
    const bySeverity = this.groupBy(diagnostics, d => d.severity);
    const byCategory = this.groupBy(diagnostics, d => d.category);
    
    return {
      total: diagnostics.length,
      bySeverity,
      byCategory,
      criticalIssues: diagnostics.filter(d => d.severity === 'critical').length,
      autoFixAvailable: diagnostics.filter(d => 
        d.solutions.some(s => s.autoFixAvailable)
      ).length,
    };
  }

  private generateHeader(visualTrace: VisualTrace): string {
    const duration = ((visualTrace.endTime || performance.now()) - visualTrace.startTime) / 1000;
    
    return `
    <header class="header">
        <h1>🔍 Cognitive Complexity Debug Report</h1>
        <div class="header-info">
            <div class="info-item">
                <strong>Session ID:</strong> ${visualTrace.sessionId}
            </div>
            <div class="info-item">
                <strong>Duration:</strong> ${duration.toFixed(2)}s
            </div>
            <div class="info-item">
                <strong>Generated:</strong> ${new Date().toLocaleString()}
            </div>
        </div>
    </header>`;
  }

  private generateSummary(reportData: any): string {
    return `
    <section class="summary">
        <h2>📊 Summary</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">${reportData.overview.totalEvents}</div>
                <div class="stat-label">Total Events</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${reportData.overview.filesProcessed}</div>
                <div class="stat-label">Files Processed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${reportData.performanceMetrics.totalRuleExecutions}</div>
                <div class="stat-label">Rule Executions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${(reportData.cacheStatistics.hitRate * 100).toFixed(1)}%</div>
                <div class="stat-label">Cache Hit Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${reportData.errorSummary.errorCount}</div>
                <div class="stat-label">Errors</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${reportData.overview.totalDiagnostics}</div>
                <div class="stat-label">Diagnostics</div>
            </div>
        </div>
    </section>`;
  }

  private generateDiagnosticsSection(diagnostics: DiagnosticResult[]): string {
    if (diagnostics.length === 0) {
      return `
      <section class="diagnostics">
          <h2>✅ Diagnostics</h2>
          <div class="no-issues">No issues found! Your analysis completed successfully.</div>
      </section>`;
    }

    const diagnosticCards = diagnostics.map(diagnostic => {
      const severityClass = `severity-${diagnostic.severity}`;
      const severityIcon = {
        info: 'ℹ️',
        warning: '⚠️',
        error: '❌',
        critical: '🚨'
      }[diagnostic.severity];

      const solutionsHtml = diagnostic.solutions.slice(0, 3).map(solution => `
        <div class="solution">
            <div class="solution-header">
                <span class="solution-title">${solution.title}</span>
                <span class="solution-meta">
                    <span class="effort-${solution.effort}">${solution.effort} effort</span>
                    <span class="risk-${solution.risk}">${solution.risk} risk</span>
                </span>
            </div>
            <div class="solution-description">${solution.description}</div>
            ${solution.autoFixAvailable ? '<div class="auto-fix">🔧 Auto-fix available</div>' : ''}
        </div>
      `).join('');

      return `
        <div class="diagnostic-card ${severityClass}">
            <div class="diagnostic-header">
                <span class="diagnostic-icon">${severityIcon}</span>
                <span class="diagnostic-title">${diagnostic.title}</span>
                <span class="diagnostic-category">${diagnostic.category}</span>
            </div>
            <div class="diagnostic-description">${diagnostic.description}</div>
            ${diagnostic.rootCause ? `
                <div class="root-cause">
                    <strong>Root Cause:</strong> ${diagnostic.rootCause.description}
                </div>
            ` : ''}
            ${solutionsHtml ? `
                <div class="solutions">
                    <strong>Solutions:</strong>
                    ${solutionsHtml}
                </div>
            ` : ''}
        </div>
      `;
    }).join('');

    return `
    <section class="diagnostics">
        <h2>🔍 Diagnostics</h2>
        <div class="diagnostics-container">
            ${diagnosticCards}
        </div>
    </section>`;
  }

  private generateTimelineSection(events: DebugEvent[]): string {
    if (!this.config.charts.enableTimeline) return '';

    // 生成时间线数据
    const timelineData = this.prepareTimelineData(events);

    return `
    <section class="timeline">
        <h2>⏱️ Execution Timeline</h2>
        <div class="chart-container">
            <canvas id="timelineChart" width="800" height="400"></canvas>
        </div>
        <div class="timeline-legend">
            <div class="legend-item"><span class="legend-color rule"></span> Rule Execution</div>
            <div class="legend-item"><span class="legend-color function"></span> Function Analysis</div>
            <div class="legend-item"><span class="legend-color file"></span> File Processing</div>
            <div class="legend-item"><span class="legend-color error"></span> Errors</div>
        </div>
    </section>`;
  }

  private generatePerformanceSection(reportData: any): string {
    if (!this.config.charts.enablePerformanceBars) return '';

    return `
    <section class="performance">
        <h2>⚡ Performance Analysis</h2>
        <div class="performance-grid">
            <div class="performance-chart">
                <h3>Rule Execution Times</h3>
                <canvas id="rulePerformanceChart" width="400" height="300"></canvas>
            </div>
            <div class="performance-metrics">
                <h3>Key Metrics</h3>
                <div class="metric">
                    <span class="metric-label">Average Rule Time:</span>
                    <span class="metric-value">${reportData.performanceMetrics.averageRuleTime.toFixed(2)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Max Rule Time:</span>
                    <span class="metric-value">${reportData.performanceMetrics.maxRuleTime.toFixed(2)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Total Execution Time:</span>
                    <span class="metric-value">${reportData.performanceMetrics.totalExecutionTime.toFixed(2)}ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Cache Hit Rate:</span>
                    <span class="metric-value">${(reportData.cacheStatistics.hitRate * 100).toFixed(1)}%</span>
                </div>
            </div>
        </div>
    </section>`;
  }

  private generateFlowGraphSection(visualTrace: VisualTrace): string {
    if (!this.config.charts.enableFlowGraph || visualTrace.flowGraph.nodes.length === 0) return '';

    return `
    <section class="flow-graph">
        <h2>🌐 Execution Flow</h2>
        <div class="graph-container">
            <div id="flowGraph" style="height: 500px;"></div>
        </div>
        <div class="graph-controls">
            <button onclick="resetZoom()">Reset Zoom</button>
            <button onclick="togglePhysics()">Toggle Physics</button>
        </div>
    </section>`;
  }

  private generateEventsSection(events: DebugEvent[]): string {
    const recentEvents = events.slice(-50); // 最近50个事件

    const eventRows = recentEvents.map(event => {
      const levelClass = `level-${event.level}`;
      const timestamp = new Date(event.timestamp).toLocaleTimeString();
      const context = [
        event.context.ruleId,
        event.context.nodeType,
        event.context.functionName
      ].filter(Boolean).join(' | ');

      return `
        <tr class="${levelClass}">
            <td>${timestamp}</td>
            <td><span class="event-type">${event.type}</span></td>
            <td><span class="event-level ${levelClass}">${event.level}</span></td>
            <td>${context}</td>
            <td>${event.performance?.duration?.toFixed(2) || '-'}ms</td>
        </tr>
      `;
    }).join('');

    return `
    <section class="events">
        <h2>📝 Recent Events</h2>
        <div class="events-table-container">
            <table class="events-table">
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Type</th>
                        <th>Level</th>
                        <th>Context</th>
                        <th>Duration</th>
                    </tr>
                </thead>
                <tbody>
                    ${eventRows}
                </tbody>
            </table>
        </div>
        <div class="events-footer">
            Showing last ${recentEvents.length} of ${events.length} total events
        </div>
    </section>`;
  }

  private generateRawDataSection(
    events: DebugEvent[],
    visualTrace: VisualTrace,
    diagnostics: DiagnosticResult[]
  ): string {
    return `
    <section class="raw-data">
        <h2>🗃️ Raw Data</h2>
        <div class="data-tabs">
            <button class="tab-button active" onclick="showTab('events')">Events</button>
            <button class="tab-button" onclick="showTab('trace')">Visual Trace</button>
            <button class="tab-button" onclick="showTab('diagnostics')">Diagnostics</button>
        </div>
        <div class="tab-content">
            <div id="events-tab" class="tab-pane active">
                <pre><code>${JSON.stringify(events, null, 2)}</code></pre>
            </div>
            <div id="trace-tab" class="tab-pane">
                <pre><code>${JSON.stringify(visualTrace, null, 2)}</code></pre>
            </div>
            <div id="diagnostics-tab" class="tab-pane">
                <pre><code>${JSON.stringify(diagnostics, null, 2)}</code></pre>
            </div>
        </div>
    </section>`;
  }

  private generateCSS(): string {
    return `
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: ${this.config.theme.text};
            background: ${this.config.theme.background};
        }
        
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        
        .header {
            background: linear-gradient(135deg, ${this.config.theme.primary}, ${this.config.theme.secondary});
            color: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 1rem; }
        .header-info { display: flex; gap: 2rem; flex-wrap: wrap; }
        .info-item { font-size: 1.1rem; }
        
        .summary { margin-bottom: 2rem; }
        .stats-grid {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem; margin-top: 1rem;
        }
        .stat-card {
            background: white; padding: 1.5rem; border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;
        }
        .stat-value { font-size: 2rem; font-weight: bold; color: ${this.config.theme.primary}; }
        .stat-label { color: #666; margin-top: 0.5rem; }
        
        .diagnostics { margin-bottom: 2rem; }
        .diagnostic-card {
            background: white; border-radius: 8px; padding: 1.5rem; margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .diagnostic-header { display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem; }
        .diagnostic-title { font-weight: bold; font-size: 1.1rem; }
        .diagnostic-category {
            background: #f8f9fa; padding: 0.25rem 0.75rem; border-radius: 4px;
            font-size: 0.875rem; color: #666;
        }
        
        .severity-critical { border-left: 4px solid ${this.config.theme.error}; }
        .severity-error { border-left: 4px solid #e74c3c; }
        .severity-warning { border-left: 4px solid ${this.config.theme.warning}; }
        .severity-info { border-left: 4px solid ${this.config.theme.primary}; }
        
        .solution {
            background: #f8f9fa; padding: 1rem; border-radius: 4px; margin-top: 0.5rem;
        }
        .solution-header { display: flex; justify-content: space-between; align-items: center; }
        .solution-title { font-weight: 500; }
        .solution-meta span {
            padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.75rem;
            margin-left: 0.5rem;
        }
        .effort-low, .risk-low { background: ${this.config.theme.success}; color: white; }
        .effort-medium, .risk-medium { background: ${this.config.theme.warning}; color: white; }
        .effort-high, .risk-high { background: ${this.config.theme.error}; color: white; }
        
        .chart-container { margin: 1rem 0; }
        
        .performance-grid {
            display: grid; grid-template-columns: 2fr 1fr; gap: 2rem; margin-top: 1rem;
        }
        .performance-chart, .performance-metrics { background: white; padding: 1.5rem; border-radius: 8px; }
        .metric { display: flex; justify-content: space-between; padding: 0.5rem 0; }
        .metric-value { font-weight: bold; color: ${this.config.theme.primary}; }
        
        .events-table-container { background: white; border-radius: 8px; overflow: hidden; }
        .events-table { width: 100%; border-collapse: collapse; }
        .events-table th, .events-table td { padding: 0.75rem; text-align: left; border-bottom: 1px solid #eee; }
        .events-table th { background: #f8f9fa; font-weight: 600; }
        .event-type { font-family: monospace; background: #f8f9fa; padding: 0.25rem 0.5rem; border-radius: 3px; }
        .event-level { padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.75rem; }
        .level-error { background: ${this.config.theme.error}; color: white; }
        .level-warn { background: ${this.config.theme.warning}; color: white; }
        .level-info { background: ${this.config.theme.primary}; color: white; }
        .level-debug { background: #6c757d; color: white; }
        .level-trace { background: #adb5bd; color: white; }
        
        .tab-button {
            background: none; border: none; padding: 0.75rem 1.5rem; cursor: pointer;
            border-bottom: 2px solid transparent; margin-right: 1rem;
        }
        .tab-button.active { border-bottom-color: ${this.config.theme.primary}; }
        .tab-pane { display: none; margin-top: 1rem; }
        .tab-pane.active { display: block; }
        pre { background: #f8f9fa; padding: 1rem; border-radius: 4px; overflow-x: auto; }
        
        h2 { color: ${this.config.theme.text}; margin-bottom: 1rem; }
        
        .no-issues {
            background: ${this.config.theme.success}; color: white; padding: 2rem;
            text-align: center; border-radius: 8px; font-size: 1.1rem;
        }
    `;
  }

  private generateJavaScript(reportData: any): string {
    return `
        // Tab functionality
        function showTab(tabName) {
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
        
        // Chart data
        const reportData = ${JSON.stringify(reportData)};
        
        // Initialize charts when document is ready
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('rulePerformanceChart')) {
                initRulePerformanceChart();
            }
            if (document.getElementById('flowGraph')) {
                initFlowGraph();
            }
        });
        
        function initRulePerformanceChart() {
            const ctx = document.getElementById('rulePerformanceChart').getContext('2d');
            const ruleStats = reportData.ruleStatistics;
            const ruleIds = Object.keys(ruleStats).slice(0, 10); // Top 10 rules
            const averageTimes = ruleIds.map(id => ruleStats[id].averageTime);
            
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ruleIds,
                    datasets: [{
                        label: 'Average Execution Time (ms)',
                        data: averageTimes,
                        backgroundColor: '${this.config.theme.primary}',
                        borderColor: '${this.config.theme.primary}',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }
        
        function initFlowGraph() {
            // Simplified flow graph implementation
            const container = document.getElementById('flowGraph');
            const data = {
                nodes: new vis.DataSet([
                    {id: 1, label: 'Start', color: '${this.config.theme.success}'},
                    {id: 2, label: 'Rule Engine', color: '${this.config.theme.primary}'},
                    {id: 3, label: 'Cache', color: '${this.config.theme.secondary}'},
                    {id: 4, label: 'Analysis', color: '${this.config.theme.warning}'},
                    {id: 5, label: 'End', color: '${this.config.theme.success}'}
                ]),
                edges: new vis.DataSet([
                    {from: 1, to: 2},
                    {from: 2, to: 3},
                    {from: 2, to: 4},
                    {from: 3, to: 4},
                    {from: 4, to: 5}
                ])
            };
            
            const options = {
                physics: { enabled: true },
                nodes: { shape: 'dot', size: 20 },
                edges: { arrows: 'to' }
            };
            
            window.flowNetwork = new vis.Network(container, data, options);
        }
        
        function resetZoom() {
            if (window.flowNetwork) {
                window.flowNetwork.fit();
            }
        }
        
        function togglePhysics() {
            if (window.flowNetwork) {
                const physics = window.flowNetwork.physics.enabled;
                window.flowNetwork.setOptions({ physics: { enabled: !physics } });
            }
        }
    `;
  }

  private prepareTimelineData(events: DebugEvent[]): any {
    // 简化的时间线数据准备
    const timeline = events.map(event => ({
      timestamp: event.timestamp,
      type: event.type,
      level: event.level,
      context: event.context,
    }));
    
    return timeline;
  }

  private generateRecommendedActions(diagnostics: DiagnosticResult[]): string[] {
    const actions: string[] = [];
    
    // 基于诊断结果生成建议
    const criticalIssues = diagnostics.filter(d => d.severity === 'critical');
    if (criticalIssues.length > 0) {
      actions.push(`Address ${criticalIssues.length} critical issues immediately`);
    }
    
    const performanceIssues = diagnostics.filter(d => d.category === 'performance');
    if (performanceIssues.length > 0) {
      actions.push(`Optimize performance - ${performanceIssues.length} issues found`);
    }
    
    const configIssues = diagnostics.filter(d => d.category === 'configuration');
    if (configIssues.length > 0) {
      actions.push(`Review and update configuration settings`);
    }
    
    const autoFixAvailable = diagnostics.filter(d => 
      d.solutions.some(s => s.autoFixAvailable)
    ).length;
    if (autoFixAvailable > 0) {
      actions.push(`Apply ${autoFixAvailable} available auto-fixes`);
    }
    
    if (actions.length === 0) {
      actions.push('No immediate actions required - analysis completed successfully');
    }
    
    return actions;
  }

  private groupBy<T>(array: T[], keyFn: (item: T) => string): Record<string, number> {
    return array.reduce((acc, item) => {
      const key = keyFn(item);
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private getTopErrors(errors: DebugEvent[], limit: number): Array<{type: string, count: number}> {
    const errorTypes = this.groupBy(errors, e => e.data?.error?.name || 'Unknown');
    return Object.entries(errorTypes)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);
  }
}