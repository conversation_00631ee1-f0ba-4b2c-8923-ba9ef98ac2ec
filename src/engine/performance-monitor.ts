/**
 * 全面性能监控系统
 * 实现规则执行时间和内存使用监控、建立缓存命中率和性能指标统计、
 * 支持性能热点识别和瓶颈分析、生成性能报告和优化建议
 */

import type { 
  EngineMetrics, 
  AnalysisContext, 
  Rule, 
  RuleResult, 
  NodeAnalysis,
  FunctionAnalysis,
  FileAnalysis 
} from './types';

// 性能热点信息
export interface PerformanceHotspot {
  type: 'rule' | 'function' | 'file' | 'cache' | 'memory';
  identifier: string;
  metric: string;
  value: number;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  suggestion: string;
  impact: string;
  frequency: number;
}

// 瓶颈分析结果
export interface BottleneckAnalysis {
  category: 'cpu' | 'memory' | 'cache' | 'io' | 'concurrency';
  description: string;
  rootCause: string;
  impact: number; // 0-100 百分比
  recommendation: string;
  estimatedImprovement: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

// 性能趋势数据
export interface PerformanceTrend {
  metric: string;
  timeWindow: 'minute' | 'hour' | 'session';
  dataPoints: Array<{ timestamp: number; value: number }>;
  trend: 'improving' | 'stable' | 'degrading';
  changeRate: number; // 变化率
}

// 内存使用详情
export interface MemoryUsageDetail {
  component: string;
  heapUsed: number;
  heapTotal: number;
  external: number;
  arrayBuffers: number;
  growthRate: number; // MB/s
  leakSuspicion: number; // 0-100 百分比
}

// 规则执行统计
export interface RuleExecutionStats {
  ruleId: string;
  totalExecutions: number;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  successRate: number;
  errorCount: number;
  cacheHitRatio: number;
  memoryImpact: number;
  frequency: number; // 执行频率 (次/秒)
}

// 缓存性能详情
export interface CachePerformanceDetail {
  layer: string;
  hitCount: number;
  missCount: number;
  hitRate: number;
  averageAccessTime: number;
  memoryUsage: number;
  evictionCount: number;
  efficiency: number; // 缓存效率分数 0-100
}

// 性能报告
export interface PerformanceReport {
  // 概览信息
  overview: {
    totalAnalysisTime: number;
    filesProcessed: number;
    functionsAnalyzed: number;
    overallThroughput: number; // 函数/秒
    memoryEfficiency: number; // 0-100
    cacheEfficiency: number; // 0-100
  };
  
  // 热点和瓶颈
  hotspots: PerformanceHotspot[];
  bottlenecks: BottleneckAnalysis[];
  
  // 详细统计
  ruleStats: RuleExecutionStats[];
  cacheStats: CachePerformanceDetail[];
  memoryDetails: MemoryUsageDetail[];
  
  // 趋势分析
  trends: PerformanceTrend[];
  
  // 优化建议
  recommendations: {
    priority: 'high' | 'medium' | 'low';
    category: string;
    title: string;
    description: string;
    expectedImpact: string;
    implementation: string;
  }[];
  
  // 报告元数据
  generatedAt: number;
  reportDuration: number;
  analysisScope: string;
}

// 实时性能监控事件
export interface PerformanceEvent {
  type: 'rule_start' | 'rule_end' | 'function_start' | 'function_end' | 
        'file_start' | 'file_end' | 'cache_hit' | 'cache_miss' | 
        'memory_warning' | 'hotspot_detected';
  timestamp: number;
  data: any;
  severity?: 'info' | 'warning' | 'error';
}

// 性能监控配置
export interface PerformanceMonitorConfig {
  // 监控开关
  enabled: boolean;
  enableRealTimeMonitoring: boolean;
  enableTrendAnalysis: boolean;
  enableHotspotDetection: boolean;
  enableMemoryTracking: boolean;
  
  // 阈值配置
  thresholds: {
    ruleExecutionTime: number; // ms
    functionAnalysisTime: number; // ms
    fileAnalysisTime: number; // ms
    memoryUsage: number; // MB
    cacheHitRate: number; // %
    cpuUsage: number; // %
  };
  
  // 数据保留
  dataRetention: {
    trendDataPoints: number;
    maxHotspots: number;
    maxBottlenecks: number;
  };
  
  // 报告设置
  reporting: {
    includeRecommendations: boolean;
    includeTrends: boolean;
    verbosityLevel: 'minimal' | 'standard' | 'detailed';
  };
}

// 默认配置
const DEFAULT_MONITOR_CONFIG: PerformanceMonitorConfig = {
  enabled: true,
  enableRealTimeMonitoring: true,
  enableTrendAnalysis: true,
  enableHotspotDetection: true,
  enableMemoryTracking: true,
  thresholds: {
    ruleExecutionTime: 50, // 50ms
    functionAnalysisTime: 100, // 100ms
    fileAnalysisTime: 1000, // 1s
    memoryUsage: 100, // 100MB
    cacheHitRate: 70, // 70%
    cpuUsage: 80, // 80%
  },
  dataRetention: {
    trendDataPoints: 100,
    maxHotspots: 20,
    maxBottlenecks: 10,
  },
  reporting: {
    includeRecommendations: true,
    includeTrends: true,
    verbosityLevel: 'standard',
  },
};

/**
 * 高级性能监控器
 * 提供全面的性能监控、热点检测、瓶颈分析和优化建议
 */
export class AdvancedPerformanceMonitor {
  private config: PerformanceMonitorConfig;
  private isEnabled: boolean;
  
  // 基础指标
  private engineMetrics: EngineMetrics;
  private sessionStartTime: number;
  private lastReportTime: number;
  
  // 实时监控数据
  private ruleExecutionMap = new Map<string, RuleExecutionStats>();
  private functionTimingMap = new Map<string, number[]>();
  private fileTimingMap = new Map<string, number[]>();
  private memorySnapshots: MemoryUsageDetail[] = [];
  private cacheMetricsMap = new Map<string, CachePerformanceDetail>();
  
  // 性能事件和趋势
  private performanceEvents: PerformanceEvent[] = [];
  private trendData = new Map<string, PerformanceTrend>();
  private detectedHotspots: PerformanceHotspot[] = [];
  private identifiedBottlenecks: BottleneckAnalysis[] = [];
  
  // 定时器和监控任务
  private monitoringTimer?: NodeJS.Timeout;
  private memoryMonitorTimer?: NodeJS.Timeout;
  private trendAnalysisTimer?: NodeJS.Timeout;

  constructor(config: Partial<PerformanceMonitorConfig> = {}) {
    this.config = { ...DEFAULT_MONITOR_CONFIG, ...config };
    this.isEnabled = this.config.enabled;
    this.sessionStartTime = performance.now();
    this.lastReportTime = this.sessionStartTime;
    this.engineMetrics = this.createEmptyMetrics();
    
    if (this.isEnabled) {
      this.startMonitoring();
    }
  }

  // ============ 公共API ============

  /**
   * 记录规则执行开始
   */
  recordRuleStart(ruleId: string, node: any, context: AnalysisContext): void {
    if (!this.isEnabled) return;
    
    this.emitEvent({
      type: 'rule_start',
      timestamp: performance.now(),
      data: { ruleId, nodeType: node.type, context: context.functionName }
    });
  }

  /**
   * 记录规则执行结束
   */
  recordRuleEnd(ruleId: string, result: RuleResult, executionTime: number): void {
    if (!this.isEnabled) return;
    
    // 更新规则统计
    this.updateRuleStats(ruleId, executionTime, result);
    
    // 检查是否为热点
    this.checkRuleHotspot(ruleId, executionTime);
    
    this.emitEvent({
      type: 'rule_end',
      timestamp: performance.now(),
      data: { ruleId, executionTime, complexity: result.complexity, cacheHit: result.cacheHit }
    });
  }

  /**
   * 记录函数分析
   */
  recordFunctionAnalysis(functionName: string, analysis: FunctionAnalysis): void {
    if (!this.isEnabled) return;
    
    const executionTime = analysis.metrics.executionTime;
    
    // 更新函数统计
    if (!this.functionTimingMap.has(functionName)) {
      this.functionTimingMap.set(functionName, []);
    }
    this.functionTimingMap.get(functionName)!.push(executionTime);
    
    // 检查函数热点
    this.checkFunctionHotspot(functionName, executionTime);
    
    this.emitEvent({
      type: 'function_end',
      timestamp: performance.now(),
      data: { 
        functionName, 
        executionTime, 
        complexity: analysis.totalComplexity,
        nodeCount: analysis.metrics.nodeCount
      }
    });
  }

  /**
   * 记录文件分析
   */
  recordFileAnalysis(filePath: string, analysis: FileAnalysis): void {
    if (!this.isEnabled) return;
    
    const executionTime = analysis.analysisTime;
    
    // 更新文件统计
    if (!this.fileTimingMap.has(filePath)) {
      this.fileTimingMap.set(filePath, []);
    }
    this.fileTimingMap.get(filePath)!.push(executionTime);
    
    // 检查文件热点
    this.checkFileHotspot(filePath, executionTime);
    
    this.emitEvent({
      type: 'file_end',
      timestamp: performance.now(),
      data: { 
        filePath, 
        executionTime, 
        functionCount: analysis.functions.length,
        totalComplexity: analysis.totalComplexity,
        cacheHitRate: analysis.cacheHitRate
      }
    });
  }

  /**
   * 记录缓存访问
   */
  recordCacheAccess(layer: string, isHit: boolean, accessTime: number): void {
    if (!this.isEnabled) return;
    
    // 更新缓存统计
    this.updateCacheStats(layer, isHit, accessTime);
    
    this.emitEvent({
      type: isHit ? 'cache_hit' : 'cache_miss',
      timestamp: performance.now(),
      data: { layer, accessTime }
    });
  }

  /**
   * 更新引擎指标
   */
  updateEngineMetrics(metrics: EngineMetrics): void {
    if (!this.isEnabled) return;
    
    this.engineMetrics = { ...metrics };
    
    // 更新趋势数据
    this.updateTrendData('cache_hit_rate', metrics.cacheHitRate);
    this.updateTrendData('parallel_efficiency', metrics.parallelEfficiency);
    this.updateTrendData('memory_usage', metrics.memoryUsage.heapUsed / 1024 / 1024); // MB
    
    // 检查内存使用情况
    this.checkMemoryUsage(metrics.memoryUsage);
  }

  /**
   * 生成性能报告
   */
  generateReport(): PerformanceReport {
    const now = performance.now();
    const reportDuration = now - this.lastReportTime;
    this.lastReportTime = now;
    
    // 分析当前性能数据
    this.analyzeCurrentPerformance();
    
    const report: PerformanceReport = {
      overview: this.generateOverview(),
      hotspots: [...this.detectedHotspots],
      bottlenecks: [...this.identifiedBottlenecks],
      ruleStats: Array.from(this.ruleExecutionMap.values()),
      cacheStats: Array.from(this.cacheMetricsMap.values()),
      memoryDetails: [...this.memorySnapshots.slice(-5)], // 最近5个快照
      trends: Array.from(this.trendData.values()),
      recommendations: this.generateRecommendations(),
      generatedAt: now,
      reportDuration,
      analysisScope: this.determineAnalysisScope(),
    };
    
    return report;
  }

  /**
   * 获取实时性能快照
   */
  getRealtimeSnapshot(): {
    currentMetrics: EngineMetrics;
    activeHotspots: PerformanceHotspot[];
    recentEvents: PerformanceEvent[];
    memoryStatus: MemoryUsageDetail;
  } {
    return {
      currentMetrics: { ...this.engineMetrics },
      activeHotspots: this.detectedHotspots.filter(h => h.severity !== 'low'),
      recentEvents: this.performanceEvents.slice(-10),
      memoryStatus: this.getCurrentMemoryStatus(),
    };
  }

  /**
   * 清理监控数据
   */
  reset(): void {
    this.ruleExecutionMap.clear();
    this.functionTimingMap.clear();
    this.fileTimingMap.clear();
    this.memorySnapshots = [];
    this.cacheMetricsMap.clear();
    this.performanceEvents = [];
    this.trendData.clear();
    this.detectedHotspots = [];
    this.identifiedBottlenecks = [];
    this.sessionStartTime = performance.now();
    this.lastReportTime = this.sessionStartTime;
  }

  /**
   * 停止监控
   */
  stop(): void {
    this.isEnabled = false;
    
    if (this.monitoringTimer) {
      clearInterval(this.monitoringTimer);
    }
    if (this.memoryMonitorTimer) {
      clearInterval(this.memoryMonitorTimer);
    }
    if (this.trendAnalysisTimer) {
      clearInterval(this.trendAnalysisTimer);
    }
  }

  // ============ 私有方法 ============

  private startMonitoring(): void {
    // 启动实时监控
    if (this.config.enableRealTimeMonitoring) {
      this.monitoringTimer = setInterval(() => {
        this.performRealtimeAnalysis();
      }, 5000); // 每5秒分析一次
      
      // 使用 unref() 防止定时器阻止进程退出
      this.monitoringTimer.unref();
    }
    
    // 启动内存监控
    if (this.config.enableMemoryTracking) {
      this.memoryMonitorTimer = setInterval(() => {
        this.captureMemorySnapshot();
      }, 10000); // 每10秒采集内存快照
      
      // 使用 unref() 防止定时器阻止进程退出
      this.memoryMonitorTimer.unref();
    }
    
    // 启动趋势分析
    if (this.config.enableTrendAnalysis) {
      this.trendAnalysisTimer = setInterval(() => {
        this.performTrendAnalysis();
      }, 30000); // 每30秒分析趋势
      
      // 使用 unref() 防止定时器阻止进程退出
      this.trendAnalysisTimer.unref();
    }
  }

  private createEmptyMetrics(): EngineMetrics {
    return {
      totalAnalysisTime: 0,
      filesProcessed: 0,
      functionsAnalyzed: 0,
      rulesExecuted: 0,
      cacheHitRate: 0,
      parallelEfficiency: 0,
      memoryUsage: process.memoryUsage(),
    };
  }

  private updateRuleStats(ruleId: string, executionTime: number, result: RuleResult): void {
    let stats = this.ruleExecutionMap.get(ruleId);
    
    if (!stats) {
      stats = {
        ruleId,
        totalExecutions: 0,
        totalTime: 0,
        averageTime: 0,
        minTime: Number.MAX_VALUE,
        maxTime: 0,
        successRate: 0,
        errorCount: 0,
        cacheHitRatio: 0,
        memoryImpact: 0,
        frequency: 0,
      };
      this.ruleExecutionMap.set(ruleId, stats);
    }
    
    stats.totalExecutions++;
    stats.totalTime += executionTime;
    stats.averageTime = stats.totalTime / stats.totalExecutions;
    stats.minTime = Math.min(stats.minTime, executionTime);
    stats.maxTime = Math.max(stats.maxTime, executionTime);
    
    // 更新缓存命中率
    const totalCacheAccess = stats.totalExecutions;
    const cacheHits = result.cacheHit ? 1 : 0;
    stats.cacheHitRatio = (stats.cacheHitRatio * (totalCacheAccess - 1) + cacheHits) / totalCacheAccess;
    
    // 计算执行频率
    const sessionDuration = (performance.now() - this.sessionStartTime) / 1000; // 秒
    stats.frequency = stats.totalExecutions / sessionDuration;
  }

  private updateCacheStats(layer: string, isHit: boolean, accessTime: number): void {
    let stats = this.cacheMetricsMap.get(layer);
    
    if (!stats) {
      stats = {
        layer,
        hitCount: 0,
        missCount: 0,
        hitRate: 0,
        averageAccessTime: 0,
        memoryUsage: 0,
        evictionCount: 0,
        efficiency: 0,
      };
      this.cacheMetricsMap.set(layer, stats);
    }
    
    if (isHit) {
      stats.hitCount++;
    } else {
      stats.missCount++;
    }
    
    const totalAccess = stats.hitCount + stats.missCount;
    stats.hitRate = stats.hitCount / totalAccess;
    stats.averageAccessTime = (stats.averageAccessTime * (totalAccess - 1) + accessTime) / totalAccess;
    
    // 计算缓存效率分数 (命中率 * 速度加权)
    stats.efficiency = (stats.hitRate * 70) + ((100 - Math.min(stats.averageAccessTime, 100)) * 0.3);
  }

  private checkRuleHotspot(ruleId: string, executionTime: number): void {
    if (!this.config.enableHotspotDetection) return;
    
    if (executionTime > this.config.thresholds.ruleExecutionTime) {
      const severity = this.determineSeverity('rule_time', executionTime, this.config.thresholds.ruleExecutionTime);
      
      const hotspot: PerformanceHotspot = {
        type: 'rule',
        identifier: ruleId,
        metric: 'execution_time',
        value: executionTime,
        threshold: this.config.thresholds.ruleExecutionTime,
        severity,
        suggestion: this.getRuleOptimizationSuggestion(ruleId, executionTime),
        impact: this.calculateRuleImpact(ruleId),
        frequency: this.ruleExecutionMap.get(ruleId)?.frequency || 0,
      };
      
      this.addHotspot(hotspot);
    }
  }

  private checkFunctionHotspot(functionName: string, executionTime: number): void {
    if (!this.config.enableHotspotDetection) return;
    
    if (executionTime > this.config.thresholds.functionAnalysisTime) {
      const severity = this.determineSeverity('function_time', executionTime, this.config.thresholds.functionAnalysisTime);
      
      const hotspot: PerformanceHotspot = {
        type: 'function',
        identifier: functionName,
        metric: 'analysis_time',
        value: executionTime,
        threshold: this.config.thresholds.functionAnalysisTime,
        severity,
        suggestion: this.getFunctionOptimizationSuggestion(functionName, executionTime),
        impact: this.calculateFunctionImpact(functionName),
        frequency: this.calculateAnalysisFrequency(functionName),
      };
      
      this.addHotspot(hotspot);
    }
  }

  private checkFileHotspot(filePath: string, executionTime: number): void {
    if (!this.config.enableHotspotDetection) return;
    
    if (executionTime > this.config.thresholds.fileAnalysisTime) {
      const severity = this.determineSeverity('file_time', executionTime, this.config.thresholds.fileAnalysisTime);
      
      const hotspot: PerformanceHotspot = {
        type: 'file',
        identifier: filePath,
        metric: 'analysis_time',
        value: executionTime,
        threshold: this.config.thresholds.fileAnalysisTime,
        severity,
        suggestion: this.getFileOptimizationSuggestion(filePath, executionTime),
        impact: this.calculateFileImpact(filePath),
        frequency: this.calculateAnalysisFrequency(filePath),
      };
      
      this.addHotspot(hotspot);
    }
  }

  private checkMemoryUsage(memoryUsage: NodeJS.MemoryUsage): void {
    const heapUsedMB = memoryUsage.heapUsed / 1024 / 1024;
    
    if (heapUsedMB > this.config.thresholds.memoryUsage) {
      this.emitEvent({
        type: 'memory_warning',
        timestamp: performance.now(),
        data: { heapUsedMB, threshold: this.config.thresholds.memoryUsage },
        severity: 'warning'
      });
      
      // 检查内存热点
      const severity = this.determineSeverity('memory', heapUsedMB, this.config.thresholds.memoryUsage);
      
      const hotspot: PerformanceHotspot = {
        type: 'memory',
        identifier: 'heap_usage',
        metric: 'memory_usage_mb',
        value: heapUsedMB,
        threshold: this.config.thresholds.memoryUsage,
        severity,
        suggestion: this.getMemoryOptimizationSuggestion(heapUsedMB),
        impact: 'High memory usage may lead to performance degradation and potential out-of-memory errors',
        frequency: 1, // 内存使用是持续的
      };
      
      this.addHotspot(hotspot);
    }
  }

  private updateTrendData(metric: string, value: number): void {
    if (!this.config.enableTrendAnalysis) return;
    
    let trend = this.trendData.get(metric);
    
    if (!trend) {
      trend = {
        metric,
        timeWindow: 'session',
        dataPoints: [],
        trend: 'stable',
        changeRate: 0,
      };
      this.trendData.set(metric, trend);
    }
    
    // 添加新数据点
    trend.dataPoints.push({
      timestamp: performance.now(),
      value,
    });
    
    // 保持数据点数量在限制内
    if (trend.dataPoints.length > this.config.dataRetention.trendDataPoints) {
      trend.dataPoints.shift();
    }
    
    // 计算趋势
    this.calculateTrend(trend);
  }

  private calculateTrend(trend: PerformanceTrend): void {
    if (trend.dataPoints.length < 2) return;
    
    const recentPoints = trend.dataPoints.slice(-10); // 最近10个点
    if (recentPoints.length < 2) return;
    
    const firstPoint = recentPoints[0];
    const lastPoint = recentPoints[recentPoints.length - 1];
    
    if (!firstPoint || !lastPoint) return;
    
    const firstValue = firstPoint.value;
    const lastValue = lastPoint.value;
    const timeSpan = lastPoint.timestamp - firstPoint.timestamp;
    
    trend.changeRate = ((lastValue - firstValue) / firstValue) * 100;
    
    if (Math.abs(trend.changeRate) < 5) {
      trend.trend = 'stable';
    } else if (trend.changeRate > 0) {
      trend.trend = 'improving';
    } else {
      trend.trend = 'degrading';
    }
  }

  private performRealtimeAnalysis(): void {
    // 分析规则执行模式
    this.analyzeRuleExecutionPatterns();
    
    // 分析缓存效率
    this.analyzeCacheEfficiency();
    
    // 检测异常模式
    this.detectAnomalousPatterns();
  }

  private analyzeCurrentPerformance(): void {
    // 识别瓶颈
    this.identifyBottlenecks();
    
    // 整理热点
    this.consolidateHotspots();
    
    // 更新效率指标
    this.updateEfficiencyMetrics();
  }

  private identifyBottlenecks(): void {
    this.identifiedBottlenecks = [];
    
    // CPU瓶颈分析
    this.analyzeCPUBottlenecks();
    
    // 内存瓶颈分析
    this.analyzeMemoryBottlenecks();
    
    // 缓存瓶颈分析
    this.analyzeCacheBottlenecks();
    
    // 并发瓶颈分析
    this.analyzeConcurrencyBottlenecks();
  }

  private analyzeCPUBottlenecks(): void {
    // 找出执行时间最长的规则
    const slowestRules = Array.from(this.ruleExecutionMap.values())
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 3);
    
    slowestRules.forEach(rule => {
      if (rule.averageTime > this.config.thresholds.ruleExecutionTime * 2) {
        this.identifiedBottlenecks.push({
          category: 'cpu',
          description: `Rule '${rule.ruleId}' has excessive execution time`,
          rootCause: `Average execution time of ${rule.averageTime.toFixed(2)}ms exceeds optimal range`,
          impact: Math.min((rule.averageTime / this.config.thresholds.ruleExecutionTime) * 20, 100),
          recommendation: `Optimize rule logic, consider caching, or implement early exit conditions`,
          estimatedImprovement: `Potential 20-40% performance gain`,
          priority: rule.averageTime > this.config.thresholds.ruleExecutionTime * 4 ? 'urgent' : 'high',
        });
      }
    });
  }

  private analyzeMemoryBottlenecks(): void {
    const currentMemoryMB = this.engineMetrics.memoryUsage.heapUsed / 1024 / 1024;
    
    if (currentMemoryMB > this.config.thresholds.memoryUsage) {
      this.identifiedBottlenecks.push({
        category: 'memory',
        description: 'High memory usage detected',
        rootCause: `Heap usage of ${currentMemoryMB.toFixed(2)}MB exceeds threshold`,
        impact: Math.min((currentMemoryMB / this.config.thresholds.memoryUsage) * 30, 100),
        recommendation: 'Implement object pooling, optimize data structures, or increase cache TTL',
        estimatedImprovement: '15-30% memory reduction possible',
        priority: currentMemoryMB > this.config.thresholds.memoryUsage * 2 ? 'urgent' : 'high',
      });
    }
  }

  private analyzeCacheBottlenecks(): void {
    const avgHitRate = Array.from(this.cacheMetricsMap.values())
      .reduce((sum, cache) => sum + cache.hitRate, 0) / this.cacheMetricsMap.size;
    
    if (avgHitRate < this.config.thresholds.cacheHitRate / 100) {
      this.identifiedBottlenecks.push({
        category: 'cache',
        description: 'Low cache hit rate affecting performance',
        rootCause: `Average cache hit rate of ${(avgHitRate * 100).toFixed(1)}% is below optimal range`,
        impact: (1 - avgHitRate) * 50, // 缓存未命中的影响
        recommendation: 'Tune cache size, adjust TTL settings, or improve cache key generation',
        estimatedImprovement: '10-25% performance improvement with better caching',
        priority: avgHitRate < 0.3 ? 'urgent' : 'medium',
      });
    }
  }

  private analyzeConcurrencyBottlenecks(): void {
    if (this.engineMetrics.parallelEfficiency < 0.6) {
      this.identifiedBottlenecks.push({
        category: 'concurrency',
        description: 'Low parallel execution efficiency',
        rootCause: `Parallel efficiency of ${(this.engineMetrics.parallelEfficiency * 100).toFixed(1)}% indicates poor task distribution`,
        impact: (1 - this.engineMetrics.parallelEfficiency) * 40,
        recommendation: 'Optimize task distribution, reduce synchronization points, or adjust concurrency settings',
        estimatedImprovement: '15-35% throughput improvement possible',
        priority: this.engineMetrics.parallelEfficiency < 0.4 ? 'high' : 'medium',
      });
    }
  }

  private generateOverview(): PerformanceReport['overview'] {
    const sessionDuration = (performance.now() - this.sessionStartTime) / 1000;
    const throughput = this.engineMetrics.functionsAnalyzed / sessionDuration;
    const memoryEfficiency = Math.max(0, 100 - (this.engineMetrics.memoryUsage.heapUsed / 1024 / 1024 / this.config.thresholds.memoryUsage) * 100);
    const cacheEfficiency = this.engineMetrics.cacheHitRate * 100;
    
    return {
      totalAnalysisTime: this.engineMetrics.totalAnalysisTime,
      filesProcessed: this.engineMetrics.filesProcessed,
      functionsAnalyzed: this.engineMetrics.functionsAnalyzed,
      overallThroughput: throughput,
      memoryEfficiency: Math.min(100, memoryEfficiency),
      cacheEfficiency: Math.min(100, cacheEfficiency),
    };
  }

  private generateRecommendations(): PerformanceReport['recommendations'] {
    const recommendations: PerformanceReport['recommendations'] = [];
    
    // 基于热点生成建议
    this.detectedHotspots.forEach(hotspot => {
      if (hotspot.severity === 'high' || hotspot.severity === 'critical') {
        recommendations.push({
          priority: hotspot.severity === 'critical' ? 'high' : 'medium',
          category: hotspot.type,
          title: `Optimize ${hotspot.type} performance`,
          description: hotspot.suggestion,
          expectedImpact: hotspot.impact,
          implementation: this.getImplementationGuide(hotspot),
        });
      }
    });
    
    // 基于瓶颈生成建议
    this.identifiedBottlenecks.forEach(bottleneck => {
      recommendations.push({
        priority: bottleneck.priority === 'urgent' ? 'high' : bottleneck.priority as any,
        category: bottleneck.category,
        title: `Address ${bottleneck.category} bottleneck`,
        description: bottleneck.recommendation,
        expectedImpact: bottleneck.estimatedImprovement,
        implementation: this.getBottleneckImplementationGuide(bottleneck),
      });
    });
    
    return recommendations.slice(0, 10); // 限制建议数量
  }

  // 辅助方法
  private determineSeverity(type: string, value: number, threshold: number): PerformanceHotspot['severity'] {
    const ratio = value / threshold;
    if (ratio >= 4) return 'critical';
    if (ratio >= 2.5) return 'high';
    if (ratio >= 1.5) return 'medium';
    return 'low';
  }

  private addHotspot(hotspot: PerformanceHotspot): void {
    // 避免重复添加相同的热点
    const existingIndex = this.detectedHotspots.findIndex(
      h => h.identifier === hotspot.identifier && h.type === hotspot.type && h.metric === hotspot.metric
    );
    
    if (existingIndex >= 0) {
      // 更新现有热点
      this.detectedHotspots[existingIndex] = hotspot;
    } else {
      this.detectedHotspots.push(hotspot);
    }
    
    // 限制热点数量
    if (this.detectedHotspots.length > this.config.dataRetention.maxHotspots) {
      this.detectedHotspots.sort((a, b) => {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        return severityOrder[b.severity] - severityOrder[a.severity];
      });
      this.detectedHotspots = this.detectedHotspots.slice(0, this.config.dataRetention.maxHotspots);
    }
    
    this.emitEvent({
      type: 'hotspot_detected',
      timestamp: performance.now(),
      data: hotspot,
      severity: hotspot.severity === 'critical' ? 'error' : 'warning'
    });
  }

  private emitEvent(event: PerformanceEvent): void {
    this.performanceEvents.push(event);
    
    // 限制事件数量
    if (this.performanceEvents.length > 1000) {
      this.performanceEvents = this.performanceEvents.slice(-500);
    }
  }

  private getCurrentMemoryStatus(): MemoryUsageDetail {
    const memUsage = process.memoryUsage();
    return {
      component: 'engine',
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      arrayBuffers: memUsage.arrayBuffers,
      growthRate: this.calculateMemoryGrowthRate(),
      leakSuspicion: this.calculateLeakSuspicion(),
    };
  }

  private captureMemorySnapshot(): void {
    const snapshot = this.getCurrentMemoryStatus();
    this.memorySnapshots.push(snapshot);
    
    // 保持快照数量限制
    if (this.memorySnapshots.length > 20) {
      this.memorySnapshots.shift();
    }
  }

  private calculateMemoryGrowthRate(): number {
    if (this.memorySnapshots.length < 2) return 0;
    
    const recent = this.memorySnapshots.slice(-5);
    if (recent.length < 2) return 0;
    
    const oldestSnapshot = recent[0];
    const newestSnapshot = recent[recent.length - 1];
    const timeDiff = performance.now() - this.sessionStartTime; // 简化计算
    const memoryDiff = (newestSnapshot.heapUsed - oldestSnapshot.heapUsed) / 1024 / 1024; // MB
    
    return memoryDiff / (timeDiff / 1000); // MB/s
  }

  private calculateLeakSuspicion(): number {
    if (this.memorySnapshots.length < 5) return 0;
    
    const recent = this.memorySnapshots.slice(-5);
    const growthTrend = recent.reduce((acc, snapshot, index) => {
      if (index === 0) return acc;
      const growth = snapshot.heapUsed - recent[index - 1].heapUsed;
      return acc + (growth > 0 ? 1 : 0);
    }, 0);
    
    // 如果最近5次快照都在增长，怀疑度较高
    return (growthTrend / (recent.length - 1)) * 100;
  }

  // 优化建议方法
  private getRuleOptimizationSuggestion(ruleId: string, executionTime: number): string {
    return `Rule '${ruleId}' execution time (${executionTime.toFixed(2)}ms) exceeds threshold. Consider implementing caching, optimizing node traversal, or adding early exit conditions.`;
  }

  private getFunctionOptimizationSuggestion(functionName: string, executionTime: number): string {
    return `Function '${functionName}' analysis time (${executionTime.toFixed(2)}ms) is excessive. Consider breaking down complex functions or implementing incremental analysis.`;
  }

  private getFileOptimizationSuggestion(filePath: string, executionTime: number): string {
    return `File '${filePath}' analysis time (${executionTime.toFixed(2)}ms) is high. Consider parallel processing or implementing file-level caching.`;
  }

  private getMemoryOptimizationSuggestion(memoryUsage: number): string {
    return `Memory usage (${memoryUsage.toFixed(2)}MB) is high. Consider implementing object pooling, reducing cache size, or using streaming processing for large files.`;
  }

  private calculateRuleImpact(ruleId: string): string {
    const stats = this.ruleExecutionMap.get(ruleId);
    if (!stats) return 'Low impact';
    
    const totalRuleTime = stats.totalTime;
    const sessionTime = performance.now() - this.sessionStartTime;
    const impactPercentage = (totalRuleTime / sessionTime) * 100;
    
    if (impactPercentage > 20) return 'High impact - significant contribution to total analysis time';
    if (impactPercentage > 10) return 'Medium impact - notable contribution to analysis time';
    return 'Low impact - minimal contribution to overall performance';
  }

  private calculateFunctionImpact(functionName: string): string {
    const timings = this.functionTimingMap.get(functionName);
    if (!timings || timings.length === 0) return 'Low impact';
    
    const avgTime = timings.reduce((sum, time) => sum + time, 0) / timings.length;
    const frequency = timings.length;
    
    if (avgTime > 100 && frequency > 10) return 'High impact - frequently analyzed complex function';
    if (avgTime > 50 || frequency > 5) return 'Medium impact - either complex or frequently analyzed';
    return 'Low impact - simple or infrequently analyzed function';
  }

  private calculateFileImpact(filePath: string): string {
    const timings = this.fileTimingMap.get(filePath);
    if (!timings || timings.length === 0) return 'Low impact';
    
    const avgTime = timings.reduce((sum, time) => sum + time, 0) / timings.length;
    
    if (avgTime > 2000) return 'High impact - very complex file requiring optimization';
    if (avgTime > 1000) return 'Medium impact - complex file with room for improvement';
    return 'Low impact - reasonably sized file';
  }

  private calculateAnalysisFrequency(identifier: string): number {
    const sessionDuration = (performance.now() - this.sessionStartTime) / 1000;
    const timings = this.functionTimingMap.get(identifier) || this.fileTimingMap.get(identifier) || [];
    return timings.length / sessionDuration;
  }

  private determineAnalysisScope(): string {
    return `Session scope: ${this.engineMetrics.filesProcessed} files, ${this.engineMetrics.functionsAnalyzed} functions analyzed`;
  }

  private getImplementationGuide(hotspot: PerformanceHotspot): string {
    const guides = {
      rule: 'Review rule implementation, add caching logic, optimize AST traversal patterns',
      function: 'Break down complex functions, implement parallel analysis where possible',
      file: 'Use streaming processing for large files, implement file-level result caching',
      memory: 'Implement object pooling, tune garbage collection, optimize data structures',
      cache: 'Adjust cache configuration, improve key generation, tune TTL settings',
    };
    return guides[hotspot.type] || 'Review implementation for optimization opportunities';
  }

  private getBottleneckImplementationGuide(bottleneck: BottleneckAnalysis): string {
    const guides = {
      cpu: 'Profile code execution, optimize algorithms, implement early exit conditions',
      memory: 'Implement memory pooling, optimize object lifecycle, tune GC settings',
      cache: 'Analyze cache patterns, adjust sizes and TTL, improve eviction policies',
      io: 'Implement async I/O, use streaming for large files, optimize file system access',
      concurrency: 'Review task distribution, reduce synchronization, optimize thread usage',
    };
    return guides[bottleneck.category] || 'Analyze bottleneck and implement targeted optimizations';
  }

  // 占位符方法 - 用于更复杂的分析
  private analyzeRuleExecutionPatterns(): void {
    // 实现规则执行模式分析逻辑
  }

  private analyzeCacheEfficiency(): void {
    // 实现缓存效率分析逻辑
  }

  private detectAnomalousPatterns(): void {
    // 实现异常模式检测逻辑
  }

  private consolidateHotspots(): void {
    // 整理和合并相似的热点
  }

  private updateEfficiencyMetrics(): void {
    // 更新各种效率指标
  }

  private performTrendAnalysis(): void {
    // 执行趋势分析
  }
}

/**
 * 空性能监控器实现（Null Object模式）
 * 提供与AdvancedPerformanceMonitor相同的接口，但所有方法都是空操作
 * 用于在禁用监控功能时提供无副作用的替代品
 */
export class NullPerformanceMonitor {
  constructor(_config?: Partial<PerformanceMonitorConfig>) {
    // 空构造函数：不创建任何定时器或监控任务
  }

  /**
   * 无操作：不记录规则执行开始
   */
  recordRuleStart(_ruleId: string, _node: any, _context: AnalysisContext): void {
    // 空方法：不记录任何数据
  }

  /**
   * 无操作：不记录规则执行结束
   */
  recordRuleEnd(_ruleId: string, _result: RuleResult, _executionTime: number): void {
    // 空方法：不记录任何数据
  }

  /**
   * 无操作：不记录函数分析
   */
  recordFunctionAnalysis(_functionName: string, _analysis: FunctionAnalysis): void {
    // 空方法：不记录任何数据
  }

  /**
   * 无操作：不记录文件分析
   */
  recordFileAnalysis(_filePath: string, _analysis: FileAnalysis): void {
    // 空方法：不记录任何数据
  }

  /**
   * 无操作：不记录缓存事件
   */
  recordCacheEvent(_layer: string, _event: 'hit' | 'miss' | 'eviction', _accessTime?: number): void {
    // 空方法：不记录任何数据
  }

  /**
   * 无操作：不记录内存快照
   */
  recordMemorySnapshot(): void {
    // 空方法：不记录任何数据
  }

  /**
   * 无操作：不更新指标
   */
  updateMetrics(_metrics: Partial<EngineMetrics>): void {
    // 空方法：不更新任何指标
  }

  /**
   * 返回空的性能报告
   */
  generateReport(): PerformanceReport {
    return {
      overview: {
        totalAnalysisTime: 0,
        filesProcessed: 0,
        functionsAnalyzed: 0,
        overallThroughput: 0,
        memoryEfficiency: 100,
        cacheEfficiency: 100,
      },
      hotspots: [],
      bottlenecks: [],
      rules: [],
      cache: [],
      memory: [],
      trends: [],
      events: [],
      recommendations: [],
      insights: {
        performanceGrade: 'A',
        topConcerns: [],
        improvementPotential: 0,
        analysisQuality: 100,
      },
      metadata: {
        generatedAt: new Date(),
        sessionDuration: 0,
        configurationHash: '',
        analysisScope: 'No monitoring enabled',
        dataPoints: 0,
      },
    };
  }

  /**
   * 返回空的性能快照
   */
  getPerformanceSnapshot(): any {
    return {
      timestamp: Date.now(),
      metrics: {},
      hotspots: [],
      memory: { heapUsed: 0, heapTotal: 0 },
      cache: { hitRate: 0, size: 0 },
    };
  }

  /**
   * 返回空的热点列表
   */
  getHotspots(): PerformanceHotspot[] {
    return [];
  }

  /**
   * 返回空的瓶颈列表
   */
  getBottlenecks(): BottleneckAnalysis[] {
    return [];
  }

  /**
   * 返回空的趋势数据
   */
  getTrends(): PerformanceTrend[] {
    return [];
  }

  /**
   * 无操作：不启用监控
   */
  enable(): void {
    // 空方法：不启用任何监控
  }

  /**
   * 无操作：不禁用监控
   */
  disable(): void {
    // 空方法：不禁用任何监控
  }

  /**
   * 无操作：没有监控需要停止
   */
  stop(): void {
    // 空方法：没有定时器或监控任务需要停止
  }

  /**
   * 无操作：没有资源需要清理
   */
  dispose(): void {
    // 空方法：没有资源需要清理
  }

  /**
   * 返回空的引擎指标
   */
  getEngineMetrics(): EngineMetrics {
    return {
      totalAnalysisTime: 0,
      filesProcessed: 0,
      functionsAnalyzed: 0,
      rulesExecuted: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      averageComplexity: 0,
      throughput: 0,
    };
  }
}