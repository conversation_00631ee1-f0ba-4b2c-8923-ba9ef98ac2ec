/**
 * 异步规则引擎演示和测试
 * 展示如何使用现代化的异步规则引擎进行复杂度分析
 */

import { createEngineWithDefaults, createCleanEngine, validateEngine } from '../engine';
import type { AsyncRuleEngine } from '../engine/types';

// 演示用的代码样例
const DEMO_CODE_SAMPLES = {
  simple: `
function simpleFunction(x: number): number {
  if (x > 0) {
    return x * 2;
  }
  return 0;
}`,

  complex: `
function complexFunction(data: any[], options: { filter?: boolean, sort?: boolean }): any[] {
  let result = data;
  
  if (options.filter) {
    result = result.filter(item => {
      if (item && item.active) {
        return item.value > 0 || item.name && item.name.length > 0;
      }
      return false;
    });
  }
  
  if (options.sort) {
    result.sort((a, b) => {
      if (a.priority !== b.priority) {
        return b.priority - a.priority;
      }
      return a.name.localeCompare(b.name);
    });
  }
  
  return result;
}`,

  jsx: `
function UserProfile({ user, isEditing }: { user: User, isEditing: boolean }) {
  const [errors, setErrors] = useState<string[]>([]);
  
  if (!user) {
    return <div>Loading...</div>;
  }
  
  return (
    <div className="user-profile">
      <h1>{user.name}</h1>
      {user.avatar && <img src={user.avatar} alt="Avatar" />}
      
      {isEditing ? (
        <form onSubmit={handleSubmit}>
          <input 
            value={user.name || ''} 
            onChange={(e) => setName(e.target.value)}
            disabled={!user.permissions?.canEdit}
          />
          {errors.length > 0 && (
            <div className="errors">
              {errors.map(error => (
                <span key={error}>{error}</span>
              ))}
            </div>
          )}
        </form>
      ) : (
        <div className="display-mode">
          <p>{user.bio || 'No bio available'}</p>
          {user.social && user.social.length > 0 && (
            <ul>
              {user.social.map(link => (
                <li key={link.platform}>
                  <a href={link.url}>{link.platform}</a>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
}`,

  recursive: `
function factorial(n: number): number {
  if (n <= 1) {
    return 1;
  }
  return n * factorial(n - 1);
}

function fibonacci(n: number): number {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}`,
};

/**
 * 演示基础引擎使用
 */
async function demonstrateBasicUsage() {
  console.log('\n=== 基础引擎使用演示 ===');
  
  // 创建带有默认规则的引擎
  const engine = await createEngineWithDefaults();
  
  try {
    // 分析简单代码
    console.log('\n1. 分析简单函数...');
    const simpleResult = await engine.analyzeCode(DEMO_CODE_SAMPLES.simple, 'simple.ts');
    console.log('简单函数分析结果:', {
      totalComplexity: simpleResult.totalComplexity,
      functionCount: simpleResult.functions.length,
      averageComplexity: simpleResult.averageComplexity,
      analysisTime: `${simpleResult.analysisTime}ms`,
    });
    
    // 分析复杂代码
    console.log('\n2. 分析复杂函数...');
    const complexResult = await engine.analyzeCode(DEMO_CODE_SAMPLES.complex, 'complex.ts');
    console.log('复杂函数分析结果:', {
      totalComplexity: complexResult.totalComplexity,
      functionCount: complexResult.functions.length,
      averageComplexity: complexResult.averageComplexity,
      analysisTime: `${complexResult.analysisTime}ms`,
    });
    
    // 显示详细的函数分析
    if (complexResult.functions.length > 0) {
      const func = complexResult.functions[0];
      if (func) {
        console.log('\n函数详细分析:', {
          name: func.functionName,
          complexity: func.totalComplexity,
          nodeCount: func.metrics.nodeCount,
          nestingDepth: func.metrics.nestingDepth,
          executionTime: `${func.metrics.executionTime}ms`,
        });
        
        // 显示应用的规则
        func.nodeAnalyses.forEach((nodeAnalysis, index) => {
          if (nodeAnalysis.complexity > 0) {
            console.log(`  节点 ${index + 1}: 复杂度 ${nodeAnalysis.complexity}, 规则: ${nodeAnalysis.appliedRules.map(r => r.ruleId).join(', ')}`);
          }
        });
      }
    }
    
  } catch (error) {
    console.error('分析过程中发生错误:', error);
  } finally {
    engine.shutdown();
  }
}

/**
 * 演示JSX代码分析
 */
async function demonstrateJSXAnalysis() {
  console.log('\n=== JSX代码分析演示 ===');
  
  const engine = await createEngineWithDefaults();
  
  try {
    const jsxResult = await engine.analyzeCode(DEMO_CODE_SAMPLES.jsx, 'UserProfile.tsx');
    
    console.log('JSX组件分析结果:', {
      totalComplexity: jsxResult.totalComplexity,
      functionCount: jsxResult.functions.length,
      averageComplexity: jsxResult.averageComplexity,
      cacheHitRate: `${(jsxResult.cacheHitRate * 100).toFixed(1)}%`,
    });
    
    // 显示豁免信息
    jsxResult.functions.forEach(func => {
      const exemptions = func.nodeAnalyses
        .flatMap(node => node.exemptions)
        .filter(exemption => exemption.type === 'structural');
      
      if (exemptions.length > 0) {
        console.log(`\n函数 '${func.functionName}' 的JSX结构豁免:`);
        exemptions.forEach((exemption, index) => {
          console.log(`  ${index + 1}. ${exemption.reason} (规则: ${exemption.appliedRule})`);
        });
      }
    });
    
  } catch (error) {
    console.error('JSX分析过程中发生错误:', error);
  } finally {
    engine.shutdown();
  }
}

/**
 * 演示性能监控和缓存
 */
async function demonstratePerformanceMonitoring() {
  console.log('\n=== 性能监控和缓存演示 ===');
  
  const engine = await createEngineWithDefaults();
  
  try {
    // 第一次分析 - 无缓存
    console.log('\n第一次分析 (冷启动):');
    const startTime1 = performance.now();
    await engine.analyzeCode(DEMO_CODE_SAMPLES.complex, 'complex.ts');
    const endTime1 = performance.now();
    
    let metrics1 = engine.getMetrics();
    console.log('第一次分析指标:', {
      analysisTime: `${endTime1 - startTime1}ms`,
      cacheHitRate: `${(metrics1.cacheHitRate * 100).toFixed(1)}%`,
      functionsAnalyzed: metrics1.functionsAnalyzed,
      rulesExecuted: metrics1.rulesExecuted,
    });
    
    // 第二次分析 - 使用缓存
    console.log('\n第二次分析 (使用缓存):');
    const startTime2 = performance.now();
    await engine.analyzeCode(DEMO_CODE_SAMPLES.complex, 'complex.ts');
    const endTime2 = performance.now();
    
    let metrics2 = engine.getMetrics();
    console.log('第二次分析指标:', {
      analysisTime: `${endTime2 - startTime2}ms`,
      cacheHitRate: `${(metrics2.cacheHitRate * 100).toFixed(1)}%`,
      speedup: `${((endTime1 - startTime1) / (endTime2 - startTime2)).toFixed(2)}x`,
    });
    
    // 显示执行池状态
    const loadInfo = engine.getExecutionLoad();
    console.log('\n执行池状态:', {
      totalCapacity: loadInfo.totalCapacity,
      utilizationRate: `${(loadInfo.utilizationRate * 100).toFixed(1)}%`,
    });
    
  } catch (error) {
    console.error('性能监控演示中发生错误:', error);
  } finally {
    engine.shutdown();
  }
}

/**
 * 演示并行文件分析
 */
async function demonstrateParallelAnalysis() {
  console.log('\n=== 并行文件分析演示 ===');
  
  const engine = await createEngineWithDefaults({
    performance: {
      maxConcurrency: 4,
      cacheSize: 10000,
      streamingThreshold: 1000,
      enableParallelExecution: true,
    }
  });
  
  try {
    // 创建多个临时代码样本进行并行分析
    const codeFiles = [
      { code: DEMO_CODE_SAMPLES.simple, path: 'simple.ts' },
      { code: DEMO_CODE_SAMPLES.complex, path: 'complex.ts' },
      { code: DEMO_CODE_SAMPLES.jsx, path: 'component.tsx' },
      { code: DEMO_CODE_SAMPLES.recursive, path: 'recursive.ts' },
    ];
    
    console.log(`\n并行分析 ${codeFiles.length} 个文件...`);
    const startTime = performance.now();
    
    // 模拟并行文件分析
    const results = await Promise.all(
      codeFiles.map(({ code, path }) => engine.analyzeCode(code, path))
    );
    
    const endTime = performance.now();
    
    console.log('并行分析结果:');
    console.log(`总耗时: ${endTime - startTime}ms`);
    
    results.forEach((result, index) => {
      const file = codeFiles[index];
      if (file && result) {
        console.log(`  ${file.path}: 复杂度 ${result.totalComplexity}, 函数 ${result.functions.length}, 耗时 ${result.analysisTime}ms`);
      }
    });
    
    const totalComplexity = results.reduce((sum, r) => sum + r.totalComplexity, 0);
    const totalFunctions = results.reduce((sum, r) => sum + r.functions.length, 0);
    
    console.log(`\n汇总: 总复杂度 ${totalComplexity}, 总函数 ${totalFunctions}`);
    
  } catch (error) {
    console.error('并行分析演示中发生错误:', error);
  } finally {
    engine.shutdown();
  }
}

/**
 * 演示引擎验证和诊断
 */
async function demonstrateEngineValidation() {
  console.log('\n=== 引擎验证和诊断演示 ===');
  
  // 创建并验证正常引擎
  console.log('\n1. 验证正常引擎:');
  const normalEngine = await createEngineWithDefaults();
  const normalValidation = await validateEngine(normalEngine);
  
  console.log('正常引擎验证:', {
    isValid: normalValidation.isValid,
    issues: normalValidation.issues,
  });
  
  if (normalValidation.stats) {
    console.log('引擎统计:', normalValidation.stats.ruleStats);
  }
  
  // 创建并验证空引擎
  console.log('\n2. 验证空引擎:');
  const emptyEngine = createCleanEngine();
  const emptyValidation = await validateEngine(emptyEngine);
  
  console.log('空引擎验证:', {
    isValid: emptyValidation.isValid,
    issues: emptyValidation.issues,
  });
  
  normalEngine.shutdown();
  emptyEngine.shutdown();
}

/**
 * 主演示函数
 */
export async function runAsyncEngineDemo() {
  console.log('🚀 异步规则引擎完整演示');
  console.log('='.repeat(50));
  
  try {
    await demonstrateBasicUsage();
    await demonstrateJSXAnalysis();
    await demonstratePerformanceMonitoring();
    await demonstrateParallelAnalysis();
    await demonstrateEngineValidation();
    
    console.log('\n✅ 异步规则引擎演示完成!');
    console.log('引擎已成功实现:');
    console.log('  • 异步规则调度和执行管理');
    console.log('  • 规则并行执行和依赖管理');
    console.log('  • 规则优先级和生命周期管理');
    console.log('  • 性能监控和错误处理机制');
    console.log('  • 智能缓存和增量分析');
    console.log('  • JSX智能分析和结构豁免');
    
  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
  }
}

// 如果直接运行此文件，则执行演示
if (require.main === module) {
  runAsyncEngineDemo().catch(console.error);
}