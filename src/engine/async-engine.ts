/**
 * 异步规则引擎核心实现
 * 高性能异步规则调度和执行管理
 */

import type { Node, Module } from '@swc/core';
import type { 
  AsyncRuleEngine,
  Rule, 
  AnalysisContext, 
  NodeAnalysis, 
  FunctionAnalysis, 
  FileAnalysis,
  EngineMetrics,
  ResolvedEngineConfig,
  ExecutionOptions
} from './types';
import { DEFAULT_ENGINE_CONFIG } from './types';
import type { CacheManager } from '../cache/types';
import { IntelligentCacheManager } from '../cache/manager';
import { FunctionFinderVisitor } from '../core/function-finder-visitor';
import { RuleRegistryImpl } from './registry';
import { ParallelExecutionPool, type ExecutionPoolStats } from './execution-pool';
import { ASTParser } from '../core/parser';
import { AdvancedPerformanceMonitor, type PerformanceReport, type PerformanceMonitorConfig } from './performance-monitor';

/**
 * 性能监控器
 */
class PerformanceMonitor {
  private metrics: EngineMetrics;
  private startTime: number;

  constructor() {
    this.metrics = this.createEmptyMetrics();
    this.startTime = performance.now();
  }

  recordAnalysis(analysisTime: number): void {
    this.metrics.totalAnalysisTime += analysisTime;
  }

  recordFile(): void {
    this.metrics.filesProcessed++;
  }

  recordFunction(): void {
    this.metrics.functionsAnalyzed++;
  }

  recordRule(): void {
    this.metrics.rulesExecuted++;
  }

  updateCacheHitRate(rate: number): void {
    this.metrics.cacheHitRate = rate;
  }

  updateParallelEfficiency(efficiency: number): void {
    this.metrics.parallelEfficiency = efficiency;
  }

  getMetrics(): EngineMetrics {
    return {
      ...this.metrics,
      memoryUsage: process.memoryUsage(),
    };
  }

  reset(): void {
    this.metrics = this.createEmptyMetrics();
    this.startTime = performance.now();
  }

  private createEmptyMetrics(): EngineMetrics {
    return {
      totalAnalysisTime: 0,
      filesProcessed: 0,
      functionsAnalyzed: 0,
      rulesExecuted: 0,
      cacheHitRate: 0,
      parallelEfficiency: 0,
      memoryUsage: process.memoryUsage(),
    };
  }
}

/**
 * 异步规则引擎实现
 */
export class AsyncRuleEngineImpl implements AsyncRuleEngine {
  private config: ResolvedEngineConfig;
  private cacheManager: CacheManager;
  private ruleRegistry: RuleRegistryImpl;
  private executionPool: ParallelExecutionPool;
  private performanceMonitor: PerformanceMonitor;
  private advancedMonitor: AdvancedPerformanceMonitor;
  private astParser: ASTParser;
  private isInitialized: boolean = false;

  constructor(config: Partial<ResolvedEngineConfig> = {}, monitorConfig: Partial<PerformanceMonitorConfig> = {}) {
    this.config = this.mergeConfig(config);
    this.cacheManager = new IntelligentCacheManager();
    this.ruleRegistry = new RuleRegistryImpl();
    this.executionPool = new ParallelExecutionPool(this.createExecutionOptions());
    this.performanceMonitor = new PerformanceMonitor();
    this.advancedMonitor = new AdvancedPerformanceMonitor(monitorConfig);
    this.astParser = new ASTParser();
    
    this.initialize();
  }

  async analyzeNode(node: Node, context: AnalysisContext): Promise<NodeAnalysis> {
    this.ensureInitialized();
    
    const startTime = performance.now();
    
    try {
      // 尝试从缓存获取结果
      const nodeHash = this.generateNodeHash(node, context);
      const cachedResult = await this.cacheManager.getCachedNodeResult(nodeHash);
      
      if (cachedResult) {
        this.updateCacheStatistics();
        // 记录缓存命中
        this.advancedMonitor.recordCacheAccess('node', true, performance.now() - startTime);
        return cachedResult;
      }

      // 获取适用的规则
      const applicableRules = this.ruleRegistry.getRulesForNode(node);
      
      if (applicableRules.length === 0) {
        return this.createEmptyNodeAnalysis(node);
      }

      // 记录规则执行开始
      applicableRules.forEach(rule => {
        this.advancedMonitor.recordRuleStart(rule.id, node, context);
      });

      // 并行执行规则
      const ruleResults = await this.executionPool.executeRules(
        applicableRules,
        node,
        context
      );

      // 记录规则执行结束
      ruleResults.forEach(result => {
        this.advancedMonitor.recordRuleEnd(result.ruleId, result, result.executionTime);
      });

      // 创建节点分析结果
      const nodeAnalysis = await this.createNodeAnalysis(node, ruleResults, context);
      
      // 缓存结果
      await this.cacheManager.setCachedNodeResult(nodeHash, nodeAnalysis);
      
      // 更新性能指标
      const analysisTime = performance.now() - startTime;
      this.performanceMonitor.recordAnalysis(analysisTime);
      this.advancedMonitor.recordCacheAccess('node', false, analysisTime);
      
      return nodeAnalysis;
    } catch (error) {
      console.error(`Error analyzing node of type '${node.type}':`, error);
      return this.createErrorNodeAnalysis(node, error);
    }
  }

  async analyzeFunction(func: Node): Promise<FunctionAnalysis> {
    this.ensureInitialized();
    
    const startTime = performance.now();
    
    try {
      const functionName = this.astParser.getFunctionName(func);
      const location = this.astParser.getLocation(func);
      
      // 创建函数分析上下文
      const context = this.createAnalysisContext(func, functionName);
      
      // 获取函数体中的所有节点
      const functionNodes = this.extractFunctionNodes(func);
      
      // 并行分析所有节点
      const nodeAnalyses = await Promise.all(
        functionNodes.map(node => this.analyzeNode(node, context))
      );
      
      // 计算总复杂度
      const totalComplexity = nodeAnalyses.reduce(
        (sum, analysis) => sum + analysis.complexity, 
        0
      );
      
      // 创建函数分析结果
      const functionAnalysis: FunctionAnalysis = {
        functionName,
        totalComplexity,
        nodeAnalyses,
        location: {
          line: location.line,
          column: location.column,
        },
        metrics: {
          nodeCount: nodeAnalyses.length,
          nestingDepth: this.calculateNestingDepth(nodeAnalyses),
          cyclomaticComplexity: totalComplexity, // 简化版本
          cognitiveComplexity: totalComplexity,
          executionTime: performance.now() - startTime,
        },
      };
      
      this.performanceMonitor.recordFunction();
      
      // 记录函数分析性能
      this.advancedMonitor.recordFunctionAnalysis(functionName, functionAnalysis);
      
      return functionAnalysis;
    } catch (error) {
      console.error(`Error analyzing function:`, error);
      throw new Error(`Function analysis failed: ${error}`);
    }
  }

  async analyzeFile(ast: Module): Promise<FileAnalysis> {
    this.ensureInitialized();
    
    const startTime = performance.now();
    
    try {
      // 查找所有函数
      const functions = FunctionFinderVisitor.find(ast);
      
      // 并行分析所有函数
      const functionAnalyses = await Promise.all(
        functions.map(func => this.analyzeFunction(func))
      );
      
      // 计算文件级别的统计信息
      const totalComplexity = functionAnalyses.reduce(
        (sum, analysis) => sum + analysis.totalComplexity, 
        0
      );
      
      const averageComplexity = functionAnalyses.length > 0 
        ? totalComplexity / functionAnalyses.length 
        : 0;
      
      const analysisTime = performance.now() - startTime;
      const cacheStats = this.cacheManager.getHitRate();
      
      const fileAnalysis: FileAnalysis = {
        filePath: '', // 将在调用方设置
        functions: functionAnalyses,
        totalComplexity,
        averageComplexity,
        analysisTime,
        cacheHitRate: cacheStats.hitRate,
      };
      
      this.performanceMonitor.recordFile();
      this.performanceMonitor.recordAnalysis(analysisTime);
      
      // 记录文件分析性能（需要在调用方设置filePath后调用）
      
      return fileAnalysis;
    } catch (error) {
      console.error(`Error analyzing file:`, error);
      throw new Error(`File analysis failed: ${error}`);
    }
  }

  registerRule(rule: Rule, quiet: boolean = false): void {
    this.ruleRegistry.registerRule(rule, quiet);
    
    // 清除相关缓存
    this.cacheManager.invalidateCache('rule');
    
    if (!quiet) {
      console.log(`Rule '${rule.id}' registered in engine`);
    }
  }

  unregisterRule(ruleId: string): void {
    this.ruleRegistry.unregisterRule(ruleId);
    
    // 清除相关缓存
    this.cacheManager.invalidateCache('rule');
    
    console.log(`Rule '${ruleId}' unregistered from engine`);
  }

  getMetrics(): EngineMetrics {
    const poolStats: ExecutionPoolStats = (this.executionPool as any).getStatistics();
    const cacheStats = this.cacheManager.getHitRate();
    
    // 更新性能指标
    this.performanceMonitor.updateCacheHitRate(cacheStats.hitRate);
    this.performanceMonitor.updateParallelEfficiency(poolStats.throughput);
    
    const metrics = this.performanceMonitor.getMetrics();
    
    // 更新高级监控器
    this.advancedMonitor.updateEngineMetrics(metrics);
    
    return metrics;
  }

  /**
   * 生成详细性能报告
   */
  generatePerformanceReport(): PerformanceReport {
    return this.advancedMonitor.generateReport();
  }

  /**
   * 获取实时性能快照
   */
  getPerformanceSnapshot(): ReturnType<AdvancedPerformanceMonitor['getRealtimeSnapshot']> {
    return this.advancedMonitor.getRealtimeSnapshot();
  }

  /**
   * 重置性能监控数据
   */
  resetPerformanceMonitoring(): void {
    this.advancedMonitor.reset();
    this.performanceMonitor.reset();
  }

  clearCache(): void {
    this.cacheManager.clearCache();
    this.performanceMonitor.reset();
    this.advancedMonitor.reset();
  }

  /**
   * 关闭引擎，清理资源
   */
  shutdown(): void {
    // 停止执行池
    this.executionPool.shutdown();
    
    // 停止性能监控
    this.advancedMonitor.stop();
    
    // 清理缓存
    this.cacheManager.clearCache();
    
    // 重置监控器
    this.performanceMonitor.reset();
    
    this.isInitialized = false;
  }

  // 配置管理
  updateConfig(newConfig: Partial<ResolvedEngineConfig>): void {
    this.config = this.mergeConfig(newConfig);
    
    // 更新执行池配置
    if (newConfig.performance?.maxConcurrency) {
      (this.executionPool as any).setMaxConcurrency(newConfig.performance.maxConcurrency);
    }
    
    // 清除缓存以应用新配置
    this.cacheManager.invalidateCache('config');
  }

  // 获取当前配置
  getConfig(): ResolvedEngineConfig {
    return { ...this.config };
  }

  // 获取规则统计信息
  getRuleStatistics() {
    return this.ruleRegistry.getStatistics();
  }

  // 获取执行池状态
  getExecutionLoad() {
    return this.executionPool.getCurrentLoad();
  }

  // 预热缓存
  async preWarmCache(nodes: Node[]): Promise<void> {
    await this.cacheManager.preWarmCache(nodes);
  }

  // 批量分析文件
  async analyzeFiles(filePaths: string[]): Promise<Map<string, FileAnalysis>> {
    this.ensureInitialized();
    
    const results = new Map<string, FileAnalysis>();
    
    if (this.config.performance.enableParallelExecution && filePaths.length > 1) {
      // 并行分析多个文件
      const analyses = await Promise.all(
        filePaths.map(async (filePath) => {
          try {
            const ast = await this.astParser.parseFile(filePath);
            const analysis = await this.analyzeFile(ast);
            const analysisWithPath = { ...analysis, filePath }; // 设置文件路径
            
            // 记录文件分析性能
            this.advancedMonitor.recordFileAnalysis(filePath, analysisWithPath);
            
            return { filePath, analysis: analysisWithPath };
          } catch (error) {
            console.error(`Failed to analyze file '${filePath}':`, error);
            return { 
              filePath, 
              analysis: this.createErrorFileAnalysis(filePath, error) 
            };
          }
        })
      );
      
      analyses.forEach(({ filePath, analysis }) => {
        results.set(filePath, analysis);
      });
    } else {
      // 串行分析文件
      for (const filePath of filePaths) {
        try {
          const ast = await this.astParser.parseFile(filePath);
          const analysis = await this.analyzeFile(ast);
          const analysisWithPath = { ...analysis, filePath };
          
          // 记录文件分析性能
          this.advancedMonitor.recordFileAnalysis(filePath, analysisWithPath);
          
          results.set(filePath, analysisWithPath);
        } catch (error) {
          console.error(`Failed to analyze file '${filePath}':`, error);
          results.set(filePath, this.createErrorFileAnalysis(filePath, error));
        }
      }
    }
    
    return results;
  }

  // 分析代码字符串
  async analyzeCode(code: string, filePath: string = 'inline'): Promise<FileAnalysis> {
    this.ensureInitialized();
    
    try {
      const ast = await this.astParser.parseCode(code, filePath);
      const analysis = await this.analyzeFile(ast);
      const analysisWithPath = { ...analysis, filePath };
      
      // 记录文件分析性能
      this.advancedMonitor.recordFileAnalysis(filePath, analysisWithPath);
      
      return analysisWithPath;
    } catch (error) {
      console.error(`Failed to analyze code:`, error);
      return this.createErrorFileAnalysis(filePath, error);
    }
  }

  private createErrorFileAnalysis(filePath: string, error: any): FileAnalysis {
    return {
      filePath,
      functions: [],
      totalComplexity: 0,
      averageComplexity: 0,
      analysisTime: 0,
      cacheHitRate: 0,
    };
  }

  private initialize(): void {
    // 注册默认的错误处理器
    this.executionPool.onRuleError((error) => {
      console.error(`Rule execution error in '${error.ruleId}':`, error.message);
      
      if (this.config.output.enableDebugInfo) {
        console.debug('Error context:', error.context);
      }
    });
    
    this.isInitialized = true;
  }

  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new Error('Engine is not initialized');
    }
  }

  private mergeConfig(partialConfig: Partial<ResolvedEngineConfig>): ResolvedEngineConfig {
    return {
      rules: {
        ...DEFAULT_ENGINE_CONFIG.rules,
        ...partialConfig.rules,
      },
      performance: {
        ...DEFAULT_ENGINE_CONFIG.performance,
        ...partialConfig.performance,
      },
      output: {
        ...DEFAULT_ENGINE_CONFIG.output,
        ...partialConfig.output,
      },
      plugins: {
        ...DEFAULT_ENGINE_CONFIG.plugins,
        ...partialConfig.plugins,
      },
    };
  }

  private createExecutionOptions(): ExecutionOptions {
    return {
      maxConcurrency: this.config.performance.maxConcurrency,
      timeout: 5000, // 5秒超时
      enableProfiling: this.config.output.enableDebugInfo,
      isolateErrors: true,
    };
  }

  private generateNodeHash(node: Node, context: AnalysisContext): string {
    // 生成节点的唯一标识
    const nodeData = {
      type: node.type,
      span: (node as any).span,
      nestingLevel: context.nestingLevel,
      configHash: this.getConfigHash(),
    };
    
    return JSON.stringify(nodeData); // 简化版本
  }

  private getConfigHash(): string {
    // 生成配置的哈希值
    return JSON.stringify(this.config); // 简化版本
  }

  private createAnalysisContext(func: Node, functionName: string): AnalysisContext {
    return {
      filePath: '', // 将在调用方设置
      fileContent: '',
      ast: {} as Module, // 将在调用方设置
      currentFunction: func,
      functionName,
      nestingLevel: 0,
      config: this.config,
      jsxMode: 'standard',
      rules: {
        core: Array.from(this.ruleRegistry.getAllRules().map(r => r.id)),
        jsx: [],
        plugins: [],
      },
      cache: this.cacheManager,
      metrics: {
        totalNodes: 0,
        processedNodes: 0,
        cacheHits: 0,
        cacheMisses: 0,
        ruleExecutions: 0,
        parallelExecutions: 0,
        errors: 0,
      },
      plugins: [],
      customData: new Map(),
    };
  }

  private extractFunctionNodes(func: Node): Node[] {
    const nodes: Node[] = [];
    
    // 使用简单的递归遍历来收集节点
    this.collectAnalyzableNodes(func, nodes);
    
    return nodes;
  }

  /**
   * 递归收集可分析的节点
   */
  private collectAnalyzableNodes(node: Node, nodes: Node[]): void {
    if (!node || typeof node !== 'object') {
      return;
    }

    if (this.isAnalyzableNode(node)) {
      nodes.push(node);
    }

    // 递归遍历所有子节点
    for (const key in node) {
      const value = (node as any)[key];
      
      if (Array.isArray(value)) {
        value.forEach(child => {
          if (child && typeof child === 'object' && child.type) {
            this.collectAnalyzableNodes(child, nodes);
          }
        });
      } else if (value && typeof value === 'object' && value.type) {
        this.collectAnalyzableNodes(value, nodes);
      }
    }
  }

  private isAnalyzableNode(node: Node): boolean {
    const analyzableTypes = [
      'IfStatement',
      'WhileStatement',
      'ForStatement',
      'DoWhileStatement',
      'SwitchStatement',
      'ConditionalExpression',
      'LogicalExpression',
      'CatchClause',
      'JSXElement',
      'JSXFragment',
    ];
    
    return analyzableTypes.includes(node.type);
  }

  private async createNodeAnalysis(
    node: Node, 
    ruleResults: import('./types').RuleResult[], 
    context: AnalysisContext
  ): Promise<NodeAnalysis> {
    const complexity = ruleResults.reduce((sum, result) => 
      result.isExempted ? sum : sum + result.complexity, 0
    );
    
    const appliedRules = ruleResults.map(result => ({
      ruleId: result.ruleId,
      ruleName: result.ruleId, // 简化版本
      complexity: result.complexity,
      isExempted: result.isExempted,
      reason: result.reason,
      executionTime: result.executionTime,
    }));
    
    const exemptions = ruleResults
      .filter(result => result.isExempted)
      .map(result => ({
        type: 'structural' as const,
        reason: result.reason,
        complexityReduced: result.complexity,
        appliedRule: result.ruleId,
      }));
    
    return {
      node,
      complexity,
      appliedRules,
      exemptions,
      children: [], // 暂时为空，完整实现中应递归分析子节点
      aggregatedComplexity: complexity,
      analysisTime: ruleResults.reduce((sum, result) => sum + result.executionTime, 0),
      cacheUtilization: ruleResults.filter(result => result.cacheHit).length / ruleResults.length,
    };
  }

  private createEmptyNodeAnalysis(node: Node): NodeAnalysis {
    return {
      node,
      complexity: 0,
      appliedRules: [],
      exemptions: [],
      children: [],
      aggregatedComplexity: 0,
      analysisTime: 0,
      cacheUtilization: 0,
    };
  }

  private createErrorNodeAnalysis(node: Node, error: any): NodeAnalysis {
    return {
      node,
      complexity: 0,
      appliedRules: [],
      exemptions: [{
        type: 'configuration',
        reason: `Analysis error: ${error.message}`,
        complexityReduced: 0,
        appliedRule: 'error-handler',
      }],
      children: [],
      aggregatedComplexity: 0,
      analysisTime: 0,
      cacheUtilization: 0,
    };
  }

  private calculateNestingDepth(nodeAnalyses: NodeAnalysis[]): number {
    // 简化的嵌套深度计算
    return Math.max(...nodeAnalyses.map(analysis => 
      analysis.appliedRules.filter(rule => rule.ruleId.includes('nesting')).length
    ), 0);
  }

  private updateCacheStatistics(): void {
    const cacheStats = this.cacheManager.getHitRate();
    this.performanceMonitor.updateCacheHitRate(cacheStats.hitRate);
  }

  // 实现AsyncRuleEngine接口的缺失方法
  async loadPlugin(plugin: any): Promise<void> {
    // 插件加载逻辑实现
    throw new Error('Plugin loading not implemented yet');
  }

  getAllRules(): any[] {
    return this.ruleRegistry.getAllRules();
  }

  enableRule(ruleId: string): void {
    this.ruleRegistry.enableRule(ruleId);
  }

  disableRule(ruleId: string): void {
    this.ruleRegistry.disableRule(ruleId);
  }
}