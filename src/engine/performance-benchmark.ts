/**
 * 性能基准测试和回归检测系统
 * 为内存和性能优化提供量化验证
 */

import type { AsyncRuleEngine, AnalysisContext, FileAnalysis } from './types';
import { performance } from 'perf_hooks';
import * as fs from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';

// 基准测试配置
export interface BenchmarkConfig {
  warmupRuns: number;           // 预热运行次数
  benchmarkRuns: number;       // 基准测试运行次数
  memoryMonitoring: boolean;   // 启用内存监控
  gcBetweenRuns: boolean;      // 运行间执行垃圾回收
  timeoutMs: number;           // 超时时间
  enableProfiling: boolean;    // 启用性能分析
  outputFile?: string;         // 结果输出文件
}

export const DEFAULT_BENCHMARK_CONFIG: BenchmarkConfig = {
  warmupRuns: 3,
  benchmarkRuns: 10,
  memoryMonitoring: true,
  gcBetweenRuns: true,
  timeoutMs: 60000, // 60秒
  enableProfiling: false,
};

// 性能指标
export interface PerformanceMetrics {
  executionTime: number;        // 执行时间(ms)
  memoryUsage: NodeJS.MemoryUsage;  // 内存使用
  cpuUsage: NodeJS.CpuUsage;   // CPU使用
  throughput: number;           // 吞吐量(operations/sec)
  cacheHitRate: number;         // 缓存命中率
  errorRate: number;            // 错误率
  customMetrics: Record<string, number>; // 自定义指标
}

// 基准测试结果
export interface BenchmarkResult {
  testName: string;
  config: BenchmarkConfig;
  timestamp: Date;
  metrics: {
    min: PerformanceMetrics;
    max: PerformanceMetrics;
    avg: PerformanceMetrics;
    median: PerformanceMetrics;
    p95: PerformanceMetrics;
    p99: PerformanceMetrics;
    stdDev: Partial<PerformanceMetrics>;
  };
  rawData: PerformanceMetrics[];
  environment: {
    nodeVersion: string;
    platform: string;
    arch: string;
    cpuCount: number;
    totalMemory: number;
  };
}

// 回归测试配置
export interface RegressionConfig {
  baselineFile: string;         // 基线文件路径
  tolerancePercent: number;     // 性能回归容忍度(%)
  criticalMetrics: string[];    // 关键指标列表
  autoSave: boolean;            // 自动保存基线
  failOnRegression: boolean;    // 回归时失败
}

export const DEFAULT_REGRESSION_CONFIG: RegressionConfig = {
  baselineFile: 'performance-baseline.json',
  tolerancePercent: 10, // 10%性能回归容忍度
  criticalMetrics: ['executionTime', 'memoryUsage.heapUsed', 'throughput'],
  autoSave: false,
  failOnRegression: true,
};

// 回归检测结果
export interface RegressionResult {
  testName: string;
  passed: boolean;
  regressions: Array<{
    metric: string;
    baseline: number;
    current: number;
    change: number;
    changePercent: number;
    isCritical: boolean;
  }>;
  improvements: Array<{
    metric: string;
    baseline: number;
    current: number;
    improvement: number;
    improvementPercent: number;
  }>;
  summary: {
    totalRegressions: number;
    criticalRegressions: number;
    totalImprovements: number;
    overallChangePercent: number;
  };
}

/**
 * 性能监控器
 */
class PerformanceProfiler {
  private startTime: number = 0;
  private startCpuUsage: NodeJS.CpuUsage = { user: 0, system: 0 };
  private startMemoryUsage: NodeJS.MemoryUsage;
  private customCounters = new Map<string, number>();

  constructor() {
    this.startMemoryUsage = process.memoryUsage();
  }

  start(): void {
    this.startTime = performance.now();
    this.startCpuUsage = process.cpuUsage();
    this.startMemoryUsage = process.memoryUsage();
    this.customCounters.clear();
  }

  addCustomMetric(name: string, value: number): void {
    this.customCounters.set(name, value);
  }

  incrementCounter(name: string, increment: number = 1): void {
    const current = this.customCounters.get(name) || 0;
    this.customCounters.set(name, current + increment);
  }

  end(): PerformanceMetrics {
    const endTime = performance.now();
    const endCpuUsage = process.cpuUsage(this.startCpuUsage);
    const endMemoryUsage = process.memoryUsage();

    const executionTime = endTime - this.startTime;
    const throughput = executionTime > 0 ? 1000 / executionTime : 0;

    const customMetrics: Record<string, number> = {};
    for (const [key, value] of this.customCounters.entries()) {
      customMetrics[key] = value;
    }

    return {
      executionTime,
      memoryUsage: endMemoryUsage,
      cpuUsage: endCpuUsage,
      throughput,
      cacheHitRate: customMetrics.cacheHitRate || 0,
      errorRate: customMetrics.errorRate || 0,
      customMetrics,
    };
  }
}

/**
 * 基准测试套件
 */
export class BenchmarkSuite extends EventEmitter {
  private config: BenchmarkConfig;
  private results = new Map<string, BenchmarkResult>();

  constructor(config: Partial<BenchmarkConfig> = {}) {
    super();
    this.config = { ...DEFAULT_BENCHMARK_CONFIG, ...config };
  }

  /**
   * 运行单个基准测试
   */
  async runBenchmark(
    testName: string,
    testFunction: () => Promise<void>,
    customConfig?: Partial<BenchmarkConfig>
  ): Promise<BenchmarkResult> {
    const finalConfig = { ...this.config, ...customConfig };
    
    this.emit('benchmark-started', { testName, config: finalConfig });
    
    const rawData: PerformanceMetrics[] = [];
    const profiler = new PerformanceProfiler();

    try {
      // 预热阶段
      this.emit('warmup-started', { testName, runs: finalConfig.warmupRuns });
      for (let i = 0; i < finalConfig.warmupRuns; i++) {
        if (finalConfig.gcBetweenRuns && global.gc) {
          global.gc();
        }
        await testFunction();
      }
      this.emit('warmup-completed', { testName });

      // 基准测试阶段
      this.emit('benchmark-runs-started', { testName, runs: finalConfig.benchmarkRuns });
      for (let i = 0; i < finalConfig.benchmarkRuns; i++) {
        if (finalConfig.gcBetweenRuns && global.gc) {
          global.gc();
          await this.sleep(100); // 给GC时间
        }

        const timeout = this.createTimeout(finalConfig.timeoutMs);
        
        try {
          profiler.start();
          
          await Promise.race([
            testFunction(),
            timeout.promise,
          ]);
          
          const metrics = profiler.end();
          rawData.push(metrics);
          
          this.emit('benchmark-run-completed', { 
            testName, 
            runIndex: i, 
            metrics 
          });
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (errorMessage === 'TIMEOUT') {
            throw new Error(`Benchmark timeout after ${finalConfig.timeoutMs}ms`);
          }
          throw error;
        } finally {
          timeout.clear();
        }
      }

      // 计算统计结果
      const result = this.calculateStatistics(testName, finalConfig, rawData);
      this.results.set(testName, result);
      
      // 保存结果
      if (finalConfig.outputFile) {
        await this.saveResults(finalConfig.outputFile);
      }
      
      this.emit('benchmark-completed', { testName, result });
      return result;
      
    } catch (error) {
      this.emit('benchmark-error', { testName, error });
      throw error;
    }
  }

  /**
   * 运行内存基准测试
   */
  async runMemoryBenchmark(
    testName: string,
    testFunction: (profiler: PerformanceProfiler) => Promise<void>
  ): Promise<BenchmarkResult> {
    return this.runBenchmark(
      testName,
      async () => {
        const profiler = new PerformanceProfiler();
        await testFunction(profiler);
      },
      { memoryMonitoring: true, gcBetweenRuns: true }
    );
  }

  /**
   * 运行并发基准测试
   */
  async runConcurrencyBenchmark(
    testName: string,
    testFunction: () => Promise<void>,
    concurrencyLevels: number[]
  ): Promise<Map<number, BenchmarkResult>> {
    const results = new Map<number, BenchmarkResult>();
    
    for (const concurrency of concurrencyLevels) {
      const concurrentTestName = `${testName}-concurrency-${concurrency}`;
      
      const result = await this.runBenchmark(
        concurrentTestName,
        async () => {
          const promises = Array(concurrency).fill(null).map(() => testFunction());
          await Promise.all(promises);
        }
      );
      
      results.set(concurrency, result);
    }
    
    return results;
  }

  /**
   * 运行负载基准测试
   */
  async runLoadBenchmark(
    testName: string,
    testFunction: (loadSize: number) => Promise<void>,
    loadSizes: number[]
  ): Promise<Map<number, BenchmarkResult>> {
    const results = new Map<number, BenchmarkResult>();
    
    for (const loadSize of loadSizes) {
      const loadTestName = `${testName}-load-${loadSize}`;
      
      const result = await this.runBenchmark(
        loadTestName,
        () => testFunction(loadSize)
      );
      
      results.set(loadSize, result);
    }
    
    return results;
  }

  /**
   * 比较两个基准测试结果
   */
  compareResults(baseline: BenchmarkResult, current: BenchmarkResult): {
    executionTimeChange: number;
    memoryUsageChange: number;
    throughputChange: number;
    improvements: string[];
    regressions: string[];
  } {
    const executionTimeChange = 
      (current.metrics.avg.executionTime - baseline.metrics.avg.executionTime) / 
      baseline.metrics.avg.executionTime * 100;
    
    const memoryUsageChange = 
      (current.metrics.avg.memoryUsage.heapUsed - baseline.metrics.avg.memoryUsage.heapUsed) / 
      baseline.metrics.avg.memoryUsage.heapUsed * 100;
    
    const throughputChange = 
      (current.metrics.avg.throughput - baseline.metrics.avg.throughput) / 
      baseline.metrics.avg.throughput * 100;
    
    const improvements: string[] = [];
    const regressions: string[] = [];
    
    if (executionTimeChange < -5) improvements.push('execution time');
    if (executionTimeChange > 10) regressions.push('execution time');
    
    if (memoryUsageChange < -5) improvements.push('memory usage');
    if (memoryUsageChange > 15) regressions.push('memory usage');
    
    if (throughputChange > 5) improvements.push('throughput');
    if (throughputChange < -10) regressions.push('throughput');
    
    return {
      executionTimeChange,
      memoryUsageChange,
      throughputChange,
      improvements,
      regressions,
    };
  }

  /**
   * 获取所有基准测试结果
   */
  getAllResults(): Map<string, BenchmarkResult> {
    return new Map(this.results);
  }

  /**
   * 生成性能报告
   */
  generateReport(): {
    summary: {
      totalTests: number;
      avgExecutionTime: number;
      avgMemoryUsage: number;
      avgThroughput: number;
    };
    detailed: BenchmarkResult[];
  } {
    const results = Array.from(this.results.values());
    
    const summary = {
      totalTests: results.length,
      avgExecutionTime: results.reduce((sum, r) => sum + r.metrics.avg.executionTime, 0) / results.length,
      avgMemoryUsage: results.reduce((sum, r) => sum + r.metrics.avg.memoryUsage.heapUsed, 0) / results.length,
      avgThroughput: results.reduce((sum, r) => sum + r.metrics.avg.throughput, 0) / results.length,
    };
    
    return { summary, detailed: results };
  }

  private calculateStatistics(
    testName: string,
    config: BenchmarkConfig,
    rawData: PerformanceMetrics[]
  ): BenchmarkResult {
    const sortedExecutionTimes = rawData.map(d => d.executionTime).sort((a, b) => a - b);
    const sortedMemoryUsage = rawData.map(d => d.memoryUsage.heapUsed).sort((a, b) => a - b);
    const sortedThroughput = rawData.map(d => d.throughput).sort((a, b) => a - b);
    
    const getPercentile = (sorted: number[], percentile: number): number => {
      const index = Math.floor((percentile / 100) * sorted.length);
      return sorted[Math.min(index, sorted.length - 1)] ?? 0;
    };
    
    const calculateMetrics = (data: PerformanceMetrics[]): PerformanceMetrics => ({
      executionTime: data.reduce((sum, d) => sum + d.executionTime, 0) / data.length,
      memoryUsage: {
        rss: data.reduce((sum, d) => sum + d.memoryUsage.rss, 0) / data.length,
        heapTotal: data.reduce((sum, d) => sum + d.memoryUsage.heapTotal, 0) / data.length,
        heapUsed: data.reduce((sum, d) => sum + d.memoryUsage.heapUsed, 0) / data.length,
        external: data.reduce((sum, d) => sum + d.memoryUsage.external, 0) / data.length,
        arrayBuffers: data.reduce((sum, d) => sum + d.memoryUsage.arrayBuffers, 0) / data.length,
      },
      cpuUsage: {
        user: data.reduce((sum, d) => sum + d.cpuUsage.user, 0) / data.length,
        system: data.reduce((sum, d) => sum + d.cpuUsage.system, 0) / data.length,
      },
      throughput: data.reduce((sum, d) => sum + d.throughput, 0) / data.length,
      cacheHitRate: data.reduce((sum, d) => sum + d.cacheHitRate, 0) / data.length,
      errorRate: data.reduce((sum, d) => sum + d.errorRate, 0) / data.length,
      customMetrics: {},
    });
    
    return {
      testName,
      config,
      timestamp: new Date(),
      metrics: {
        min: rawData.reduce((min, current) => 
          current.executionTime < min.executionTime ? current : min
        ),
        max: rawData.reduce((max, current) => 
          current.executionTime > max.executionTime ? current : max
        ),
        avg: calculateMetrics(rawData),
        median: rawData[Math.floor(rawData.length / 2)] ?? this.getDefaultMetrics(),
        p95: rawData[Math.floor(rawData.length * 0.95)] ?? this.getDefaultMetrics(),
        p99: rawData[Math.floor(rawData.length * 0.99)] ?? this.getDefaultMetrics(),
        stdDev: this.calculateStandardDeviation(rawData),
      },
      rawData,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        cpuCount: require('os').cpus().length,
        totalMemory: require('os').totalmem(),
      },
    };
  }

  private calculateStandardDeviation(data: PerformanceMetrics[]): Partial<PerformanceMetrics> {
    const avg = data.reduce((sum, d) => sum + d.executionTime, 0) / data.length;
    const variance = data.reduce((sum, d) => sum + Math.pow(d.executionTime - avg, 2), 0) / data.length;
    
    return {
      executionTime: Math.sqrt(variance),
      // 可以添加更多指标的标准差计算
    };
  }

  private getDefaultMetrics(): PerformanceMetrics {
    return {
      executionTime: 0,
      memoryUsage: {
        heapUsed: 0,
        heapTotal: 0,
        external: 0,
        rss: 0
      },
      throughput: 0,
      cpuUsage: {
        user: 0,
        system: 0
      }
    };
  }

  private createTimeout(ms: number): { promise: Promise<never>; clear: () => void } {
    let timeoutId: NodeJS.Timeout;
    
    const promise = new Promise<never>((_, reject) => {
      timeoutId = setTimeout(() => reject(new Error('TIMEOUT')), ms);
    });
    
    return {
      promise,
      clear: () => clearTimeout(timeoutId),
    };
  }

  private async saveResults(filename: string): Promise<void> {
    const results = Object.fromEntries(this.results);
    const json = JSON.stringify(results, null, 2);
    await fs.promises.writeFile(filename, json, 'utf-8');
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 回归测试器
 */
export class RegressionTester {
  private config: RegressionConfig;

  constructor(config: Partial<RegressionConfig> = {}) {
    this.config = { ...DEFAULT_REGRESSION_CONFIG, ...config };
  }

  /**
   * 运行回归测试
   */
  async runRegressionTest(
    testName: string,
    currentResult: BenchmarkResult
  ): Promise<RegressionResult> {
    const baseline = await this.loadBaseline(testName);
    
    if (!baseline) {
      // 如果没有基线，保存当前结果作为基线
      if (this.config.autoSave) {
        await this.saveBaseline(testName, currentResult);
      }
      
      return {
        testName,
        passed: true,
        regressions: [],
        improvements: [],
        summary: {
          totalRegressions: 0,
          criticalRegressions: 0,
          totalImprovements: 0,
          overallChangePercent: 0,
        },
      };
    }
    
    return this.compareWithBaseline(testName, baseline, currentResult);
  }

  /**
   * 更新基线
   */
  async updateBaseline(testName: string, result: BenchmarkResult): Promise<void> {
    await this.saveBaseline(testName, result);
  }

  /**
   * 批量回归测试
   */
  async runBatchRegressionTest(
    results: Map<string, BenchmarkResult>
  ): Promise<Map<string, RegressionResult>> {
    const regressionResults = new Map<string, RegressionResult>();
    
    for (const [testName, result] of results) {
      const regressionResult = await this.runRegressionTest(testName, result);
      regressionResults.set(testName, regressionResult);
    }
    
    return regressionResults;
  }

  private async loadBaseline(testName: string): Promise<BenchmarkResult | null> {
    try {
      const baselineData = await fs.promises.readFile(this.config.baselineFile, 'utf-8');
      const baselines = JSON.parse(baselineData);
      return baselines[testName] || null;
    } catch (error) {
      return null;
    }
  }

  private async saveBaseline(testName: string, result: BenchmarkResult): Promise<void> {
    let baselines: Record<string, BenchmarkResult> = {};
    
    try {
      const existingData = await fs.promises.readFile(this.config.baselineFile, 'utf-8');
      baselines = JSON.parse(existingData);
    } catch (error) {
      // 文件不存在或无效，使用空对象
    }
    
    baselines[testName] = result;
    
    const json = JSON.stringify(baselines, null, 2);
    await fs.promises.writeFile(this.config.baselineFile, json, 'utf-8');
  }

  private compareWithBaseline(
    testName: string,
    baseline: BenchmarkResult,
    current: BenchmarkResult
  ): RegressionResult {
    const regressions: RegressionResult['regressions'] = [];
    const improvements: RegressionResult['improvements'] = [];
    
    // 检查关键指标
    for (const metricPath of this.config.criticalMetrics) {
      const baselineValue = this.getNestedValue(baseline.metrics.avg, metricPath);
      const currentValue = this.getNestedValue(current.metrics.avg, metricPath);
      
      if (baselineValue && currentValue && baselineValue > 0) {
        const change = currentValue - baselineValue;
        const changePercent = (change / baselineValue) * 100;
        
        if (Math.abs(changePercent) > this.config.tolerancePercent) {
          if (changePercent > 0) {
            // 性能回归
            regressions.push({
              metric: metricPath,
              baseline: baselineValue,
              current: currentValue,
              change,
              changePercent,
              isCritical: true,
            });
          } else {
            // 性能改进
            improvements.push({
              metric: metricPath,
              baseline: baselineValue,
              current: currentValue,
              improvement: Math.abs(change),
              improvementPercent: Math.abs(changePercent),
            });
          }
        }
      }
    }
    
    const totalRegressions = regressions.length;
    const criticalRegressions = regressions.filter(r => r.isCritical).length;
    const totalImprovements = improvements.length;
    
    const overallChangePercent = regressions.length > 0 
      ? regressions.reduce((sum, r) => sum + r.changePercent, 0) / regressions.length
      : 0;
    
    const passed = this.config.failOnRegression ? criticalRegressions === 0 : true;
    
    return {
      testName,
      passed,
      regressions,
      improvements,
      summary: {
        totalRegressions,
        criticalRegressions,
        totalImprovements,
        overallChangePercent,
      },
    };
  }

  private getNestedValue(obj: any, path: string): number | null {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return null;
      }
    }
    
    return typeof current === 'number' ? current : null;
  }
}

/**
 * 集成测试套件
 */
export class IntegratedPerformanceTest {
  private benchmarkSuite: BenchmarkSuite;
  private regressionTester: RegressionTester;

  constructor(
    benchmarkConfig: Partial<BenchmarkConfig> = {},
    regressionConfig: Partial<RegressionConfig> = {}
  ) {
    this.benchmarkSuite = new BenchmarkSuite(benchmarkConfig);
    this.regressionTester = new RegressionTester(regressionConfig);
  }

  /**
   * 运行完整的性能测试和回归检测
   */
  async runFullTest(
    testName: string,
    testFunction: () => Promise<void>
  ): Promise<{
    benchmark: BenchmarkResult;
    regression: RegressionResult;
  }> {
    // 运行基准测试
    const benchmark = await this.benchmarkSuite.runBenchmark(testName, testFunction);
    
    // 运行回归测试
    const regression = await this.regressionTester.runRegressionTest(testName, benchmark);
    
    return { benchmark, regression };
  }

  /**
   * 运行引擎性能测试
   */
  async testEnginePerformance(
    engine: AsyncRuleEngine,
    testFiles: string[]
  ): Promise<Map<string, { benchmark: BenchmarkResult; regression: RegressionResult }>> {
    const results = new Map();
    
    for (const filePath of testFiles) {
      const testName = `engine-performance-${path.basename(filePath)}`;
      
      const result = await this.runFullTest(testName, async () => {
        // 模拟文件分析
        const code = await fs.promises.readFile(filePath, 'utf-8');
        await engine.analyzeCode(code, filePath);
      });
      
      results.set(filePath, result);
    }
    
    return results;
  }

  /**
   * 生成完整性能报告
   */
  generateComprehensiveReport(): {
    benchmarks: ReturnType<BenchmarkSuite['generateReport']>;
    summary: {
      totalTests: number;
      passedRegressions: number;
      failedRegressions: number;
      totalImprovements: number;
    };
  } {
    const benchmarks = this.benchmarkSuite.generateReport();
    
    // 这里需要收集回归测试结果的摘要
    // 简化实现
    return {
      benchmarks,
      summary: {
        totalTests: benchmarks.summary.totalTests,
        passedRegressions: 0,
        failedRegressions: 0,
        totalImprovements: 0,
      },
    };
  }
}

// 便捷函数
export function createBenchmarkSuite(config?: Partial<BenchmarkConfig>): BenchmarkSuite {
  return new BenchmarkSuite(config);
}

export function createRegressionTester(config?: Partial<RegressionConfig>): RegressionTester {
  return new RegressionTester(config);
}

export function createIntegratedTest(
  benchmarkConfig?: Partial<BenchmarkConfig>,
  regressionConfig?: Partial<RegressionConfig>
): IntegratedPerformanceTest {
  return new IntegratedPerformanceTest(benchmarkConfig, regressionConfig);
}