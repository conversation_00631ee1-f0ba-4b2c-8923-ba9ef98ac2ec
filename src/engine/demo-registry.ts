/**
 * 规则注册和发现系统演示
 * 展示完整的规则管理功能
 */

import { RuleRegistryImpl } from '../engine/registry';
import { RuleSetManager } from '../rules/rule-sets';
import type { Node } from '@swc/core';

async function demoRuleRegistrySystem() {
  console.log('🚀 规则注册和发现系统演示\n');

  // 创建注册表实例
  const registry = new RuleRegistryImpl();

  console.log('📋 1. 注册默认规则集...');
  RuleSetManager.registerAllToEngine(registry);

  console.log('\n📊 2. 查看注册表统计信息:');
  const stats = registry.getStatistics();
  console.log(`- 总注册规则数: ${stats.totalRegistered}`);
  console.log(`- 启用规则数: ${stats.enabled}`);
  console.log(`- 禁用规则数: ${stats.disabled}`);
  console.log(`- 按类别分布:`, stats.byCategory);
  console.log(`- 冲突数量: ${stats.conflicts}`);

  console.log('\n🔍 3. 规则发现演示:');
  
  // 模拟不同类型的AST节点
  const ifNode: Node = { type: 'IfStatement', span: { start: 0, end: 10, ctxt: 0 } } as any;
  const jsxNode: Node = { type: 'JSXElement', span: { start: 0, end: 10, ctxt: 0 } } as any;
  const logicalNode: Node = { type: 'LogicalExpression', span: { start: 0, end: 10, ctxt: 0 } } as any;

  console.log('\n  📍 针对 IfStatement 节点:');
  const ifRules = registry.getRulesForNode(ifNode);
  ifRules.forEach(rule => {
    console.log(`    - ${rule.name} (${rule.id}) - 优先级: ${rule.priority}`);
  });

  console.log('\n  📍 针对 JSXElement 节点:');
  const jsxRules = registry.getRulesForNode(jsxNode);
  jsxRules.forEach(rule => {
    console.log(`    - ${rule.name} (${rule.id}) - 优先级: ${rule.priority}`);
  });

  console.log('\n  📍 针对 LogicalExpression 节点:');
  const logicalRules = registry.getRulesForNode(logicalNode);
  logicalRules.forEach(rule => {
    console.log(`    - ${rule.name} (${rule.id}) - 优先级: ${rule.priority}`);
  });

  console.log('\n🏷️ 4. 按类别获取规则:');
  const categories = ['core', 'jsx', 'logical', 'plugin'];
  categories.forEach(category => {
    const categoryRules = registry.getRulesByCategory(category as any);
    console.log(`  ${category}: ${categoryRules.length} 个规则`);
    categoryRules.forEach(rule => {
      console.log(`    - ${rule.name} (${rule.id})`);
    });
  });

  console.log('\n📈 5. 按优先级排序的规则:');
  const rulesByPriority = registry.getRulesByPriority();
  rulesByPriority.forEach((rule, index) => {
    console.log(`  ${index + 1}. ${rule.name} (${rule.id}) - 优先级: ${rule.priority}`);
  });

  console.log('\n🔧 6. 规则状态管理演示:');
  const ruleToDisable = rulesByPriority[0];
  if (!ruleToDisable) {
    console.log('  没有可用的规则进行演示');
    return;
  }
  console.log(`  禁用规则: ${ruleToDisable.name}`);
  registry.disableRule(ruleToDisable.id);
  
  console.log(`  规则 ${ruleToDisable.id} 状态: ${registry.isRuleEnabled(ruleToDisable.id) ? '启用' : '禁用'}`);
  
  console.log(`  重新启用规则: ${ruleToDisable.name}`);
  registry.enableRule(ruleToDisable.id);
  console.log(`  规则 ${ruleToDisable.id} 状态: ${registry.isRuleEnabled(ruleToDisable.id) ? '启用' : '禁用'}`);

  console.log('\n⚠️ 7. 冲突检测演示:');
  const conflicts = registry.detectConflicts();
  if (conflicts.length > 0) {
    console.log(`  发现 ${conflicts.length} 个冲突:`);
    conflicts.forEach(conflict => {
      console.log(`    - ${conflict.type} 冲突: ${conflict.description}`);
      console.log(`      涉及规则: ${conflict.involvedRules.join(', ')}`);
      console.log(`      严重程度: ${conflict.severity}`);
    });
  } else {
    console.log('  ✅ 未发现规则冲突');
  }

  console.log('\n🔍 8. 依赖解析演示:');
  const allRules = registry.getAllRules();
  allRules.forEach(rule => {
    const dependencies = rule.getDependencies();
    if (dependencies.length > 0) {
      console.log(`  规则 ${rule.id} 依赖: ${dependencies.join(', ')}`);
      const validation = registry.validateDependencies(rule.id);
      if (!validation.isValid) {
        console.log(`    ❌ 依赖验证失败:`);
        validation.missingDependencies.forEach(dep => {
          console.log(`      - 缺失依赖: ${dep}`);
        });
        validation.circularDependencies.forEach(dep => {
          console.log(`      - 循环依赖: ${dep}`);
        });
      } else {
        console.log(`    ✅ 依赖验证通过`);
      }
    }
  });

  console.log('\n🎯 9. 验证规则集完整性:');
  const validation = RuleSetManager.validateRuleSet();
  if (validation.isValid) {
    console.log('  ✅ 规则集验证通过');
  } else {
    console.log('  ❌ 规则集验证失败:');
    validation.issues.forEach(issue => {
      console.log(`    - ${issue}`);
    });
  }

  console.log('\n📊 10. 最终统计信息:');
  const finalStats = registry.getStatistics();
  console.log('  规则分布统计:');
  console.log(`    - 总计: ${finalStats.totalRegistered} 个规则`);
  console.log(`    - 高优先级 (≥800): ${Object.entries(finalStats.byPriority).filter(([p]) => parseInt(p) >= 800).reduce((sum, [, count]) => sum + count, 0)} 个`);
  console.log(`    - 中优先级 (500-799): ${Object.entries(finalStats.byPriority).filter(([p]) => parseInt(p) >= 500 && parseInt(p) < 800).reduce((sum, [, count]) => sum + count, 0)} 个`);
  console.log(`    - 低优先级 (<500): ${Object.entries(finalStats.byPriority).filter(([p]) => parseInt(p) < 500).reduce((sum, [, count]) => sum + count, 0)} 个`);

  console.log('\n✅ 规则注册和发现系统演示完成！');
}

// 执行演示
if (require.main === module) {
  demoRuleRegistrySystem().catch(error => {
    console.error('演示过程中发生错误:', error);
    process.exit(1);
  });
}

export { demoRuleRegistrySystem };