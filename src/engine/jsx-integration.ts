/**
 * 规则引擎集成器
 * 将JSX规则集成到现有的AsyncRuleEngine中
 */

import { AsyncRuleEngineImpl } from '../engine/async-engine';
import { JSXStructuralExemptionRule } from '../rules/jsx/structural-exemption';
import type { ResolvedEngineConfig } from '../engine/types';

/**
 * 创建集成了JSX规则的引擎实例
 */
export function createEngineWithJSXRules(config?: Partial<ResolvedEngineConfig>): AsyncRuleEngineImpl {
  // 创建引擎实例
  const engine = new AsyncRuleEngineImpl(config);
  
  // 注册JSX结构豁免规则
  const jsxStructuralRule = new JSXStructuralExemptionRule();
  engine.registerRule(jsxStructuralRule);
  
  console.log('JSX Structural Exemption Rule registered with engine');
  
  return engine;
}

/**
 * 为现有引擎添加JSX规则
 */
export function addJSXRulesToEngine(engine: AsyncRuleEngineImpl): void {
  // 注册JSX结构豁免规则
  const jsxStructuralRule = new JSXStructuralExemptionRule();
  engine.registerRule(jsxStructuralRule);
  
  console.log('JSX rules added to existing engine');
}

/**
 * 获取JSX规则的默认配置
 */
export function getJSXRuleConfig() {
  return {
    rules: {
      jsx: {
        enabled: true,
        exemptions: {
          structuralNodes: true,        // 启用结构节点豁免
          attributeExpressions: true,   // 启用属性表达式豁免  
          nullishCoalescing: true,      // 启用空值合并豁免
          simpleConditionals: true,     // 启用简单条件豁免
        },
        scoring: {
          conditionalRendering: true,   // 启用条件渲染计分
          eventHandlers: true,         // 启用事件处理器计分
          loopRendering: true,         // 启用循环渲染计分
          nestedComponents: true,      // 启用嵌套组件计分
        },
        advanced: {
          detectHookComplexity: false, // 暂未实现
          analyzeProps: false,         // 暂未实现
          trackContextUsage: false,    // 暂未实现
        },
      },
    },
  };
}