/**
 * 规则注册和发现系统
 * 支持动态规则加载、依赖解析和冲突检测
 */

import type { Node } from '@swc/core';
import type { Rule, AsyncRuleEngine } from '../engine/types';

// 规则注册表接口
export interface RuleRegistry {
  // 规则管理
  registerRule(rule: Rule, quiet?: boolean): void;
  unregisterRule(ruleId: string): void;
  getRule(ruleId: string): Rule | null;
  getAllRules(): Rule[];
  hasRule(ruleId: string): boolean;
  
  // 规则发现
  getRulesForNode(node: Node): Rule[];
  getRulesByPriority(): Rule[];
  getRulesByCategory(category: string): Rule[];
  
  // 依赖管理
  resolveDependencies(ruleId: string): Rule[];
  validateDependencies(ruleId: string): DependencyValidationResult;
  
  // 插件管理
  loadPlugin(pluginPath: string): Promise<void>;
  unloadPlugin(pluginId: string): void;
  getLoadedPlugins(): LoadedPlugin[];
  
  // 规则状态
  isRuleEnabled(ruleId: string): boolean;
  enableRule(ruleId: string): void;
  disableRule(ruleId: string): void;
  
  // 冲突检测
  detectConflicts(): RuleConflict[];
  resolveConflict(conflictId: string, resolution: ConflictResolution): void;
}

// 依赖验证结果
export interface DependencyValidationResult {
  isValid: boolean;
  missingDependencies: string[];
  circularDependencies: string[];
  suggestions: string[];
}

// 加载的插件
export interface LoadedPlugin {
  id: string;
  name: string;
  version: string;
  path: string;
  rules: Rule[];
  isActive: boolean;
  loadTime: number;
  metadata?: PluginMetadata;
}

// 插件元数据
export interface PluginMetadata {
  author?: string;
  description?: string;
  homepage?: string;
  keywords?: string[];
  dependencies?: string[];
  peerDependencies?: string[];
}

// 规则冲突
export interface RuleConflict {
  id: string;
  type: 'priority' | 'functionality' | 'dependency';
  description: string;
  involvedRules: string[];
  severity: 'low' | 'medium' | 'high' | 'critical';
  suggestedResolution?: ConflictResolution;
}

// 冲突解决方案
export interface ConflictResolution {
  type: 'disable' | 'reorder' | 'configure' | 'merge';
  target: string;
  parameters?: Record<string, any>;
}

// 规则分类
export interface RuleCategory {
  id: string;
  name: string;
  description: string;
  rules: string[];
  priority: number;
}

// 规则统计
export interface RuleStatistics {
  totalRegistered: number;
  enabled: number;
  disabled: number;
  byCategory: Record<string, number>;
  byPriority: Record<number, number>;
  conflicts: number;
  pluginRules: number;
}

/**
 * 规则注册表实现
 */
export class RuleRegistryImpl implements RuleRegistry {
  private rules = new Map<string, Rule>();
  private enabledRules = new Set<string>();
  private disabledRules = new Set<string>();
  private loadedPlugins = new Map<string, LoadedPlugin>();
  private categories = new Map<string, RuleCategory>();
  private conflicts: RuleConflict[] = [];
  private dependencyGraph = new Map<string, string[]>();

  constructor() {
    this.initializeCategories();
  }

  registerRule(rule: Rule, quiet: boolean = false): void {
    // 验证规则
    this.validateRule(rule);
    
    // 检查是否已存在
    if (this.rules.has(rule.id)) {
      throw new Error(`Rule with id '${rule.id}' is already registered`);
    }
    
    // 注册规则
    this.rules.set(rule.id, rule);
    this.enabledRules.add(rule.id);
    
    // 构建依赖图
    this.dependencyGraph.set(rule.id, rule.getDependencies());
    
    // 分配到类别
    this.assignRuleToCategory(rule);
    
    // 检测冲突
    this.detectAndRecordConflicts(rule);
    
    if (!quiet) {
      console.log(`Rule '${rule.id}' registered successfully`);
    }
  }

  unregisterRule(ruleId: string): void {
    const rule = this.rules.get(ruleId);
    if (!rule) {
      throw new Error(`Rule with id '${ruleId}' not found`);
    }
    
    // 检查依赖关系
    const dependents = this.findDependentRules(ruleId);
    if (dependents.length > 0) {
      throw new Error(`Cannot unregister rule '${ruleId}': it is depended on by rules: ${dependents.join(', ')}`);
    }
    
    // 移除规则
    this.rules.delete(ruleId);
    this.enabledRules.delete(ruleId);
    this.disabledRules.delete(ruleId);
    this.dependencyGraph.delete(ruleId);
    
    // 从类别中移除
    this.removeRuleFromCategories(ruleId);
    
    // 移除相关冲突
    this.conflicts = this.conflicts.filter(conflict => 
      !conflict.involvedRules.includes(ruleId)
    );
    
    console.log(`Rule '${ruleId}' unregistered successfully`);
  }

  getRule(ruleId: string): Rule | null {
    return this.rules.get(ruleId) || null;
  }

  hasRule(ruleId: string): boolean {
    return this.rules.has(ruleId);
  }

  getAllRules(): Rule[] {
    return Array.from(this.rules.values());
  }

  getRulesForNode(node: Node): Rule[] {
    const applicableRules: Rule[] = [];
    
    for (const rule of this.rules.values()) {
      if (this.isRuleEnabled(rule.id) && rule.canHandle(node)) {
        applicableRules.push(rule);
      }
    }
    
    // 按优先级排序
    return applicableRules.sort((a, b) => b.priority - a.priority);
  }

  getRulesByPriority(): Rule[] {
    return Array.from(this.rules.values())
      .filter(rule => this.isRuleEnabled(rule.id))
      .sort((a, b) => b.priority - a.priority);
  }

  getRulesByCategory(categoryId: string): Rule[] {
    const category = this.categories.get(categoryId);
    if (!category) {
      return [];
    }
    
    return category.rules
      .map(ruleId => this.rules.get(ruleId))
      .filter((rule): rule is Rule => rule !== undefined)
      .filter(rule => this.isRuleEnabled(rule.id));
  }

  resolveDependencies(ruleId: string): Rule[] {
    const dependencies = this.dependencyGraph.get(ruleId) || [];
    const resolvedRules: Rule[] = [];
    const visited = new Set<string>();
    const visiting = new Set<string>();
    
    const resolve = (id: string): void => {
      if (visited.has(id)) return;
      if (visiting.has(id)) {
        throw new Error(`Circular dependency detected involving rule '${id}'`);
      }
      
      visiting.add(id);
      
      const ruleDeps = this.dependencyGraph.get(id) || [];
      for (const depId of ruleDeps) {
        resolve(depId);
      }
      
      visiting.delete(id);
      visited.add(id);
      
      const rule = this.rules.get(id);
      if (rule) {
        resolvedRules.push(rule);
      }
    };
    
    for (const depId of dependencies) {
      resolve(depId);
    }
    
    return resolvedRules;
  }

  validateDependencies(ruleId: string): DependencyValidationResult {
    const dependencies = this.dependencyGraph.get(ruleId) || [];
    const missingDependencies: string[] = [];
    const circularDependencies: string[] = [];
    
    // 检查缺失的依赖
    for (const depId of dependencies) {
      if (!this.rules.has(depId)) {
        missingDependencies.push(depId);
      }
    }
    
    // 检查循环依赖
    try {
      this.resolveDependencies(ruleId);
    } catch (error) {
      if (error instanceof Error && error.message.includes('Circular dependency')) {
        circularDependencies.push(ruleId);
      }
    }
    
    const isValid = missingDependencies.length === 0 && circularDependencies.length === 0;
    
    return {
      isValid,
      missingDependencies,
      circularDependencies,
      suggestions: this.generateDependencySuggestions(ruleId, missingDependencies),
    };
  }

  async loadPlugin(pluginPath: string, quiet: boolean = false): Promise<void> {
    try {
      // 动态导入插件
      const pluginModule = await import(pluginPath);
      const plugin = pluginModule.default || pluginModule;
      
      if (!this.isValidPlugin(plugin)) {
        throw new Error(`Invalid plugin at '${pluginPath}': must export a valid plugin object`);
      }
      
      // 检查插件是否已加载
      if (this.loadedPlugins.has(plugin.id)) {
        throw new Error(`Plugin '${plugin.id}' is already loaded`);
      }
      
      // 注册插件规则
      const loadedRules: Rule[] = [];
      for (const rule of plugin.rules) {
        try {
          this.registerRule(rule, quiet);
          loadedRules.push(rule);
        } catch (error) {
          console.warn(`Failed to register rule '${rule.id}' from plugin '${plugin.id}':`, error);
        }
      }
      
      // 记录插件信息
      const loadedPlugin: LoadedPlugin = {
        id: plugin.id,
        name: plugin.name || plugin.id,
        version: plugin.version || '1.0.0',
        path: pluginPath,
        rules: loadedRules,
        isActive: true,
        loadTime: Date.now(),
        metadata: plugin.metadata,
      };
      
      this.loadedPlugins.set(plugin.id, loadedPlugin);
      
      // 调用插件的加载钩子
      if (typeof plugin.onLoad === 'function') {
        await plugin.onLoad();
      }
      
      if (!quiet) {
        console.log(`Plugin '${plugin.id}' loaded successfully with ${loadedRules.length} rules`);
      }
    } catch (error) {
      throw new Error(`Failed to load plugin from '${pluginPath}': ${error}`);
    }
  }

  unloadPlugin(pluginId: string): void {
    const plugin = this.loadedPlugins.get(pluginId);
    if (!plugin) {
      throw new Error(`Plugin '${pluginId}' is not loaded`);
    }
    
    // 卸载插件规则
    for (const rule of plugin.rules) {
      try {
        this.unregisterRule(rule.id);
      } catch (error) {
        console.warn(`Failed to unregister rule '${rule.id}' from plugin '${pluginId}':`, error);
      }
    }
    
    // 移除插件记录
    this.loadedPlugins.delete(pluginId);
    
    console.log(`Plugin '${pluginId}' unloaded successfully`);
  }

  getLoadedPlugins(): LoadedPlugin[] {
    return Array.from(this.loadedPlugins.values());
  }

  isRuleEnabled(ruleId: string): boolean {
    return this.enabledRules.has(ruleId) && !this.disabledRules.has(ruleId);
  }

  enableRule(ruleId: string): void {
    if (!this.rules.has(ruleId)) {
      throw new Error(`Rule '${ruleId}' is not registered`);
    }
    this.enabledRules.add(ruleId);
    this.disabledRules.delete(ruleId);
  }

  disableRule(ruleId: string): void {
    if (!this.rules.has(ruleId)) {
      throw new Error(`Rule '${ruleId}' is not registered`);
    }
    this.disabledRules.add(ruleId);
  }

  detectConflicts(): RuleConflict[] {
    return [...this.conflicts];
  }

  resolveConflict(conflictId: string, resolution: ConflictResolution): void {
    const conflictIndex = this.conflicts.findIndex(c => c.id === conflictId);
    if (conflictIndex === -1) {
      throw new Error(`Conflict '${conflictId}' not found`);
    }
    
    const conflict = this.conflicts[conflictIndex];
    if (!conflict) {
      throw new Error(`Conflict '${conflictId}' not found in conflicts array`);
    }
    
    try {
      this.applyConflictResolution(conflict, resolution);
      this.conflicts.splice(conflictIndex, 1);
      console.log(`Conflict '${conflictId}' resolved successfully`);
    } catch (error) {
      throw new Error(`Failed to resolve conflict '${conflictId}': ${error}`);
    }
  }

  // 获取规则统计信息
  getStatistics(): RuleStatistics {
    const byCategory: Record<string, number> = {};
    const byPriority: Record<number, number> = {};
    
    for (const rule of this.rules.values()) {
      // 按类别统计
      const category = this.findRuleCategory(rule.id);
      if (category) {
        byCategory[category] = (byCategory[category] || 0) + 1;
      }
      
      // 按优先级统计
      byPriority[rule.priority] = (byPriority[rule.priority] || 0) + 1;
    }
    
    return {
      totalRegistered: this.rules.size,
      enabled: this.enabledRules.size,
      disabled: this.disabledRules.size,
      byCategory,
      byPriority,
      conflicts: this.conflicts.length,
      pluginRules: Array.from(this.loadedPlugins.values())
        .reduce((sum, plugin) => sum + plugin.rules.length, 0),
    };
  }

  private validateRule(rule: Rule): void {
    if (!rule.id || typeof rule.id !== 'string') {
      throw new Error('Rule must have a valid string id');
    }
    
    if (!rule.name || typeof rule.name !== 'string') {
      throw new Error('Rule must have a valid string name');
    }
    
    if (typeof rule.priority !== 'number') {
      throw new Error('Rule must have a numeric priority');
    }
    
    if (typeof rule.evaluate !== 'function') {
      throw new Error('Rule must have an evaluate function');
    }
    
    if (typeof rule.canHandle !== 'function') {
      throw new Error('Rule must have a canHandle function');
    }
    
    if (typeof rule.getDependencies !== 'function') {
      throw new Error('Rule must have a getDependencies function');
    }
  }

  private initializeCategories(): void {
    const defaultCategories: RuleCategory[] = [
      {
        id: 'core',
        name: 'Core Rules',
        description: 'Basic complexity calculation rules',
        rules: [],
        priority: 1000,
      },
      {
        id: 'jsx',
        name: 'JSX Rules',
        description: 'React/JSX specific rules',
        rules: [],
        priority: 800,
      },
      {
        id: 'logical',
        name: 'Logical Rules',
        description: 'Logical operator and condition rules',
        rules: [],
        priority: 600,
      },
      {
        id: 'plugin',
        name: 'Plugin Rules',
        description: 'Rules from loaded plugins',
        rules: [],
        priority: 400,
      },
    ];
    
    for (const category of defaultCategories) {
      this.categories.set(category.id, category);
    }
  }

  private assignRuleToCategory(rule: Rule): void {
    let categoryId = 'core'; // 默认类别
    
    if (rule.id.startsWith('jsx.')) {
      categoryId = 'jsx';
    } else if (rule.id.startsWith('logical.')) {
      categoryId = 'logical';
    } else if (rule.id.includes('plugin.')) {
      categoryId = 'plugin';
    }
    
    const category = this.categories.get(categoryId);
    if (category) {
      category.rules.push(rule.id);
    }
  }

  private removeRuleFromCategories(ruleId: string): void {
    for (const category of this.categories.values()) {
      category.rules = category.rules.filter(id => id !== ruleId);
    }
  }

  private findRuleCategory(ruleId: string): string | null {
    for (const [categoryId, category] of this.categories.entries()) {
      if (category.rules.includes(ruleId)) {
        return categoryId;
      }
    }
    return null;
  }

  private detectAndRecordConflicts(newRule: Rule): void {
    for (const existingRule of this.rules.values()) {
      if (existingRule.id === newRule.id) continue;
      
      const conflict = this.detectRuleConflict(newRule, existingRule);
      if (conflict) {
        this.conflicts.push(conflict);
      }
    }
  }

  private detectRuleConflict(rule1: Rule, rule2: Rule): RuleConflict | null {
    // 优先级冲突检测
    if (rule1.priority === rule2.priority && rule1.id !== rule2.id) {
      return {
        id: `priority-${rule1.id}-${rule2.id}`,
        type: 'priority',
        description: `Rules '${rule1.id}' and '${rule2.id}' have the same priority (${rule1.priority})`,
        involvedRules: [rule1.id, rule2.id],
        severity: 'medium',
        suggestedResolution: {
          type: 'reorder',
          target: rule2.id,
          parameters: { newPriority: rule2.priority - 1 },
        },
      };
    }
    
    return null;
  }

  private findDependentRules(ruleId: string): string[] {
    const dependents: string[] = [];
    
    for (const [id, dependencies] of this.dependencyGraph.entries()) {
      if (dependencies.includes(ruleId)) {
        dependents.push(id);
      }
    }
    
    return dependents;
  }

  private generateDependencySuggestions(ruleId: string, missingDependencies: string[]): string[] {
    const suggestions: string[] = [];
    
    for (const missingDep of missingDependencies) {
      suggestions.push(`Consider registering the missing dependency rule '${missingDep}'`);
    }
    
    return suggestions;
  }

  private isValidPlugin(plugin: any): boolean {
    return (
      plugin &&
      typeof plugin.id === 'string' &&
      Array.isArray(plugin.rules) &&
      plugin.rules.every((rule: any) => 
        rule && 
        typeof rule.id === 'string' &&
        typeof rule.evaluate === 'function' &&
        typeof rule.canHandle === 'function'
      )
    );
  }

  private applyConflictResolution(conflict: RuleConflict, resolution: ConflictResolution): void {
    switch (resolution.type) {
      case 'disable':
        this.disableRule(resolution.target);
        break;
      case 'reorder':
        const rule = this.rules.get(resolution.target);
        if (rule && resolution.parameters?.newPriority) {
          // 注意：这里需要创建新的规则对象，因为优先级是只读的
          console.log(`Reordering rule '${resolution.target}' priority to ${resolution.parameters.newPriority}`);
        }
        break;
      case 'configure':
        console.log(`Configuring rule '${resolution.target}' with parameters:`, resolution.parameters);
        break;
      case 'merge':
        console.log(`Merging conflicting rules: ${conflict.involvedRules.join(', ')}`);
        break;
      default:
        throw new Error(`Unknown conflict resolution type: ${resolution.type}`);
    }
  }
}