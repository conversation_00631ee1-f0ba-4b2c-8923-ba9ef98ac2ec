/**
 * 流式AST处理器
 * 实现流式处理以避免内存溢出，优化大型文件分析性能
 */

import type { Node, Module } from '@swc/core';
import type { AsyncRuleEngine, MutableAnalysisContext, MutableNodeAnalysis, FunctionAnalysis } from './types';
import * as stream from 'stream';
import { EventEmitter } from 'events';

// 流式处理配置
export interface StreamingConfig {
  batchSize: number;            // 批处理大小
  maxMemoryUsage: number;       // 最大内存使用量(MB)
  highWaterMark: number;        // 流高水位标记
  enableBackpressure: boolean;  // 启用背压控制
  memoryThreshold: number;      // 内存阈值监控
  gcInterval: number;           // 垃圾回收间隔
}

export const DEFAULT_STREAMING_CONFIG: StreamingConfig = {
  batchSize: 100,
  maxMemoryUsage: 512, // 512MB
  highWaterMark: 1024 * 16, // 16KB
  enableBackpressure: true,
  memoryThreshold: 0.8, // 80%内存使用率触发清理
  gcInterval: 10000, // 10秒
};

// 处理状态
export interface ProcessingState {
  totalNodes: number;
  processedNodes: number;
  memoryUsage: number;
  peakMemoryUsage: number;
  processingRate: number; // 节点/秒
  isBackpressureActive: boolean;
  lastGCTime: number;
}

// 流式处理结果
export interface StreamingResult<T> {
  data: T;
  metadata: {
    batchIndex: number;
    processingTime: number;
    memoryUsage: number;
    nodeCount: number;
  };
}

/**
 * 内存监控器
 */
class MemoryMonitor extends EventEmitter {
  private config: StreamingConfig;
  private lastMemoryCheck = 0;
  private checkInterval: NodeJS.Timeout;
  private peakUsage = 0;

  constructor(config: StreamingConfig) {
    super();
    this.config = config;
    
    // 定期检查内存使用
    this.checkInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, 1000);
  }

  checkMemoryUsage(): void {
    const usage = process.memoryUsage();
    const usageMB = usage.heapUsed / 1024 / 1024;
    
    this.peakUsage = Math.max(this.peakUsage, usageMB);
    
    // 检查是否超过阈值
    if (usageMB > this.config.maxMemoryUsage * this.config.memoryThreshold) {
      this.emit('memory-pressure', {
        current: usageMB,
        threshold: this.config.maxMemoryUsage * this.config.memoryThreshold,
        peak: this.peakUsage,
      });
    }
    
    // 检查是否需要强制垃圾回收
    if (usageMB > this.config.maxMemoryUsage * 0.9) {
      this.emit('force-gc', { usage: usageMB });
    }
    
    this.lastMemoryCheck = Date.now();
  }

  getCurrentUsage(): number {
    return process.memoryUsage().heapUsed / 1024 / 1024;
  }

  getPeakUsage(): number {
    return this.peakUsage;
  }

  resetPeak(): void {
    this.peakUsage = 0;
  }

  destroy(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
  }
}

/**
 * 对象池管理器
 */
class ObjectPool<T> {
  private pool: T[] = [];
  private factory: () => T;
  private reset?: (obj: T) => void;
  private maxSize: number;

  constructor(factory: () => T, maxSize: number = 1000, reset?: (obj: T) => void) {
    this.factory = factory;
    this.maxSize = maxSize;
    this.reset = reset;
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.factory();
  }

  release(obj: T): void {
    if (this.pool.length < this.maxSize) {
      if (this.reset) {
        this.reset(obj);
      }
      this.pool.push(obj);
    }
  }

  clear(): void {
    this.pool = [];
  }

  get size(): number {
    return this.pool.length;
  }
}

/**
 * 节点批处理器
 */
class NodeBatchProcessor {
  private batch: Node[] = [];
  private batchSize: number;
  private onBatchReady: (batch: Node[]) => Promise<void>;

  constructor(batchSize: number, onBatchReady: (batch: Node[]) => Promise<void>) {
    this.batchSize = batchSize;
    this.onBatchReady = onBatchReady;
  }

  async addNode(node: Node): Promise<void> {
    this.batch.push(node);
    
    if (this.batch.length >= this.batchSize) {
      await this.flushBatch();
    }
  }

  async flushBatch(): Promise<void> {
    if (this.batch.length > 0) {
      const batchToProcess = [...this.batch];
      this.batch = []; // 立即清空批次以释放内存
      
      await this.onBatchReady(batchToProcess);
    }
  }

  get currentBatchSize(): number {
    return this.batch.length;
  }
}

/**
 * 流式AST处理器
 */
export class StreamingASTProcessor extends EventEmitter {
  private config: StreamingConfig;
  private memoryMonitor: MemoryMonitor;
  private analysisContextPool: ObjectPool<MutableAnalysisContext>;
  private nodeAnalysisPool: ObjectPool<MutableNodeAnalysis>;
  private state: ProcessingState;
  private isProcessing = false;
  private processingStartTime = 0;

  constructor(config: StreamingConfig = DEFAULT_STREAMING_CONFIG) {
    super();
    this.config = config;
    this.memoryMonitor = new MemoryMonitor(config);
    this.setupEventHandlers();
    this.initializeObjectPools();
    this.initializeState();
  }

  /**
   * 流式处理文件
   */
  async *processFileStream(
    engine: AsyncRuleEngine,
    ast: Module,
    filePath: string
  ): AsyncGenerator<StreamingResult<FunctionAnalysis>, void, unknown> {
    this.isProcessing = true;
    this.processingStartTime = Date.now();
    
    try {
      // 提取所有函数
      const functions = this.extractFunctions(ast);
      this.state.totalNodes = functions.length;
      
      this.emit('processing-started', {
        filePath,
        totalFunctions: functions.length,
      });

      // 创建批处理器
      const batchProcessor = new NodeBatchProcessor(
        this.config.batchSize,
        (batch) => this.processFunctionBatch(engine, batch, filePath)
      );

      // 流式处理函数
      for (const func of functions) {
        // 检查背压
        if (this.config.enableBackpressure && this.state.isBackpressureActive) {
          await this.waitForBackpressureRelief();
        }

        await batchProcessor.addNode(func);
        
        // 定期触发垃圾回收
        if (this.shouldTriggerGC()) {
          this.triggerGC();
        }
        
        // 为每个函数生成一个结果（简化实现）
        const analysis = await engine.analyzeFunction(func);
        yield {
          data: analysis,
          metadata: {
            batchIndex: 0, // 单个文件处理时为0
            processingTime: Date.now() - this.processingStartTime,
            memoryUsage: this.state.memoryUsage,
            nodeCount: 1,
          },
        } as StreamingResult<FunctionAnalysis>;
        
        this.state.processedNodes++;
      }

      // 处理剩余的批次
      await batchProcessor.flushBatch();

      this.emit('processing-completed', {
        filePath,
        totalProcessed: this.state.processedNodes,
        peakMemory: this.memoryMonitor.getPeakUsage(),
        processingTime: Date.now() - this.processingStartTime,
      });

    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 流式处理多个文件
   */
  async *processMultipleFiles(
    engine: AsyncRuleEngine,
    filePaths: string[]
  ): AsyncGenerator<StreamingResult<{ filePath: string; analysis: FunctionAnalysis[] }>, void, unknown> {
    let fileIndex = 0;
    
    for (const filePath of filePaths) {
      try {
        this.emit('file-started', { filePath, index: fileIndex, total: filePaths.length });
        
        // 解析文件AST（使用流式解析器）
        const ast = await this.parseFileStreaming(filePath);
        const functionAnalyses: FunctionAnalysis[] = [];
        
        // 处理单个文件
        for await (const result of this.processFileStream(engine, ast, filePath)) {
          functionAnalyses.push(result.data);
          
          // 检查内存压力，适时yield控制权
          if (this.state.memoryUsage > this.config.maxMemoryUsage * 0.7) {
            yield {
              data: { filePath, analysis: [...functionAnalyses] },
              metadata: {
                batchIndex: fileIndex,
                processingTime: Date.now() - this.processingStartTime,
                memoryUsage: this.state.memoryUsage,
                nodeCount: functionAnalyses.length,
              },
            };
            
            functionAnalyses.length = 0; // 清空以释放内存
            await this.sleep(10); // 让事件循环处理其他任务
          }
        }
        
        // 最终结果
        if (functionAnalyses.length > 0) {
          yield {
            data: { filePath, analysis: functionAnalyses },
            metadata: {
              batchIndex: fileIndex,
              processingTime: Date.now() - this.processingStartTime,
              memoryUsage: this.state.memoryUsage,
              nodeCount: functionAnalyses.length,
            },
          };
        }
        
        this.emit('file-completed', { filePath, index: fileIndex });
        
      } catch (error) {
        this.emit('file-error', { filePath, error, index: fileIndex });
        
        // 继续处理下一个文件
        yield {
          data: { filePath, analysis: [] },
          metadata: {
            batchIndex: fileIndex,
            processingTime: 0,
            memoryUsage: this.state.memoryUsage,
            nodeCount: 0,
          },
        };
      }
      
      fileIndex++;
    }
  }

  /**
   * 创建转换流
   */
  createTransformStream(engine: AsyncRuleEngine): stream.Transform {
    const self = this; // 保存引用以便在transform函数中使用
    return new stream.Transform({
      objectMode: true,
      highWaterMark: this.config.highWaterMark,
      
      async transform(chunk: { filePath: string; ast: Module }, encoding, callback) {
        try {
          const results: FunctionAnalysis[] = [];
          
          for await (const result of self.processFileStream(engine, chunk.ast, chunk.filePath)) {
            results.push(result.data);
          }
          
          callback(null, { filePath: chunk.filePath, analyses: results });
        } catch (error) {
          const err = error instanceof Error ? error : new Error(String(error));
          callback(err);
        }
      }
    });
  }

  /**
   * 获取处理状态
   */
  getProcessingState(): ProcessingState {
    return { ...this.state };
  }

  /**
   * 暂停处理（启用背压）
   */
  pause(): void {
    this.state.isBackpressureActive = true;
    this.emit('backpressure-activated');
  }

  /**
   * 恢复处理（释放背压）
   */
  resume(): void {
    this.state.isBackpressureActive = false;
    this.emit('backpressure-relieved');
  }

  /**
   * 强制垃圾回收
   */
  forceGC(): void {
    this.triggerGC();
    this.memoryMonitor.resetPeak();
  }

  /**
   * 销毁处理器，清理资源
   */
  destroy(): void {
    this.isProcessing = false;
    this.memoryMonitor.destroy();
    this.analysisContextPool.clear();
    this.nodeAnalysisPool.clear();
    this.removeAllListeners();
  }

  private async processFunctionBatch(
    engine: AsyncRuleEngine,
    functions: Node[],
    filePath: string
  ): Promise<void> {
    const batchStartTime = Date.now();
    
    try {
      // 并行处理批次中的函数
      const promises = functions.map(async (func) => {
        const context = this.analysisContextPool.acquire();
        try {
          const analysis = await engine.analyzeFunction(func);
          this.state.processedNodes++;
          return analysis;
        } finally {
          this.analysisContextPool.release(context);
        }
      });

      await Promise.all(promises);
      
      // 更新状态
      this.updateProcessingRate();
      this.state.memoryUsage = this.memoryMonitor.getCurrentUsage();
      
      this.emit('batch-processed', {
        batchSize: functions.length,
        processingTime: Date.now() - batchStartTime,
        memoryUsage: this.state.memoryUsage,
        totalProcessed: this.state.processedNodes,
      });

    } catch (error) {
      this.emit('batch-error', { error, batchSize: functions.length });
      throw error;
    }
  }

  private setupEventHandlers(): void {
    this.memoryMonitor.on('memory-pressure', (data) => {
      this.emit('memory-pressure', data);
      
      // 启用背压以减缓处理速度
      if (this.config.enableBackpressure) {
        this.pause();
      }
    });

    this.memoryMonitor.on('force-gc', (data) => {
      this.triggerGC();
      this.emit('forced-gc', data);
    });
  }

  private initializeObjectPools(): void {
    // 分析上下文对象池
    this.analysisContextPool = new ObjectPool<MutableAnalysisContext>(
      () => ({
        filePath: '',
        fileContent: '',
        ast: {} as Module,
        currentFunction: undefined,
        functionName: '',
        nestingLevel: 0,
        config: {} as any, // 临时类型断言，待配置系统完善
        jsxMode: 'standard' as const,
        rules: { core: [], jsx: [], plugins: [] },
        cache: {} as any, // 临时类型断言，待缓存系统完善
        metrics: {
          totalNodes: 0,
          processedNodes: 0,
          cacheHits: 0,
          cacheMisses: 0,
          ruleExecutions: 0,
          parallelExecutions: 0,
          errors: 0,
        },
        plugins: [],
        customData: new Map(),
      }),
      200,
      (ctx: MutableAnalysisContext) => {
        // 重置上下文对象
        ctx.filePath = '';
        ctx.fileContent = '';
        ctx.currentFunction = undefined;
        ctx.functionName = '';
        ctx.nestingLevel = 0;
        ctx.customData.clear();
      }
    );

    // 节点分析结果对象池
    this.nodeAnalysisPool = new ObjectPool<MutableNodeAnalysis>(
      () => ({
        node: {} as Node,
        complexity: 0,
        appliedRules: [],
        exemptions: [],
        children: [],
        aggregatedComplexity: 0,
        analysisTime: 0,
        cacheUtilization: 0,
      }),
      500,
      (analysis: MutableNodeAnalysis) => {
        // 重置分析结果对象
        analysis.complexity = 0;
        analysis.appliedRules = [];
        analysis.exemptions = [];
        analysis.children = [];
        analysis.aggregatedComplexity = 0;
        analysis.analysisTime = 0;
        analysis.cacheUtilization = 0;
      }
    );
  }

  private initializeState(): void {
    this.state = {
      totalNodes: 0,
      processedNodes: 0,
      memoryUsage: 0,
      peakMemoryUsage: 0,
      processingRate: 0,
      isBackpressureActive: false,
      lastGCTime: Date.now(),
    };
  }

  private extractFunctions(ast: Module): Node[] {
    const functions: Node[] = [];
    
    // 使用迭代方式遍历AST，避免递归导致的栈溢出
    const nodeQueue: Node[] = [ast];
    
    while (nodeQueue.length > 0) {
      const currentNode = nodeQueue.shift()!;
      
      // 检查是否为函数节点
      if (this.isFunctionNode(currentNode)) {
        functions.push(currentNode);
      }
      
      // 添加子节点到队列
      if ('body' in currentNode && Array.isArray(currentNode.body)) {
        nodeQueue.push(...currentNode.body as Node[]);
      }
      
      // 处理其他可能包含子节点的情况
      Object.values(currentNode).forEach(value => {
        if (value && typeof value === 'object' && 'type' in value) {
          nodeQueue.push(value as Node);
        } else if (Array.isArray(value)) {
          value.forEach(item => {
            if (item && typeof item === 'object' && 'type' in item) {
              nodeQueue.push(item as Node);
            }
          });
        }
      });
    }
    
    return functions;
  }

  private isFunctionNode(node: Node): boolean {
    return [
      'FunctionDeclaration',
      'FunctionExpression',
      'ArrowFunctionExpression',
      'MethodDefinition',
    ].includes(node.type);
  }

  private async parseFileStreaming(filePath: string): Promise<Module> {
    // 这里应该实现流式解析，目前使用简化版本
    // 在实际实现中，可以使用SWC的流式解析API
    const fs = await import('fs');
    const content = await fs.promises.readFile(filePath, 'utf-8');
    
    // 使用SWC解析（这里需要引入实际的解析器）
    // 为了演示，返回一个占位符
    return {} as Module;
  }

  private shouldTriggerGC(): boolean {
    const now = Date.now();
    return (now - this.state.lastGCTime) > this.config.gcInterval;
  }

  private triggerGC(): void {
    if (global.gc) {
      global.gc();
      this.state.lastGCTime = Date.now();
    } else {
      // 如果没有global.gc，使用其他方式触发垃圾回收
      const largeArray = new Array(1000000);
      largeArray.fill(null);
      largeArray.length = 0;
    }
  }

  private updateProcessingRate(): void {
    const elapsedTime = (Date.now() - this.processingStartTime) / 1000;
    this.state.processingRate = elapsedTime > 0 ? this.state.processedNodes / elapsedTime : 0;
  }

  private async waitForBackpressureRelief(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.state.isBackpressureActive) {
        resolve();
        return;
      }
      
      const checkInterval = setInterval(() => {
        if (!this.state.isBackpressureActive) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 创建流式处理器工厂
 */
export function createStreamingProcessor(config?: Partial<StreamingConfig>): StreamingASTProcessor {
  const finalConfig = { ...DEFAULT_STREAMING_CONFIG, ...config };
  return new StreamingASTProcessor(finalConfig);
}

/**
 * 流式处理辅助函数
 */
export class StreamingUtils {
  /**
   * 估算AST大小
   */
  static estimateASTSize(ast: Module): number {
    return JSON.stringify(ast).length;
  }

  /**
   * 检查是否需要流式处理
   */
  static shouldUseStreaming(ast: Module, threshold: number = 1024 * 1024): boolean {
    return this.estimateASTSize(ast) > threshold;
  }

  /**
   * 分块处理大型数组
   */
  static async *processInChunks<T, R>(
    items: T[],
    chunkSize: number,
    processor: (chunk: T[]) => Promise<R[]>
  ): AsyncGenerator<R[], void, unknown> {
    for (let i = 0; i < items.length; i += chunkSize) {
      const chunk = items.slice(i, i + chunkSize);
      const results = await processor(chunk);
      yield results;
    }
  }

  /**
   * 内存使用监控
   */
  static getMemoryUsage(): { used: number; total: number; percentage: number } {
    const usage = process.memoryUsage();
    const used = usage.heapUsed / 1024 / 1024; // MB
    const total = usage.heapTotal / 1024 / 1024; // MB
    const percentage = total > 0 ? (used / total) * 100 : 0;
    
    return { used, total, percentage };
  }
}