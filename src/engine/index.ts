/**
 * 现代化规则引擎导出
 * 导出所有核心组件和类型定义
 */

// 核心类型
export type {
  // 引擎类型
  AsyncRuleEngine,
  AnalysisContext,
  Rule,
  RuleResult,
  NodeAnalysis,
  FunctionAnalysis,
  FileAnalysis,
  EngineConfig,
  ResolvedEngineConfig,
  EngineMetrics,
  
  // 配置类型
  JSXRuleConfig,
  LogicalRuleConfig,
  NestingRuleConfig,
  
  // 其他核心类型
  ExecutionOptions,
  ExecutionLoad,
  RuleError,
  Suggestion,
  CodeLocation,
  LoadedPlugin,
} from './types';

// 缓存系统类型
export type {
  CacheManager,
  CacheConfig,
  CacheStatistics,
  CacheSize,
  TypeInfo,
} from '../cache/types';

// 核心实现
export { AsyncRuleEngineImpl } from './async-engine';
export { RuleRegistryImpl } from './registry';
export { ParallelExecutionPool } from './execution-pool';

// 缓存实现
export { IntelligentCacheManager } from '../cache/manager';

// 性能基准测试系统
export {
  TypeSafeBenchmarkSuite,
  TypeSafeRegressionTester,
  createTypeSafeBenchmarkSuite,
  createTypeSafeRegressionTester,
} from './type-safe-benchmark';
export type {
  BenchmarkError,
  SafePerformanceMetrics,
  ValidatedBenchmarkConfig,
  BenchmarkConfig,
  SafeBenchmarkResult,
} from './type-safe-benchmark';

// 性能内核
export { PerformanceKernel, createPerformanceKernel } from './performance-kernel';
export type { 
  PerformanceKernelConfig, 
  CompressionConfig, 
  LargeFileConfig, 
  TimeCacheConfig 
} from './performance-kernel';

// 默认配置
export { DEFAULT_ENGINE_CONFIG } from './types';
export { DEFAULT_CACHE_CONFIG } from '../cache/types';

// 规则集合
export { RuleSetManager, CoreRuleSet, JSXRuleSet } from '../rules/rule-sets';

import type { ResolvedEngineConfig, AsyncRuleEngine } from './types';

// 工厂函数
export function createAsyncRuleEngine(config?: Partial<ResolvedEngineConfig>) {
  const { AsyncRuleEngineImpl } = require('./async-engine');
  return new AsyncRuleEngineImpl(config);
}

// 便捷函数 - 创建带有默认规则的引擎
export async function createEngineWithDefaults(config?: Partial<ResolvedEngineConfig>) {
  const { AsyncRuleEngineImpl } = await import('./async-engine');
  const engine = new AsyncRuleEngineImpl(config);
  
  // 注册所有默认规则
  const { RuleSetManager } = await import('../rules/rule-sets');
  RuleSetManager.registerAllToEngine(engine);
  
  // 验证规则集合完整性
  const validation = RuleSetManager.validateRuleSet();
  if (!validation.isValid) {
    console.warn('Rule set validation issues found:', validation.issues);
  }
  
  console.log('AsyncRuleEngine initialized with default rules');
  console.log('Rule statistics:', RuleSetManager.getStatistics());
  
  return engine;
}

// 创建纯净引擎（不带默认规则）
export function createCleanEngine(config?: Partial<ResolvedEngineConfig>) {
  const { AsyncRuleEngineImpl } = require('./async-engine');
  const engine = new AsyncRuleEngineImpl(config);
  console.log('Clean AsyncRuleEngine created (no default rules)');
  return engine;
}

// 引擎状态检查函数
export async function validateEngine(engine: AsyncRuleEngine): Promise<{
  isValid: boolean;
  issues: string[];
  stats: any;
}> {
  const issues: string[] = [];
  
  try {
    // 检查引擎基本功能
    const metrics = engine.getMetrics();
    const ruleStats = engine.getRuleStatistics();
    
    if (ruleStats.totalRegistered === 0) {
      issues.push('No rules registered in engine');
    }
    
    if (ruleStats.enabled === 0) {
      issues.push('No rules are enabled');
    }
    
    if (ruleStats.conflicts > 0) {
      issues.push(`${ruleStats.conflicts} rule conflicts detected`);
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      stats: {
        metrics,
        ruleStats,
      }
    };
  } catch (error) {
    issues.push(`Engine validation failed: ${error}`);
    return {
      isValid: false,
      issues,
      stats: null,
    };
  }
}