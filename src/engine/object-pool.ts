/**
 * 高级对象池管理系统
 * 减少垃圾回收开销，提升内存分配性能
 */

import type { Node } from '@swc/core';
import type { 
  AnalysisContext, 
  MutableAnalysisContext,
  NodeAnalysis,
  MutableNodeAnalysis, 
  RuleResult,
  MutableRuleResult, 
  FunctionAnalysis 
} from './types';
import { EventEmitter } from 'events';

// 对象池配置
export interface ObjectPoolConfig {
  initialSize: number;          // 初始池大小
  maxSize: number;             // 最大池大小
  growthFactor: number;        // 增长因子
  shrinkThreshold: number;     // 收缩阈值(使用率)
  shrinkInterval: number;      // 收缩检查间隔(ms)
  enableStatistics: boolean;   // 启用统计信息
  enableLRUEviction: boolean;  // 启用LRU淘汰
  objectLifetime: number;      // 对象生命周期(ms)
}

export const DEFAULT_POOL_CONFIG: ObjectPoolConfig = {
  initialSize: 50,
  maxSize: 1000,
  growthFactor: 1.5,
  shrinkThreshold: 0.3, // 使用率低于30%时收缩
  shrinkInterval: 30000, // 30秒
  enableStatistics: true,
  enableLRUEviction: true,
  objectLifetime: 300000, // 5分钟
};

// 池化对象接口
export interface PoolableObject {
  reset(): void;
  isValid(): boolean;
  getSize(): number;
}

// 对象包装器
export interface PooledObject<T> {
  object: T;
  createdAt: number;
  lastUsedAt: number;
  useCount: number;
  isInUse: boolean;
}

// 池统计信息
export interface PoolStatistics {
  totalCreated: number;
  totalAcquired: number;
  totalReleased: number;
  currentSize: number;
  activeObjects: number;
  hitRate: number;
  avgLifetime: number;
  memoryUsage: number;
  maxSize: number;
}

// 对象工厂接口
export interface ObjectFactory<T> {
  create(): T;
  reset?(obj: T): void;
  validate?(obj: T): boolean;
  size?(obj: T): number;
}

/**
 * 增强型对象池实现
 */
export class EnhancedObjectPool<T> extends EventEmitter {
  private config: ObjectPoolConfig;
  private factory: ObjectFactory<T>;
  private pool: PooledObject<T>[] = [];
  private activeObjects = new Set<PooledObject<T>>();
  private statistics: PoolStatistics;
  private shrinkTimer?: NodeJS.Timeout;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(factory: ObjectFactory<T>, config: Partial<ObjectPoolConfig> = {}) {
    super();
    this.factory = factory;
    this.config = { ...DEFAULT_POOL_CONFIG, ...config };
    this.statistics = this.createEmptyStatistics();
    
    this.initialize();
    this.startMaintenance();
  }

  /**
   * 从池中获取对象
   */
  acquire(): T {
    const startTime = performance.now();
    let pooledObj: PooledObject<T> | undefined;

    // 尝试从池中获取可用对象
    for (let i = 0; i < this.pool.length; i++) {
      const obj = this.pool[i];
      if (obj && !obj.isInUse && this.isObjectValid(obj)) {
        pooledObj = obj;
        this.pool.splice(i, 1);
        break;
      }
    }

    // 如果池中没有可用对象，创建新对象
    if (!pooledObj) {
      if (this.canCreateNewObject()) {
        pooledObj = this.createNewObject();
        this.statistics.totalCreated++;
      } else {
        // 池已满，强制清理过期对象
        this.cleanupExpiredObjects();
        
        // 再次尝试获取
        for (let i = 0; i < this.pool.length; i++) {
          const obj = this.pool[i];
          if (obj && !obj.isInUse) {
            pooledObj = obj;
            this.pool.splice(i, 1);
            break;
          }
        }
        
        // 如果仍然没有，创建临时对象（不加入池中）
        if (!pooledObj) {
          this.emit('pool-exhausted', {
            currentSize: this.pool.length,
            activeObjects: this.activeObjects.size,
            maxSize: this.config.maxSize,
          });
          
          return this.factory.create(); // 返回临时对象
        }
      }
    }

    // 更新对象状态
    pooledObj.isInUse = true;
    pooledObj.lastUsedAt = Date.now();
    pooledObj.useCount++;
    
    this.activeObjects.add(pooledObj);
    
    // 重置对象状态
    if (this.factory.reset) {
      this.factory.reset(pooledObj.object);
    }

    // 更新统计信息
    this.statistics.totalAcquired++;
    const acquisitionTime = performance.now() - startTime;
    
    if (this.config.enableStatistics) {
      this.updateAcquisitionStatistics(acquisitionTime);
    }

    return pooledObj.object;
  }

  /**
   * 将对象归还到池中
   */
  release(obj: T): boolean {
    // 查找对应的池化对象
    const pooledObj = this.findPooledObject(obj);
    
    if (!pooledObj) {
      // 可能是临时对象，直接忽略
      return false;
    }

    if (!pooledObj.isInUse) {
      console.warn('Attempting to release an object that is not in use');
      return false;
    }

    // 验证对象有效性
    if (!this.isObjectValid(pooledObj)) {
      this.removeObject(pooledObj);
      return false;
    }

    // 更新对象状态
    pooledObj.isInUse = false;
    pooledObj.lastUsedAt = Date.now();
    
    this.activeObjects.delete(pooledObj);
    
    // 检查是否应该保留在池中
    if (this.shouldRetainObject(pooledObj)) {
      this.pool.push(pooledObj);
      
      // 按使用频率排序（LRU策略）
      if (this.config.enableLRUEviction) {
        this.pool.sort((a, b) => b.lastUsedAt - a.lastUsedAt);
      }
    } else {
      // 对象已过期或池已满，直接销毁
      this.removeObject(pooledObj);
    }

    // 更新统计信息
    this.statistics.totalReleased++;
    
    return true;
  }

  /**
   * 批量获取对象
   */
  acquireBatch(count: number): T[] {
    const objects: T[] = [];
    
    for (let i = 0; i < count; i++) {
      objects.push(this.acquire());
    }
    
    return objects;
  }

  /**
   * 批量释放对象
   */
  releaseBatch(objects: T[]): number {
    let released = 0;
    
    for (const obj of objects) {
      if (this.release(obj)) {
        released++;
      }
    }
    
    return released;
  }

  /**
   * 预热池子
   */
  preWarm(count?: number): void {
    const targetCount = count || this.config.initialSize;
    
    for (let i = 0; i < targetCount; i++) {
      if (this.pool.length >= this.config.maxSize) {
        break;
      }
      
      const pooledObj = this.createNewObject();
      this.pool.push(pooledObj);
      this.statistics.totalCreated++;
    }
    
    this.emit('prewarmed', {
      count: this.pool.length,
      target: targetCount,
    });
  }

  /**
   * 获取池统计信息
   */
  getStatistics(): PoolStatistics {
    this.updateStatistics();
    return { ...this.statistics };
  }

  /**
   * 清理池中的过期对象
   */
  cleanup(): number {
    const before = this.pool.length;
    this.cleanupExpiredObjects();
    const cleaned = before - this.pool.length;
    
    if (cleaned > 0) {
      this.emit('cleaned', { count: cleaned });
    }
    
    return cleaned;
  }

  /**
   * 收缩池大小
   */
  shrink(): number {
    const utilizationRate = this.activeObjects.size / this.pool.length;
    
    if (utilizationRate > this.config.shrinkThreshold) {
      return 0; // 使用率较高，不收缩
    }

    const targetSize = Math.max(
      this.config.initialSize,
      Math.floor(this.pool.length * 0.7) // 收缩30%
    );
    
    const objectsToRemove = this.pool.length - targetSize;
    
    if (objectsToRemove <= 0) {
      return 0;
    }

    // 按LRU顺序移除对象
    this.pool.sort((a, b) => a.lastUsedAt - b.lastUsedAt);
    const removed = this.pool.splice(0, objectsToRemove);
    
    this.emit('shrunk', {
      removed: removed.length,
      newSize: this.pool.length,
      utilizationRate,
    });
    
    return removed.length;
  }

  /**
   * 清空池
   */
  clear(): void {
    this.pool = [];
    this.activeObjects.clear();
    this.statistics = this.createEmptyStatistics();
    this.emit('cleared');
  }

  /**
   * 销毁池
   */
  destroy(): void {
    this.clear();
    
    if (this.shrinkTimer) {
      clearInterval(this.shrinkTimer);
    }
    
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    this.removeAllListeners();
  }

  /**
   * 检查池健康状态
   */
  getHealthStatus(): {
    isHealthy: boolean;
    issues: string[];
    recommendations: string[];
  } {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    const stats = this.getStatistics();
    
    // 检查命中率
    if (stats.hitRate < 0.7) {
      issues.push('Low hit rate indicating frequent object creation');
      recommendations.push('Consider increasing initial pool size');
    }
    
    // 检查内存使用
    if (stats.memoryUsage > 100) { // 100MB
      issues.push('High memory usage by object pool');
      recommendations.push('Consider reducing max pool size or enabling more aggressive cleanup');
    }
    
    // 检查池利用率
    const utilizationRate = stats.activeObjects / stats.currentSize;
    if (utilizationRate < 0.2) {
      issues.push('Low pool utilization');
      recommendations.push('Consider reducing pool size or increasing shrink frequency');
    }
    
    return {
      isHealthy: issues.length === 0,
      issues,
      recommendations,
    };
  }

  private initialize(): void {
    // 预创建初始对象
    this.preWarm(this.config.initialSize);
  }

  private startMaintenance(): void {
    // 定期收缩池
    if (this.config.shrinkInterval > 0) {
      this.shrinkTimer = setInterval(() => {
        this.shrink();
      }, this.config.shrinkInterval);
      
      // 使用 unref() 防止定时器阻止进程退出
      this.shrinkTimer.unref();
    }
    
    // 定期清理过期对象
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, 60000); // 每分钟清理一次
    
    // 使用 unref() 防止定时器阻止进程退出
    this.cleanupTimer.unref();
  }

  private createNewObject(): PooledObject<T> {
    const obj = this.factory.create();
    
    return {
      object: obj,
      createdAt: Date.now(),
      lastUsedAt: Date.now(),
      useCount: 0,
      isInUse: false,
    };
  }

  private canCreateNewObject(): boolean {
    return (this.pool.length + this.activeObjects.size) < this.config.maxSize;
  }

  private isObjectValid(pooledObj: PooledObject<T>): boolean {
    // 检查生命周期
    const age = Date.now() - pooledObj.createdAt;
    if (age > this.config.objectLifetime) {
      return false;
    }
    
    // 使用工厂的验证方法
    if (this.factory.validate) {
      return this.factory.validate(pooledObj.object);
    }
    
    return true;
  }

  private shouldRetainObject(pooledObj: PooledObject<T>): boolean {
    // 检查池是否已满
    if (this.pool.length >= this.config.maxSize) {
      return false;
    }
    
    // 检查对象有效性
    return this.isObjectValid(pooledObj);
  }

  private findPooledObject(obj: T): PooledObject<T> | undefined {
    for (const pooledObj of this.activeObjects) {
      if (pooledObj.object === obj) {
        return pooledObj;
      }
    }
    return undefined;
  }

  private removeObject(pooledObj: PooledObject<T>): void {
    this.activeObjects.delete(pooledObj);
    const index = this.pool.indexOf(pooledObj);
    if (index >= 0) {
      this.pool.splice(index, 1);
    }
  }

  private cleanupExpiredObjects(): void {
    const now = Date.now();
    const before = this.pool.length;
    
    this.pool = this.pool.filter(pooledObj => {
      const age = now - pooledObj.createdAt;
      return age <= this.config.objectLifetime && this.isObjectValid(pooledObj);
    });
    
    const cleaned = before - this.pool.length;
    if (cleaned > 0) {
      this.emit('expired-cleaned', { count: cleaned });
    }
  }

  private updateStatistics(): void {
    this.statistics.currentSize = this.pool.length;
    this.statistics.activeObjects = this.activeObjects.size;
    
    // 计算命中率
    if (this.statistics.totalAcquired > 0) {
      const hits = this.statistics.totalAcquired - this.statistics.totalCreated;
      this.statistics.hitRate = hits / this.statistics.totalAcquired;
    }
    
    // 计算内存使用
    let memoryUsage = 0;
    for (const pooledObj of this.pool) {
      if (this.factory.size) {
        memoryUsage += this.factory.size(pooledObj.object);
      }
    }
    for (const pooledObj of this.activeObjects) {
      if (this.factory.size) {
        memoryUsage += this.factory.size(pooledObj.object);
      }
    }
    this.statistics.memoryUsage = memoryUsage;
    
    // 计算平均生命周期
    const now = Date.now();
    let totalLifetime = 0;
    let count = 0;
    
    for (const pooledObj of this.pool) {
      totalLifetime += now - pooledObj.createdAt;
      count++;
    }
    for (const pooledObj of this.activeObjects) {
      totalLifetime += now - pooledObj.createdAt;
      count++;
    }
    
    this.statistics.avgLifetime = count > 0 ? totalLifetime / count : 0;
    this.statistics.maxSize = this.config.maxSize;
  }

  private updateAcquisitionStatistics(acquisitionTime: number): void {
    // 这里可以添加获取时间的统计信息
    this.emit('object-acquired', {
      acquisitionTime,
      poolSize: this.pool.length,
      activeObjects: this.activeObjects.size,
    });
  }

  private createEmptyStatistics(): PoolStatistics {
    return {
      totalCreated: 0,
      totalAcquired: 0,
      totalReleased: 0,
      currentSize: 0,
      activeObjects: 0,
      hitRate: 0,
      avgLifetime: 0,
      memoryUsage: 0,
      maxSize: this.config.maxSize,
    };
  }
}

/**
 * 特定类型的对象工厂实现
 */

// 分析上下文工厂
export class AnalysisContextFactory implements ObjectFactory<MutableAnalysisContext> {
  create(): MutableAnalysisContext {
    return {
      filePath: '',
      fileContent: '',
      ast: {} as any,
      currentFunction: undefined,
      functionName: '',
      nestingLevel: 0,
      config: {} as any,
      jsxMode: 'standard',
      rules: { core: [], jsx: [], plugins: [] },
      cache: {} as any,
      metrics: {
        totalNodes: 0,
        processedNodes: 0,
        cacheHits: 0,
        cacheMisses: 0,
        ruleExecutions: 0,
        parallelExecutions: 0,
        errors: 0,
      },
      plugins: [],
      customData: new Map(),
    };
  }

  reset(context: MutableAnalysisContext): void {
    context.filePath = '';
    context.fileContent = '';
    context.currentFunction = undefined;
    context.functionName = '';
    context.nestingLevel = 0;
    context.customData.clear();
    context.metrics.totalNodes = 0;
    context.metrics.processedNodes = 0;
    context.metrics.cacheHits = 0;
    context.metrics.cacheMisses = 0;
    context.metrics.ruleExecutions = 0;
    context.metrics.parallelExecutions = 0;
    context.metrics.errors = 0;
  }

  validate(context: MutableAnalysisContext): boolean {
    return context.customData instanceof Map;
  }

  size(context: MutableAnalysisContext): number {
    // 粗略估算对象大小
    return 1024 + context.customData.size * 100; // 基础1KB + 自定义数据
  }
}

// 节点分析结果工厂
export class NodeAnalysisFactory implements ObjectFactory<MutableNodeAnalysis> {
  create(): MutableNodeAnalysis {
    return {
      node: {} as Node,
      complexity: 0,
      appliedRules: [],
      exemptions: [],
      children: [],
      aggregatedComplexity: 0,
      analysisTime: 0,
      cacheUtilization: 0,
    };
  }

  reset(analysis: MutableNodeAnalysis): void {
    analysis.complexity = 0;
    analysis.appliedRules = [];
    analysis.exemptions = [];
    analysis.children = [];
    analysis.aggregatedComplexity = 0;
    analysis.analysisTime = 0;
    analysis.cacheUtilization = 0;
  }

  validate(analysis: MutableNodeAnalysis): boolean {
    return Array.isArray(analysis.appliedRules) && 
           Array.isArray(analysis.exemptions) && 
           Array.isArray(analysis.children);
  }

  size(analysis: MutableNodeAnalysis): number {
    return 512 + 
           analysis.appliedRules.length * 64 + 
           analysis.exemptions.length * 32 + 
           analysis.children.length * 100;
  }
}

// 规则结果工厂
export class RuleResultFactory implements ObjectFactory<MutableRuleResult> {
  create(): MutableRuleResult {
    return {
      ruleId: '',
      complexity: 0,
      isExempted: false,
      shouldIncreaseNesting: false,
      reason: '',
      suggestions: [],
      metadata: { nodeType: '' },
      executionTime: 0,
      cacheHit: false,
    };
  }

  reset(result: MutableRuleResult): void {
    result.ruleId = '';
    result.complexity = 0;
    result.isExempted = false;
    result.shouldIncreaseNesting = false;
    result.reason = '';
    result.suggestions = [];
    result.metadata = { nodeType: '' };
    result.executionTime = 0;
    result.cacheHit = false;
  }

  validate(result: MutableRuleResult): boolean {
    return Array.isArray(result.suggestions) && 
           typeof result.metadata === 'object';
  }

  size(result: MutableRuleResult): number {
    return 256 + result.suggestions.length * 128 + JSON.stringify(result.metadata).length;
  }
}

/**
 * 对象池管理器
 * 统一管理所有类型的对象池
 */
export class ObjectPoolManager {
  private pools = new Map<string, EnhancedObjectPool<any>>();
  private statistics = new Map<string, PoolStatistics>();

  constructor() {
    this.initializeStandardPools();
  }

  /**
   * 获取指定类型的对象池
   */
  getPool<T>(type: string): EnhancedObjectPool<T> | undefined {
    return this.pools.get(type);
  }

  /**
   * 注册新的对象池
   */
  registerPool<T>(
    type: string, 
    factory: ObjectFactory<T>, 
    config?: Partial<ObjectPoolConfig>
  ): void {
    const pool = new EnhancedObjectPool(factory, config);
    this.pools.set(type, pool);
    
    // 监听池事件
    pool.on('pool-exhausted', (data) => {
      console.warn(`Pool exhausted for type '${type}':`, data);
    });
    
    pool.on('shrunk', (data) => {
      console.debug(`Pool shrunk for type '${type}':`, data);
    });
  }

  /**
   * 获取所有池的统计信息
   */
  getAllStatistics(): Map<string, PoolStatistics> {
    for (const [type, pool] of this.pools) {
      this.statistics.set(type, pool.getStatistics());
    }
    return new Map(this.statistics);
  }

  /**
   * 清理所有池
   */
  cleanupAll(): Map<string, number> {
    const cleanupResults = new Map<string, number>();
    
    for (const [type, pool] of this.pools) {
      const cleaned = pool.cleanup();
      cleanupResults.set(type, cleaned);
    }
    
    return cleanupResults;
  }

  /**
   * 预热所有池
   */
  preWarmAll(): void {
    for (const pool of this.pools.values()) {
      pool.preWarm();
    }
  }

  /**
   * 获取整体健康状态
   */
  getOverallHealth(): {
    isHealthy: boolean;
    poolHealth: Map<string, ReturnType<EnhancedObjectPool<any>['getHealthStatus']>>;
    totalMemoryUsage: number;
  } {
    const poolHealth = new Map<string, ReturnType<EnhancedObjectPool<any>['getHealthStatus']>>();
    let isOverallHealthy = true;
    let totalMemoryUsage = 0;
    
    for (const [type, pool] of this.pools) {
      const health = pool.getHealthStatus();
      poolHealth.set(type, health);
      
      if (!health.isHealthy) {
        isOverallHealthy = false;
      }
      
      totalMemoryUsage += pool.getStatistics().memoryUsage;
    }
    
    return {
      isHealthy: isOverallHealthy,
      poolHealth,
      totalMemoryUsage,
    };
  }

  /**
   * 销毁所有池
   */
  destroyAll(): void {
    for (const pool of this.pools.values()) {
      pool.destroy();
    }
    this.pools.clear();
    this.statistics.clear();
  }

  private initializeStandardPools(): void {
    // 注册标准对象池
    this.registerPool('AnalysisContext', new AnalysisContextFactory(), {
      initialSize: 20,
      maxSize: 200,
    });
    
    this.registerPool('NodeAnalysis', new NodeAnalysisFactory(), {
      initialSize: 100,
      maxSize: 500,
    });
    
    this.registerPool('RuleResult', new RuleResultFactory(), {
      initialSize: 50,
      maxSize: 300,
    });
  }
}

// 全局对象池管理器实例
export const globalPoolManager = new ObjectPoolManager();

/**
 * 便捷函数
 */
export function acquireAnalysisContext(): AnalysisContext {
  const pool = globalPoolManager.getPool<AnalysisContext>('AnalysisContext');
  return pool ? pool.acquire() : new AnalysisContextFactory().create();
}

export function releaseAnalysisContext(context: AnalysisContext): void {
  const pool = globalPoolManager.getPool<AnalysisContext>('AnalysisContext');
  if (pool) {
    pool.release(context);
  }
}

export function acquireNodeAnalysis(): MutableNodeAnalysis {
  const pool = globalPoolManager.getPool<MutableNodeAnalysis>('NodeAnalysis');
  return pool ? pool.acquire() : new NodeAnalysisFactory().create();
}

export function releaseNodeAnalysis(analysis: MutableNodeAnalysis): void {
  const pool = globalPoolManager.getPool<MutableNodeAnalysis>('NodeAnalysis');
  if (pool) {
    pool.release(analysis);
  }
}

export function acquireRuleResult(): MutableRuleResult {
  const pool = globalPoolManager.getPool<MutableRuleResult>('RuleResult');
  return pool ? pool.acquire() : new RuleResultFactory().create();
}

export function releaseRuleResult(result: MutableRuleResult): void {
  const pool = globalPoolManager.getPool<MutableRuleResult>('RuleResult');
  if (pool) {
    pool.release(result);
  }
}