export interface CognitiveConfig {
  failOnComplexity: number;
  exclude: string[];
  report: {
    json?: string;
    html?: string;
  };
  severityMapping: SeverityLevel[];
  rules?: {
    enableMixedLogicOperatorPenalty?: boolean;
    recursionChainThreshold?: number;
  };
  // 新增：详细模式配置支持
  enableDetails?: boolean;                // 在配置文件中启用详细模式，默认false
  // UI服务器配置
  ui?: {
    port?: number;                      // Web UI端口，默认3000
    host?: string;                      // Web UI主机，默认localhost
    openBrowser?: boolean;              // 是否自动打开浏览器，默认false
    autoShutdown?: boolean;             // 分析完成后是否自动关闭服务器，默认false
  };
  // 新增的性能和安全配置
  fileProcessingTimeout?: number;     // 单个文件处理超时时间(毫秒)，默认30000
  maxRecursionDepth?: number;         // 最大递归深度，默认50
  enableIterativeParser?: boolean;    // 是否启用迭代解析器(性能优化)，默认false
  enablePerformanceLogging?: boolean; // 是否输出性能统计信息，默认false
  // 智能排除功能配置
  excludeDefaults?: boolean;          // 是否使用默认排除规则，默认true
  includeOverrides?: string[];        // 强制包含路径，即使匹配排除规则
  disableSmartExclusion?: boolean;    // 禁用智能项目类型检测和排除，默认false
  // 文件级复杂度过滤配置
  minFileComplexity?: number;         // 文件级复杂度过滤阈值，默认1
  minFunctionComplexity?: number;     // 函数级复杂度过滤阈值，默认1
}

export interface SeverityLevel {
  level: 'Critical' | 'Warning' | 'Info';
  threshold: number;
}

export interface CLIOptions {
  paths: string[];
  failOn?: number;
  createBaseline?: boolean;
  updateBaseline?: boolean;
  config?: string;
  outputDir?: string;
  details?: boolean;
  showContext?: boolean;          // 显示代码上下文
  showAllContext?: boolean;       // 显示所有步骤的上下文
  format?: 'text' | 'json' | 'html';
  min?: number;
  minFileComplexity?: number;        // 文件级复杂度过滤阈值
  minFunctionComplexity?: number;    // 函数级复杂度过滤阈值，默认1
  showZeroComplexity?: boolean;      // 是否显示复杂度为0的函数，默认false
  sort?: 'name' | 'complexity';
  exclude?: string[];
  ui?: boolean;
  open?: boolean;                    // 启动UI时自动打开浏览器
  // 扫描排除功能的CLI选项
  excludePattern?: string[];         // 添加额外的排除模式
  includeDeps?: boolean;             // 是否包含依赖目录（如node_modules）
  excludeDefaults?: boolean | string; // 是否使用默认排除规则
  showExcluded?: boolean;            // 是否显示被排除的文件和路径
  // 新增的用户体验选项
  quiet?: boolean;                   // 静默模式，减少输出
  noColors?: boolean;                // 禁用彩色输出
  // 智能上下文过滤选项
  maxContextItems?: number;          // 最大上下文项目数量
  minComplexityIncrement?: number;   // 最小复杂度增量阈值
  contextLines?: number;             // 上下文显示的行数
  // 调试和诊断选项
  debug?: boolean;                   // 启用调试模式
  debugLevel?: string;               // 调试级别: trace | debug | info | warn | error
  debugOutput?: string;              // 调试数据输出文件路径
  visualReport?: boolean;            // 生成可视化调试报告
  // 断点和步进调试选项
  enableBreakpoints?: boolean;       // 启用断点调试功能
  breakOnRule?: string[];            // 在指定规则执行时设置断点
  breakOnComplexity?: number;        // 在复杂度达到阈值时设置断点
  stepByStep?: boolean;              // 启用步进调试模式
}

// 项目类型枚举
export type ProjectType = 'nodejs' | 'python' | 'java' | 'rust' | 'go' | 'unknown';

// 项目检测结果
export interface ProjectDetectionResult {
  type: ProjectType;
  configFiles: string[];           // 检测到的配置文件路径
  excludePatterns: string[];       // 该项目类型的排除模式
}

// 排除规则配置
export interface ExclusionRules {
  patterns: string[];              // 排除模式列表
  reason: string;                  // 排除原因描述
  source: 'default' | 'config' | 'cli' | 'smart'; // 规则来源
}

// 排除统计信息
export interface ExclusionStats {
  excludedDirectories: number;     // 排除的目录数量
  excludedFiles: number;           // 排除的文件数量
  appliedRules: ExclusionRules[];  // 应用的排除规则
  projectType?: ProjectType;       // 检测到的项目类型
}