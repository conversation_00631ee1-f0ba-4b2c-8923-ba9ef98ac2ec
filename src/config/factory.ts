/**
 * 配置工厂和预设 - 智能配置生成和项目类型检测
 * 
 * 特性:
 * 1. 自动项目类型检测
 * 2. 项目类型特定的预设配置
 * 3. 配置模板和生成器
 * 4. 配置组合和继承
 */

import { existsSync, readFileSync } from 'fs';
import { resolve, join } from 'path';
import type {
  CognitiveComplexityConfig,
  PartialCognitiveConfig,
  ProjectType,
  JSXRuleConfig,
  LogicalRuleConfig,
  NestingRuleConfig,
  PerformanceConfig,
  SmartExclusionConfig,
} from './schema';
import { createDefaultConfig, mergeConfigs } from './modern-manager';

// =============================================================================
// 项目类型检测
// =============================================================================

/**
 * 项目检测配置
 */
interface ProjectDetectionConfig {
  /** 检测文件列表 */
  detectionFiles: string[];
  /** 依赖检测 */
  dependencies?: string[];
  /** 配置文件模式 */
  configPatterns?: string[];
  /** 目录结构模式 */
  directoryPatterns?: string[];
}

/**
 * 项目类型检测规则
 */
const PROJECT_DETECTION_RULES: Record<ProjectType, ProjectDetectionConfig> = {
  react: {
    detectionFiles: ['package.json'],
    dependencies: ['react', 'react-dom'],
    configPatterns: ['.babelrc', 'babel.config.*', 'webpack.config.*'],
    directoryPatterns: ['src/components', 'src/hooks'],
  },
  nextjs: {
    detectionFiles: ['package.json', 'next.config.js', 'next.config.ts'],
    dependencies: ['next', 'react'],
    directoryPatterns: ['pages', 'app', 'components'],
  },
  gatsby: {
    detectionFiles: ['package.json', 'gatsby-config.js', 'gatsby-config.ts'],
    dependencies: ['gatsby', 'react'],
    directoryPatterns: ['src/pages', 'src/components'],
  },
  vue: {
    detectionFiles: ['package.json'],
    dependencies: ['vue', '@vue/cli-service', 'vite'],
    configPatterns: ['vue.config.js', 'vite.config.*'],
  },
  angular: {
    detectionFiles: ['package.json', 'angular.json'],
    dependencies: ['@angular/core', '@angular/cli'],
    directoryPatterns: ['src/app'],
  },
  nodejs: {
    detectionFiles: ['package.json'],
    dependencies: ['express', 'fastify', 'koa', 'hapi'],
    configPatterns: ['tsconfig.json', '.eslintrc.*'],
  },
  generic: {
    detectionFiles: ['package.json', 'tsconfig.json'],
  },
};

/**
 * 项目类型检测器
 */
export class ProjectTypeDetector {
  /**
   * 检测项目类型
   */
  public static detectProjectType(projectPath: string): ProjectType {
    const detectionResults = new Map<ProjectType, number>();
    
    // 对每种项目类型进行检测
    for (const [projectType, config] of Object.entries(PROJECT_DETECTION_RULES)) {
      const score = this.calculateDetectionScore(projectPath, config);
      detectionResults.set(projectType as ProjectType, score);
    }
    
    // 找到得分最高的项目类型
    let maxScore = 0;
    let detectedType: ProjectType = 'generic';
    
    for (const [type, score] of detectionResults) {
      if (score > maxScore) {
        maxScore = score;
        detectedType = type;
      }
    }
    
    return maxScore > 0 ? detectedType : 'generic';
  }
  
  /**
   * 计算检测得分
   */
  private static calculateDetectionScore(
    projectPath: string,
    config: ProjectDetectionConfig
  ): number {
    let score = 0;
    
    // 检测必需文件
    for (const file of config.detectionFiles) {
      if (existsSync(resolve(projectPath, file))) {
        score += 10;
      }
    }
    
    // 检测依赖
    if (config.dependencies) {
      const packageJsonPath = resolve(projectPath, 'package.json');
      if (existsSync(packageJsonPath)) {
        try {
          const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
          const allDeps = {
            ...packageJson.dependencies,
            ...packageJson.devDependencies,
            ...packageJson.peerDependencies,
          };
          
          for (const dep of config.dependencies) {
            if (allDeps[dep]) {
              score += 20;
            }
          }
        } catch {
          // 忽略解析错误
        }
      }
    }
    
    // 检测配置文件模式
    if (config.configPatterns) {
      for (const pattern of config.configPatterns) {
        // 简单的文件名匹配，可以扩展为 glob 匹配
        const files = pattern.includes('*') 
          ? this.findMatchingFiles(projectPath, pattern)
          : [pattern];
        
        for (const file of files) {
          if (existsSync(resolve(projectPath, file))) {
            score += 5;
          }
        }
      }
    }
    
    // 检测目录结构
    if (config.directoryPatterns) {
      for (const dir of config.directoryPatterns) {
        if (existsSync(resolve(projectPath, dir))) {
          score += 15;
        }
      }
    }
    
    return score;
  }
  
  /**
   * 查找匹配的文件（简单实现）
   */
  private static findMatchingFiles(projectPath: string, pattern: string): string[] {
    // 这里可以实现更复杂的 glob 匹配逻辑
    // 暂时返回常见的匹配项
    const commonMatches: Record<string, string[]> = {
      'babel.config.*': ['babel.config.js', 'babel.config.json', 'babel.config.ts'],
      'webpack.config.*': ['webpack.config.js', 'webpack.config.ts'],
      'vite.config.*': ['vite.config.js', 'vite.config.ts'],
      '.eslintrc.*': ['.eslintrc.js', '.eslintrc.json', '.eslintrc.yml'],
    };
    
    return commonMatches[pattern] || [pattern];
  }
}

// =============================================================================
// 预设配置定义
// =============================================================================

/**
 * React项目预设配置
 */
const REACT_PRESET: PartialCognitiveConfig = {
  metadata: {
    name: 'React项目预设',
    description: 'React项目的认知复杂度分析优化配置',
    version: '2.0',
  },
  rules: {
    jsx: {
      enabled: true,
      exemptions: {
        structuralNodes: true,
        attributeExpressions: true,
        nullishCoalescing: true,
        simpleConditionals: true,
      },
      scoring: {
        conditionalRendering: true,
        eventHandlers: true,
        loopRendering: true,
        nestedComponents: false,
      },
      advanced: {
        detectHookComplexity: true,
        analyzeProps: false,
        trackContextUsage: true,
      },
    },
  },
  exclude: [
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/*.stories.ts',
    '**/*.stories.tsx',
    '**/*.d.ts',
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    'public/**',
    'static/**',
    'coverage/**',
  ],
  smartExclusion: {
    enabled: true,
    customRules: [
      'src/**/*.mock.ts',
      'src/**/*.mock.tsx',
      '__mocks__/**',
    ],
  },
  output: {
    includeMetrics: true,
    detailLevel: 'standard',
    format: ['text', 'html'],
  },
};

/**
 * Next.js项目预设配置
 */
const NEXTJS_PRESET: PartialCognitiveConfig = {
  ...REACT_PRESET,
  metadata: {
    name: 'Next.js项目预设',
    description: 'Next.js项目的认知复杂度分析优化配置',
    version: '2.0',
  },
  exclude: [
    ...REACT_PRESET.exclude!,
    '**/.next/**',
    '**/out/**',
    'pages/api/**/*.test.*',
    'app/**/*.test.*',
  ],
  rules: {
    ...REACT_PRESET.rules,
    jsx: {
      ...REACT_PRESET.rules!.jsx,
      advanced: {
        ...REACT_PRESET.rules!.jsx!.advanced,
        analyzeProps: true as boolean, // Next.js中Props分析更重要
        detectHookComplexity: REACT_PRESET.rules!.jsx!.advanced?.detectHookComplexity ?? true,
        trackContextUsage: REACT_PRESET.rules!.jsx!.advanced?.trackContextUsage ?? true,
      },
    },
  },
};

/**
 * Vue项目预设配置
 */
const VUE_PRESET: PartialCognitiveConfig = {
  metadata: {
    name: 'Vue项目预设',
    description: 'Vue项目的认知复杂度分析优化配置',
    version: '2.0',
  },
  rules: {
    jsx: {
      enabled: false, // Vue主要使用模板语法，不是JSX
    },
    logical: {
      enableMixedLogicOperatorPenalty: true,
      logicalChainThreshold: 2,
      detectShortCircuit: true,
    },
  },
  exclude: [
    '**/*.test.ts',
    '**/*.test.js',
    '**/*.spec.ts',
    '**/*.spec.js',
    '**/*.d.ts',
    '**/node_modules/**',
    '**/dist/**',
    'coverage/**',
  ],
};

/**
 * Node.js项目预设配置
 */
const NODEJS_PRESET: PartialCognitiveConfig = {
  metadata: {
    name: 'Node.js项目预设',
    description: 'Node.js服务端项目的认知复杂度分析配置',
    version: '2.0',
  },
  rules: {
    jsx: {
      enabled: false,
    },
    logical: {
      enableMixedLogicOperatorPenalty: true,
      logicalChainThreshold: 3,
      detectShortCircuit: true,
    },
    nesting: {
      maxNestingDepth: 40, // 服务端代码通常层次更深
      recursionChainThreshold: 15,
      enableIterativeParser: true,
    },
  },
  performance: {
    maxConcurrency: 6, // 服务端可以使用更多并发
    fileProcessingTimeout: 45000, // 更长的处理时间
    enablePerformanceLogging: true,
  },
  exclude: [
    '**/*.test.ts',
    '**/*.test.js',
    '**/*.spec.ts',
    '**/*.spec.js',
    '**/*.d.ts',
    '**/node_modules/**',
    '**/dist/**',
    '**/lib/**',
    'coverage/**',
    'logs/**',
  ],
};

/**
 * 通用TypeScript项目预设
 */
const TYPESCRIPT_PRESET: PartialCognitiveConfig = {
  metadata: {
    name: 'TypeScript项目预设',
    description: '通用TypeScript项目的认知复杂度分析配置',
    version: '2.0',
  },
  rules: {
    jsx: {
      enabled: false,
    },
    logical: {
      enableMixedLogicOperatorPenalty: false,
      logicalChainThreshold: 4,
      detectShortCircuit: false,
    },
  },
  exclude: [
    '**/*.test.ts',
    '**/*.spec.ts',
    '**/*.d.ts',
    '**/node_modules/**',
    '**/dist/**',
    'coverage/**',
  ],
};

/**
 * 预设配置映射
 */
const PRESET_CONFIGS: Record<ProjectType, PartialCognitiveConfig> = {
  react: REACT_PRESET,
  nextjs: NEXTJS_PRESET,
  gatsby: REACT_PRESET, // Gatsby使用React预设
  vue: VUE_PRESET,
  angular: TYPESCRIPT_PRESET, // Angular使用TypeScript预设，暂时简化
  nodejs: NODEJS_PRESET,
  generic: TYPESCRIPT_PRESET,
};

// =============================================================================
// 配置工厂
// =============================================================================

/**
 * 配置生成选项
 */
export interface ConfigGenerationOptions {
  /** 项目路径 */
  projectPath?: string;
  /** 强制指定项目类型 */
  forceProjectType?: ProjectType;
  /** 自定义配置覆盖 */
  customOverrides?: PartialCognitiveConfig;
  /** 是否启用性能优化 */
  enablePerformanceOptimization?: boolean;
  /** 是否启用详细输出 */
  enableVerboseOutput?: boolean;
}

/**
 * 智能配置工厂
 */
export class ConfigFactory {
  /**
   * 生成智能配置
   */
  public static generateConfig(options: ConfigGenerationOptions = {}): CognitiveComplexityConfig {
    const {
      projectPath = process.cwd(),
      forceProjectType,
      customOverrides = {},
      enablePerformanceOptimization = false,
      enableVerboseOutput = false,
    } = options;
    
    // 检测或使用指定的项目类型
    const projectType = forceProjectType || ProjectTypeDetector.detectProjectType(projectPath);
    
    // 获取基础配置
    const baseConfig = createDefaultConfig();
    
    // 获取项目类型预设
    const presetConfig = PRESET_CONFIGS[projectType] || PRESET_CONFIGS.generic;
    
    // 应用性能优化
    const performanceConfig = enablePerformanceOptimization 
      ? this.createPerformanceConfig()
      : {};
    
    // 应用详细输出配置
    const verboseConfig = enableVerboseOutput
      ? this.createVerboseConfig()
      : {};
    
    // 合并所有配置
    const finalConfig = mergeConfigs(
      baseConfig,
      presetConfig,
      performanceConfig,
      verboseConfig,
      customOverrides
    ) as CognitiveComplexityConfig;
    
    // 更新元数据
    finalConfig.metadata = {
      ...finalConfig.metadata,
      name: `${projectType}项目智能配置`,
      description: `基于${projectType}项目类型自动生成的配置`,
      createdAt: new Date().toISOString(),
    };
    
    // 更新智能排除配置
    if (finalConfig.smartExclusion.enabled) {
      finalConfig.smartExclusion.detectedProjectType = projectType;
    }
    
    return finalConfig;
  }
  
  /**
   * 为特定项目类型生成配置
   */
  public static generateForProjectType(
    projectType: ProjectType,
    customOverrides: PartialCognitiveConfig = {}
  ): CognitiveComplexityConfig {
    return this.generateConfig({
      forceProjectType: projectType,
      customOverrides,
    });
  }
  
  /**
   * 生成高性能配置
   */
  public static generateHighPerformanceConfig(
    projectType?: ProjectType
  ): CognitiveComplexityConfig {
    return this.generateConfig({
      forceProjectType: projectType,
      enablePerformanceOptimization: true,
    });
  }
  
  /**
   * 生成开发调试配置
   */
  public static generateDebugConfig(
    projectType?: ProjectType
  ): CognitiveComplexityConfig {
    return this.generateConfig({
      forceProjectType: projectType,
      enableVerboseOutput: true,
      customOverrides: {
        performance: {
          enablePerformanceLogging: true,
        },
        output: {
          includeMetrics: true,
          detailLevel: 'verbose',
        },
      },
    });
  }
  
  /**
   * 创建性能优化配置
   */
  private static createPerformanceConfig(): PartialCognitiveConfig {
    return {
      performance: {
        maxConcurrency: Math.max(2, Math.min(8, require('os').cpus().length)),
        cacheSize: 2000,
        streamingThreshold: 512 * 1024, // 512KB
        enablePerformanceLogging: false,
      },
      rules: {
        nesting: {
          enableIterativeParser: true,
        },
      },
    };
  }
  
  /**
   * 创建详细输出配置
   */
  private static createVerboseConfig(): PartialCognitiveConfig {
    return {
      output: {
        includeMetrics: true,
        detailLevel: 'verbose',
        format: ['text', 'json'],
      },
      performance: {
        enablePerformanceLogging: true,
      },
    };
  }
}

// =============================================================================
// 配置预设导出
// =============================================================================

/**
 * 获取所有可用的预设配置
 */
export function getAvailablePresets(): Record<string, PartialCognitiveConfig> {
  return {
    react: REACT_PRESET,
    nextjs: NEXTJS_PRESET,
    vue: VUE_PRESET,
    nodejs: NODEJS_PRESET,
    typescript: TYPESCRIPT_PRESET,
  };
}

/**
 * 获取特定预设配置
 */
export function getPresetConfig(presetName: keyof typeof PRESET_CONFIGS): PartialCognitiveConfig {
  return PRESET_CONFIGS[presetName] || PRESET_CONFIGS.generic;
}

/**
 * 检测项目类型的便利函数
 */
export function detectProjectType(projectPath: string = process.cwd()): ProjectType {
  return ProjectTypeDetector.detectProjectType(projectPath);
}