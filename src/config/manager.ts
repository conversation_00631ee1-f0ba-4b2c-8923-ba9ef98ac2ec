import { cosmiconfig } from 'cosmiconfig';
import type { CognitiveConfig, CLIOptions, ExclusionRules } from './types';

export class ConfigManager {
  private static DEFAULT_CONFIG: CognitiveConfig = {
    failOnComplexity: 15,
    exclude: [
      "**/*.test.ts",
      "**/*.spec.ts", 
      "**/node_modules/**",
      "dist/**"
    ],
    report: {},
    severityMapping: [
      { level: 'Critical', threshold: 30 },
      { level: 'Warning', threshold: 12 }
    ],
    rules: {
      enableMixedLogicOperatorPenalty: false,
      recursionChainThreshold: 10
    },
    // 智能排除功能默认配置
    excludeDefaults: true,
    includeOverrides: [],
    disableSmartExclusion: false
  };
  
  public static async loadConfig(configPath?: string, searchFrom?: string, quiet: boolean = false): Promise<CognitiveConfig> {
    try {
      // 为每次调用创建新的cosmiconfig实例，避免并发测试中的内存竞态
      const explorer = cosmiconfig('cognitive-complexity', {
        searchPlaces: [
          'package.json',
          'cognitive.config.json',
          'cognitive.config.js', 
          'cognitive.config.yaml',
          'cognitive.config.yml',
          'cognitive-complexity.config.js',
          'cognitive-complexity.config.ts',
          'cognitive-complexity.config.json',
          '.cognitiverc',
          '.cognitiverc.json',
          '.cognitive-complexityrc',
          '.cognitive-complexityrc.json',
          '.cognitive-complexityrc.js',
          '.cognitive-complexityrc.ts',
        ],
        loaders: {
          '.ts': async (filepath: string) => {
            // 使用Bun的动态导入加载TypeScript配置文件
            const config = await import(filepath);
            return config.default || config;
          }
        }
      });
      let result;
      
      if (configPath) {
        // 加载指定的配置文件
        try {
          result = await explorer.load(configPath);
          if (!result) {
            throw new Error(`Configuration file not found: ${configPath}`);
          }
        } catch (error) {
          // 检查是否是文件不存在的错误
          if (error instanceof Error && error.message.includes('ENOENT')) {
            throw error; // 文件不存在，应该失败
          }
          // 其他错误（如JSON解析错误）应该警告并使用默认配置
          if (!quiet) {
            console.warn(`Failed to load config file ${configPath}: ${error instanceof Error ? error.message : String(error)}`);
            console.log('Using default configuration');
          }
          return this.DEFAULT_CONFIG;
        }
      } else {
        // 自动搜索配置文件 - 找不到时使用默认配置
        if (searchFrom) {
          result = await explorer.search(searchFrom);
        } else {
          result = await explorer.search();
        }
        
        if (!result) {
          if (!quiet) {
            console.log('No config file found, using default configuration');
          }
          return this.DEFAULT_CONFIG;
        }
      }
      
      const mergedConfig = this.mergeWithDefaults(result.config);
      if (!quiet) {
        console.log(`Loaded config from: ${result.filepath}`);
      }
      
      return this.validateConfig(mergedConfig);
    } catch (error) {
      // 如果是明确指定配置文件且是文件不存在错误，应该抛出错误
      if (configPath && error instanceof Error && error.message.includes('ENOENT')) {
        throw error;
      }
      
      // 其他所有错误都fallback到默认配置
      if (!quiet) {
        console.warn(`Failed to load config: ${(error as Error).message}`);
        console.log('Using default configuration');
      }
      return this.DEFAULT_CONFIG;
    }
  }
  
  private static mergeWithDefaults(userConfig: Partial<CognitiveConfig>): CognitiveConfig {
    return {
      ...this.DEFAULT_CONFIG,
      ...userConfig,
      severityMapping: userConfig.severityMapping || this.DEFAULT_CONFIG.severityMapping,
      exclude: [
        ...this.DEFAULT_CONFIG.exclude,
        ...(userConfig.exclude || [])
      ],
      // 智能排除功能配置合并
      excludeDefaults: userConfig.excludeDefaults !== undefined ? userConfig.excludeDefaults : this.DEFAULT_CONFIG.excludeDefaults,
      includeOverrides: [
        ...(this.DEFAULT_CONFIG.includeOverrides || []),
        ...(userConfig.includeOverrides || [])
      ],
      disableSmartExclusion: userConfig.disableSmartExclusion !== undefined ? userConfig.disableSmartExclusion : this.DEFAULT_CONFIG.disableSmartExclusion,
      // 正确合并嵌套的rules配置
      rules: {
        ...(this.DEFAULT_CONFIG.rules || {}),
        ...(userConfig.rules || {})
      }
    };
  }
  
  public static validateConfig(config: any): CognitiveConfig {
    const errors: string[] = [];
    
    if (typeof config.failOnComplexity !== 'number' || config.failOnComplexity < 0) {
      errors.push('failOnComplexity must be a non-negative number');
    }
    
    if (!Array.isArray(config.exclude)) {
      errors.push('exclude must be an array of strings');
    }
    
    if (!Array.isArray(config.severityMapping)) {
      errors.push('severityMapping must be an array');
    } else {
      config.severityMapping.forEach((mapping: any, index: number) => {
        if (!mapping.level || !['Critical', 'Warning', 'Info'].includes(mapping.level)) {
          errors.push(`severityMapping[${index}].level must be 'Critical', 'Warning', or 'Info'`);
        }
        if (typeof mapping.threshold !== 'number' || mapping.threshold < 0) {
          errors.push(`severityMapping[${index}].threshold must be a non-negative number`);
        }
      });
    }
    
    if (config.report && typeof config.report !== 'object') {
      errors.push('report must be an object');
    }
    
    if (config.rules && typeof config.rules !== 'object') {
      errors.push('rules must be an object');
    } else if (config.rules) {
      if (config.rules.enableMixedLogicOperatorPenalty !== undefined && typeof config.rules.enableMixedLogicOperatorPenalty !== 'boolean') {
        errors.push('rules.enableMixedLogicOperatorPenalty must be a boolean');
      }
      if (config.rules.recursionChainThreshold !== undefined && (typeof config.rules.recursionChainThreshold !== 'number' || config.rules.recursionChainThreshold < 0)) {
        errors.push('rules.recursionChainThreshold must be a non-negative number');
      }
    }

    // 验证智能排除功能配置
    if (config.excludeDefaults !== undefined && typeof config.excludeDefaults !== 'boolean') {
      errors.push('excludeDefaults must be a boolean');
    }

    if (config.includeOverrides !== undefined) {
      if (!Array.isArray(config.includeOverrides)) {
        errors.push('includeOverrides must be an array of strings');
      } else {
        config.includeOverrides.forEach((pattern: any, index: number) => {
          if (typeof pattern !== 'string') {
            errors.push(`includeOverrides[${index}] must be a string`);
          }
        });
      }
    }

    if (config.disableSmartExclusion !== undefined && typeof config.disableSmartExclusion !== 'boolean') {
      errors.push('disableSmartExclusion must be a boolean');
    }

    // 验证性能和安全配置
    if (config.fileProcessingTimeout !== undefined && (typeof config.fileProcessingTimeout !== 'number' || config.fileProcessingTimeout <= 0)) {
      errors.push('fileProcessingTimeout must be a positive number');
    }

    if (config.maxRecursionDepth !== undefined && (typeof config.maxRecursionDepth !== 'number' || config.maxRecursionDepth <= 0)) {
      errors.push('maxRecursionDepth must be a positive number');
    }

    if (config.enableIterativeParser !== undefined && typeof config.enableIterativeParser !== 'boolean') {
      errors.push('enableIterativeParser must be a boolean');
    }

    if (config.enablePerformanceLogging !== undefined && typeof config.enablePerformanceLogging !== 'boolean') {
      errors.push('enablePerformanceLogging must be a boolean');
    }
    
    if (errors.length > 0) {
      throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
    }
    
    return config as CognitiveConfig;
  }

  /**
   * 合并运行时排除规则 - 将配置文件设置与CLI参数智能合并
   */
  public static mergeRuntimeExcludes(
    config: CognitiveConfig, 
    cliOptions: CLIOptions,
    quiet: boolean = false
  ): { patterns: string[]; rules: ExclusionRules[] } {
    const patterns: string[] = [];
    const rules: ExclusionRules[] = [];

    // 1. 验证CLI参数中的排除模式
    const validatedCliExcludes = this.validateExcludePatterns(cliOptions.exclude || []);
    const validatedCliPatterns = this.validateExcludePatterns(cliOptions.excludePattern || []);

    if (validatedCliExcludes.invalid.length > 0) {
      if (!quiet) {
        console.warn('警告: 以下CLI exclude模式无效:', validatedCliExcludes.invalid.join(', '));
      }
    }
    if (validatedCliPatterns.invalid.length > 0) {
      if (!quiet) {
        console.warn('警告: 以下CLI excludePattern模式无效:', validatedCliPatterns.invalid.join(', '));
      }
    }

    // 2. 基本排除规则（总是生效）
    const basePatterns = ['**/*.d.ts'];
    patterns.push(...basePatterns);
    rules.push({
      patterns: basePatterns,
      reason: '基本排除规则 - TypeScript声明文件',
      source: 'default'
    });

    // 3. 配置文件排除规则（根据excludeDefaults设置）
    const useConfigDefaults = cliOptions.excludeDefaults !== false && config.excludeDefaults !== false;
    if (useConfigDefaults && config.exclude && config.exclude.length > 0) {
      const validatedConfigExcludes = this.validateExcludePatterns(config.exclude);
      
      if (validatedConfigExcludes.invalid.length > 0) {
        if (!quiet) {
          console.warn('警告: 以下配置文件exclude模式无效:', validatedConfigExcludes.invalid.join(', '));
        }
      }

      patterns.push(...validatedConfigExcludes.valid);
      rules.push({
        patterns: validatedConfigExcludes.valid,
        reason: '配置文件排除规则',
        source: 'config'
      });
    }

    // 4. CLI排除参数（高优先级）
    if (validatedCliExcludes.valid.length > 0) {
      patterns.push(...validatedCliExcludes.valid);
      rules.push({
        patterns: validatedCliExcludes.valid,
        reason: 'CLI --exclude 参数',
        source: 'cli'
      });
    }

    // 5. CLI excludePattern参数（高优先级）
    if (validatedCliPatterns.valid.length > 0) {
      patterns.push(...validatedCliPatterns.valid);
      rules.push({
        patterns: validatedCliPatterns.valid,
        reason: 'CLI --exclude-pattern 参数',
        source: 'cli'
      });
    }

    // 去重并排序
    const uniquePatterns = [...new Set(patterns)];

    return {
      patterns: uniquePatterns,
      rules
    };
  }

  /**
   * 验证排除模式的有效性
   */
  private static validateExcludePatterns(patterns: string[]): { valid: string[]; invalid: string[] } {
    const valid: string[] = [];
    const invalid: string[] = [];

    for (const pattern of patterns) {
      if (this.isValidGlobPattern(pattern)) {
        valid.push(pattern);
      } else {
        invalid.push(pattern);
      }
    }

    return { valid, invalid };
  }

  /**
   * 检查是否为有效的glob模式
   */
  private static isValidGlobPattern(pattern: string): boolean {
    // 基本验证：非空字符串，不包含危险字符
    if (!pattern || typeof pattern !== 'string') {
      return false;
    }

    // 检查是否包含危险的路径操作
    if (pattern.includes('..') || pattern.startsWith('/')) {
      return false;
    }

    // 验证glob语法基本有效性
    try {
      // 简单的glob模式验证 - 避免明显的语法错误
      if (pattern.includes('[') && !pattern.includes(']')) {
        return false;
      }
      if (pattern.includes('{') && !pattern.includes('}')) {
        return false;
      }
      return true;
    } catch {
      return false;
    }
  }
}