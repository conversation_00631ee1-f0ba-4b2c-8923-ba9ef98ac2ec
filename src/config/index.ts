/**
 * 现代化配置系统 - 统一导出
 * 
 * 这个文件提供配置系统的统一入口点，包括：
 * 1. 类型安全的配置架构
 * 2. 现代化配置管理器
 * 3. 智能配置工厂和预设
 * 4. 向后兼容性支持
 */

// =============================================================================
// 核心配置类型和接口
// =============================================================================
export type {
  // 主配置接口
  CognitiveComplexityConfig,
  PartialCognitiveConfig,
  ResolvedConfig,
  
  // 规则配置接口
  JSXRuleConfig,
  LogicalRuleConfig,
  NestingRuleConfig,
  
  // 系统配置接口
  PerformanceConfig,
  OutputConfig,
  SmartExclusionConfig,
  UIConfig,
  PluginConfig,
  
  // 辅助类型
  SeverityMapping,
  ProjectType,
  OutputFormat,
  DetailLevel,
  
  // 验证相关类型
  ConfigValidationResult,
  ConfigValidationError,
  ConfigSchema,
  
  // 热重载相关类型
  ConfigChangeEvent,
  ConfigChangeListener,
  ConfigChangeEventType,
  
  // 向后兼容的类型别名
  CognitiveComplexityConfig as CognitiveConfig,
  SeverityMapping as SeverityLevel,
} from './schema';

// =============================================================================
// 项目检测相关类型和功能
// =============================================================================
export type {
  ConfigRecommendation,
} from './project-detector';

export {
  ProjectDetector,
  generateProjectConfig,
} from './project-detector';

// =============================================================================
// 现代化配置管理器
// =============================================================================
export {
  ModernConfigManager,
  loadConfig,
  loadSmartConfig,
  enableConfigHotReload,
  mergeConfigs,
  createDefaultConfig,
} from './modern-manager';

// =============================================================================
// 智能配置工厂和预设
// =============================================================================
export {
  ConfigFactory,
  ProjectTypeDetector,
  getAvailablePresets,
  getPresetConfig,
} from './factory';

export type {
  ConfigGenerationOptions,
} from './factory';

// =============================================================================
// 向后兼容的旧配置管理器
// =============================================================================
export {
  ConfigManager,
} from './manager';

export type {
  CLIOptions,
  ExclusionRules,
  ExclusionStats,
} from './types';

// =============================================================================
// 便利函数和工具
// =============================================================================

import { ConfigFactory, detectProjectType } from './factory';
import { loadConfig, createDefaultConfig } from './modern-manager';
import type { 
  CognitiveComplexityConfig, 
  PartialCognitiveConfig, 
  ProjectType
} from './schema';

// 配置生成选项
interface ConfigGenerationOptions {
  enablePerformanceOptimizations?: boolean;
  includeAdvancedRules?: boolean;
  customRules?: Record<string, unknown>;
}

/**
 * 快速生成适合当前项目的配置
 */
export async function generateSmartConfig(
  projectPath: string = process.cwd(),
  options: Partial<ConfigGenerationOptions> = {}
): Promise<CognitiveComplexityConfig> {
  return ConfigFactory.generateConfig({
    projectPath,
    ...options,
  });
}

/**
 * 加载或生成配置的智能函数
 * 
 * 如果找到配置文件则加载，否则根据项目类型生成智能配置
 */
export async function loadOrGenerateConfig(
  configPath?: string,
  projectPath: string = process.cwd()
): Promise<CognitiveComplexityConfig> {
  try {
    // 首先尝试加载现有配置
    const resolvedConfig = await loadConfig(configPath, projectPath);
    
    // 如果是默认配置且没有指定配置文件，生成智能配置
    if (resolvedConfig.source.isDefault && !configPath) {
      return generateSmartConfig(projectPath);
    }
    
    return resolvedConfig;
  } catch (error) {
    // 加载失败时生成智能配置
    console.warn('Failed to load config, generating smart config for project');
    return generateSmartConfig(projectPath);
  }
}

/**
 * 创建项目类型特定的配置
 */
export function createProjectConfig(
  projectType: ProjectType,
  customOverrides: PartialCognitiveConfig = {}
): CognitiveComplexityConfig {
  return ConfigFactory.generateForProjectType(projectType, customOverrides);
}

/**
 * 创建高性能配置
 */
export function createHighPerformanceConfig(projectType?: ProjectType): CognitiveComplexityConfig {
  return ConfigFactory.generateHighPerformanceConfig(projectType);
}

/**
 * 创建开发调试配置
 */
export function createDebugConfig(projectType?: ProjectType): CognitiveComplexityConfig {
  return ConfigFactory.generateDebugConfig(projectType);
}

/**
 * 验证配置的便利函数
 */
export function validateConfig(config: CognitiveComplexityConfig) {
  const { ModernConfigManager } = require('./modern-manager');
  return ModernConfigManager.getInstance().validateConfig(config);
}

/**
 * 获取配置摘要信息
 */
export function getConfigSummary(config: CognitiveComplexityConfig): {
  projectType?: ProjectType;
  rulesEnabled: string[];
  outputFormats: string[];
  excludePatterns: number;
  performanceLevel: 'basic' | 'optimized' | 'high';
} {
  const rulesEnabled: string[] = [];
  
  if (config.rules.jsx.enabled) rulesEnabled.push('JSX');
  if (config.rules.logical.enableMixedLogicOperatorPenalty) rulesEnabled.push('Mixed Logic Operators');
  if (config.rules.nesting.enableIterativeParser) rulesEnabled.push('Iterative Parser');
  
  let performanceLevel: 'basic' | 'optimized' | 'high' = 'basic';
  if (config.performance.maxConcurrency > 4) performanceLevel = 'optimized';
  if (config.performance.maxConcurrency > 6 && config.performance.enablePerformanceLogging) {
    performanceLevel = 'high';
  }
  
  return {
    projectType: config.smartExclusion.detectedProjectType,
    rulesEnabled,
    outputFormats: config.output.format,
    excludePatterns: config.exclude.length,
    performanceLevel,
  };
}

// =============================================================================
// 配置迁移工具
// =============================================================================

/**
 * 从旧配置格式迁移到新格式
 */
export function migrateFromLegacyConfig(legacyConfig: any): CognitiveComplexityConfig {
  const baseConfig = createDefaultConfig();
  
  // 映射旧配置字段到新字段
  const migrated: PartialCognitiveConfig = {
    failOnComplexity: legacyConfig.failOnComplexity ?? baseConfig.failOnComplexity,
    exclude: legacyConfig.exclude ?? baseConfig.exclude,
    severityMapping: legacyConfig.severityMapping ?? baseConfig.severityMapping,
    
    // 迁移rules配置
    rules: {
      jsx: {
        enabled: true, // 默认启用JSX支持
        exemptions: baseConfig.rules.jsx.exemptions,
        scoring: baseConfig.rules.jsx.scoring,
        advanced: baseConfig.rules.jsx.advanced,
      },
      logical: {
        enableMixedLogicOperatorPenalty: legacyConfig.rules?.enableMixedLogicOperatorPenalty ?? false,
        logicalChainThreshold: 3,
        detectShortCircuit: false,
      },
      nesting: {
        maxNestingDepth: legacyConfig.maxRecursionDepth ?? 50,
        recursionChainThreshold: legacyConfig.rules?.recursionChainThreshold ?? 10,
        enableIterativeParser: legacyConfig.enableIterativeParser ?? false,
      },
      custom: {},
    },
    
    // 迁移UI配置
    ui: {
      port: legacyConfig.ui?.port ?? 3000,
      host: legacyConfig.ui?.host ?? 'localhost',
      openBrowser: legacyConfig.ui?.openBrowser ?? false,
      autoShutdown: legacyConfig.ui?.autoShutdown ?? false,
      theme: 'auto',
    },
    
    // 迁移输出配置
    output: {
      includeMetrics: legacyConfig.enablePerformanceLogging ?? false,
      detailLevel: 'standard',
      format: ['text'],
      reportPaths: legacyConfig.report ?? {},
    },
    
    // 迁移性能配置
    performance: {
      maxConcurrency: 4,
      cacheSize: 1000,
      streamingThreshold: 1024 * 1024,
      fileProcessingTimeout: legacyConfig.fileProcessingTimeout ?? 30000,
      enablePerformanceLogging: legacyConfig.enablePerformanceLogging ?? false,
    },
    
    // 迁移智能排除配置
    smartExclusion: {
      enabled: !legacyConfig.disableSmartExclusion,
      projectTypeRules: baseConfig.smartExclusion.projectTypeRules,
      customRules: legacyConfig.includeOverrides ?? [],
    },
    
    // 添加迁移元数据
    metadata: {
      version: '2.0',
      name: 'Migrated Configuration',
      description: 'Configuration migrated from legacy format',
      createdAt: new Date().toISOString(),
    },
  };
  
  return { ...baseConfig, ...migrated } as CognitiveComplexityConfig;
}

/**
 * 检查配置是否为旧格式
 */
export function isLegacyConfig(config: any): boolean {
  // 检查是否包含旧格式的字段结构
  return (
    config &&
    typeof config === 'object' &&
    !config.metadata && // 新格式包含metadata
    !config.rules?.jsx && // 新格式包含jsx规则
    (config.rules?.enableMixedLogicOperatorPenalty !== undefined || // 旧格式字段
     config.enableIterativeParser !== undefined ||
     config.fileProcessingTimeout !== undefined)
  );
}