/**
 * 配置系统测试和验证工具
 * 
 * 用于验证配置系统的正确性，包括：
 * 1. 配置类型验证
 * 2. 配置合并测试
 * 3. 项目类型检测测试
 * 4. 性能基准测试
 */

import { writeFileSync, readFileSync, unlinkSync, mkdirSync, rmSync } from 'fs';
import { join, resolve } from 'path';
import { tmpdir } from 'os';
import type {
  CognitiveComplexityConfig,
  PartialCognitiveConfig,
  ResolvedConfig,
  ProjectType,
} from './schema';
import {
  loadConfig,
  createDefaultConfig,
  ConfigFactory,
  ProjectTypeDetector,
  mergeConfigs,
  validateConfig,
  migrateFromLegacyConfig,
} from './index';

// =============================================================================
// 测试配置样本
// =============================================================================

/**
 * 有效的测试配置
 */
const VALID_TEST_CONFIG: PartialCognitiveConfig = {
  failOnComplexity: 20,
  exclude: ['**/*.test.ts', 'dist/**'],
  include: ['src/**/*.ts'],
  rules: {
    jsx: {
      enabled: true,
      exemptions: {
        structuralNodes: true,
        attributeExpressions: false,
        nullishCoalescing: true,
        simpleConditionals: false,
      },
      scoring: {
        conditionalRendering: true,
        eventHandlers: true,
        loopRendering: false,
        nestedComponents: true,
      },
      advanced: {
        detectHookComplexity: true,
        analyzeProps: true,
        trackContextUsage: false,
      },
    },
    logical: {
      enableMixedLogicOperatorPenalty: true,
      logicalChainThreshold: 2,
      detectShortCircuit: true,
    },
  },
  performance: {
    maxConcurrency: 6,
    fileProcessingTimeout: 45000,
    enablePerformanceLogging: true,
  },
  output: {
    includeMetrics: true,
    detailLevel: 'verbose',
    format: ['text', 'json', 'html'],
  },
  metadata: {
    name: 'Test Configuration',
    description: 'Configuration for testing purposes',
    version: '2.0',
  },
};

/**
 * 无效的测试配置（用于验证错误处理）
 */
const INVALID_TEST_CONFIG: any = {
  failOnComplexity: -5, // 负数，无效
  exclude: ['not-an-array'], // 修复为数组
  rules: {
    jsx: {
      enabled: 'yes', // 应该是boolean
    },
    logical: {
      logicalChainThreshold: 'invalid', // 应该是数字
    },
  },
  performance: {
    maxConcurrency: 0, // 应该大于0
    fileProcessingTimeout: -1000, // 负数，无效
  },
};

/**
 * 遗留配置格式样本
 */
const LEGACY_CONFIG_SAMPLE = {
  failOnComplexity: 15,
  exclude: ['**/*.test.ts'],
  report: {
    json: 'complexity.json',
    html: 'complexity.html',
  },
  severityMapping: [
    { level: 'Critical', threshold: 30 },
    { level: 'Warning', threshold: 12 },
  ],
  rules: {
    enableMixedLogicOperatorPenalty: true,
    recursionChainThreshold: 8,
  },
  ui: {
    port: 3001,
    openBrowser: false,
  },
  fileProcessingTimeout: 25000,
  enablePerformanceLogging: true,
  disableSmartExclusion: false,
  includeOverrides: ['src/important.ts'],
};

// =============================================================================
// 配置验证测试器
// =============================================================================

/**
 * 配置验证测试器
 */
export class ConfigValidator {
  /**
   * 运行所有配置验证测试
   */
  public static async runAllTests(): Promise<ValidationTestResult> {
    const results: ValidationTestResult = {
      passed: 0,
      failed: 0,
      tests: [],
      startTime: Date.now(),
      endTime: 0,
    };
    
    console.log('🧪 Running configuration system validation tests...\n');
    
    // 基本配置验证测试
    await this.runTest('Basic Config Validation', () => this.testBasicValidation(), results);
    
    // 配置合并测试
    await this.runTest('Config Merging', () => this.testConfigMerging(), results);
    
    // 项目类型检测测试
    await this.runTest('Project Type Detection', () => this.testProjectTypeDetection(), results);
    
    // 配置工厂测试
    await this.runTest('Config Factory', () => this.testConfigFactory(), results);
    
    // 热重载测试
    await this.runTest('Hot Reload', () => this.testHotReload(), results);
    
    // 遗留配置迁移测试
    await this.runTest('Legacy Config Migration', () => this.testLegacyMigration(), results);
    
    // 性能测试
    await this.runTest('Performance Benchmark', () => this.testPerformance(), results);
    
    results.endTime = Date.now();
    
    this.printResults(results);
    
    return results;
  }
  
  /**
   * 测试基本配置验证
   */
  private static testBasicValidation(): TestResult {
    try {
      // 测试有效配置
      const validConfig = mergeConfigs(createDefaultConfig(), VALID_TEST_CONFIG) as CognitiveComplexityConfig;
      const validationResult = validateConfig(validConfig);
      
      if (!validationResult.isValid) {
        return {
          success: false,
          message: `Valid config failed validation: ${validationResult.errors.map((e: any) => e.message).join(', ')}`,
        };
      }
      
      // 测试无效配置
      const invalidConfig = mergeConfigs(createDefaultConfig(), INVALID_TEST_CONFIG) as CognitiveComplexityConfig;
      const invalidValidationResult = validateConfig(invalidConfig);
      
      if (invalidValidationResult.isValid) {
        return {
          success: false,
          message: 'Invalid config passed validation when it should have failed',
        };
      }
      
      return {
        success: true,
        message: `Validation working correctly (${invalidValidationResult.errors.length} errors detected)`,
      };
    } catch (error) {
      return {
        success: false,
        message: `Validation test failed: ${error}`,
      };
    }
  }
  
  /**
   * 测试配置合并
   */
  private static testConfigMerging(): TestResult {
    try {
      const base = createDefaultConfig();
      const override = VALID_TEST_CONFIG;
      
      const merged = mergeConfigs(base, override) as CognitiveComplexityConfig;
      
      // 验证合并结果
      if (merged.failOnComplexity !== override.failOnComplexity) {
        return {
          success: false,
          message: 'Basic field merging failed',
        };
      }
      
      if (!merged.rules.jsx.enabled || merged.rules.jsx.exemptions.structuralNodes !== true) {
        return {
          success: false,
          message: 'Nested object merging failed',
        };
      }
      
      if (merged.exclude.length < base.exclude.length) {
        return {
          success: false,
          message: 'Array merging failed',
        };
      }
      
      return {
        success: true,
        message: 'Config merging working correctly',
      };
    } catch (error) {
      return {
        success: false,
        message: `Config merging test failed: ${error}`,
      };
    }
  }
  
  /**
   * 测试项目类型检测
   */
  private static testProjectTypeDetection(): TestResult {
    try {
      const tempDir = join(tmpdir(), 'cognitive-complexity-test-' + Date.now());
      mkdirSync(tempDir, { recursive: true });
      
      try {
        // 创建React项目结构
        const reactPackageJson = {
          dependencies: {
            react: '^18.0.0',
            'react-dom': '^18.0.0',
          },
        };
        writeFileSync(join(tempDir, 'package.json'), JSON.stringify(reactPackageJson, null, 2));
        mkdirSync(join(tempDir, 'src', 'components'), { recursive: true });
        
        const detectedType = ProjectTypeDetector.detectProjectType(tempDir);
        
        if (detectedType !== 'react') {
          return {
            success: false,
            message: `Expected 'react', got '${detectedType}'`,
          };
        }
        
        return {
          success: true,
          message: `Project type detection working (detected: ${detectedType})`,
        };
      } finally {
        // 清理临时目录
        rmSync(tempDir, { recursive: true, force: true });
      }
    } catch (error) {
      return {
        success: false,
        message: `Project type detection test failed: ${error}`,
      };
    }
  }
  
  /**
   * 测试配置工厂
   */
  private static testConfigFactory(): TestResult {
    try {
      // 测试React项目配置生成
      const reactConfig = ConfigFactory.generateForProjectType('react');
      
      if (!reactConfig.rules.jsx.enabled) {
        return {
          success: false,
          message: 'React config should have JSX rules enabled',
        };
      }
      
      if (!reactConfig.metadata.name?.includes('react')) {
        return {
          success: false,
          message: 'React config metadata not set correctly',
        };
      }
      
      // 测试高性能配置生成
      const perfConfig = ConfigFactory.generateHighPerformanceConfig('nodejs');
      
      if (perfConfig.performance.maxConcurrency <= 2) {
        return {
          success: false,
          message: 'High performance config should have higher concurrency',
        };
      }
      
      return {
        success: true,
        message: 'Config factory working correctly',
      };
    } catch (error) {
      return {
        success: false,
        message: `Config factory test failed: ${error}`,
      };
    }
  }
  
  /**
   * 测试热重载
   */
  private static async testHotReload(): Promise<TestResult> {
    try {
      const tempConfigPath = join(tmpdir(), 'test-config-' + Date.now() + '.json');
      
      // 创建初始配置文件
      writeFileSync(tempConfigPath, JSON.stringify({
        failOnComplexity: 10,
      }, null, 2));
      
      let changeDetected = false;
      
      // 设置热重载监听
      const { enableConfigHotReload } = await import('./index');
      enableConfigHotReload(tempConfigPath, (event) => {
        changeDetected = true;
      });
      
      // 等待一小段时间让监听器设置完成
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 修改配置文件
      writeFileSync(tempConfigPath, JSON.stringify({
        failOnComplexity: 20,
      }, null, 2));
      
      // 等待变更检测
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // 清理
      unlinkSync(tempConfigPath);
      
      if (!changeDetected) {
        return {
          success: false,
          message: 'Config change was not detected',
        };
      }
      
      return {
        success: true,
        message: 'Hot reload working correctly',
      };
    } catch (error) {
      return {
        success: false,
        message: `Hot reload test failed: ${error}`,
      };
    }
  }
  
  /**
   * 测试遗留配置迁移
   */
  private static testLegacyMigration(): TestResult {
    try {
      const migratedConfig = migrateFromLegacyConfig(LEGACY_CONFIG_SAMPLE);
      
      // 验证基本字段迁移
      if (migratedConfig.failOnComplexity !== LEGACY_CONFIG_SAMPLE.failOnComplexity) {
        return {
          success: false,
          message: 'Basic field migration failed',
        };
      }
      
      // 验证嵌套字段迁移
      if (migratedConfig.ui.port !== LEGACY_CONFIG_SAMPLE.ui.port) {
        return {
          success: false,
          message: 'Nested field migration failed',
        };
      }
      
      // 验证规则迁移
      if (migratedConfig.rules.logical.enableMixedLogicOperatorPenalty !== LEGACY_CONFIG_SAMPLE.rules.enableMixedLogicOperatorPenalty) {
        return {
          success: false,
          message: 'Rules migration failed',
        };
      }
      
      // 验证元数据添加
      if (!migratedConfig.metadata.version || !migratedConfig.metadata.name) {
        return {
          success: false,
          message: 'Metadata not added during migration',
        };
      }
      
      return {
        success: true,
        message: 'Legacy config migration working correctly',
      };
    } catch (error) {
      return {
        success: false,
        message: `Legacy migration test failed: ${error}`,
      };
    }
  }
  
  /**
   * 测试性能
   */
  private static async testPerformance(): Promise<TestResult> {
    try {
      const iterations = 1000;
      
      // 测试配置加载性能
      const startTime = Date.now();
      
      for (let i = 0; i < iterations; i++) {
        const config = createDefaultConfig();
        validateConfig(config);
      }
      
      const endTime = Date.now();
      const avgTime = (endTime - startTime) / iterations;
      
      if (avgTime > 10) { // 如果平均时间超过10ms，可能有性能问题
        return {
          success: false,
          message: `Performance too slow: ${avgTime.toFixed(2)}ms per operation`,
        };
      }
      
      return {
        success: true,
        message: `Performance acceptable: ${avgTime.toFixed(2)}ms per operation`,
      };
    } catch (error) {
      return {
        success: false,
        message: `Performance test failed: ${error}`,
      };
    }
  }
  
  /**
   * 运行单个测试
   */
  private static async runTest(
    name: string,
    testFunc: () => TestResult | Promise<TestResult>,
    results: ValidationTestResult
  ): Promise<void> {
    try {
      console.log(`⏳ ${name}...`);
      const result = await testFunc();
      
      if (result.success) {
        console.log(`✅ ${name}: ${result.message}\n`);
        results.passed++;
      } else {
        console.log(`❌ ${name}: ${result.message}\n`);
        results.failed++;
      }
      
      results.tests.push({
        name,
        success: result.success,
        message: result.message,
      });
    } catch (error) {
      console.log(`💥 ${name}: Unexpected error - ${error}\n`);
      results.failed++;
      results.tests.push({
        name,
        success: false,
        message: `Unexpected error: ${error}`,
      });
    }
  }
  
  /**
   * 打印测试结果
   */
  private static printResults(results: ValidationTestResult): void {
    const totalTests = results.passed + results.failed;
    const duration = results.endTime - results.startTime;
    
    console.log('📊 Test Results Summary:');
    console.log(`   Total: ${totalTests}`);
    console.log(`   Passed: ${results.passed} ✅`);
    console.log(`   Failed: ${results.failed} ❌`);
    console.log(`   Duration: ${duration}ms`);
    console.log(`   Success Rate: ${((results.passed / totalTests) * 100).toFixed(1)}%`);
    
    if (results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      results.tests
        .filter(test => !test.success)
        .forEach(test => {
          console.log(`   • ${test.name}: ${test.message}`);
        });
    }
    
    console.log(results.failed === 0 ? '\n🎉 All tests passed!' : '\n⚠️  Some tests failed!');
  }
}

// =============================================================================
// 类型定义
// =============================================================================

interface TestResult {
  success: boolean;
  message: string;
}

interface ValidationTestResult {
  passed: number;
  failed: number;
  tests: Array<{
    name: string;
    success: boolean;
    message: string;
  }>;
  startTime: number;
  endTime: number;
}

// =============================================================================
// 导出便利函数
// =============================================================================

/**
 * 运行配置验证测试的便利函数
 */
export async function runConfigValidationTests(): Promise<boolean> {
  const results = await ConfigValidator.runAllTests();
  return results.failed === 0;
}

/**
 * 验证配置文件的便利函数
 */
export async function validateConfigFile(configPath: string): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}> {
  try {
    const config = await loadConfig(configPath);
    const validation = validateConfig(config);
    
    return {
      isValid: validation.isValid,
      errors: validation.errors.map((e: any) => `${e.path}: ${e.message}`),
      warnings: validation.warnings,
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [`Failed to load config: ${error}`],
      warnings: [],
    };
  }
}