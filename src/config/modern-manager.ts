/**
 * 现代化配置管理器 - 类型安全的配置系统实现
 * 
 * 特性:
 * 1. 完整的TypeScript类型支持和验证
 * 2. 智能默认值和配置合并
 * 3. 配置继承和组合模式
 * 4. 热重载机制支持
 * 5. 配置模式验证和错误报告
 */

import { cosmiconfig } from 'cosmiconfig';
import { readFileSync, watchFile, unwatchFile } from 'fs';
import { resolve, dirname } from 'path';
import type {
  CognitiveComplexityConfig,
  PartialCognitiveConfig,
  ResolvedConfig,
  ConfigValidationResult,
  ConfigValidationError,
  ConfigChangeEvent,
  ConfigChangeListener,
  ConfigSchema,
  JSXRuleConfig,
  LogicalRuleConfig,
  NestingRuleConfig,
  PerformanceConfig,
  OutputConfig,
  SmartExclusionConfig,
  UIConfig,
  PluginConfig,
  SeverityMapping,
  ProjectType,
} from './schema';
import { 
  ProjectDetector, 
  type ProjectDetectionResult, 
  type ConfigRecommendation,
  detectProjectType,
  generateProjectConfig,
  generateSmartConfig
} from './project-detector';

// =============================================================================
// 默认配置定义
// =============================================================================

/**
 * 默认的JSX规则配置
 */
const DEFAULT_JSX_RULES: JSXRuleConfig = {
  enabled: true,
  exemptions: {
    structuralNodes: true,      // 豁免JSX结构节点
    attributeExpressions: true, // 豁免属性表达式
    nullishCoalescing: true,    // 豁免空值合并
    simpleConditionals: true,   // 豁免简单条件
  },
  scoring: {
    conditionalRendering: true, // 对条件渲染计分
    eventHandlers: true,        // 对事件处理器计分
    loopRendering: true,        // 对循环渲染计分
    nestedComponents: false,    // 不对嵌套组件计分
  },
  advanced: {
    detectHookComplexity: false, // 不检测Hook复杂度
    analyzeProps: false,         // 不分析Props
    trackContextUsage: false,    // 不跟踪Context使用
  },
};

/**
 * 默认的逻辑规则配置
 */
const DEFAULT_LOGICAL_RULES: LogicalRuleConfig = {
  enableMixedLogicOperatorPenalty: false,
  logicalChainThreshold: 3,
  detectShortCircuit: false,
};

/**
 * 默认的嵌套规则配置
 */
const DEFAULT_NESTING_RULES: NestingRuleConfig = {
  maxNestingDepth: 50,
  recursionChainThreshold: 10,
  enableIterativeParser: false,
};

/**
 * 默认的性能配置
 */
const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  maxConcurrency: 4,
  cacheSize: 1000,
  streamingThreshold: 1024 * 1024, // 1MB
  fileProcessingTimeout: 30000,    // 30秒
  enablePerformanceLogging: false,
};

/**
 * 默认的输出配置
 */
const DEFAULT_OUTPUT_CONFIG: OutputConfig = {
  includeMetrics: false,
  detailLevel: 'standard',
  format: ['text'],
  reportPaths: {},
};

/**
 * 默认的严重程度映射
 */
const DEFAULT_SEVERITY_MAPPING: SeverityMapping[] = [
  { level: 'Critical', threshold: 30, color: 'red', icon: '🔴' },
  { level: 'Warning', threshold: 12, color: 'yellow', icon: '🟡' },
  { level: 'Info', threshold: 0, color: 'blue', icon: '🔵' },
];

/**
 * 项目类型特定的排除规则
 */
const PROJECT_TYPE_EXCLUSIONS: Record<ProjectType, string[]> = {
  react: [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/*.d.ts',
    'public/**',
    'static/**',
  ],
  nextjs: [
    '**/node_modules/**',
    '**/.next/**',
    '**/out/**',
    '**/dist/**',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/*.d.ts',
    'public/**',
    'static/**',
  ],
  gatsby: [
    '**/node_modules/**',
    '**/.cache/**',
    '**/public/**',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/*.d.ts',
  ],
  vue: [
    '**/node_modules/**',
    '**/dist/**',
    '**/*.test.ts',
    '**/*.test.js',
    '**/*.spec.ts',
    '**/*.spec.js',
    '**/*.d.ts',
  ],
  angular: [
    '**/node_modules/**',
    '**/dist/**',
    '**/*.test.ts',
    '**/*.spec.ts',
    '**/*.d.ts',
    '**/coverage/**',
  ],
  nodejs: [
    '**/node_modules/**',
    '**/dist/**',
    '**/lib/**',
    '**/*.test.js',
    '**/*.test.ts',
    '**/*.spec.js',
    '**/*.spec.ts',
    '**/*.d.ts',
  ],
  generic: [
    '**/node_modules/**',
    '**/dist/**',
    '**/*.test.*',
    '**/*.spec.*',
    '**/*.d.ts',
  ],
};

/**
 * 默认的智能排除配置
 */
const DEFAULT_SMART_EXCLUSION: SmartExclusionConfig = {
  enabled: true,
  projectTypeRules: PROJECT_TYPE_EXCLUSIONS,
  customRules: [],
};

/**
 * 默认的UI配置
 */
const DEFAULT_UI_CONFIG: UIConfig = {
  port: 3000,
  host: 'localhost',
  openBrowser: false,
  autoShutdown: false,
  theme: 'auto',
};

/**
 * 默认的插件配置
 */
const DEFAULT_PLUGIN_CONFIG: PluginConfig = {
  enabled: [],
  searchPaths: ['./node_modules', './plugins'],
  pluginSettings: {},
};

/**
 * 完整的默认配置
 */
const DEFAULT_CONFIG: CognitiveComplexityConfig = {
  failOnComplexity: 15,
  exclude: [
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/*.d.ts',
    '**/node_modules/**',
    '**/dist/**',
  ],
  include: [],
  severityMapping: DEFAULT_SEVERITY_MAPPING,
  rules: {
    jsx: DEFAULT_JSX_RULES,
    logical: DEFAULT_LOGICAL_RULES,
    nesting: DEFAULT_NESTING_RULES,
    custom: {},
  },
  performance: DEFAULT_PERFORMANCE_CONFIG,
  output: DEFAULT_OUTPUT_CONFIG,
  smartExclusion: DEFAULT_SMART_EXCLUSION,
  ui: DEFAULT_UI_CONFIG,
  plugins: DEFAULT_PLUGIN_CONFIG,
  metadata: {
    version: '2.0',
    name: 'default',
    description: 'Default cognitive complexity configuration',
    createdAt: new Date().toISOString(),
  },
};

// =============================================================================
// 现代化配置管理器实现
// =============================================================================

/**
 * 现代化配置管理器
 */
export class ModernConfigManager {
  private static instance: ModernConfigManager;
  private readonly configCache = new Map<string, ResolvedConfig>();
  private readonly watchers = new Map<string, { listeners: ConfigChangeListener[]; watching: boolean }>();
  private readonly schemas: ConfigSchema[] = [];
  private readonly projectDetector: ProjectDetector;
  
  private constructor() {
    this.initializeSchemas();
    this.projectDetector = new ProjectDetector();
  }
  
  /**
   * 获取配置管理器单例
   */
  public static getInstance(): ModernConfigManager {
    if (!ModernConfigManager.instance) {
      ModernConfigManager.instance = new ModernConfigManager();
    }
    return ModernConfigManager.instance;
  }
  
  /**
   * 智能加载配置 - 结合项目检测自动优化配置
   */
  public async loadSmartConfig(
    configPath?: string,
    searchFrom?: string,
    enableProjectDetection: boolean = true
  ): Promise<{
    config: ResolvedConfig;
    detection?: ProjectDetectionResult;
    recommendation?: ConfigRecommendation;
  }> {
    try {
      // 1. 首先加载用户配置
      const userConfig = await this.loadConfig(configPath, searchFrom);
      
      // 2. 如果启用项目检测，进行智能优化
      if (enableProjectDetection) {
        const targetPath = searchFrom || dirname(configPath || process.cwd());
        
        try {
          // 执行项目检测
          const detection = await this.projectDetector.detectProject(targetPath);
          
          // 生成配置推荐
          const recommendation = this.projectDetector.generateConfigRecommendation(detection);
          
          // 合并智能配置
          const smartConfig = this.mergeConfigs(
            recommendation.config,
            this.configToPartial(userConfig)
          );
          
          // 重新解析配置
          const finalConfig = this.resolveConfig(smartConfig, configPath);
          
          // 验证最终配置
          const validation = this.validateConfig(finalConfig);
          if (!validation.isValid) {
            console.warn('智能配置验证失败，使用原始配置');
            return { config: userConfig };
          }
          
          // 更新缓存
          const cacheKey = configPath || searchFrom || 'default';
          this.configCache.set(cacheKey, finalConfig);
          
          return {
            config: finalConfig,
            detection,
            recommendation,
          };
          
        } catch (detectionError) {
          console.warn('项目检测失败，使用原始配置:', detectionError);
          return { config: userConfig };
        }
      }
      
      return { config: userConfig };
      
    } catch (error) {
      console.error('智能配置加载失败:', error);
      return { config: this.createDefaultResolvedConfig() };
    }
  }
  
  /**
   * 检测项目类型
   */
  public async detectProject(projectPath?: string): Promise<ProjectDetectionResult> {
    return this.projectDetector.detectProject(projectPath);
  }
  
  /**
   * 生成项目配置推荐
   */
  public generateProjectRecommendation(detection: ProjectDetectionResult): ConfigRecommendation {
    return this.projectDetector.generateConfigRecommendation(detection);
  }
  
  /**
   * 应用智能配置推荐到现有配置
   */
  public applySmartRecommendations(
    baseConfig: ResolvedConfig,
    recommendation: ConfigRecommendation,
    options: {
      overrideUserConfig?: boolean;
      preserveUserRules?: boolean;
      logChanges?: boolean;
    } = {}
  ): ResolvedConfig {
    const {
      overrideUserConfig = false,
      preserveUserRules = true,
      logChanges = false,
    } = options;
    
    // 转换为部分配置进行合并
    const basePartial = this.configToPartial(baseConfig);
    let mergedConfig: PartialCognitiveConfig;
    
    if (overrideUserConfig) {
      // 推荐配置优先
      mergedConfig = this.mergeConfigs(basePartial, recommendation.config);
    } else {
      // 用户配置优先
      mergedConfig = this.mergeConfigs(recommendation.config, basePartial);
    }
    
    // 如果要保留用户规则，特殊处理规则部分
    if (preserveUserRules && baseConfig.rules) {
      mergedConfig.rules = this.deepMerge(recommendation.config.rules || {}, baseConfig.rules);
    }
    
    // 重新解析配置
    const finalConfig = this.resolveConfig(mergedConfig, baseConfig.source.filePath);
    
    // 记录变更（如果启用）
    if (logChanges) {
      this.logConfigChanges(baseConfig, finalConfig, recommendation);
    }
    
    return finalConfig;
  }
  public async loadConfig(
    configPath?: string,
    searchFrom?: string
  ): Promise<ResolvedConfig> {
    try {
      const cacheKey = configPath || searchFrom || 'default';
      
      // 检查缓存
      if (this.configCache.has(cacheKey)) {
        return this.configCache.get(cacheKey)!;
      }
      
      // 加载配置
      const rawConfig = await this.loadRawConfig(configPath, searchFrom);
      const resolvedConfig = this.resolveConfig(rawConfig, configPath);
      
      // 验证配置
      const validation = this.validateConfig(resolvedConfig);
      if (!validation.isValid) {
        throw new Error(`Configuration validation failed:\n${this.formatValidationErrors(validation.errors)}`);
      }
      
      // 缓存配置
      this.configCache.set(cacheKey, resolvedConfig);
      
      return resolvedConfig;
    } catch (error) {
      console.error('Failed to load configuration:', error);
      return this.createDefaultResolvedConfig();
    }
  }
  
  /**
   * 启用配置热重载
   */
  public enableHotReload(
    configPath: string,
    listener: ConfigChangeListener
  ): void {
    const normalizedPath = resolve(configPath);
    
    if (!this.watchers.has(normalizedPath)) {
      this.watchers.set(normalizedPath, { listeners: [], watching: false });
    }
    
    const watcher = this.watchers.get(normalizedPath)!;
    watcher.listeners.push(listener);
    
    if (!watcher.watching) {
      this.startWatching(normalizedPath);
      watcher.watching = true;
    }
  }
  
  /**
   * 禁用配置热重载
   */
  public disableHotReload(configPath: string, listener?: ConfigChangeListener): void {
    const normalizedPath = resolve(configPath);
    const watcher = this.watchers.get(normalizedPath);
    
    if (!watcher) return;
    
    if (listener) {
      const index = watcher.listeners.indexOf(listener);
      if (index > -1) {
        watcher.listeners.splice(index, 1);
      }
    } else {
      watcher.listeners.length = 0;
    }
    
    if (watcher.listeners.length === 0 && watcher.watching) {
      unwatchFile(normalizedPath);
      watcher.watching = false;
    }
  }
  
  /**
   * 清除配置缓存
   */
  public clearCache(configPath?: string): void {
    if (configPath) {
      this.configCache.delete(configPath);
    } else {
      this.configCache.clear();
    }
  }
  
  /**
   * 合并配置
   */
  public mergeConfigs(
    base: PartialCognitiveConfig,
    ...overrides: PartialCognitiveConfig[]
  ): PartialCognitiveConfig {
    return overrides.reduce((merged, override) => {
      return this.deepMerge(merged, override);
    }, base);
  }
  
  /**
   * 验证配置
   */
  public validateConfig(config: CognitiveComplexityConfig): ConfigValidationResult {
    const errors: ConfigValidationError[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];
    
    // 基本字段验证
    this.validateBasicFields(config, errors);
    
    // 规则配置验证
    this.validateRulesConfig(config.rules, errors);
    
    // 性能配置验证
    this.validatePerformanceConfig(config.performance, errors);
    
    // 输出配置验证
    this.validateOutputConfig(config.output, errors);
    
    // 生成警告和建议
    this.generateWarningsAndSuggestions(config, warnings, suggestions);
    
    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }
  
  // =============================================================================
  // 私有方法实现
  // =============================================================================
  
  /**
   * 加载原始配置
   */
  private async loadRawConfig(
    configPath?: string,
    searchFrom?: string
  ): Promise<PartialCognitiveConfig> {
    const explorer = cosmiconfig('cognitive-complexity', {
      searchPlaces: [
        'cognitive-complexity.config.ts',
        'cognitive-complexity.config.js',
        'cognitive-complexity.config.json',
        'cognitive.config.ts',
        'cognitive.config.js',
        'cognitive.config.json',
        '.cognitiverc',
        '.cognitiverc.json',
        '.cognitiverc.js',
        '.cognitiverc.ts',
        'package.json',
      ],
      loaders: {
        '.ts': async (filepath: string) => {
          try {
            const config = await import(filepath);
            return config.default || config;
          } catch (error) {
            console.warn(`Failed to load TypeScript config ${filepath}:`, error);
            return {};
          }
        }
      }
    });
    
    let result;
    
    if (configPath) {
      result = await explorer.load(configPath);
      if (!result) {
        throw new Error(`Configuration file not found: ${configPath}`);
      }
    } else {
      result = await explorer.search(searchFrom);
    }
    
    return result?.config || {};
  }
  
  /**
   * 解析配置（填充默认值和元数据）
   */
  private resolveConfig(
    userConfig: PartialCognitiveConfig,
    configPath?: string
  ): ResolvedConfig {
    const mergedConfig = this.deepMerge(DEFAULT_CONFIG, userConfig as Partial<CognitiveComplexityConfig>);
    
    // 添加解析时元数据
    const resolvedConfig: ResolvedConfig = {
      ...mergedConfig,
      source: {
        filePath: configPath,
        isDefault: !configPath && Object.keys(userConfig).length === 0,
        mergeHistory: ['default', ...(configPath ? [configPath] : [])],
      },
      runtime: {
        resolvedAt: new Date(),
        nodeVersion: process.version,
        toolVersion: this.getToolVersion(),
      },
      metadata: {
        ...mergedConfig.metadata,
        updatedAt: new Date().toISOString(),
      },
    };
    
    return resolvedConfig;
  }
  
  /**
   * 创建默认解析配置
   */
  private createDefaultResolvedConfig(): ResolvedConfig {
    return this.resolveConfig({});
  }
  
  /**
   * 深度合并对象
   */
  private deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
    const result = { ...target };
    
    for (const key in source) {
      const sourceValue = source[key];
      const targetValue = result[key];
      
      if (sourceValue === undefined) {
        continue;
      }
      
      if (Array.isArray(sourceValue)) {
        result[key] = [...sourceValue] as any;
      } else if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
        if (targetValue && typeof targetValue === 'object' && !Array.isArray(targetValue)) {
          result[key] = this.deepMerge(targetValue, sourceValue);
        } else {
          result[key] = { ...sourceValue } as any;
        }
      } else {
        result[key] = sourceValue as any;
      }
    }
    
    return result;
  }
  
  /**
   * 开始监听配置文件变化
   */
  private startWatching(configPath: string): void {
    watchFile(configPath, async () => {
      try {
        const oldConfig = this.configCache.get(configPath);
        this.configCache.delete(configPath); // 清除缓存
        
        const newRawConfig = await this.loadRawConfig(configPath);
        const newConfig = this.resolveConfig(newRawConfig, configPath);
        
        const event: ConfigChangeEvent = {
          type: 'modified',
          filePath: configPath,
          timestamp: new Date(),
          oldConfig,
          newConfig,
        };
        
        const watcher = this.watchers.get(configPath);
        if (watcher) {
          for (const listener of watcher.listeners) {
            try {
              await listener(event);
            } catch (error) {
              console.error('Config change listener error:', error);
            }
          }
        }
      } catch (error) {
        console.error('Error handling config file change:', error);
      }
    });
  }
  
  /**
   * 初始化配置模式
   */
  private initializeSchemas(): void {
    // 这里可以添加配置模式定义，用于更详细的验证
    // 暂时保持简单，基本验证在validateConfig中实现
  }
  
  /**
   * 验证基本字段
   */
  private validateBasicFields(config: CognitiveComplexityConfig, errors: ConfigValidationError[]): void {
    if (typeof config.failOnComplexity !== 'number' || config.failOnComplexity < 0) {
      errors.push({
        path: 'failOnComplexity',
        message: 'Must be a non-negative number',
        value: config.failOnComplexity,
        expected: 'number >= 0',
      });
    }
    
    if (!Array.isArray(config.exclude)) {
      errors.push({
        path: 'exclude',
        message: 'Must be an array of strings',
        value: config.exclude,
        expected: 'string[]',
      });
    }
    
    if (!Array.isArray(config.include)) {
      errors.push({
        path: 'include',
        message: 'Must be an array of strings',
        value: config.include,
        expected: 'string[]',
      });
    }
  }
  
  /**
   * 验证规则配置
   */
  private validateRulesConfig(rules: CognitiveComplexityConfig['rules'], errors: ConfigValidationError[]): void {
    if (!rules || typeof rules !== 'object') {
      errors.push({
        path: 'rules',
        message: 'Must be an object',
        value: rules,
        expected: 'object',
      });
      return;
    }
    
    // 验证JSX规则
    if (rules.jsx && typeof rules.jsx.enabled !== 'boolean') {
      errors.push({
        path: 'rules.jsx.enabled',
        message: 'Must be a boolean',
        value: rules.jsx.enabled,
        expected: 'boolean',
      });
    }
    
    // 验证逻辑规则
    if (rules.logical && typeof rules.logical.enableMixedLogicOperatorPenalty !== 'boolean') {
      errors.push({
        path: 'rules.logical.enableMixedLogicOperatorPenalty',
        message: 'Must be a boolean',
        value: rules.logical.enableMixedLogicOperatorPenalty,
        expected: 'boolean',
      });
    }
  }
  
  /**
   * 验证性能配置
   */
  private validatePerformanceConfig(performance: PerformanceConfig, errors: ConfigValidationError[]): void {
    if (typeof performance.maxConcurrency !== 'number' || performance.maxConcurrency < 1) {
      errors.push({
        path: 'performance.maxConcurrency',
        message: 'Must be a positive integer',
        value: performance.maxConcurrency,
        expected: 'number >= 1',
      });
    }
    
    if (typeof performance.fileProcessingTimeout !== 'number' || performance.fileProcessingTimeout <= 0) {
      errors.push({
        path: 'performance.fileProcessingTimeout',
        message: 'Must be a positive number',
        value: performance.fileProcessingTimeout,
        expected: 'number > 0',
      });
    }
  }
  
  /**
   * 验证输出配置
   */
  private validateOutputConfig(output: OutputConfig, errors: ConfigValidationError[]): void {
    if (!Array.isArray(output.format)) {
      errors.push({
        path: 'output.format',
        message: 'Must be an array of output formats',
        value: output.format,
        expected: "('text' | 'json' | 'html' | 'xml')[]",
      });
    }
  }
  
  /**
   * 生成警告和建议
   */
  private generateWarningsAndSuggestions(
    config: CognitiveComplexityConfig,
    warnings: string[],
    suggestions: string[]
  ): void {
    // 性能建议
    if (config.performance.maxConcurrency > 8) {
      warnings.push('High concurrency setting may cause resource contention');
      suggestions.push('Consider reducing maxConcurrency to 4-6 for optimal performance');
    }
    
    // JSX配置建议
    if (config.rules.jsx.enabled && !config.rules.jsx.exemptions.structuralNodes) {
      suggestions.push('Consider enabling JSX structural node exemptions for better React analysis');
    }
    
    // 排除规则建议
    if (config.exclude.length === 0) {
      warnings.push('No exclusion patterns defined, may analyze unwanted files');
      suggestions.push('Add exclusion patterns for test files and dependencies');
    }
  }
  
  /**
   * 格式化验证错误
   */
  private formatValidationErrors(errors: ConfigValidationError[]): string {
    return errors.map(error => 
      `  ${error.path}: ${error.message} (received: ${JSON.stringify(error.value)}, expected: ${error.expected})`
    ).join('\n');
  }
  
  /**
   * 将ResolvedConfig转换为PartialCognitiveConfig
   */
  private configToPartial(config: ResolvedConfig): PartialCognitiveConfig {
    const { source, runtime, ...configData } = config;
    return configData;
  }
  
  /**
   * 记录配置变更
   */
  private logConfigChanges(
    oldConfig: ResolvedConfig,
    newConfig: ResolvedConfig,
    recommendation: ConfigRecommendation
  ): void {
    console.log('🔧 智能配置应用完成:');
    
    // 记录项目类型检测结果
    if (newConfig.smartExclusion?.detectedProjectType) {
      console.log(`  📋 检测到项目类型: ${newConfig.smartExclusion.detectedProjectType}`);
    }
    
    // 记录主要变更
    const changes: string[] = [];
    
    if (JSON.stringify(oldConfig.exclude) !== JSON.stringify(newConfig.exclude)) {
      changes.push(`排除规则: ${oldConfig.exclude.length} -> ${newConfig.exclude.length} 项`);
    }
    
    if (oldConfig.rules.jsx?.enabled !== newConfig.rules.jsx?.enabled) {
      changes.push(`JSX分析: ${oldConfig.rules.jsx?.enabled ? '启用' : '禁用'} -> ${newConfig.rules.jsx?.enabled ? '启用' : '禁用'}`);
    }
    
    if (oldConfig.performance.maxConcurrency !== newConfig.performance.maxConcurrency) {
      changes.push(`并发数: ${oldConfig.performance.maxConcurrency} -> ${newConfig.performance.maxConcurrency}`);
    }
    
    if (changes.length > 0) {
      console.log('  🔄 主要变更:');
      changes.forEach(change => console.log(`    • ${change}`));
    }
    
    // 记录推荐原因
    if (recommendation.reasons.length > 0) {
      console.log('  💡 推荐原因:');
      recommendation.reasons.forEach(reason => console.log(`    • ${reason}`));
    }
    
    // 记录优化建议
    if (recommendation.suggestions.length > 0) {
      console.log('  📝 优化建议:');
      recommendation.suggestions.forEach(suggestion => console.log(`    • ${suggestion}`));
    }
  }
  private getToolVersion(): string {
    try {
      const packageJson = JSON.parse(readFileSync(resolve(__dirname, '../../package.json'), 'utf-8'));
      return packageJson.version || '0.0.0';
    } catch {
      return '0.0.0';
    }
  }
}

// =============================================================================
// 导出便利函数
// =============================================================================

/**
 * 加载配置的便利函数
 */
export async function loadConfig(configPath?: string, searchFrom?: string): Promise<ResolvedConfig> {
  return ModernConfigManager.getInstance().loadConfig(configPath, searchFrom);
}

/**
 * 智能加载配置的便利函数
 */
export async function loadSmartConfig(
  configPath?: string,
  searchFrom?: string,
  enableProjectDetection: boolean = true
): Promise<{
  config: ResolvedConfig;
  detection?: ProjectDetectionResult;
  recommendation?: ConfigRecommendation;
}> {
  return ModernConfigManager.getInstance().loadSmartConfig(configPath, searchFrom, enableProjectDetection);
}

/**
 * 启用热重载的便利函数
 */
export function enableConfigHotReload(configPath: string, listener: ConfigChangeListener): void {
  ModernConfigManager.getInstance().enableHotReload(configPath, listener);
}

/**
 * 合并配置的便利函数
 */
export function mergeConfigs(
  base: PartialCognitiveConfig,
  ...overrides: PartialCognitiveConfig[]
): PartialCognitiveConfig {
  return ModernConfigManager.getInstance().mergeConfigs(base, ...overrides);
}

/**
 * 创建默认配置
 */
export function createDefaultConfig(): CognitiveComplexityConfig {
  return JSON.parse(JSON.stringify(DEFAULT_CONFIG));
}