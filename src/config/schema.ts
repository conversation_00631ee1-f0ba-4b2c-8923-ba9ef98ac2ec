/**
 * 现代化配置架构 - 类型安全的配置系统
 * 
 * 设计原则:
 * 1. 完整的TypeScript类型支持
 * 2. 配置验证和智能默认值
 * 3. 配置继承和组合模式
 * 4. 热重载机制支持
 */

// =============================================================================
// 核心配置接口
// =============================================================================

/**
 * JSX规则配置
 */
export interface JSXRuleConfig {
  /** 是否启用JSX分析规则 */
  enabled: boolean;
  
  /** 豁免配置 */
  exemptions: {
    /** JSX结构节点豁免 - 豁免纯UI结构复杂度 */
    structuralNodes: boolean;
    /** 属性表达式豁免 - 豁免简单属性表达式 */
    attributeExpressions: boolean;
    /** 空值合并豁免 - 豁免 ?. 和 ?? 操作符 */
    nullishCoalescing: boolean;
    /** 简单条件豁免 - 豁免简单的三元表达式 */
    simpleConditionals: boolean;
  };
  
  /** 计分配置 */
  scoring: {
    /** 条件渲染计分 - 是否对条件渲染计分 */
    conditionalRendering: boolean;
    /** 事件处理器计分 - 是否对事件处理器计分 */
    eventHandlers: boolean;
    /** 循环渲染计分 - 是否对 map/filter 等计分 */
    loopRendering: boolean;
    /** 嵌套组件计分 - 是否对深层嵌套组件计分 */
    nestedComponents: boolean;
  };
  
  /** 高级配置 */
  advanced: {
    /** Hook复杂度检测 - 分析useEffect等Hook的复杂度 */
    detectHookComplexity: boolean;
    /** Props分析 - 分析props传递的复杂度 */
    analyzeProps: boolean;
    /** Context使用跟踪 - 跟踪Context的使用模式 */
    trackContextUsage: boolean;
  };
}

/**
 * 逻辑运算符规则配置
 */
export interface LogicalRuleConfig {
  /** 是否启用混合逻辑运算符惩罚 */
  enableMixedLogicOperatorPenalty: boolean;
  /** 逻辑链复杂度阈值 */
  logicalChainThreshold: number;
  /** 短路求值优化检测 */
  detectShortCircuit: boolean;
}

/**
 * 嵌套规则配置
 */
export interface NestingRuleConfig {
  /** 最大嵌套深度 */
  maxNestingDepth: number;
  /** 递归链阈值 */
  recursionChainThreshold: number;
  /** 是否启用迭代解析器优化 */
  enableIterativeParser: boolean;
}

/**
 * 性能配置
 */
export interface PerformanceConfig {
  /** 最大并发数 */
  maxConcurrency: number;
  /** 缓存大小限制 */
  cacheSize: number;
  /** 流式处理阈值(字节) */
  streamingThreshold: number;
  /** 单个文件处理超时时间(毫秒) */
  fileProcessingTimeout: number;
  /** 是否启用性能日志 */
  enablePerformanceLogging: boolean;
}

/**
 * 输出格式类型
 */
export type OutputFormat = 'text' | 'json' | 'html' | 'xml';

/**
 * 输出详细程度
 */
export type DetailLevel = 'minimal' | 'standard' | 'verbose';

/**
 * 输出配置
 */
export interface OutputConfig {
  /** 是否包含性能指标 */
  includeMetrics: boolean;
  /** 详细程度 */
  detailLevel: DetailLevel;
  /** 输出格式 */
  format: OutputFormat[];
  /** 报告输出路径 */
  reportPaths: {
    json?: string;
    html?: string;
    xml?: string;
  };
}

/**
 * 严重程度级别
 */
export type SeverityLevel = 'Critical' | 'Warning' | 'Info';

/**
 * 严重程度映射
 */
export interface SeverityMapping {
  level: SeverityLevel;
  threshold: number;
  /** 颜色配置(可选) */
  color?: string;
  /** 图标配置(可选) */
  icon?: string;
}

/**
 * 项目类型
 */
export type ProjectType = 'react' | 'nextjs' | 'gatsby' | 'vue' | 'angular' | 'nodejs' | 'generic';

/**
 * 智能排除配置
 */
export interface SmartExclusionConfig {
  /** 是否启用智能排除 */
  enabled: boolean;
  /** 自动检测的项目类型 */
  detectedProjectType?: ProjectType;
  /** 项目类型特定的排除规则 */
  projectTypeRules: Record<ProjectType, string[]>;
  /** 自定义排除规则 */
  customRules: string[];
}

/**
 * Web UI配置
 */
export interface UIConfig {
  /** Web UI端口 */
  port: number;
  /** Web UI主机 */
  host: string;
  /** 是否自动打开浏览器 */
  openBrowser: boolean;
  /** 分析完成后是否自动关闭服务器 */
  autoShutdown: boolean;
  /** UI主题 */
  theme: 'light' | 'dark' | 'auto';
}

/**
 * 插件配置
 */
export interface PluginConfig {
  /** 启用的插件列表 */
  enabled: string[];
  /** 插件搜索路径 */
  searchPaths: string[];
  /** 插件特定配置 */
  pluginSettings: Record<string, unknown>;
}

// =============================================================================
// 主配置接口
// =============================================================================

/**
 * 完整的配置接口 - 这是用户可以配置的所有选项
 */
export interface CognitiveComplexityConfig {
  /** 复杂度失败阈值 */
  failOnComplexity: number;
  
  /** 基本排除模式 */
  exclude: string[];
  
  /** 强制包含路径(覆盖排除规则) */
  include: string[];
  
  /** 严重程度映射配置 */
  severityMapping: SeverityMapping[];
  
  /** 规则配置 */
  rules: {
    jsx: JSXRuleConfig;
    logical: LogicalRuleConfig;
    nesting: NestingRuleConfig;
    /** 自定义规则配置 */
    custom: Record<string, unknown>;
  };
  
  /** 性能配置 */
  performance: PerformanceConfig;
  
  /** 输出配置 */
  output: OutputConfig;
  
  /** 智能排除配置 */
  smartExclusion: SmartExclusionConfig;
  
  /** Web UI配置 */
  ui: UIConfig;
  
  /** 插件配置 */
  plugins: PluginConfig;
  
  /** 配置元数据 */
  metadata: {
    /** 配置版本 */
    version: string;
    /** 配置名称 */
    name?: string;
    /** 配置描述 */
    description?: string;
    /** 配置创建时间 */
    createdAt?: string;
    /** 最后修改时间 */
    updatedAt?: string;
  };
}

/**
 * 部分配置接口 - 用户实际配置时可以只配置部分选项
 */
export type PartialCognitiveConfig = {
  [K in keyof CognitiveComplexityConfig]?: K extends 'rules'
    ? {
        [R in keyof CognitiveComplexityConfig['rules']]?: R extends 'jsx' | 'logical' | 'nesting'
          ? Partial<CognitiveComplexityConfig['rules'][R]>
          : CognitiveComplexityConfig['rules'][R];
      }
    : K extends 'performance' | 'output' | 'smartExclusion' | 'ui' | 'plugins' | 'metadata'
    ? Partial<CognitiveComplexityConfig[K]>
    : CognitiveComplexityConfig[K];
};

/**
 * 解析后的配置 - 经过验证和默认值填充的最终配置
 */
export interface ResolvedConfig extends CognitiveComplexityConfig {
  /** 配置来源信息 */
  readonly source: {
    /** 配置文件路径 */
    filePath?: string;
    /** 是否使用默认配置 */
    isDefault: boolean;
    /** 配置合并历史 */
    mergeHistory: string[];
  };
  
  /** 运行时信息 */
  readonly runtime: {
    /** 配置解析时间 */
    resolvedAt: Date;
    /** Node.js版本 */
    nodeVersion: string;
    /** 工具版本 */
    toolVersion: string;
  };
}

// =============================================================================
// 配置验证相关类型
// =============================================================================

/**
 * 配置验证错误
 */
export interface ConfigValidationError {
  /** 错误路径 */
  path: string;
  /** 错误消息 */
  message: string;
  /** 错误值 */
  value: unknown;
  /** 期望的类型或值 */
  expected: string;
}

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
  /** 是否验证通过 */
  isValid: boolean;
  /** 验证错误列表 */
  errors: ConfigValidationError[];
  /** 警告列表 */
  warnings: string[];
  /** 建议列表 */
  suggestions: string[];
}

/**
 * 配置模式定义
 */
export interface ConfigSchema {
  /** 属性名 */
  property: keyof CognitiveComplexityConfig;
  /** 是否必需 */
  required: boolean;
  /** 类型验证函数 */
  validator: (value: unknown) => boolean;
  /** 默认值 */
  defaultValue: unknown;
  /** 描述 */
  description: string;
  /** 示例 */
  examples?: unknown[];
}

// =============================================================================
// 热重载相关类型
// =============================================================================

/**
 * 配置变更事件类型
 */
export type ConfigChangeEventType = 'created' | 'modified' | 'deleted' | 'renamed';

/**
 * 配置变更事件
 */
export interface ConfigChangeEvent {
  /** 事件类型 */
  type: ConfigChangeEventType;
  /** 配置文件路径 */
  filePath: string;
  /** 变更时间 */
  timestamp: Date;
  /** 旧配置(仅在修改时提供) */
  oldConfig?: ResolvedConfig;
  /** 新配置 */
  newConfig: ResolvedConfig;
}

/**
 * 配置监听器函数
 */
export type ConfigChangeListener = (event: ConfigChangeEvent) => void | Promise<void>;

// =============================================================================
// 导出所有类型
// =============================================================================

export type {
  // 向后兼容的旧类型别名
  CognitiveComplexityConfig as CognitiveConfig,
};