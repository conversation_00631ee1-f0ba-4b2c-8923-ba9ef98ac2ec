/**
 * 智能项目检测器 - 自动检测项目类型并应用最佳实践配置
 * 
 * 特性:
 * 1. 自动检测React、Next.js、Gatsby、Vue、Angular等项目类型
 * 2. 根据项目类型应用最佳实践配置
 * 3. 支持monorepo和多项目配置
 * 4. 提供配置推荐和优化建议
 * 
 * <AUTHOR> Code
 * @version 2.0
 */

import { existsSync, readFileSync, statSync } from 'fs';
import { join, resolve } from 'path';
import { glob } from 'glob';
import type { 
  ProjectType, 
  PartialCognitiveConfig,
  JSXRuleConfig 
} from './schema';

// =============================================================================
// 项目检测接口定义
// =============================================================================

/**
 * 项目检测结果
 */
export interface ProjectDetectionResult {
  /** 检测到的项目类型 */
  type: ProjectType;
  /** 检测依据的配置文件 */
  configFiles: string[];
  /** 推荐的排除模式 */
  excludePatterns: string[];
  /** 检测置信度 (0-1) */
  confidence: number;
  /** 检测到的框架版本 */
  frameworkVersion?: string;
  /** 是否为monorepo项目 */
  isMonorepo: boolean;
  /** 子项目信息(仅在monorepo中) */
  subProjects?: ProjectDetectionResult[];
  /** 项目根路径 */
  rootPath: string;
  /** 检测日志信息 */
  detectionLog: string[];
}

/**
 * 项目特征配置
 */
interface ProjectSignature {
  /** 项目类型 */
  type: ProjectType;
  /** 必需的配置文件匹配模式 */
  requiredFiles: string[];
  /** 可选的配置文件匹配模式 */
  optionalFiles: string[];
  /** package.json依赖匹配模式 */
  dependencies: string[];
  /** 目录结构匹配模式 */
  directories: string[];
  /** 检测优先级 (数字越大优先级越高) */
  priority: number;
  /** 最小置信度要求 */
  minConfidence: number;
}

/**
 * 配置推荐结果
 */
export interface ConfigRecommendation {
  /** 推荐的配置 */
  config: PartialCognitiveConfig;
  /** 推荐原因 */
  reasons: string[];
  /** 优化建议 */
  suggestions: string[];
  /** 配置应用优先级 */
  priority: 'high' | 'medium' | 'low';
}

// =============================================================================
// 项目特征定义
// =============================================================================

/**
 * 支持的项目类型特征定义
 */
const PROJECT_SIGNATURES: ProjectSignature[] = [
  // Next.js 项目 (最高优先级，因为它是React的超集)
  {
    type: 'nextjs',
    requiredFiles: ['next.config.js', 'next.config.ts', 'next.config.mjs'],
    optionalFiles: ['pages/**/*.tsx', 'pages/**/*.ts', 'app/**/*.tsx', 'app/**/*.ts'],
    dependencies: ['next'], // 简化依赖检查，只检查核心包
    directories: ['pages', 'app', 'public'],
    priority: 100,
    minConfidence: 0.7, // 稍微提高阈值
  },
  
  // Gatsby 项目
  {
    type: 'gatsby',
    requiredFiles: ['gatsby-config.js', 'gatsby-config.ts', 'gatsby-node.js'],
    optionalFiles: ['src/pages/**/*.tsx', 'src/templates/**/*.tsx'],
    dependencies: ['gatsby', 'gatsby-cli'],
    directories: ['src/pages', 'src/templates', 'static', '.cache'],
    priority: 95,
    minConfidence: 0.8,
  },
  
  // React 项目 (通用)
  {
    type: 'react',
    requiredFiles: [],
    optionalFiles: ['src/**/*.tsx', 'src/**/*.jsx', 'public/index.html'],
    dependencies: ['react', 'react-dom', '@types/react'],
    directories: ['src', 'public', 'build'],
    priority: 85, // 提高优先级，高于Node.js
    minConfidence: 0.6, // 提高阈值，确保有足够的React特征
  },
  
  // Vue 项目
  {
    type: 'vue',
    requiredFiles: [], // 移除必需文件要求，因为现代Vue项目可能没有配置文件
    optionalFiles: ['src/**/*.vue', 'src/main.ts', 'src/main.js', 'vue.config.js', 'vue.config.ts'],
    dependencies: ['vue', '@vue/cli-service', 'vite'],
    directories: ['src', 'dist'],
    priority: 82, // 比Node.js高，但比React低
    minConfidence: 0.5, // 稍微提高阈值，确保有足够的Vue特征
  },
  
  // Angular 项目
  {
    type: 'angular',
    requiredFiles: ['angular.json', 'ng.json'],
    optionalFiles: ['src/app/**/*.ts', 'src/main.ts'],
    dependencies: ['@angular/core', '@angular/cli'],
    directories: ['src/app', 'e2e', 'dist'],
    priority: 90,
    minConfidence: 0.8,
  },
  
  // Node.js 项目 - 只在没有其他框架特征时作为后备
  {
    type: 'nodejs',
    requiredFiles: ['package.json'],
    optionalFiles: ['src/**/*.ts', 'lib/**/*.js', 'index.js', 'index.ts'],
    dependencies: ['@types/node', 'typescript', 'commander', 'express', 'fastify', 'koa'],
    directories: ['src', 'lib', 'dist', 'build'],
    priority: 70,
    minConfidence: 0.2, // 更低的阈值，作为最后的后备选择
  },
  
  // 通用项目 (兜底)
  {
    type: 'generic',
    requiredFiles: [],
    optionalFiles: ['**/*.ts', '**/*.js'],
    dependencies: [],
    directories: [],
    priority: 10,
    minConfidence: 0.1,
  },
];

/**
 * 项目类型特定的排除规则
 */
const PROJECT_EXCLUSION_RULES: Record<ProjectType, string[]> = {
  react: [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/*.d.ts',
    '**/coverage/**',
    'public/**',
    'static/**',
  ],
  nextjs: [
    '**/node_modules/**',
    '**/.next/**',
    '**/out/**',
    '**/dist/**',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/*.d.ts',
    '**/coverage/**',
    'public/**',
    'static/**',
    '.next/**',
  ],
  gatsby: [
    '**/node_modules/**',
    '**/.cache/**',
    '**/public/**',
    '**/*.test.ts',
    '**/*.test.tsx',
    '**/*.spec.ts',
    '**/*.spec.tsx',
    '**/*.d.ts',
    '**/coverage/**',
    'static/**',
  ],
  vue: [
    '**/node_modules/**',
    '**/dist/**',
    '**/*.test.ts',
    '**/*.test.js',
    '**/*.spec.ts',
    '**/*.spec.js',
    '**/*.d.ts',
    '**/coverage/**',
  ],
  angular: [
    '**/node_modules/**',
    '**/dist/**',
    '**/*.test.ts',
    '**/*.spec.ts',
    '**/*.d.ts',
    '**/coverage/**',
    '**/e2e/**',
  ],
  nodejs: [
    '**/node_modules/**',
    '**/dist/**',
    '**/lib/**',
    '**/*.test.js',
    '**/*.test.ts',
    '**/*.spec.js',
    '**/*.spec.ts',
    '**/*.d.ts',
    '**/coverage/**',
  ],
  generic: [
    '**/node_modules/**',
    '**/dist/**',
    '**/*.test.*',
    '**/*.spec.*',
    '**/*.d.ts',
    '**/coverage/**',
  ],
};

/**
 * 项目类型特定的JSX规则推荐
 */
const PROJECT_JSX_CONFIG: Record<ProjectType, Partial<JSXRuleConfig>> = {
  react: {
    enabled: true,
    exemptions: {
      structuralNodes: true,
      attributeExpressions: true,
      nullishCoalescing: true,
      simpleConditionals: true,
    },
    scoring: {
      conditionalRendering: true,
      eventHandlers: true,
      loopRendering: true,
      nestedComponents: false,
    },
    advanced: {
      detectHookComplexity: true,
      analyzeProps: false,
      trackContextUsage: false,
    },
  },
  nextjs: {
    enabled: true,
    exemptions: {
      structuralNodes: true,
      attributeExpressions: true,
      nullishCoalescing: true,
      simpleConditionals: true,
    },
    scoring: {
      conditionalRendering: true,
      eventHandlers: true,
      loopRendering: true,
      nestedComponents: true, // Next.js常有深层嵌套
    },
    advanced: {
      detectHookComplexity: true,
      analyzeProps: true, // Next.js props传递复杂
      trackContextUsage: true,
    },
  },
  gatsby: {
    enabled: true,
    exemptions: {
      structuralNodes: true,
      attributeExpressions: true,
      nullishCoalescing: true,
      simpleConditionals: true,
    },
    scoring: {
      conditionalRendering: true,
      eventHandlers: false, // Gatsby更多是静态渲染
      loopRendering: true,
      nestedComponents: false,
    },
    advanced: {
      detectHookComplexity: true,
      analyzeProps: true, // GraphQL props
      trackContextUsage: false,
    },
  },
  vue: {
    enabled: false, // Vue使用不同的模板语法
  },
  angular: {
    enabled: false, // Angular使用TypeScript装饰器
  },
  nodejs: {
    enabled: false, // 后端Node.js项目通常不用JSX
  },
  generic: {
    enabled: false, // 默认不启用JSX分析
  },
};

// =============================================================================
// 智能项目检测器实现
// =============================================================================

/**
 * 智能项目检测器
 */
export class ProjectDetector {
  private readonly projectPath: string;
  private readonly detectionCache = new Map<string, ProjectDetectionResult>();
  
  constructor(projectPath: string = process.cwd()) {
    this.projectPath = resolve(projectPath);
  }
  
  /**
   * 检测项目类型
   */
  public async detectProject(path?: string): Promise<ProjectDetectionResult> {
    const targetPath = path ? resolve(path) : this.projectPath;
    
    // 检查缓存
    const cacheKey = targetPath;
    if (this.detectionCache.has(cacheKey)) {
      return this.detectionCache.get(cacheKey)!;
    }
    
    const detectionLog: string[] = [];
    detectionLog.push(`开始检测项目类型: ${targetPath}`);
    
    // 检查路径是否存在
    if (!existsSync(targetPath)) {
      throw new Error(`项目路径不存在: ${targetPath}`);
    }
    
    // 检查是否为目录
    if (!statSync(targetPath).isDirectory()) {
      throw new Error(`路径不是目录: ${targetPath}`);
    }
    
    // 执行项目类型检测
    const candidates = await this.scoreProjectTypes(targetPath, detectionLog);
    
    // 选择最佳匹配
    const bestMatch = this.selectBestMatch(candidates, detectionLog);
    
    // 检测是否为monorepo
    const isMonorepo = await this.detectMonorepo(targetPath, detectionLog);
    
    // 构建检测结果
    const result: ProjectDetectionResult = {
      type: bestMatch.type,
      configFiles: bestMatch.configFiles,
      excludePatterns: PROJECT_EXCLUSION_RULES[bestMatch.type],
      confidence: bestMatch.confidence,
      frameworkVersion: bestMatch.frameworkVersion,
      isMonorepo,
      subProjects: isMonorepo ? await this.detectSubProjects(targetPath, detectionLog) : undefined,
      rootPath: targetPath,
      detectionLog,
    };
    
    detectionLog.push(`检测完成: ${result.type} (置信度: ${result.confidence.toFixed(2)})`);
    
    // 缓存结果
    this.detectionCache.set(cacheKey, result);
    
    return result;
  }
  
  /**
   * 生成配置推荐
   */
  public generateConfigRecommendation(detection: ProjectDetectionResult): ConfigRecommendation {
    const reasons: string[] = [];
    const suggestions: string[] = [];
    
    reasons.push(`检测到项目类型: ${detection.type}`);
    
    // 基础配置推荐
    const config: PartialCognitiveConfig = {
      exclude: detection.excludePatterns,
      smartExclusion: {
        enabled: true,
        detectedProjectType: detection.type,
        projectTypeRules: {
          [detection.type]: detection.excludePatterns,
        } as any,
        customRules: [],
      },
    };
    
    // JSX规则推荐
    const jsxConfig = PROJECT_JSX_CONFIG[detection.type];
    if (jsxConfig) {
      config.rules = {
        jsx: jsxConfig,
      };
      
      if (jsxConfig.enabled) {
        reasons.push(`启用JSX分析规则(适用于${detection.type}项目)`);
        suggestions.push('JSX规则已针对项目类型进行优化');
      } else {
        reasons.push(`禁用JSX分析规则(${detection.type}项目不需要)`);
      }
    }
    
    // 性能优化建议
    if (detection.isMonorepo) {
      config.performance = {
        maxConcurrency: 6, // monorepo通常文件较多
        cacheSize: 2000,
      };
      reasons.push('检测到monorepo，增加并发数和缓存大小');
      suggestions.push('建议为每个子项目单独配置复杂度阈值');
    }
    
    // 项目类型特定建议
    this.addProjectSpecificRecommendations(detection.type, suggestions);
    
    return {
      config,
      reasons,
      suggestions,
      priority: detection.confidence > 0.8 ? 'high' : detection.confidence > 0.6 ? 'medium' : 'low',
    };
  }
  
  /**
   * 清除检测缓存
   */
  public clearCache(): void {
    this.detectionCache.clear();
  }
  
  // =============================================================================
  // 私有方法实现
  // =============================================================================
  
  /**
   * 对所有项目类型进行评分
   */
  private async scoreProjectTypes(
    projectPath: string, 
    detectionLog: string[]
  ): Promise<Array<{
    type: ProjectType;
    confidence: number;
    configFiles: string[];
    frameworkVersion?: string;
  }>> {
    const results = [];
    
    for (const signature of PROJECT_SIGNATURES) {
      const score = await this.scoreProjectType(projectPath, signature, detectionLog);
      
      if (score.confidence >= signature.minConfidence) {
        results.push({
          type: signature.type,
          confidence: score.confidence,
          configFiles: score.foundFiles,
          frameworkVersion: score.version,
        });
        
        detectionLog.push(
          `${signature.type}: ${score.confidence.toFixed(2)} (文件: ${score.foundFiles.length})`
        );
      }
    }
    
    // 按置信度和优先级排序
    return results.sort((a, b) => {
      const priorityA = PROJECT_SIGNATURES.find(s => s.type === a.type)?.priority || 0;
      const priorityB = PROJECT_SIGNATURES.find(s => s.type === b.type)?.priority || 0;
      
      // 首先按置信度排序，然后按优先级排序
      const confidenceDiff = b.confidence - a.confidence;
      if (Math.abs(confidenceDiff) > 0.1) {
        return confidenceDiff;
      }
      
      return priorityB - priorityA;
    });
  }
  
  /**
   * 为特定项目类型评分
   */
  private async scoreProjectType(
    projectPath: string,
    signature: ProjectSignature,
    detectionLog: string[]
  ): Promise<{
    confidence: number;
    foundFiles: string[];
    version?: string;
  }> {
    const foundFiles: string[] = [];
    let version: string | undefined;
    
    // 检查是否有必需文件
    const hasRequiredFiles = signature.requiredFiles.length > 0;
    
    // 1. 检查必需文件 (权重: 40%)
    const requiredWeight = 0.4;
    let requiredFileScore = 0;
    if (hasRequiredFiles) {
      let requiredFound = false;
      for (const pattern of signature.requiredFiles) {
        const files = await this.findFiles(projectPath, pattern);
        if (files.length > 0) {
          requiredFileScore = requiredWeight; // 找到任何一个必需文件就给满分
          foundFiles.push(...files);
          requiredFound = true;
          break; // 只要找到一个必需文件就够了
        }
      }
      
      // 如果没有找到任何必需文件，这个项目类型评分为0
      if (!requiredFound) {
        return {
          confidence: 0,
          foundFiles: [],
          version,
        };
      }
    } else if (signature.type === 'nodejs') {
      // 对于Node.js项目，检查package.json是否存在且可解析
      const packageJsonPath = join(projectPath, 'package.json');
      if (existsSync(packageJsonPath)) {
        try {
          JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
          requiredFileScore = requiredWeight; // package.json存在且可解析
          foundFiles.push(packageJsonPath);
        } catch (error) {
          // package.json存在但无法解析，Node.js项目失败
          return {
            confidence: 0,
            foundFiles: [],
            version,
          };
        }
      } else {
        // package.json不存在，Node.js项目失败
        return {
          confidence: 0,
          foundFiles: [],
          version,
        };
      }
    }
    
    // 2. 检查可选文件 (权重: 20%，如果没有必需文件则增加到40%)
    const optionalWeight = hasRequiredFiles ? 0.2 : 0.4;
    let optionalFileScore = 0;
    if (signature.optionalFiles.length > 0) {
      let optionalFound = 0;
      for (const pattern of signature.optionalFiles) {
        const files = await this.findFiles(projectPath, pattern);
        if (files.length > 0) {
          optionalFound++;
          foundFiles.push(...files.slice(0, 3)); // 最多记录3个文件
        }
      }
      
      optionalFileScore = (optionalFound / signature.optionalFiles.length) * optionalWeight;
    }
    
    // 3. 检查package.json依赖 (权重: 30%，如果没有必需文件则增加到40%) 
    const dependencyWeight = hasRequiredFiles ? 0.3 : 0.4;
    let dependencyScore = 0;
    if (signature.dependencies.length > 0) {
      const packageJsonPath = join(projectPath, 'package.json');
      if (existsSync(packageJsonPath)) {
        try {
          const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
          const allDeps = {
            ...packageJson.dependencies,
            ...packageJson.devDependencies,
            ...packageJson.peerDependencies,
          };
          
          let depsFound = 0;
          let frameworkDepFound = false;
          
          for (const dep of signature.dependencies) {
            if (allDeps[dep]) {
              depsFound++;
              
              // 检查是否是特定框架的关键依赖
              if (['react', 'vue', 'next', '@angular/core', 'gatsby'].includes(dep)) {
                frameworkDepFound = true;
              }
              
              // 尝试提取版本信息
              if (!version && allDeps[dep]) {
                version = allDeps[dep].replace(/[\^~>=<]/, '');
              }
            }
          }
          
          // 计算基础依赖分数
          dependencyScore = (depsFound / signature.dependencies.length) * dependencyWeight;
          
          // 对特定框架依赖给予奖励
          if (frameworkDepFound && signature.type !== 'nodejs' && signature.type !== 'generic') {
            dependencyScore = Math.min(dependencyScore * 1.5, dependencyWeight); // 增加50%奖励但不超过最大权重
          }
          
          if (depsFound > 0) {
            foundFiles.push(packageJsonPath); // 使用完整路径
          }
        } catch (error) {
          detectionLog.push(`无法解析package.json: ${error}`);
          // 对于非Node.js项目，解析失败不完全阻止评分，但不给依赖分数
        }
      }
    }
    
    // 4. 检查目录结构 (权重: 10%，如果没有必需文件则增加到20%)
    const directoryWeight = hasRequiredFiles ? 0.1 : 0.2;
    let directoryScore = 0;
    if (signature.directories.length > 0) {
      let dirsFound = 0;
      for (const dir of signature.directories) {
        const dirPath = join(projectPath, dir);
        if (existsSync(dirPath) && statSync(dirPath).isDirectory()) {
          dirsFound++;
        }
      }
      
      // 对于Next.js项目，只要找到一个关键目录就给部分高分
      if (signature.type === 'nextjs' && dirsFound > 0) {
        directoryScore = directoryWeight * 0.8; // 给80%的目录分数
      } else {
        directoryScore = (dirsFound / signature.directories.length) * directoryWeight;
      }
    }
    
    // 计算总分
    const totalScore = requiredFileScore + optionalFileScore + dependencyScore + directoryScore;
    
    // 如果没有设置任何条件，给予最小分数
    if (signature.requiredFiles.length === 0 && 
        signature.optionalFiles.length === 0 && 
        signature.dependencies.length === 0 && 
        signature.directories.length === 0) {
      return {
        confidence: 0.1,
        foundFiles: [...new Set(foundFiles)], // 去重
        version,
      };
    }
    
    return {
      confidence: Math.min(totalScore, 1),
      foundFiles: [...new Set(foundFiles)], // 去重
      version,
    };
  }
  
  /**
   * 选择最佳匹配的项目类型
   */
  private selectBestMatch(
    candidates: Array<{
      type: ProjectType;
      confidence: number;
      configFiles: string[];
      frameworkVersion?: string;
    }>,
    detectionLog: string[]
  ): {
    type: ProjectType;
    confidence: number;
    configFiles: string[];
    frameworkVersion?: string;
  } {
    if (candidates.length === 0) {
      detectionLog.push('未找到匹配的项目类型，使用generic');
      return {
        type: 'generic',
        confidence: 0.1,
        configFiles: [],
      };
    }
    
    // 过滤掉generic类型，优先选择具体的项目类型
    const nonGenericCandidates = candidates.filter(c => c.type !== 'generic');
    
    if (nonGenericCandidates.length > 0) {
      // 特殊处理：如果存在具体框架类型和nodejs类型，优先选择框架类型
      const frameworkCandidates = nonGenericCandidates.filter(c => 
        ['react', 'vue', 'nextjs', 'gatsby', 'angular'].includes(c.type)
      );
      const nodejsCandidates = nonGenericCandidates.filter(c => c.type === 'nodejs');
      
      // 如果同时有框架类型和nodejs类型，优先选择框架类型
      if (frameworkCandidates.length > 0 && nodejsCandidates.length > 0) {
        // 按置信度排序框架候选项
        frameworkCandidates.sort((a, b) => b.confidence - a.confidence);
        const bestFramework = frameworkCandidates[0];
        const nodejs = nodejsCandidates[0];
        
        // 如果框架置信度不低于nodejs太多，选择框架
        if (bestFramework && nodejs && bestFramework.confidence >= nodejs.confidence * 0.8) {
          detectionLog.push(`优先选择框架类型: ${bestFramework.type} (置信度: ${bestFramework.confidence.toFixed(2)}) 而不是 nodejs (置信度: ${nodejs.confidence.toFixed(2)})`);
          return bestFramework;
        }
      }
      
      const bestMatch = nonGenericCandidates[0];
      if (bestMatch) {
        detectionLog.push(`选择最佳匹配: ${bestMatch.type} (置信度: ${bestMatch.confidence.toFixed(2)})`);
        return bestMatch;
      }
    }
    
    // 如果只有generic类型匹配，才使用它
    const bestMatch = candidates[0];
    if (bestMatch) {
      detectionLog.push(`只找到通用类型匹配: ${bestMatch.type} (置信度: ${bestMatch.confidence.toFixed(2)})`);
      return bestMatch;
    }
    
    // 如果没有匹配项，返回默认配置
    return {
      type: 'generic' as const,
      confidence: 0.1,
      configFiles: [],
    };
  }
  
  /**
   * 检测是否为monorepo项目
   */
  private async detectMonorepo(projectPath: string, detectionLog: string[]): Promise<boolean> {
    // 检查常见的monorepo标志
    const monorepoIndicators = [
      'lerna.json',
      'nx.json',
      'rush.json',
      'pnpm-workspace.yaml',
      'yarn.lock', // 配合workspaces字段
    ];
    
    for (const indicator of monorepoIndicators) {
      if (existsSync(join(projectPath, indicator))) {
        detectionLog.push(`检测到monorepo标志: ${indicator}`);
        return true;
      }
    }
    
    // 检查package.json中的workspaces字段
    const packageJsonPath = join(projectPath, 'package.json');
    if (existsSync(packageJsonPath)) {
      try {
        const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
        if (packageJson.workspaces) {
          detectionLog.push('检测到package.json workspaces字段');
          return true;
        }
      } catch (error) {
        // 忽略解析错误
      }
    }
    
    return false;
  }
  
  /**
   * 检测子项目
   */
  private async detectSubProjects(
    projectPath: string,
    detectionLog: string[]
  ): Promise<ProjectDetectionResult[]> {
    const subProjects: ProjectDetectionResult[] = [];
    
    // 查找可能的子项目目录
    const possibleSubDirs = [
      'packages/*',
      'apps/*',
      'projects/*',
      'libs/*',
    ];
    
    for (const pattern of possibleSubDirs) {
      const dirs = await this.findDirectories(projectPath, pattern);
      
      for (const dir of dirs) {
        try {
          const subDetection = await this.detectProject(dir);
          if (subDetection.type !== 'generic') {
            subProjects.push(subDetection);
            detectionLog.push(`检测到子项目: ${dir} (${subDetection.type})`);
          }
        } catch (error) {
          // 忽略子项目检测错误
        }
      }
    }
    
    return subProjects;
  }
  
  /**
   * 查找文件
   */
  private async findFiles(basePath: string, pattern: string): Promise<string[]> {
    try {
      const files = await glob(pattern, {
        cwd: basePath,
        absolute: true,
        ignore: ['**/node_modules/**'],
      });
      return files.filter(file => existsSync(file) && statSync(file).isFile());
    } catch (error) {
      return [];
    }
  }
  
  /**
   * 查找目录
   */
  private async findDirectories(basePath: string, pattern: string): Promise<string[]> {
    try {
      const dirs = await glob(pattern, {
        cwd: basePath,
        absolute: true,
        ignore: ['**/node_modules/**'],
      });
      return dirs.filter(dir => existsSync(dir) && statSync(dir).isDirectory());
    } catch (error) {
      return [];
    }
  }
  
  /**
   * 添加项目类型特定的建议
   */
  private addProjectSpecificRecommendations(projectType: ProjectType, suggestions: string[]): void {
    switch (projectType) {
      case 'nextjs':
        suggestions.push('考虑为pages/和app/目录设置不同的复杂度阈值');
        suggestions.push('建议启用Server Components复杂度分析');
        break;
        
      case 'react':
        suggestions.push('建议为组件和业务逻辑设置不同的复杂度标准');
        suggestions.push('考虑启用Hook复杂度检测');
        break;
        
      case 'gatsby':
        suggestions.push('GraphQL查询复杂度可以单独评估');
        suggestions.push('静态查询通常可以豁免复杂度检查');
        break;
        
      case 'vue':
        suggestions.push('Vue组件的template部分通常不需要复杂度分析');
        suggestions.push('建议专注于script部分的逻辑复杂度');
        break;
        
      case 'angular':
        suggestions.push('服务类和组件类建议使用不同的复杂度标准');
        suggestions.push('装饰器和依赖注入不应计入复杂度');
        break;
        
      case 'nodejs':
        suggestions.push('区分业务逻辑和基础设施代码的复杂度标准');
        suggestions.push('中间件函数通常可以设置较低的复杂度阈值');
        break;
        
      default:
        suggestions.push('建议根据项目特点自定义复杂度分析规则');
        break;
    }
  }
}

// =============================================================================
// 便利函数导出
// =============================================================================

/**
 * 检测项目类型的便利函数
 */
export async function detectProjectType(projectPath?: string): Promise<ProjectDetectionResult> {
  const detector = new ProjectDetector(projectPath);
  return detector.detectProject();
}

/**
 * 生成项目配置推荐的便利函数
 */
export function generateProjectConfig(detection: ProjectDetectionResult): ConfigRecommendation {
  const detector = new ProjectDetector();
  return detector.generateConfigRecommendation(detection);
}

/**
 * 智能配置生成 - 结合项目检测和配置推荐
 */
export async function generateSmartConfig(projectPath?: string): Promise<{
  detection: ProjectDetectionResult;
  recommendation: ConfigRecommendation;
  config: PartialCognitiveConfig;
}> {
  const detection = await detectProjectType(projectPath);
  const recommendation = generateProjectConfig(detection);
  
  return {
    detection,
    recommendation,
    config: recommendation.config,
  };
}