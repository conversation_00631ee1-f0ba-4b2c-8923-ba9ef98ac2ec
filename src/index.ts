// 主要模块导出 - 移除ComplexityCalculator导出
export { ComplexityVisitor } from './core/complexity-visitor';
export { ASTParser } from './core/parser';
export { DetailCollector } from './core/detail-collector';
export { RuleRegistry } from './core/rule-registry';
export { ConfigManager } from './config/manager';
export { BaselineManager } from './baseline/manager';
export * from './core/types';
export * from './config/types';
export * from './baseline/types';
export * from './core/errors';

// 工厂模块导出 - 暂时移除CalculatorFactory导出
// TODO: 将来可能需要基于ComplexityVisitor的工厂模式
// export { 
//   CalculatorFactory,
//   createCalculatorFactory,
//   createLightweightFactory,
//   createFullFeaturedFactory
// } from './core/calculator-factory';

// 规则相关导出
export { 
  initializeRules, 
  getDefaultRulesConfig, 
  getRulesByCategory,
  isKnownRule,
  getDefaultIncrement,
  getNodeTypeRuleId,
  getLogicalOperatorRuleId
} from './core/default-rules';

// 规则初始化系统导出
export { 
  initializeRuleSystem, 
  getRuleEngine,
  RuleInitializationManager,
  createRuleManager,
  resetDefaultManager
} from './core/rule-initialization';

import type { CalculationOptions, FileResult, AnalysisResult } from './core/types';
import type { CognitiveConfig } from './config/types';
import { ComplexityVisitor } from './core/complexity-visitor';
import { ASTParser } from './core/parser';
import { DetailCollector } from './core/detail-collector';
import * as fs from 'fs/promises';
import * as path from 'path';

// 主要函数导出
export async function analyzeFile(
  filePath: string, 
  options?: CalculationOptions
): Promise<FileResult> {
  try {
    // 读取文件内容
    const fileContent = await fs.readFile(filePath, 'utf-8');
    
    // 解析AST
    const parser = new ASTParser();
    const ast = await parser.parseCode(fileContent, filePath);
    
    // 创建详细信息收集器
    const detailCollector = options?.enableDetails ? new DetailCollector() : undefined;
    
    // 创建复杂度访问者
    const visitor = new ComplexityVisitor(fileContent, detailCollector, options);
    
    // 找到所有函数节点并分析
    const functions = findFunctionNodes(ast);
    
    for (const functionNode of functions) {
      visitor.visitFunction(functionNode);
    }
    
    // 获取结果
    const results = visitor.getResults();
    
    // 设置文件路径
    results.forEach(result => {
      result.filePath = filePath;
    });
    
    const totalComplexity = results.reduce((sum, fn) => sum + fn.complexity, 0);
    const averageComplexity = results.length > 0 ? totalComplexity / results.length : 0;
    
    return {
      filePath,
      complexity: totalComplexity,
      functions: results,
      averageComplexity
    };
  } catch (error) {
    // 错误处理：返回空结果但保留错误信息
    return {
      filePath,
      complexity: 0,
      functions: [],
      averageComplexity: 0
    };
  }
}

/**
 * 从AST中找到所有函数节点
 * @param ast AST根节点
 * @returns 函数节点数组
 */
function findFunctionNodes(ast: any): any[] {
  const functions: any[] = [];
  
  function traverse(node: any) {
    if (!node || typeof node !== 'object') return;
    
    // 检查是否是函数节点
    if (isFunctionNode(node)) {
      functions.push(node);
    }
    
    // 遍历子节点
    for (const key in node) {
      if (key === 'span' || key === 'type') continue;
      
      const value = node[key];
      if (Array.isArray(value)) {
        value.forEach(traverse);
      } else if (value && typeof value === 'object') {
        traverse(value);
      }
    }
  }
  
  traverse(ast);
  return functions;
}

/**
 * 检查节点是否是函数节点
 * @param node 节点
 * @returns 是否是函数节点
 */
function isFunctionNode(node: any): boolean {
  if (!node || !node.type) return false;
  
  const functionTypes = [
    'FunctionDeclaration',
    'FunctionExpression', 
    'ArrowFunctionExpression',
    'MethodDefinition',
    'ClassMethod'
  ];
  
  return functionTypes.includes(node.type);
}

export async function analyzeProject(
  paths: string[], 
  config?: CognitiveConfig
): Promise<AnalysisResult> {
  // 使用静态方法进行批量文件分析，自动管理资源
  // 未来可以使用 ComplexityCalculator.analyzeFiles(paths, config?.calculationOptions);
  
  throw new Error('Not implemented yet');
}