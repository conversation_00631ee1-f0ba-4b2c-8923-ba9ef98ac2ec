import type { BaselineEntry, BaselineData } from './types';
import type { AnalysisResult, FunctionResult } from '../core/types';
import { readFile, writeFile } from 'fs/promises';
import { existsSync } from 'fs';
import * as path from 'path';

export class BaselineManager {
  private baselinePath: string;

  constructor(baselinePath: string = 'cognitive-baseline.json') {
    this.baselinePath = baselinePath;
  }

  /**
   * 创建基线文件，记录所有超过阈值的函数
   */
  public async createBaseline(
    analysisResult: AnalysisResult, 
    threshold?: number
  ): Promise<void> {
    const entries: BaselineEntry[] = [];
    const now = new Date().toISOString();

    // 遍历所有分析结果，找出超过阈值的函数
    for (const fileResult of analysisResult.results) {
      for (const func of fileResult.functions) {
        if (!threshold || func.complexity >= threshold) {
          entries.push({
            filePath: fileResult.filePath,
            functionName: func.name,
            line: func.line,
            complexity: func.complexity,
            lastUpdated: now
          });
        }
      }
    }

    const baselineData: BaselineData = {
      version: '1.0.0',
      createdAt: now,
      entries
    };

    await this.saveBaseline(baselineData);
    
    console.log(`✅ 基线文件已创建: ${this.baselinePath}`);
    console.log(`📊 记录了 ${entries.length} 个超标函数`);
    if (threshold) {
      console.log(`🎯 阈值: ${threshold}`);
    }
  }

  /**
   * 加载现有的基线文件
   */
  public async loadBaseline(): Promise<BaselineData | null> {
    if (!existsSync(this.baselinePath)) {
      return null;
    }

    try {
      const content = await readFile(this.baselinePath, 'utf-8');
      const baselineData = JSON.parse(content) as BaselineData;
      
      // 验证基线文件格式
      this.validateBaseline(baselineData);
      
      return baselineData;
    } catch (error) {
      console.warn(`⚠️  无法加载基线文件 ${this.baselinePath}: ${error instanceof Error ? error.message : String(error)}`);
      return null;
    }
  }

  /**
   * 更新基线文件，移除已修复的函数，添加新的超标函数
   */
  public async updateBaseline(
    analysisResult: AnalysisResult,
    threshold?: number
  ): Promise<void> {
    const existingBaseline = await this.loadBaseline();
    
    if (!existingBaseline) {
      // 如果不存在基线文件，创建新的
      await this.createBaseline(analysisResult, threshold);
      return;
    }

    const now = new Date().toISOString();
    const updatedEntries: BaselineEntry[] = [];
    const currentFunctions = new Map<string, FunctionResult>();

    // 构建当前分析结果的函数映射
    for (const fileResult of analysisResult.results) {
      for (const func of fileResult.functions) {
        const key = `${fileResult.filePath}:${func.name}:${func.line}`;
        currentFunctions.set(key, func);
      }
    }

    // 处理现有基线条目
    for (const entry of existingBaseline.entries) {
      const key = `${entry.filePath}:${entry.functionName}:${entry.line}`;
      const currentFunc = currentFunctions.get(key);

      if (currentFunc) {
        // 函数仍然存在
        if (!threshold || currentFunc.complexity >= threshold) {
          // 仍然超标，更新复杂度和时间戳
          updatedEntries.push({
            ...entry,
            complexity: currentFunc.complexity,
            lastUpdated: now
          });
        }
        // 如果复杂度已降低到阈值以下，则不再包含在基线中
      }
      // 如果函数不存在了，也不再包含在基线中
    }

    // 添加新的超标函数（不在原基线中的）
    for (const fileResult of analysisResult.results) {
      for (const func of fileResult.functions) {
        if (!threshold || func.complexity >= threshold) {
          const key = `${fileResult.filePath}:${func.name}:${func.line}`;
          const wasInBaseline = existingBaseline.entries.some(
            entry => `${entry.filePath}:${entry.functionName}:${entry.line}` === key
          );

          if (!wasInBaseline) {
            updatedEntries.push({
              filePath: fileResult.filePath,
              functionName: func.name,
              line: func.line,
              complexity: func.complexity,
              lastUpdated: now
            });
          }
        }
      }
    }

    const updatedBaseline: BaselineData = {
      ...existingBaseline,
      entries: updatedEntries
    };

    await this.saveBaseline(updatedBaseline);

    console.log(`✅ 基线文件已更新: ${this.baselinePath}`);
    console.log(`📊 当前记录 ${updatedEntries.length} 个超标函数`);
    
    const removedCount = existingBaseline.entries.length - updatedEntries.length + 
      (updatedEntries.length - existingBaseline.entries.filter(old => 
        updatedEntries.some(updated => 
          old.filePath === updated.filePath && 
          old.functionName === updated.functionName && 
          old.line === updated.line
        )
      ).length);
    
    if (removedCount > 0) {
      console.log(`🎉 移除了 ${removedCount} 个已修复的函数`);
    }
  }

  /**
   * 将当前分析结果与基线进行比较
   * 返回只包含新问题或恶化问题的结果
   */
  public compareWithBaseline(
    current: AnalysisResult, 
    baseline: BaselineData
  ): AnalysisResult {
    // 创建基线函数映射
    const baselineMap = new Map<string, BaselineEntry>();
    for (const entry of baseline.entries) {
      const key = `${entry.filePath}:${entry.functionName}:${entry.line}`;
      baselineMap.set(key, entry);
    }

    const filteredResults = current.results.map(fileResult => {
      const filteredFunctions = fileResult.functions.filter(func => {
        const key = `${fileResult.filePath}:${func.name}:${func.line}`;
        const baselineEntry = baselineMap.get(key);

        if (!baselineEntry) {
          // 新的超标函数
          return true;
        }

        if (func.complexity > baselineEntry.complexity) {
          // 复杂度恶化的函数
          return true;
        }

        // 在基线中且没有恶化的函数，不报告
        return false;
      });

      return {
        ...fileResult,
        functions: filteredFunctions,
        complexity: filteredFunctions.reduce((sum, func) => sum + func.complexity, 0)
      };
    }).filter(fileResult => fileResult.functions.length > 0);

    // 重新计算汇总信息
    const totalComplexity = filteredResults.reduce((sum, file) => sum + file.complexity, 0);
    const functionsAnalyzed = filteredResults.reduce((sum, file) => sum + file.functions.length, 0);

    return {
      summary: {
        totalComplexity,
        averageComplexity: functionsAnalyzed > 0 ? totalComplexity / functionsAnalyzed : 0,
        filesAnalyzed: filteredResults.length,
        functionsAnalyzed,
        highComplexityFunctions: functionsAnalyzed // 所有过滤出的函数都是高复杂度的
      },
      results: filteredResults,
      baseline
    };
  }

  /**
   * 检查是否存在基线文件
   */
  public hasBaseline(): boolean {
    return existsSync(this.baselinePath);
  }

  /**
   * 获取基线文件路径
   */
  public getBaselinePath(): string {
    return path.resolve(this.baselinePath);
  }

  /**
   * 保存基线数据到文件
   */
  private async saveBaseline(baselineData: BaselineData): Promise<void> {
    const content = JSON.stringify(baselineData, null, 2);
    await writeFile(this.baselinePath, content, 'utf-8');
  }

  /**
   * 验证基线文件格式
   */
  private validateBaseline(baseline: any): void {
    if (!baseline || typeof baseline !== 'object') {
      throw new Error('基线文件格式无效：不是有效的JSON对象');
    }

    if (!baseline.version || !baseline.createdAt || !Array.isArray(baseline.entries)) {
      throw new Error('基线文件格式无效：缺少必要字段');
    }

    for (const entry of baseline.entries) {
      if (!entry.filePath || !entry.functionName || 
          typeof entry.line !== 'number' || 
          typeof entry.complexity !== 'number') {
        throw new Error('基线文件格式无效：条目格式错误');
      }
    }
  }
}