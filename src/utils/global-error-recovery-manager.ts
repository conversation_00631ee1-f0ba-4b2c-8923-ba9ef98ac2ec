import { ErrorOutputFormatter } from './error-output-formatter';
import { ErrorRecoveryService } from './error-recovery-service';
import { CognitiveComplexityError } from '../core/errors';

export interface ErrorRecoveryConfig {
  enabled?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  fallbackStrategies?: string[];
  logLevel?: 'none' | 'error' | 'warning' | 'info' | 'debug';
  enableMetrics?: boolean;
}

export interface ErrorRecoveryMetrics {
  totalErrors: number;
  recoveredErrors: number;
  failedRecoveries: number;
  averageRecoveryTime: number;
  errorsByType: Record<string, number>;
  recoveryStrategiesBySuccess: Record<string, { attempts: number; successes: number }>;
  performanceImpact: {
    timeSpentOnRecovery: number;
    memoryUsedForRecovery: number;
  };
}

export interface GlobalErrorContext {
  component: string;
  operation: string;
  filePath?: string;
  metadata?: Record<string, any>;
}

/**
 * 全局错误恢复优化管理器
 * 统一管理整个应用的错误处理、恢复策略和性能优化
 */
export class GlobalErrorRecoveryManager {
  private static instance: GlobalErrorRecoveryManager | null = null;
  
  private config: Required<ErrorRecoveryConfig>;
  private errorFormatter: ErrorOutputFormatter;
  private fileRecoveryService: ErrorRecoveryService;
  private metrics: ErrorRecoveryMetrics;
  private errorHistory: Array<{
    error: Error;
    context: GlobalErrorContext;
    timestamp: number;
    recovered: boolean;
    recoveryTime?: number;
    strategy?: string;
  }> = [];

  private constructor(config: ErrorRecoveryConfig = {}) {
    this.config = {
      enabled: config.enabled ?? true,
      maxRetries: config.maxRetries ?? 3,
      retryDelay: config.retryDelay ?? 1000,
      fallbackStrategies: config.fallbackStrategies ?? ['fallback', 'skip', 'default'],
      logLevel: config.logLevel ?? 'warning',
      enableMetrics: config.enableMetrics ?? true
    };

    this.errorFormatter = new ErrorOutputFormatter({
      showStackTrace: this.config.logLevel === 'debug',
      showDetails: this.config.logLevel !== 'none',
      colorize: true,
      includeTimestamp: this.config.enableMetrics
    });

    this.fileRecoveryService = new ErrorRecoveryService({
      maxRetries: this.config.maxRetries,
      retryDelay: this.config.retryDelay,
      enableCaching: true,
      logErrors: this.config.logLevel !== 'none'
    });

    this.metrics = this.initializeMetrics();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(config?: ErrorRecoveryConfig): GlobalErrorRecoveryManager {
    if (!GlobalErrorRecoveryManager.instance) {
      GlobalErrorRecoveryManager.instance = new GlobalErrorRecoveryManager(config);
    }
    return GlobalErrorRecoveryManager.instance;
  }

  /**
   * 处理全局错误，提供统一的错误恢复机制
   */
  public async handleError<T>(
    error: Error,
    context: GlobalErrorContext,
    recoveryFunction?: () => Promise<T> | T
  ): Promise<{ success: boolean; result?: T; error?: Error; strategy: string }> {
    if (!this.config.enabled) {
      return { success: false, error, strategy: 'disabled' };
    }

    const startTime = performance.now();
    
    try {
      this.updateMetrics(error, context);
      
      // 记录错误到历史
      const historyEntry: {
        error: Error;
        context: GlobalErrorContext;
        timestamp: number;
        recovered: boolean;
        recoveryTime?: number;
        strategy?: string;
      } = {
        error,
        context,
        timestamp: Date.now(),
        recovered: false
      };

      // 输出错误信息（根据日志级别）
      if (this.shouldLogError(error)) {
        this.errorFormatter.outputError(error, context.component);
      }

      // 尝试恢复策略
      const recoveryResult = await this.attemptRecovery(error, context, recoveryFunction);
      
      // 更新历史记录
      historyEntry.recovered = recoveryResult.success;
      historyEntry.recoveryTime = performance.now() - startTime;
      historyEntry.strategy = recoveryResult.strategy;
      
      this.errorHistory.push(historyEntry);

      // 更新性能指标
      if (this.config.enableMetrics) {
        this.updateRecoveryMetrics(recoveryResult.strategy, recoveryResult.success, historyEntry.recoveryTime);
      }

      return recoveryResult;

    } catch (handlingError) {
      // 错误处理本身失败
      const fallbackError = new CognitiveComplexityError(
        `错误处理失败: ${handlingError instanceof Error ? handlingError.message : String(handlingError)}`,
        'ERROR_HANDLING_FAILED',
        { originalError: error.message, context }
      );

      if (this.config.logLevel !== 'none') {
        console.error('[GlobalErrorRecovery] 错误处理失败:', fallbackError.message);
      }

      return { success: false, error: fallbackError, strategy: 'handling-failed' };
    }
  }

  /**
   * 批量处理错误
   */
  public async handleErrors(
    errors: Array<{ error: Error; context: GlobalErrorContext }>
  ): Promise<Array<{ success: boolean; error?: Error; strategy: string }>> {
    const results = [];
    
    for (const { error, context } of errors) {
      const result = await this.handleError(error, context);
      results.push({
        success: result.success,
        error: result.error,
        strategy: result.strategy
      });
    }

    return results;
  }

  /**
   * 获取错误恢复指标
   */
  public getMetrics(): ErrorRecoveryMetrics {
    if (this.config.enableMetrics) {
      return { ...this.metrics };
    }
    return this.initializeMetrics();
  }

  /**
   * 输出错误恢复报告
   */
  public outputRecoveryReport(): void {
    if (!this.config.enableMetrics) {
      console.log('错误恢复指标收集已禁用');
      return;
    }

    const metrics = this.getMetrics();
    
    console.log('\n=== 错误恢复报告 ===');
    console.log(`总错误数: ${metrics.totalErrors}`);
    console.log(`成功恢复: ${metrics.recoveredErrors}`);
    console.log(`恢复失败: ${metrics.failedRecoveries}`);
    
    if (metrics.totalErrors > 0) {
      const recoveryRate = Math.round((metrics.recoveredErrors / metrics.totalErrors) * 100);
      console.log(`恢复率: ${recoveryRate}%`);
      console.log(`平均恢复时间: ${Math.round(metrics.averageRecoveryTime)}ms`);
    }

    if (Object.keys(metrics.errorsByType).length > 0) {
      console.log('\n错误类型分布:');
      for (const [type, count] of Object.entries(metrics.errorsByType)) {
        console.log(`  ${type}: ${count}`);
      }
    }

    if (Object.keys(metrics.recoveryStrategiesBySuccess).length > 0) {
      console.log('\n恢复策略效果:');
      for (const [strategy, stats] of Object.entries(metrics.recoveryStrategiesBySuccess)) {
        const successRate = stats.attempts > 0 ? Math.round((stats.successes / stats.attempts) * 100) : 0;
        console.log(`  ${strategy}: ${stats.successes}/${stats.attempts} (${successRate}%)`);
      }
    }

    console.log(`\n性能影响:`);
    console.log(`  恢复耗时: ${Math.round(metrics.performanceImpact.timeSpentOnRecovery)}ms`);
    console.log(`  内存使用: ${Math.round(metrics.performanceImpact.memoryUsedForRecovery / 1024)}KB`);
    console.log('========================\n');
  }

  /**
   * 重置指标
   */
  public resetMetrics(): void {
    this.metrics = this.initializeMetrics();
    this.errorHistory = [];
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ErrorRecoveryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重新创建 errorFormatter 以应用新配置
    this.errorFormatter = new ErrorOutputFormatter({
      showStackTrace: this.config.logLevel === 'debug',
      showDetails: this.config.logLevel !== 'none',
      colorize: true,
      includeTimestamp: this.config.enableMetrics
    });
  }

  /**
   * 获取错误历史
   */
  public getErrorHistory(): Array<{
    error: Error;
    context: GlobalErrorContext;
    timestamp: number;
    recovered: boolean;
    recoveryTime?: number;
    strategy?: string;
  }> {
    return [...this.errorHistory];
  }

  // 私有方法

  private async attemptRecovery<T>(
    error: Error,
    context: GlobalErrorContext,
    recoveryFunction?: () => Promise<T> | T
  ): Promise<{ success: boolean; result?: T; error?: Error; strategy: string }> {
    // 如果是文件相关错误，使用文件恢复服务
    if (context.operation.includes('file') || context.filePath) {
      if (context.operation === 'file-read' && context.filePath) {
        const fileResult = await this.fileRecoveryService.readFileWithRecovery(context.filePath);
        return {
          success: fileResult.success,
          result: fileResult.result as T,
          error: fileResult.error,
          strategy: `file-recovery-${fileResult.strategy}`
        };
      }
    }

    // 如果提供了恢复函数，尝试执行
    if (recoveryFunction) {
      for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
        try {
          const result = await Promise.resolve(recoveryFunction());
          return {
            success: true,
            result,
            strategy: `custom-recovery-attempt-${attempt}`
          };
        } catch (recoveryError) {
          if (attempt === this.config.maxRetries) {
            return {
              success: false,
              error: recoveryError as Error,
              strategy: `custom-recovery-failed-after-${attempt}-attempts`
            };
          }
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        }
      }
    }

    // 使用默认恢复策略
    return this.applyFallbackStrategy(error, context);
  }

  private applyFallbackStrategy(
    error: Error,
    context: GlobalErrorContext
  ): { success: boolean; error?: Error; strategy: string } {
    for (const strategy of this.config.fallbackStrategies) {
      switch (strategy) {
        case 'fallback':
          // 提供默认/降级值
          return { success: true, strategy: 'fallback-default' };
        
        case 'skip':
          // 跳过当前操作，继续执行
          return { success: true, strategy: 'skip-operation' };
        
        case 'default':
          // 使用默认行为
          return { success: true, strategy: 'default-behavior' };
      }
    }

    return {
      success: false,
      error,
      strategy: 'no-fallback-available'
    };
  }

  private shouldLogError(error: Error): boolean {
    if (this.config.logLevel === 'none') return false;
    if (this.config.logLevel === 'debug') return true;
    
    // 根据错误类型和严重程度决定是否记录
    if (error instanceof CognitiveComplexityError) {
      const code = error.code;
      if (code === 'FILE_READ_ERROR' && this.config.logLevel === 'error') return true;
      if (code === 'PARSE_ERROR' && this.config.logLevel === 'error') return true;
      if (code === 'CODE_FRAME_ERROR' && ['warning', 'info'].includes(this.config.logLevel)) return true;
    }

    return this.config.logLevel === 'info';
  }

  private updateMetrics(error: Error, context: GlobalErrorContext): void {
    if (!this.config.enableMetrics) return;

    this.metrics.totalErrors++;
    
    const errorType = error instanceof CognitiveComplexityError ? error.code : error.constructor.name;
    this.metrics.errorsByType[errorType] = (this.metrics.errorsByType[errorType] || 0) + 1;
  }

  private updateRecoveryMetrics(strategy: string, success: boolean, recoveryTime?: number): void {
    if (!this.config.enableMetrics) return;

    if (success) {
      this.metrics.recoveredErrors++;
    } else {
      this.metrics.failedRecoveries++;
    }

    if (recoveryTime) {
      const totalTime = this.metrics.averageRecoveryTime * (this.metrics.recoveredErrors + this.metrics.failedRecoveries - 1);
      this.metrics.averageRecoveryTime = (totalTime + recoveryTime) / (this.metrics.recoveredErrors + this.metrics.failedRecoveries);
      this.metrics.performanceImpact.timeSpentOnRecovery += recoveryTime;
    }

    // 更新策略统计
    if (!this.metrics.recoveryStrategiesBySuccess[strategy]) {
      this.metrics.recoveryStrategiesBySuccess[strategy] = { attempts: 0, successes: 0 };
    }
    this.metrics.recoveryStrategiesBySuccess[strategy].attempts++;
    if (success) {
      this.metrics.recoveryStrategiesBySuccess[strategy].successes++;
    }
  }

  private initializeMetrics(): ErrorRecoveryMetrics {
    return {
      totalErrors: 0,
      recoveredErrors: 0,
      failedRecoveries: 0,
      averageRecoveryTime: 0,
      errorsByType: {},
      recoveryStrategiesBySuccess: {},
      performanceImpact: {
        timeSpentOnRecovery: 0,
        memoryUsedForRecovery: 0
      }
    };
  }
}

// 便捷函数
export function getGlobalErrorRecoveryManager(config?: ErrorRecoveryConfig): GlobalErrorRecoveryManager {
  return GlobalErrorRecoveryManager.getInstance(config);
}

// 全局错误处理函数
export async function handleGlobalError<T>(
  error: Error,
  context: GlobalErrorContext,
  recoveryFunction?: () => Promise<T> | T
): Promise<{ success: boolean; result?: T; error?: Error; strategy: string }> {
  const manager = getGlobalErrorRecoveryManager();
  return manager.handleError(error, context, recoveryFunction);
}