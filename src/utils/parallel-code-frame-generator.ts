/**
 * 并行代码框架生成器
 * 提供高性能的批量代码框架生成，支持并发控制和进度监控
 */

import { CodeFrameGenerator } from './code-frame-generator';
import type { CodeFrameOptions, CodeFrameResult } from './code-frame-generator';
import { FileContentCache, createFileCache } from './file-cache';

export interface ParallelGenerationOptions {
  concurrency?: number; // 并发数量
  enableProgressCallback?: boolean;
  enableBatching?: boolean;
  batchSize?: number;
  enablePreloading?: boolean;
  timeout?: number; // 毫秒
}

export interface GenerationTask {
  id: string;
  filePath: string;
  line: number;
  column: number;
  options?: CodeFrameOptions;
}

export interface SpanGenerationTask {
  id: string;
  filePath: string;
  span: { start: number; end: number };
  options?: CodeFrameOptions;
}

export interface GenerationResult {
  id: string;
  result: CodeFrameResult;
  duration: number;
  error?: Error;
}

export interface GenerationProgress {
  completed: number;
  total: number;
  percentage: number;
  currentTask?: string;
  errors: number;
  averageTime: number;
}

export type ProgressCallback = (progress: GenerationProgress) => void;

/**
 * 并行代码框架生成器
 * 支持批量生成、并发控制和性能优化
 */
export class ParallelCodeFrameGenerator {
  private generator: CodeFrameGenerator;
  private fileCache: FileContentCache;
  
  constructor(options?: {
    fileCache?: FileContentCache;
    cacheOptions?: {
      maxSize?: number;
      maxMemory?: number;
      defaultTTL?: number;
    };
  }) {
    this.fileCache = options?.fileCache || createFileCache({
      maxSize: 3000,
      maxMemory: 300 * 1024 * 1024, // 300MB
      defaultTTL: 45 * 60 * 1000, // 45分钟
      cleanupInterval: 3 * 60 * 1000, // 3分钟
      enableStatistics: true,
      ...options?.cacheOptions,
    });
    
    this.generator = new CodeFrameGenerator(this.fileCache);
  }

  /**
   * 并行生成多个代码框架
   */
  async generateFrames(
    tasks: GenerationTask[],
    options: ParallelGenerationOptions = {},
    progressCallback?: ProgressCallback
  ): Promise<GenerationResult[]> {
    const {
      concurrency = 10,
      enablePreloading = true,
      enableBatching = true,
      batchSize = 50,
      timeout = 30000,
    } = options;

    // 预加载文件内容
    if (enablePreloading) {
      const uniqueFilePaths = [...new Set(tasks.map(task => task.filePath))];
      await this.preloadFiles(uniqueFilePaths);
    }

    // 准备进度跟踪
    const progress: GenerationProgress = {
      completed: 0,
      total: tasks.length,
      percentage: 0,
      errors: 0,
      averageTime: 0,
    };

    const results: GenerationResult[] = [];
    const durations: number[] = [];

    // 批处理或直接处理
    if (enableBatching && tasks.length > batchSize) {
      const batches = this.chunkArray(tasks, batchSize);
      
      for (const batch of batches) {
        const batchResults = await this.processBatch(
          batch,
          concurrency, 
          timeout,
          (batchProgress) => {
            progress.completed += batchProgress;
            progress.percentage = (progress.completed / progress.total) * 100;
            progress.averageTime = durations.length > 0 
              ? durations.reduce((a, b) => a + b, 0) / durations.length 
              : 0;
            progressCallback?.(progress);
          }
        );
        
        results.push(...batchResults);
        durations.push(...batchResults.map(r => r.duration));
        
        // 收集错误统计
        progress.errors += batchResults.filter(r => r.error).length;
      }
    } else {
      const batchResults = await this.processBatch(
        tasks,
        concurrency,
        timeout,
        (completed) => {
          progress.completed = completed;
          progress.percentage = (progress.completed / progress.total) * 100;
          progress.averageTime = durations.length > 0 
            ? durations.reduce((a, b) => a + b, 0) / durations.length 
            : 0;
          progressCallback?.(progress);
        }
      );
      
      results.push(...batchResults);
      progress.errors = batchResults.filter(r => r.error).length;
    }

    // 最终进度更新
    progress.completed = tasks.length;
    progress.percentage = 100;
    progressCallback?.(progress);

    return results;
  }

  /**
   * 并行生成基于span的代码框架
   */
  async generateFramesFromSpans(
    tasks: SpanGenerationTask[],
    options: ParallelGenerationOptions = {},
    progressCallback?: ProgressCallback
  ): Promise<GenerationResult[]> {
    const {
      concurrency = 10,
      enablePreloading = true,
      timeout = 30000,
    } = options;

    // 预加载文件内容
    if (enablePreloading) {
      const uniqueFilePaths = [...new Set(tasks.map(task => task.filePath))];
      await this.preloadFiles(uniqueFilePaths);
    }

    const progress: GenerationProgress = {
      completed: 0,
      total: tasks.length,
      percentage: 0,
      errors: 0,
      averageTime: 0,
    };

    const results = await this.processConcurrently(
      tasks,
      async (task) => {
        const startTime = performance.now();
        
        try {
          const result = await Promise.race([
            this.generator.generateFrameFromSpan(task.filePath, task.span, task.options),
            this.createTimeoutPromise(timeout),
          ]);
          
          const duration = performance.now() - startTime;
          
          return {
            id: task.id,
            result: result as CodeFrameResult,
            duration,
          };
        } catch (error) {
          const duration = performance.now() - startTime;
          
          return {
            id: task.id,
            result: {
              frame: `[生成失败: ${task.filePath}]`,
              success: false,
              error: error instanceof Error ? error.message : '超时或未知错误',
              cached: false,
            },
            duration,
            error: error instanceof Error ? error : new Error(String(error)),
          };
        }
      },
      concurrency,
      (completed) => {
        progress.completed = completed;
        progress.percentage = (completed / tasks.length) * 100;
        progressCallback?.(progress);
      }
    );

    progress.errors = results.filter(r => r.error).length;
    progress.averageTime = results.length > 0 
      ? results.reduce((sum, r) => sum + r.duration, 0) / results.length 
      : 0;
    
    progressCallback?.(progress);

    return results;
  }

  /**
   * 预加载文件到缓存
   */
  async preloadFiles(filePaths: string[]): Promise<void> {
    await this.fileCache.preloadFiles(filePaths, 15);
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      fileCache: this.fileCache.getCacheStats(),
      cacheDetails: this.fileCache.getCacheDetails(),
      generatorStats: this.generator.getCacheStats(),
    };
  }

  /**
   * 优化缓存性能
   */
  async optimizeCache() {
    const result = await this.fileCache.optimizeCache();
    await this.generator.optimizeCache();
    return result;
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    this.fileCache.destroy();
    this.generator.clearCache();
  }

  // 私有方法

  private async processBatch(
    tasks: GenerationTask[],
    concurrency: number,
    timeout: number,
    progressCallback?: (completed: number) => void
  ): Promise<GenerationResult[]> {
    return this.processConcurrently(
      tasks,
      async (task) => {
        const startTime = performance.now();
        
        try {
          const result = await Promise.race([
            this.generator.generateFrame(task.filePath, task.line, task.column, task.options),
            this.createTimeoutPromise(timeout),
          ]);
          
          const duration = performance.now() - startTime;
          
          return {
            id: task.id,
            result: result as CodeFrameResult,
            duration,
          };
        } catch (error) {
          const duration = performance.now() - startTime;
          
          return {
            id: task.id,
            result: {
              frame: `[生成失败: ${task.filePath}]`,
              success: false,
              error: error instanceof Error ? error.message : '超时或未知错误',
              cached: false,
            },
            duration,
            error: error instanceof Error ? error : new Error(String(error)),
          };
        }
      },
      concurrency,
      progressCallback
    );
  }

  private async processConcurrently<T, R>(
    items: T[],
    processor: (item: T) => Promise<R>,
    concurrency: number,
    progressCallback?: (completed: number) => void
  ): Promise<R[]> {
    const results: R[] = [];
    let completed = 0;
    let index = 0;

    const executeNext = async (): Promise<void> => {
      if (index >= items.length) return;
      
      const currentIndex = index++;
      const item = items[currentIndex];
      
      if (!item) {
        // 递归调用以处理下一个任务
        return executeNext();
      }
      
      try {
        const result = await processor(item);
        results[currentIndex] = result;
      } catch (error) {
        // 错误处理已在processor中完成
        results[currentIndex] = error as R;
      }
      
      completed++;
      progressCallback?.(completed);
      
      // 继续处理下一个任务
      await executeNext();
    };

    // 启动并发工作者
    const workers = Array.from({ length: Math.min(concurrency, items.length) }, () => 
      executeNext()
    );
    
    await Promise.all(workers);
    
    return results;
  }

  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('代码框架生成超时')), timeout);
    });
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}

// 便捷函数
export function createParallelGenerator(options?: {
  maxConcurrency?: number;
  cacheSize?: number;
  cacheMemory?: number;
}): ParallelCodeFrameGenerator {
  return new ParallelCodeFrameGenerator({
    cacheOptions: {
      maxSize: options?.cacheSize || 3000,
      maxMemory: options?.cacheMemory || 300 * 1024 * 1024,
    },
  });
}

// 全局实例
let globalParallelGenerator: ParallelCodeFrameGenerator | null = null;

export function getGlobalParallelGenerator(): ParallelCodeFrameGenerator {
  if (!globalParallelGenerator) {
    globalParallelGenerator = createParallelGenerator();
  }
  return globalParallelGenerator;
}