import chalk from 'chalk';
import { CognitiveComplexityError } from '../core/errors';

export interface ErrorOutputOptions {
  showStackTrace?: boolean;
  showDetails?: boolean;
  colorize?: boolean;
  maxDetailLength?: number;
  includeTimestamp?: boolean;
}

export interface FormattedError {
  message: string;
  level: 'error' | 'warning' | 'info';
  timestamp?: string;
  context?: string;
}

/**
 * 错误信息输出格式化器
 * 提供统一的错误信息格式化和输出机制
 */
export class ErrorOutputFormatter {
  private readonly options: Required<ErrorOutputOptions>;

  constructor(options: ErrorOutputOptions = {}) {
    this.options = {
      showStackTrace: options.showStackTrace ?? false,
      showDetails: options.showDetails ?? true,
      colorize: options.colorize ?? true,
      maxDetailLength: options.maxDetailLength ?? 200,
      includeTimestamp: options.includeTimestamp ?? false
    };
  }

  /**
   * 格式化错误信息用于控制台输出
   */
  formatError(error: Error | CognitiveComplexityError, context?: string): FormattedError {
    const isComplexityError = error instanceof CognitiveComplexityError;
    
    let message = error.message;
    let level: 'error' | 'warning' | 'info' = 'error';

    // 根据错误代码确定级别
    if (isComplexityError) {
      level = this.getErrorLevel(error.code);
      message = this.formatComplexityError(error);
    }

    const formattedError: FormattedError = {
      message,
      level,
      context
    };

    if (this.options.includeTimestamp) {
      formattedError.timestamp = new Date().toISOString();
    }

    return formattedError;
  }

  /**
   * 将格式化的错误输出到控制台
   */
  outputError(error: Error | CognitiveComplexityError, context?: string): void {
    const formatted = this.formatError(error, context);
    const output = this.renderFormattedError(formatted, error);
    
    // 根据级别选择输出方法
    switch (formatted.level) {
      case 'error':
        console.error(output);
        break;
      case 'warning':
        console.warn(output);
        break;
      case 'info':
        console.info(output);
        break;
    }

    // 输出堆栈信息（如果启用）
    if (this.options.showStackTrace && error.stack) {
      console.error(this.formatStackTrace(error.stack));
    }
  }

  /**
   * 批量输出多个错误
   */
  outputErrors(errors: Array<{ error: Error | CognitiveComplexityError; context?: string }>): void {
    if (errors.length === 0) {
      return;
    }

    const header = this.options.colorize 
      ? chalk.red.bold(`\n发现 ${errors.length} 个错误:`)
      : `\n发现 ${errors.length} 个错误:`;
    
    console.error(header);
    console.error(this.options.colorize ? chalk.gray(''.padEnd(50, '-')) : ''.padEnd(50, '-'));

    errors.forEach((item, index) => {
      const prefix = this.options.colorize 
        ? chalk.gray(`${index + 1}. `)
        : `${index + 1}. `;
      
      const formatted = this.formatError(item.error, item.context);
      const output = this.renderFormattedError(formatted, item.error);
      
      console.error(prefix + output);
      
      // 如果不是最后一个错误，添加分隔符
      if (index < errors.length - 1) {
        console.error();
      }
    });

    console.error(); // 结尾空行
  }

  /**
   * 格式化错误恢复信息
   */
  formatRecoveryInfo(attempts: number, strategy: string, success: boolean): string {
    const status = success 
      ? (this.options.colorize ? chalk.green('成功') : '成功')
      : (this.options.colorize ? chalk.red('失败') : '失败');
    
    const info = `恢复尝试: ${attempts} 次, 策略: ${strategy}, 状态: ${status}`;
    
    return this.options.colorize ? chalk.gray(`[${info}]`) : `[${info}]`;
  }

  /**
   * 输出错误统计信息
   */
  outputErrorStats(stats: {
    totalErrors: number;
    errorsByType: Record<string, number>;
    recoveryAttempts?: number;
    successfulRecoveries?: number;
  }): void {
    const title = this.options.colorize 
      ? chalk.cyan.bold('\n错误统计:')
      : '\n错误统计:';
    
    console.log(title);
    console.log(this.options.colorize ? chalk.gray(''.padEnd(30, '-')) : ''.padEnd(30, '-'));
    
    console.log(`总错误数: ${stats.totalErrors}`);
    
    if (Object.keys(stats.errorsByType).length > 0) {
      console.log('错误类型分布:');
      for (const [type, count] of Object.entries(stats.errorsByType)) {
        const displayType = this.getErrorTypeDisplayName(type);
        console.log(`  ${displayType}: ${count}`);
      }
    }

    if (stats.recoveryAttempts !== undefined) {
      const recoveryRate = stats.totalErrors > 0 
        ? Math.round((stats.successfulRecoveries || 0) / stats.totalErrors * 100)
        : 0;
      
      console.log(`恢复尝试: ${stats.recoveryAttempts}`);
      console.log(`成功恢复: ${stats.successfulRecoveries || 0}`);
      console.log(`恢复率: ${recoveryRate}%`);
    }

    console.log(); // 结尾空行
  }

  // 私有方法

  private formatComplexityError(error: CognitiveComplexityError): string {
    let message = error.message;

    // 如果有详细信息且启用了详细显示
    if (this.options.showDetails && error.details) {
      const details = this.formatErrorDetails(error.details);
      if (details) {
        message += `\n${details}`;
      }
    }

    return message;
  }

  private formatErrorDetails(details: Record<string, any>): string {
    const parts: string[] = [];

    // 文件路径
    if (details.filePath) {
      parts.push(`文件: ${details.filePath}`);
    }

    // 位置信息
    if (details.position) {
      const pos = details.position;
      parts.push(`位置: 第 ${pos.line} 行，第 ${pos.column} 列`);
    }

    // 错误代码
    if (details.errorCode) {
      parts.push(`错误代码: ${details.errorCode}`);
    }

    // 原始错误
    if (details.originalError) {
      parts.push(`原因: ${details.originalError}`);
    }

    const detailString = parts.join(', ');
    
    // 限制详细信息长度
    if (detailString.length > this.options.maxDetailLength) {
      return detailString.substring(0, this.options.maxDetailLength - 3) + '...';
    }

    return detailString;
  }

  private getErrorLevel(errorCode: string): 'error' | 'warning' | 'info' {
    switch (errorCode) {
      case 'FILE_READ_ERROR':
      case 'PARSE_ERROR':
        return 'error';
      case 'CODE_FRAME_ERROR':
      case 'DETAIL_COLLECTION_ERROR':
        return 'warning';
      case 'VALIDATION_ERROR':
        return 'info';
      default:
        return 'error';
    }
  }

  private getErrorTypeDisplayName(errorCode: string): string {
    const nameMap: Record<string, string> = {
      'FILE_READ_ERROR': '文件读取错误',
      'PARSE_ERROR': '解析错误',
      'CODE_FRAME_ERROR': '代码框架错误',
      'DETAIL_COLLECTION_ERROR': '详情收集错误',
      'VALIDATION_ERROR': '验证错误',
      'CONFIG_ERROR': '配置错误'
    };

    return nameMap[errorCode] || errorCode;
  }

  private renderFormattedError(formatted: FormattedError, originalError: Error): string {
    if (!this.options.colorize) {
      return this.renderPlainError(formatted);
    }

    return this.renderColorizedError(formatted, originalError);
  }

  private renderPlainError(formatted: FormattedError): string {
    const parts: string[] = [];

    // 添加时间戳
    if (formatted.timestamp) {
      parts.push(`[${formatted.timestamp}]`);
    }

    // 添加级别
    parts.push(`[${formatted.level.toUpperCase()}]`);

    // 添加上下文
    if (formatted.context) {
      parts.push(`[${formatted.context}]`);
    }

    // 添加消息
    parts.push(formatted.message);

    return parts.join(' ');
  }

  private renderColorizedError(formatted: FormattedError, originalError: Error): string {
    const parts: string[] = [];

    // 添加时间戳
    if (formatted.timestamp) {
      parts.push(chalk.gray(`[${formatted.timestamp}]`));
    }

    // 添加级别（带颜色）
    const levelText = `[${formatted.level.toUpperCase()}]`;
    switch (formatted.level) {
      case 'error':
        parts.push(chalk.red.bold(levelText));
        break;
      case 'warning':
        parts.push(chalk.yellow.bold(levelText));
        break;
      case 'info':
        parts.push(chalk.blue.bold(levelText));
        break;
    }

    // 添加上下文
    if (formatted.context) {
      parts.push(chalk.cyan(`[${formatted.context}]`));
    }

    // 添加消息（根据级别着色）
    let colorizedMessage: string;
    switch (formatted.level) {
      case 'error':
        colorizedMessage = chalk.red(formatted.message);
        break;
      case 'warning':
        colorizedMessage = chalk.yellow(formatted.message);
        break;
      case 'info':
        colorizedMessage = chalk.blue(formatted.message);
        break;
    }

    parts.push(colorizedMessage);

    return parts.join(' ');
  }

  private formatStackTrace(stack: string): string {
    if (!this.options.colorize) {
      return stack;
    }

    const lines = stack.split('\n');
    const formattedLines = lines.map((line, index) => {
      if (index === 0) {
        // 错误消息行
        return chalk.red.bold(line);
      } else if (line.trim().startsWith('at ')) {
        // 堆栈帧
        return chalk.gray(line);
      } else {
        return line;
      }
    });

    return formattedLines.join('\n');
  }
}

// 单例实例
let instance: ErrorOutputFormatter | null = null;

export function getErrorOutputFormatter(options?: ErrorOutputOptions): ErrorOutputFormatter {
  if (!instance) {
    instance = new ErrorOutputFormatter(options);
  }
  return instance;
}