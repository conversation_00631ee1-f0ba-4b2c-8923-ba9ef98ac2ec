import { promises as fs } from 'fs';
import semver from 'semver';
import { codeFrameColumns } from '@babel/code-frame';
import chalk from 'chalk';
import { getErrorRecoveryService } from './error-recovery-service';
import { getCodeFrameGenerator } from './code-frame-generator';
import { CodeFrameError, ValidationError } from '../core/errors';

export interface CompatibilityCheckResult {
  compatible: boolean;
  version?: string;
  warnings: string[];
  errors: string[];
  features: {
    codeFrame: boolean;
    colorSupport: boolean;
    errorRecovery: boolean;
  };
}

export interface DegradeOptions {
  enableFallback: boolean;
  useBasicOutput: boolean;
  skipCodeFrames: boolean;
  disableColors: boolean;
}

/**
 * 兼容性服务
 * 提供版本检查、降级输出和错误恢复保证机制
 */
export class CompatibilityService {
  private checkResults?: CompatibilityCheckResult;
  private degradeOptions: DegradeOptions = {
    enableFallback: true,
    useBasicOutput: false,
    skipCodeFrames: false,
    disableColors: false
  };

  /**
   * 检查@babel/code-frame版本兼容性
   */
  async checkBabelCodeFrameCompatibility(): Promise<CompatibilityCheckResult> {
    const result: CompatibilityCheckResult = {
      compatible: false,
      warnings: [],
      errors: [],
      features: {
        codeFrame: false,
        colorSupport: false,
        errorRecovery: false
      }
    };

    try {
      // 读取package.json获取依赖版本
      const packageJsonPath = require.resolve('@babel/code-frame/package.json');
      const packageData = JSON.parse(await fs.readFile(packageJsonPath, 'utf-8'));
      const installedVersion = packageData.version;
      
      result.version = installedVersion;

      // 检查版本兼容性 (支持 >= 7.0.0)
      const minVersion = '7.0.0';
      if (semver.gte(installedVersion, minVersion)) {
        result.compatible = true;
        result.features.codeFrame = true;
        
        // 测试基本功能
        await this.testCodeFrameBasicFeatures(result);
        
        // 测试颜色支持
        this.testColorSupport(result);
        
        // 测试错误恢复
        this.testErrorRecovery(result);
        
      } else {
        result.errors.push(`@babel/code-frame 版本 ${installedVersion} 过低，需要 >= ${minVersion}`);
        result.warnings.push('建议升级到最新版本以获得更好的性能和功能支持');
      }

    } catch (error) {
      result.errors.push(`无法检查 @babel/code-frame 版本: ${error instanceof Error ? error.message : String(error)}`);
      result.warnings.push('将使用降级输出模式');
      this.degradeOptions.skipCodeFrames = true;
    }

    this.checkResults = result;
    return result;
  }

  /**
   * 测试代码框架基本功能
   */
  private async testCodeFrameBasicFeatures(result: CompatibilityCheckResult): Promise<void> {
    try {
      const testCode = 'function test() {\n  console.log("hello");\n}';
      const frame = codeFrameColumns(testCode, { start: { line: 2, column: 3 } });
      
      if (frame && frame.length > 0) {
        result.features.codeFrame = true;
      } else {
        result.warnings.push('代码框架生成功能可能不完整');
      }
    } catch (error) {
      result.warnings.push(`代码框架基本功能测试失败: ${error instanceof Error ? error.message : String(error)}`);
      result.features.codeFrame = false;
    }
  }

  /**
   * 测试颜色支持
   */
  private testColorSupport(result: CompatibilityCheckResult): void {
    try {
      // 检查终端颜色支持
      const hasColorSupport = process.stdout.isTTY && process.env.NO_COLOR !== '1';
      
      if (hasColorSupport) {
        // 测试chalk功能
        const testColorOutput = chalk.red('test');
        if (testColorOutput.includes('\u001b[')) {
          result.features.colorSupport = true;
        }
      }
      
      if (!result.features.colorSupport) {
        result.warnings.push('颜色输出可能不被支持，将使用纯文本模式');
        this.degradeOptions.disableColors = true;
      }
    } catch (error) {
      result.warnings.push(`颜色支持检测失败: ${error instanceof Error ? error.message : String(error)}`);
      result.features.colorSupport = false;
    }
  }

  /**
   * 测试错误恢复功能
   */
  private testErrorRecovery(result: CompatibilityCheckResult): void {
    try {
      const errorRecoveryService = getErrorRecoveryService();
      if (errorRecoveryService) {
        result.features.errorRecovery = true;
      }
    } catch (error) {
      result.warnings.push(`错误恢复功能测试失败: ${error instanceof Error ? error.message : String(error)}`);
      result.features.errorRecovery = false;
    }
  }

  /**
   * 生成兼容的代码框架
   */
  async generateCompatibleCodeFrame(
    filePath: string,
    line: number,
    column: number,
    options: { highlightCode?: boolean; forceColor?: boolean } = {}
  ): Promise<string> {
    // 如果兼容性检查失败或配置跳过代码框架，返回降级输出
    if (!this.checkResults?.features.codeFrame || this.degradeOptions.skipCodeFrames) {
      return this.generateDegradedFrame(filePath, line, column);
    }

    try {
      const codeFrameGenerator = getCodeFrameGenerator();
      const frameResult = await codeFrameGenerator.generateFrame(filePath, line, column, {
        highlightCode: options.highlightCode ?? true,
        forceColor: options.forceColor && this.checkResults.features.colorSupport,
        linesAbove: 2,
        linesBelow: 2
      });

      if (frameResult.success) {
        return frameResult.frame;
      } else {
        // 如果生成失败，返回降级输出
        return this.generateDegradedFrame(filePath, line, column, frameResult.error);
      }
    } catch (error) {
      // 捕获任何未预期的错误
      return this.generateDegradedFrame(filePath, line, column, error instanceof Error ? error.message : String(error));
    }
  }

  /**
   * 生成降级代码框架
   */
  private generateDegradedFrame(filePath: string, line: number, column: number, errorMessage?: string): string {
    const relativePath = filePath.replace(process.cwd(), '.');
    const colorize = this.checkResults?.features.colorSupport && !this.degradeOptions.disableColors;
    
    const lines = [
      `  ${line} | [代码上下文不可用]`,
      `      | 文件: ${relativePath}`,
      `      | 位置: 第 ${line} 行，第 ${column} 列`
    ];

    if (errorMessage) {
      lines.push(`      | 错误: ${errorMessage}`);
    }

    if (colorize) {
      return lines.map(line => {
        if (line.includes('代码上下文不可用')) {
          return chalk.yellow(line);
        } else if (line.includes('错误:')) {
          return chalk.red(line);
        }
        return chalk.gray(line);
      }).join('\n');
    }

    return lines.join('\n');
  }

  /**
   * 验证现有--details命令兼容性
   */
  validateDetailsCommandCompatibility(options: any): { compatible: boolean; issues: string[] } {
    const issues: string[] = [];
    let compatible = true;

    // 检查是否存在已知的不兼容选项
    if (options.showContext && !options.details) {
      issues.push('--show-context 参数必须与 --details 一起使用');
      compatible = false;
    }

    if (options.showAllContext && !options.details) {
      issues.push('--show-all-context 参数必须与 --details 一起使用');
      compatible = false;
    }

    // 检查参数冲突
    if (options.showContext && options.showAllContext) {
      issues.push('--show-context 和 --show-all-context 不能同时使用');
      compatible = false;
    }

    // 检查过滤器参数
    if (options.maxContextItems !== undefined && !options.details) {
      issues.push('--max-context-items 参数必须与 --details 一起使用');
      compatible = false;
    }

    if (options.minComplexityIncrement !== undefined && !options.details) {
      issues.push('--min-complexity-increment 参数必须与 --details 一起使用');
      compatible = false;
    }

    return { compatible, issues };
  }

  /**
   * 应用兼容性设置
   */
  applyCompatibilitySettings(options: any): any {
    const compatibleOptions = { ...options };

    // 如果代码框架不可用，禁用相关功能
    if (!this.checkResults?.features.codeFrame) {
      if (compatibleOptions.showContext) {
        console.warn('警告: 代码框架功能不可用，--show-context 将被忽略');
        compatibleOptions.showContext = false;
      }
      if (compatibleOptions.showAllContext) {
        console.warn('警告: 代码框架功能不可用，--show-all-context 将被忽略');
        compatibleOptions.showAllContext = false;
      }
    }

    // 如果颜色不支持，禁用颜色输出
    if (!this.checkResults?.features.colorSupport) {
      compatibleOptions.noColors = true;
    }

    return compatibleOptions;
  }

  /**
   * 获取兼容性报告
   */
  getCompatibilityReport(): string {
    if (!this.checkResults) {
      return '兼容性检查尚未执行';
    }

    const lines = [];
    lines.push('=== 兼容性检查报告 ===');
    
    if (this.checkResults.version) {
      lines.push(`@babel/code-frame 版本: ${this.checkResults.version}`);
    }
    
    lines.push(`整体兼容性: ${this.checkResults.compatible ? '✓ 兼容' : '✗ 不兼容'}`);
    
    lines.push('\n功能支持:');
    lines.push(`  代码框架生成: ${this.checkResults.features.codeFrame ? '✓' : '✗'}`);
    lines.push(`  颜色输出: ${this.checkResults.features.colorSupport ? '✓' : '✗'}`);
    lines.push(`  错误恢复: ${this.checkResults.features.errorRecovery ? '✓' : '✗'}`);

    if (this.checkResults.warnings.length > 0) {
      lines.push('\n警告:');
      this.checkResults.warnings.forEach(warning => {
        lines.push(`  ⚠ ${warning}`);
      });
    }

    if (this.checkResults.errors.length > 0) {
      lines.push('\n错误:');
      this.checkResults.errors.forEach(error => {
        lines.push(`  ✗ ${error}`);
      });
    }

    return lines.join('\n');
  }

  /**
   * 获取降级选项
   */
  getDegradeOptions(): DegradeOptions {
    return { ...this.degradeOptions };
  }

  /**
   * 重置兼容性状态
   */
  reset(): void {
    this.checkResults = undefined;
    this.degradeOptions = {
      enableFallback: true,
      useBasicOutput: false,
      skipCodeFrames: false,
      disableColors: false
    };
  }
}

// 单例实例
let instance: CompatibilityService | null = null;

export function getCompatibilityService(): CompatibilityService {
  if (!instance) {
    instance = new CompatibilityService();
  }
  return instance;
}