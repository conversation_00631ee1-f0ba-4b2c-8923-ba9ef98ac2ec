/**
 * Type guards and utility functions for TypeScript type safety enhancement
 * 
 * This module provides comprehensive type guard functions and conditional type utilities
 * to improve type safety throughout the cognitive complexity analysis tool.
 * 
 * Requirements: 4.1, 4.2, 4.3
 */

/**
 * Type guard that narrows T | undefined to T
 * @param value - Value to check for undefined
 * @returns true if value is not undefined
 */
export function isNotUndefined<T>(value: T | undefined): value is T {
  return value !== undefined;
}

/**
 * Type guard that narrows T | null to T
 * @param value - Value to check for null
 * @returns true if value is not null
 */
export function isNotNull<T>(value: T | null): value is T {
  return value !== null;
}

/**
 * Type guard that narrows T | null | undefined to T
 * @param value - Value to check for null or undefined
 * @returns true if value is neither null nor undefined
 */
export function isNotNullish<T>(value: T | null | undefined): value is T {
  return value != null;
}

/**
 * Type guard for checking if a value is a string
 * @param value - Value to check
 * @returns true if value is a string
 */
export function isString(value: unknown): value is string {
  return typeof value === 'string';
}

/**
 * Type guard for checking if a value is a number
 * @param value - Value to check
 * @returns true if value is a number and not NaN
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * Type guard for checking if a value is a boolean
 * @param value - Value to check
 * @returns true if value is a boolean
 */
export function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

/**
 * Type guard for checking if a value is an object (not null, not array)
 * @param value - Value to check
 * @returns true if value is a plain object
 */
export function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

/**
 * Type guard for checking if a value is an array
 * @param value - Value to check
 * @returns true if value is an array
 */
export function isArray(value: unknown): value is unknown[] {
  return Array.isArray(value);
}

/**
 * Type guard for AST node with operator property
 * @param node - Node to check
 * @returns true if node has operator property
 */
export function hasOperator(node: unknown): node is { operator: string } {
  return isObject(node) && isString((node as any).operator);
}

/**
 * Type guard for AST node with left and right properties
 * @param node - Node to check
 * @returns true if node has left and right properties
 */
export function hasBinaryProperties(node: unknown): node is { left: unknown; right: unknown } {
  return isObject(node) && 'left' in node && 'right' in node;
}

/**
 * Type guard for AST node with type property
 * @param node - Node to check
 * @returns true if node has type property
 */
export function hasNodeType(node: unknown): node is { type: string } {
  return isObject(node) && isString((node as any).type);
}

/**
 * Type guard for AST node with span property
 * @param node - Node to check
 * @returns true if node has span property with line and column info
 */
export function hasSpanInfo(node: unknown): node is { 
  span: { 
    start: { line: number; column: number }; 
    end: { line: number; column: number } 
  } 
} {
  return isObject(node) && 
         isObject((node as any).span) && 
         isObject((node as any).span.start) &&
         isObject((node as any).span.end) &&
         isNumber((node as any).span.start.line) &&
         isNumber((node as any).span.start.column) &&
         isNumber((node as any).span.end.line) &&
         isNumber((node as any).span.end.column);
}

/**
 * Type guard for AST node with simplified span property
 * @param node - Node to check
 * @returns true if node has basic span property
 */
export function hasSpan(node: unknown): node is { span: unknown } {
  return isObject(node) && 'span' in node;
}

/**
 * Type guard for binary expression node
 * @param node - Node to check
 * @returns true if node is a binary expression with operator
 */
export function isBinaryExpression(node: unknown): node is { 
  type: 'BinaryExpression'; 
  operator: string; 
  left: unknown; 
  right: unknown 
} {
  return hasNodeType(node) && 
         node.type === 'BinaryExpression' &&
         hasOperator(node) &&
         hasBinaryProperties(node);
}

/**
 * Type guard for logical binary expression
 * @param node - Node to check
 * @returns true if node is a logical binary expression
 */
export function isLogicalBinaryExpression(node: unknown): node is { 
  type: 'BinaryExpression'; 
  operator: '&&' | '||' | '??'; 
  left: unknown; 
  right: unknown 
} {
  return isBinaryExpression(node) && 
         ['&&', '||', '??'].includes(node.operator);
}

/**
 * Type guard for call expression node
 * @param node - Node to check
 * @returns true if node is a call expression
 */
export function isCallExpression(node: unknown): node is { 
  type: 'CallExpression'; 
  callee: unknown; 
  arguments: unknown[] 
} {
  return hasNodeType(node) && 
         node.type === 'CallExpression' &&
         hasProperty(node, 'callee') &&
         hasProperty(node, 'arguments') &&
         isArray((node as any).arguments);
}

/**
 * Type guard for checking if a value is a function
 * @param value - Value to check
 * @returns true if value is a function
 */
export function isFunction(value: unknown): value is Function {
  return typeof value === 'function';
}

/**
 * Type guard for checking if a value has a specific property
 * @param obj - Object to check
 * @param prop - Property name to check for
 * @returns true if object has the property
 */
export function hasProperty<K extends PropertyKey>(
  obj: unknown,
  prop: K
): obj is Record<K, unknown> {
  return isObject(obj) && prop in obj;
}

/**
 * Type guard for checking if a value has a specific property with a specific type
 * @param obj - Object to check
 * @param prop - Property name to check for
 * @param typeGuard - Type guard function for the property value
 * @returns true if object has the property with the correct type
 */
export function hasTypedProperty<K extends PropertyKey, V>(
  obj: unknown,
  prop: K,
  typeGuard: (value: unknown) => value is V
): obj is Record<K, V> {
  return hasProperty(obj, prop) && typeGuard(obj[prop]);
}

/**
 * Type guard for checking if an error is an instance of Error
 * @param error - Error to check
 * @returns true if error is an Error instance
 */
export function isError(error: unknown): error is Error {
  return error instanceof Error;
}

/**
 * Type guard for checking if a value is a valid array index
 * @param value - Value to check
 * @param arrayLength - Length of the array to check against
 * @returns true if value is a valid array index
 */
export function isValidArrayIndex(value: unknown, arrayLength: number): value is number {
  return isNumber(value) && 
         Number.isInteger(value) && 
         value >= 0 && 
         value < arrayLength;
}

// Conditional type utilities

/**
 * Utility type that excludes undefined from T
 */
export type NonUndefined<T> = T extends undefined ? never : T;

/**
 * Utility type that excludes null from T
 */
export type NonNull<T> = T extends null ? never : T;

/**
 * Utility type that excludes both null and undefined from T
 */
export type NonNullish<T> = T extends null | undefined ? never : T;

/**
 * Utility type for extracting the value type from a Promise
 */
export type PromiseValue<T> = T extends Promise<infer U> ? U : T;

/**
 * Utility type for making specific properties required
 */
export type RequiredProperties<T, K extends keyof T> = T & Required<Pick<T, K>>;

/**
 * Utility type for making specific properties optional
 */
export type OptionalProperties<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * Utility type for deep readonly
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends (infer U)[]
    ? readonly DeepReadonly<U>[]
    : T[P] extends Record<string, any>
    ? DeepReadonly<T[P]>
    : T[P];
};

/**
 * Utility type for extracting function return type
 */
export type ReturnTypeOf<T> = T extends (...args: any[]) => infer R ? R : never;

/**
 * Utility type for extracting function parameter types
 */
export type ParametersOf<T> = T extends (...args: infer P) => any ? P : never;