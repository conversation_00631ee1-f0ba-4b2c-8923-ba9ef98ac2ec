/**
 * Safe operation utilities for TypeScript type safety enhancement
 * 
 * This module provides safe operation functions that handle null/undefined values
 * gracefully and provide type-safe alternatives to common operations.
 * 
 * Requirements: 4.1, 4.2, 4.3
 */

import { isNotNullish, isNotUndefined, type NonNullish, isObject, isArray, isFunction } from './type-guards';

/**
 * Safely access a property from an object that might be null or undefined
 * @param obj - Object to access property from
 * @param key - Property key to access
 * @returns The property value or undefined if object is nullish
 */
export function safeGet<T, K extends keyof T>(
  obj: T | undefined | null,
  key: K
): T[K] | undefined {
  return obj?.[key];
}

/**
 * Safely access nested properties using a chain of keys
 * @param obj - Object to access properties from
 * @param keys - Array of property keys to access in sequence
 * @returns The final property value or undefined if any intermediate value is nullish
 */
export function safeGetNested<T>(
  obj: T | undefined | null,
  keys: (string | number)[]
): any {
  if (!isNotNullish(obj) || keys.length === 0) {
    return undefined;
  }

  let current: any = obj;
  for (const key of keys) {
    if (!isNotNullish(current) || (typeof current !== 'object')) {
      return undefined;
    }
    current = current[key];
  }
  
  return current;
}

/**
 * Safely execute an accessor function on an object that might be null or undefined
 * @param obj - Object to access
 * @param accessor - Function to extract value from object
 * @returns Result of accessor function or undefined if object is nullish or accessor throws
 */
export function safeAccess<T, R>(
  obj: T | undefined | null,
  accessor: (obj: T) => R
): R | undefined {
  if (isNotNullish(obj)) {
    try {
      return accessor(obj);
    } catch {
      return undefined;
    }
  }
  return undefined;
}

/**
 * Provide a default value when the original value is null or undefined
 * @param value - Original value that might be nullish
 * @param defaultValue - Default value to use if original is nullish
 * @returns The original value if not nullish, otherwise the default value
 */
export function withDefault<T>(
  value: T | undefined | null,
  defaultValue: T
): T {
  return isNotNullish(value) ? value : defaultValue;
}

/**
 * Provide a lazily evaluated default value when the original value is null or undefined
 * @param value - Original value that might be nullish
 * @param defaultFactory - Function that produces the default value
 * @returns The original value if not nullish, otherwise the result of defaultFactory
 */
export function withLazyDefault<T>(
  value: T | undefined | null,
  defaultFactory: () => T
): T {
  return isNotNullish(value) ? value : defaultFactory();
}

/**
 * Safely convert a value to string, handling null and undefined
 * @param value - Value to convert to string
 * @param defaultValue - Default string to use if value is nullish
 * @returns String representation or default value
 */
export function safeString(value: unknown, defaultValue: string = ''): string {
  if (isNotNullish(value)) {
    try {
      return String(value);
    } catch {
      return defaultValue;
    }
  }
  return defaultValue;
}

/**
 * Safely convert a value to number, handling null and undefined
 * @param value - Value to convert to number
 * @param defaultValue - Default number to use if value is nullish or NaN
 * @returns Number representation or default value
 */
export function safeNumber(value: unknown, defaultValue: number = 0): number {
  if (isNotNullish(value)) {
    const num = Number(value);
    return isNaN(num) ? defaultValue : num;
  }
  return defaultValue;
}

/**
 * Safely convert a value to integer, handling null and undefined
 * @param value - Value to convert to integer
 * @param defaultValue - Default integer to use if value is nullish or NaN
 * @returns Integer representation or default value
 */
export function safeInteger(value: unknown, defaultValue: number = 0): number {
  if (isNotNullish(value)) {
    const num = parseInt(String(value), 10);
    return isNaN(num) ? defaultValue : num;
  }
  return defaultValue;
}

/**
 * Safely access array element by index
 * @param array - Array to access
 * @param index - Index to access
 * @returns Array element or undefined if index is out of bounds or array is nullish
 */
export function safeArrayGet<T>(
  array: T[] | undefined | null,
  index: number
): T | undefined {
  if (isArray(array) && index >= 0 && index < array.length) {
    return array[index];
  }
  return undefined;
}

/**
 * Safely get first element of an array
 * @param array - Array to get first element from
 * @returns First element or undefined if array is empty or nullish
 */
export function safeFirst<T>(array: T[] | undefined | null): T | undefined {
  return safeArrayGet(array, 0);
}

/**
 * Safely get last element of an array
 * @param array - Array to get last element from
 * @returns Last element or undefined if array is empty or nullish
 */
export function safeLast<T>(array: T[] | undefined | null): T | undefined {
  if (isArray(array) && array.length > 0) {
    return array[array.length - 1];
  }
  return undefined;
}

/**
 * Safely call a function with error handling
 * @param fn - Function to call
 * @param args - Arguments to pass to function
 * @returns Result of function call or undefined if function throws or is nullish
 */
export function safeCall<T extends any[], R>(
  fn: ((...args: T) => R) | undefined | null,
  ...args: T
): R | undefined {
  if (isFunction(fn)) {
    try {
      return fn(...args);
    } catch {
      return undefined;
    }
  }
  return undefined;
}

/**
 * Safely call an async function with error handling
 * @param fn - Async function to call
 * @param args - Arguments to pass to function
 * @returns Promise that resolves to result or undefined if function throws or is nullish
 */
export async function safeCallAsync<T extends any[], R>(
  fn: ((...args: T) => Promise<R>) | undefined | null,
  ...args: T
): Promise<R | undefined> {
  if (isFunction(fn)) {
    try {
      return await fn(...args);
    } catch {
      return undefined;
    }
  }
  return undefined;
}

/**
 * Safely parse JSON with error handling
 * @param jsonString - JSON string to parse
 * @param defaultValue - Default value to return if parsing fails
 * @returns Parsed JSON object or default value
 */
export function safeJSONParse<T = any>(
  jsonString: string | undefined | null,
  defaultValue: T
): T {
  if (isNotNullish(jsonString) && typeof jsonString === 'string') {
    try {
      return JSON.parse(jsonString);
    } catch {
      return defaultValue;
    }
  }
  return defaultValue;
}

/**
 * Safely parse JSON with null as default value
 * @param jsonString - JSON string to parse
 * @returns Parsed JSON object or null if parsing fails
 */
export function safeParse(jsonString: string | undefined | null): any {
  return safeJSONParse(jsonString, null);
}

/**
 * Safely stringify object to JSON with error handling
 * @param obj - Object to stringify
 * @param defaultValue - Default string to return if stringification fails
 * @returns JSON string or default value
 */
export function safeJSONStringify(
  obj: any,
  defaultValue: string = '{}'
): string {
  try {
    return JSON.stringify(obj);
  } catch {
    return defaultValue;
  }
}

/**
 * Safely stringify object to JSON with null as default value
 * @param obj - Object to stringify
 * @returns JSON string or 'null' if stringification fails
 */
export function safeStringify(obj: any): string {
  try {
    const result = JSON.stringify(obj);
    return result !== undefined ? result : 'null';
  } catch {
    return 'null';
  }
}

/**
 * Safely filter an array removing null and undefined values
 * @param array - Array to filter
 * @returns New array with non-nullish values
 */
export function filterNullish<T>(array: (T | null | undefined)[]): T[] {
  return array.filter(isNotNullish);
}

/**
 * Safely map an array with a function that might return null/undefined
 * @param array - Array to map
 * @param mapper - Mapping function
 * @returns New array with non-nullish mapped values
 */
export function safeMap<T, R>(
  array: T[] | undefined | null,
  mapper: (item: T, index: number) => R | null | undefined
): R[] {
  if (!isArray(array)) {
    return [];
  }
  
  const results: R[] = [];
  for (let i = 0; i < array.length; i++) {
    const item = array[i];
    if (item === undefined) {
      continue; // 跳过undefined元素
    }
    const mapped = mapper(item, i);
    if (isNotNullish(mapped)) {
      results.push(mapped);
    }
  }
  
  return results;
}

/**
 * Safely find first element in array that matches predicate
 * @param array - Array to search
 * @param predicate - Predicate function
 * @returns First matching element or undefined
 */
export function safeFind<T>(
  array: T[] | undefined | null,
  predicate: (item: T, index: number) => boolean
): T | undefined {
  if (!isArray(array)) {
    return undefined;
  }
  
  for (let i = 0; i < array.length; i++) {
    try {
      const item = array[i];
      if (isNotUndefined(item)) {
        const result = predicate(item, i);
        if (result === true) {
          return item;
        }
      }
    } catch {
      // Continue searching if predicate throws
    }
  }
  
  return undefined;
}

/**
 * Create a safe version of an object with all methods wrapped in error handling
 * @param obj - Object to make safe
 * @returns Proxy object with safe method calls
 */
export function makeSafe<T extends Record<string, any>>(obj: T): T {
  return new Proxy(obj, {
    get(target, prop, receiver) {
      const value = Reflect.get(target, prop, receiver);
      
      if (isFunction(value)) {
        return function(this: any, ...args: any[]) {
          try {
            const result = (value as any).call(this, ...args);
            return isNotUndefined(result) ? result : undefined;
          } catch {
            return undefined;
          }
        };
      }
      
      return value;
    }
  });
}

/**
 * Utility to create a type-safe object merger
 * @param defaults - Default values object
 * @param overrides - Override values object
 * @returns Merged object with type safety
 */
export function safeMerge<T extends Record<string, any>>(
  defaults: T,
  overrides: Partial<T> | undefined | null
): T {
  if (!isObject(defaults)) {
    throw new TypeError('Defaults must be an object');
  }
  
  if (!isNotNullish(overrides) || !isObject(overrides)) {
    return { ...defaults };
  }
  
  const result = { ...defaults };
  
  for (const key in overrides) {
    if (overrides.hasOwnProperty(key)) {
      const value = overrides[key];
      if (isNotUndefined(value)) {
        (result as any)[key] = value;
      }
    }
  }
  
  return result;
}