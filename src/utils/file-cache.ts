/**
 * 文件内容缓存器
 * 提供智能文件内容缓存，支持内存限制、TTL和统计信息
 */

import { promises as fs } from 'fs';
import { createHash } from 'crypto';

export interface FileCacheOptions {
  maxSize?: number;
  maxMemory?: number; // 字节
  defaultTTL?: number; // 毫秒
  cleanupInterval?: number; // 毫秒
  enableCompression?: boolean;
  enableStatistics?: boolean;
}

export interface FileCacheEntry {
  content: string;
  timestamp: number;
  lastAccessed: number;
  accessCount: number;
  size: number;
  hash: string;
  ttl?: number;
}

export interface FileCacheStatistics {
  totalFiles: number;
  totalSize: number;
  memoryUsage: number;
  hitCount: number;
  missCount: number;
  hitRate: number;
  averageFileSize: number;
  cacheUtilization: number;
  oldestEntry: number;
  newestEntry: number;
}

/**
 * 高性能文件内容缓存器
 * 支持智能内存管理、TTL过期和统计监控
 */
export class FileContentCache {
  private cache = new Map<string, FileCacheEntry>();
  private accessOrder: string[] = [];
  private totalMemoryUsage = 0;
  private hitCount = 0;
  private missCount = 0;
  private cleanupTimer?: NodeJS.Timeout;
  
  private readonly maxSize: number;
  private readonly maxMemory: number;
  private readonly defaultTTL: number;
  private readonly cleanupInterval: number;
  private readonly enableCompression: boolean;
  private readonly enableStatistics: boolean;

  constructor(options: FileCacheOptions = {}) {
    this.maxSize = options.maxSize ?? 1000;
    this.maxMemory = options.maxMemory ?? 100 * 1024 * 1024; // 100MB 默认
    this.defaultTTL = options.defaultTTL ?? 30 * 60 * 1000; // 30分钟默认
    this.cleanupInterval = options.cleanupInterval ?? 5 * 60 * 1000; // 5分钟清理间隔
    this.enableCompression = options.enableCompression ?? false;
    this.enableStatistics = options.enableStatistics ?? true;
    
    // 启动定期清理
    if (this.cleanupInterval > 0) {
      this.cleanupTimer = setInterval(() => {
        this.performCleanup();
      }, this.cleanupInterval);
      
      // 使用 unref() 防止定时器阻止进程退出
      // 这样当主任务完成时进程可以正常退出，但定时器在进程活跃时仍会正常工作
      this.cleanupTimer.unref();
    }
  }

  /**
   * 获取文件内容
   * @param filePath 文件路径
   * @param ttl 可选的TTL覆盖
   */
  async getFileContent(filePath: string, ttl?: number): Promise<string> {
    const now = Date.now();
    
    // 检查缓存
    const cached = this.cache.get(filePath);
    if (cached && !this.isExpired(cached, now)) {
      // 更新访问统计
      cached.lastAccessed = now;
      cached.accessCount++;
      this.updateAccessOrder(filePath);
      
      if (this.enableStatistics) {
        this.hitCount++;
      }
      
      return cached.content;
    }

    // 缓存未命中，读取文件
    if (this.enableStatistics) {
      this.missCount++;
    }

    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const size = Buffer.byteLength(content, 'utf-8');
      const hash = this.generateContentHash(content);
      
      // 检查是否需要淘汰旧条目
      await this.ensureCapacity(size);
      
      const entry: FileCacheEntry = {
        content,
        timestamp: now,
        lastAccessed: now,
        accessCount: 1,
        size,
        hash,
        ttl: ttl || this.defaultTTL,
      };
      
      this.cache.set(filePath, entry);
      this.totalMemoryUsage += size;
      this.accessOrder.push(filePath);
      
      return content;
      
    } catch (error) {
      // 文件读取失败，如果有过期的缓存条目，返回它
      if (cached) {
        cached.lastAccessed = now;
        cached.accessCount++;
        return cached.content;
      }
      throw error;
    }
  }

  /**
   * 检查文件是否已缓存且未过期
   */
  hasValidCache(filePath: string): boolean {
    const cached = this.cache.get(filePath);
    return cached ? !this.isExpired(cached) : false;
  }

  /**
   * 预加载文件到缓存
   */
  async preloadFile(filePath: string, ttl?: number): Promise<void> {
    await this.getFileContent(filePath, ttl);
  }

  /**
   * 批量预加载文件
   */
  async preloadFiles(filePaths: string[], concurrency = 10): Promise<void> {
    const chunks = this.chunkArray(filePaths, concurrency);
    
    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(filePath => this.preloadFile(filePath))
      );
    }
  }

  /**
   * 手动设置文件内容到缓存
   */
  setFileContent(filePath: string, content: string, ttl?: number): void {
    const now = Date.now();
    const size = Buffer.byteLength(content, 'utf-8');
    const hash = this.generateContentHash(content);
    
    // 移除旧条目（如果存在）
    const oldEntry = this.cache.get(filePath);
    if (oldEntry) {
      this.totalMemoryUsage -= oldEntry.size;
      this.removeFromAccessOrder(filePath);
    }
    
    const entry: FileCacheEntry = {
      content,
      timestamp: now,
      lastAccessed: now,
      accessCount: 1,
      size,
      hash,
      ttl: ttl || this.defaultTTL,
    };
    
    this.cache.set(filePath, entry);
    this.totalMemoryUsage += size;
    this.accessOrder.push(filePath);
  }

  /**
   * 删除缓存条目
   */
  removeFromCache(filePath: string): boolean {
    const entry = this.cache.get(filePath);
    if (entry) {
      this.cache.delete(filePath);
      this.totalMemoryUsage -= entry.size;
      this.removeFromAccessOrder(filePath);
      return true;
    }
    return false;
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.accessOrder = [];
    this.totalMemoryUsage = 0;
    this.hitCount = 0;
    this.missCount = 0;
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): FileCacheStatistics {
    const entries = Array.from(this.cache.values());
    const now = Date.now();
    
    return {
      totalFiles: this.cache.size,
      totalSize: this.totalMemoryUsage,
      memoryUsage: this.totalMemoryUsage,
      hitCount: this.hitCount,
      missCount: this.missCount,
      hitRate: this.getTotalRequests() > 0 ? this.hitCount / this.getTotalRequests() : 0,
      averageFileSize: entries.length > 0 ? this.totalMemoryUsage / entries.length : 0,
      cacheUtilization: this.maxMemory > 0 ? this.totalMemoryUsage / this.maxMemory : 0,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : now,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : now,
    };
  }

  /**
   * 获取详细的缓存信息
   */
  getCacheDetails(): {
    entries: Array<{
      filePath: string;
      size: number;
      accessCount: number;
      lastAccessed: number;
      age: number;
      isExpired: boolean;
    }>;
    memoryDistribution: {
      small: number; // < 10KB
      medium: number; // 10KB - 100KB  
      large: number; // > 100KB
    };
  } {
    const now = Date.now();
    const entries = [];
    const memoryDistribution = { small: 0, medium: 0, large: 0 };
    
    for (const [filePath, entry] of this.cache.entries()) {
      entries.push({
        filePath,
        size: entry.size,
        accessCount: entry.accessCount,
        lastAccessed: entry.lastAccessed,
        age: now - entry.timestamp,
        isExpired: this.isExpired(entry, now),
      });
      
      // 统计内存分布
      if (entry.size < 10 * 1024) {
        memoryDistribution.small++;
      } else if (entry.size < 100 * 1024) {
        memoryDistribution.medium++;
      } else {
        memoryDistribution.large++;
      }
    }
    
    // 按访问时间排序
    entries.sort((a, b) => b.lastAccessed - a.lastAccessed);
    
    return { entries, memoryDistribution };
  }

  /**
   * 执行缓存优化
   */
  async optimizeCache(): Promise<{
    removedExpired: number;
    removedLRU: number;
    memoryFreed: number;
  }> {
    const startMemory = this.totalMemoryUsage;
    let removedExpired = 0;
    let removedLRU = 0;
    
    // 1. 清理过期条目
    removedExpired = this.removeExpiredEntries();
    
    // 2. 如果内存使用仍然过高，执行LRU淘汰
    while (this.totalMemoryUsage > this.maxMemory * 0.8 && this.cache.size > 0) {
      if (this.evictLRU()) {
        removedLRU++;
      } else {
        break;
      }
    }
    
    const memoryFreed = startMemory - this.totalMemoryUsage;
    
    return {
      removedExpired,
      removedLRU,
      memoryFreed,
    };
  }

  /**
   * 销毁缓存器，清理资源
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.clearCache();
  }

  // 私有方法

  private isExpired(entry: FileCacheEntry, now: number = Date.now()): boolean {
    return entry.ttl ? (now - entry.timestamp) > entry.ttl : false;
  }

  private updateAccessOrder(filePath: string): void {
    this.removeFromAccessOrder(filePath);
    this.accessOrder.push(filePath);
  }

  private removeFromAccessOrder(filePath: string): void {
    const index = this.accessOrder.indexOf(filePath);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
  }

  private async ensureCapacity(newEntrySize: number): Promise<void> {
    // 检查条目数量限制
    while (this.cache.size >= this.maxSize) {
      if (!this.evictLRU()) break;
    }
    
    // 检查内存限制
    while (this.totalMemoryUsage + newEntrySize > this.maxMemory && this.cache.size > 0) {
      if (!this.evictLRU()) break;
    }
    
    // 清理过期条目
    this.removeExpiredEntries();
  }

  private evictLRU(): boolean {
    if (this.accessOrder.length === 0) return false;
    
    const lruPath = this.accessOrder.shift()!;
    return this.removeFromCache(lruPath);
  }

  private removeExpiredEntries(): number {
    const now = Date.now();
    let removed = 0;
    
    for (const [filePath, entry] of this.cache.entries()) {
      if (this.isExpired(entry, now)) {
        this.removeFromCache(filePath);
        removed++;
      }
    }
    
    return removed;
  }

  private performCleanup(): void {
    this.removeExpiredEntries();
    
    // 如果内存使用过高，执行激进清理
    if (this.totalMemoryUsage > this.maxMemory * 0.9) {
      this.optimizeCache();
    }
  }

  private generateContentHash(content: string): string {
    return createHash('sha256').update(content).digest('hex').substring(0, 16);
  }

  private getTotalRequests(): number {
    return this.hitCount + this.missCount;
  }

  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}

// 全局单例实例
let globalFileCache: FileContentCache | null = null;

/**
 * 获取全局文件缓存实例
 */
export function getGlobalFileCache(): FileContentCache {
  if (!globalFileCache) {
    globalFileCache = new FileContentCache({
      maxSize: 2000,
      maxMemory: 200 * 1024 * 1024, // 200MB
      defaultTTL: 30 * 60 * 1000, // 30分钟
      cleanupInterval: 5 * 60 * 1000, // 5分钟
      enableCompression: false,
      enableStatistics: true,
    });
  }
  return globalFileCache;
}

/**
 * 创建文件缓存实例
 */
export function createFileCache(options?: FileCacheOptions): FileContentCache {
  return new FileContentCache(options);
}