import { promises as fs } from 'fs';
import { codeFrameColumns } from '@babel/code-frame';
import chalk from 'chalk';
import { PositionConverter } from './position-converter';
import { getErrorRecoveryService, ErrorRecoveryService } from './error-recovery-service';
import { CodeFrameError } from '../core/errors';
import { getGlobalFileCache, FileContentCache } from './file-cache';

export interface CodeFrameOptions {
  highlightCode?: boolean;
  linesAbove?: number;
  linesBelow?: number;
  forceColor?: boolean;
  messageColor?: string;
}

export interface CodeFrameResult {
  frame: string;
  success: boolean;
  error?: string;
  cached: boolean;
  recoveryAttempts?: number;
  recoveryStrategy?: string;
  memoryUsage?: number; // 估算的内存使用量（字节）
}

/**
 * 内存池化的代码框架请求
 */
interface PooledFrameRequest {
  filePath: string;
  line: number;
  column: number;
  options: CodeFrameOptions;
  resolve: (result: CodeFrameResult) => void;
  reject: (error: Error) => void;
  createdAt: number;
}

/**
 * 代码框架生成器
 * 使用 @babel/code-frame 生成带有上下文的代码片段，集成错误恢复机制和高性能文件缓存
 * 新增内存优化：对象池、批量处理、内存监控
 */
export class CodeFrameGenerator {
  private fileCache: FileContentCache;
  private errorRecoveryService: ErrorRecoveryService;

  // 内存优化相关
  private requestPool: PooledFrameRequest[] = [];
  private isProcessing = false;
  private batchSize = 10; // 批量处理大小
  private maxMemoryUsage = 50 * 1024 * 1024; // 50MB 内存限制
  private currentMemoryUsage = 0;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(fileCache?: FileContentCache) {
    this.fileCache = fileCache || getGlobalFileCache();
    this.errorRecoveryService = getErrorRecoveryService({
      maxRetries: 3,
      retryDelay: 500,
      enableCaching: true,
      logErrors: true,
    });

    // 启动定期内存清理
    this.startMemoryCleanup();
  }

  /**
   * 生成代码框架（内存优化版本）
   * @param filePath 文件路径
   * @param line 行号（1-based）
   * @param column 列号（1-based）
   * @param options 选项
   */
  public async generateFrame(
    filePath: string,
    line: number,
    column: number,
    options: CodeFrameOptions = {}
  ): Promise<CodeFrameResult> {
    // 内存压力检查
    if (this.currentMemoryUsage > this.maxMemoryUsage) {
      await this.performMemoryCleanup();
    }

    const frameOptions = {
      highlightCode: options.highlightCode ?? true,
      linesAbove: options.linesAbove ?? 2,
      linesBelow: options.linesBelow ?? 2,
      forceColor: options.forceColor ?? false,
    };

    const memoryBefore = this.estimateMemoryUsage();

    try {
      const recoveryResult = await this.errorRecoveryService.generateCodeFrameWithRecovery(
        filePath,
        line,
        column,
        async (sourceCode: string) => {
          // 优先使用缓存的文件内容
          const content = await this.fileCache.getFileContent(filePath);
          return codeFrameColumns(content, { start: { line, column } }, frameOptions);
        }
      );

      const memoryAfter = this.estimateMemoryUsage();
      const memoryDelta = memoryAfter - memoryBefore;
      this.currentMemoryUsage += memoryDelta;

      if (recoveryResult.success && recoveryResult.result) {
        return {
          frame: recoveryResult.result,
          success: true,
          cached: this.fileCache.hasValidCache(filePath),
          recoveryAttempts: recoveryResult.attempts.length,
          recoveryStrategy: recoveryResult.strategy,
          memoryUsage: memoryDelta,
        };
      }

      // 如果恢复失败，返回错误结果
      return {
        frame: this.generateFallbackFrame(filePath, line),
        success: false,
        error: recoveryResult.error?.message || '未知错误',
        cached: false,
        recoveryAttempts: recoveryResult.attempts.length,
        recoveryStrategy: recoveryResult.strategy,
        memoryUsage: memoryDelta,
      };
    } catch (error) {
      return {
        frame: this.generateFallbackFrame(filePath, line),
        success: false,
        error: error instanceof Error ? error.message : String(error),
        cached: false,
        memoryUsage: 0,
      };
    }
  }

  /**
   * 批量生成代码框架（内存优化）
   */
  public async generateFramesBatch(
    requests: Array<{
      filePath: string;
      line: number;
      column: number;
      options?: CodeFrameOptions;
    }>
  ): Promise<CodeFrameResult[]> {
    // 按文件路径分组，优化文件缓存利用率
    const groupedRequests = this.groupRequestsByFile(requests);
    const results: CodeFrameResult[] = [];

    for (const [filePath, fileRequests] of groupedRequests.entries()) {
      // 预加载文件到缓存
      try {
        await this.fileCache.preloadFile(filePath);
      } catch {
        // 预加载失败，继续处理
      }

      // 批量处理该文件的所有请求
      for (let i = 0; i < fileRequests.length; i += this.batchSize) {
        const batch = fileRequests.slice(i, i + this.batchSize);
        const batchPromises = batch.map((req) => this.generateFrame(req.filePath, req.line, req.column, req.options));

        const batchResults = await Promise.allSettled(batchPromises);
        results.push(
          ...batchResults.map((result) =>
            result.status === 'fulfilled'
              ? result.value
              : {
                  frame: this.generateFallbackFrame(filePath, 1),
                  success: false,
                  error: result.status === 'rejected' ? result.reason.message : '批量处理失败',
                  cached: false,
                }
          )
        );

        // 检查内存压力
        if (this.currentMemoryUsage > this.maxMemoryUsage * 0.8) {
          await this.performMemoryCleanup();
        }
      }
    }

    return results;
  }

  /**
   * 从SWC span生成代码框架
   * @param filePath 文件路径
   * @param span SWC span信息
   * @param options 选项
   */
  public async generateFrameFromSpan(
    filePath: string,
    span: { start: number; end: number },
    options: CodeFrameOptions = {}
  ): Promise<CodeFrameResult> {
    try {
      const fileResult = await this.errorRecoveryService.readFileWithRecovery(filePath);

      if (!fileResult.success || !fileResult.result) {
        return {
          frame: this.generateFallbackFrame(filePath, 1),
          success: false,
          error: fileResult.error?.message || '文件读取失败',
          cached: false,
          recoveryAttempts: fileResult.attempts.length,
          recoveryStrategy: fileResult.strategy,
        };
      }

      // 使用缓存的文件内容进行位置转换
      const content = await this.fileCache.getFileContent(filePath);
      const position = PositionConverter.spanToPosition(content, span.start);
      return this.generateFrame(filePath, position.line, position.column, options);
    } catch (error) {
      return {
        frame: this.generateFallbackFrame(filePath, 1),
        success: false,
        error: error instanceof Error ? error.message : String(error),
        cached: false,
        recoveryAttempts: 1,
        recoveryStrategy: 'span-conversion-failed',
      };
    }
  }

  /**
   * 生成后备代码框架（当生成失败时）
   */
  private generateFallbackFrame(filePath: string, line: number): string {
    try {
      // 尝试提供更有意义的后备信息
      const fileName = filePath.split('/').pop() || filePath;

      // 尝试从文件缓存中获取行内容
      if (this.fileCache && this.fileCache.hasValidCache) {
        try {
          const content = this.fileCache.getFileContent?.(filePath);
          if (content) {
            const lines = content.split('\n');
            if (line > 0 && line <= lines.length) {
              const targetLine = lines[line - 1]?.trim();
              if (targetLine && targetLine !== '}' && targetLine !== '{') {
                return chalk.gray(`  ${line} | ${targetLine.substring(0, 80)}${targetLine.length > 80 ? '...' : ''}`);
              }

              // 寻找附近的有效代码行
              for (let i = Math.max(0, line - 5); i < Math.min(lines.length, line + 5); i++) {
                const nearbyLine = lines[i]?.trim();
                if (nearbyLine && nearbyLine !== '}' && nearbyLine !== '{' && !nearbyLine.startsWith('//')) {
                  const lineNum = i + 1;
                  return chalk.gray(
                    `  ${lineNum} | ${nearbyLine.substring(0, 80)}${nearbyLine.length > 80 ? '...' : ''}`
                  );
                }
              }
            }
          }
        } catch {
          // 忽略错误，继续使用默认后备方案
        }
      }

      return chalk.gray(`  > ${line} | [代码框架生成失败: ${fileName}]`);
    } catch (error) {
      // 最终后备方案
      return chalk.gray(`  > ${line} | [代码框架生成失败]`);
    }
  }

  /**
   * 清空文件缓存
   */
  public clearCache(): void {
    this.fileCache.clearCache();
    this.errorRecoveryService.clearCache();
    this.currentMemoryUsage = 0;
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats(): {
    fileCache: ReturnType<FileContentCache['getCacheStats']>;
    errorStats: {
      cacheSize: number;
      failureCount: number;
      failedFiles: string[];
    };
    memoryStats: {
      currentUsage: number;
      maxUsage: number;
      utilizationPercent: number;
    };
  } {
    return {
      fileCache: this.fileCache.getCacheStats(),
      errorStats: this.errorRecoveryService.getErrorStats(),
      memoryStats: {
        currentUsage: this.currentMemoryUsage,
        maxUsage: this.maxMemoryUsage,
        utilizationPercent: (this.currentMemoryUsage / this.maxMemoryUsage) * 100,
      },
    };
  }

  /**
   * 预加载文件到缓存
   */
  public async preloadFiles(filePaths: string[]): Promise<void> {
    await this.fileCache.preloadFiles(filePaths);
  }

  /**
   * 优化缓存性能
   */
  public async optimizeCache(): Promise<{
    beforeMemory: number;
    afterMemory: number;
    freedMemory: number;
  }> {
    const beforeMemory = this.currentMemoryUsage;

    // 清理文件缓存
    await this.fileCache.optimizeCache();

    // 执行内存清理
    await this.performMemoryCleanup();

    const afterMemory = this.currentMemoryUsage;

    return {
      beforeMemory,
      afterMemory,
      freedMemory: beforeMemory - afterMemory,
    };
  }

  /**
   * 销毁生成器，释放资源
   */
  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }

    this.clearCache();
    this.requestPool = [];
  }

  // 内存优化私有方法

  /**
   * 启动内存清理定时器
   */
  private startMemoryCleanup(): void {
    this.cleanupTimer = setInterval(async () => {
      if (this.currentMemoryUsage > this.maxMemoryUsage * 0.7) {
        await this.performMemoryCleanup();
      }
    }, 30000); // 每30秒检查一次
    
    // 使用 unref() 防止定时器阻止进程退出
    this.cleanupTimer.unref();
  }

  /**
   * 执行内存清理
   */
  private async performMemoryCleanup(): Promise<void> {
    // 清理过期的请求池
    const now = Date.now();
    this.requestPool = this.requestPool.filter(
      (req) => now - req.createdAt < 60000 // 保留1分钟内的请求
    );

    // 优化文件缓存
    await this.fileCache.optimizeCache();

    // 强制垃圾回收（如果可用）
    if (global.gc) {
      global.gc();
    }

    // 重新估算内存使用
    this.currentMemoryUsage = this.estimateMemoryUsage();
  }

  /**
   * 估算当前内存使用量
   */
  private estimateMemoryUsage(): number {
    const fileStats = this.fileCache.getCacheStats();
    const errorStats = this.errorRecoveryService.getErrorStats();

    return (
      fileStats.memoryUsage +
      errorStats.cacheSize * 1024 + // 估算错误缓存
      this.requestPool.length * 200
    ); // 估算请求池
  }

  /**
   * 按文件分组请求
   */
  private groupRequestsByFile(
    requests: Array<{
      filePath: string;
      line: number;
      column: number;
      options?: CodeFrameOptions;
    }>
  ): Map<string, typeof requests> {
    const grouped = new Map<string, typeof requests>();

    for (const request of requests) {
      const existing = grouped.get(request.filePath) || [];
      existing.push(request);
      grouped.set(request.filePath, existing);
    }

    return grouped;
  }
}

// 单例实例
let instance: CodeFrameGenerator | null = null;

export function getCodeFrameGenerator(): CodeFrameGenerator {
  if (!instance) {
    instance = new CodeFrameGenerator();
  }
  return instance;
}
