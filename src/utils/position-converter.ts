/**
 * 位置转换工具
 * 专注于SWC解析器字节偏移量与行列位置之间的纯粹转换
 * 支持行映射缓存以优化内存和性能
 * 
 * 职责：
 * - 字节偏移量与行列位置的转换
 * - 基于偏移量的高效行内容获取
 * - 智能缓存管理提升大文件处理性能
 * 
 * 不包含：
 * - span 位置修正逻辑
 * - 代码内容语义分析
 * - 复杂的错误恢复机制
 */

export interface Position {
  line: number;
  column: number;
}

/**
 * 行映射缓存条目
 */
interface LineMapCacheEntry {
  lineMap: number[];
  timestamp: number;
  accessCount: number;
}

/**
 * 位置转换器
 * 提供SWC span偏移量与行列位置之间的纯粹转换功能
 * 内置智能缓存系统优化大文件处理性能
 * 
 * 简化后的职责：
 * - 仅负责精确的位置转换，不包含修正逻辑
 * - 基于偏移量的高效行内容获取
 * - 智能缓存管理和性能优化
 */
export class PositionConverter {
  private static lineMapCache = new Map<string, LineMapCacheEntry>();
  private static maxCacheSize = 100; // 最大缓存条目数
  private static cacheCleanupThreshold = 150; // 清理阈值

  /**
   * 将SWC span的字节偏移量转换为行列位置
   * @param sourceCode 源代码内容
   * @param spanStart span的起始偏移量
   * @returns 行列位置 (1-based)
   */
  public static spanToPosition(sourceCode: string, spanStart: number): Position {
    const lineMap = this.getCachedLineMap(sourceCode);

    // 验证span有效性
    if (spanStart < 0 || spanStart > sourceCode.length) {
      return { line: 1, column: 1 };
    }

    // 使用二分查找找到对应的行（优化性能）
    const lineIndex = this.binarySearchLineIndex(lineMap, spanStart);

    // 计算列位置
    const lineStartOffset = lineMap[lineIndex]!;
    const column = spanStart - lineStartOffset + 1; // 1-based

    return {
      line: lineIndex + 1, // 1-based
      column: Math.max(1, column), // 确保至少为1
    };
  }

  /**
   * 将行列位置转换为字节偏移量
   * @param sourceCode 源代码内容
   * @param line 行号 (1-based)
   * @param column 列号 (1-based)
   * @returns 字节偏移量
   */
  public static lineColumnToOffset(sourceCode: string, line: number, column: number): number {
    const lineMap = this.getCachedLineMap(sourceCode);

    // 转换为0-based索引
    const lineIndex = line - 1;
    const columnIndex = column - 1;

    // 边界检查
    if (lineIndex < 0 || lineIndex >= lineMap.length) {
      throw new Error(`Line ${line} is out of range. File has ${lineMap.length} lines.`);
    }

    const lineStartOffset = lineMap[lineIndex]!;

    // 获取行的内容长度（不包括换行符）
    const lineEndOffset =
      lineIndex + 1 < lineMap.length
        ? lineMap[lineIndex + 1]! - 1 // 减去换行符
        : sourceCode.length;

    const lineLength = lineEndOffset - lineStartOffset;

    // 列边界检查
    if (columnIndex < 0 || columnIndex > lineLength) {
      throw new Error(`Column ${column} is out of range. Line ${line} has ${lineLength} characters.`);
    }

    return lineStartOffset + columnIndex;
  }

  /**
   * 获取指定行的内容
   * @param sourceCode 源代码内容
   * @param line 行号 (1-based)
   * @returns 行内容（不包括换行符）
   */
  public static getLineContent(sourceCode: string, line: number): string {
    const lineMap = this.getCachedLineMap(sourceCode);
    const lineIndex = line - 1; // 转换为0-based

    if (lineIndex < 0 || lineIndex >= lineMap.length) {
      throw new Error(`Line ${line} is out of range. File has ${lineMap.length} lines.`);
    }

    const lineStartOffset = lineMap[lineIndex]!;
    const lineEndOffset =
      lineIndex + 1 < lineMap.length
        ? lineMap[lineIndex + 1]! - 1 // 减去换行符
        : sourceCode.length;

    return sourceCode.slice(lineStartOffset, lineEndOffset);
  }

  /**
   * 获取span覆盖的代码片段
   * @param sourceCode 源代码内容
   * @param span SWC span信息
   * @returns 代码片段
   */
  public static extractSpanText(sourceCode: string, span: { start: number; end: number }): string {
    if (span.start < 0 || span.end > sourceCode.length || span.start > span.end) {
      throw new Error(`Invalid span: start=${span.start}, end=${span.end}, sourceLength=${sourceCode.length}`);
    }

    return sourceCode.slice(span.start, span.end);
  }

  /**
   * 验证位置是否有效
   * @param sourceCode 源代码内容
   * @param position 位置信息
   * @returns 是否有效
   */
  public static isValidPosition(sourceCode: string, position: Position): boolean {
    try {
      this.lineColumnToOffset(sourceCode, position.line, position.column);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取缓存的行映射，支持智能缓存管理
   * @param sourceCode 源代码内容
   * @returns 行映射数组
   */
  private static getCachedLineMap(sourceCode: string): number[] {
    // 生成内容哈希作为缓存键（对于大文件使用采样哈希以提高性能）
    const cacheKey = this.generateCacheKey(sourceCode);

    // 检查缓存
    const cached = this.lineMapCache.get(cacheKey);
    if (cached) {
      cached.accessCount++;
      cached.timestamp = Date.now();
      return cached.lineMap;
    }

    // 缓存未命中，构建行映射
    const lineMap = this.buildLineMap(sourceCode);

    // 缓存管理
    this.manageCacheSize();

    // 添加到缓存
    this.lineMapCache.set(cacheKey, {
      lineMap,
      timestamp: Date.now(),
      accessCount: 1,
    });

    return lineMap;
  }

  /**
   * 生成源代码的缓存键
   * 对于大文件使用采样策略以提高性能
   */
  private static generateCacheKey(sourceCode: string): string {
    if (sourceCode.length <= 10000) {
      // 小文件直接使用内容长度和前后100字符
      const start = sourceCode.substring(0, 100);
      const end = sourceCode.substring(Math.max(0, sourceCode.length - 100));
      return `${sourceCode.length}-${start}-${end}`;
    } else {
      // 大文件使用采样策略：长度 + 开头 + 中间 + 结尾
      const middle = sourceCode.substring(
        Math.floor(sourceCode.length / 2) - 50,
        Math.floor(sourceCode.length / 2) + 50
      );
      const start = sourceCode.substring(0, 100);
      const end = sourceCode.substring(Math.max(0, sourceCode.length - 100));
      return `${sourceCode.length}-${start}-${middle}-${end}`;
    }
  }

  /**
   * 管理缓存大小，防止内存泄漏
   */
  private static manageCacheSize(): void {
    if (this.lineMapCache.size <= this.maxCacheSize) {
      return;
    }

    // 当缓存超过清理阈值时，执行清理
    if (this.lineMapCache.size >= this.cacheCleanupThreshold) {
      this.performCacheCleanup();
    }
  }

  /**
   * 执行缓存清理，移除最少使用的条目
   */
  private static performCacheCleanup(): void {
    const entries = Array.from(this.lineMapCache.entries());

    // 按访问频率和时间排序（LRU + LFU混合策略）
    entries.sort((a, b) => {
      const scoreA = a[1].accessCount * 0.7 + (Date.now() - a[1].timestamp) * -0.3;
      const scoreB = b[1].accessCount * 0.7 + (Date.now() - b[1].timestamp) * -0.3;
      return scoreA - scoreB;
    });

    // 移除最低分的条目，保留75%的缓存
    const removeCount = Math.floor(this.lineMapCache.size * 0.25);
    const toRemove = entries.slice(0, removeCount);

    for (const [key] of toRemove) {
      this.lineMapCache.delete(key);
    }
  }

  /**
   * 使用二分查找定位行索引（优化大文件性能）
   */
  private static binarySearchLineIndex(lineMap: number[], offset: number): number {
    let left = 0;
    let right = lineMap.length - 1;

    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const midOffset = lineMap[mid]!;
      const nextOffset = lineMap[mid + 1];

      if (offset >= midOffset && (nextOffset === undefined || offset < nextOffset)) {
        return mid;
      } else if (offset < midOffset) {
        right = mid - 1;
      } else {
        left = mid + 1;
      }
    }

    // 后备方案：线性搜索
    for (let i = 0; i < lineMap.length - 1; i++) {
      if (offset >= lineMap[i]! && offset < lineMap[i + 1]!) {
        return i;
      }
    }

    return Math.max(0, lineMap.length - 1);
  }

  /**
   * 构建行映射表
   * 创建一个数组，其中每个元素是对应行的起始字节偏移量
   * @param sourceCode 源代码内容
   * @returns 行起始偏移量数组
   */
  private static buildLineMap(sourceCode: string): number[] {
    const lineMap: number[] = [];
    lineMap.push(0); // 第一行从偏移量0开始

    // 优化：对于大文件使用更高效的搜索方式
    if (sourceCode.length > 50000) {
      // 大文件：分块处理以减少内存压力
      const chunkSize = 10000;
      for (let start = 0; start < sourceCode.length; start += chunkSize) {
        const end = Math.min(start + chunkSize, sourceCode.length);
        const chunk = sourceCode.slice(start, end);

        for (let i = 0; i < chunk.length; i++) {
          if (chunk[i] === '\n') {
            lineMap.push(start + i + 1);
          }
        }
      }
    } else {
      // 小文件：直接处理
      for (let i = 0; i < sourceCode.length; i++) {
        if (sourceCode[i] === '\n') {
          lineMap.push(i + 1); // 下一行从换行符后开始
        }
      }
    }

    return lineMap;
  }

  /**
   * 清除缓存（用于测试和内存管理）
   */
  public static clearCache(): void {
    this.lineMapCache.clear();
  }

  /**
   * 获取缓存统计信息
   */
  public static getCacheStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    memoryEstimate: number;
  } {
    let totalAccess = 0;
    let memoryEstimate = 0;

    const entries = Array.from(this.lineMapCache.values());
    for (const entry of entries) {
      totalAccess += entry.accessCount;
      memoryEstimate += entry.lineMap.length * 8; // 估算字节数
    }

    return {
      size: this.lineMapCache.size,
      maxSize: this.maxCacheSize,
      hitRate: totalAccess > 0 ? (totalAccess - this.lineMapCache.size) / totalAccess : 0,
      memoryEstimate,
    };
  }
}
