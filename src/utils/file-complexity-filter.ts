import type { FileResult, AnalysisResult } from '../core/types';
import { 
  TypeSafeError, 
  SystemError, 
  ErrorUtils, 
  TypeValidationError 
} from '../core/type-safe-errors';
import { CLIUIHelper } from '../cli/ui-helper';
import { getGlobalErrorRecoveryManager } from './global-error-recovery-manager';

/**
 * 文件复杂度过滤器配置选项
 */
export interface FileFilterOptions {
  /** 文件复杂度阈值，默认为1 */
  threshold: number;
  /** 是否在静默模式下运行 */
  quiet?: boolean;
}

/**
 * 文件复杂度过滤器错误类
 */
export class FileComplexityFilterError extends SystemError {
  constructor(operation: string, message: string, isRecoverable = true, originalError?: Error) {
    super('process', `文件复杂度过滤失败 (${operation}): ${message}`, isRecoverable, {
      operation,
      originalError: originalError?.message,
      component: 'FileComplexityFilter',
    });
  }

  override getRecoveryActions(): string[] {
    return [
      '使用默认过滤设置',
      '显示所有文件（不应用过滤）',
      '检查输入数据的有效性',
      '重新初始化过滤器实例',
    ];
  }
}

/**
 * 过滤后的结果集
 */
export interface FilteredResults {
  /** 过滤后的文件结果数组 */
  filteredFiles: FileResult[];
  /** 过滤统计信息 */
  statistics: FilterStatistics;
}

/**
 * 文件复杂度过滤统计信息
 */
export interface FilterStatistics {
  /** 原始文件总数 */
  totalFiles: number;
  /** 显示的文件数 */
  displayedFiles: number;
  /** 隐藏的文件数 */
  hiddenFiles: number;
  /** 过滤阈值 */
  threshold: number;
  /** 是否应用了过滤 */
  hasFiltering: boolean;
  /** 过滤原因描述 */
  filterReason: string;
  /** 隐藏文件的平均复杂度 */
  hiddenFilesAvgComplexity: number;
  /** 显示文件的平均复杂度 */
  displayedFilesAvgComplexity: number;
}

/**
 * 文件复杂度过滤器
 * 基于文件总复杂度阈值过滤文件结果，支持统计信息收集和错误恢复
 */
export class FileComplexityFilter {
  private uiHelper: CLIUIHelper;
  private errorRecoveryManager = getGlobalErrorRecoveryManager();
  
  constructor() {
    this.uiHelper = new CLIUIHelper();
  }

  /**
   * 过滤文件结果，保留复杂度达到阈值的文件（带错误处理）
   * @param results 原始文件结果数组
   * @param options 过滤选项
   * @returns 过滤后的结果集
   */
  public async filterResults(results: FileResult[], options: FileFilterOptions): Promise<FilteredResults> {
    try {
      // 验证输入参数
      this.validateInputs(results, options);

      // 如果阈值为0或负数，显示所有文件
      if (options.threshold <= 0) {
        return {
          filteredFiles: results,
          statistics: this.generateStatistics(results, results, options.threshold)
        };
      }

      // 基于文件复杂度阈值进行过滤
      const filteredFiles = this.filterFilesByComplexity(results, options.threshold);
      
      // 生成统计信息
      const statistics = this.generateStatistics(results, filteredFiles, options.threshold);

      return {
        filteredFiles,
        statistics
      };

    } catch (error) {
      // 使用全局错误恢复管理器处理错误
      const recoveryResult = await this.errorRecoveryManager.handleError(
        error as Error,
        {
          component: 'FileComplexityFilter',
          operation: 'filterResults',
          metadata: { 
            resultCount: results?.length || 0, 
            threshold: options?.threshold || 0 
          }
        },
        // 恢复函数：返回未过滤的结果
        () => this.createFallbackResults(results, options)
      );

      if (recoveryResult.success && recoveryResult.result) {
        // 显示警告但继续执行
        if (!options.quiet) {
          this.uiHelper.warning(`文件过滤器遇到问题，使用未过滤的结果继续执行`);
        }
        return recoveryResult.result;
      } else {
        // 恢复失败，抛出新的错误
        throw new FileComplexityFilterError(
          'filterResults', 
          `过滤操作失败且无法恢复: ${ErrorUtils.getErrorMessage(error)}`,
          false,
          error as Error
        );
      }
    }
  }

  /**
   * 同步版本的过滤方法（向后兼容，内部使用优雅降级）
   */
  public filterResultsSync(results: FileResult[], options: FileFilterOptions): FilteredResults {
    try {
      // 验证输入参数
      this.validateInputs(results, options);

      // 如果阈值为0或负数，显示所有文件
      if (options.threshold <= 0) {
        return {
          filteredFiles: results,
          statistics: this.generateStatistics(results, results, options.threshold)
        };
      }

      // 基于文件复杂度阈值进行过滤
      const filteredFiles = this.filterFilesByComplexity(results, options.threshold);
      
      // 生成统计信息
      const statistics = this.generateStatistics(results, filteredFiles, options.threshold);

      return {
        filteredFiles,
        statistics
      };

    } catch (error) {
      // 同步版本的优雅降级：显示警告并返回未过滤的结果
      if (!options.quiet) {
        this.uiHelper.warning(
          `文件过滤器遇到问题: ${ErrorUtils.getErrorMessage(error)}`
        );
        this.uiHelper.showTip('使用未过滤的结果继续执行');
      }

      return this.createFallbackResults(results, options);
    }
  }

  /**
   * 应用文件复杂度过滤到完整的分析结果（带错误处理）
   * @param analysisResult 原始分析结果
   * @param options 过滤选项
   * @returns 过滤后的分析结果
   */
  public async applyToAnalysisResult(analysisResult: AnalysisResult, options: FileFilterOptions): Promise<AnalysisResult> {
    try {
      const filteredResults = await this.filterResults(analysisResult.results, options);
      
      // 创建新的分析结果，保持汇总信息基于所有文件（符合需求1.5）
      const filteredAnalysisResult: AnalysisResult = {
        summary: {
          ...analysisResult.summary,
          // 汇总统计信息仍基于所有分析的文件，不受过滤影响
        },
        results: filteredResults.filteredFiles,
        baseline: analysisResult.baseline
      };

      // 添加过滤统计信息到结果中（如果需要）
      (filteredAnalysisResult as any).filterStatistics = filteredResults.statistics;

      return filteredAnalysisResult;
      
    } catch (error) {
      // 应用过滤失败时的优雅降级
      const recoveryResult = await this.errorRecoveryManager.handleError(
        error as Error,
        {
          component: 'FileComplexityFilter',
          operation: 'applyToAnalysisResult',
          metadata: { 
            resultCount: analysisResult.results?.length || 0, 
            threshold: options.threshold 
          }
        },
        // 恢复函数：返回原始未过滤的分析结果
        () => {
          const fallbackResults = this.createFallbackResults(analysisResult.results, options);
          return {
            ...analysisResult,
            filterStatistics: fallbackResults.statistics
          };
        }
      );

      if (recoveryResult.success && recoveryResult.result) {
        if (!options.quiet) {
          this.uiHelper.warning('分析结果过滤失败，返回未过滤的完整结果');
        }
        return recoveryResult.result;
      } else {
        throw new FileComplexityFilterError(
          'applyToAnalysisResult',
          `无法应用文件过滤到分析结果: ${ErrorUtils.getErrorMessage(error)}`,
          false,
          error as Error
        );
      }
    }
  }

  /**
   * 同步版本的分析结果过滤方法（向后兼容）
   */
  public applyToAnalysisResultSync(analysisResult: AnalysisResult, options: FileFilterOptions): AnalysisResult {
    try {
      const filteredResults = this.filterResultsSync(analysisResult.results, options);
      
      // 创建新的分析结果，保持汇总信息基于所有文件（符合需求1.5）
      const filteredAnalysisResult: AnalysisResult = {
        summary: {
          ...analysisResult.summary,
          // 汇总统计信息仍基于所有分析的文件，不受过滤影响
        },
        results: filteredResults.filteredFiles,
        baseline: analysisResult.baseline
      };

      // 添加过滤统计信息到结果中（如果需要）
      (filteredAnalysisResult as any).filterStatistics = filteredResults.statistics;

      return filteredAnalysisResult;
      
    } catch (error) {
      // 同步版本的优雅降级
      if (!options.quiet) {
        this.uiHelper.warning(
          `分析结果过滤失败: ${ErrorUtils.getErrorMessage(error)}`
        );
        this.uiHelper.showTip('返回未过滤的完整结果');
      }

      const fallbackResults = this.createFallbackResults(analysisResult.results, options);
      return {
        ...analysisResult,
        filterStatistics: fallbackResults.statistics
      } as AnalysisResult & { filterStatistics: FilterStatistics };
    }
  }

  /**
   * 获取过滤摘要信息
   * @param statistics 过滤统计信息
   * @returns 格式化的摘要字符串
   */
  public getFilterSummary(statistics: FilterStatistics): string {
    if (!statistics.hasFiltering) {
      return '';
    }

    const { displayedFiles, totalFiles, hiddenFiles, threshold } = statistics;
    
    if (hiddenFiles === 0) {
      return `显示全部 ${totalFiles} 个文件`;
    }

    return `显示 ${displayedFiles}/${totalFiles} 个文件（已隐藏 ${hiddenFiles} 个复杂度 < ${threshold} 的文件）`;
  }

  /**
   * 判断是否应该显示过滤摘要
   * @param statistics 过滤统计信息
   * @param quiet 是否为静默模式
   * @param format 输出格式
   * @returns 是否应该显示过滤摘要
   */
  public shouldShowFilterSummary(statistics: FilterStatistics, quiet: boolean = false, format: string = 'text'): boolean {
    // 满足需求3.3：在静默模式或JSON格式下不显示过滤统计信息
    if (quiet || format === 'json') {
      return false;
    }

    // 满足需求3.4：没有文件被过滤时不显示过滤统计信息
    if (!statistics.hasFiltering || statistics.hiddenFiles === 0) {
      return false;
    }

    return true;
  }

  /**
   * 生成过滤统计信息
   * @param originalFiles 原始文件数组
   * @param filteredFiles 过滤后文件数组
   * @param threshold 过滤阈值
   * @returns 过滤统计信息
   */
  private generateStatistics(
    originalFiles: FileResult[], 
    filteredFiles: FileResult[], 
    threshold: number
  ): FilterStatistics {
    const totalFiles = originalFiles.length;
    const displayedFiles = filteredFiles.length;
    const hiddenFiles = totalFiles - displayedFiles;
    const hasFiltering = threshold > 0 && hiddenFiles > 0;

    // 计算隐藏文件和显示文件的平均复杂度
    const hiddenFileComplexities = originalFiles
      .filter(file => file.complexity < threshold)
      .map(file => file.complexity);
    
    const displayedFileComplexities = filteredFiles.map(file => file.complexity);

    const hiddenFilesAvgComplexity = hiddenFileComplexities.length > 0
      ? hiddenFileComplexities.reduce((sum, complexity) => sum + complexity, 0) / hiddenFileComplexities.length
      : 0;

    const displayedFilesAvgComplexity = displayedFileComplexities.length > 0
      ? displayedFileComplexities.reduce((sum, complexity) => sum + complexity, 0) / displayedFileComplexities.length
      : 0;

    // 生成过滤原因描述
    let filterReason = '';
    if (!hasFiltering) {
      filterReason = '未应用文件级过滤';
    } else if (hiddenFiles === 0) {
      filterReason = '所有文件均符合复杂度要求';
    } else {
      const hiddenPercentage = (hiddenFiles / totalFiles) * 100;
      if (hiddenPercentage < 25) {
        filterReason = '轻度过滤：隐藏少量低复杂度文件';
      } else if (hiddenPercentage < 50) {
        filterReason = '中度过滤：隐藏部分低复杂度文件';
      } else if (hiddenPercentage < 75) {
        filterReason = '重度过滤：大部分文件被隐藏';
      } else {
        filterReason = '极重过滤：仅显示最复杂的文件';
      }
    }

    return {
      totalFiles,
      displayedFiles,
      hiddenFiles,
      threshold,
      hasFiltering,
      filterReason,
      hiddenFilesAvgComplexity,
      displayedFilesAvgComplexity
    };
  }

  // 私有辅助方法

  /**
   * 验证输入参数
   */
  private validateInputs(results: FileResult[], options: FileFilterOptions): void {
    if (!Array.isArray(results)) {
      throw new TypeValidationError(
        'FileResult[]',
        typeof results,
        'results',
        { component: 'FileComplexityFilter' }
      );
    }

    if (typeof options !== 'object' || options === null) {
      throw new TypeValidationError(
        'FileFilterOptions',
        typeof options,
        'options',
        { component: 'FileComplexityFilter' }
      );
    }

    if (typeof options.threshold !== 'number' || isNaN(options.threshold)) {
      throw new TypeValidationError(
        'number',
        typeof options.threshold,
        'options.threshold',
        { 
          component: 'FileComplexityFilter',
          actualValue: options.threshold 
        }
      );
    }

    // 验证文件结果数组中的每个项目
    for (let i = 0; i < results.length; i++) {
      const file = results[i];
      if (!file || typeof file.complexity !== 'number' || isNaN(file.complexity)) {
        throw new TypeValidationError(
          'FileResult with valid complexity',
          typeof file?.complexity,
          `results[${i}].complexity`,
          { 
            component: 'FileComplexityFilter',
            fileIndex: i,
            actualComplexity: file?.complexity
          }
        );
      }
    }
  }

  /**
   * 基于复杂度阈值过滤文件
   */
  private filterFilesByComplexity(results: FileResult[], threshold: number): FileResult[] {
    try {
      return results.filter(file => {
        if (typeof file.complexity !== 'number' || isNaN(file.complexity)) {
          // 记录警告但不阻止过滤
          this.uiHelper.warning(
            `文件 ${file.filePath || '<unknown>'} 的复杂度值无效，跳过该文件`
          );
          return false;
        }
        return file.complexity >= threshold;
      });
    } catch (error) {
      throw new FileComplexityFilterError(
        'filterFilesByComplexity',
        `过滤文件时发生错误: ${ErrorUtils.getErrorMessage(error)}`,
        true,
        error as Error
      );
    }
  }

  /**
   * 创建回退结果（用于优雅降级）
   */
  private createFallbackResults(results: FileResult[], options: FileFilterOptions): FilteredResults {
    try {
      // 确保输入是有效的数组
      const safeResults = Array.isArray(results) ? results : [];
      const safeThreshold = typeof options?.threshold === 'number' && !isNaN(options.threshold) 
        ? options.threshold 
        : 0;

      return {
        filteredFiles: safeResults,
        statistics: this.generateStatistics(safeResults, safeResults, safeThreshold)
      };
    } catch (error) {
      // 最后的回退：返回空结果
      return {
        filteredFiles: [],
        statistics: {
          totalFiles: 0,
          displayedFiles: 0,
          hiddenFiles: 0,
          threshold: 0,
          hasFiltering: false,
          filterReason: '创建回退结果时发生错误',
          hiddenFilesAvgComplexity: 0,
          displayedFilesAvgComplexity: 0
        }
      };
    }
  }
}

/**
 * 获取默认的文件过滤选项
 * @returns 默认文件过滤选项
 */
export function getDefaultFileFilterOptions(): FileFilterOptions {
  return {
    threshold: 1,
    quiet: false
  };
}

/**
 * 创建文件复杂度过滤器实例的工厂函数
 * @returns FileComplexityFilter实例
 */
export function createFileComplexityFilter(): FileComplexityFilter {
  return new FileComplexityFilter();
}