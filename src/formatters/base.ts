import type { CognitiveConfig, CLIOptions } from '../config/types';
import type { AnalysisResult } from '../core/types';
import { FileComplexityFilter, type FilterStatistics } from '../utils/file-complexity-filter';

export abstract class BaseFormatter {
  protected config: CognitiveConfig;
  private fileFilter: FileComplexityFilter;
  
  constructor(config: CognitiveConfig) {
    this.config = config;
    this.fileFilter = new FileComplexityFilter();
  }
  
  public abstract format(result: AnalysisResult, showDetails?: boolean, options?: CLIOptions): Promise<string>;
  public writeToFile?(result: AnalysisResult, outputPath: string, options?: CLIOptions): Promise<void>;
  
  /**
   * 应用文件复杂度过滤到分析结果（带错误处理）
   * @param result 原始分析结果
   * @param options CLI选项
   * @returns 过滤后的分析结果
   */
  protected async applyFileComplexityFilter(result: AnalysisResult, options?: CLIOptions): Promise<AnalysisResult> {
    try {
      // 获取文件复杂度阈值，优先使用CLI参数，然后使用配置文件，最后使用默认值1
      const threshold = options?.minFileComplexity ?? this.config.minFileComplexity ?? 1;
      
      // 如果阈值为0或负数，直接返回原始结果（显示所有文件）
      if (threshold <= 0) {
        return result;
      }
      
      // 应用文件复杂度过滤（使用异步版本）
      const filteredResult = await this.fileFilter.applyToAnalysisResult(result, {
        threshold,
        quiet: options?.quiet || false
      });
      
      return filteredResult;
    } catch (error) {
      // 发生错误时使用同步版本作为回退
      console.warn('异步文件过滤失败，回退到同步版本:', error);
      return this.applyFileComplexityFilterSync(result, options);
    }
  }

  /**
   * 同步版本的文件复杂度过滤（用作回退）
   * @param result 原始分析结果
   * @param options CLI选项
   * @returns 过滤后的分析结果
   */
  protected applyFileComplexityFilterSync(result: AnalysisResult, options?: CLIOptions): AnalysisResult {
    // 获取文件复杂度阈值，优先使用CLI参数，然后使用配置文件，最后使用默认值1
    const threshold = options?.minFileComplexity ?? this.config.minFileComplexity ?? 1;
    
    // 如果阈值为0或负数，直接返回原始结果（显示所有文件）
    if (threshold <= 0) {
      return result;
    }
    
    // 应用文件复杂度过滤（使用同步版本）
    const filteredResult = this.fileFilter.applyToAnalysisResultSync(result, {
      threshold,
      quiet: options?.quiet || false
    });
    
    return filteredResult;
  }
  
  /**
   * 获取文件过滤统计信息
   * @param result 已过滤的分析结果
   * @returns 过滤统计信息，如果没有则返回undefined
   */
  protected getFilterStatistics(result: AnalysisResult): FilterStatistics | undefined {
    return (result as any).filterStatistics;
  }
  
  /**
   * 生成过滤摘要信息
   * @param result 已过滤的分析结果
   * @param options CLI选项
   * @returns 过滤摘要字符串，如果不需要显示则返回空字符串
   */
  protected getFilterSummary(result: AnalysisResult, options?: CLIOptions): string {
    const statistics = this.getFilterStatistics(result);
    if (!statistics) {
      return '';
    }
    
    const format = options?.format || 'text';
    const quiet = options?.quiet || false;
    
    // 检查是否应该显示过滤摘要
    if (!this.fileFilter.shouldShowFilterSummary(statistics, quiet, format)) {
      return '';
    }
    
    return this.fileFilter.getFilterSummary(statistics);
  }
  
  protected getSeverityLevel(complexity: number): 'Critical' | 'Warning' | 'Info' | undefined {
    const severityMapping = this.config.severityMapping || [];
    
    for (const mapping of severityMapping.sort((a, b) => b.threshold - a.threshold)) {
      if (complexity >= mapping.threshold) {
        return mapping.level;
      }
    }
    
    return undefined;
  }
  
  protected formatComplexity(complexity: number): string {
    const severity = this.getSeverityLevel(complexity);
    const prefix = severity ? `[${severity}] ` : '';
    return `${prefix}${complexity}`;
  }
}