import type { DetailStep } from '../core/types';
import { DiagnosticMarker } from '../core/types';

/**
 * 智能过滤选项配置
 */
export interface FilterOptions {
  /** 最小复杂度增量阈值，低于此值的步骤可能被过滤 */
  minComplexityIncrement: number;
  /** 最大上下文项目数量，超过此数量将应用智能过滤 */
  maxContextItems: number;
  /** 是否强制显示所有上下文（绕过过滤） */
  forceShowAll: boolean;
}

/**
 * 过滤统计信息
 */
export interface FilterSummary {
  /** 原始步骤数量 */
  originalCount: number;
  /** 过滤后显示的步骤数量 */
  filteredCount: number;
  /** 被过滤掉的步骤数量 */
  hiddenCount: number;
  /** 过滤原因 */
  filterReason: string;
}

/**
 * 智能上下文过滤器
 * 基于复杂度增量和重要性智能过滤DetailStep，避免输出过于冗长
 */
export class SmartContextFilter {
  /**
   * 过滤详细步骤，保留最重要的信息
   * @param steps 原始详细步骤数组
   * @param options 过滤选项
   * @returns 过滤后的步骤数组
   */
  public filterDetailSteps(steps: DetailStep[], options: FilterOptions): DetailStep[] {
    // 总是先应用复杂度增量过滤
    const incrementFiltered = steps.filter(step => 
      step.increment >= options.minComplexityIncrement
    );
    
    // 如果复杂度过滤后没有步骤，返回空数组
    if (incrementFiltered.length === 0) {
      return [];
    }
    
    // 如果强制显示所有或过滤后的步骤数量较少，不进行进一步过滤
    if (options.forceShowAll || incrementFiltered.length <= options.maxContextItems) {
      return incrementFiltered;
    }

    // 应用智能过滤算法（在已过滤的步骤上进行）
    const filtered = this.applySmartFilterOnFiltered(incrementFiltered, options);
    
    // 确保不会过度过滤（至少保留一些重要步骤）
    const minRetain = Math.min(3, incrementFiltered.length);
    if (filtered.length < minRetain) {
      return this.selectTopSteps(incrementFiltered, minRetain);
    }

    return filtered;
  }

  /**
   * 判断步骤是否应该显示上下文
   * @param step 详细步骤
   * @param rank 步骤在排序后的位置（0为最重要）
   * @param options 过滤选项
   * @returns 是否应该显示上下文
   */
  public shouldShowContext(step: DetailStep, rank: number, options: FilterOptions): boolean {
    // 强制显示所有的情况
    if (options.forceShowAll) {
      return true;
    }

    // 已显式设置shouldShowContext的情况
    if (step.shouldShowContext !== undefined) {
      return step.shouldShowContext;
    }

    // 基于重要性和排名的判断
    return this.calculateStepImportance(step) > 0.5 && rank < options.maxContextItems;
  }

  /**
   * 获取过滤统计摘要
   * @param originalCount 原始步骤数量
   * @param filteredCount 过滤后步骤数量
   * @returns 过滤统计信息
   */
  public getFilterSummary(originalCount: number, filteredCount: number): FilterSummary {
    const hiddenCount = originalCount - filteredCount;
    let filterReason = '';

    if (hiddenCount === 0) {
      filterReason = '未应用过滤';
    } else if (hiddenCount < originalCount * 0.3) {
      filterReason = '轻度过滤：隐藏低重要性步骤';
    } else if (hiddenCount < originalCount * 0.7) {
      filterReason = '中度过滤：保留核心复杂度步骤';
    } else {
      filterReason = '高度过滤：仅显示最关键步骤';
    }

    return {
      originalCount,
      filteredCount,
      hiddenCount,
      filterReason
    };
  }

  /**
   * 应用智能过滤算法
   * @param steps 详细步骤数组
   * @param options 过滤选项
   * @returns 过滤后的步骤数组
   */
  private applySmartFilter(steps: DetailStep[], options: FilterOptions): DetailStep[] {
    // 1. 首先根据复杂度增量过滤
    const incrementFiltered = steps.filter(step => 
      step.increment >= options.minComplexityIncrement
    );
    
    // 如果过滤后没有步骤，返回空数组
    if (incrementFiltered.length === 0) {
      return [];
    }
    
    // 2. 计算每个步骤的重要性分数
    const scoredSteps = incrementFiltered.map(step => ({
      step,
      importance: this.calculateStepImportance(step)
    }));

    // 3. 按重要性排序
    scoredSteps.sort((a, b) => b.importance - a.importance);

    // 4. 选择重要性最高的步骤
    const retained = scoredSteps
      .slice(0, options.maxContextItems)
      .map(scored => scored.step);

    // 5. 按原始顺序重新排列
    return steps.filter(step => retained.includes(step));
  }

  /**
   * 在已经复杂度过滤的步骤上应用智能过滤算法
   * @param filteredSteps 已经按复杂度增量过滤的步骤数组
   * @param options 过滤选项
   * @returns 进一步过滤后的步骤数组
   */
  private applySmartFilterOnFiltered(filteredSteps: DetailStep[], options: FilterOptions): DetailStep[] {
    // 1. 计算每个步骤的重要性分数
    const scoredSteps = filteredSteps.map(step => ({
      step,
      importance: this.calculateStepImportance(step)
    }));

    // 2. 按重要性排序
    scoredSteps.sort((a, b) => b.importance - a.importance);

    // 3. 选择重要性最高的步骤
    const retained = scoredSteps
      .slice(0, options.maxContextItems)
      .map(scored => scored.step);

    // 4. 按原始顺序重新排列
    return filteredSteps.filter(step => retained.includes(step));
  }

  /**
   * 计算步骤的重要性分数
   * @param step 详细步骤
   * @returns 重要性分数 (0-1)
   */
  private calculateStepImportance(step: DetailStep): number {
    let score = 0;

    // 基础分数：基于复杂度增量
    if (step.increment > 0) {
      score += Math.min(step.increment * 0.2, 0.5); // 最多0.5分
    }

    // 奖励分数：特殊规则类型
    if (this.isHighImportanceRule(step.ruleId)) {
      score += 0.3;
    }

    // 奖励分数：高嵌套层级
    if (step.nestingLevel >= 3) {
      score += Math.min(step.nestingLevel * 0.1, 0.3);
    }

    // 奖励分数：有诊断标记（非NONE）
    if (step.diagnosticMarker) {
      score += 0.2;
    }

    // 奖励分数：有上下文信息
    if (step.context && step.context.trim().length > 0) {
      score += 0.1;
    }

    // 惩罚分数：无增量的步骤
    if (step.increment === 0) {
      score -= 0.2;
    }

    // 确保分数在0-1范围内
    return Math.max(0, Math.min(1, score));
  }

  /**
   * 判断是否为高重要性规则
   * @param ruleId 规则ID
   * @returns 是否为高重要性规则
   */
  private isHighImportanceRule(ruleId: string): boolean {
    const highImportanceRules = [
      'if-statement',
      'switch-statement',
      'for-loop',
      'while-loop',
      'try-catch',
      'logical-operator',
      'function-call',
      'recursion',
      'jsx-hook',
      'jsx-event-handler'
    ];

    return highImportanceRules.some(rule => ruleId.includes(rule));
  }

  /**
   * 选择最重要的前N个步骤
   * @param steps 步骤数组
   * @param count 要选择的数量
   * @returns 选中的步骤数组
   */
  private selectTopSteps(steps: DetailStep[], count: number): DetailStep[] {
    const scoredSteps = steps.map(step => ({
      step,
      importance: this.calculateStepImportance(step)
    }));

    // 按重要性排序并选择前N个
    const selected = scoredSteps
      .sort((a, b) => b.importance - a.importance)
      .slice(0, count)
      .map(scored => scored.step);

    // 按原始顺序重新排列
    return steps.filter(step => selected.includes(step));
  }
}

/**
 * 获取默认的过滤选项
 * @returns 默认过滤选项
 */
export function getDefaultFilterOptions(): FilterOptions {
  return {
    minComplexityIncrement: 1,
    maxContextItems: 10,
    forceShowAll: false
  };
}

/**
 * 创建智能过滤器实例的工厂函数
 * @returns SmartContextFilter实例
 */
export function createSmartContextFilter(): SmartContextFilter {
  return new SmartContextFilter();
}