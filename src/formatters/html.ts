import { BaseFormatter } from './base';
import type { AnalysisResult, FileResult, FunctionResult } from '../core/types';
import type { CLIOptions } from '../config/types';
import { writeFile } from 'fs/promises';

export class HtmlFormatter extends BaseFormatter {
  public override async format(result: AnalysisResult, showDetails?: boolean, options?: CLIOptions): Promise<string> {
    // 应用文件复杂度过滤（异步）
    const filteredResult = await this.applyFileComplexityFilter(result, options);
    const enrichedResult = this.enrichWithSeverity(filteredResult);
    return this.generateHtmlReport(enrichedResult, options);
  }
  
  public override async writeToFile(result: AnalysisResult, outputPath: string, options?: CLIOptions): Promise<void> {
    const htmlContent = await this.format(result, true, options);
    
    try {
      await writeFile(outputPath, htmlContent, 'utf-8');
    } catch (error) {
      throw new Error(`无法写入HTML文件 ${outputPath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  private enrichWithSeverity(result: AnalysisResult): AnalysisResult {
    if (!result.results) {
      return result;
    }
    const enrichedResults = result.results.map(fileResult => ({
      ...fileResult,
      functions: fileResult.functions.map(func => ({
        ...func,
        severity: this.getSeverityLevel(func.complexity)
      }))
    }));
    
    return {
      ...result,
      results: enrichedResults
    };
  }
  
  private generateHtmlReport(result: AnalysisResult, options?: CLIOptions): string {
    const timestamp = new Date().toLocaleString('zh-CN');
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认知复杂度分析报告</title>
    <style>
        ${this.getStyles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>认知复杂度分析报告</h1>
            <p class="timestamp">生成时间: ${timestamp}</p>
        </header>
        
        <section class="summary">
            ${this.generateSummarySection(result.summary, result, options)}
        </section>
        
        <section class="filters">
            ${this.generateFiltersSection()}
        </section>
        
        <section class="results">
            ${this.generateResultsSection(result.results)}
        </section>
        
        <section class="charts">
            ${this.generateChartsSection(result)}
        </section>
    </div>
    
    <script>
        ${this.getJavaScript(result)}
    </script>
</body>
</html>`;
  }
  
  private getStyles(): string {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .timestamp {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .summary {
            background: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .summary h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .filter-summary {
            background: #e6fffa;
            border: 1px solid #4fd1c7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .filter-notice {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .filter-icon {
            font-size: 1.2em;
        }
        
        .filter-text {
            color: #2c7a7b;
            font-weight: 500;
        }
        
        .summary-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .summary-item .icon {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .summary-item .value {
            font-size: 2em;
            font-weight: bold;
            color: #2d3748;
        }
        
        .summary-item .label {
            color: #718096;
            font-size: 0.9em;
        }
        
        .filters {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .filter-group {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .filter-group label {
            font-weight: 500;
            color: #4a5568;
        }
        
        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .results {
            margin-bottom: 30px;
        }
        
        .file-item {
            background: white;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .file-header {
            background: #edf2f7;
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.3s;
        }
        
        .file-header:hover {
            background: #e2e8f0;
        }
        
        .file-info h3 {
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .file-meta {
            color: #718096;
            font-size: 0.9em;
        }
        
        .file-complexity {
            text-align: right;
        }
        
        .complexity-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .complexity-badge.critical {
            background: #fed7d7;
            color: #c53030;
        }
        
        .complexity-badge.warning {
            background: #fefcbf;
            color: #d69e2e;
        }
        
        .complexity-badge.info {
            background: #bee3f8;
            color: #3182ce;
        }
        
        .complexity-badge.normal {
            background: #c6f6d5;
            color: #38a169;
        }
        
        .functions-list {
            padding: 0 20px 20px;
            display: none;
        }
        
        .functions-list.expanded {
            display: block;
        }
        
        .function-item {
            padding: 15px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .function-item.critical {
            border-left-color: #e53e3e;
            background: #fed7d7;
        }
        
        .function-item.warning {
            border-left-color: #d69e2e;
            background: #fefcbf;
        }
        
        .function-item.info {
            border-left-color: #3182ce;
            background: #bee3f8;
        }
        
        .function-info h4 {
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .function-location {
            color: #718096;
            font-size: 0.9em;
            font-family: 'Monaco', 'Menlo', monospace;
        }
        
        .charts {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .charts h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .chart-container {
            margin-bottom: 30px;
        }
        
        .chart {
            height: 300px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            position: relative;
        }
        
        .toggle-btn {
            background: none;
            border: none;
            color: #718096;
            font-size: 1.2em;
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .toggle-btn.expanded {
            transform: rotate(90deg);
        }
        
        .hidden {
            display: none !important;
        }
    `;
  }
  
  private generateSummarySection(summary: AnalysisResult['summary'], result: AnalysisResult, options?: CLIOptions): string {
    // 提供默认的汇总数据，防止 undefined
    const safeSummary = summary || {
      filesAnalyzed: 0,
      functionsAnalyzed: 0,
      averageComplexity: 0,
      totalComplexity: 0,
      highComplexityFunctions: 0
    };

    const filterSummary = this.getFilterSummary(result, options);
    const filterSummaryHtml = filterSummary ? `
        <div class="filter-summary">
            <div class="filter-notice">
                <span class="filter-icon">🔍</span>
                <span class="filter-text">${filterSummary}</span>
            </div>
        </div>
    ` : '';
    
    return `
        <h2>📊 分析汇总</h2>
        ${filterSummaryHtml}
        <div class="summary-grid">
            <div class="summary-item">
                <div class="icon">📁</div>
                <div class="value">${safeSummary.filesAnalyzed}</div>
                <div class="label">分析文件数</div>
            </div>
            <div class="summary-item">
                <div class="icon">🔍</div>
                <div class="value">${safeSummary.functionsAnalyzed}</div>
                <div class="label">分析函数数</div>
            </div>
            <div class="summary-item">
                <div class="icon">📊</div>
                <div class="value">${safeSummary.averageComplexity.toFixed(1)}</div>
                <div class="label">平均复杂度</div>
            </div>
            <div class="summary-item">
                <div class="icon">📈</div>
                <div class="value">${safeSummary.totalComplexity}</div>
                <div class="label">总复杂度</div>
            </div>
            <div class="summary-item">
                <div class="icon">${safeSummary.highComplexityFunctions > 0 ? '⚠️' : '✅'}</div>
                <div class="value">${safeSummary.highComplexityFunctions}</div>
                <div class="label">高复杂度函数</div>
            </div>
        </div>
    `;
  }
  
  private generateFiltersSection(): string {
    return `
        <div class="filter-group">
            <label for="severity-filter">严重级别过滤:</label>
            <select id="severity-filter">
                <option value="all">全部</option>
                <option value="critical">严重</option>
                <option value="warning">警告</option>
                <option value="info">信息</option>
                <option value="normal">正常</option>
            </select>
            
            <label for="file-complexity-threshold">文件复杂度阈值:</label>
            <input type="number" id="file-complexity-threshold" min="0" value="0" />
            
            <label for="function-complexity-threshold">函数复杂度阈值:</label>
            <input type="number" id="function-complexity-threshold" min="0" value="0" />
            
            <label for="search-filter">搜索文件:</label>
            <input type="text" id="search-filter" placeholder="输入文件名或路径..." />
        </div>
    `;
  }
  
  private generateResultsSection(results: FileResult[]): string {
    // 提供默认的结果数据，防止 undefined
    const safeResults = results || [];
    const fileItems = safeResults.map(file => this.generateFileItem(file)).join('');
    return `
        <h2>📋 详细结果</h2>
        <div id="results-container">
            ${fileItems}
        </div>
    `;
  }
  
  private generateFileItem(file: FileResult): string {
    const complexityClass = this.getComplexityClass(file.complexity);
    const functions = file.functions.map(func => this.generateFunctionItem(func)).join('');
    
    return `
        <div class="file-item" data-file-path="${file.filePath}" data-complexity="${file.complexity}">
            <div class="file-header" onclick="toggleFileExpansion(this)">
                <div class="file-info">
                    <h3>📄 ${file.filePath}</h3>
                    <div class="file-meta">
                        ${file.functions.length} 个函数 | 平均复杂度: ${file.averageComplexity.toFixed(1)}
                    </div>
                </div>
                <div class="file-complexity">
                    <span class="complexity-badge ${complexityClass}">${file.complexity}</span>
                    <button class="toggle-btn">▶</button>
                </div>
            </div>
            <div class="functions-list">
                ${functions}
            </div>
        </div>
    `;
  }
  
  private generateFunctionItem(func: FunctionResult): string {
    const complexityClass = this.getComplexityClass(func.complexity);
    
    return `
        <div class="function-item ${complexityClass}" data-complexity="${func.complexity}" data-severity="${func.severity || 'normal'}">
            <div class="function-info">
                <h4>🔧 ${func.name}</h4>
                <div class="function-location">${func.line}:${func.column}</div>
            </div>
            <div class="function-complexity">
                <span class="complexity-badge ${complexityClass}">${func.complexity}</span>
            </div>
        </div>
    `;
  }
  
  private generateChartsSection(result: AnalysisResult): string {
    return `
        <h2>📈 可视化图表</h2>
        <div class="chart-container">
            <h3>复杂度分布直方图</h3>
            <div class="chart" id="complexity-histogram"></div>
        </div>
        <div class="chart-container">
            <h3>文件复杂度排行</h3>
            <div class="chart" id="file-ranking"></div>
        </div>
    `;
  }
  
  private getComplexityClass(complexity: number): string {
    const severity = this.getSeverityLevel(complexity);
    switch (severity) {
      case 'Critical': return 'critical';
      case 'Warning': return 'warning';
      case 'Info': return 'info';
      default: return 'normal';
    }
  }
  
  private getJavaScript(result: AnalysisResult): string {
    return `
        // 全局数据
        const analysisData = ${JSON.stringify(result, null, 2)};
        
        // 切换文件展开/收起
        function toggleFileExpansion(header) {
            const functionsList = header.nextElementSibling;
            const toggleBtn = header.querySelector('.toggle-btn');
            
            if (functionsList.classList.contains('expanded')) {
                functionsList.classList.remove('expanded');
                toggleBtn.classList.remove('expanded');
            } else {
                functionsList.classList.add('expanded');
                toggleBtn.classList.add('expanded');
            }
        }
        
        // 过滤功能
        function applyFilters() {
            const severityFilter = document.getElementById('severity-filter').value;
            const fileComplexityThreshold = parseInt(document.getElementById('file-complexity-threshold').value) || 0;
            const functionComplexityThreshold = parseInt(document.getElementById('function-complexity-threshold').value) || 0;
            const searchFilter = document.getElementById('search-filter').value.toLowerCase();
            
            const fileItems = document.querySelectorAll('.file-item');
            
            fileItems.forEach(item => {
                const filePath = item.dataset.filePath.toLowerCase();
                const fileComplexity = parseInt(item.dataset.complexity);
                
                let showFile = true;
                
                // 搜索过滤
                if (searchFilter && !filePath.includes(searchFilter)) {
                    showFile = false;
                }
                
                // 文件复杂度阈值过滤
                if (fileComplexity < fileComplexityThreshold) {
                    showFile = false;
                }
                
                // 函数复杂度和严重级别过滤
                if (functionComplexityThreshold > 0 || severityFilter !== 'all') {
                    const functionItems = item.querySelectorAll('.function-item');
                    let hasMatchingFunction = false;
                    
                    functionItems.forEach(funcItem => {
                        const funcComplexity = parseInt(funcItem.dataset.complexity);
                        const severity = funcItem.dataset.severity;
                        let showFunction = true;
                        
                        // 函数复杂度阈值过滤
                        if (funcComplexity < functionComplexityThreshold) {
                            showFunction = false;
                        }
                        
                        // 严重级别过滤
                        if (severityFilter !== 'all' && severity !== severityFilter) {
                            showFunction = false;
                        }
                        
                        if (showFunction) {
                            hasMatchingFunction = true;
                            funcItem.style.display = 'flex';
                        } else {
                            funcItem.style.display = 'none';
                        }
                    });
                    
                    // 如果启用了函数级过滤且没有匹配的函数，隐藏整个文件
                    if ((functionComplexityThreshold > 0 || severityFilter !== 'all') && !hasMatchingFunction) {
                        showFile = false;
                    }
                } else {
                    // 如果没有函数级过滤，显示所有函数
                    const functionItems = item.querySelectorAll('.function-item');
                    functionItems.forEach(funcItem => {
                        funcItem.style.display = 'flex';
                    });
                }
                
                item.style.display = showFile ? 'block' : 'none';
            });
        }
        
        // 绑定过滤器事件
        document.getElementById('severity-filter').addEventListener('change', applyFilters);
        document.getElementById('file-complexity-threshold').addEventListener('input', applyFilters);
        document.getElementById('function-complexity-threshold').addEventListener('input', applyFilters);
        document.getElementById('search-filter').addEventListener('input', applyFilters);
        
        // 生成复杂度分布直方图
        function generateComplexityHistogram() {
            const histogramContainer = document.getElementById('complexity-histogram');
            const complexities = [];
            
            analysisData.results.forEach(file => {
                file.functions.forEach(func => {
                    complexities.push(func.complexity);
                });
            });
            
            if (complexities.length === 0) {
                histogramContainer.innerHTML = '<p style="text-align: center; color: #718096;">无数据可显示</p>';
                return;
            }
            
            const maxComplexity = Math.max(...complexities);
            const bins = Math.min(10, maxComplexity);
            const binSize = Math.ceil(maxComplexity / bins);
            const histogram = new Array(bins).fill(0);
            
            complexities.forEach(complexity => {
                const binIndex = Math.min(Math.floor(complexity / binSize), bins - 1);
                histogram[binIndex]++;
            });
            
            const maxCount = Math.max(...histogram);
            let histogramHtml = '<div style="display: flex; align-items: end; height: 200px; gap: 5px;">';
            
            histogram.forEach((count, index) => {
                const height = (count / maxCount) * 180;
                const rangeStart = index * binSize;
                const rangeEnd = (index + 1) * binSize - 1;
                
                histogramHtml += \`
                    <div style="flex: 1; background: #667eea; height: \${height}px; display: flex; flex-direction: column; justify-content: end; align-items: center; border-radius: 4px 4px 0 0; position: relative;">
                        <div style="color: white; font-size: 12px; margin-bottom: 5px;">\${count}</div>
                        <div style="position: absolute; bottom: -25px; font-size: 11px; color: #718096; text-align: center;">\${rangeStart}-\${rangeEnd}</div>
                    </div>
                \`;
            });
            
            histogramHtml += '</div>';
            histogramContainer.innerHTML = histogramHtml;
        }
        
        // 生成文件复杂度排行
        function generateFileRanking() {
            const rankingContainer = document.getElementById('file-ranking');
            const files = [...analysisData.results]
                .sort((a, b) => b.complexity - a.complexity)
                .slice(0, 10);
            
            if (files.length === 0) {
                rankingContainer.innerHTML = '<p style="text-align: center; color: #718096;">无数据可显示</p>';
                return;
            }
            
            const maxComplexity = files[0].complexity;
            let rankingHtml = '';
            
            files.forEach((file, index) => {
                const percentage = (file.complexity / maxComplexity) * 100;
                const fileName = file.filePath.split('/').pop();
                
                rankingHtml += \`
                    <div style="margin-bottom: 15px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span style="font-weight: 500;">\${index + 1}. \${fileName}</span>
                            <span style="font-weight: bold; color: #667eea;">\${file.complexity}</span>
                        </div>
                        <div style="background: #e2e8f0; height: 8px; border-radius: 4px; overflow: hidden;">
                            <div style="background: #667eea; height: 100%; width: \${percentage}%; border-radius: 4px;"></div>
                        </div>
                    </div>
                \`;
            });
            
            rankingContainer.innerHTML = rankingHtml;
        }
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            generateComplexityHistogram();
            generateFileRanking();
        });
    `;
  }
}