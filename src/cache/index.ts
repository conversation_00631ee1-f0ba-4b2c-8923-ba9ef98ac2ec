/**
 * 智能缓存系统索引文件
 * 提供统一的缓存系统访问接口
 */

import { DEFAULT_CACHE_CONFIG } from './types';
import type { CacheConfig } from './types';
import { IntelligentCacheManager } from './manager';
import { AdvancedCacheMonitor } from './monitor';

export { IntelligentCacheManager } from './manager';
export { AdvancedCacheMonitor } from './monitor';
export type {
  CacheManager,
  CacheConfig,
  CacheStatistics,
  CacheSize,
  CacheEntry,
  CacheStrategy,
  LayerConfig,
  TypeInfo,
  CacheKeyGenerator,
  CachePerformanceMonitor,
  IntelligentCacheAnalyzer,
  CacheOptimization,
  AccessPattern,
  PredictionResult,
  CacheEvent,
  CacheListener,
} from './types';

import type {
  CacheStatistics,
  CacheOptimization,
} from './types';
export { DEFAULT_CACHE_CONFIG } from './types';

/**
 * 缓存系统工厂
 * 用于创建和配置缓存系统组件
 */
export class CacheSystemFactory {
  /**
   * 创建智能缓存管理器
   */
  static createCacheManager(config?: Partial<CacheConfig>): IntelligentCacheManager {
    const finalConfig = config ? { ...DEFAULT_CACHE_CONFIG, ...config } : DEFAULT_CACHE_CONFIG;
    return new IntelligentCacheManager(finalConfig);
  }

  /**
   * 创建高级缓存监控器
   */
  static createCacheMonitor(alertThresholds?: {
    lowHitRate?: number;
    highMemoryUsage?: number;
    slowAccessTime?: number;
  }): AdvancedCacheMonitor {
    return new AdvancedCacheMonitor(alertThresholds);
  }

  /**
   * 创建预配置的缓存系统
   * 适合特定场景的缓存配置
   */
  static createPresetCacheSystem(preset: CachePreset): {
    manager: IntelligentCacheManager;
    monitor: AdvancedCacheMonitor;
  } {
    const config = this.getPresetConfig(preset);
    const manager = this.createCacheManager(config);
    const monitor = this.createCacheMonitor();
    
    return { manager, monitor };
  }

  /**
   * 创建开发环境缓存系统
   * 更激进的缓存策略，更详细的监控
   */
  static createDevelopmentCacheSystem(): {
    manager: IntelligentCacheManager;
    monitor: AdvancedCacheMonitor;
  } {
    return this.createPresetCacheSystem('development');
  }

  /**
   * 创建生产环境缓存系统
   * 保守的缓存策略，重点关注稳定性
   */
  static createProductionCacheSystem(): {
    manager: IntelligentCacheManager;
    monitor: AdvancedCacheMonitor;
  } {
    return this.createPresetCacheSystem('production');
  }

  /**
   * 创建测试环境缓存系统
   * 小容量，快速失效，便于测试
   */
  static createTestingCacheSystem(): {
    manager: IntelligentCacheManager;
    monitor: AdvancedCacheMonitor;
  } {
    return this.createPresetCacheSystem('testing');
  }

  private static getPresetConfig(preset: CachePreset): Partial<CacheConfig> {
    switch (preset) {
      case 'development':
        return {
          enableCaching: true,
          enableMetrics: true,
          nodeCache: {
            enabled: true,
            strategy: {
              evictionPolicy: 'LRU',
              maxSize: 10000,
              defaultTTL: 600000, // 10分钟
              cleanupInterval: 30000, // 30秒
              preWarmEnabled: true,
              preWarmSize: 200,
            },
            enableCompression: false,
          },
          ruleCache: {
            enabled: true,
            strategy: {
              evictionPolicy: 'LRU',
              maxSize: 20000,
              defaultTTL: 900000, // 15分钟
              cleanupInterval: 60000, // 1分钟
              preWarmEnabled: true,
            },
            enableCompression: false,
          },
        };

      case 'production':
        return {
          enableCaching: true,
          enableMetrics: true,
          nodeCache: {
            enabled: true,
            strategy: {
              evictionPolicy: 'LRU',
              maxSize: 5000,
              defaultTTL: 300000, // 5分钟
              cleanupInterval: 120000, // 2分钟
              preWarmEnabled: true,
              preWarmSize: 100,
            },
            enableCompression: true,
          },
          ruleCache: {
            enabled: true,
            strategy: {
              evictionPolicy: 'LRU',
              maxSize: 10000,
              defaultTTL: 600000, // 10分钟
              cleanupInterval: 180000, // 3分钟
              preWarmEnabled: false,
            },
            enableCompression: true,
          },
        };

      case 'testing':
        return {
          enableCaching: true,
          enableMetrics: false,
          nodeCache: {
            enabled: true,
            strategy: {
              evictionPolicy: 'LRU',
              maxSize: 100,
              defaultTTL: 10000, // 10秒
              cleanupInterval: 5000, // 5秒
              preWarmEnabled: false,
            },
            enableCompression: false,
          },
          ruleCache: {
            enabled: true,
            strategy: {
              evictionPolicy: 'LRU',
              maxSize: 200,
              defaultTTL: 15000, // 15秒
              cleanupInterval: 10000, // 10秒
              preWarmEnabled: false,
            },
            enableCompression: false,
          },
        };

      default:
        throw new Error(`Unknown cache preset: ${preset}`);
    }
  }
}

/**
 * 缓存系统预设类型
 */
export type CachePreset = 'development' | 'production' | 'testing';

/**
 * 缓存系统实用工具
 */
export class CacheUtils {
  /**
   * 估算对象的内存大小
   */
  static estimateObjectSize(obj: any): number {
    try {
      const jsonString = JSON.stringify(obj);
      return new Blob([jsonString]).size;
    } catch {
      // 如果对象不能序列化，返回估算值
      return 1024;
    }
  }

  /**
   * 生成缓存键的哈希值
   */
  static hashCacheKey(key: string): string {
    let hash = 0;
    for (let i = 0; i < key.length; i++) {
      const char = key.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * 验证缓存配置
   */
  static validateCacheConfig(config: Partial<CacheConfig>): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查基本配置
    if (config.nodeCache?.strategy?.maxSize && config.nodeCache.strategy.maxSize < 100) {
      warnings.push('Node cache size is very small, may impact performance');
    }

    if (config.ruleCache?.strategy?.maxSize && config.ruleCache.strategy.maxSize < 100) {
      warnings.push('Rule cache size is very small, may impact performance');
    }

    // 检查TTL设置
    if (config.nodeCache?.strategy?.defaultTTL && config.nodeCache.strategy.defaultTTL < 1000) {
      warnings.push('Node cache TTL is very short, may cause excessive cache turnover');
    }

    // 检查清理间隔
    if (config.nodeCache?.strategy?.cleanupInterval && 
        config.nodeCache?.strategy?.defaultTTL &&
        config.nodeCache.strategy.cleanupInterval > config.nodeCache.strategy.defaultTTL) {
      errors.push('Cleanup interval should be shorter than TTL');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 格式化缓存统计信息
   */
  static formatCacheStatistics(stats: CacheStatistics): string {
    const hitRate = (stats.hitRate * 100).toFixed(1);
    const memoryMB = (stats.memoryUsage / 1024 / 1024).toFixed(2);
    
    return `
缓存统计信息:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
总体性能:
  总请求数: ${stats.totalRequests.toLocaleString()}
  命中数: ${stats.hits.toLocaleString()}
  未命中数: ${stats.misses.toLocaleString()}  
  命中率: ${hitRate}%
  内存使用: ${memoryMB} MB
  平均命中时间: ${stats.averageHitTime.toFixed(2)} ms
  平均未命中时间: ${stats.averageMissTime.toFixed(2)} ms

分层统计:
  节点缓存: ${(stats.nodeCache.hitRate * 100).toFixed(1)}% (${stats.nodeCache.size}/${stats.nodeCache.maxSize})
  规则缓存: ${(stats.ruleCache.hitRate * 100).toFixed(1)}% (${stats.ruleCache.size}/${stats.ruleCache.maxSize})
  类型缓存: ${(stats.typeCache.hitRate * 100).toFixed(1)}% (${stats.typeCache.size}/${stats.typeCache.maxSize})
  模式缓存: ${(stats.patternCache.hitRate * 100).toFixed(1)}% (${stats.patternCache.size}/${stats.patternCache.maxSize})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    `.trim();
  }

  /**
   * 创建缓存性能报告
   */
  static generatePerformanceReport(
    stats: CacheStatistics,
    optimizations?: CacheOptimization[]
  ): string {
    let report = this.formatCacheStatistics(stats);
    
    if (optimizations && optimizations.length > 0) {
      report += '\n\n优化建议:\n';
      optimizations.forEach((opt, index) => {
        report += `${index + 1}. [${opt.impact.toUpperCase()}] ${opt.description}\n`;
      });
    }

    return report;
  }
}

// 导出便捷函数
export const createCacheManager = CacheSystemFactory.createCacheManager;
export const createCacheMonitor = CacheSystemFactory.createCacheMonitor;
export const createDevelopmentCache = CacheSystemFactory.createDevelopmentCacheSystem;
export const createProductionCache = CacheSystemFactory.createProductionCacheSystem;
export const createTestingCache = CacheSystemFactory.createTestingCacheSystem;