/**
 * 智能缓存系统类型定义
 * 支持多层级缓存策略和智能失效机制
 */

import type { Node } from '@swc/core';
import type { NodeAnalysis, RuleResult, AnalysisContext, CacheInvalidationReason } from '../engine/types';

// 缓存管理器接口
export interface CacheManager {
  // 节点级缓存
  getCachedNodeResult(nodeHash: string): Promise<NodeAnalysis | null>;
  setCachedNodeResult(nodeHash: string, result: NodeAnalysis): Promise<void>;
  
  // 规则结果缓存
  getCachedRuleResult(ruleId: string, nodeHash: string): Promise<RuleResult | null>;
  setCachedRuleResult(ruleId: string, nodeHash: string, result: RuleResult): Promise<void>;
  
  // 类型级缓存
  getCachedTypeInfo(nodeType: string): TypeInfo | null;
  peekCachedTypeInfo(nodeType: string): TypeInfo | null;
  setCachedTypeInfo(nodeType: string, info: TypeInfo): void;
  
  // 模式缓存
  getCachedByPattern(pattern: string, node: Node, context: AnalysisContext): Promise<NodeAnalysis | null>;
  setCachedByPattern(pattern: string, result: NodeAnalysis): Promise<void>;
  
  // 缓存管理
  invalidateCache(reason: CacheInvalidationReason, affectedKeys?: string[]): void;
  clearCache(): void;
  getHitRate(): CacheStatistics;
  getSize(): CacheSize;
  
  // 缓存预热
  preWarmCache(nodes: Node[]): Promise<void>;
}

// 类型信息
export interface TypeInfo {
  nodeType: string;
  isApplicable(node: Node): boolean;
  defaultComplexity: number;
  exemptionRules: string[];
  lastUpdated: number;
  hitCount: number;
}

// 缓存统计
export interface CacheStatistics {
  // 整体统计
  totalRequests: number;
  hits: number;
  misses: number;
  hitRate: number;
  
  // 分层统计
  nodeCache: LayerStatistics;
  ruleCache: LayerStatistics;
  typeCache: LayerStatistics;
  patternCache: LayerStatistics;
  
  // 性能统计
  averageHitTime: number;
  averageMissTime: number;
  memoryUsage: number;
}

// 层级统计
export interface LayerStatistics {
  requests: number;
  hits: number;
  misses: number;
  hitRate: number;
  size: number;
  maxSize: number;
}

// 缓存大小信息
export interface CacheSize {
  nodeCache: number;
  ruleCache: number;
  typeCache: number;
  patternCache: number;
  total: number;
  maxTotal: number;
}

// 缓存条目
export interface CacheEntry<T> {
  key: string;
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  ttl?: number;
  metadata?: CacheEntryMetadata;
}

// 缓存条目元数据
export interface CacheEntryMetadata {
  nodeType?: string;
  ruleId?: string;
  pattern?: string;
  contextHash?: string;
  size?: number;
  [key: string]: any;
}

// 缓存策略
export interface CacheStrategy {
  // 淘汰策略
  evictionPolicy: 'LRU' | 'LFU' | 'FIFO' | 'TTL';
  
  // 容量管理
  maxSize: number;
  maxMemory?: number;
  
  // TTL设置
  defaultTTL?: number;
  cleanupInterval?: number;
  
  // 预热策略
  preWarmEnabled: boolean;
  preWarmSize?: number;
}

// 缓存配置
export interface CacheConfig {
  // 分层配置
  nodeCache: LayerConfig;
  ruleCache: LayerConfig;
  typeCache: LayerConfig;
  patternCache: LayerConfig;
  
  // 全局配置
  enableCaching: boolean;
  enablePersistence: boolean;
  persistencePath?: string;
  enableCompression: boolean;
  enableMetrics: boolean;
}

// 层级配置
export interface LayerConfig {
  enabled: boolean;
  strategy: CacheStrategy;
  enableCompression?: boolean;
  enablePersistence?: boolean;
}

// 缓存键生成器
export interface CacheKeyGenerator {
  generateNodeKey(node: Node, context: AnalysisContext): string;
  generateRuleKey(ruleId: string, node: Node, context: AnalysisContext): string;
  generateTypeKey(nodeType: string, configHash: string): string;
  generatePatternKey(pattern: string, nodeHash: string): string;
  generateContextHash(context: AnalysisContext): string;
}

// 缓存序列化器
export interface CacheSerializer {
  serialize<T>(value: T): string | Buffer;
  deserialize<T>(data: string | Buffer): T;
  getSize(value: any): number;
}

// 缓存持久化接口
export interface CachePersistence {
  save(key: string, value: any): Promise<void>;
  load(key: string): Promise<any | null>;
  delete(key: string): Promise<void>;
  clear(): Promise<void>;
  exists(key: string): Promise<boolean>;
  getAllKeys(): Promise<string[]>;
}

// LRU缓存接口
export interface LRUCache<K, V> {
  get(key: K): V | undefined;
  set(key: K, value: V): void;
  has(key: K): boolean;
  delete(key: K): boolean;
  clear(): void;
  size: number;
  maxSize: number;
}

// 缓存事件
export type CacheEvent = 
  | { type: 'hit', key: string, layer: string }
  | { type: 'miss', key: string, layer: string }
  | { type: 'set', key: string, layer: string, size: number }
  | { type: 'evicted', key: string, layer: string, reason: string }
  | { type: 'invalidated', keys: string[], reason: CacheInvalidationReason }
  | { type: 'cleared', layer?: string };

// 缓存监听器
export interface CacheListener {
  onCacheEvent(event: CacheEvent): void;
}

// 缓存性能监控
export interface CachePerformanceMonitor {
  recordHit(layer: string, key: string, accessTime: number): void;
  recordMiss(layer: string, key: string, accessTime: number): void;
  recordSet(layer: string, key: string, size: number): void;
  recordEviction(layer: string, key: string, reason: string): void;
  getStatistics(): CacheStatistics;
  reset(): void;
}

// 智能缓存分析器
export interface IntelligentCacheAnalyzer {
  // 模式检测
  detectNodePattern(node: Node): string;
  detectAccessPattern(key: string, timestamps: number[]): AccessPattern;
  
  // 预测
  predictCacheNeeds(context: AnalysisContext): PredictionResult;
  predictEvictionCandidates(): string[];
  
  // 优化建议
  suggestCacheOptimizations(): CacheOptimization[];
}

// 访问模式
export interface AccessPattern {
  type: 'frequent' | 'burst' | 'periodic' | 'rare';
  frequency: number;
  lastAccess: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

// 预测结果
export interface PredictionResult {
  likelyKeys: string[];
  confidence: number;
  reason: string;
}

// 缓存优化建议
export interface CacheOptimization {
  type: 'size' | 'strategy' | 'ttl' | 'pattern';
  description: string;
  impact: 'low' | 'medium' | 'high';
  config?: Partial<CacheConfig>;
}

// 默认缓存配置
export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  nodeCache: {
    enabled: true,
    strategy: {
      evictionPolicy: 'LRU',
      maxSize: 5000,
      defaultTTL: 300000, // 5分钟
      preWarmEnabled: true,
      preWarmSize: 100,
    },
    enableCompression: false,
    enablePersistence: false,
  },
  ruleCache: {
    enabled: true,
    strategy: {
      evictionPolicy: 'LRU',
      maxSize: 10000,
      defaultTTL: 600000, // 10分钟
      preWarmEnabled: false,
    },
    enableCompression: false,
    enablePersistence: false,
  },
  typeCache: {
    enabled: true,
    strategy: {
      evictionPolicy: 'LFU',
      maxSize: 1000,
      preWarmEnabled: true,
      preWarmSize: 50,
    },
    enableCompression: false,
    enablePersistence: true,
  },
  patternCache: {
    enabled: true,
    strategy: {
      evictionPolicy: 'LRU',
      maxSize: 2000,
      defaultTTL: 900000, // 15分钟
      preWarmEnabled: false,
    },
    enableCompression: true,
    enablePersistence: false,
  },
  enableCaching: true,
  enablePersistence: false,
  enableCompression: false,
  enableMetrics: true,
};