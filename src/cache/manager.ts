/**
 * 智能缓存管理器实现
 * 支持多层级缓存策略、智能失效和性能监控
 */

import type { Node, Module } from '@swc/core';
import type { NodeAnalysis, RuleResult, AnalysisContext, CacheInvalidationReason } from '../engine/types';
import type { 
  CacheManager, 
  TypeInfo, 
  CacheStatistics, 
  CacheSize, 
  CacheConfig, 
  CacheEntry,
  LRUCache,
  CachePerformanceMonitor,
  IntelligentCacheAnalyzer,
  CacheKeyGenerator,
  AccessPattern,
  PredictionResult,
  CacheOptimization,
} from './types';
import { DEFAULT_CACHE_CONFIG } from './types';
import { createHash } from 'crypto';

/**
 * 增强型LRU缓存实现
 * 支持TTL、内存限制和智能淘汰策略
 */
class EnhancedLRUCache<K, V> implements LRUCache<K, V> {
  private cache = new Map<K, CacheEntry<V>>();
  private accessOrder: K[] = [];
  private totalMemoryUsage = 0;
  public readonly maxSize: number;
  private readonly maxMemory?: number;
  private readonly defaultTTL?: number;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(maxSize: number, options?: { maxMemory?: number; defaultTTL?: number; cleanupInterval?: number }) {
    this.maxSize = maxSize;
    this.maxMemory = options?.maxMemory;
    this.defaultTTL = options?.defaultTTL;
    
    // 启动定期清理过期条目
    if (options?.cleanupInterval) {
      this.cleanupTimer = setInterval(() => this.cleanupExpired(), options.cleanupInterval);
      
      // 使用 unref() 防止定时器阻止进程退出
      this.cleanupTimer.unref();
    }
  }

  get(key: K): V | undefined {
    const entry = this.cache.get(key);
    if (entry) {
      // 检查TTL
      if (this.isExpired(entry)) {
        this.delete(key);
        return undefined;
      }
      
      // 更新访问统计
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.updateAccessOrder(key);
      return entry.value;
    }
    return undefined;
  }

  set(key: K, value: V, ttl?: number): void {
    const timestamp = Date.now();
    const estimatedSize = this.estimateSize(value);
    
    if (this.cache.has(key)) {
      // 更新已存在的条目
      const oldEntry = this.cache.get(key)!;
      this.totalMemoryUsage -= (oldEntry.metadata?.size || 0);
      
      const newEntry: CacheEntry<V> = {
        key: key as string,
        value,
        timestamp,
        accessCount: oldEntry.accessCount + 1,
        lastAccessed: timestamp,
        ttl: ttl || this.defaultTTL,
        metadata: { size: estimatedSize },
      };
      
      this.cache.set(key, newEntry);
      this.totalMemoryUsage += estimatedSize;
      this.updateAccessOrder(key);
    } else {
      // 检查是否需要淘汰
      this.evictIfNecessary(estimatedSize);
      
      const newEntry: CacheEntry<V> = {
        key: key as string,
        value,
        timestamp,
        accessCount: 1,
        lastAccessed: timestamp,
        ttl: ttl || this.defaultTTL,
        metadata: { size: estimatedSize },
      };
      
      this.cache.set(key, newEntry);
      this.totalMemoryUsage += estimatedSize;
      this.accessOrder.push(key);
    }
  }

  has(key: K): boolean {
    const entry = this.cache.get(key);
    return entry ? !this.isExpired(entry) : false;
  }

  delete(key: K): boolean {
    const entry = this.cache.get(key);
    if (entry && this.cache.delete(key)) {
      this.totalMemoryUsage -= (entry.metadata?.size || 0);
      this.accessOrder = this.accessOrder.filter(k => k !== key);
      return true;
    }
    return false;
  }

  clear(): void {
    this.cache.clear();
    this.accessOrder = [];
    this.totalMemoryUsage = 0;
  }

  get size(): number {
    return this.cache.size;
  }

  get memoryUsage(): number {
    return this.totalMemoryUsage;
  }

  // 获取缓存统计信息
  getStats() {
    return {
      size: this.size,
      maxSize: this.maxSize,
      memoryUsage: this.totalMemoryUsage,
      maxMemory: this.maxMemory,
      hitRate: this.calculateHitRate(),
    };
  }

  // 手动触发过期清理
  cleanupExpired(): number {
    let cleanedCount = 0;
    const now = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry, now)) {
        this.delete(key);
        cleanedCount++;
      }
    }
    
    return cleanedCount;
  }

  // 析构函数
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clear();
  }

  private isExpired(entry: CacheEntry<V>, now: number = Date.now()): boolean {
    return entry.ttl ? (now - entry.timestamp) > entry.ttl : false;
  }

  private updateAccessOrder(key: K): void {
    this.accessOrder = this.accessOrder.filter(k => k !== key);
    this.accessOrder.push(key);
  }

  private evictIfNecessary(newEntrySize: number): void {
    // 基于大小淘汰
    while (this.size >= this.maxSize) {
      this.evictLRU();
    }
    
    // 基于内存淘汰
    if (this.maxMemory) {
      while (this.totalMemoryUsage + newEntrySize > this.maxMemory && this.size > 0) {
        this.evictLRU();
      }
    }
    
    // 清理过期条目
    this.cleanupExpired();
  }

  private evictLRU(): void {
    if (this.accessOrder.length > 0) {
      const lruKey = this.accessOrder.shift()!;
      this.delete(lruKey);
    }
  }

  private estimateSize(value: V): number {
    try {
      return JSON.stringify(value).length * 2; // 粗略估算
    } catch {
      return 1024; // 默认大小
    }
  }

  private calculateHitRate(): number {
    let totalHits = 0;
    let totalRequests = 0;
    
    for (const entry of this.cache.values()) {
      totalRequests += entry.accessCount;
      if (entry.accessCount > 1) {
        totalHits += entry.accessCount - 1;
      }
    }
    
    return totalRequests > 0 ? totalHits / totalRequests : 0;
  }
}

/**
 * 缓存键生成器实现
 */
class CacheKeyGeneratorImpl implements CacheKeyGenerator {
  generateNodeKey(node: Node, context: AnalysisContext): string {
    const nodeHash = this.hashNode(node);
    const contextHash = this.generateContextHash(context);
    return `node:${nodeHash}:${contextHash}`;
  }

  generateRuleKey(ruleId: string, node: Node, context: AnalysisContext): string {
    const nodeHash = this.hashNode(node);
    const contextHash = this.generateContextHash(context);
    return `rule:${ruleId}:${nodeHash}:${contextHash}`;
  }

  generateTypeKey(nodeType: string, configHash: string): string {
    return `type:${nodeType}:${configHash}`;
  }

  generatePatternKey(pattern: string, nodeHash: string): string {
    return `pattern:${pattern}:${nodeHash}`;
  }

  generateContextHash(context: AnalysisContext): string {
    const contextData = {
      nestingLevel: context.nestingLevel,
      jsxMode: context.jsxMode,
      enabledRules: Array.from(Object.values(context.rules)).flat().sort(),
    };
    return createHash('sha256').update(JSON.stringify(contextData)).digest('hex').substring(0, 16);
  }

  private hashNode(node: Node): string {
    const nodeData = {
      type: node.type,
      span: (node as any).span,
      // 添加更多用于唯一标识节点的属性
    };
    return createHash('sha256').update(JSON.stringify(nodeData)).digest('hex').substring(0, 16);
  }
}

/**
 * 缓存性能监控器实现
 */
class CachePerformanceMonitorImpl implements CachePerformanceMonitor {
  private statistics: CacheStatistics;

  constructor() {
    this.statistics = this.createEmptyStatistics();
  }

  recordHit(layer: string, key: string, accessTime: number): void {
    this.statistics.totalRequests++;
    this.statistics.hits++;
    
    const layerStats = this.getLayerStats(layer);
    layerStats.requests++;
    layerStats.hits++;
    layerStats.hitRate = layerStats.hits / layerStats.requests;
    
    this.updateAverageTime('hit', accessTime);
    this.updateOverallHitRate();
  }

  recordMiss(layer: string, key: string, accessTime: number): void {
    this.statistics.totalRequests++;
    this.statistics.misses++;
    
    const layerStats = this.getLayerStats(layer);
    layerStats.requests++;
    layerStats.misses++;
    layerStats.hitRate = layerStats.hits / layerStats.requests;
    
    this.updateAverageTime('miss', accessTime);
    this.updateOverallHitRate();
  }

  recordSet(layer: string, key: string, size: number): void {
    const layerStats = this.getLayerStats(layer);
    layerStats.size++;
    // 更新内存使用统计
    this.statistics.memoryUsage += size;
  }

  recordEviction(layer: string, key: string, reason: string): void {
    const layerStats = this.getLayerStats(layer);
    layerStats.size--;
  }

  getStatistics(): CacheStatistics {
    return { ...this.statistics };
  }

  reset(): void {
    this.statistics = this.createEmptyStatistics();
  }

  private createEmptyStatistics(): CacheStatistics {
    return {
      totalRequests: 0,
      hits: 0,
      misses: 0,
      hitRate: 0,
      nodeCache: { requests: 0, hits: 0, misses: 0, hitRate: 0, size: 0, maxSize: 5000 },
      ruleCache: { requests: 0, hits: 0, misses: 0, hitRate: 0, size: 0, maxSize: 10000 },
      typeCache: { requests: 0, hits: 0, misses: 0, hitRate: 0, size: 0, maxSize: 1000 },
      patternCache: { requests: 0, hits: 0, misses: 0, hitRate: 0, size: 0, maxSize: 2000 },
      averageHitTime: 0,
      averageMissTime: 0,
      memoryUsage: 0,
    };
  }

  private getLayerStats(layer: string) {
    switch (layer) {
      case 'node': return this.statistics.nodeCache;
      case 'rule': return this.statistics.ruleCache;
      case 'type': return this.statistics.typeCache;
      case 'pattern': return this.statistics.patternCache;
      default: return this.statistics.nodeCache;
    }
  }

  private updateAverageTime(type: 'hit' | 'miss', time: number): void {
    if (type === 'hit') {
      this.statistics.averageHitTime = 
        (this.statistics.averageHitTime * (this.statistics.hits - 1) + time) / this.statistics.hits;
    } else {
      this.statistics.averageMissTime = 
        (this.statistics.averageMissTime * (this.statistics.misses - 1) + time) / this.statistics.misses;
    }
  }

  private updateOverallHitRate(): void {
    this.statistics.hitRate = this.statistics.hits / this.statistics.totalRequests;
  }
}

/**
 * 智能缓存分析器实现
 */
class IntelligentCacheAnalyzerImpl implements IntelligentCacheAnalyzer {
  private accessPatterns = new Map<string, number[]>();
  private nodePatterns = new Map<string, string>();

  detectNodePattern(node: Node): string {
    // 检测常见的AST节点模式
    const cacheKey = `${node.type}`;
    
    if (this.nodePatterns.has(cacheKey)) {
      return this.nodePatterns.get(cacheKey)!;
    }

    let pattern = 'unknown';
    
    // JSX 相关模式
    if (node.type.startsWith('JSX')) {
      pattern = 'jsx-element';
    }
    // 控制流模式
    else if (['IfStatement', 'WhileStatement', 'ForStatement', 'SwitchStatement'].includes(node.type)) {
      pattern = 'control-flow';
    }
    // 逻辑表达式模式
    else if (['LogicalExpression', 'ConditionalExpression'].includes(node.type)) {
      pattern = 'logical-expression';
    }
    // 函数模式  
    else if (['FunctionDeclaration', 'ArrowFunctionExpression', 'FunctionExpression'].includes(node.type)) {
      pattern = 'function';
    }
    // 基础模式
    else {
      pattern = 'basic';
    }

    this.nodePatterns.set(cacheKey, pattern);
    return pattern;
  }

  detectAccessPattern(key: string, timestamps: number[]): AccessPattern {
    if (timestamps.length < 2) {
      return { type: 'rare', frequency: 1, lastAccess: timestamps[0] || 0, trend: 'stable' };
    }

    // 计算访问频率
    const lastTs = timestamps[timestamps.length - 1];
    const firstTs = timestamps[0];
    if (!lastTs || !firstTs) {
      return { type: 'rare', frequency: 0, lastAccess: firstTs || 0, trend: 'stable' };
    }
    const timeSpan = lastTs - firstTs;
    const frequency = timestamps.length / (timeSpan / 1000); // 每秒访问次数

    // 分析访问趋势
    const recentAccesses = timestamps.slice(-Math.min(5, timestamps.length));
    const intervals = [];
    for (let i = 1; i < recentAccesses.length; i++) {
      const current = recentAccesses[i];
      const previous = recentAccesses[i - 1];
      if (current && previous) {
        intervals.push(current - previous);
      }
    }

    let trend: AccessPattern['trend'] = 'stable';
    if (intervals.length > 1) {
      const avgEarly = intervals.slice(0, Math.floor(intervals.length / 2)).reduce((a, b) => a + b, 0) / Math.floor(intervals.length / 2);
      const avgLater = intervals.slice(Math.floor(intervals.length / 2)).reduce((a, b) => a + b, 0) / Math.ceil(intervals.length / 2);
      
      if (avgLater < avgEarly * 0.8) {
        trend = 'increasing';
      } else if (avgLater > avgEarly * 1.2) {
        trend = 'decreasing';
      }
    }

    // 确定访问类型
    let type: AccessPattern['type'] = 'rare';
    if (frequency > 10) {
      type = 'frequent';
    } else if (frequency > 1 && intervals.length > 0) {
      // 检查是否为突发访问
      const maxInterval = Math.max(...intervals);
      const minInterval = Math.min(...intervals);
      if (maxInterval > minInterval * 10) {
        type = 'burst';
      } else {
        type = 'periodic';
      }
    }

    return {
      type,
      frequency,
      lastAccess: timestamps[timestamps.length - 1] ?? 0,
      trend,
    };
  }

  predictCacheNeeds(context: AnalysisContext): PredictionResult {
    const likelyKeys: string[] = [];
    
    // 基于当前分析上下文预测可能需要的缓存键
    const { currentFunction, nestingLevel, jsxMode } = context;
    
    // 预测常见的节点类型
    const commonTypes = ['IfStatement', 'LogicalExpression', 'ConditionalExpression'];
    if (jsxMode === 'strict' || jsxMode === 'standard') {
      commonTypes.push('JSXElement', 'JSXFragment', 'JSXAttribute');
    }
    
    for (const type of commonTypes) {
      likelyKeys.push(`type:${type}:config-hash`);
    }

    // 基于嵌套级别预测
    if (nestingLevel > 2) {
      likelyKeys.push(`pattern:nested:*`);
    }

    return {
      likelyKeys,
      confidence: likelyKeys.length > 0 ? 0.7 : 0.3,
      reason: `Based on analysis context: JSX mode=${jsxMode}, nesting=${nestingLevel}`,
    };
  }

  predictEvictionCandidates(): string[] {
    // 基于访问模式预测哪些缓存条目可能被淘汰
    const candidates: string[] = [];
    
    for (const [key, timestamps] of this.accessPatterns.entries()) {
      const pattern = this.detectAccessPattern(key, timestamps);
      
      // 很少访问且趋势下降的条目
      if (pattern.type === 'rare' && pattern.trend === 'decreasing') {
        candidates.push(key);
      }
      
      // 最后访问时间超过10分钟的条目
      if (Date.now() - pattern.lastAccess > 10 * 60 * 1000) {
        candidates.push(key);
      }
    }
    
    return candidates;
  }

  suggestCacheOptimizations(): CacheOptimization[] {
    const optimizations: CacheOptimization[] = [];
    
    // 分析访问模式以提供优化建议
    const patterns = Array.from(this.accessPatterns.entries()).map(([key, timestamps]) => ({
      key,
      pattern: this.detectAccessPattern(key, timestamps),
    }));

    // 高频访问建议增加缓存大小
    const highFrequencyCount = patterns.filter(p => p.pattern.type === 'frequent').length;
    if (highFrequencyCount > 100) {
      optimizations.push({
        type: 'size',
        description: 'Increase cache size to accommodate high-frequency access patterns',
        impact: 'high',
        config: {
          nodeCache: {
            enabled: true,
            strategy: {
              evictionPolicy: 'LRU',
              maxSize: 10000,
              preWarmEnabled: true,
            },
          },
        },
      });
    }

    // TTL优化建议
    const rareAccessCount = patterns.filter(p => p.pattern.type === 'rare').length;
    if (rareAccessCount > patterns.length * 0.6) {
      optimizations.push({
        type: 'ttl',
        description: 'Reduce TTL for better memory utilization due to many rare-access items',
        impact: 'medium',
        config: {
          nodeCache: {
            enabled: true,
            strategy: {
              evictionPolicy: 'LRU',
              maxSize: 5000,
              defaultTTL: 60000, // 1分钟
              preWarmEnabled: false,
            },
          },
        },
      });
    }

    return optimizations;
  }

  // 记录访问模式
  recordAccess(key: string): void {
    const timestamps = this.accessPatterns.get(key) || [];
    timestamps.push(Date.now());
    
    // 只保留最近50次访问记录
    if (timestamps.length > 50) {
      timestamps.shift();
    }
    
    this.accessPatterns.set(key, timestamps);
  }
}
/**
 * 智能缓存管理器实现
 */
export class IntelligentCacheManager implements CacheManager {
  private config: CacheConfig;
  private keyGenerator: CacheKeyGenerator;
  private performanceMonitor: CachePerformanceMonitor;
  private analyzer: IntelligentCacheAnalyzerImpl;
  
  // 分层缓存
  private nodeCache: EnhancedLRUCache<string, NodeAnalysis>;
  private ruleCache: EnhancedLRUCache<string, RuleResult>;
  private typeCache: Map<string, TypeInfo>;
  private patternCache: EnhancedLRUCache<string, NodeAnalysis>;

  constructor(config: CacheConfig = DEFAULT_CACHE_CONFIG) {
    this.config = config;
    this.keyGenerator = new CacheKeyGeneratorImpl();
    this.performanceMonitor = new CachePerformanceMonitorImpl();
    this.analyzer = new IntelligentCacheAnalyzerImpl();
    
    // 初始化分层缓存
    this.nodeCache = new EnhancedLRUCache(
      config.nodeCache.strategy.maxSize,
      {
        maxMemory: config.nodeCache.strategy.maxMemory,
        defaultTTL: config.nodeCache.strategy.defaultTTL,
        cleanupInterval: config.nodeCache.strategy.cleanupInterval,
      }
    );
    
    this.ruleCache = new EnhancedLRUCache(
      config.ruleCache.strategy.maxSize,
      {
        maxMemory: config.ruleCache.strategy.maxMemory,
        defaultTTL: config.ruleCache.strategy.defaultTTL,
        cleanupInterval: config.ruleCache.strategy.cleanupInterval,
      }
    );
    
    this.typeCache = new Map();
    
    this.patternCache = new EnhancedLRUCache(
      config.patternCache.strategy.maxSize,
      {
        maxMemory: config.patternCache.strategy.maxMemory,
        defaultTTL: config.patternCache.strategy.defaultTTL,
        cleanupInterval: config.patternCache.strategy.cleanupInterval,
      }
    );
  }

  async getCachedNodeResult(nodeHash: string): Promise<NodeAnalysis | null> {
    if (!this.config.nodeCache.enabled) {
      return null;
    }

    const startTime = performance.now();
    const result = this.nodeCache.get(nodeHash);
    const accessTime = performance.now() - startTime;

    // 记录访问模式供智能分析器使用
    this.analyzer.recordAccess(nodeHash);

    if (result) {
      this.performanceMonitor.recordHit('node', nodeHash, accessTime);
      return result;
    } else {
      this.performanceMonitor.recordMiss('node', nodeHash, accessTime);
      return null;
    }
  }

  async setCachedNodeResult(nodeHash: string, result: NodeAnalysis): Promise<void> {
    if (!this.config.nodeCache.enabled) {
      return;
    }

    this.nodeCache.set(nodeHash, result);
    const size = this.estimateSize(result);
    this.performanceMonitor.recordSet('node', nodeHash, size);
  }

  async getCachedRuleResult(ruleId: string, nodeHash: string): Promise<RuleResult | null> {
    if (!this.config.ruleCache.enabled) {
      return null;
    }

    const key = `${ruleId}:${nodeHash}`;
    const startTime = performance.now();
    const result = this.ruleCache.get(key);
    const accessTime = performance.now() - startTime;

    // 记录访问模式
    this.analyzer.recordAccess(key);

    if (result) {
      this.performanceMonitor.recordHit('rule', key, accessTime);
      return result;
    } else {
      this.performanceMonitor.recordMiss('rule', key, accessTime);
      return null;
    }
  }

  async setCachedRuleResult(ruleId: string, nodeHash: string, result: RuleResult): Promise<void> {
    if (!this.config.ruleCache.enabled) {
      return;
    }

    const key = `${ruleId}:${nodeHash}`;
    this.ruleCache.set(key, result);
    const size = this.estimateSize(result);
    this.performanceMonitor.recordSet('rule', key, size);
  }

  getCachedTypeInfo(nodeType: string): TypeInfo | null {
    if (!this.config.typeCache.enabled) {
      return null;
    }

    const startTime = performance.now();
    const result = this.typeCache.get(nodeType);
    const accessTime = performance.now() - startTime;

    if (result) {
      this.performanceMonitor.recordHit('type', nodeType, accessTime);
      result.hitCount++;
      return result;
    } else {
      this.performanceMonitor.recordMiss('type', nodeType, accessTime);
      return null;
    }
  }

  /**
   * 获取缓存的类型信息，但不增加访问计数（仅用于检查）
   */
  peekCachedTypeInfo(nodeType: string): TypeInfo | null {
    if (!this.config.typeCache.enabled) {
      return null;
    }

    return this.typeCache.get(nodeType) || null;
  }

  setCachedTypeInfo(nodeType: string, info: TypeInfo): void {
    if (!this.config.typeCache.enabled) {
      return;
    }

    this.typeCache.set(nodeType, {
      ...info,
      lastUpdated: Date.now(),
      hitCount: 0,
    });
    const size = this.estimateSize(info);
    this.performanceMonitor.recordSet('type', nodeType, size);
  }

  async getCachedByPattern(pattern: string, node: Node, context: AnalysisContext): Promise<NodeAnalysis | null> {
    if (!this.config.patternCache.enabled) {
      return null;
    }

    const nodeHash = this.keyGenerator.generateNodeKey(node, context);
    const key = this.keyGenerator.generatePatternKey(pattern, nodeHash);
    
    const startTime = performance.now();
    const result = this.patternCache.get(key);
    const accessTime = performance.now() - startTime;

    if (result) {
      this.performanceMonitor.recordHit('pattern', key, accessTime);
      return result;
    } else {
      this.performanceMonitor.recordMiss('pattern', key, accessTime);
      return null;
    }
  }

  async setCachedByPattern(pattern: string, result: NodeAnalysis): Promise<void> {
    if (!this.config.patternCache.enabled) {
      return;
    }

    const nodeHash = this.keyGenerator.generateNodeKey(result.node, {} as AnalysisContext);
    const key = this.keyGenerator.generatePatternKey(pattern, nodeHash);
    
    this.patternCache.set(key, result);
    const size = this.estimateSize(result);
    this.performanceMonitor.recordSet('pattern', key, size);
  }

  invalidateCache(reason: CacheInvalidationReason, affectedKeys?: string[]): void {
    switch (reason) {
      case 'config':
        this.clearCache();
        break;
      case 'rule':
        this.ruleCache.clear();
        this.nodeCache.clear(); // 节点分析可能受规则变更影响
        break;
      case 'node':
        if (affectedKeys) {
          affectedKeys.forEach(key => {
            this.nodeCache.delete(key);
            this.patternCache.delete(key);
          });
        }
        break;
      case 'manual':
        this.clearCache();
        break;
    }
  }

  clearCache(): void {
    this.nodeCache.clear();
    this.ruleCache.clear();
    this.typeCache.clear();
    this.patternCache.clear();
    this.performanceMonitor.reset();
  }

  getHitRate(): CacheStatistics {
    return this.performanceMonitor.getStatistics();
  }

  getSize(): CacheSize {
    return {
      nodeCache: this.nodeCache.size,
      ruleCache: this.ruleCache.size,
      typeCache: this.typeCache.size,
      patternCache: this.patternCache.size,
      total: this.nodeCache.size + this.ruleCache.size + this.typeCache.size + this.patternCache.size,
      maxTotal: this.config.nodeCache.strategy.maxSize + 
                this.config.ruleCache.strategy.maxSize + 
                this.config.typeCache.strategy.maxSize + 
                this.config.patternCache.strategy.maxSize,
    };
  }

  async preWarmCache(nodes: Node[]): Promise<void> {
    if (!this.config.nodeCache.strategy.preWarmEnabled) {
      return;
    }

    // 预分析常见的节点类型
    const commonTypes = this.identifyCommonNodeTypes(nodes);
    
    for (const nodeType of commonTypes) {
      if (!this.typeCache.has(nodeType)) {
        const typeInfo: TypeInfo = {
          nodeType,
          isApplicable: (node: Node) => node.type === nodeType,
          defaultComplexity: this.getDefaultComplexityForType(nodeType),
          exemptionRules: this.getExemptionRulesForType(nodeType),
          lastUpdated: Date.now(),
          hitCount: 0,
        };
        this.setCachedTypeInfo(nodeType, typeInfo);
      }
    }
    
    // 基于智能分析器预测缓存需求
    if (nodes.length > 0) {
      const firstNode = nodes[0];
      if (firstNode) {
        const sampleContext = this.createSampleContext(firstNode);
        const prediction = this.analyzer.predictCacheNeeds(sampleContext);
        
        // 预热预测的缓存键对应的类型信息
        for (const key of prediction.likelyKeys) {
          if (key.startsWith('type:')) {
            const nodeType = key.split(':')[1];
            if (nodeType && !this.typeCache.has(nodeType)) {
              const typeInfo: TypeInfo = {
                nodeType,
                isApplicable: (node: Node) => node.type === nodeType,
                defaultComplexity: this.getDefaultComplexityForType(nodeType),
                exemptionRules: this.getExemptionRulesForType(nodeType),
                lastUpdated: Date.now(),
                hitCount: 0,
              };
              this.setCachedTypeInfo(nodeType, typeInfo);
            }
          }
        }
      }
    }
  }

  // 增量分析支持
  async performIncrementalAnalysis(
    changedNodes: Node[], 
    removedNodeHashes: string[]
  ): Promise<void> {
    // 清理已删除节点的缓存
    for (const nodeHash of removedNodeHashes) {
      this.nodeCache.delete(nodeHash);
      
      // 同时清理相关的规则缓存
      for (const [key] of Array.from(this.ruleCache['cache'].keys())) {
        if (key && key.includes(nodeHash)) {
          this.ruleCache.delete(key);
        }
      }
    }
    
    // 为新增/修改的节点预热缓存
    if (changedNodes.length > 0) {
      const validNodes = changedNodes.filter((node): node is Node => node != null);
      if (validNodes.length > 0) {
        await this.preWarmCache(validNodes);
      }
    }
    
    // 基于变更模式优化缓存配置
    this.optimizeCacheForChanges(changedNodes, removedNodeHashes);
  }

  // 获取缓存优化建议
  getCacheOptimizations(): CacheOptimization[] {
    return this.analyzer.suggestCacheOptimizations();
  }

  // 析构函数，清理资源
  destroy(): void {
    this.nodeCache.destroy();
    this.ruleCache.destroy();
    this.patternCache.destroy();
    this.typeCache.clear();
  }

  private createSampleContext(node: Node): AnalysisContext {
    // 创建一个简化的分析上下文用于预测
    return {
      filePath: 'sample',
      fileContent: '',
      ast: {} as Module,
      currentFunction: node,
      functionName: 'sample',
      nestingLevel: 1,
      config: {} as any,
      jsxMode: node.type.startsWith('JSX') ? 'strict' : 'lenient',
      rules: {} as any,
      cache: this,
      metrics: {} as any,
      plugins: [],
      customData: new Map(),
    };
  }

  private optimizeCacheForChanges(changedNodes: Node[], removedNodeHashes: string[]): void {
    // 如果变更频繁，建议增加缓存大小
    if (changedNodes.length > 50) {
      // 记录高变更频率，供优化建议使用
      this.analyzer.recordAccess('high-change-frequency');
    }
    
    // 如果删除节点较多，触发垃圾回收
    if (removedNodeHashes.length > 20) {
      this.nodeCache.cleanupExpired();
      this.ruleCache.cleanupExpired();
      this.patternCache.cleanupExpired();
    }
  }

  private identifyCommonNodeTypes(nodes: Node[]): string[] {
    const typeCount = new Map<string, number>();
    
    nodes.forEach(node => {
      const count = typeCount.get(node.type) || 0;
      typeCount.set(node.type, count + 1);
    });

    // 返回出现频率最高的类型
    return Array.from(typeCount.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, this.config.typeCache.strategy.preWarmSize || 50)
      .map(([type]) => type);
  }

  private getDefaultComplexityForType(nodeType: string): number {
    const complexityMap: Record<string, number> = {
      'IfStatement': 1,
      'WhileStatement': 1,
      'ForStatement': 1,
      'SwitchStatement': 1,
      'ConditionalExpression': 1,
      'LogicalExpression': 1,
      'JSXElement': 0,
      'JSXFragment': 0,
      'JSXAttribute': 0,
    };
    return complexityMap[nodeType] || 0;
  }

  private getExemptionRulesForType(nodeType: string): string[] {
    const exemptionMap: Record<string, string[]> = {
      'JSXElement': ['jsx.structural.exemption'],
      'JSXFragment': ['jsx.structural.exemption'],
      'JSXAttribute': ['jsx.structural.exemption'],
      'JSXText': ['jsx.structural.exemption'],
    };
    return exemptionMap[nodeType] || [];
  }

  private estimateSize(obj: any): number {
    // 简单的对象大小估算
    return JSON.stringify(obj).length * 2; // 粗略估算，每个字符占2字节
  }
}