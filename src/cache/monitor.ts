/**
 * 高级缓存监控和性能分析工具
 * 提供详细的缓存性能分析、优化建议和实时监控
 */

import type { 
  CacheStatistics, 
  CachePerformanceMonitor,
  CacheOptimization,
  LayerStatistics,
  CacheEvent,
  CacheListener
} from './types';

/**
 * 高级缓存性能监控器
 * 支持实时监控、趋势分析和智能优化建议
 */
export class AdvancedCacheMonitor implements CachePerformanceMonitor {
  private statistics: CacheStatistics;
  private listeners: CacheListener[] = [];
  private performanceHistory: PerformanceSnapshot[] = [];
  private alertThresholds: AlertThresholds;
  private monitoringInterval?: NodeJS.Timeout;

  constructor(alertThresholds?: Partial<AlertThresholds>) {
    this.statistics = this.createEmptyStatistics();
    this.alertThresholds = {
      lowHitRate: 0.3,
      highMemoryUsage: 0.8,
      slowAccessTime: 100,
      ...alertThresholds,
    };
    
    // 开始性能监控
    this.startPerformanceMonitoring();
  }

  recordHit(layer: string, key: string, accessTime: number): void {
    this.statistics.totalRequests++;
    this.statistics.hits++;
    
    const layerStats = this.getLayerStats(layer);
    layerStats.requests++;
    layerStats.hits++;
    layerStats.hitRate = layerStats.hits / layerStats.requests;
    
    this.updateAverageTime('hit', accessTime);
    this.updateOverallHitRate();
    
    // 发送事件
    this.emitEvent({ type: 'hit', key, layer });
    
    // 检查性能警告
    this.checkPerformanceAlerts(layer, accessTime);
  }

  recordMiss(layer: string, key: string, accessTime: number): void {
    this.statistics.totalRequests++;
    this.statistics.misses++;
    
    const layerStats = this.getLayerStats(layer);
    layerStats.requests++;
    layerStats.misses++;
    layerStats.hitRate = layerStats.hits / layerStats.requests;
    
    this.updateAverageTime('miss', accessTime);
    this.updateOverallHitRate();
    
    // 发送事件
    this.emitEvent({ type: 'miss', key, layer });
    
    // 检查性能警告
    this.checkPerformanceAlerts(layer, accessTime);
  }

  recordSet(layer: string, key: string, size: number): void {
    const layerStats = this.getLayerStats(layer);
    layerStats.size++;
    this.statistics.memoryUsage += size;
    
    // 发送事件
    this.emitEvent({ type: 'set', key, layer, size });
  }

  recordEviction(layer: string, key: string, reason: string): void {
    const layerStats = this.getLayerStats(layer);
    layerStats.size--;
    
    // 发送事件
    this.emitEvent({ type: 'evicted', key, layer, reason });
  }

  getStatistics(): CacheStatistics {
    return { ...this.statistics };
  }

  reset(): void {
    this.statistics = this.createEmptyStatistics();
    this.performanceHistory = [];
  }

  // 添加事件监听器
  addListener(listener: CacheListener): void {
    this.listeners.push(listener);
  }

  // 移除事件监听器
  removeListener(listener: CacheListener): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 获取性能趋势分析
  getPerformanceTrends(): PerformanceTrends {
    if (this.performanceHistory.length < 2) {
      return {
        hitRateTrend: 'stable',
        memoryUsageTrend: 'stable',
        accessTimeTrend: 'stable',
        recommendations: [],
      };
    }

    const recent = this.performanceHistory.slice(-10);
    const older = this.performanceHistory.slice(-20, -10);
    
    if (older.length === 0) {
      return {
        hitRateTrend: 'stable',
        memoryUsageTrend: 'stable', 
        accessTimeTrend: 'stable',
        recommendations: [],
      };
    }

    const recentAvg = this.calculateAverages(recent);
    const olderAvg = this.calculateAverages(older);

    const hitRateTrend = this.determineTrend(recentAvg.hitRate, olderAvg.hitRate);
    const memoryUsageTrend = this.determineTrend(recentAvg.memoryUsage, olderAvg.memoryUsage);
    const accessTimeTrend = this.determineTrend(recentAvg.accessTime, olderAvg.accessTime, true);

    const recommendations = this.generateTrendRecommendations(
      hitRateTrend,
      memoryUsageTrend,
      accessTimeTrend
    );

    return {
      hitRateTrend,
      memoryUsageTrend,
      accessTimeTrend,
      recommendations,
    };
  }

  // 获取缓存效率报告
  getEfficiencyReport(): CacheEfficiencyReport {
    const stats = this.statistics;
    
    return {
      overallEfficiency: this.calculateOverallEfficiency(),
      layerEfficiency: {
        node: this.calculateLayerEfficiency(stats.nodeCache),
        rule: this.calculateLayerEfficiency(stats.ruleCache),
        type: this.calculateLayerEfficiency(stats.typeCache),
        pattern: this.calculateLayerEfficiency(stats.patternCache),
      },
      memoryUtilization: this.calculateMemoryUtilization(),
      performanceScore: this.calculatePerformanceScore(),
      improvementSuggestions: this.generateImprovementSuggestions(),
    };
  }

  // 实时性能监控
  startRealTimeMonitoring(callback: (metrics: RealTimeMetrics) => void): () => void {
    const interval = setInterval(() => {
      const metrics: RealTimeMetrics = {
        timestamp: Date.now(),
        hitRate: this.statistics.hitRate,
        memoryUsage: this.statistics.memoryUsage,
        averageAccessTime: (this.statistics.averageHitTime + this.statistics.averageMissTime) / 2,
        totalRequests: this.statistics.totalRequests,
        alerts: this.checkCurrentAlerts(),
      };
      
      callback(metrics);
    }, 1000);

    // 清理函数
    return () => {
      clearInterval(interval);
    };
  }

  // 停止监控
  destroy(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    this.listeners = [];
    this.performanceHistory = [];
  }

  private createEmptyStatistics(): CacheStatistics {
    return {
      totalRequests: 0,
      hits: 0,
      misses: 0,
      hitRate: 0,
      nodeCache: { requests: 0, hits: 0, misses: 0, hitRate: 0, size: 0, maxSize: 5000 },
      ruleCache: { requests: 0, hits: 0, misses: 0, hitRate: 0, size: 0, maxSize: 10000 },
      typeCache: { requests: 0, hits: 0, misses: 0, hitRate: 0, size: 0, maxSize: 1000 },
      patternCache: { requests: 0, hits: 0, misses: 0, hitRate: 0, size: 0, maxSize: 2000 },
      averageHitTime: 0,
      averageMissTime: 0,
      memoryUsage: 0,
    };
  }

  private getLayerStats(layer: string): LayerStatistics {
    switch (layer) {
      case 'node': return this.statistics.nodeCache;
      case 'rule': return this.statistics.ruleCache;
      case 'type': return this.statistics.typeCache;
      case 'pattern': return this.statistics.patternCache;
      default: return this.statistics.nodeCache;
    }
  }

  private updateAverageTime(type: 'hit' | 'miss', time: number): void {
    if (type === 'hit') {
      this.statistics.averageHitTime = 
        (this.statistics.averageHitTime * (this.statistics.hits - 1) + time) / this.statistics.hits;
    } else {
      this.statistics.averageMissTime = 
        (this.statistics.averageMissTime * (this.statistics.misses - 1) + time) / this.statistics.misses;
    }
  }

  private updateOverallHitRate(): void {
    this.statistics.hitRate = this.statistics.hits / this.statistics.totalRequests;
  }

  private emitEvent(event: CacheEvent): void {
    this.listeners.forEach(listener => {
      try {
        listener.onCacheEvent(event);
      } catch (error) {
        console.error('Cache event listener error:', error);
      }
    });
  }

  private checkPerformanceAlerts(layer: string, accessTime: number): void {
    // 检查访问时间警告
    if (accessTime > this.alertThresholds.slowAccessTime) {
      console.warn(`Slow cache access detected: ${layer} took ${accessTime}ms`);
    }

    // 检查命中率警告
    const layerStats = this.getLayerStats(layer);
    if (layerStats.requests > 100 && layerStats.hitRate < this.alertThresholds.lowHitRate) {
      console.warn(`Low hit rate detected: ${layer} hit rate is ${layerStats.hitRate.toFixed(2)}`);
    }
  }

  private startPerformanceMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      const snapshot: PerformanceSnapshot = {
        timestamp: Date.now(),
        hitRate: this.statistics.hitRate,
        memoryUsage: this.statistics.memoryUsage,
        accessTime: (this.statistics.averageHitTime + this.statistics.averageMissTime) / 2,
        totalRequests: this.statistics.totalRequests,
      };
      
      this.performanceHistory.push(snapshot);
      
      // 只保留最近1000个快照
      if (this.performanceHistory.length > 1000) {
        this.performanceHistory.shift();
      }
    }, 5000); // 每5秒记录一次
    
    // 使用 unref() 防止定时器阻止进程退出
    this.monitoringInterval.unref();
  }

  private calculateAverages(snapshots: PerformanceSnapshot[]): PerformanceSnapshot {
    const count = snapshots.length;
    return {
      timestamp: Date.now(),
      hitRate: snapshots.reduce((sum, s) => sum + s.hitRate, 0) / count,
      memoryUsage: snapshots.reduce((sum, s) => sum + s.memoryUsage, 0) / count,
      accessTime: snapshots.reduce((sum, s) => sum + s.accessTime, 0) / count,
      totalRequests: snapshots.reduce((sum, s) => sum + s.totalRequests, 0) / count,
    };
  }

  private determineTrend(recent: number, older: number, inverse = false): TrendDirection {
    const threshold = 0.05; // 5% 变化阈值
    const diff = (recent - older) / older;
    
    if (Math.abs(diff) < threshold) {
      return 'stable';
    }
    
    if (inverse) {
      return diff > 0 ? 'decreasing' : 'improving';
    } else {
      return diff > 0 ? 'improving' : 'decreasing';
    }
  }

  private generateTrendRecommendations(
    hitRateTrend: TrendDirection,
    memoryUsageTrend: TrendDirection,
    accessTimeTrend: TrendDirection
  ): string[] {
    const recommendations: string[] = [];

    if (hitRateTrend === 'decreasing') {
      recommendations.push('Consider increasing cache size or adjusting TTL settings');
    }

    if (memoryUsageTrend === 'decreasing' && false) { // 内存使用趋势相反逻辑
      recommendations.push('Monitor memory usage and consider implementing more aggressive eviction policies');
    }

    if (accessTimeTrend === 'decreasing') {
      recommendations.push('Cache access time is increasing, check for memory pressure or CPU bottlenecks');
    }

    return recommendations;
  }

  private calculateOverallEfficiency(): number {
    const hitRateScore = this.statistics.hitRate * 40; // 40%权重
    const memoryScore = (1 - this.statistics.memoryUsage / 100000000) * 30; // 30%权重，假设100MB为满分
    const speedScore = Math.max(0, (100 - this.statistics.averageHitTime) / 100) * 30; // 30%权重
    
    return Math.max(0, Math.min(100, hitRateScore + memoryScore + speedScore));
  }

  private calculateLayerEfficiency(layer: LayerStatistics): number {
    if (layer.requests === 0) return 0;
    
    const hitRateScore = layer.hitRate * 50;
    const utilizationScore = (layer.size / layer.maxSize) * 30;
    const balanceScore = Math.max(0, 20 - Math.abs(50 - utilizationScore)); // 50%利用率最优
    
    return Math.max(0, Math.min(100, hitRateScore + utilizationScore + balanceScore));
  }

  private calculateMemoryUtilization(): number {
    return (this.statistics.memoryUsage / (100 * 1024 * 1024)) * 100; // 假设100MB为基准
  }

  private calculatePerformanceScore(): number {
    const efficiency = this.calculateOverallEfficiency();
    const accessTime = Math.max(0, (100 - this.statistics.averageHitTime) / 100 * 100);
    const stability = this.calculateStabilityScore();
    
    return (efficiency * 0.4 + accessTime * 0.3 + stability * 0.3);
  }

  private calculateStabilityScore(): number {
    if (this.performanceHistory.length < 10) return 50;
    
    const recent = this.performanceHistory.slice(-10);
    const hitRateVariance = this.calculateVariance(recent.map(s => s.hitRate));
    const accessTimeVariance = this.calculateVariance(recent.map(s => s.accessTime));
    
    // 低方差意味着高稳定性
    const hitRateStability = Math.max(0, 100 - hitRateVariance * 1000);
    const accessTimeStability = Math.max(0, 100 - accessTimeVariance / 10);
    
    return (hitRateStability + accessTimeStability) / 2;
  }

  private calculateVariance(values: number[]): number {
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
    return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private generateImprovementSuggestions(): string[] {
    const suggestions: string[] = [];
    const stats = this.statistics;

    if (stats.hitRate < 0.5) {
      suggestions.push('Low hit rate detected. Consider increasing cache size or optimizing cache keys.');
    }

    if (stats.averageHitTime > 50) {
      suggestions.push('High access time detected. Consider memory optimization or cache layer tuning.');
    }

    if (stats.memoryUsage > 50 * 1024 * 1024) { // 50MB
      suggestions.push('High memory usage detected. Consider implementing compression or more aggressive eviction.');
    }

    const utilizationRates = [
      stats.nodeCache.size / stats.nodeCache.maxSize,
      stats.ruleCache.size / stats.ruleCache.maxSize,
      stats.typeCache.size / stats.typeCache.maxSize,
      stats.patternCache.size / stats.patternCache.maxSize,
    ];

    const underutilized = utilizationRates.filter(rate => rate < 0.2).length;
    const overutilized = utilizationRates.filter(rate => rate > 0.9).length;

    if (underutilized > 1) {
      suggestions.push('Some cache layers are underutilized. Consider reducing their size to save memory.');
    }

    if (overutilized > 0) {
      suggestions.push('Some cache layers are nearly full. Consider increasing their size or implementing better eviction policies.');
    }

    return suggestions;
  }

  private checkCurrentAlerts(): CacheAlert[] {
    const alerts: CacheAlert[] = [];

    if (this.statistics.hitRate < this.alertThresholds.lowHitRate) {
      alerts.push({
        level: 'warning',
        message: `Low hit rate: ${(this.statistics.hitRate * 100).toFixed(1)}%`,
        timestamp: Date.now(),
      });
    }

    if (this.statistics.memoryUsage > 80 * 1024 * 1024) { // 80MB
      alerts.push({
        level: 'error',
        message: `High memory usage: ${(this.statistics.memoryUsage / 1024 / 1024).toFixed(1)}MB`,
        timestamp: Date.now(),
      });
    }

    if (this.statistics.averageHitTime > this.alertThresholds.slowAccessTime) {
      alerts.push({
        level: 'warning',
        message: `Slow access time: ${this.statistics.averageHitTime.toFixed(1)}ms`,
        timestamp: Date.now(),
      });
    }

    return alerts;
  }
}

// 类型定义
interface PerformanceSnapshot {
  timestamp: number;
  hitRate: number;
  memoryUsage: number;
  accessTime: number;
  totalRequests: number;
}

interface AlertThresholds {
  lowHitRate: number;
  highMemoryUsage: number;
  slowAccessTime: number;
}

type TrendDirection = 'improving' | 'stable' | 'decreasing';

interface PerformanceTrends {
  hitRateTrend: TrendDirection;
  memoryUsageTrend: TrendDirection;
  accessTimeTrend: TrendDirection;
  recommendations: string[];
}

interface CacheEfficiencyReport {
  overallEfficiency: number;
  layerEfficiency: {
    node: number;
    rule: number;
    type: number;
    pattern: number;
  };
  memoryUtilization: number;
  performanceScore: number;
  improvementSuggestions: string[];
}

interface RealTimeMetrics {
  timestamp: number;
  hitRate: number;
  memoryUsage: number;
  averageAccessTime: number;
  totalRequests: number;
  alerts: CacheAlert[];
}

interface CacheAlert {
  level: 'info' | 'warning' | 'error';
  message: string;
  timestamp: number;
}