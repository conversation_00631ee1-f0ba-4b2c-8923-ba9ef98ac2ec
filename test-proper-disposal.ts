import { ComplexityCalculator } from './src/core/calculator';

async function testProperDisposal() {
  console.log('测试正确的资源清理方案...');
  
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  const calculator = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: true
  });
  
  try {
    const results = await calculator.calculateCode(code, 'test.js');
    console.log('总复杂度:', results[0]?.complexity);
    console.log('✅ 计算完成');
    
    // 正确清理资源
    await calculator.dispose();
    console.log('✅ 资源清理完成');
    
    console.log('⏱️ 等待5秒，看看进程是否会自然退出...');
    
    // 设置一个5秒的超时，如果进程没有自然退出，就打印警告
    setTimeout(() => {
      console.log('⚠️ 进程仍在运行，可能仍有未清理的资源');
      process.exit(1);
    }, 5000);
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

// 添加进程退出监听器
process.on('beforeExit', () => {
  console.log('🎉 进程即将自然退出 - 资源清理成功！');
});

testProperDisposal().catch((error) => {
  console.error('测试失败:', error);
  process.exit(1);
});