/**
 * BaseRule 验证测试脚本
 * 验证 BaseRule 基类的所有功能是否正常工作
 */

import { BaseRule } from './src/rules/base-rule';
import type { Node } from '@swc/core';
import type { AnalysisContext, RuleResult } from './src/engine/types';

// 创建一个测试用的规则实现
class TestRule extends BaseRule {
  readonly id = 'test-rule';
  readonly name = 'Test Rule';
  readonly priority = 100;

  canHandle(node: Node): boolean {
    return node.type === 'IfStatement';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    // 测试各种功能
    
    // 1. 基础复杂度结果
    if (context.nestingLevel === 0) {
      return this.createComplexityResult(
        node,
        1,
        'Base if statement',
        true,
        this.generateSuggestions('if statement', 1)
      );
    }

    // 2. 嵌套惩罚应用
    if (context.nestingLevel > 0) {
      return this.createComplexityResultWithNesting(
        node,
        1,
        context,
        'Nested if statement',
        true,
        this.generateNestingSuggestions('if statement', context.nestingLevel)
      );
    }

    // 3. 豁免情况
    return this.createExemptionResult(node, 'Test exemption');
  }
}

// 创建模拟的上下文
function createMockContext(nestingLevel: number = 0): AnalysisContext {
  return {
    filePath: 'test.ts',
    fileContent: 'test code',
    ast: {} as any,
    functionName: 'testFunc',
    nestingLevel,
    config: {} as any,
    jsxMode: 'enabled' as any,
    rules: {} as any,
    cache: {
      getCachedRuleResult: async () => null,
      setCachedRuleResult: async () => {},
    } as any,
    metrics: {} as any,
    plugins: [],
    customData: new Map(),
  };
}

// 创建模拟节点
function createMockIfNode(): Node {
  return {
    type: 'IfStatement',
    span: { start: 0, end: 10, ctxt: 0 },
  } as any;
}

async function verifyBaseRuleFeatures() {
  console.log('🧪 BaseRule 功能验证测试');
  console.log('======================');

  const rule = new TestRule();
  const node = createMockIfNode();

  try {
    // 测试 1: 基础复杂度计算
    console.log('\n✅ 测试 1: 基础复杂度计算');
    const context1 = createMockContext(0);
    const result1 = await rule.evaluate(node, context1);
    console.log(`   - 规则ID: ${result1.ruleId}`);
    console.log(`   - 复杂度: ${result1.complexity}`);
    console.log(`   - 是否豁免: ${result1.isExempted}`);
    console.log(`   - 是否增加嵌套: ${result1.shouldIncreaseNesting}`);
    console.log(`   - 建议数量: ${result1.suggestions.length}`);

    // 测试 2: 嵌套惩罚应用
    console.log('\n✅ 测试 2: 嵌套惩罚应用');  
    const context2 = createMockContext(2);
    const result2 = await rule.evaluate(node, context2);
    console.log(`   - 规则ID: ${result2.ruleId}`);
    console.log(`   - 复杂度 (应该是 1 + 2 = 3): ${result2.complexity}`);
    console.log(`   - 元数据包含嵌套信息: ${JSON.stringify(result2.metadata)}`);
    console.log(`   - 嵌套建议数量: ${result2.suggestions.length}`);

    // 测试 3: 节点类型检查
    console.log('\n✅ 测试 3: 节点类型检查功能'); 
    console.log(`   - canHandle IfStatement: ${rule.canHandle(node)}`);
    console.log(`   - isConditionalNode: ${(rule as any).isConditionalNode(node)}`);
    console.log(`   - isLoopNode: ${(rule as any).isLoopNode(node)}`);
    console.log(`   - shouldIncreaseNesting: ${(rule as any).shouldIncreaseNesting(node)}`);

    // 测试 4: 辅助方法
    console.log('\n✅ 测试 4: 辅助方法');
    const baseComplexity = 1;
    const nestingLevel = 3;
    const penaltyApplied = (rule as any).applyNestingPenalty(baseComplexity, nestingLevel);
    console.log(`   - 基础复杂度: ${baseComplexity}`);
    console.log(`   - 嵌套级别: ${nestingLevel}`);
    console.log(`   - 应用惩罚后: ${penaltyApplied}`);

    // 测试 5: 建议生成
    console.log('\n✅ 测试 5: 建议生成');
    const suggestions = (rule as any).generateSuggestions('if statement', 4);
    const nestingSuggestions = (rule as any).generateNestingSuggestions('if statement', 3);
    console.log(`   - 基础建议数量: ${suggestions.length}`);
    console.log(`   - 嵌套建议数量: ${nestingSuggestions.length}`);
    if (suggestions.length > 0) {
      console.log(`   - 建议示例: ${suggestions[0].message}`);
    }

    console.log('\n🎉 BaseRule 验证完成！');
    console.log('所有核心功能正常工作：');
    console.log('  ✅ 抽象规则接口实现');
    console.log('  ✅ 复杂度计算和结果创建');
    console.log('  ✅ 嵌套惩罚机制');
    console.log('  ✅ 节点类型检查方法');
    console.log('  ✅ 建议生成系统');
    console.log('  ✅ 元数据管理');

  } catch (error) {
    console.error('❌ BaseRule 验证失败:', error);
    process.exit(1);
  }
}

verifyBaseRuleFeatures().catch(console.error);