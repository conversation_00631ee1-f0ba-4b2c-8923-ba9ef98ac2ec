import { analyzeFile } from './src/index';

async function testRefactoring() {
  console.log('=== 测试重构后的 analyzeFile 函数 ===');
  
  const testCode = `
function simpleFunction() {
  return true;
}

function withIf() {
  if (condition) {
    return true;
  }
  return false;
}

function nested() {
  for (let i = 0; i < 10; i++) {        // +1
    if (condition) {                    // +1 + 1(嵌套) = +2
      while (otherCondition) {          // +1 + 2(嵌套) = +3
        break;
      }
    }
  }
}
  `;
  
  // 写入临时文件
  await Bun.write('test-temp.ts', testCode);
  
  try {
    console.log('\n--- 分析测试代码 ---');
    const result = await analyzeFile('test-temp.ts');
    
    console.log('文件路径:', result.filePath);
    console.log('总复杂度:', result.complexity);
    console.log('平均复杂度:', result.averageComplexity);
    console.log('函数数量:', result.functions.length);
    
    console.log('\n--- 函数详情 ---');
    result.functions.forEach((func, index) => {
      console.log(`${index + 1}. ${func.name} (${func.line}:${func.column}) - 复杂度: ${func.complexity}`);
    });
    
    // 验证预期结果
    const expectedResults = [
      { name: 'simpleFunction', complexity: 0 },
      { name: 'withIf', complexity: 1 },
      { name: 'nested', complexity: 6 } // 1 + 2 + 3
    ];
    
    console.log('\n--- 验证结果 ---');
    expectedResults.forEach((expected, index) => {
      const actual = result.functions[index];
      const match = actual?.name === expected.name && actual?.complexity === expected.complexity;
      console.log(`${expected.name}: 期望复杂度 ${expected.complexity}, 实际复杂度 ${actual?.complexity} - ${match ? '✅' : '❌'}`);
    });
    
    if (result.functions.length === 3) {
      console.log('\n✅ 重构后的 analyzeFile 函数工作正常！');
    } else {
      console.log('\n❌ 函数数量不匹配，期望 3 个，实际 ' + result.functions.length + ' 个');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 清理临时文件
    try {
      await Bun.write('test-temp.ts', '');
    } catch {}
  }
}

testRefactoring();