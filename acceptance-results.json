{"功能验收": [{"name": "文本格式详细输出验收", "passed": true, "details": {"hasCorrectFormat": true, "hasLineNumbers": true, "hasComplexityIncrement": true, "hasCumulativeScore": true, "hasRuleId": true, "hasNestingLevel": true, "sampleOutput": "🧠 认知复杂度分析工具\n=========================\nNo config file found, using default configuration\nSuccessfully registered 22 complexity rules\n[1/1 100%] 分析: test-acceptance-temp/basic-test.ts\n🧠 认知复杂度分析汇总\n=========================\n📁 分析文件数: 1\n🔍 分析函数数: 2\n📊 平均复杂度: 7.50\n📈 总复杂度: 15\n✅ 高复杂度函数: 0\n\n📋 详细分析结果:\n──────────────────────────────────────────────────\n\n📄 test-acceptance-temp/basic-test.ts (复杂度: 15.00, 平均: 7.50)\n  ⚠️  complexFunction (10:0) - 最终复杂度: 14.00\n      - L11: +1 (累计: 1) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)\n      - L11: +1 (累计: 2) - Logical OR (||) operators increase complexity by 1 [logical-or] (嵌套层级: 0)\n      - L11: +1 (累计: 3) - Logical OR (||) operators increase complexity by 1 [logical-or] (嵌套层级: 0)\n      - L16: +1 (累计: 4) - For-of loops increase complexity by 1 + nesting penalty [for-of-statement] (嵌套层级: 0)\n      - L17: +2 (累计: 6) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)\n      - L17: +1 (累计: 7) - Logical AND (&&) operators increase complexity by 1 [logical-and] (嵌套层级: 0)\n      - L17: +1 (累计: 8) - Logical AND (&&) operators increase complexity by 1 [logical-and] (嵌套层级: 0)\n      - L18: +3 (累计: 11) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)\n      - L21: +4 (累计: 15) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)\n      - L28: +1 (累计: 16) - Ternary operators (condition ? a : b) increase complexity by 1 + nesting penalty [conditional-expression] (嵌套层级: 0)\n  🔧 basicFunction (3:0) - 最终复杂度: 1.00\n      - L4: +1 (累计: 1) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)\n\n\n🔍 分析汇总\n=======\n📁 文件数量: 1\n🔍 函数数量: 2\n📊 平均复杂度: 7.5\n📈 总复杂度: 15\n✅ 高复杂度函数: 0\n⏱️  处理时间: 0.03s\n\n💡 性能优化建议:\n✅ 质量门禁通过！所有函数复杂度都低于 15 的阈值\n"}}, {"name": "JSON格式详细输出验收", "passed": true, "details": {"hasMetadata": true, "hasResults": true, "hasDetails": true, "hasRequiredFields": true, "schemaVersion": "2.1.0"}}, {"name": "嵌套函数处理验收", "passed": true, "details": {"functionSections": 3, "hasNestedIndentation": true, "hasCorrectNestingLevels": true}}, {"name": "规则标识符一致性验收", "passed": true, "details": {"allKebabCase": true, "hasCommonRules": true, "ruleIds": ["if-statement", "logical-or", "for-of-statement", "logical-and", "conditional-expression"], "totalRuleIds": 11}}, {"name": "诊断标记显示验收", "passed": true, "details": {"hasWarningSupport": true, "hasUnknownSupport": true, "hasErrorSupport": true, "hasRuleDescriptions": true}}], "性能验收": [{"name": "详细模式性能要求验收", "passed": true, "details": {"normalTime": 111, "detailsTime": 117, "performanceRatio": 1.05, "threshold": 2}}, {"name": "内存使用验收", "passed": true, "details": {"outputLength": 1825, "completed": true}}], "质量验收": [{"name": "单元测试验收", "passed": false, "details": {"testsPassed": false, "hasDetailCollectorTests": true, "hasRuleRegistryTests": false, "failCount": 0, "passCount": 0, "totalTests": 0, "detailsRelatedFailures": true, "note": "包含详细输出功能相关的测试失败"}, "error": "测试失败统计: 0失败, 0成功"}, {"name": "集成测试验收", "passed": true, "details": {"testResults": [{"command": "bun run start --details test-acceptance-temp/basic-test.ts", "success": true, "outputLength": 1825}, {"command": "bun run start --details --format=json test-acceptance-temp/nested-test.ts", "success": true, "outputLength": 3367}, {"command": "bun run start --details --format=json test-acceptance-temp/jsx-test.tsx", "success": true, "outputLength": 2790}], "totalTests": 3, "passedTests": 3}}, {"name": "真实项目验证", "passed": true, "details": {"hasValidDetailFormat": true, "hasComplexityInfo": true, "hasDetailsContent": true, "hasReasonableLength": true, "outputLength": 33016, "note": "专注验证详细输出功能，不检查退出码"}}], "overall": {"total": 10, "passed": 9, "failed": 1, "success": true}}