#!/usr/bin/env bun

/**
 * 测试TextFormatter是否导致超时的脚本
 */

import { TextFormatter } from './src/formatters/text';

async function testTextFormatter() {
  console.log('🧪 测试TextFormatter是否导致超时...\n');
  
  try {
    // 创建TextFormatter（不传config，让它使用默认配置）
    console.log('🔧 创建TextFormatter...');
    const formatter = new TextFormatter();
    
    // 构造一个简单的模拟AnalysisResult
    const mockAnalysisResult = {
      summary: {
        filesAnalyzed: 1,
        functionsAnalyzed: 1,
        totalComplexity: 0,
        averageComplexity: 0,
        highComplexityFunctions: 0
      },
      results: [{
        filePath: 'test.ts',
        complexity: 0,
        averageComplexity: 0,
        functions: [{
          name: 'simpleFunction',
          complexity: 0,
          line: 1,
          column: 0,
          filePath: 'test.ts'
        }]
      }]
    };
    
    console.log('📝 开始格式化...');
    const output = await formatter.format(mockAnalysisResult, false);
    console.log('✅ 格式化完成');
    
    console.log('📄 输出结果:');
    console.log(output);
    
    console.log('\n🎯 测试完成，等待进程退出...');
    
    // 显式退出
    setTimeout(() => {
      console.log('💡 进程应该在这之后立即退出');
      process.exit(0);
    }, 1000);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

testTextFormatter();