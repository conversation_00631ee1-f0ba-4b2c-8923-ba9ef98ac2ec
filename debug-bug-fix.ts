#!/usr/bin/env bun

/**
 * 修复版本的调试脚本
 * 正确处理资源清理，确保进程能够正常退出
 */

import { analyzeFile } from './src/index';
import { TextFormatter } from './src/formatters/text';
import { ConfigManager } from './src/config/manager';
import { writeFileSync, unlinkSync } from 'fs';
import { getCodeFrameGenerator } from './src/utils/code-frame-generator';

async function debugOutputFormatFixed() {
  console.log('🔍 调试输出格式问题（修复版）...\n');

  // 创建一个测试文件内容，包含一些简单函数
  const testCode = `
// 测试文件：包含不同复杂度的函数
export function simpleFunction() {
  return 'hello';
}

export function functionWithIf(x: number) {
  if (x > 0) {
    return x * 2;
  }
  return 0;
}

export function complexFunction(a: number, b: number) {
  if (a > 0) {
    if (b > 0) {
      return a + b;
    } else if (b < 0) {
      return a - b;
    }
  }
  return 0;
}

export function verySimpleFunction() {
  console.log('simple');
}
`.trim();

  // 写入临时测试文件
  const testFilePath = `/tmp/test-complexity-${Date.now()}.ts`;
  writeFileSync(testFilePath, testCode);

  // 获取单例实例的引用，以便稍后清理
  const codeFrameGenerator = getCodeFrameGenerator();

  try {
    console.log('📄 测试文件路径:', testFilePath);
    console.log('📝 测试文件内容:');
    console.log(testCode);
    console.log('\n' + '='.repeat(50) + '\n');

    // 分析文件
    console.log('🧠 开始分析...');
    const fileResult = await analyzeFile(testFilePath, { enableDetails: true });

    console.log('📊 分析结果:');
    console.log('- 总复杂度:', fileResult.complexity);
    console.log('- 平均复杂度:', fileResult.averageComplexity);
    console.log('- 函数数量:', fileResult.functions.length);

    console.log('\n📄 文件结果:');
    console.log('- 文件路径:', fileResult.filePath);
    console.log('- 文件复杂度:', fileResult.complexity);
    console.log('- 函数列表:');
    
    fileResult.functions.forEach((func, index) => {
      console.log(`  ${index + 1}. ${func.name}:`);
      console.log(`     - 复杂度: ${func.complexity}`);
      console.log(`     - 位置: 第${func.line}行，第${func.column}列`);
      console.log(`     - 详细步骤数: ${func.details?.length || 0}`);
    });

    // 测试格式化输出
    console.log('\n' + '='.repeat(50));
    console.log('🎨 格式化输出测试:\n');

    // 构造一个模拟的AnalysisResult用于测试格式化器
    const mockAnalysisResult = {
      summary: {
        filesAnalyzed: 1,
        functionsAnalyzed: fileResult.functions.length,
        totalComplexity: fileResult.complexity,
        averageComplexity: fileResult.averageComplexity,
        highComplexityFunctions: fileResult.functions.filter(f => f.complexity > 15).length
      },
      results: [fileResult]
    };

    const formatter = new TextFormatter();

    // 测试默认输出（不显示详细信息）
    console.log('📋 默认输出格式:');
    const defaultOutput = await formatter.format(mockAnalysisResult, false);
    console.log(defaultOutput);

    console.log('\n' + '-'.repeat(30) + '\n');

    // 测试详细输出
    console.log('📋 详细输出格式:');
    const detailOutput = await formatter.format(mockAnalysisResult, true);
    console.log(detailOutput);

    // 分析问题
    console.log('\n' + '='.repeat(50));
    console.log('🔍 问题分析:');
    
    const zeroComplexityFunctions = fileResult.functions.filter(f => f.complexity === 0);
    const invalidPositions = fileResult.functions.filter(f => f.line === 1 && f.column === 0);
    
    console.log(`- 复杂度为0的函数数量: ${zeroComplexityFunctions.length}`);
    console.log(`- 位置信息为(1:0)的函数数量: ${invalidPositions.length}`);
    
    if (zeroComplexityFunctions.length > 0) {
      console.log('- 复杂度为0的函数:');
      zeroComplexityFunctions.forEach(f => {
        console.log(`  * ${f.name} (${f.line}:${f.column})`);
      });
    }
    
    if (invalidPositions.length > 0) {
      console.log('- 位置信息错误的函数:');
      invalidPositions.forEach(f => {
        console.log(`  * ${f.name} - 复杂度: ${f.complexity}`);
      });
    }

    console.log('\n✅ 分析完成');

  } catch (error) {
    console.error('❌ 分析过程中出错:', error);
  } finally {
    // ===== 关键修复：清理资源 =====
    console.log('\n🧹 清理资源...');
    
    try {
      // 1. 销毁 CodeFrameGenerator 实例，清理定时器
      codeFrameGenerator.destroy();
      console.log('✅ CodeFrameGenerator 资源已清理');
      
      // 2. 清理临时文件
      unlinkSync(testFilePath);
      console.log('✅ 临时文件已清理');
      
    } catch (cleanupError) {
      console.warn('⚠️ 清理资源时出现错误:', cleanupError);
    }
    
    console.log('🎉 脚本执行完成，进程将正常退出');
  }
}

// 运行调试，并捕获未处理的 Promise 错误
debugOutputFormatFixed()
  .then(() => {
    console.log('✨ 调试脚本正常结束');
    process.exit(0); // 强制退出进程
  })
  .catch((error) => {
    console.error('💥 调试脚本失败:', error);
    
    // 确保即使出错也清理资源
    try {
      const codeFrameGenerator = getCodeFrameGenerator();
      codeFrameGenerator.destroy();
      console.log('🧹 错误处理中已清理资源');
    } catch (cleanupError) {
      console.warn('⚠️ 错误处理中的资源清理失败:', cleanupError);
    }
    
    process.exit(1);
  });