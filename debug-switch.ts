#!/usr/bin/env bun

/**
 * 快速测试switch语句复杂度
 */

import { ComplexityCalculator } from './src/core/calculator';

async function testSwitchComplexity() {
  const switchCode = `
    function withSwitch(value: number) {
      switch (value) {
        case 1:
          return "one";
        case 2:
          return "two";
        default:
          return "other";
      }
    }
  `;
  
  const results = await ComplexityCalculator.analyze(switchCode);
  console.log('Switch语句复杂度:', results[0]?.complexity);
}

testSwitchComplexity().catch(console.error);