#!/usr/bin/env bun

/**
 * 调试 ComplexityVisitor 处理 try-catch 的过程
 */

import { ComplexityVisitor } from './src/core/complexity-visitor';
import { ASTParser } from './src/core/parser';

async function debugVisitorTryCatch() {
  console.log('=== 调试 ComplexityVisitor try-catch 处理 ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  try {
    // 解析AST
    const parser = new ASTParser();
    const ast = await parser.parseCode(testCode, 'debug.ts');
    
    console.log('=== 使用 ComplexityVisitor 分析 ===');
    
    // 创建访问者并分析
    const visitor = new ComplexityVisitor(testCode);
    
    // 访问整个AST
    visitor.visit(ast);
    
    // 获取结果
    const totalComplexity = visitor.getTotalComplexity();
    const results = visitor.getResults();
    
    console.log('总复杂度:', totalComplexity);
    console.log('函数结果:', JSON.stringify(results, null, 2));
    
    // 查看访问者的内部状态
    console.log('\n=== 访问者状态 ===');
    console.log('当前嵌套级别:', visitor.getCurrentNestingLevel());
    
  } catch (error) {
    console.error('错误:', error);
  }
}

debugVisitorTryCatch().catch(console.error);