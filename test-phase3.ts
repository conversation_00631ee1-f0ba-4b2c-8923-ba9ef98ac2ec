#!/usr/bin/env bun
/**
 * Phase 3 功能验证测试
 * 验证 IoC 重构中移除单例模式和基于配置的规则引擎初始化
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';
import type { CalculatorOptions } from './src/engine/types';

async function testPhase3Implementation() {
  console.log('🧪 测试 Phase 3 - 移除单例模式和配置驱动的规则引擎');
  
  // 测试 1: 默认配置的工厂
  console.log('\n📋 测试 1: 默认配置工厂');
  const defaultFactory = new CalculatorFactory();
  const defaultCalculator = new ComplexityCalculator({}, defaultFactory);
  
  const code1 = `
    function simple() {
      if (a && b) {
        return true;
      }
      return false;
    }
  `;
  
  try {
    const results1 = await defaultCalculator.calculateCode(code1, 'test1.ts');
    console.log(`✅ 默认配置: 找到 ${results1.length} 个函数，复杂度 ${results1[0]?.complexity || 0}`);
  } catch (error) {
    console.log(`⚠️ 默认配置错误: ${error}`);
  }
  
  // 测试 2: 自定义规则引擎配置
  console.log('\n📋 测试 2: 自定义规则引擎配置');
  const customOptions: CalculatorOptions = {
    enableMonitoring: false,
    enableCaching: true,
    maxConcurrency: 2,
    debugMode: false,
    quiet: true,
    ruleEngineConfig: {
      maxRuleConcurrency: 5,
      enableRuleCaching: false,
      ruleDebugMode: false,
    }
  };
  
  const customFactory = new CalculatorFactory(customOptions);
  const customCalculator = new ComplexityCalculator({}, customFactory);
  
  try {
    const results2 = await customCalculator.calculateCode(code1, 'test2.ts');
    console.log(`✅ 自定义配置: 找到 ${results2.length} 个函数，复杂度 ${results2[0]?.complexity || 0}`);
    
    // 验证工厂配置
    const features = customCalculator.getFactoryFeatures();
    console.log('📊 工厂功能状态:', features);
  } catch (error) {
    console.log(`⚠️ 自定义配置错误: ${error}`);
  }
  
  // 测试 3: 静态 API 方法
  console.log('\n📋 测试 3: 静态 API 方法');
  try {
    const results3 = await ComplexityCalculator.analyze(code1);
    console.log(`✅ 静态 analyze: 找到 ${results3.length} 个函数，复杂度 ${results3[0]?.complexity || 0}`);
  } catch (error) {
    console.log(`⚠️ 静态 analyze 错误: ${error}`);
  }
  
  // 测试 4: 配置传递验证
  console.log('\n📋 测试 4: 规则引擎配置传递验证');
  const configFactory = new CalculatorFactory({
    ruleEngineConfig: {
      maxRuleConcurrency: 15,
      enableRuleCaching: true,
      ruleDebugMode: true,
    }
  });
  
  const ruleManager = configFactory.createRuleManager();
  console.log('✅ 规则管理器创建成功');
  
  // 测试 5: 独立实例验证
  console.log('\n📋 测试 5: 独立实例验证');
  const factory1 = new CalculatorFactory({ maxConcurrency: 2 });
  const factory2 = new CalculatorFactory({ maxConcurrency: 8 });
  
  const calc1 = new ComplexityCalculator({}, factory1);
  const calc2 = new ComplexityCalculator({}, factory2);
  
  const features1 = calc1.getFactoryFeatures();
  const features2 = calc2.getFactoryFeatures();
  
  console.log('🏭 Factory 1 特性:', features1);
  console.log('🏭 Factory 2 特性:', features2);
  
  if (features1.parallelExecution !== features2.parallelExecution) {
    console.log('✅ 独立实例配置验证成功：不同的工厂具有不同的配置');
  } else {
    console.log('⚠️ 独立实例配置可能有问题');
  }
  
  // 清理资源
  await defaultCalculator.dispose();
  await customCalculator.dispose();
  await calc1.dispose();
  await calc2.dispose();
  
  console.log('\n🎉 Phase 3 功能验证完成');
}

// 运行测试
testPhase3Implementation().catch(console.error);