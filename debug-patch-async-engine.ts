#!/usr/bin/env bun

/**
 * 临时修改AsyncRuleEngine来添加调试信息
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';

// 猴子补丁AsyncRuleEngine来添加调试
async function patchAndTest() {
  console.log('=== 临时添加调试信息到AsyncRuleEngine ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  // 动态导入并修改AsyncRuleEngine
  const asyncEngineModule = await import('./src/engine/async-engine');
  const originalAnalyzeNode = asyncEngineModule.AsyncRuleEngineImpl.prototype.analyzeNode;
  
  // 修改analyzeNode方法
  asyncEngineModule.AsyncRuleEngineImpl.prototype.analyzeNode = async function(node, context) {
    console.log(`\\n[DEBUG] analyzeNode called for: ${node.type}`);
    
    // 检查规则注册表的所有规则
    const allRules = this.ruleRegistry.getAllRules();
    console.log(`[DEBUG] Total registered rules: ${allRules.size}`);
    allRules.forEach(rule => {
      const isEnabled = this.ruleRegistry.isRuleEnabled(rule.id);
      const canHandle = rule.canHandle(node);
      console.log(`  - ${rule.id}: enabled=${isEnabled}, canHandle=${canHandle}`);
    });
    
    // 检查规则注册表
    const applicableRules = this.ruleRegistry.getRulesForNode(node);
    console.log(`[DEBUG] Found ${applicableRules.length} applicable rules for ${node.type}:`);
    applicableRules.forEach(rule => {
      console.log(`  - ${rule.id} (priority: ${rule.priority})`);
    });
    
    if (applicableRules.length === 0) {
      console.log(`[DEBUG] No rules found for ${node.type}, returning empty analysis`);
      return this.createEmptyNodeAnalysis(node);
    }
    
    // 调用原始方法
    const result = await originalAnalyzeNode.call(this, node, context);
    console.log(`[DEBUG] Node analysis result for ${node.type}: complexity=${result.complexity}`);
    
    return result;
  };
  
  console.log('AsyncRuleEngine已修改为调试版本');
  
  // 测试
  const factory = new CalculatorFactory({
    debugMode: false,
    quiet: true
  });
  
  const calculator = new ComplexityCalculator({}, factory);
  
  try {
    console.log('\\n开始分析...');
    const results = await calculator.calculateCode(testCode, 'debug.ts');
    console.log('\\n=== 最终结果 ===');
    console.log(results.map(r => ({name: r.name, complexity: r.complexity})));
  } catch (error) {
    console.error('分析错误:', error);
  } finally {
    await calculator.dispose();
  }
}

patchAndTest().catch(console.error);