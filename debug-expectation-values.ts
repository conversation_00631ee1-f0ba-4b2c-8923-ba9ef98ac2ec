#!/usr/bin/env bun

/**
 * 调试期望值脚本
 * 用于获取特定代码片段的实际复杂度值，以便更新测试期望值
 */

import { ComplexityCalculator } from './src/core/calculator';

async function debugExpectationValues() {
  console.log('=== 调试期望值脚本 ===\n');
  
  // 测试用例 1: 简单函数
  const simpleCode = `
    function simple() {
      return 42;
    }
  `;
  
  // 测试用例 2: if 语句
  const ifCode = `
    function withIf(x) {
      if (x > 0) {
        return x;
      }
      return 0;
    }
  `;
  
  // 测试用例 3: 递归函数
  const recursiveCode = `
    function factorial(n) {
      if (n <= 1) return 1;
      return n * factorial(n - 1);
    }
  `;
  
  // 测试用例 4: 嵌套结构
  const nestedCode = `
    function nested(x) {
      if (x > 0) {
        while (x > 10) {
          x--;
        }
      }
      return x;
    }
  `;

  const testCases = [
    { name: 'Simple function', code: simpleCode },
    { name: 'If statement', code: ifCode },
    { name: 'Recursive function', code: recursiveCode },
    { name: 'Nested structure', code: nestedCode }
  ];

  for (const testCase of testCases) {
    console.log(`--- ${testCase.name} ---`);
    console.log('Code:');
    console.log(testCase.code);
    
    try {
      const results = await ComplexityCalculator.analyze(testCase.code);
      console.log('Results:');
      results.forEach(result => {
        console.log(`  Function: ${result.name}, Complexity: ${result.complexity}`);
      });
    } catch (error) {
      console.error('Error:', error);
    }
    console.log();
  }
}

debugExpectationValues().catch(console.error);