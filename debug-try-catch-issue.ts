import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';

async function debugTryCatchIssue() {
  // 让我们分别测试不同的 try-catch 组合
  
  console.log('=== 调试 try-catch 额外复杂度问题 ===');
  
  // 测试1: 简单的 try-catch
  console.log('\n--- 测试1: try-catch only ---');
  let sourceCode = `
    function test() {
      try {
        doSomething();
      } catch (error) {
        handleError();
      }
    }
  `;
  
  let parser = new ASTParser();
  let ast = await parser.parseCode(sourceCode, 'debug.ts');
  let visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('复杂度:', visitor.getTotalComplexity(), '(期望: 1)');

  // 测试2: try-catch with finally
  console.log('\n--- 测试2: try-catch-finally ---');
  sourceCode = `
    function test() {
      try {
        doSomething();
      } catch (error) {
        handleError();
      } finally {
        cleanup();
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('复杂度:', visitor.getTotalComplexity(), '(期望: 1, finally不增加复杂度)');

  // 测试3: try with multiple catch
  console.log('\n--- 测试3: try with multiple catch ---');
  sourceCode = `
    function test() {
      try {
        doSomething();
      } catch (error) {
        if (error instanceof TypeError) {
          handleTypeError();
        } else {
          handleOtherError();
        }
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('复杂度:', visitor.getTotalComplexity(), '(期望: 2, catch=1+0, if=1+1)');

  // 测试4: 嵌套在其他结构中的 try-catch
  console.log('\n--- 测试4: nested try-catch ---');
  sourceCode = `
    function test() {
      if (condition) {
        try {
          doSomething();
        } catch (error) {
          handleError();
        }
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('复杂度:', visitor.getTotalComplexity(), '(期望: 3, if=1+0, catch=1+1)');
}

debugTryCatchIssue().catch(console.error);