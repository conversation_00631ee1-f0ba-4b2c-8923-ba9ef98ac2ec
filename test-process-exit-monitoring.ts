#!/usr/bin/env bun
/**
 * 启用监控后进程退出验证脚本
 * 验证启用监控功能进行分析后，进程能在5秒内自然退出
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';
import type { CalculatorOptions } from './src/engine/types';

async function testMonitoringProcessExit() {
  console.log('🧪 测试启用监控后进程退出');
  
  const startTime = Date.now();
  let calculator: ComplexityCalculator | null = null;
  
  try {
    // 创建启用监控的配置
    const monitoringOptions: CalculatorOptions = {
      enableMonitoring: true,
      enableCaching: true,
      maxConcurrency: 4,
      debugMode: false,
      quiet: false,
      monitorConfig: {
        collectMetrics: true,
        enableProfiling: true,
        maxMemoryMB: 100,
      }
    };
    
    console.log('📋 创建启用监控的工厂和计算器...');
    const factory = new CalculatorFactory(monitoringOptions);
    calculator = new ComplexityCalculator({}, factory);
    
    // 测试复杂代码分析
    const complexCode = `
      function complexAnalysis(data) {
        if (!data) {
          return null;
        }
        
        const results = [];
        for (let i = 0; i < data.length; i++) {
          const item = data[i];
          
          if (item.type === 'valid') {
            if (item.value > 0) {
              while (item.processing) {
                if (item.status === 'ready') {
                  results.push(item.value * 2);
                  break;
                } else if (item.status === 'pending') {
                  continue;
                } else {
                  throw new Error('Invalid status');
                }
              }
            }
          } else if (item.type === 'invalid') {
            console.warn('Invalid item:', item);
          }
        }
        
        return results.length > 0 ? results : null;
      }
      
      function recursiveFunction(n) {
        if (n <= 1) {
          return n;
        }
        return recursiveFunction(n - 1) + recursiveFunction(n - 2);
      }
      
      function logicalMixing(a, b, c, d) {
        return (a && b) || (c && d) || (a || c) && (b || d);
      }
    `;
    
    console.log('📊 执行启用监控的复杂度分析...');
    const results = await calculator.calculateCode(complexCode, 'monitoring-test.ts');
    
    console.log(`✅ 监控分析完成: 找到 ${results.length} 个函数`);
    results.forEach(result => {
      console.log(`  - ${result.name}: 复杂度 ${result.complexity}`);
    });
    
    // 获取工厂特性信息
    const features = calculator.getFactoryFeatures();
    console.log('🏭 工厂特性:', features);
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`⏱️ 监控分析耗时: ${duration}ms`);
    console.log('🎉 监控分析测试完成，正在清理资源...');
    
    // 显式清理资源
    await calculator.dispose();
    calculator = null;
    
    console.log('✨ 资源清理完成，进程应该自动退出');
    
  } catch (error) {
    console.error('❌ 监控分析测试失败:', error);
    
    // 确保资源清理
    if (calculator) {
      try {
        await calculator.dispose();
      } catch (disposeError) {
        console.error('⚠️ 资源清理失败:', disposeError);
      }
    }
    
    process.exit(1);
  }
}

// 运行测试
testMonitoringProcessExit().catch(error => {
  console.error('💥 监控测试执行失败:', error);
  process.exit(1);
});