# Cognitive Complexity Analyzer

[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Bun](https://img.shields.io/badge/Bun-1.0+-orange.svg)](https://bun.sh)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

一个高性能的 **认知复杂度分析工具**，专门用于分析 TypeScript 和 JavaScript 代码的认知复杂度。基于 SWC 解析器构建，完全兼容Node.js生态，提供准确、快速的代码复杂度分析。

## 🎯 核心特性

- **准确的复杂度计算**: 基于认知复杂度算法，比圈复杂度更准确地反映代码理解难度
- **高性能分析**: 基于 SWC 解析器，比传统工具更快更准确
- **完全兼容**: 支持标准Node.js环境和npm/npx工作流
- **多格式输出**: 支持文本、JSON、HTML 等多种输出格式
- **基线管理**: 支持复杂度基线创建和更新，便于长期跟踪
- **CI/CD 集成**: 可配置阈值，支持构建失败机制
- **Web UI**: 提供可视化界面进行复杂度分析和趋势查看

## 🚀 快速开始

### 用户安装和使用

```bash
# 全局安装
npm install -g @imd/cognitive-complexity

# 分析项目
complexity src/

# 或使用npx直接运行
npx cognitive-complexity src/

# 项目内安装
npm install --save-dev cognitive-complexity
npx complexity src/
```

### 开发环境设置（贡献者）

```bash
# 安装依赖（使用Bun获得更快体验）
bun install

# 开发模式（监听文件变化）
bun run dev

# 构建CLI版本
bun run build

# 运行测试
bun test
```

## 📖 命令行参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `[paths...]` | string[] | `['.']` | 要分析的文件或目录路径 |
| `--fail-on <threshold>` | number | `15` | 当复杂度超过阈值时失败 |
| `--create-baseline` | boolean | `false` | 为当前项目创建基线文件 |
| `--update-baseline` | boolean | `false` | 更新基线文件 |
| `-c, --config <path>` | string | - | 配置文件路径 |
| `-o, --output-dir <path>` | string | - | 报告输出目录 |
| `-d, --details` | boolean | `false` | 显示详细的复杂度计算步骤和规则信息 |
| `-f, --format <format>` | string | `'text'` | 输出格式 (text, json, html) |
| `-m, --min <threshold>` | number | - | 只显示函数复杂度超过阈值的结果 |
| `--min-file-complexity <threshold>` | number | `1` | 只显示文件总复杂度超过阈值的文件，默认隐藏零复杂度文件 |
| `-s, --sort <key>` | string | `'complexity'` | 结果排序方式 |
| `--ui` | boolean | `false` | 启动Web UI进行可视化 |

## 🔧 配置文件

支持多种配置文件格式，使用 [cosmiconfig](https://github.com/davidtheclark/cosmiconfig) 自动发现：

- `cognitive-complexity.config.js`
- `cognitive-complexity.config.json`
- `.cognitiverc`
- `package.json` 中的 `cognitive-complexity` 字段

### 配置示例

```json
{
  "maxComplexity": 15,
  "includeDetails": true,
  "outputFormat": "json",
  "exclude": ["**/*.test.ts", "**/node_modules/**"],
  "baseline": {
    "path": ".cognitive-complexity-baseline.json",
    "failOnIncrease": true
  }
}
```

## 📊 输出格式

### 文本格式 (默认)

**基本输出:**
```
🧠 认知复杂度分析汇总
=========================
📁 分析文件数: 1
🔍 分析函数数: 2
📊 平均复杂度: 10.00
📈 总复杂度: 20
⚠️  高复杂度函数: 1

📋 详细分析结果:
──────────────────────────────────────────────────

📄 src/complex-function.ts (复杂度: 20.00, 平均: 10.00)
  🔧 complexLogic (15:0) - 最终复杂度: 12.00
  🔧 handleUserInput (58:0) - 最终复杂度: 8.00
```

**详细模式输出 (--details):**
```
📄 test-details-final.ts (复杂度: 26.00, 平均: 8.67)
  ⚠️  complexFunction (2:0) - 最终复杂度: 21.00
      - L4: +1 (累计: 1) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)
      - L5: +2 (累计: 3) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)
      - L6: +3 (累计: 6) - For loops increase complexity by 1 + nesting penalty [for-statement] (嵌套层级: 0)
      - L7: +4 (累计: 10) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)
      - L8: +5 (累计: 15) - For loops increase complexity by 1 + nesting penalty [for-statement] (嵌套层级: 0)
      - L9: +6 (累计: 21) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)
  🔧 arrowFunction (19:0) - 最终复杂度: 3.00
      - L20: +1 (累计: 1) - Logical OR (||) operators increase complexity by 1 [logical-or] (嵌套层级: 0)
      - L20: +1 (累计: 2) - Logical AND (&&) operators increase complexity by 1 [logical-and] (嵌套层级: 0)
  🔧 method (24:0) - 最终复杂度: 2.00
      - L26: +1 (累计: 1) - If statements increase complexity by 1 + nesting penalty [if-statement] (嵌套层级: 0)
      - L29: +1 (累计: 2) - Catch clauses increase complexity by 1 + nesting penalty [catch-clause] (嵌套层级: 0)
```

### JSON 格式

**基本输出:**
```json
{
  "summary": {
    "totalComplexity": 20,
    "averageComplexity": 10.0,
    "filesAnalyzed": 1,
    "functionsAnalyzed": 2,
    "highComplexityFunctions": 1
  },
  "results": [
    {
      "filePath": "src/complex-function.ts",
      "complexity": 20,
      "functions": [
        {
          "name": "complexLogic",
          "complexity": 12,
          "line": 15,
          "column": 0,
          "filePath": "src/complex-function.ts"
        },
        {
          "name": "handleUserInput",
          "complexity": 8,
          "line": 58,
          "column": 0,
          "filePath": "src/complex-function.ts"
        }
      ],
      "averageComplexity": 10.0
    }
  ]
}
```

**详细模式输出 (--details --format=json):**
```json
{
  "summary": {
    "totalComplexity": 26,
    "averageComplexity": 8.666666666666666,
    "filesAnalyzed": 1,
    "functionsAnalyzed": 3,
    "highComplexityFunctions": 1
  },
  "results": [
    {
      "filePath": "test-details-final.ts",
      "complexity": 26,
      "functions": [
        {
          "name": "complexFunction",
          "complexity": 21,
          "line": 2,
          "column": 0,
          "filePath": "test-details-final.ts",
          "details": [
            {
              "line": 4,
              "column": 0,
              "increment": 1,
              "cumulative": 1,
              "ruleId": "if-statement",
              "description": "If statements increase complexity by 1 + nesting penalty",
              "nestingLevel": 0,
              "context": "IfStatement at 4:0"
            },
            {
              "line": 5,
              "column": 0,
              "increment": 2,
              "cumulative": 3,
              "ruleId": "if-statement",
              "description": "If statements increase complexity by 1 + nesting penalty",
              "nestingLevel": 0,
              "context": "IfStatement at 5:0"
            }
          ]
        }
      ],
      "averageComplexity": 8.666666666666666
    }
  ],
  "metadata": {
    "schemaVersion": "2.1.0",
    "generatedAt": "2025-07-29T02:48:30.993Z",
    "format": "cognitive-complexity-json",
    "detailsEnabled": true,
    "contextEnabled": false,
    "contextAllEnabled": false,
    "errorRecoveryEnabled": true
  }
}
```

### HTML 格式

生成交互式 HTML 报告，包含：
- 复杂度概览图表
- 文件级别的复杂度分析
- 函数级别的详细信息
- 趋势分析（如果有基线）

## 🎛️ 基线管理

基线功能帮助你跟踪项目复杂度的变化趋势：

```bash
# 创建初始基线
bun run dev --create-baseline

# 更新现有基线
bun run dev --update-baseline

# 与基线比较（自动）
bun run dev --details
```

基线文件格式：
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "summary": {
    "totalComplexity": 156,
    "averageComplexity": 8.2,
    "totalFunctions": 19
  },
  "files": {...}
}
```

## 🔌 API 使用

除了CLI工具，也可以作为库使用：

```typescript
import { 
  analyzeFile, 
  analyzeProject, 
  ComplexityCalculator 
} from 'cognitive-complexity';

// 分析单个文件
const fileResult = await analyzeFile('src/complex.ts', {
  maxComplexity: 15,
  includeDetails: true
});

console.log(`Total complexity: ${fileResult.complexity}`);

// 使用计算器类
const calculator = new ComplexityCalculator({
  maxComplexity: 10
});

const functions = await calculator.calculateFile('src/example.ts');
functions.forEach(fn => {
  console.log(`${fn.name}: ${fn.complexity}`);
});

// 启用详细模式获取计算步骤
const detailedCalculator = new ComplexityCalculator({
  enableDetails: true
});

const detailedResults = await detailedCalculator.calculateCode(`
function complexFunction(data: any[]) {
  for (const item of data) {
    if (item.active && item.verified) {
      if (item.role === 'admin') {
        return item;
      }
    }
  }
  return null;
}
`, 'inline.ts');

detailedResults.forEach(fn => {
  console.log(`${fn.name}: 复杂度 ${fn.complexity}`);
  if (fn.details) {
    fn.details.forEach(step => {
      console.log(`  L${step.line}: +${step.increment} (累计: ${step.cumulative}) - ${step.description} [${step.ruleId}]`);
    });
  }
});
```

### 使用详细模式分析

```bash
# 基本分析
cognitive-complexity src/

# 显示详细计算步骤
cognitive-complexity --details src/

# 详细模式 + JSON 输出
cognitive-complexity --details --format=json src/ > complexity-report.json

# 详细模式 + HTML 报告
cognitive-complexity --details --format=html src/ --output-dir reports/

# 只显示超过阈值的函数，带详细信息
cognitive-complexity --details --min=10 src/

# 文件级过滤示例
cognitive-complexity --min-file-complexity 0 src/    # 显示所有文件(包括零复杂度)
cognitive-complexity --min-file-complexity 5 src/    # 只显示文件复杂度≥5的文件
cognitive-complexity --min 3 --min-file-complexity 10 src/  # 函数级+文件级双重过滤
```

## 🧪 开发和测试

### 开发命令（贡献者）

```bash
# 安装依赖（开发环境使用Bun）
bun install

# 开发模式（带监听）
bun run dev

# 运行测试
bun test

# 类型检查
bun run typecheck

# 构建生产版本（输出Node.js兼容代码）
bun run build

# 清理构建文件
bun run clean
```

### 重要说明

- **开发者**: 使用Bun进行开发，享受快速启动和构建体验
- **用户**: 安装后在任何Node.js环境中正常使用
- **兼容性**: 构建产物完全兼容Node.js 18+，无需Bun运行时

### 测试覆盖率

- **核心算法**: 100% 行覆盖率
- **CLI功能**: 90% 分支覆盖率
- **集成测试**: 覆盖主要使用场景

## 🏗️ 项目架构

```
src/
├── core/           # 核心算法和类型定义
│   ├── calculator.ts   # 复杂度计算器主类
│   ├── parser.ts       # AST解析器封装
│   ├── types.ts        # 核心类型定义
│   └── errors.ts       # 自定义错误类型
├── cli/            # 命令行界面
│   ├── index.ts        # CLI入口点
│   └── commands.ts     # 命令处理器
├── config/         # 配置管理
│   ├── manager.ts      # 配置加载和管理
│   └── types.ts        # 配置相关类型
├── baseline/       # 基线管理
│   ├── manager.ts      # 基线管理器
│   └── types.ts        # 基线相关类型
├── formatters/     # 输出格式化
│   ├── base.ts         # 格式化器基类
│   ├── text.ts         # 文本格式输出
│   ├── json.ts         # JSON格式输出
│   └── html.ts         # HTML格式输出
└── __test__/       # 测试文件
```

## 🤝 CI/CD 集成

### GitHub Actions 示例

```yaml
name: Code Quality Check
on: [push, pull_request]

jobs:
  complexity-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
        
      - name: Install cognitive-complexity
        run: npm install -g @imd/cognitive-complexity
        
      - name: Run complexity analysis
        run: cognitive-complexity --fail-on 15 --format json src/
```

### 预提交钩子

```bash
#!/bin/sh
# .git/hooks/pre-commit
npx cognitive-complexity --fail-on 15 src/
if [ $? -ne 0 ]; then
  echo "❌ Complexity check failed"
  exit 1
fi
echo "✅ Complexity check passed"
```

## 📋 认知复杂度算法

本工具实现了标准的认知复杂度算法，相比传统的圈复杂度，认知复杂度更准确地反映了代码的理解难度：

### 计算规则

1. **基础复杂度**: 每个函数基础复杂度为 0
2. **控制流语句**: `if`, `else`, `while`, `for`, `do-while`, `switch` 等 +1
3. **嵌套惩罚**: 每层嵌套 +1 (递归计算)
4. **逻辑运算符**: `&&`, `||` 操作符 +1
5. **异常处理**: `try-catch`, `throw` +1
6. **递归调用**: 函数自调用 +1

### 复杂度等级

| 复杂度范围 | 等级 | 建议 |
|------------|------|------|
| 1-5 | 🟢 简单 | 易于理解和维护 |
| 6-10 | 🟡 中等 | 需要注意，考虑重构 |
| 11-15 | 🟠 复杂 | 建议拆分函数 |
| 16+ | 🔴 非常复杂 | 强烈建议重构 |

## 🎯 最佳实践

1. **设置合理阈值**: 建议将复杂度阈值设置为 10-15
2. **定期检查**: 将复杂度检查集成到 CI/CD 流程
3. **基线跟踪**: 使用基线功能跟踪项目复杂度趋势
4. **渐进改进**: 专注于改进复杂度最高的函数
5. **团队标准**: 建立团队统一的复杂度标准

## 🐛 故障排除

### 常见问题

**Q: 分析时出现解析错误**
A: 确保 TypeScript 版本兼容 (>=4.5.0)，检查代码语法是否正确

**Q: 性能较慢**
A: 使用 `--min` 参数过滤低复杂度结果，或指定具体文件而非整个目录

**Q: 配置文件不生效**
A: 检查配置文件命名和格式，确保在项目根目录

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

- [SWC](https://swc.rs/) - 高性能 TypeScript/JavaScript 解析器
- [Commander.js](https://github.com/tj/commander.js) - 命令行界面框架
- [cosmiconfig](https://github.com/davidtheclark/cosmiconfig) - 配置管理
- [Node.js](https://nodejs.org/) - JavaScript运行时环境

---

**在代码质量的道路上，认知复杂度是你的指南针 🧭**

*本工具在开发时使用Bun获得最佳体验，在用户环境中完全兼容Node.js生态。*
