#!/usr/bin/env bun

import { ComplexityCalculator } from './src/core/calculator';
import { ASTParser } from './src/core/parser';

// 测试文件内容 - 模拟导出对象的TypeScript文件
const testCode = `// Test file
export interface UserProps {
  name: string;
  age: number;
}

export const defaultUser = {
  name: '<PERSON>',
  age: 25
};

export function getUserDetails(user: UserProps) {
  if (user.age > 18) {
    return \`Adult: \${user.name}\`;
  }
  return \`Minor: \${user.name}\`;
}
`;

async function testCodeContextFix() {
  console.log('🔧 测试代码上下文空行错误修复...\n');

  try {
    // 创建解析器和计算器
    const parser = new ASTParser();
    const calculator = new ComplexityCalculator({
      includeDetails: true,
      showContext: true
    });

    // 解析测试代码
    const ast = await parser.parseCode(testCode, 'test.ts');
    
    // 计算复杂度
    const results = await calculator.calculateCode(testCode, 'test.ts');

    console.log('📊 分析结果:');
    console.log('函数数量:', results.length);
    
    for (const result of results) {
      console.log('\n' + '='.repeat(50));
      console.log(`函数: ${result.name}`);
      console.log(`复杂度: ${result.complexity}`);
      console.log(`位置: 第${result.startLine}行 - 第${result.endLine}行`);
      
      if (result.details && result.details.length > 0) {
        console.log('\n详细信息:');
        for (const detail of result.details) {
          console.log(`  - 行${detail.line}: ${detail.description} (+${detail.complexity})`);
          
          // 检查是否指向空行或无效行
          const lines = testCode.split('\n');
          if (detail.line > 0 && detail.line <= lines.length) {
            const lineContent = lines[detail.line - 1]?.trim();
            if (!lineContent || lineContent === '}' || lineContent === '{') {
              console.log(`    ⚠️  警告: 指向空行或花括号行: "${lineContent}"`);
            } else {
              console.log(`    ✅ 正常: "${lineContent}"`);
            }
          }
        }
      }
    }

    console.log('\n✅ 测试完成!');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testCodeContextFix();