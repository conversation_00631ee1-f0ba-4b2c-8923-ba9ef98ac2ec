import { ComplexityCalculator } from './src/core/calculator';

async function diagnosticTest() {
  console.log('诊断测试：查找阻止进程退出的资源...');
  
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  const calculator = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: false // 禁用详细模式简化测试
  });
  
  try {
    console.log('1. 开始计算...');
    const results = await calculator.calculateCode(code, 'test.js');
    console.log('总复杂度:', results[0]?.complexity);
    
    console.log('2. 计算完成，开始清理...');
    await calculator.dispose();
    console.log('3. 清理完成');
    
    // 检查活跃的句柄
    console.log('4. 检查活跃的句柄...');
    if (typeof process._getActiveHandles === 'function') {
      const handles = process._getActiveHandles();
      console.log('活跃句柄数量:', handles.length);
      handles.forEach((handle, index) => {
        console.log(`  句柄 ${index}:`, handle.constructor.name);
      });
    }
    
    if (typeof process._getActiveRequests === 'function') {
      const requests = process._getActiveRequests();
      console.log('活跃请求数量:', requests.length);
      requests.forEach((request, index) => {
        console.log(`  请求 ${index}:`, request.constructor.name);
      });
    }
    
    console.log('5. 等待5秒检查是否自然退出...');
    setTimeout(() => {
      console.log('❌ 进程未自然退出');
      process.exit(1);
    }, 5000);
    
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

process.on('beforeExit', () => {
  console.log('✅ 进程即将自然退出！');
});

diagnosticTest().catch(console.error);