import { parseSync } from '@swc/core';

const code = `
export class Simple {
  public testMethod() {
    if (true) {
      return 1;
    }
    return 0;
  }
}
`;

console.log("模拟计算器处理过程...");

try {
  const ast = parseSync(code, {
    syntax: 'typescript',
    tsx: false,
    decorators: true,
    dynamicImport: true,
    target: 'es2022'
  });
  
  // 模拟extractFunctions
  const functions: any[] = [];
  
  function extractFunctions(node: any): void {
    if (node.type === 'ClassDeclaration') {
      node.body?.forEach((member: any) => {
        if (member.type === 'ClassMethod' && member.key?.value !== 'constructor') {
          console.log(`找到ClassMethod: ${member.key.value}`);
          functions.push(member);
        }
      });
    }
    
    // 递归遍历
    for (const key in node) {
      if (node[key] && typeof node[key] === 'object') {
        if (Array.isArray(node[key])) {
          node[key].forEach((child: any) => {
            if (child && typeof child === 'object' && child.type) {
              extractFunctions(child);
            }
          });
        } else if (node[key].type) {
          extractFunctions(node[key]);
        }
      }
    }
  }
  
  extractFunctions(ast);
  
  // 模拟getActualFunctionNode
  function getActualFunctionNode(node: any): any {
    if (node.type === 'ClassMethod') {
      const functionPart = node.function;
      if (functionPart && functionPart.body) {
        console.log(`提取ClassMethod.function.body: ${functionPart.body.type}`);
        return functionPart.body;
      }
      return node;
    }
    return node;
  }
  
  // 模拟visitNode - 简化版
  function visitNode(node: any, depth = 0): number {
    const indent = '  '.repeat(depth);
    console.log(`${indent}访问节点: ${node.type}`);
    
    let complexity = 0;
    
    switch (node.type) {
      case 'IfStatement':
        console.log(`${indent}  -> IfStatement: +1`);
        complexity = 1;
        break;
    }
    
    // 递归访问子节点
    for (const key in node) {
      if (node[key] && typeof node[key] === 'object') {
        if (Array.isArray(node[key])) {
          node[key].forEach((child: any) => {
            if (child && typeof child === 'object' && child.type) {
              complexity += visitNode(child, depth + 1);
            }
          });
        } else if (node[key].type) {
          complexity += visitNode(node[key], depth + 1);
        }
      }
    }
    
    return complexity;
  }
  
  // 处理每个函数
  functions.forEach(func => {
    console.log(`\n=== 处理函数: ${func.key.value} ===`);
    const actualNode = getActualFunctionNode(func);
    console.log(`实际节点类型: ${actualNode.type}`);
    
    const complexity = visitNode(actualNode);
    console.log(`最终复杂度: ${complexity}`);
  });
  
} catch (error) {
  console.error("处理失败:", error);
}