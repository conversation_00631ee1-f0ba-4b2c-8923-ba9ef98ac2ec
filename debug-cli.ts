#!/usr/bin/env bun

import { CommandProcessor } from './src/cli/commands';

async function testCLI() {
  try {
    const processor = new CommandProcessor();
    const success = await processor.execute({
      paths: ['./simple-test.js'],
      format: 'json',
      quiet: true
    } as any);
    
    console.log('CLI执行结果:', success);
  } catch (error) {
    console.error('CLI错误:', error);
  }
}

testCLI();