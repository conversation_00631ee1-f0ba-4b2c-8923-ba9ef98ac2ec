import { ComplexityCalculator } from './src/core/calculator';

async function testWithForcedExit() {
  console.log('使用强制退出的测试...');
  
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  const calculator = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: false  // 禁用详细模式减少复杂性
  });
  
  const results = await calculator.calculateCode(code, 'test.js');
  console.log('总复杂度:', results[0]?.complexity);
  console.log('✅ 计算完成，现在强制退出');
  
  // 立即强制退出，不等待事件循环
  process.exit(0);
}

// 添加未捕获异常处理
process.on('uncaughtException', (error) => {
  console.error('未捕获异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

testWithForcedExit().catch((error) => {
  console.error('测试失败:', error);
  process.exit(1);
});