#!/usr/bin/env bun

/**
 * 比较不同方式获取的AsyncRuleEngine
 */

import { RuleInitializationManager } from './src/core/rule-initialization';
import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';

async function compareAsyncRuleEngines() {
  console.log('=== 比较不同的AsyncRuleEngine实例 ===\n');
  
  let directRules: any = null;
  let calcRules: any = null;
  
  // 方法1: 直接通过RuleInitializationManager创建
  console.log('--- 方法1: 直接创建 ---');
  const directManager = new RuleInitializationManager(true);
  const directEngine = await directManager.initializeAsyncRuleEngine({
    maxRuleConcurrency: 10,
    enableRuleCaching: true,
    ruleDebugMode: false
  });
  
  if (directEngine) {
    directRules = (directEngine as any).ruleRegistry.getAllRules();
    console.log(`直接创建的引擎规则数: ${directRules.size || directRules.length || 'unknown'}`);
    if (directRules.size) {
      console.log('规则列表:', Array.from(directRules.keys()));
    } else if (directRules.length) {
      console.log('规则列表:', directRules.map((r: any) => r.id));
    } else {
      console.log('规则列表:', Object.keys(directRules));
    }
  }
  
  // 方法2: 通过Calculator获取
  console.log('\\n--- 方法2: 通过Calculator ---');
  const factory = new CalculatorFactory({
    debugMode: false,
    quiet: true
  });
  
  const calculator = new ComplexityCalculator({}, factory);
  
  // 触发AsyncRuleEngine初始化
  try {
    const testCode = 'function test() { if(true) return; }';
    await calculator.calculateCode(testCode, 'test.ts');
    
    // 获取Calculator内部的AsyncRuleEngine
    const calculatorEngine = (calculator as any).asyncRuleEngine;
    if (calculatorEngine) {
      calcRules = calculatorEngine.ruleRegistry.getAllRules();
      console.log(`Calculator的引擎规则数: ${calcRules.size || calcRules.length || 'unknown'}`);
      if (calcRules.size) {
        console.log('规则列表:', Array.from(calcRules.keys()));
      } else if (calcRules.length) {
        console.log('规则列表:', calcRules.map((r: any) => r.id));
      } else {
        console.log('规则列表:', Object.keys(calcRules));
      }
      
      // 比较实例
      console.log('\\n--- 比较 ---');
      console.log('是同一个实例?', directEngine === calculatorEngine);
      
      // 如果两个都是数组，比较规则ID
      if (directRules && calcRules) {
        const getIds = (rules: any) => {
          if (rules.size) return Array.from(rules.keys());
          if (rules.length) return rules.map((r: any) => r.id);
          return Object.keys(rules);
        };
        
        const directIds = getIds(directRules);
        const calcIds = getIds(calcRules);
        
        console.log('规则数量是否相同?', directIds.length === calcIds.length);
        console.log('直接创建的规则:', directIds);
        console.log('Calculator的规则:', calcIds);
        
        const missingInCalc = directIds.filter(id => !calcIds.includes(id));
        const extraInCalc = calcIds.filter(id => !directIds.includes(id));
        
        if (missingInCalc.length > 0) {
          console.log('Calculator中缺失的规则:', missingInCalc);
        }
        if (extraInCalc.length > 0) {
          console.log('Calculator中额外的规则:', extraInCalc);
        }
      }
    } else {
      console.log('Calculator的AsyncRuleEngine为null');
    }
    
  } catch (error) {
    console.error('测试Calculator时出错:', error);
  } finally {
    await calculator.dispose();
  }
}

compareAsyncRuleEngines().catch(console.error);