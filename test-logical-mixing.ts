import { ComplexityCalculator } from './src/core/calculator';

async function testLogicalMixing() {
  const tests = [
    {
      name: '混用检测 - && 和 ||',
      code: 'function test() { if (a && b || c) { console.log("mixed"); } }',
      expected: 4  // if(1) + &&(1) + ||(1) + 混用惩罚(1)
    },
    {
      name: '非混用 - 只有 &&',
      code: 'function test() { if (a && b && c) { console.log("all and"); } }',
      expected: 3  // if(1) + &&(1) + &&(1) = 3
    },
    {
      name: '非混用 - 只有 ||',
      code: 'function test() { if (a || b || c) { console.log("all or"); } }',
      expected: 3  // if(1) + ||(1) + ||(1) = 3
    }
  ];
  
  const calculator = new ComplexityCalculator({ enableMixedLogicOperatorPenalty: true });
  
  try {
    for (const test of tests) {
      console.log(`\n测试: ${test.name}`);
      console.log(`代码: ${test.code}`);
      console.log(`期望复杂度: ${test.expected}`);
      
      const results = await calculator.calculateCode(test.code, 'test.js');
      const actual = results[0]?.complexity || 0;
      
      console.log(`实际复杂度: ${actual}`);
      console.log(`结果: ${actual === test.expected ? '✅ 通过' : '❌ 失败'}`);
    }
  } finally {
    // 确保资源清理
    await calculator.dispose();
    console.log('\n✅ 资源清理完成');
  }
}

testLogicalMixing().catch(console.error);