/**
 * IoC重构演示脚本
 * 展示新的静态API和资源管理功能
 */

import { ComplexityCalculator } from './src/core/calculator';

async function demonstrateStaticAPI() {
  console.log('🚀 IoC重构演示 - 静态API和自动资源管理\n');

  // 演示1: 基本的静态分析
  console.log('1️⃣ 基本静态分析:');
  const simpleCode = `
    function simple() {
      return 42;
    }
    
    function withCondition(x) {
      if (x > 0) {
        if (x > 10) {
          return x * 2;
        }
        return x;
      }
      return 0;
    }
  `;

  const results = await ComplexityCalculator.analyze(simpleCode);
  console.log(`  ✅ 分析了 ${results.length} 个函数:`);
  results.forEach(result => {
    console.log(`     - ${result.name}: 复杂度 ${result.complexity}`);
  });

  // 演示2: 快速分析概览
  console.log('\n2️⃣ 快速分析概览:');
  const complexCode = `
    function simple() { return 1; }
    
    function moderate(x) {
      if (x > 0) {
        while (x > 0) {
          if (x % 2 === 0) {
            console.log(x);
          }
          x--;
        }
      }
    }
    
    function complex(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].active) {
          for (let j = 0; j < data[i].items.length; j++) {
            if (data[i].items[j].valid) {
              if (data[i].items[j].type === 'special') {
                console.log('Found special item');
              }
            }
          }
        }
      }
    }
  `;

  const overview = await ComplexityCalculator.quickAnalyze(complexCode);
  console.log(`  ✅ 概览统计:`);
  console.log(`     - 函数总数: ${overview.functionCount}`);
  console.log(`     - 总复杂度: ${overview.totalComplexity}`);
  console.log(`     - 平均复杂度: ${overview.averageComplexity}`);
  console.log(`     - 最大复杂度: ${overview.maxComplexity}`);
  console.log(`     - 复杂函数 (≥5): ${overview.complexFunctions.length}`);
  
  if (overview.complexFunctions.length > 0) {
    console.log('     复杂函数详情:');
    overview.complexFunctions.forEach(func => {
      console.log(`       - ${func.name}: ${func.complexity}`);
    });
  }

  // 演示3: 批量文件分析
  console.log('\n3️⃣ 批量文件分析:');
  const fileResults = await ComplexityCalculator.analyzeFiles([]);
  console.log(`  ✅ 批量分析完成，处理了 ${fileResults.size} 个文件`);

  // 演示4: 资源管理验证
  console.log('\n4️⃣ 资源管理验证:');
  const initialHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
  
  // 连续调用多次静态方法
  await ComplexityCalculator.analyze('function test1() { return 1; }');
  await ComplexityCalculator.analyze('function test2() { return 2; }');
  await ComplexityCalculator.quickAnalyze('function test3() { return 3; }');
  
  const finalHandles = process._getActiveHandles ? process._getActiveHandles().length : 0;
  console.log(`  ✅ 资源管理验证:`);
  console.log(`     - 初始句柄数: ${initialHandles}`);
  console.log(`     - 最终句柄数: ${finalHandles}`);
  console.log(`     - 句柄泄漏: ${finalHandles === initialHandles ? '无' : '检测到'}`);

  console.log('\n🎉 IoC重构演示完成！');
  console.log('\n📋 重构亮点:');
  console.log('  • 💉 依赖注入 - 使用ComponentFactory创建组件');
  console.log('  • 🔄 Null Object模式 - 轻量级组件替代重量级组件');
  console.log('  • 🧹 自动资源清理 - 静态方法自动管理生命周期');
  console.log('  • ⚡ 性能优化 - 根据使用场景选择合适的组件');
  console.log('  • 🔧 配置驱动 - 通过CalculatorOptions控制组件行为');
}

// 运行演示
demonstrateStaticAPI().catch(console.error);