/**
 * 测试 ComplexityCalculator 的新异步重构
 */
import { ComplexityCalculator } from './src/core/calculator';

async function testAsyncRefactoring() {
  console.log('🔄 测试 ComplexityCalculator 异步重构...');

  try {
    // 测试简单代码
    const code = `
      function simpleFunction() {
        if (condition) {
          return true;
        }
        return false;
      }
    `;

    const results = await ComplexityCalculator.analyze(code);
    console.log('✅ 异步分析成功');
    
    const totalComplexity = results.reduce((sum, r) => sum + r.complexity, 0);
    console.log(`  - 总复杂度: ${totalComplexity}`);
    console.log(`  - 函数数量: ${results.length}`);
    
    if (results.length > 0) {
      console.log(`  - 第一个函数: ${results[0].name}, 复杂度: ${results[0].complexity}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testAsyncRefactoring().catch(console.error);