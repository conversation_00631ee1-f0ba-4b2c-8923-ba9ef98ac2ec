{"summary": {"totalComplexity": 11, "averageComplexity": 3.6666666666666665, "filesAnalyzed": 1, "functionsAnalyzed": 3, "highComplexityFunctions": 0}, "results": [{"filePath": "/var/folders/sf/rnr3jjbn3qb0c43yyjl3p9900000gn/T/cognitive-jgEGoE/test-mixed-logic.ts", "complexity": 11, "functions": [{"name": "mixedLogicExample", "complexity": 6, "line": 2, "column": 9, "filePath": "/var/folders/sf/rnr3jjbn3qb0c43yyjl3p9900000gn/T/cognitive-jgEGoE/test-mixed-logic.ts"}, {"name": "pureAndExample", "complexity": 5, "line": 8, "column": 9, "filePath": "/var/folders/sf/rnr3jjbn3qb0c43yyjl3p9900000gn/T/cognitive-jgEGoE/test-mixed-logic.ts"}, {"name": "defaultValueExample", "complexity": 0, "line": 14, "column": 9, "filePath": "/var/folders/sf/rnr3jjbn3qb0c43yyjl3p9900000gn/T/cognitive-jgEGoE/test-mixed-logic.ts"}], "averageComplexity": 3.6666666666666665}], "filterStatistics": {"totalFiles": 1, "displayedFiles": 1, "hiddenFiles": 0, "threshold": 1, "hasFiltering": false, "filterReason": "未应用文件级过滤", "hiddenFilesAvgComplexity": 0, "displayedFilesAvgComplexity": 11}, "metadata": {"schemaVersion": "2.1.0", "generatedAt": "2025-07-30T09:52:14.053Z", "format": "cognitive-complexity-json", "detailsEnabled": true, "contextEnabled": false, "contextAllEnabled": false, "errorRecoveryEnabled": true}}