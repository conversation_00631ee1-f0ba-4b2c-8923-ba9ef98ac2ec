#!/usr/bin/env bun

/**
 * 最小测试：验证 FileContentCache 定时器导致的超时问题
 */

import { getGlobalFileCache } from './src/utils/file-cache';

console.log('🔍 测试 FileContentCache 定时器问题...');

// 只是获取全局缓存实例（这会创建实例并启动定时器）
const cache = getGlobalFileCache();

console.log('✅ FileContentCache 实例已创建');
console.log('⏰ 定时器已启动，脚本应该在这里卡住...');
console.log('💭 如果你看到这条消息后脚本没有退出，就证实了定时器问题');

// 不做任何其他操作，脚本应该自然结束
// 但是因为 setInterval 的存在，进程不会退出