import { spawn } from 'child_process';
import { writeFileSync, mkdirSync, rmSync, existsSync } from 'fs';
import { join } from 'path';

async function testCLI() {
  const testDir = join(process.cwd(), 'temp-test-dir');
  
  // 创建测试目录和文件
  if (!existsSync(testDir)) {
    mkdirSync(testDir, { recursive: true });
  }
  
  writeFileSync(join(testDir, 'empty.ts'), 'export const CONSTANT = 42;');
  writeFileSync(join(testDir, 'simple.ts'), `
function simple() {
  if (condition) { // +1
    return true;
  }
  return false;
}
`);

  console.log('Testing CLI with default behavior...');
  
  // 测试CLI
  const result = await new Promise((resolve) => {
    const child = spawn('node', ['dist/cli/index.js', testDir], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      resolve({ stdout, stderr, exitCode: code });
    });
  });
  
  console.log('Exit Code:', result.exitCode);
  console.log('STDOUT:', result.stdout);
  console.log('STDERR:', result.stderr);
  
  // 清理
  if (existsSync(testDir)) {
    rmSync(testDir, { recursive: true, force: true });
  }
}

testCLI().catch(console.error);