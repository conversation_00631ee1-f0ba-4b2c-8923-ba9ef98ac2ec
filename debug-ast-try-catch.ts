#!/usr/bin/env bun

/**
 * 调试 AST 中 try-catch 节点结构
 */

import { ASTParser } from './src/core/parser';

async function debugAstTryCatch() {
  console.log('=== 调试 AST try-catch 节点结构 ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  try {
    const parser = new ASTParser();
    const ast = await parser.parseCode(testCode, 'debug.ts');
    console.log('=== AST 结构 ===');
    console.log(JSON.stringify(ast, null, 2));
    
    // 查找 try-catch 相关节点
    console.log('\n=== 查找 try-catch 节点 ===');
    findTryCatchNodes(ast);
    
  } catch (error) {
    console.error('解析错误:', error);
  }
}

function findTryCatchNodes(node: any, depth = 0): void {
  if (!node || typeof node !== 'object') {
    return;
  }
  
  const indent = '  '.repeat(depth);
  
  if (node.type === 'TryStatement') {
    console.log(`${indent}发现 TryStatement:`, {
      type: node.type,
      hasBlock: !!node.block,
      hasHandler: !!node.handler, 
      hasFinalizer: !!node.finalizer
    });
  }
  
  if (node.type === 'CatchClause') {
    console.log(`${indent}发现 CatchClause:`, {
      type: node.type,
      hasParam: !!node.param,
      hasBody: !!node.body
    });
  }
  
  // 递归查找子节点
  for (const key in node) {
    if (key === 'span' || key === 'type') continue;
    
    const value = node[key];
    if (Array.isArray(value)) {
      value.forEach(child => findTryCatchNodes(child, depth + 1));
    } else if (value && typeof value === 'object') {
      findTryCatchNodes(value, depth + 1);
    }
  }
}

debugAstTryCatch().catch(console.error);