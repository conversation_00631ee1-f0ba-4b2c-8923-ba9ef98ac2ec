#!/usr/bin/env bun

/**
 * Test to reproduce the exact bug from the report
 */

import { parse } from '@swc/core';
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

async function testSpecificBugCase() {
  console.log('=== Reproduce Specific Bug Report Case ===\n');

  // Exact code from bug report
  const testCode = `/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Ellipsis, enqueueSnackbar, useFireExport,
} from '@imile/components'

function someFunction() {
  return true;
}`;

  try {
    // Parse the code
    const ast = await parse(testCode, {
      syntax: 'typescript',
      tsx: false,
      decorators: true,
      dynamicImport: true,
    });

    console.log('AST Structure:');
    if (ast.type === 'Module' && ast.body) {
      ast.body.forEach((item, index) => {
        console.log(`Item ${index}: ${item.type}`);
        
        // Deep inspection of import declaration
        if (item.type === 'ImportDeclaration') {
          console.log('  Import details:');
          console.log(`    Source: ${(item as any).source?.value}`);
          console.log(`    Specifiers count: ${(item as any).specifiers?.length}`);
          
          // Check if there are any binary expressions in the import
          const checkForBinaryExpressions = (node: any, path: string = '') => {
            if (!node || typeof node !== 'object') return;
            
            if (node.type === 'BinaryExpression') {
              console.log(`    🔍 Found BinaryExpression at ${path}:`, {
                operator: node.operator,
                type: node.type
              });
            }
            
            // Recursively check properties
            for (const [key, value] of Object.entries(node)) {
              if (key !== 'span' && key !== 'parent') {
                if (Array.isArray(value)) {
                  value.forEach((item, i) => checkForBinaryExpressions(item, `${path}.${key}[${i}]`));
                } else if (value && typeof value === 'object') {
                  checkForBinaryExpressions(value, `${path}.${key}`);
                }
              }
            }
          };
          
          checkForBinaryExpressions(item, 'ImportDeclaration');
        }
      });
    }

    console.log('\n--- Running Complexity Analysis ---');

    const detailCollector = new DetailCollector();
    const visitor = new ComplexityVisitor(testCode, detailCollector, {
      includeDetails: true
    });

    // Visit the AST
    visitor.visit(ast);
    const results = visitor.getResults();

    console.log('\nComplexity Results:', JSON.stringify(results, null, 2));

    // Check each function's details for logical operator issues in early lines
    for (const func of results) {
      if (func.details) {
        console.log(`\nFunction "${func.name}" details:`);
        func.details.forEach(detail => {
          console.log(`  Line ${detail.line}: +${detail.increment} - ${detail.ruleId} - ${detail.description}`);
          
          // Flag potential false positives
          if (detail.line <= 5 && detail.ruleId === 'logical-operator') {
            console.error(`  ❌ POTENTIAL BUG: Logical operator detected on line ${detail.line} (import area)`);
          }
        });
      }
    }

  } catch (error) {
    console.error('Error during analysis:', error);
  }
}

// Create another test with simpler import that might trigger the bug
async function testSimpleImport() {
  console.log('\n=== Test Simple Import ===\n');

  const simpleCode = `import { a, b } from 'module'`;

  try {
    const ast = await parse(simpleCode, {
      syntax: 'typescript',
      tsx: false,
    });

    console.log('Simple import AST body length:', ast.body?.length);
    
    // Check for any BinaryExpression in the import
    const importDecl = ast.body?.[0];
    if (importDecl?.type === 'ImportDeclaration') {
      console.log('Import declaration found');
      
      // Create a minimal visitor to check what nodes get visited
      const visitor = new ComplexityVisitor(simpleCode);
      
      // Override the visit method to log all visited nodes
      const originalVisit = visitor.visit.bind(visitor);
      visitor.visit = function<T>(node: T): T {
        console.log(`Visiting node: ${(node as any)?.type}`);
        return originalVisit(node);
      };
      
      visitor.visit(ast);
    }

  } catch (error) {
    console.error('Error in simple import test:', error);
  }
}

testSpecificBugCase().then(() => testSimpleImport());