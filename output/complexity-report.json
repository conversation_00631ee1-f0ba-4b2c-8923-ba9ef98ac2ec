{"summary": {"totalComplexity": 4476, "averageComplexity": 2.1123171307220385, "filesAnalyzed": 98, "functionsAnalyzed": 2119, "highComplexityFunctions": 39}, "results": [{"filePath": "src/core/calculator.ts", "complexity": 301, "functions": [{"name": "calculate", "complexity": 38, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts", "severity": "Critical"}, {"name": "visitNode", "complexity": 25, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts", "severity": "Warning"}, {"name": "containsLogicalOperators", "complexity": 20, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts", "severity": "Warning"}, {"name": "detectLogicalOperatorMixing", "complexity": 14, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts", "severity": "Warning"}, {"name": "leftOperandIsPropertyChain", "complexity": 13, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts", "severity": "Warning"}, {"name": "visitLogicalOperatorsDepthFirst", "complexity": 12, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts", "severity": "Warning"}, {"name": "visit<PERSON><PERSON><PERSON><PERSON>", "complexity": 11, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "traverseAllChildren", "complexity": 10, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getActualFunctionNode", "complexity": 9, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "containsLogicalGrouping", "complexity": 9, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "shouldContinueDetailCollection", "complexity": 9, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "optimizeMemoryUsage", "complexity": 9, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getCalleeIdentifier", "complexity": 8, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isPropertyAccessPattern", "complexity": 7, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isChainedPropertyAccessPattern", "complexity": 7, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "addDetailStepSafely", "complexity": 6, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "tryAutoRegisterRule", "complexity": 6, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "setDetailsMode", "complexity": 5, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "extractBaseIdentifier", "complexity": 5, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "hasDirectLogicalChildren", "complexity": 5, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getNodeOperator", "complexity": 5, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "hasLogicalGroupingParentheses", "complexity": 5, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getMemoryUsage", "complexity": 5, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "validateAndNormalizeRuleId", "complexity": 4, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getNodeColumnNumber", "complexity": 4, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isPartOfDefaultValuePattern", "complexity": 4, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "get<PERSON><PERSON>ty<PERSON><PERSON>ess<PERSON><PERSON><PERSON>", "complexity": 4, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "hasNestedLogicalOperators", "complexity": 4, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getFinalRuleDescription", "complexity": 3, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isDefaultValueAssignment", "complexity": 3, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "hasLogicalMixingPotential", "complexity": 3, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "hasParenthesizedOperands", "complexity": 3, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "initializeDetailsMode", "complexity": 2, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "registerRulesToDetailCollector", "complexity": 2, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "visitNodeWithNesting", "complexity": 2, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isRecursiveCall", "complexity": 2, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getNodeLineNumber", "complexity": 2, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isDetailsMode", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "inferRuleIdFromNodeType", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getFallbackRuleId", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getRecursiveCallDescription", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getLoopDescription", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getLogicalOperatorDescription", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isLogicalOperator", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isInDefaultValueAssignmentContext", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "getCurrentFunctionName", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "hasConflictingLogicalOperators", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "collectLogicalOperators", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isDirectlyParenthesized", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "recordExemption", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "logDebug", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "logError", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "logPerformance", "complexity": 1, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "calculateFile", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "calculateCode", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "calculateFunctionComplexity", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "shouldIncreaseNesting", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "visit<PERSON><PERSON><PERSON><PERSON>", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "calculateNestingPenalty", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isInAssignmentContext", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isTopLevelLogicalExpression", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isLogicalRelevantNode", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}, {"name": "isNodeIgnored", "complexity": 0, "line": 1729, "column": 0, "filePath": "src/core/calculator.ts"}], "averageComplexity": 4.777777777777778}, {"filePath": "src/__test__/helpers/output-validator.ts", "complexity": 265, "functions": [{"name": "validateMultilineStructure", "complexity": 45, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts", "severity": "Critical"}, {"name": "extractMetrics", "complexity": 37, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts", "severity": "Critical"}, {"name": "validateAdvancedPerformance", "complexity": 20, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts", "severity": "Warning"}, {"name": "getSuggestions", "complexity": 19, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts", "severity": "Warning"}, {"name": "validateObjectAgainstSchema", "complexity": 16, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts", "severity": "Warning"}, {"name": "advancedPatternMatch", "complexity": 13, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts", "severity": "Warning"}, {"name": "compareOutputs", "complexity": 12, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts", "severity": "Warning"}, {"name": "validateTableOutput", "complexity": 11, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "validatePerformanceOutput", "complexity": 11, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "validateJSONOutput", "complexity": 10, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "fuzzyMatch", "complexity": 10, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "validateCLIResult", "complexity": 10, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "calculateSimilarity", "complexity": 9, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "createDebugSession", "complexity": 8, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "assertOutputMatches", "complexity": 7, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "createExplorer", "complexity": 6, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "findInOutput", "complexity": 5, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "assertOutputContains", "complexity": 5, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "generateDiffReport", "complexity": 4, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "buildRegexFlags", "complexity": 2, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "createDebugInfo", "complexity": 2, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "debugOutput", "complexity": 1, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "highlightKeywords", "complexity": 1, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}, {"name": "analyzeOutput", "complexity": 1, "line": 1338, "column": 0, "filePath": "src/__test__/helpers/output-validator.ts"}], "averageComplexity": 11.041666666666666}, {"filePath": "src/cli/commands.ts", "complexity": 184, "functions": [{"name": "analyzeFiles", "complexity": 38, "line": 796, "column": 0, "filePath": "src/cli/commands.ts", "severity": "Critical"}, {"name": "execute", "complexity": 33, "line": 796, "column": 0, "filePath": "src/cli/commands.ts", "severity": "Critical"}, {"name": "outputResults", "complexity": 29, "line": 796, "column": 0, "filePath": "src/cli/commands.ts", "severity": "Warning"}, {"name": "detectProjectType", "complexity": 21, "line": 796, "column": 0, "filePath": "src/cli/commands.ts", "severity": "Warning"}, {"name": "getIgnorePatterns", "complexity": 17, "line": 796, "column": 0, "filePath": "src/cli/commands.ts", "severity": "Warning"}, {"name": "discoverFiles", "complexity": 14, "line": 796, "column": 0, "filePath": "src/cli/commands.ts", "severity": "Warning"}, {"name": "validateOptions", "complexity": 13, "line": 796, "column": 0, "filePath": "src/cli/commands.ts", "severity": "Warning"}, {"name": "applyQualityGate", "complexity": 9, "line": 796, "column": 0, "filePath": "src/cli/commands.ts"}, {"name": "applyFilters", "complexity": 4, "line": 796, "column": 0, "filePath": "src/cli/commands.ts"}, {"name": "handleCreateBaseline", "complexity": 2, "line": 796, "column": 0, "filePath": "src/cli/commands.ts"}, {"name": "handleUpdateBaseline", "complexity": 2, "line": 796, "column": 0, "filePath": "src/cli/commands.ts"}, {"name": "ensureDirectoryExists", "complexity": 1, "line": 796, "column": 0, "filePath": "src/cli/commands.ts"}, {"name": "handleUIMode", "complexity": 1, "line": 796, "column": 0, "filePath": "src/cli/commands.ts"}, {"name": "isGlobPattern", "complexity": 0, "line": 796, "column": 0, "filePath": "src/cli/commands.ts"}], "averageComplexity": 13.142857142857142}, {"filePath": "src/engine/iterative-algorithms.ts", "complexity": 154, "functions": [{"name": "getChildNodes", "complexity": 51, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts", "severity": "Critical"}, {"name": "extractFunctionName", "complexity": 14, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts", "severity": "Warning"}, {"name": "find<PERSON><PERSON>est<PERSON>eaf", "complexity": 13, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts", "severity": "Warning"}, {"name": "traverseDepthFirst", "complexity": 12, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts", "severity": "Warning"}, {"name": "traverseBreadthFirst", "complexity": 12, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts", "severity": "Warning"}, {"name": "flatten", "complexity": 10, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "findPattern", "complexity": 10, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "buildPathMap", "complexity": 7, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "binarySearch", "complexity": 6, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "traverseInBatches", "complexity": 4, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "findNodesIteratively", "complexity": 4, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "slidingWindowSearch", "complexity": 3, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "analyzeFileIteratively", "complexity": 2, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "getNodeDepth", "complexity": 1, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "calculateFunctionComplexity", "complexity": 1, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "analyzeFunctionsInBatches", "complexity": 1, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "calculateNestingDepth", "complexity": 1, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "calculateCyclomaticComplexity", "complexity": 1, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "calculateNodeComplexity", "complexity": 1, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "createIterativeCalculator", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "createIterativeTraverser", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "reset", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "shouldSkipNode", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "reset", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "initializeComplexityRules", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "isFunctionNode", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "isNestingNode", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "isDecisionNode", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "createIterativeCalculator", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}, {"name": "createIterativeTraverser", "complexity": 0, "line": 743, "column": 0, "filePath": "src/engine/iterative-algorithms.ts"}], "averageComplexity": 5.133333333333334}, {"filePath": "src/core/parser.ts", "complexity": 132, "functions": [{"name": "findFunctionsInNode", "complexity": 29, "line": 559, "column": 0, "filePath": "src/core/parser.ts", "severity": "Warning"}, {"name": "findFunctionsIterative", "complexity": 22, "line": 559, "column": 0, "filePath": "src/core/parser.ts", "severity": "Warning"}, {"name": "getFunctionName", "complexity": 21, "line": 559, "column": 0, "filePath": "src/core/parser.ts", "severity": "Warning"}, {"name": "visit<PERSON><PERSON><PERSON><PERSON>", "complexity": 11, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "addChildrenTo<PERSON><PERSON>ue", "complexity": 11, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "getLocation", "complexity": 8, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "getLineFromSpan", "complexity": 6, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "isFunctionNode", "complexity": 5, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "visitChildrenSelectively", "complexity": 4, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "isLineIgnored", "complexity": 4, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "findIgnoreExemptions", "complexity": 4, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "findFunctions", "complexity": 3, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "detectSyntax", "complexity": 2, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "parseCode", "complexity": 1, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "parseFile", "complexity": 1, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "visitNode", "complexity": 0, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "visit<PERSON><PERSON><PERSON><PERSON>", "complexity": 0, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "isFunctionDeclaration", "complexity": 0, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "isFunctionExpression", "complexity": 0, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}, {"name": "hasIgnoreComment", "complexity": 0, "line": 559, "column": 0, "filePath": "src/core/parser.ts"}], "averageComplexity": 6.6}, {"filePath": "src/engine/debug-system.ts", "complexity": 131, "functions": [{"name": "checkBreakpoints", "complexity": 26, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts", "severity": "Warning"}, {"name": "matchesBreakpoint", "complexity": 15, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts", "severity": "Warning"}, {"name": "buildCallStack", "complexity": 15, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts", "severity": "Warning"}, {"name": "getEvents", "complexity": 9, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "checkStepMode", "complexity": 7, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "updateVisualTrace", "complexity": 7, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "emitEvent", "complexity": 6, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "recordCacheAccess", "complexity": 4, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "runDiagnostics", "complexity": 4, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "endSession", "complexity": 3, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "removeEventListener", "complexity": 3, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "shouldTrace", "complexity": 3, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "detectPerformanceIssues", "complexity": 3, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "detectConfigurationIssues", "complexity": 3, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "recordRuleEnd", "complexity": 2, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "recordNodeStart", "complexity": 2, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "toggleBreakpoint", "complexity": 2, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "generateDebugReport", "complexity": 2, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "startSession", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "recordRuleStart", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "recordNodeEnd", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "removeBreakpoint", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "continue", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "getExecutionState", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "addEventListener", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "evaluateComplexityCondition", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "captureSnapshot", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "detectCorrectnessIssues", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "detectUsageIssues", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "calculateAverageRuleTime", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "calculateOverallCacheHitRate", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "getMemoryUsageStats", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "logInfo", "complexity": 1, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "recordError", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "recordWarning", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "addBreakpoint", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "stepI<PERSON>", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "stepOver", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "stepOut", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "getDiagnostics", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "getVisualTrace", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "createEmptyVisualTrace", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "initializeProblemDetectors", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "getEventStatistics", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "generateSessionId", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "generateId", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}, {"name": "logEvent", "complexity": 0, "line": 1585, "column": 0, "filePath": "src/engine/debug-system.ts"}], "averageComplexity": 2.7872340425531914}, {"filePath": "src/rules/jsx-hook-complexity.ts", "complexity": 126, "functions": [{"name": "analyzeEffectHook", "complexity": 13, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts", "severity": "Warning"}, {"name": "findControlStructures", "complexity": 13, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts", "severity": "Warning"}, {"name": "analyzeCallbackHook", "complexity": 11, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "analyzeMemoHook", "complexity": 11, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "analyzeHookComplexity", "complexity": 10, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isHookCall", "complexity": 9, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "hasComplexInitialization", "complexity": 9, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "analyzeCustomHook", "complexity": 8, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "analyzeHookCallPattern", "complexity": 6, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isComplexDependency", "complexity": 6, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isComplexArgument", "complexity": 6, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "analyzeStateHook", "complexity": 5, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "analyzeFunctionComplexity", "complexity": 5, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "analyzeDependencyArray", "complexity": 5, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isKnownHookName", "complexity": 3, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isCustomHook", "complexity": 3, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "getMemberExpressionDepth", "complexity": 2, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "canHandle", "complexity": 1, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "evaluate", "complexity": 0, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isEffectHook", "complexity": 0, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isCallbackHook", "complexity": 0, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isMemoHook", "complexity": 0, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}, {"name": "isBuiltinHook", "complexity": 0, "line": 688, "column": 0, "filePath": "src/rules/jsx-hook-complexity.ts"}], "averageComplexity": 5.478260869565218}, {"filePath": "src/config/project-detector.ts", "complexity": 114, "functions": [{"name": "scoreProjectType", "complexity": 72, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts", "severity": "Critical"}, {"name": "detectSubProjects", "complexity": 9, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "selectBestMatch", "complexity": 8, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "detect<PERSON><PERSON>repo", "complexity": 8, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "generateConfigRecommendation", "complexity": 6, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "detectProject", "complexity": 5, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "scoreProjectTypes", "complexity": 3, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "findFiles", "complexity": 1, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "findDirectories", "complexity": 1, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "addProjectSpecificRecommendations", "complexity": 1, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "detectProjectType", "complexity": 0, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "generateProjectConfig", "complexity": 0, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "generateSmartConfig", "complexity": 0, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "clearCache", "complexity": 0, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "detectProjectType", "complexity": 0, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "generateProjectConfig", "complexity": 0, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}, {"name": "generateSmartConfig", "complexity": 0, "line": 939, "column": 0, "filePath": "src/config/project-detector.ts"}], "averageComplexity": 6.705882352941177}, {"filePath": "src/plugins/validator.ts", "complexity": 110, "functions": [{"name": "validateConfigAgainstSchema", "complexity": 26, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts", "severity": "Warning"}, {"name": "validateBasicStructure", "complexity": 18, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts", "severity": "Warning"}, {"name": "validateConfigValue", "complexity": 16, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts", "severity": "Warning"}, {"name": "validateSecurity", "complexity": 12, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts", "severity": "Warning"}, {"name": "validateConfigProperties", "complexity": 10, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "validateConfigSchema", "complexity": 5, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "compareVersions", "complexity": 5, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "validate<PERSON>lugin", "complexity": 4, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "validateCompatibility", "complexity": 4, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "isVersionCompatible", "complexity": 3, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "validateRules", "complexity": 2, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "validateDependencies", "complexity": 2, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "validateEngineCompatibility", "complexity": 2, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "validateConfig", "complexity": 1, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "isValidVersion", "complexity": 0, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "isValidVersionRange", "complexity": 0, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}, {"name": "isValidDependencyFormat", "complexity": 0, "line": 680, "column": 0, "filePath": "src/plugins/validator.ts"}], "averageComplexity": 6.470588235294118}, {"filePath": "src/rules/jsx-event-handler-rule.ts", "complexity": 109, "functions": [{"name": "analyzeNodeContent", "complexity": 14, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts", "severity": "Warning"}, {"name": "determineHandlerType", "complexity": 10, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "calculateBaseComplexity", "complexity": 10, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "analyzeEventHandler", "complexity": 8, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "determineHandlerPattern", "complexity": 8, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "analyzeHandlerNode", "complexity": 7, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "hasExpensiveOperations", "complexity": 7, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "isDelegatedPattern", "complexity": 6, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "isStateUpdate", "complexity": 5, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "generateEventHandlerSuggestions", "complexity": 5, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "canHandle", "complexity": 4, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "isAsyncCall", "complexity": 4, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "calculateEventHandlerComplexity", "complexity": 4, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "isEventAttribute", "complexity": 3, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "isEventHandlerFunction", "complexity": 3, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "detectPerformanceIssues", "complexity": 3, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "evaluateOptimality", "complexity": 3, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "extractEventType", "complexity": 2, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "hasPerformanceAntiPatterns", "complexity": 2, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "looksLikeEventHandler", "complexity": 1, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "evaluate", "complexity": 0, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}, {"name": "isApiCall", "complexity": 0, "line": 604, "column": 0, "filePath": "src/rules/jsx-event-handler-rule.ts"}], "averageComplexity": 4.954545454545454}, {"filePath": "src/cache/manager.ts", "complexity": 107, "functions": [{"name": "detectAccessPattern", "complexity": 15, "line": 981, "column": 0, "filePath": "src/cache/manager.ts", "severity": "Warning"}, {"name": "preWarmCache", "complexity": 14, "line": 981, "column": 0, "filePath": "src/cache/manager.ts", "severity": "Warning"}, {"name": "detectNodePattern", "complexity": 11, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "performIncrementalAnalysis", "complexity": 7, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "predictEvictionCandidates", "complexity": 6, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "evictIfNecessary", "complexity": 5, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "calculateHitRate", "complexity": 4, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "predictCacheNeeds", "complexity": 4, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "get", "complexity": 3, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "cleanupExpired", "complexity": 3, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "invalidateCache", "complexity": 3, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "delete", "complexity": 2, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "suggestCacheOptimizations", "complexity": 2, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getCachedNodeResult", "complexity": 2, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getCachedRuleResult", "complexity": 2, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getCachedTypeInfo", "complexity": 2, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "peekCachedTypeInfo", "complexity": 2, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getCachedByPattern", "complexity": 2, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "optimizeCacheForChanges", "complexity": 2, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "set", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "has", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "destroy", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "isExpired", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "evictLRU", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "estimateSize", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getLayerStats", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "updateAverageTime", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "recordAccess", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "setCachedNodeResult", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "setCachedRuleResult", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "setCachedTypeInfo", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "setCachedByPattern", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "createSampleContext", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getDefaultComplexityForType", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getExemptionRulesForType", "complexity": 1, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "clear", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "size", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "memoryUsage", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getStats", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "updateAccessOrder", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "generateNodeKey", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "generateRuleKey", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "generateTypeKey", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "generatePatternKey", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "generateContextHash", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "hashNode", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "recordHit", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "recordMiss", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "recordSet", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "recordEviction", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getStatistics", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "reset", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "createEmptyStatistics", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "updateOverallHitRate", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "clearCache", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getHitRate", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getSize", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "getCacheOptimizations", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "destroy", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "identifyCommonNodeTypes", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}, {"name": "estimateSize", "complexity": 0, "line": 981, "column": 0, "filePath": "src/cache/manager.ts"}], "averageComplexity": 1.7540983606557377}, {"filePath": "src/engine/execution-pool.ts", "complexity": 102, "functions": [{"name": "waitForTasks", "complexity": 20, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts", "severity": "Warning"}, {"name": "setMaxConcurrency", "complexity": 14, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts", "severity": "Warning"}, {"name": "adjustWorkerPool", "complexity": 9, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "executeTaskWithWorker", "complexity": 9, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "buildExecutionPlan", "complexity": 8, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "processQueueWithLoadBalancing", "complexity": 8, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "calculateDynamicPriority", "complexity": 7, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "enqueue", "complexity": 4, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "executeRules", "complexity": 4, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "executeTask", "complexity": 3, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "isRecoverableError", "complexity": 3, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "notifyErrorHandlers", "complexity": 3, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "shouldRetryTask", "complexity": 2, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "updateStats", "complexity": 2, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "updateStats", "complexity": 1, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "findOptimalWorker", "complexity": 1, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "getOptimalConcurrency", "complexity": 1, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "estimateExecutionTime", "complexity": 1, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "getCurrentLoad", "complexity": 1, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "initializeWorkers", "complexity": 1, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "dequeue", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "isEmpty", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "size", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "peek", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "clear", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "executeWithTimeout", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "areDependenciesSatisfied", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "updateRealTimeStats", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "startThroughputMonitoring", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "updateThroughput", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "calculateOptimalParallelism", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "onRuleError", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "getStatistics", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "shutdown", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "createTask", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "initializeStats", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "startExecution", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "processQueue", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}, {"name": "sleep", "complexity": 0, "line": 796, "column": 0, "filePath": "src/engine/execution-pool.ts"}], "averageComplexity": 2.6153846153846154}, {"filePath": "src/config/manager.ts", "complexity": 98, "functions": [{"name": "validateConfig", "complexity": 37, "line": 351, "column": 0, "filePath": "src/config/manager.ts", "severity": "Critical"}, {"name": "loadConfig", "complexity": 27, "line": 351, "column": 0, "filePath": "src/config/manager.ts", "severity": "Warning"}, {"name": "mergeRuntimeExcludes", "complexity": 17, "line": 351, "column": 0, "filePath": "src/config/manager.ts", "severity": "Warning"}, {"name": "isValidGlobPattern", "complexity": 9, "line": 351, "column": 0, "filePath": "src/config/manager.ts"}, {"name": "mergeWithDefaults", "complexity": 5, "line": 351, "column": 0, "filePath": "src/config/manager.ts"}, {"name": "validateExcludePatterns", "complexity": 3, "line": 351, "column": 0, "filePath": "src/config/manager.ts"}], "averageComplexity": 16.333333333333332}, {"filePath": "src/plugins/version-manager.ts", "complexity": 93, "functions": [{"name": "satisfies<PERSON><PERSON><PERSON>", "complexity": 11, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "validateVersionFormat", "complexity": 10, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "compareVersions", "complexity": 9, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "checkDependencyCompatibility", "complexity": 7, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "getUpgradePath", "complexity": 6, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "buildCompatibilityMatrix", "complexity": 6, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "analyzeDependencyChanges", "complexity": 6, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "generateChangelog", "complexity": 6, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "parseVersion", "complexity": 3, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "generateUpgradeInfo", "complexity": 3, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "checkApiCompatibility", "complexity": 3, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "hasMinorBreakingChanges", "complexity": 3, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "checkPairwiseCompatibility", "complexity": 3, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "generateCompatibilityRecommendations", "complexity": 3, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "estimateUpgradeDuration", "complexity": 3, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "satisfies<PERSON><PERSON><PERSON><PERSON><PERSON>", "complexity": 2, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "checkEngineCompatibility", "complexity": 2, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "getConflictSeverity", "complexity": 2, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "satisfiesCaretRange", "complexity": 1, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "checkNodeCompatibility", "complexity": 1, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "checkConfigurationCompatibility", "complexity": 1, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "extractVersionRange", "complexity": 1, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "assessUpgradeRisks", "complexity": 1, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "checkPluginCompatibility", "complexity": 0, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "isRuleApiCompatible", "complexity": 0, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "validateConfigSchemaCompatibility", "complexity": 0, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}, {"name": "extractPluginId", "complexity": 0, "line": 773, "column": 0, "filePath": "src/plugins/version-manager.ts"}], "averageComplexity": 3.4444444444444446}, {"filePath": "src/__test__/helpers/data-consistency-validator.ts", "complexity": 92, "functions": [{"name": "validateSummaryData", "complexity": 17, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts", "severity": "Warning"}, {"name": "validateFileResults", "complexity": 10, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "validatePerformanceData", "complexity": 10, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "validateOutputFormat", "complexity": 9, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "validateFixtureConsistency", "complexity": 7, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "validateTestSuite", "complexity": 6, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "quickValidateComplexity", "complexity": 6, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "quickValidateComplexity", "complexity": 6, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "validateOutputContent", "complexity": 5, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "quickValidateOutput", "complexity": 3, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "quickValidateOutput", "complexity": 3, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "validateCLIOutputConsistency", "complexity": 2, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "validateExitCode", "complexity": 2, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "generateConsistencyReport", "complexity": 2, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "createMigrationValidator", "complexity": 2, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "quickValidatePerformance", "complexity": 1, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}, {"name": "quickValidatePerformance", "complexity": 1, "line": 637, "column": 0, "filePath": "src/__test__/helpers/data-consistency-validator.ts"}], "averageComplexity": 5.411764705882353}, {"filePath": "src/plugins/dependency-resolver.ts", "complexity": 89, "functions": [{"name": "detectVersionConflicts", "complexity": 16, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts", "severity": "Warning"}, {"name": "validateDependencies", "complexity": 14, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts", "severity": "Warning"}, {"name": "optimizeLoadOrder", "complexity": 10, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "detectCircularDependencies", "complexity": 9, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "generateDependencyReport", "complexity": 8, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "resolveDependencies", "complexity": 6, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "calculateLoadOrder", "complexity": 6, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "getTransitiveDependencies", "complexity": 5, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "detectMutualDependencies", "complexity": 5, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "canSafelyRemove", "complexity": 4, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "calculateDependencyDepth", "complexity": 4, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "buildDependencyGraph", "complexity": 1, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "extractCircularPath", "complexity": 1, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}, {"name": "areVersionsCompatible", "complexity": 0, "line": 529, "column": 0, "filePath": "src/plugins/dependency-resolver.ts"}], "averageComplexity": 6.357142857142857}, {"filePath": "src/engine/object-pool.ts", "complexity": 81, "functions": [{"name": "acquire", "complexity": 19, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts", "severity": "Warning"}, {"name": "updateStatistics", "complexity": 10, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "release", "complexity": 6, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "releaseBatch", "complexity": 3, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "preWarm", "complexity": 3, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "getHealthStatus", "complexity": 3, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "findPooledObject", "complexity": 3, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "getOverallHealth", "complexity": 3, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "shrink", "complexity": 2, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "destroy", "complexity": 2, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "isObjectValid", "complexity": 2, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "validate", "complexity": 2, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "acquireAnalysisContext", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "releaseAnalysisContext", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "acquireNodeAnalysis", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "releaseNodeAnalysis", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "acquireRuleResult", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "releaseRuleResult", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "acquireBatch", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "cleanup", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "startMaintenance", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "shouldRetainObject", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "removeObject", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "cleanupExpiredObjects", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "validate", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "getAllStatistics", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "cleanupAll", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "preWarmAll", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "destroyAll", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "acquireAnalysisContext", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "releaseAnalysisContext", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "acquireNodeAnalysis", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "releaseNodeAnalysis", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "acquireRuleResult", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "releaseRuleResult", "complexity": 1, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "getStatistics", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "clear", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "initialize", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "createNewObject", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "canCreateNewObject", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "updateAcquisitionStatistics", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "createEmptyStatistics", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "create", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "reset", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "validate", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "size", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "create", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "reset", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "size", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "create", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "reset", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "size", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "getPool", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "registerPool", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "initializeStandardPools", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "reset", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "getSize", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "create", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "reset", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "validate", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}, {"name": "size", "complexity": 0, "line": 856, "column": 0, "filePath": "src/engine/object-pool.ts"}], "averageComplexity": 1.3064516129032258}, {"filePath": "src/engine/registry.ts", "complexity": 79, "functions": [{"name": "validateDependencies", "complexity": 8, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "validateRule", "complexity": 8, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "loadPlugin", "complexity": 7, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "assignRuleToCategory", "complexity": 7, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "resolveDependencies", "complexity": 5, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "detectAndRecordConflicts", "complexity": 5, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getRulesForNode", "complexity": 4, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "unloadPlugin", "complexity": 4, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "applyConflictResolution", "complexity": 4, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getStatistics", "complexity": 3, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "findRuleCategory", "complexity": 3, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "findDependentRules", "complexity": 3, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "isValidPlugin", "complexity": 3, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "unregisterRule", "complexity": 2, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "resolveConflict", "complexity": 2, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "detectRuleConflict", "complexity": 2, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "registerRule", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getRule", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getRulesByCategory", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "isRuleEnabled", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "enableRule", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "disableRule", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "initializeCategories", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "removeRuleFromCategories", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "generateDependencySuggestions", "complexity": 1, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getAllRules", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getRulesByPriority", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getLoadedPlugins", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "detectConflicts", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "registerRule", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "unregisterRule", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getRule", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getAllRules", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getRulesForNode", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getRulesByPriority", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getRulesByCategory", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "resolveDependencies", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "validateDependencies", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "loadPlugin", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "unloadPlugin", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "getLoadedPlugins", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "isRuleEnabled", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "enableRule", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "disableRule", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "detectConflicts", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}, {"name": "resolveConflict", "complexity": 0, "line": 611, "column": 0, "filePath": "src/engine/registry.ts"}], "averageComplexity": 1.7173913043478262}, {"filePath": "src/config/modern-manager.ts", "complexity": 73, "functions": [{"name": "deepMerge", "complexity": 17, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts", "severity": "Warning"}, {"name": "logConfigChanges", "complexity": 7, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "loadSmartConfig", "complexity": 6, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "disableHotReload", "complexity": 6, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "validateRulesConfig", "complexity": 6, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "applySmartRecommendations", "complexity": 4, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "loadRawConfig", "complexity": 4, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "validate<PERSON><PERSON><PERSON><PERSON><PERSON>s", "complexity": 4, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "validatePerformanceConfig", "complexity": 4, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "generateWarningsAndSuggestions", "complexity": 4, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "loadConfig", "complexity": 3, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "enableHotReload", "complexity": 2, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "getToolVersion", "complexity": 2, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "getInstance", "complexity": 1, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "clearCache", "complexity": 1, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "resolveConfig", "complexity": 1, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "validateOutputConfig", "complexity": 1, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "loadConfig", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "loadSmartConfig", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "enableConfigHotReload", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "mergeConfigs", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "createDefaultConfig", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "detectProject", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "generateProjectRecommendation", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "mergeConfigs", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "validateConfig", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "createDefaultResolvedConfig", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "startWatching", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "initializeSchemas", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "formatValidationErrors", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "configToPartial", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "loadConfig", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "loadSmartConfig", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "enableConfigHotReload", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "mergeConfigs", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}, {"name": "createDefaultConfig", "complexity": 0, "line": 962, "column": 0, "filePath": "src/config/modern-manager.ts"}], "averageComplexity": 2.0277777777777777}, {"filePath": "src/__test__/helpers/test-data-optimizer.ts", "complexity": 71, "functions": [{"name": "generateUsageReport", "complexity": 10, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "createVariation", "complexity": 9, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "levenshteinDistance", "complexity": 8, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "createBatchData", "complexity": 7, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "optimizeMemoryUsage", "complexity": 6, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "initialize", "complexity": 4, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "loadPersistedCache", "complexity": 4, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "warmupCache", "complexity": 3, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "calculateSimilarity", "complexity": 3, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "getCachedData", "complexity": 3, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "findReusableData", "complexity": 2, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "setCachedData", "complexity": 2, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "getStatistics", "complexity": 2, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "persistCache", "complexity": 2, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "getOrCreateFixture", "complexity": 1, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "getOrCreateScenario", "complexity": 1, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "getOrCreateConfig", "complexity": 1, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "updateUsageStats", "complexity": 1, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "calculateChecksum", "complexity": 1, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "calculateCacheSize", "complexity": 1, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "scheduleCleanup", "complexity": 0, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "clearCache", "complexity": 0, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "getConfiguration", "complexity": 0, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "updateConfiguration", "complexity": 0, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "createOptimizedFixtures", "complexity": 0, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "createOptimizedScenarios", "complexity": 0, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}, {"name": "getOrCreatePerformanceData", "complexity": 0, "line": 762, "column": 0, "filePath": "src/__test__/helpers/test-data-optimizer.ts"}], "averageComplexity": 2.6296296296296298}, {"filePath": "src/__test__/helpers/cli-testing-utils.ts", "complexity": 69, "functions": [{"name": "userEvent", "complexity": 10, "line": 1089, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "cleanup", "complexity": 10, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "kill", "complexity": 9, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "optimizeTestSuite", "complexity": 7, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "_handleTimeout", "complexity": 5, "line": 1012, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "validateExecution", "complexity": 5, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "returnInstance", "complexity": 4, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "renderCLI", "complexity": 4, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "waitForOutput", "complexity": 3, "line": 1067, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "cleanup", "complexity": 3, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "queryByText", "complexity": 2, "line": 1053, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "_clearTimeout", "complexity": 1, "line": 1006, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "findByText", "complexity": 1, "line": 1044, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getByText", "complexity": 1, "line": 1060, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "waitForExit", "complexity": 1, "line": 1080, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getInstance", "complexity": 1, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "borrowInstance", "complexity": 1, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "runTestBatch", "complexity": 1, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "_waitPromise_internal", "complexity": 0, "line": 904, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "_handleBufferOverflow", "complexity": 0, "line": 1024, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "stdout", "complexity": 0, "line": 1034, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "stderr", "complexity": 0, "line": 1038, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "exitCode", "complexity": 0, "line": 1039, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "isRunning", "complexity": 0, "line": 1041, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "clear", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getRuntime", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getProcessId", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "isKilled", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getPoolKey", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "recordExecution", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getMetrics", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getSuggestions", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "reset", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "waitForOutput", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "findByText", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "queryByText", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "sendInput", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "cleanupAll", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "forceCleanupAll", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getActiveProcessCount", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getActiveProcessesInfo", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "setGlobalErrorHandler", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getDefaultConfig", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "setDefaultConfig", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getPerformanceMetrics", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getPerformanceSuggestions", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "resetPerformanceMonitor", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getProcessPoolStatus", "complexity": 0, "line": 1098, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "findByText", "complexity": 0, "line": 751, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "queryByText", "complexity": 0, "line": 752, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "getByText", "complexity": 0, "line": 753, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "waitForOutput", "complexity": 0, "line": 756, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "waitForExit", "complexity": 0, "line": 756, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "clear", "complexity": 0, "line": 764, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "kill", "complexity": 0, "line": 766, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "type", "complexity": 0, "line": 759, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}, {"name": "keyboard", "complexity": 0, "line": 762, "column": 0, "filePath": "src/__test__/helpers/cli-testing-utils.ts"}], "averageComplexity": 1.1896551724137931}, {"filePath": "src/rules/smart-conditional-rendering.ts", "complexity": 69, "functions": [{"name": "extractConditionInfo", "complexity": 16, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts", "severity": "Warning"}, {"name": "calculateComplexNestingDepth", "complexity": 14, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts", "severity": "Warning"}, {"name": "checkOperandComplexity", "complexity": 9, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "analyzeConditionalPattern", "complexity": 7, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "collectLogicalOperators", "complexity": 7, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "generateConditionalSuggestions", "complexity": 5, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "isInJSXContext", "complexity": 3, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "canHandle", "complexity": 2, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "hasComplexLogic", "complexity": 2, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "calculateLogicComplexity", "complexity": 2, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "hasComplexOperands", "complexity": 1, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "getOperatorComplexity", "complexity": 1, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "evaluate", "complexity": 0, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}, {"name": "hasMultipleLogicalOperators", "complexity": 0, "line": 471, "column": 0, "filePath": "src/rules/smart-conditional-rendering.ts"}], "averageComplexity": 4.928571428571429}, {"filePath": "src/plugins/manager.ts", "complexity": 68, "functions": [{"name": "updatePluginConfig", "complexity": 10, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "loadPlugin", "complexity": 6, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "unloadPlugin", "complexity": 6, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "watchPlugins", "complexity": 5, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "isValidPluginObject", "complexity": 5, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "watchPlugin", "complexity": 4, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "enablePlugin", "complexity": 3, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "disablePlugin", "complexity": 3, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "getPluginStatistics", "complexity": 3, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "loadPluginFromFile", "complexity": 3, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "registerPluginRules", "complexity": 3, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "unregisterPluginRules", "complexity": 3, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "reloadPlugin", "complexity": 2, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "backupPluginConfig", "complexity": 2, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "getPlugin", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "resolveDependencies", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "validateDependencies", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "getPluginConfig", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "getPluginHealth", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "resolvePluginSource", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "loadPluginFromPackage", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "createPluginMetadata", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "initializePluginConfig", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "unwatchPlugin", "complexity": 1, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "getAllPlugins", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "getActivePlugins", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "getInactivePlugins", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "isWatching", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "validate<PERSON>lugin", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "createSandbox", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "on", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "off", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "emit", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "loadPluginFromUrl", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "initializePluginStats", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "initializePluginHealth", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}, {"name": "setupError<PERSON><PERSON>ling", "complexity": 0, "line": 904, "column": 0, "filePath": "src/plugins/manager.ts"}], "averageComplexity": 1.837837837837838}, {"filePath": "src/formatters/json.ts", "complexity": 64, "functions": [{"name": "collectDiagnostics", "complexity": 57, "line": 486, "column": 0, "filePath": "src/formatters/json.ts", "severity": "Critical"}, {"name": "enrichWithDiagnostics", "complexity": 3, "line": 486, "column": 0, "filePath": "src/formatters/json.ts"}, {"name": "enrichWithDiagnosticsAsync", "complexity": 3, "line": 486, "column": 0, "filePath": "src/formatters/json.ts"}, {"name": "writeToFile", "complexity": 1, "line": 486, "column": 0, "filePath": "src/formatters/json.ts"}, {"name": "format", "complexity": 0, "line": 486, "column": 0, "filePath": "src/formatters/json.ts"}, {"name": "enrichWithSeverity", "complexity": 0, "line": 486, "column": 0, "filePath": "src/formatters/json.ts"}, {"name": "enrichWithSeverityAsync", "complexity": 0, "line": 486, "column": 0, "filePath": "src/formatters/json.ts"}, {"name": "enrichDetailsWithContext", "complexity": 0, "line": 486, "column": 0, "filePath": "src/formatters/json.ts"}, {"name": "enrichDetailsWithContextAsync", "complexity": 0, "line": 486, "column": 0, "filePath": "src/formatters/json.ts"}], "averageComplexity": 7.111111111111111}, {"filePath": "src/formatters/text.ts", "complexity": 64, "functions": [{"name": "formatRuleDescription", "complexity": 11, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "formatDetailSteps", "complexity": 9, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "format", "complexity": 8, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "generateContextFrame", "complexity": 7, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "formatFunctionResult", "complexity": 6, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "generateRecoveryContextFrame", "complexity": 6, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "formatComplexity", "complexity": 6, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "formatFileResult", "complexity": 3, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "formatDetailStep", "complexity": 2, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "shouldShowContext", "complexity": 2, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "generateFallbackContextInfo", "complexity": 2, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "writeToFile", "complexity": 1, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "formatSummary", "complexity": 1, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "sortFunctions", "complexity": 0, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}, {"name": "createFilterOptions", "complexity": 0, "line": 410, "column": 0, "filePath": "src/formatters/text.ts"}], "averageComplexity": 4.266666666666667}, {"filePath": "src/__test__/helpers/test-utils.ts", "complexity": 61, "functions": [{"name": "generateTypeScriptCode", "complexity": 20, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts", "severity": "Warning"}, {"name": "runConcurrencyTest", "complexity": 12, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts", "severity": "Warning"}, {"name": "runCLIBenchmark", "complexity": 5, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "monitorSystemResources", "complexity": 3, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createNestedTestCode", "complexity": 2, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectCLIOutput", "complexity": 2, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectCLIOutputMatches", "complexity": 2, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectCLIJSONOutput", "complexity": 2, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectCLITableOutput", "complexity": 2, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectCLIPerformance", "complexity": 2, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "calculateExpectedNestedComplexity", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "cleanupTempFile", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectCLIFailure", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "validateCLIComprehensive", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateTableOutput", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "endTimer", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectPerformance", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generatePerformanceReport", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "getCurrentPerformanceStats", "complexity": 1, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createMockAnalysisContext", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createMockAsyncRuleEngine", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "analyzeCode", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createMockFunctionResult", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createMockFileResult", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createMockAnalysisResult", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectComplexity", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createTestCode", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createLogicalOperatorTestCode", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "withTempDir", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "wait", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "executeCommand", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createTempFile", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "executeCLITest", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "expectCLISuccess", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "runCLIScenario", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "debugCLIOutput", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "randomComplexity", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "randomFunctionName", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "randomFilePath", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateTestFunctions", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateCLITestScenario", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateCLITestBatch", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateCLITestConfig", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generatePerformanceBenchmark", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateCLIOutput", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateComplexityJSON", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateInteractiveFlow", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateErrorScenario", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateStressTestData", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateConcurrencyConfig", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "generateTestFixturePackage", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "initializeBenchmarks", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "addBenchmark", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "getBenchmark", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "startTimer", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "measureAsync", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "measure", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "measureCLIPerformance", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "cleanup", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "resetBenchmarks", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createTestContext", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createTestNode", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createMockAnalysisContext", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}, {"name": "createMockAsyncRuleEngine", "complexity": 0, "line": 1479, "column": 0, "filePath": "src/__test__/helpers/test-utils.ts"}], "averageComplexity": 0.953125}, {"filePath": "src/engine/performance-monitor.ts", "complexity": 61, "functions": [{"name": "calculateFunctionImpact", "complexity": 6, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "calculateTrend", "complexity": 5, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "calculateFileImpact", "complexity": 4, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "stop", "complexity": 3, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "startMonitoring", "complexity": 3, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "updateTrendData", "complexity": 3, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "determineSeverity", "complexity": 3, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "calculateRuleImpact", "complexity": 3, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "recordFunctionAnalysis", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "recordFileAnalysis", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "updateRuleStats", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "updateCacheStats", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "checkRuleHotspot", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "checkFunctionHotspot", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "checkFileHotspot", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "addHotspot", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "calculateMemoryGrowthRate", "complexity": 2, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "recordRuleStart", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "recordRuleEnd", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "recordCacheAccess", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "updateEngineMetrics", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "checkMemoryUsage", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "analyzeMemoryBottlenecks", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "analyzeCacheBottlenecks", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "analyzeConcurrencyBottlenecks", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "emitEvent", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "captureMemorySnapshot", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "calculateLeakSuspicion", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "getImplementationGuide", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "getBottleneckImplementationGuide", "complexity": 1, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "generateReport", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "getRealtimeSnapshot", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "reset", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "createEmptyMetrics", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "performRealtimeAnalysis", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "analyzeCurrentPerformance", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "identifyBottlenecks", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "analyzeCPUBottlenecks", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "generateOverview", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "generateRecommendations", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "getCurrentMemoryStatus", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "getRuleOptimizationSuggestion", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "getFunctionOptimizationSuggestion", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "getFileOptimizationSuggestion", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "getMemoryOptimizationSuggestion", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "calculateAnalysisFrequency", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "determineAnalysisScope", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "analyzeRuleExecutionPatterns", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "analyzeCacheEfficiency", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "detectAnomalousPatterns", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "consolidateHotspots", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "updateEfficiencyMetrics", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}, {"name": "performTrendAnalysis", "complexity": 0, "line": 1061, "column": 0, "filePath": "src/engine/performance-monitor.ts"}], "averageComplexity": 1.150943396226415}, {"filePath": "src/utils/global-error-recovery-manager.ts", "complexity": 57, "functions": [{"name": "attemptRecovery", "complexity": 15, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts", "severity": "Warning"}, {"name": "shouldLogError", "complexity": 12, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts", "severity": "Warning"}, {"name": "outputRecoveryReport", "complexity": 11, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "handleError", "complexity": 6, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "updateRecoveryMetrics", "complexity": 5, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "applyFallbackStrategy", "complexity": 3, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "updateMetrics", "complexity": 2, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "getInstance", "complexity": 1, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "handleErrors", "complexity": 1, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "getMetrics", "complexity": 1, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "getGlobalErrorRecoveryManager", "complexity": 0, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "handleGlobalError", "complexity": 0, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "resetMetrics", "complexity": 0, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "updateConfig", "complexity": 0, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "getErrorHistory", "complexity": 0, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "initializeMetrics", "complexity": 0, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "getGlobalErrorRecoveryManager", "complexity": 0, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}, {"name": "handleGlobalError", "complexity": 0, "line": 424, "column": 0, "filePath": "src/utils/global-error-recovery-manager.ts"}], "averageComplexity": 3.1666666666666665}, {"filePath": "src/engine/performance-benchmark.ts", "complexity": 55, "functions": [{"name": "runBenchmark", "complexity": 15, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts", "severity": "Warning"}, {"name": "compareWithBaseline", "complexity": 14, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts", "severity": "Warning"}, {"name": "compareResults", "complexity": 6, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "getNestedValue", "complexity": 6, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "end", "complexity": 4, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "runRegressionTest", "complexity": 3, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "loadBaseline", "complexity": 2, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "runConcurrencyBenchmark", "complexity": 1, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "runLoadBenchmark", "complexity": 1, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "runBatchRegressionTest", "complexity": 1, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "saveBaseline", "complexity": 1, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "testEnginePerformance", "complexity": 1, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "start", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "addCustomMetric", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "incrementCounter", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "createBenchmarkSuite", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "createRegressionTester", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "createIntegratedTest", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "runMemoryBenchmark", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "getAllResults", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "generateReport", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "calculateStatistics", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "calculateStandardDeviation", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "createTimeout", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "saveResults", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "sleep", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "updateBaseline", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "runFullTest", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "generateComprehensiveReport", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "createBenchmarkSuite", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "createRegressionTester", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}, {"name": "createIntegratedTest", "complexity": 0, "line": 768, "column": 0, "filePath": "src/engine/performance-benchmark.ts"}], "averageComplexity": 1.71875}, {"filePath": "src/plugins/error-recovery.ts", "complexity": 55, "functions": [{"name": "applyDegradation", "complexity": 13, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts", "severity": "Warning"}, {"name": "matchPattern", "complexity": 7, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "execute", "complexity": 4, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "handlePluginError", "complexity": 4, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "onSuccess", "complexity": 3, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "performHealthCheck", "complexity": 3, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "matchError", "complexity": 3, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "registerHealthCheck", "complexity": 2, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "recoverPlugin", "complexity": 2, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "selectRecoveryStrategy", "complexity": 2, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "executeRecoveryStrategy", "complexity": 2, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "resetPlugin", "complexity": 2, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "onFailure", "complexity": 1, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "unregisterHealthCheck", "complexity": 1, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "cleanup", "complexity": 1, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "registerRecoveryPolicy", "complexity": 1, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "setDegradationLevel", "complexity": 1, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "executeWithCircuitBreaker", "complexity": 1, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "initializeCircuitBreakers", "complexity": 1, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "fallback<PERSON><PERSON><PERSON>", "complexity": 1, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "getState", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "reset", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "getHealthStatus", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "getAllHealthStatus", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "createErrorRecoveryManager", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "registerFallbackConfig", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "getPluginHealth", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "getAllPluginHealth", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "getCircuitBreakerState", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "cleanup", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "setupEventHandlers", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "restartPlugin", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "reloadPlugin", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "disablePlugin", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "isolatePlugin", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "performDegradation", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "createFailureResult", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}, {"name": "createErrorRecoveryManager", "complexity": 0, "line": 725, "column": 0, "filePath": "src/plugins/error-recovery.ts"}], "averageComplexity": 1.4473684210526316}, {"filePath": "src/plugins/hot-reload.ts", "complexity": 54, "functions": [{"name": "restorePluginState", "complexity": 8, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "reloadPlugin", "complexity": 7, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "notifyDependents", "complexity": 6, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "start", "complexity": 5, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "stop", "complexity": 5, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "checkDependencyChanges", "complexity": 5, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "watchPlugin", "complexity": 4, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "preservePluginState", "complexity": 4, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "unwatchPlugin", "complexity": 3, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "batchReload", "complexity": 2, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "scheduleReload", "complexity": 2, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "processBatchReload", "complexity": 2, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "startFileWatching", "complexity": 1, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "createHotReloadManager", "complexity": 0, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "getStatus", "complexity": 0, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "getReloadStatistics", "complexity": 0, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "cleanup", "complexity": 0, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "startDependencyWatching", "complexity": 0, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "updateAverageReloadTime", "complexity": 0, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}, {"name": "createHotReloadManager", "complexity": 0, "line": 476, "column": 0, "filePath": "src/plugins/hot-reload.ts"}], "averageComplexity": 2.7}, {"filePath": "src/plugins/communication.ts", "complexity": 51, "functions": [{"name": "deliverMessage", "complexity": 11, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "validateMessage", "complexity": 9, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "broadcastMessage", "complexity": 5, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "processQueue", "complexity": 5, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "sendMessage", "complexity": 3, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "unsubscribe", "complexity": 3, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "updateStatistics", "complexity": 3, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "isAllowed", "complexity": 2, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "checkSendPermission", "complexity": 2, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "cleanup", "complexity": 2, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "reset", "complexity": 1, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "subscribe", "complexity": 1, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "getPermissions", "complexity": 1, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "checkReceivePermission", "complexity": 1, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "handleDeliveryError", "complexity": 1, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "updateLatencyStatistics", "complexity": 1, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "createDefaultCommunicationPermissions", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "createPermissiveCommunicationPermissions", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "request", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "setPermissions", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "getStatistics", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "startQueueProcessor", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "startStatisticsCollector", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "createDefaultCommunicationPermissions", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "createPermissiveCommunicationPermissions", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "sendMessage", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "broadcastMessage", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "subscribe", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "unsubscribe", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "request", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "on", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "off", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "emit", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "getStatistics", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "setPermissions", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}, {"name": "getPermissions", "complexity": 0, "line": 525, "column": 0, "filePath": "src/plugins/communication.ts"}], "averageComplexity": 1.4166666666666667}, {"filePath": "src/__test__/helpers/interactive-test-helper.ts", "complexity": 49, "functions": [{"name": "simulateUserFlow", "complexity": 18, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts", "severity": "Warning"}, {"name": "interactiveTest", "complexity": 5, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "validateResult", "complexity": 5, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "interactiveTest", "complexity": 5, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "selectFromMenu", "complexity": 4, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "interruptProgram", "complexity": 3, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "waitForPrompt", "complexity": 2, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "sendKeySequence", "complexity": 2, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "_sendInput", "complexity": 2, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "confirmDialog", "complexity": 1, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "inputPassword", "complexity": 1, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "inputMultilineText", "complexity": 1, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "inputText", "complexity": 0, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "debugOutput", "complexity": 0, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}, {"name": "_delay", "complexity": 0, "line": 576, "column": 0, "filePath": "src/__test__/helpers/interactive-test-helper.ts"}], "averageComplexity": 3.2666666666666666}, {"filePath": "src/plugins/sandbox.ts", "complexity": 48, "functions": [{"name": "createSecureRequire", "complexity": 6, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "createSecureFileSystem", "complexity": 5, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "canRequireModule", "complexity": 4, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "canAccess", "complexity": 4, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "matchesPattern", "complexity": 3, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "cleanup", "complexity": 3, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "executeCode", "complexity": 3, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "createVMContext", "complexity": 3, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "isBuiltinModule", "complexity": 2, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "destroy", "complexity": 2, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "doExecute", "complexity": 2, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "createSandboxContext", "complexity": 2, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "isWithinLimits", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "getExecutionTime", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "canAccessFile", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "allocateMemory", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "releaseMemory", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "isWithinLimits", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "execute", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "executeFunction", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "canAccessNetwork", "complexity": 1, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "startExecution", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "endExecution", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "getMemoryUsage", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "reset", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "updateMemoryUsage", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "createSecureProcess", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "isSafeModule", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "getMemoryUsage", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "setTimeout", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "getTimeout", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}, {"name": "requestPermission", "complexity": 0, "line": 574, "column": 0, "filePath": "src/plugins/sandbox.ts"}], "averageComplexity": 1.5}, {"filePath": "src/cli/debug-commands.ts", "complexity": 47, "functions": [{"name": "generateDebugReports", "complexity": 14, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts", "severity": "Warning"}, {"name": "executeWithDebug", "complexity": 10, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "performAnalysisWithDebug", "complexity": 7, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "setupBreakpoints", "complexity": 4, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "showCurrentState", "complexity": 3, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "simulateFileAnalysis", "complexity": 3, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "showPerformanceMetrics", "complexity": 3, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "initializeDebugSystem", "complexity": 1, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "setupEventListeners", "complexity": 1, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "handleStepByStepDebugging", "complexity": 1, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}, {"name": "registerDefaultRules", "complexity": 0, "line": 543, "column": 0, "filePath": "src/cli/debug-commands.ts"}], "averageComplexity": 4.2727272727272725}, {"filePath": "src/baseline/manager.ts", "complexity": 46, "functions": [{"name": "updateBaseline", "complexity": 23, "line": 269, "column": 0, "filePath": "src/baseline/manager.ts", "severity": "Warning"}, {"name": "validateBaseline", "complexity": 11, "line": 269, "column": 0, "filePath": "src/baseline/manager.ts"}, {"name": "createBaseline", "complexity": 8, "line": 269, "column": 0, "filePath": "src/baseline/manager.ts"}, {"name": "loadBaseline", "complexity": 2, "line": 269, "column": 0, "filePath": "src/baseline/manager.ts"}, {"name": "compareWithBaseline", "complexity": 2, "line": 269, "column": 0, "filePath": "src/baseline/manager.ts"}, {"name": "hasBaseline", "complexity": 0, "line": 269, "column": 0, "filePath": "src/baseline/manager.ts"}, {"name": "getBaselinePath", "complexity": 0, "line": 269, "column": 0, "filePath": "src/baseline/manager.ts"}, {"name": "saveBaseline", "complexity": 0, "line": 269, "column": 0, "filePath": "src/baseline/manager.ts"}], "averageComplexity": 5.75}, {"filePath": "src/config/factory.ts", "complexity": 46, "functions": [{"name": "calculateDetectionScore", "complexity": 35, "line": 598, "column": 0, "filePath": "src/config/factory.ts", "severity": "Critical"}, {"name": "detectProjectType", "complexity": 5, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "generateConfig", "complexity": 3, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "getPresetConfig", "complexity": 1, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "findMatchingFiles", "complexity": 1, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "getPresetConfig", "complexity": 1, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "getAvailablePresets", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "detectProjectType", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "generateForProjectType", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "generateHighPerformanceConfig", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "generateDebugConfig", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "createPerformanceConfig", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "createVerboseConfig", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "getAvailablePresets", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}, {"name": "detectProjectType", "complexity": 0, "line": 598, "column": 0, "filePath": "src/config/factory.ts"}], "averageComplexity": 3.066666666666667}, {"filePath": "src/utils/position-converter.ts", "complexity": 46, "functions": [{"name": "buildLineMap", "complexity": 15, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts", "severity": "Warning"}, {"name": "binarySearchLineIndex", "complexity": 12, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts", "severity": "Warning"}, {"name": "lineColumnToOffset", "complexity": 5, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "extractSpanText", "complexity": 3, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "get<PERSON>ineC<PERSON>nt", "complexity": 2, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "manageCacheSize", "complexity": 2, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "getCacheStats", "complexity": 2, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "spanToPosition", "complexity": 1, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "isValidPosition", "complexity": 1, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "getCachedLineMap", "complexity": 1, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "generate<PERSON>ache<PERSON>ey", "complexity": 1, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "performCacheCleanup", "complexity": 1, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}, {"name": "clearCache", "complexity": 0, "line": 329, "column": 0, "filePath": "src/utils/position-converter.ts"}], "averageComplexity": 3.5384615384615383}, {"filePath": "src/engine/performance-kernel.ts", "complexity": 45, "functions": [{"name": "compress", "complexity": 5, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "calculateAdaptiveTTL", "complexity": 5, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "getCompressionStats", "complexity": 3, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "set", "complexity": 3, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "get", "complexity": 3, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "getStats", "complexity": 3, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "cleanup", "complexity": 3, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "evictColdEntries", "complexity": 3, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "extractFunctions", "complexity": 3, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "has", "complexity": 2, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "processLargeFile", "complexity": 2, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "initializeCompression", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "decompress", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "shouldCompress", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "<PERSON><PERSON>ey", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "destroy", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "isExpired", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "recordAccess", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "createChunks", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "performMemoryCleanup", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "release", "complexity": 1, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "delete", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "clear", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "startCleanupTimer", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "calculateInitialHeatScore", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "updateHeatScore", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "shouldProcessAsLargeFile", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "estimateASTSize", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "isFunctionNode", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "getCurrentMemoryUsage", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "calculateCacheUtilization", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "acquire", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "createPerformanceKernel", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "processLargeFile", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "cacheAnalysis", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "getCachedAnalysis", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "hasCachedAnalysis", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "compressData", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "decompressData", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "getMetrics", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "cleanup", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "evictCache", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "destroy", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "initializeMetrics", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "updateCompressionMetrics", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "updateCacheMetrics", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}, {"name": "createPerformanceKernel", "complexity": 0, "line": 906, "column": 0, "filePath": "src/engine/performance-kernel.ts"}], "averageComplexity": 0.9574468085106383}, {"filePath": "src/utils/file-cache.ts", "complexity": 45, "functions": [{"name": "getFileContent", "complexity": 8, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "ensureCapacity", "complexity": 7, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "getCacheDetails", "complexity": 6, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "getCacheStats", "complexity": 5, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "optimizeCache", "complexity": 4, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "removeExpiredEntries", "complexity": 3, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "getGlobalFileCache", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "preloadFiles", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "removeFromCache", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "destroy", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "isExpired", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "removeFromAccessOrder", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "evictLRU", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "performCleanup", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "chunkArray", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "getGlobalFileCache", "complexity": 1, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "createFileCache", "complexity": 0, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "preloadFile", "complexity": 0, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "clearCache", "complexity": 0, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "updateAccessOrder", "complexity": 0, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "generateContentHash", "complexity": 0, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "getTotalRequests", "complexity": 0, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}, {"name": "createFileCache", "complexity": 0, "line": 440, "column": 0, "filePath": "src/utils/file-cache.ts"}], "averageComplexity": 1.8}, {"filePath": "src/__test__/helpers/debug-enhancer.ts", "complexity": 44, "functions": [{"name": "diagnoseFailure", "complexity": 24, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts", "severity": "Warning"}, {"name": "generateDebugReport", "complexity": 9, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "createFailureReport", "complexity": 4, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "executeWithDebug", "complexity": 3, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "addSnapshot", "complexity": 1, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "addDiagnostic", "complexity": 1, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "completeSession", "complexity": 1, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "diagnoseTestFailure", "complexity": 1, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "createSession", "complexity": 0, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "getSession", "complexity": 0, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "getAllSessions", "complexity": 0, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "clearSessions", "complexity": 0, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "matchesPatterns", "complexity": 0, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "setEnabled", "complexity": 0, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "getSessionsSummary", "complexity": 0, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}, {"name": "clearDebugData", "complexity": 0, "line": 510, "column": 0, "filePath": "src/__test__/helpers/debug-enhancer.ts"}], "averageComplexity": 2.75}, {"filePath": "src/utils/compatibility-service.ts", "complexity": 42, "functions": [{"name": "validateDetailsCommandCompatibility", "complexity": 10, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "testColorSupport", "complexity": 6, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "applyCompatibilitySettings", "complexity": 6, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "generateCompatibleCodeFrame", "complexity": 4, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "getCompatibilityReport", "complexity": 4, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "testCodeFrameBasicFeatures", "complexity": 3, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "generateDegradedFrame", "complexity": 3, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "checkBabelCodeFrameCompatibility", "complexity": 2, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "testError<PERSON><PERSON><PERSON>y", "complexity": 2, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "getCompatibilityService", "complexity": 1, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "getCompatibilityService", "complexity": 1, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "getDegradeOptions", "complexity": 0, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}, {"name": "reset", "complexity": 0, "line": 352, "column": 0, "filePath": "src/utils/compatibility-service.ts"}], "averageComplexity": 3.230769230769231}, {"filePath": "src/__test__/helpers/resource-manager.ts", "complexity": 41, "functions": [{"name": "handleResourceContention", "complexity": 8, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getResourceTrend", "complexity": 6, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "checkResourceConflict", "complexity": 5, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "optimizeResources", "complexity": 4, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "cleanupExpiredAllocations", "complexity": 3, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "cleanupNamespace", "complexity": 3, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "requestAllocation", "complexity": 2, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "initialize", "complexity": 2, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "startMonitoring", "complexity": 1, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "stopMonitoring", "complexity": 1, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getProcessCount", "complexity": 1, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "createNamespace", "complexity": 1, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "addResourceToNamespace", "complexity": 1, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getIsolationStatus", "complexity": 1, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getInstance", "complexity": 1, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "requestResources", "complexity": 1, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getCurrentUsage", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getCPUUsage", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getOpenFileCount", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getResourceHistory", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "clearHistory", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "releaseAllocation", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getAllocationStatus", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "cleanup", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "releaseResources", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getResourceTrend", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}, {"name": "getStatus", "complexity": 0, "line": 565, "column": 0, "filePath": "src/__test__/helpers/resource-manager.ts"}], "averageComplexity": 1.5185185185185186}, {"filePath": "src/utils/error-output-formatter.ts", "complexity": 39, "functions": [{"name": "outputErrorStats", "complexity": 7, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "formatErrorDetails", "complexity": 5, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "formatRecoveryInfo", "complexity": 4, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "formatComplexityError", "complexity": 4, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "renderColorizedError", "complexity": 4, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "outputError", "complexity": 3, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "formatError", "complexity": 2, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "outputErrors", "complexity": 2, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>r", "complexity": 2, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "getErrorOutputFormatter", "complexity": 1, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "getErrorLevel", "complexity": 1, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "getErrorTypeDisplayName", "complexity": 1, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "renderFormattedError", "complexity": 1, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "formatStackTrace", "complexity": 1, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}, {"name": "getErrorOutputFormatter", "complexity": 1, "line": 360, "column": 0, "filePath": "src/utils/error-output-formatter.ts"}], "averageComplexity": 2.6}, {"filePath": "src/core/detail-collector.ts", "complexity": 38, "functions": [{"name": "validateStepData", "complexity": 9, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "addStep", "complexity": 6, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "attemptRecovery", "complexity": 3, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "outputErrorSummary", "complexity": 3, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "handleUnknownRule", "complexity": 2, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getFunctionAtLevel", "complexity": 2, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "detectNestingAnomalies", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "recoverFromError", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "endFunction", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "reset", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getCurrentFunctionName", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getCurrentComplexity", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getCurrentNestingLevel", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getRuleDescription", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "registerRules", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "releaseSteps", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "setMemoryOptimization", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "validateStepInternal", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getErrorStats", "complexity": 1, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "startFunction", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "addStepWithDiagnostic", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "addWarningStep", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "addErrorStep", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "isTracking", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getFunctionStack", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "validateStep", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "registerRule", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "hasRule", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getRegisteredRuleIds", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "copySteps", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getPerformanceStats", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "handleError", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getErrors", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "getWarnings", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}, {"name": "clearErrorsAndWarnings", "complexity": 0, "line": 584, "column": 0, "filePath": "src/core/detail-collector.ts"}], "averageComplexity": 1.0857142857142856}, {"filePath": "src/engine/streaming-processor.ts", "complexity": 36, "functions": [{"name": "processMultipleFiles", "complexity": 10, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "processFileStream", "complexity": 6, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "extractFunctions", "complexity": 6, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "release", "complexity": 3, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "checkMemoryUsage", "complexity": 2, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "destroy", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "acquire", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "addNode", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "flushBatch", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "processFunctionBatch", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "triggerGC", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "updateProcessingRate", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "processInChunks", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "getMemoryUsage", "complexity": 1, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "getCurrentUsage", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "getPeakUsage", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "resetPeak", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "clear", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "size", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "currentBatchSize", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "createStreamingProcessor", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "createTransformStream", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "getProcessingState", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "pause", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "resume", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "forceGC", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "destroy", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "setupEventHandlers", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "initializeObjectPools", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "initializeState", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "isFunctionNode", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "parseFileStreaming", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "shouldTriggerGC", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "waitForBackpressureRelief", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "sleep", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "createStreamingProcessor", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "estimateASTSize", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}, {"name": "shouldUseStreaming", "complexity": 0, "line": 686, "column": 0, "filePath": "src/engine/streaming-processor.ts"}], "averageComplexity": 0.9473684210526315}, {"filePath": "src/utils/error-recovery-service.ts", "complexity": 36, "functions": [{"name": "readFileWithRecovery", "complexity": 17, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts", "severity": "Warning"}, {"name": "validateFileAccess", "complexity": 7, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "generateCodeFrameWithRecovery", "complexity": 4, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "logError", "complexity": 3, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "attemptFallbackReading", "complexity": 2, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "getErrorRecoveryService", "complexity": 1, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "recordFailure", "complexity": 1, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "getErrorRecoveryService", "complexity": 1, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "clearCache", "complexity": 0, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "getErrorStats", "complexity": 0, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "generateFallbackFrame", "complexity": 0, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "delay", "complexity": 0, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "createSuccessResult", "complexity": 0, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}, {"name": "createFailureResult", "complexity": 0, "line": 421, "column": 0, "filePath": "src/utils/error-recovery-service.ts"}], "averageComplexity": 2.5714285714285716}, {"filePath": "src/rules/logical-operator-rule.ts", "complexity": 35, "functions": [{"name": "getCalleeIdentifier", "complexity": 8, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "collectLogicalOperators", "complexity": 6, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "isDefaultValueAssignment", "complexity": 5, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "isPropertyAccessPattern", "complexity": 5, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "canHandle", "complexity": 2, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "detectLogicalOperatorMixing", "complexity": 2, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "generateLogicalSuggestions", "complexity": 2, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "isRecursiveCall", "complexity": 2, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "isLogicalBinaryExpression", "complexity": 1, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "isInAssignmentContext", "complexity": 1, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "generateLogicalReason", "complexity": 1, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "evaluate", "complexity": 0, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "canHandle", "complexity": 0, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}, {"name": "evaluate", "complexity": 0, "line": 281, "column": 0, "filePath": "src/rules/logical-operator-rule.ts"}], "averageComplexity": 2.5}, {"filePath": "src/rules/rule-sets.ts", "complexity": 35, "functions": [{"name": "validateRuleSet", "complexity": 19, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts", "severity": "Warning"}, {"name": "getAllRules", "complexity": 3, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "registerAllToEngine", "complexity": 3, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "registerSetToEngine", "complexity": 3, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "getStatistics", "complexity": 3, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "listRuleSets", "complexity": 3, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "getRulesBySet", "complexity": 1, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "getRules", "complexity": 0, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "getDescription", "complexity": 0, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "getRules", "complexity": 0, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "getDescription", "complexity": 0, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "registerRuleSet", "complexity": 0, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "clearAll", "complexity": 0, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}, {"name": "resetToDefaults", "complexity": 0, "line": 272, "column": 0, "filePath": "src/rules/rule-sets.ts"}], "averageComplexity": 2.5}, {"filePath": "src/core/rule-registry.ts", "complexity": 32, "functions": [{"name": "validateConfig", "complexity": 13, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts", "severity": "Warning"}, {"name": "registerBatch", "complexity": 7, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "register", "complexity": 6, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "registerRule", "complexity": 2, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "getDescription", "complexity": 1, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "getRule", "complexity": 1, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "updateRule", "complexity": 1, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "validateRuleId", "complexity": 1, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "getAllRules", "complexity": 0, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "getRulesByCategory", "complexity": 0, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "hasRule", "complexity": 0, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "getAllRuleIds", "complexity": 0, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "unregister", "complexity": 0, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "clear", "complexity": 0, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "getStatistics", "complexity": 0, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}, {"name": "exportConfig", "complexity": 0, "line": 288, "column": 0, "filePath": "src/core/rule-registry.ts"}], "averageComplexity": 2}, {"filePath": "src/rules/jsx/structural-exemption.ts", "complexity": 32, "functions": [{"name": "checkExpressionComplexity", "complexity": 6, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "isSimpleConditionalRendering", "complexity": 6, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "hasNestedConditionals", "complexity": 4, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "isComplexExpression", "complexity": 3, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "hasNestedLogicalOperators", "complexity": 3, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "hasComplexLogic", "complexity": 2, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "checkAttributeComplexity", "complexity": 2, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "isSimpleCondition", "complexity": 2, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "isJSXResult", "complexity": 2, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "analyzeExemptionReason", "complexity": 1, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "isNullishCoalescing", "complexity": 1, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "canHandle", "complexity": 0, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "evaluate", "complexity": 0, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}, {"name": "isSimpleValueExpression", "complexity": 0, "line": 387, "column": 0, "filePath": "src/rules/jsx/structural-exemption.ts"}], "averageComplexity": 2.2857142857142856}, {"filePath": "src/config/index.ts", "complexity": 30, "functions": [{"name": "getConfigSummary", "complexity": 6, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "isLegacyConfig", "complexity": 6, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "getConfigSummary", "complexity": 6, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "isLegacyConfig", "complexity": 6, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "loadOrGenerateConfig", "complexity": 3, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "loadOrGenerateConfig", "complexity": 3, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "generateSmartConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "createProjectConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "createHighPerformanceConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "createDebugConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "validateConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "migrateFromLegacyConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "generateSmartConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "createProjectConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "createHighPerformanceConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "createDebugConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "validateConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}, {"name": "migrateFromLegacyConfig", "complexity": 0, "line": 320, "column": 0, "filePath": "src/config/index.ts"}], "averageComplexity": 1.6666666666666667}, {"filePath": "src/config/validator.ts", "complexity": 30, "functions": [{"name": "testLegacyMigration", "complexity": 6, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "testConfigMerging", "complexity": 5, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "testConfigFactory", "complexity": 4, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "testBasicValidation", "complexity": 3, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "testPerformance", "complexity": 3, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "testProjectTypeDetection", "complexity": 2, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "testHotReload", "complexity": 2, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "runTest", "complexity": 2, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "validateConfigFile", "complexity": 1, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "printResults", "complexity": 1, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "validateConfigFile", "complexity": 1, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "runConfigValidationTests", "complexity": 0, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "runAllTests", "complexity": 0, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}, {"name": "runConfigValidationTests", "complexity": 0, "line": 615, "column": 0, "filePath": "src/config/validator.ts"}], "averageComplexity": 2.142857142857143}, {"filePath": "src/cache/monitor.ts", "complexity": 28, "functions": [{"name": "determineTrend", "complexity": 6, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "generateImprovementSuggestions", "complexity": 5, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "checkPerformanceAlerts", "complexity": 3, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "generateTrendRecommendations", "complexity": 3, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "checkCurrentAlerts", "complexity": 3, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "getPerformanceTrends", "complexity": 2, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "removeListener", "complexity": 1, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "destroy", "complexity": 1, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "getLayerStats", "complexity": 1, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "updateAverageTime", "complexity": 1, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "calculateLayerEfficiency", "complexity": 1, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "calculateStabilityScore", "complexity": 1, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "recordHit", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "recordMiss", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "recordSet", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "recordEviction", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "getStatistics", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "reset", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "addListener", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "getEfficiencyReport", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "startRealTimeMonitoring", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "createEmptyStatistics", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "updateOverallHitRate", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "emitEvent", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "startPerformanceMonitoring", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "calculateAverages", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "calculateOverallEfficiency", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "calculateMemoryUtilization", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "calculatePerformanceScore", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}, {"name": "calculateVariance", "complexity": 0, "line": 504, "column": 0, "filePath": "src/cache/monitor.ts"}], "averageComplexity": 0.9333333333333333}, {"filePath": "src/cli/ui-helper.ts", "complexity": 28, "functions": [{"name": "formatComplexity", "complexity": 6, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showErrorStats", "complexity": 6, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showPerformanceTips", "complexity": 4, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showSummary", "complexity": 2, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "errorWithSuggestion", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "updateSpinner", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "failSpinner", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "stopSpinner", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "updateProgress", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "stopProgress", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "formatSeverity", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showQualityGate", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showContextualHelp", "complexity": 1, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "success", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "error", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "warning", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "info", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "section", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "startSpinner", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "createProgressBar", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showTip", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "separator", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showFileProgress", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "clear", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showWelcome", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}, {"name": "showConfigWizard", "complexity": 0, "line": 343, "column": 0, "filePath": "src/cli/ui-helper.ts"}], "averageComplexity": 1.037037037037037}, {"filePath": "src/plugins/dev-tools.ts", "complexity": 28, "functions": [{"name": "benchmark", "complexity": 6, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "runTestInternal", "complexity": 4, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generatePlugin", "complexity": 2, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "validatePluginStructure", "complexity": 2, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generatePluginCode", "complexity": 2, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getBasicInfo", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getMetadata", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "analyzeRules", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "runAll", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "runTest", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "start", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "stop", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "profile", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateDocs", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generatePackageJson", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateReadme", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generatePluginReadme", "complexity": 1, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "setBreakpoint", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "removeBreakpoint", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "listBreakpoints", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "step", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "continue", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "pause", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getVariables", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "evaluate", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getCallStack", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "enableLogging", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getLogs", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "clearLogs", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "on", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "off", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "validateRules", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "analyzeDependencies", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getPerformanceMetrics", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getMemoryUsage", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "analyzeConfiguration", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "addTest", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "removeTest", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "listTests", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateCoverage", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "restart", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getStatus", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "enableHotReload", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "disableHotReload", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "addMiddleware", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "removeMiddleware", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "watchFiles", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "unwatchFiles", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "createPluginDevTools", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateRule", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "debug", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "inspect", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "createTestSuite", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "runTests", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "startDevServer", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "lintPlugin", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "formatPlugin", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateRuleCode", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateTestCode", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "templateToRule", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateRuleTests", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateRuleDocs", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateApiDocs", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateRulesDocs", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "createPluginDevTools", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generatePlugin", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateRule", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "debug", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "inspect", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "createTestSuite", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "runTests", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "profile", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "benchmark", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateDocs", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "startDevServer", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "validatePluginStructure", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "lintPlugin", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "formatPlugin", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "setBreakpoint", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "removeBreakpoint", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "listBreakpoints", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "step", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "continue", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "pause", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getVariables", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "evaluate", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getCallStack", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "enableLogging", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getLogs", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "clearLogs", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "on", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "off", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getBasicInfo", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getMetadata", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "analyzeRules", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "validateRules", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "analyzeDependencies", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getPerformanceMetrics", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getMemoryUsage", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "analyzeConfiguration", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "addTest", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "removeTest", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "listTests", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "runAll", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "runTest", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "generateCoverage", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "start", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "stop", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "restart", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "getStatus", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "enableHotReload", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "disableHotReload", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "addMiddleware", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "removeMiddleware", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "watchFiles", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "unwatchFiles", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "on", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}, {"name": "off", "complexity": 0, "line": 1579, "column": 0, "filePath": "src/plugins/dev-tools.ts"}], "averageComplexity": 0.23728813559322035}, {"filePath": "src/__test__/helpers/concurrent-test-manager.ts", "complexity": 27, "functions": [{"name": "executeTask", "complexity": 9, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "executeTasks", "complexity": 5, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "waitForTaskSlot", "complexity": 4, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "executeBatch", "complexity": 3, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "calculateMetrics", "complexity": 2, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "isWithinLimits", "complexity": 1, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "createBatches", "complexity": 1, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "canStartNewTask", "complexity": 1, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "waitForAllTasks", "complexity": 1, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "start", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "addTask", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "removeTask", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "updateMemoryPeak", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "getCurrentConcurrency", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "getMemoryPeakMB", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "getDuration", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "logPerformanceReport", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "delay", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "getRunningTasksStatus", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}, {"name": "forceStop", "complexity": 0, "line": 405, "column": 0, "filePath": "src/__test__/helpers/concurrent-test-manager.ts"}], "averageComplexity": 1.35}, {"filePath": "src/rules/base-rule.ts", "complexity": 26, "functions": [{"name": "isPermissionCheck", "complexity": 10, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "complexity": 4, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "isNestedConditional", "complexity": 4, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "getLogicalOperator", "complexity": 2, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "calculateNestingDepth", "complexity": 2, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "generateSuggestions", "complexity": 2, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "evaluateWithCache", "complexity": 1, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "debug", "complexity": 1, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "canHandle", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "evaluate", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "getDependencies", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "onLoad", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "onUnload", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "generate<PERSON>ache<PERSON>ey", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "createExemptionResult", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "createComplexityResult", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "createNonExemptionResult", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "getNodeHash", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "isJSXNode", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "isConditionalNode", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "isLoopNode", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "isLogicalNode", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "warn", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}, {"name": "error", "complexity": 0, "line": 267, "column": 0, "filePath": "src/rules/base-rule.ts"}], "averageComplexity": 1.0833333333333333}, {"filePath": "src/utils/code-frame-generator.ts", "complexity": 26, "functions": [{"name": "generateFramesBatch", "complexity": 8, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "generateFrame", "complexity": 7, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "generateFrameFromSpan", "complexity": 6, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "getCodeFrameGenerator", "complexity": 1, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "destroy", "complexity": 1, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "performMemoryCleanup", "complexity": 1, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "groupRequestsByFile", "complexity": 1, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "getCodeFrameGenerator", "complexity": 1, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "generateFallbackFrame", "complexity": 0, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "clearCache", "complexity": 0, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "getCacheStats", "complexity": 0, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "preloadFiles", "complexity": 0, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "optimizeCache", "complexity": 0, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "startMemoryCleanup", "complexity": 0, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}, {"name": "estimateMemoryUsage", "complexity": 0, "line": 408, "column": 0, "filePath": "src/utils/code-frame-generator.ts"}], "averageComplexity": 1.7333333333333334}, {"filePath": "src/utils/concurrent-validation-service.ts", "complexity": 26, "functions": [{"name": "validateSync", "complexity": 9, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "validateConcurrently", "complexity": 8, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "createSummary", "complexity": 3, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "acquire", "complexity": 1, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "release", "complexity": 1, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "getDefaultValidationService", "complexity": 1, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "validateSingleRule", "complexity": 1, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "executeRuleWithTimeout", "complexity": 1, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "getDefaultValidationService", "complexity": 1, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "createValidationService", "complexity": 0, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "addRule", "complexity": 0, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "addRules", "complexity": 0, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "getRules", "complexity": 0, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "clearRules", "complexity": 0, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "getStats", "complexity": 0, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}, {"name": "createValidationService", "complexity": 0, "line": 651, "column": 0, "filePath": "src/utils/concurrent-validation-service.ts"}], "averageComplexity": 1.625}, {"filePath": "src/formatters/smart-filter.ts", "complexity": 22, "functions": [{"name": "calculateStepImportance", "complexity": 8, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "getFilter<PERSON><PERSON>mary", "complexity": 6, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "filterDetailSteps", "complexity": 4, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "shouldShowContext", "complexity": 3, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "applySmartFilter", "complexity": 1, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "getDefaultFilterOptions", "complexity": 0, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "createSmartContextFilter", "complexity": 0, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "applySmartFilterOnFiltered", "complexity": 0, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "isHighImportanceRule", "complexity": 0, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "selectTopSteps", "complexity": 0, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "getDefaultFilterOptions", "complexity": 0, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}, {"name": "createSmartContextFilter", "complexity": 0, "line": 283, "column": 0, "filePath": "src/formatters/smart-filter.ts"}], "averageComplexity": 1.8333333333333333}, {"filePath": "src/__test__/helpers/fixture-manager.ts", "complexity": 21, "functions": [{"name": "materializeFixture", "complexity": 7, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "generateDynamicFixture", "complexity": 7, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "createPerformanceFixture", "complexity": 3, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "initialize", "complexity": 1, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "cleanupFixture", "complexity": 1, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "generateFixtureBatch", "complexity": 1, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "cleanup", "complexity": 1, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "registerFixture", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "getFixture", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "getFixtureNames", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "getFixturesByTag", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "getFixturesByCategory", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "getFixturesByDifficulty", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "registerDefaultFixtures", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "getStatistics", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "createCLITestFixture", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}, {"name": "createErrorScenarioFixture", "complexity": 0, "line": 690, "column": 0, "filePath": "src/__test__/helpers/fixture-manager.ts"}], "averageComplexity": 1.2352941176470589}, {"filePath": "src/engine/debug-visualizer.ts", "complexity": 20, "functions": [{"name": "generateRecommendedActions", "complexity": 5, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "getPerformanceMetrics", "complexity": 3, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateTextReport", "complexity": 2, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateJSONReport", "complexity": 2, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateFlowGraphSection", "complexity": 2, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateHTMLReport", "complexity": 1, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "processReportData", "complexity": 1, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "getCacheStatistics", "complexity": 1, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateDiagnosticsSection", "complexity": 1, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateTimelineSection", "complexity": 1, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generatePerformanceSection", "complexity": 1, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "getEventStatistics", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "getError<PERSON><PERSON><PERSON><PERSON>", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "getRuleStatistics", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "getDiagnosticsSummary", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateHeader", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateSummary", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateEventsSection", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateRawDataSection", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateCSS", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "generateJavaScript", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "prepareTimelineData", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "groupBy", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}, {"name": "getTopErrors", "complexity": 0, "line": 870, "column": 0, "filePath": "src/engine/debug-visualizer.ts"}], "averageComplexity": 0.8333333333333334}, {"filePath": "src/engine/async-engine.ts", "complexity": 16, "functions": [{"name": "analyzeFiles", "complexity": 7, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "analyzeNode", "complexity": 3, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "analyzeFile", "complexity": 2, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "analyzeFunction", "complexity": 1, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "updateConfig", "complexity": 1, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "analyzeCode", "complexity": 1, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "ensureInitialized", "complexity": 1, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "recordAnalysis", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "recordFile", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "recordFunction", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "recordRule", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "updateCacheHitRate", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "updateParallelEfficiency", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "getMetrics", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "reset", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "createEmptyMetrics", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "registerRule", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "unregisterRule", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "getMetrics", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "generatePerformanceReport", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "getPerformanceSnapshot", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "resetPerformanceMonitoring", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "clearCache", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "shutdown", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "getConfig", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "getRuleStatistics", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "getExecutionLoad", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "preWarmCache", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "createErrorFileAnalysis", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "initialize", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "mergeConfig", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "createExecutionOptions", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "generateNodeHash", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "getConfigHash", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "createAnalysisContext", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "extractFunctionNodes", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "isAnalyzableNode", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "createNodeAnalysis", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "createEmptyNodeAnalysis", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "createErrorNodeAnalysis", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "calculateNestingDepth", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}, {"name": "updateCacheStatistics", "complexity": 0, "line": 677, "column": 0, "filePath": "src/engine/async-engine.ts"}], "averageComplexity": 0.38095238095238093}, {"filePath": "src/cache/index.ts", "complexity": 15, "functions": [{"name": "validateCacheConfig", "complexity": 9, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "generatePerformanceReport", "complexity": 2, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "createCacheManager", "complexity": 1, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "getPresetConfig", "complexity": 1, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "estimateObjectSize", "complexity": 1, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "hash<PERSON><PERSON><PERSON><PERSON>", "complexity": 1, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "createCacheMonitor", "complexity": 0, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "createPresetCacheSystem", "complexity": 0, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "createDevelopmentCacheSystem", "complexity": 0, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "createProductionCacheSystem", "complexity": 0, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "createTestingCacheSystem", "complexity": 0, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}, {"name": "formatCacheStatistics", "complexity": 0, "line": 326, "column": 0, "filePath": "src/cache/index.ts"}], "averageComplexity": 1.25}, {"filePath": "src/rules/jsx-structural-exemption.ts", "complexity": 15, "functions": [{"name": "extractTagName", "complexity": 4, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "extractMemberExpressionName", "complexity": 3, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "countAttributes", "complexity": 3, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "hasJSXChildren", "complexity": 2, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "analyzeExemptionReason", "complexity": 1, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "createExemptionMetadata", "complexity": 1, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "shouldExemptJSXNode", "complexity": 1, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "canHandle", "complexity": 0, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "evaluate", "complexity": 0, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}, {"name": "isJSXStructuralNode", "complexity": 0, "line": 138, "column": 0, "filePath": "src/rules/jsx-structural-exemption.ts"}], "averageComplexity": 1.5}, {"filePath": "src/__test__/jsx-exemption-validation.ts", "complexity": 14, "functions": [{"name": "main", "complexity": 6, "line": 268, "column": 0, "filePath": "src/__test__/jsx-exemption-validation.ts"}, {"name": "testJSXStructuralExemption", "complexity": 4, "line": 268, "column": 0, "filePath": "src/__test__/jsx-exemption-validation.ts"}, {"name": "testSpecificJSXExemptions", "complexity": 3, "line": 268, "column": 0, "filePath": "src/__test__/jsx-exemption-validation.ts"}, {"name": "validateTask31Completion", "complexity": 1, "line": 268, "column": 0, "filePath": "src/__test__/jsx-exemption-validation.ts"}], "averageComplexity": 3.5}, {"filePath": "src/ui/server.ts", "complexity": 13, "functions": [{"name": "start", "complexity": 3, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "findAvailablePort", "complexity": 3, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "getStoredResult", "complexity": 2, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "cleanupResult", "complexity": 2, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "hasResults", "complexity": 1, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "storeResult", "complexity": 1, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "openBrowser", "complexity": 1, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "setupRoutes", "complexity": 0, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "getResultPath", "complexity": 0, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}, {"name": "stop", "complexity": 0, "line": 326, "column": 0, "filePath": "src/ui/server.ts"}], "averageComplexity": 1.3}, {"filePath": "src/utils/parallel-code-frame-generator.ts", "complexity": 12, "functions": [{"name": "generateFrames", "complexity": 5, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "generateFramesFromSpans", "complexity": 2, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "processConcurrently", "complexity": 2, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "getGlobalParallelGenerator", "complexity": 1, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "chunkArray", "complexity": 1, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "getGlobalParallelGenerator", "complexity": 1, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "createParallelGenerator", "complexity": 0, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "preloadFiles", "complexity": 0, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "getCacheStats", "complexity": 0, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "optimizeCache", "complexity": 0, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "cleanup", "complexity": 0, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "processBatch", "complexity": 0, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "createTimeoutPromise", "complexity": 0, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}, {"name": "createParallelGenerator", "complexity": 0, "line": 405, "column": 0, "filePath": "src/utils/parallel-code-frame-generator.ts"}], "averageComplexity": 0.8571428571428571}, {"filePath": "src/core/default-rules.ts", "complexity": 10, "functions": [{"name": "initializeRules", "complexity": 2, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "initializeRules", "complexity": 2, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getDefaultIncrement", "complexity": 1, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getNodeTypeRuleId", "complexity": 1, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getLogicalOperatorRuleId", "complexity": 1, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getDefaultIncrement", "complexity": 1, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getNodeTypeRuleId", "complexity": 1, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getLogicalOperatorRuleId", "complexity": 1, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getDefaultRulesConfig", "complexity": 0, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getRulesByCategory", "complexity": 0, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "isKnownRule", "complexity": 0, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getDefaultRulesConfig", "complexity": 0, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "getRulesByCategory", "complexity": 0, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}, {"name": "isKnownRule", "complexity": 0, "line": 308, "column": 0, "filePath": "src/core/default-rules.ts"}], "averageComplexity": 0.7142857142857143}, {"filePath": "src/engine/index.ts", "complexity": 10, "functions": [{"name": "validateEngine", "complexity": 4, "line": 143, "column": 0, "filePath": "src/engine/index.ts"}, {"name": "validateEngine", "complexity": 4, "line": 143, "column": 0, "filePath": "src/engine/index.ts"}, {"name": "createEngineWithDefaults", "complexity": 1, "line": 143, "column": 0, "filePath": "src/engine/index.ts"}, {"name": "createEngineWithDefaults", "complexity": 1, "line": 143, "column": 0, "filePath": "src/engine/index.ts"}, {"name": "createAsyncRuleEngine", "complexity": 0, "line": 143, "column": 0, "filePath": "src/engine/index.ts"}, {"name": "createCleanEngine", "complexity": 0, "line": 143, "column": 0, "filePath": "src/engine/index.ts"}, {"name": "createAsyncRuleEngine", "complexity": 0, "line": 143, "column": 0, "filePath": "src/engine/index.ts"}, {"name": "createCleanEngine", "complexity": 0, "line": 143, "column": 0, "filePath": "src/engine/index.ts"}], "averageComplexity": 1.25}, {"filePath": "src/core/object-pool.ts", "complexity": 9, "functions": [{"name": "acquire", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "release", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "getStats", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "warmUp", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "acquire", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "release", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "getStats", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "warmUp", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "getInstance", "complexity": 1, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "size", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "clear", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "createNewStep", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "resetStep", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "size", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "clear", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "createNewContext", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "resetContext", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "getDetailStepPool", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "getFunctionContextPool", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "getAllStats", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "clearAllPools", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "acquire", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "release", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "size", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}, {"name": "clear", "complexity": 0, "line": 278, "column": 0, "filePath": "src/core/object-pool.ts"}], "averageComplexity": 0.36}, {"filePath": "src/engine/demo.ts", "complexity": 8, "functions": [{"name": "demonstrateBasicUsage", "complexity": 2, "line": 365, "column": 0, "filePath": "src/engine/demo.ts"}, {"name": "demonstrateJSXAnalysis", "complexity": 1, "line": 365, "column": 0, "filePath": "src/engine/demo.ts"}, {"name": "demonstratePerformanceMonitoring", "complexity": 1, "line": 365, "column": 0, "filePath": "src/engine/demo.ts"}, {"name": "demonstrateParallelAnalysis", "complexity": 1, "line": 365, "column": 0, "filePath": "src/engine/demo.ts"}, {"name": "demonstrateEngineValidation", "complexity": 1, "line": 365, "column": 0, "filePath": "src/engine/demo.ts"}, {"name": "runAsyncEngineDemo", "complexity": 1, "line": 365, "column": 0, "filePath": "src/engine/demo.ts"}, {"name": "runAsyncEngineDemo", "complexity": 1, "line": 365, "column": 0, "filePath": "src/engine/demo.ts"}], "averageComplexity": 1.1428571428571428}, {"filePath": "src/rules/base/rule.ts", "complexity": 7, "functions": [{"name": "evaluateWithCache", "complexity": 3, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "createErrorResult", "complexity": 2, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "getNodeHash", "complexity": 1, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "debug", "complexity": 1, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "canHandle", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "evaluate", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "getDependencies", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "onLoad", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "onUnload", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "generate<PERSON>ache<PERSON>ey", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "createNonExemptionResult", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "createExemptionResult", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "createComplexityResult", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "isJSXNode", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "isJSXStructuralNode", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}, {"name": "isExpressionNode", "complexity": 0, "line": 223, "column": 0, "filePath": "src/rules/base/rule.ts"}], "averageComplexity": 0.4375}, {"filePath": "src/formatters/base.ts", "complexity": 4, "functions": [{"name": "getSeverityLevel", "complexity": 3, "line": 31, "column": 0, "filePath": "src/formatters/base.ts"}, {"name": "formatComplexity", "complexity": 1, "line": 31, "column": 0, "filePath": "src/formatters/base.ts"}, {"name": "format", "complexity": 0, "line": 31, "column": 0, "filePath": "src/formatters/base.ts"}, {"name": "writeToFile", "complexity": 0, "line": 31, "column": 0, "filePath": "src/formatters/base.ts"}], "averageComplexity": 1}, {"filePath": "src/formatters/html.ts", "complexity": 4, "functions": [{"name": "writeToFile", "complexity": 1, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "generateSummarySection", "complexity": 1, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "generateFunctionItem", "complexity": 1, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "getComplexityClass", "complexity": 1, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "format", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "enrichWithSeverity", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "generateHtmlReport", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "getStyles", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "generateFiltersSection", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "generateResultsSection", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "generateFileItem", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "generateChartsSection", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}, {"name": "getJavaScript", "complexity": 0, "line": 640, "column": 0, "filePath": "src/formatters/html.ts"}], "averageComplexity": 0.3076923076923077}, {"filePath": "src/engine/demo-registry.ts", "complexity": 2, "functions": [{"name": "demoRuleRegistrySystem", "complexity": 2, "line": 143, "column": 0, "filePath": "src/engine/demo-registry.ts"}], "averageComplexity": 2}, {"filePath": "src/index.ts", "complexity": 2, "functions": [{"name": "analyzeFile", "complexity": 1, "line": 52, "column": 0, "filePath": "src/index.ts"}, {"name": "analyzeFile", "complexity": 1, "line": 52, "column": 0, "filePath": "src/index.ts"}, {"name": "analyzeProject", "complexity": 0, "line": 52, "column": 0, "filePath": "src/index.ts"}, {"name": "analyzeProject", "complexity": 0, "line": 52, "column": 0, "filePath": "src/index.ts"}], "averageComplexity": 0.5}, {"filePath": "src/rules/core-complexity-rule.ts", "complexity": 2, "functions": [{"name": "getBaseComplexity", "complexity": 1, "line": 121, "column": 0, "filePath": "src/rules/core-complexity-rule.ts"}, {"name": "generateReason", "complexity": 1, "line": 121, "column": 0, "filePath": "src/rules/core-complexity-rule.ts"}, {"name": "canHandle", "complexity": 0, "line": 121, "column": 0, "filePath": "src/rules/core-complexity-rule.ts"}, {"name": "evaluate", "complexity": 0, "line": 121, "column": 0, "filePath": "src/rules/core-complexity-rule.ts"}, {"name": "calculateNodeComplexity", "complexity": 0, "line": 121, "column": 0, "filePath": "src/rules/core-complexity-rule.ts"}, {"name": "calculateNestingPenalty", "complexity": 0, "line": 121, "column": 0, "filePath": "src/rules/core-complexity-rule.ts"}, {"name": "shouldIncreaseNesting", "complexity": 0, "line": 121, "column": 0, "filePath": "src/rules/core-complexity-rule.ts"}], "averageComplexity": 0.2857142857142857}, {"filePath": "src/__test__/fixtures/cases/basic.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/__test__/fixtures/cases/edge-cases.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/__test__/fixtures/cases/logical.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/__test__/fixtures/cases/mixed-logical.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/__test__/fixtures/cases/nested.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/baseline/types.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/cache/types.ts", "complexity": 0, "functions": [{"name": "getCachedNodeResult", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "setCachedNodeResult", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "getCachedRuleResult", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "setCachedRuleResult", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "getCachedTypeInfo", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "peekCachedTypeInfo", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "setCachedTypeInfo", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "getCachedByPattern", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "setCachedByPattern", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "invalidateCache", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "clearCache", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "getHitRate", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "getSize", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "preWarmCache", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "isApplicable", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "generateNodeKey", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "generateRuleKey", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "generateTypeKey", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "generatePatternKey", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "generateContextHash", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "serialize", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "deserialize", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "getSize", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "save", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "load", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "delete", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "clear", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "exists", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "getAllKeys", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "get", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "set", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "has", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "delete", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "clear", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "onCacheEvent", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "recordHit", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "recordMiss", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "recordSet", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "recordEviction", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "getStatistics", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "reset", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "detectNodePattern", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "detectAccessPattern", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "predictCacheNeeds", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "predictEvictionCandidates", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}, {"name": "suggestCacheOptimizations", "complexity": 0, "line": 300, "column": 0, "filePath": "src/cache/types.ts"}], "averageComplexity": 0}, {"filePath": "src/cli/index.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/config/schema.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/config/types.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/core/errors.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/core/types.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/engine/jsx-integration.ts", "complexity": 0, "functions": [{"name": "createEngineWithJSXRules", "complexity": 0, "line": 65, "column": 0, "filePath": "src/engine/jsx-integration.ts"}, {"name": "addJSXRulesToEngine", "complexity": 0, "line": 65, "column": 0, "filePath": "src/engine/jsx-integration.ts"}, {"name": "getJSXRuleConfig", "complexity": 0, "line": 65, "column": 0, "filePath": "src/engine/jsx-integration.ts"}, {"name": "createEngineWithJSXRules", "complexity": 0, "line": 65, "column": 0, "filePath": "src/engine/jsx-integration.ts"}, {"name": "addJSXRulesToEngine", "complexity": 0, "line": 65, "column": 0, "filePath": "src/engine/jsx-integration.ts"}, {"name": "getJSXRuleConfig", "complexity": 0, "line": 65, "column": 0, "filePath": "src/engine/jsx-integration.ts"}], "averageComplexity": 0}, {"filePath": "src/engine/types.ts", "complexity": 0, "functions": [{"name": "evaluate", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "canHandle", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "getDependencies", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "onLoad", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "onUnload", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "analyzeNode", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "analyzeFunction", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "analyzeFile", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "analyzeFiles", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "analyzeCode", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "registerRule", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "unregisterRule", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "getMetrics", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "clearCache", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "updateConfig", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "getConfig", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "getRuleStatistics", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "getExecutionLoad", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "preWarmCache", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}, {"name": "shutdown", "complexity": 0, "line": 404, "column": 0, "filePath": "src/engine/types.ts"}], "averageComplexity": 0}, {"filePath": "src/plugins/index.ts", "complexity": 0, "functions": [{"name": "createPluginSystem", "complexity": 0, "line": 153, "column": 0, "filePath": "src/plugins/index.ts"}, {"name": "createPluginSystem", "complexity": 0, "line": 153, "column": 0, "filePath": "src/plugins/index.ts"}], "averageComplexity": 0}, {"filePath": "src/plugins/types.ts", "complexity": 0, "functions": [{"name": "onLoad", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "onUnload", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "onConfigChange", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getConfigDefaults", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "validateConfig", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getMetadata", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getDocumentation", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getExamples", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "canDisable", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getCompatibilityInfo", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "loadPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "unloadPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "reloadPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getAllPlugins", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getActivePlugins", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getInactivePlugins", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "enablePlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "disablePlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "resolveDependencies", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "validateDependencies", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "updatePluginConfig", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getPluginConfig", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getPluginStatistics", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getPluginHealth", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "watchPlugins", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "isWatching", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "validate<PERSON>lugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "createSandbox", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "on", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "off", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "emit", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "allocateMemory", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "releaseMemory", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getMemoryUsage", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "setTimeout", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getTimeout", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "isWithinLimits", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "canAccess", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "requestPermission", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "cleanup", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "destroy", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "searchPlugins", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getPluginInfo", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "validatePluginSource", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "downloadPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "search", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getPluginInfo", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "downloadPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "verifyPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "get", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "set", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "delete", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "clear", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "cachePlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getCachedPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "invalidate<PERSON><PERSON><PERSON>", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "cacheRuleResult", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getCachedRuleResult", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getHitRate", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getSize", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "optimize", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "start", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "stop", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getStatus", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "reloadPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "batchReload", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "watchPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "unwatchPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getReloadStatistics", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "on", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "off", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "emit", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "cleanup", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getState", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "setState", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "onHotReload", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "onStateRestore", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "generatePlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "generateRule", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "debug", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "inspect", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "createTestSuite", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "runTests", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "profile", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "benchmark", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "generateDocs", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "startDevServer", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "validatePluginStructure", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "lintPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "formatPlugin", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "setBreakpoint", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "removeBreakpoint", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "listBreakpoints", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "step", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "continue", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "pause", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getVariables", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "evaluate", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getCallStack", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "enableLogging", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getLogs", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "clearLogs", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "on", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "off", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getBasicInfo", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getMetadata", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "analyzeRules", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "validateRules", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "analyzeDependencies", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getPerformanceMetrics", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getMemoryUsage", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "analyzeConfiguration", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "addTest", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "removeTest", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "listTests", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "runAll", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "runTest", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "generateCoverage", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "start", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "stop", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "restart", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "getStatus", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "enableHotReload", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "disableHotReload", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "addMiddleware", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "removeMiddleware", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "watchFiles", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "unwatchFiles", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "on", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}, {"name": "off", "complexity": 0, "line": 1260, "column": 0, "filePath": "src/plugins/types.ts"}], "averageComplexity": 0}, {"filePath": "src/rules/index.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/rules/jsx/index.ts", "complexity": 0, "functions": [], "averageComplexity": 0}, {"filePath": "src/utils/index.ts", "complexity": 0, "functions": [], "averageComplexity": 0}], "qualityGateInfo": {"passed": false, "threshold": 15, "failingFunctions": 39, "failingItems": [{"name": "calculate", "filePath": "src/core/calculator.ts", "line": 1729, "complexity": 38}, {"name": "visitNode", "filePath": "src/core/calculator.ts", "line": 1729, "complexity": 25}, {"name": "containsLogicalOperators", "filePath": "src/core/calculator.ts", "line": 1729, "complexity": 20}, {"name": "validateMultilineStructure", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 45}, {"name": "extractMetrics", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 37}, {"name": "validateAdvancedPerformance", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 20}, {"name": "getSuggestions", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 19}, {"name": "validateObjectAgainstSchema", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 16}, {"name": "analyzeFiles", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 38}, {"name": "execute", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 33}, {"name": "outputResults", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 29}, {"name": "detectProjectType", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 21}, {"name": "getIgnorePatterns", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 17}, {"name": "getChildNodes", "filePath": "src/engine/iterative-algorithms.ts", "line": 743, "complexity": 51}, {"name": "findFunctionsInNode", "filePath": "src/core/parser.ts", "line": 559, "complexity": 29}, {"name": "findFunctionsIterative", "filePath": "src/core/parser.ts", "line": 559, "complexity": 22}, {"name": "getFunctionName", "filePath": "src/core/parser.ts", "line": 559, "complexity": 21}, {"name": "checkBreakpoints", "filePath": "src/engine/debug-system.ts", "line": 1585, "complexity": 26}, {"name": "scoreProjectType", "filePath": "src/config/project-detector.ts", "line": 939, "complexity": 72}, {"name": "validateConfigAgainstSchema", "filePath": "src/plugins/validator.ts", "line": 680, "complexity": 26}, {"name": "validateBasicStructure", "filePath": "src/plugins/validator.ts", "line": 680, "complexity": 18}, {"name": "validateConfigValue", "filePath": "src/plugins/validator.ts", "line": 680, "complexity": 16}, {"name": "waitForTasks", "filePath": "src/engine/execution-pool.ts", "line": 796, "complexity": 20}, {"name": "validateConfig", "filePath": "src/config/manager.ts", "line": 351, "complexity": 37}, {"name": "loadConfig", "filePath": "src/config/manager.ts", "line": 351, "complexity": 27}, {"name": "mergeRuntimeExcludes", "filePath": "src/config/manager.ts", "line": 351, "complexity": 17}, {"name": "validateSummaryData", "filePath": "src/__test__/helpers/data-consistency-validator.ts", "line": 637, "complexity": 17}, {"name": "detectVersionConflicts", "filePath": "src/plugins/dependency-resolver.ts", "line": 529, "complexity": 16}, {"name": "acquire", "filePath": "src/engine/object-pool.ts", "line": 856, "complexity": 19}, {"name": "deepMerge", "filePath": "src/config/modern-manager.ts", "line": 962, "complexity": 17}, {"name": "extractConditionInfo", "filePath": "src/rules/smart-conditional-rendering.ts", "line": 471, "complexity": 16}, {"name": "collectDiagnostics", "filePath": "src/formatters/json.ts", "line": 486, "complexity": 57}, {"name": "generateTypeScriptCode", "filePath": "src/__test__/helpers/test-utils.ts", "line": 1479, "complexity": 20}, {"name": "simulateUserFlow", "filePath": "src/__test__/helpers/interactive-test-helper.ts", "line": 576, "complexity": 18}, {"name": "updateBaseline", "filePath": "src/baseline/manager.ts", "line": 269, "complexity": 23}, {"name": "calculateDetectionScore", "filePath": "src/config/factory.ts", "line": 598, "complexity": 35}, {"name": "diagnoseFailure", "filePath": "src/__test__/helpers/debug-enhancer.ts", "line": 510, "complexity": 24}, {"name": "readFileWithRecovery", "filePath": "src/utils/error-recovery-service.ts", "line": 421, "complexity": 17}, {"name": "validateRuleSet", "filePath": "src/rules/rule-sets.ts", "line": 272, "complexity": 19}]}, "metadata": {"schemaVersion": "2.1.0", "generatedAt": "2025-07-29T06:04:59.153Z", "format": "cognitive-complexity-json", "detailsEnabled": true, "contextEnabled": false, "contextAllEnabled": false, "errorRecoveryEnabled": true, "qualityGate": {"passed": false, "threshold": 15, "failingFunctions": 39, "failingItems": [{"name": "calculate", "filePath": "src/core/calculator.ts", "line": 1729, "complexity": 38}, {"name": "visitNode", "filePath": "src/core/calculator.ts", "line": 1729, "complexity": 25}, {"name": "containsLogicalOperators", "filePath": "src/core/calculator.ts", "line": 1729, "complexity": 20}, {"name": "validateMultilineStructure", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 45}, {"name": "extractMetrics", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 37}, {"name": "validateAdvancedPerformance", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 20}, {"name": "getSuggestions", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 19}, {"name": "validateObjectAgainstSchema", "filePath": "src/__test__/helpers/output-validator.ts", "line": 1338, "complexity": 16}, {"name": "analyzeFiles", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 38}, {"name": "execute", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 33}, {"name": "outputResults", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 29}, {"name": "detectProjectType", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 21}, {"name": "getIgnorePatterns", "filePath": "src/cli/commands.ts", "line": 796, "complexity": 17}, {"name": "getChildNodes", "filePath": "src/engine/iterative-algorithms.ts", "line": 743, "complexity": 51}, {"name": "findFunctionsInNode", "filePath": "src/core/parser.ts", "line": 559, "complexity": 29}, {"name": "findFunctionsIterative", "filePath": "src/core/parser.ts", "line": 559, "complexity": 22}, {"name": "getFunctionName", "filePath": "src/core/parser.ts", "line": 559, "complexity": 21}, {"name": "checkBreakpoints", "filePath": "src/engine/debug-system.ts", "line": 1585, "complexity": 26}, {"name": "scoreProjectType", "filePath": "src/config/project-detector.ts", "line": 939, "complexity": 72}, {"name": "validateConfigAgainstSchema", "filePath": "src/plugins/validator.ts", "line": 680, "complexity": 26}, {"name": "validateBasicStructure", "filePath": "src/plugins/validator.ts", "line": 680, "complexity": 18}, {"name": "validateConfigValue", "filePath": "src/plugins/validator.ts", "line": 680, "complexity": 16}, {"name": "waitForTasks", "filePath": "src/engine/execution-pool.ts", "line": 796, "complexity": 20}, {"name": "validateConfig", "filePath": "src/config/manager.ts", "line": 351, "complexity": 37}, {"name": "loadConfig", "filePath": "src/config/manager.ts", "line": 351, "complexity": 27}, {"name": "mergeRuntimeExcludes", "filePath": "src/config/manager.ts", "line": 351, "complexity": 17}, {"name": "validateSummaryData", "filePath": "src/__test__/helpers/data-consistency-validator.ts", "line": 637, "complexity": 17}, {"name": "detectVersionConflicts", "filePath": "src/plugins/dependency-resolver.ts", "line": 529, "complexity": 16}, {"name": "acquire", "filePath": "src/engine/object-pool.ts", "line": 856, "complexity": 19}, {"name": "deepMerge", "filePath": "src/config/modern-manager.ts", "line": 962, "complexity": 17}, {"name": "extractConditionInfo", "filePath": "src/rules/smart-conditional-rendering.ts", "line": 471, "complexity": 16}, {"name": "collectDiagnostics", "filePath": "src/formatters/json.ts", "line": 486, "complexity": 57}, {"name": "generateTypeScriptCode", "filePath": "src/__test__/helpers/test-utils.ts", "line": 1479, "complexity": 20}, {"name": "simulateUserFlow", "filePath": "src/__test__/helpers/interactive-test-helper.ts", "line": 576, "complexity": 18}, {"name": "updateBaseline", "filePath": "src/baseline/manager.ts", "line": 269, "complexity": 23}, {"name": "calculateDetectionScore", "filePath": "src/config/factory.ts", "line": 598, "complexity": 35}, {"name": "diagnoseFailure", "filePath": "src/__test__/helpers/debug-enhancer.ts", "line": 510, "complexity": 24}, {"name": "readFileWithRecovery", "filePath": "src/utils/error-recovery-service.ts", "line": 421, "complexity": 17}, {"name": "validateRuleSet", "filePath": "src/rules/rule-sets.ts", "line": 272, "complexity": 19}]}}}