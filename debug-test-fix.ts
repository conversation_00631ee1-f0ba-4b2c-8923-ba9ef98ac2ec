#!/usr/bin/env bun

// 测试复杂度过滤功能的具体实现

import { analyzeFile } from './src';
import { TextFormatter } from './src/formatters/text';
import { CognitiveConfig, CLIOptions } from './src/config/types';

const testCode = `
// 简单函数 - 复杂度0
function simpleFunction() {
    return 'hello';
}

// 有条件的函数 - 复杂度1
function functionWithIf(x: number) {
    if (x > 0) {
        return x * 2;
    }
    return 0;
}

// 复杂函数 - 复杂度>1
function complexFunction(a: number, b: number) {
    if (a > 0) {
        if (b > 0) {
            for (let i = 0; i < a; i++) {
                if (i % 2 === 0) {
                    console.log(i);
                }
            }
        }
    }
    return a + b;
}

// 另一个简单函数 - 复杂度0
function verySimpleFunction() {
    const x = 1;
    const y = 2;
    return x + y;
}
`;

async function testBugFix() {
    console.log('🔍 测试 output-format-complexity-zero-filter bug 修复...\n');
    
    // 创建临时文件
    const tmpDir = `test-bug-fix-${Date.now()}`;
    const tmpFile = `${tmpDir}/test.ts`;
    const fs = await import('fs/promises');
    
    try {
        await fs.mkdir(tmpDir, { recursive: true });
        await fs.writeFile(tmpFile, testCode);
        
        // 分析文件
        const result = await analyzeFile(tmpFile, { enableDetails: true });
        
        console.log('📊 原始分析结果:');
        result.functions.forEach(func => {
            console.log(`  - ${func.name}: 复杂度 ${func.complexity}, 位置 (${func.line}:${func.column})`);
        });
        console.log('');
        
        // 构建分析结果对象
        const analysisResult = {
            summary: {
                totalComplexity: result.complexity,
                averageComplexity: result.averageComplexity,
                filesAnalyzed: 1,
                functionsAnalyzed: result.functions.length,
                highComplexityFunctions: result.functions.filter(f => f.complexity > 15).length
            },
            results: [result]
        };
        
        // 测试1: 默认配置 (minFunctionComplexity=1)
        console.log('🧪 测试1: 默认配置 - 应该隐藏复杂度0的函数');
        console.log('='.repeat(60));
        
        const defaultConfig: CognitiveConfig = {
            failOnComplexity: 15,
            exclude: [],
            report: {},
            severityMapping: [
                { level: 'Critical', threshold: 25 },
                { level: 'Warning', threshold: 15 },
                { level: 'Info', threshold: 10 }
            ],
            minFileComplexity: 1,
            minFunctionComplexity: 1
        };
        
        const formatter1 = new TextFormatter(defaultConfig);
        const options1: CLIOptions = {
            paths: [tmpFile],
            format: 'text'
        };
        
        const output1 = await formatter1.format(analysisResult, true, options1);
        console.log(output1);
        console.log('\n');
        
        // 测试2: 显示所有函数 (minFunctionComplexity=0)
        console.log('🧪 测试2: 显示所有函数 - 应该显示复杂度0的函数');
        console.log('='.repeat(60));
        
        const options2: CLIOptions = {
            paths: [tmpFile],
            format: 'text',
            minFunctionComplexity: 0
        };
        
        const output2 = await formatter1.format(analysisResult, true, options2);
        console.log(output2);
        console.log('\n');
        
        // 测试3: 使用showZeroComplexity选项
        console.log('🧪 测试3: showZeroComplexity选项 - 应该显示复杂度0的函数');
        console.log('='.repeat(60));
        
        const options3: CLIOptions = {
            paths: [tmpFile],
            format: 'text',
            showZeroComplexity: true
        };
        
        const output3 = await formatter1.format(analysisResult, true, options3);
        console.log(output3);
        console.log('\n');
        
        // 验证结果
        console.log('📝 验证结果:');
        
        const zeroComplexityFunctions = result.functions.filter(f => f.complexity === 0);
        console.log(`- 复杂度为0的函数数量: ${zeroComplexityFunctions.length}`);
        zeroComplexityFunctions.forEach(f => {
            console.log(`  * ${f.name} (${f.line}:${f.column})`);
        });
        
        const positionErrors = result.functions.filter(f => f.line === 1 && f.column === 0);
        console.log(`- 位置信息错误的函数数量: ${positionErrors.length}`);
        
        console.log('\n✅ Bug修复验证完成!');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        // 清理临时文件
        try {
            await fs.rm(tmpDir, { recursive: true, force: true });
        } catch (cleanupError) {
            console.warn('清理临时文件失败:', cleanupError);
        }
    }
}

// 运行测试
testBugFix().catch(console.error);