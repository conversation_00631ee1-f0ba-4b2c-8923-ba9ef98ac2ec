import { ComplexityCalculator } from './src/core/calculator';

async function testGracefulExit() {
  console.log('开始测试脚本退出问题...');
  
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  const calculator = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: true
  });
  
  const results = await calculator.calculateCode(code, 'test.js');
  console.log('总复杂度:', results[0]?.complexity);
  
  console.log('计算完成，强制退出...');
  
  // 强制退出进程
  process.exit(0);
}

testGracefulExit().catch((error) => {
  console.error('错误:', error);
  process.exit(1);
});