// 更复杂的测试文件，包含导出对象和类型定义
export interface UserProps {
  name: string;
  age: number;
  isActive: boolean;
}

export const configOptions = {
  timeout: 5000,
  retries: 3,
  debug: false
};

export type StatusType = 'pending' | 'completed' | 'failed';

export class UserManager {
  private users: UserProps[] = [];

  addUser(user: UserProps): void {
    if (user.age < 0) {
      throw new Error('Age cannot be negative');
    }
    
    if (this.users.some(u => u.name === user.name)) {
      console.warn('User already exists');
      return;
    }
    
    this.users.push(user);
  }

  getActiveUsers(): UserProps[] {
    return this.users.filter(user => {
      if (user.isActive && user.age >= 18) {
        return true;
      }
      return false;
    });
  }
}

export function processUser(user: UserProps): StatusType {
  if (!user.name || user.name.trim().length === 0) {
    return 'failed';
  }
  
  if (user.age < 13) {
    return 'pending';
  }
  
  return 'completed';
}