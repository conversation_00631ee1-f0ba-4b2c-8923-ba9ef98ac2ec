import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';

async function debugComplexityCalculation() {
  const sourceCode = `
    function test() {
      if (condition1) {                   // +1 (基础) + 0 (嵌套层级) = +1
        for (let i = 0; i < 10; i++) {   // +1 (基础) + 1 (嵌套层级) = +2
          if (condition2) {               // +1 (基础) + 2 (嵌套层级) = +3
            try {                         // try 本身不增加复杂度
              riskyOperation();
            } catch (error) {             // +1 (基础) + 3 (嵌套层级) = +4
              handleError();
            }
          }
        }
      }
    }
  `;
  console.log('源代码:', sourceCode);

  const parser = new ASTParser();
  const ast = await parser.parseCode(sourceCode, 'debug.ts');
  
  const visitor = new ComplexityVisitor(sourceCode);
  console.log('开始分析...');
  
  visitor.visit(ast);
  
  console.log('分析完成');
  console.log('总复杂度:', visitor.getTotalComplexity());
  console.log('期望复杂度: 10');
  console.log('差异:', visitor.getTotalComplexity() - 10);
}

debugComplexityCalculation().catch(console.error);