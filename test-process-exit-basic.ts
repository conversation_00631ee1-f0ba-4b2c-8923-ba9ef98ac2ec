#!/usr/bin/env bun
/**
 * 基本分析后进程退出验证脚本
 * 验证使用静态API进行基本分析后，进程能在5秒内自然退出
 */

import { ComplexityCalculator } from './src/core/calculator';

async function testBasicProcessExit() {
  console.log('🧪 测试基本分析后进程退出');
  
  const startTime = Date.now();
  
  try {
    // 测试简单代码分析
    const simpleCode = `
      function simple() {
        return 42;
      }
      
      function withCondition(x) {
        if (x > 0) {
          return x * 2;
        }
        return 0;
      }
    `;
    
    console.log('📋 执行基本复杂度分析...');
    const results = await ComplexityCalculator.analyze(simpleCode);
    
    console.log(`✅ 分析完成: 找到 ${results.length} 个函数`);
    results.forEach(result => {
      console.log(`  - ${result.name}: 复杂度 ${result.complexity}`);
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`⏱️ 分析耗时: ${duration}ms`);
    console.log('🎉 基本分析测试完成，进程应该自动退出');
    
  } catch (error) {
    console.error('❌ 基本分析测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testBasicProcessExit().catch(error => {
  console.error('💥 测试执行失败:', error);
  process.exit(1);
});