#!/usr/bin/env bun

/**
 * 调试Calculator的具体执行过程
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';

async function debugCalculatorProcess() {
  console.log('=== 调试Calculator执行过程 ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  // 使用详细日志
  const factory = new CalculatorFactory({
    debugMode: true,
    quiet: false, // 开启详细日志
    enableCaching: false
  });
  
  const calculator = new ComplexityCalculator({
    enableDebugLog: true,
    quiet: false
  }, factory);
  
  try {
    console.log('开始分析...');
    const results = await calculator.calculateCode(testCode, 'debug.ts');
    console.log('\n=== 最终结果 ===');
    console.log(JSON.stringify(results, null, 2));
  } catch (error) {
    console.error('分析错误:', error);
  } finally {
    await calculator.dispose();
  }
}

debugCalculatorProcess().catch(console.error);