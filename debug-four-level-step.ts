import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';

async function debugFourLevelStep() {
  const sourceCode = `
    function test() {
      if (condition1) {
        for (let i = 0; i < 10; i++) {
          if (condition2) {
            try {
              riskyOperation();
            } catch (error) {
              handleError();
            }
          }
        }
      }
    }
  `;
  
  console.log('=== 四层嵌套逐步测试 ===');
  console.log('源代码:', sourceCode.trim());

  const parser = new ASTParser();
  const ast = await parser.parseCode(sourceCode, 'debug.ts');
  
  const visitor = new ComplexityVisitor(sourceCode);
  
  console.log('\n=== 分析结果 ===');
  visitor.visit(ast);
  
  console.log('总复杂度:', visitor.getTotalComplexity());
  console.log('\n=== 期望分析 ===');
  console.log('- if (condition1): 基础1 + 嵌套0 = 1');
  console.log('- for loop: 基础1 + 嵌套1 = 2');
  console.log('- if (condition2): 基础1 + 嵌套2 = 3');
  console.log('- catch clause: 基础1 + 嵌套3 = 4');
  console.log('- 期望总计: 1 + 2 + 3 + 4 = 10');
  console.log('- 实际差异:', visitor.getTotalComplexity() - 10);

  // 让我们检查有没有额外的逻辑运算符被计算
  console.log('\n=== 可能的额外计算 ===');
  console.log('- try语句本身: 通常不增加复杂度');
  console.log('- 函数调用: riskyOperation(), handleError() 通常不增加复杂度');
  console.log('- 其他控制结构: 没有');
}

debugFourLevelStep().catch(console.error);