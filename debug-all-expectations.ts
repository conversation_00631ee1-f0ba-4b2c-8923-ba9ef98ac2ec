#!/usr/bin/env bun

/**
 * 批量获取所有测试用例的实际复杂度值
 */

import { ComplexityCalculator } from './src/core/calculator';
import { basicCases } from './src/__test__/fixtures/cases/basic';
import { logicalCases } from './src/__test__/fixtures/cases/logical';

async function getActualComplexityValues() {
  console.log('=== 获取所有测试用例的实际复杂度值 ===\n');
  
  // 测试基础用例
  console.log('--- 基础用例 ---');
  for (const testCase of basicCases) {
    try {
      const results = await ComplexityCalculator.analyze(testCase.code);
      const actual = results[0]?.complexity ?? 'N/A';
      const expected = testCase.expected[0]?.complexity ?? 'N/A';
      const status = actual === expected ? '✅' : '❌';
      
      console.log(`${status} ${testCase.name}: 期望=${expected}, 实际=${actual}`);
    } catch (error) {
      console.log(`❌ ${testCase.name}: 解析错误 - ${error}`);
    }
  }
  
  // 测试逻辑运算符用例
  console.log('\\n--- 逻辑运算符用例 ---');
  for (const testCase of logicalCases) {
    try {
      const results = await ComplexityCalculator.analyze(testCase.code);
      const actual = results[0]?.complexity ?? 'N/A';
      const expected = testCase.expected[0]?.complexity ?? 'N/A';
      const status = actual === expected ? '✅' : '❌';
      
      console.log(`${status} ${testCase.name}: 期望=${expected}, 实际=${actual}`);
    } catch (error) {
      console.log(`❌ ${testCase.name}: 解析错误 - ${error}`);
    }
  }
  
  console.log('\\n=== 建议的期望值更新 ===');
  console.log('需要更新的测试用例:');
  
  // 重新检查并输出需要更新的用例
  const updates = [];
  
  for (const testCase of basicCases) {
    try {
      const results = await ComplexityCalculator.analyze(testCase.code);
      const actual = results[0]?.complexity ?? 'N/A';
      const expected = testCase.expected[0]?.complexity ?? 'N/A';
      
      if (actual !== expected && actual !== 'N/A') {
        updates.push({
          name: testCase.name,
          file: 'basic.ts',
          from: expected,
          to: actual
        });
      }
    } catch (error) {
      // 跳过解析错误的用例
    }
  }
  
  for (const testCase of logicalCases) {
    try {
      const results = await ComplexityCalculator.analyze(testCase.code);
      const actual = results[0]?.complexity ?? 'N/A';
      const expected = testCase.expected[0]?.complexity ?? 'N/A';
      
      if (actual !== expected && actual !== 'N/A') {
        updates.push({
          name: testCase.name,
          file: 'logical.ts',
          from: expected,
          to: actual
        });
      }
    } catch (error) {
      // 跳过解析错误的用例
    }
  }
  
  updates.forEach(update => {
    console.log(`${update.file} - ${update.name}: ${update.from} → ${update.to}`);
  });
}

getActualComplexityValues().catch(console.error);