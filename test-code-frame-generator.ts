#!/usr/bin/env bun

/**
 * 测试CodeFrameGenerator是否是超时的根本原因
 */

import { getCodeFrameGenerator } from './src/utils/code-frame-generator';

function testCodeFrameGenerator() {
  console.log('🧪 测试CodeFrameGenerator是否导致超时...\n');
  
  try {
    console.log('🔧 获取CodeFrameGenerator单例...');
    const generator = getCodeFrameGenerator();
    
    console.log('📝 CodeFrameGenerator已创建，检查是否启动了定时器...');
    
    // 检查是否有活跃的定时器
    console.log('⏰ 活跃handle数量:', process._getActiveHandles().length);
    console.log('🔄 活跃请求数量:', process._getActiveRequests().length);
    
    console.log('\n🎯 等待观察是否超时...');
    console.log('💡 如果有定时器运行，进程不会自动退出');
    
    // 等待一段时间看是否有定时器活动
    setTimeout(() => {
      console.log('⏰ 5秒后活跃handle数量:', process._getActiveHandles().length);
      console.log('🔄 5秒后活跃请求数量:', process._getActiveRequests().length);
      
      // 尝试销毁生成器
      console.log('\n🧹 尝试销毁CodeFrameGenerator...');
      generator.destroy();
      
      setTimeout(() => {
        console.log('⏰ 销毁后活跃handle数量:', process._getActiveHandles().length);
        console.log('🔄 销毁后活跃请求数量:', process._getActiveRequests().length);
        console.log('✅ 测试完成，进程应该退出');
        process.exit(0);
      }, 1000);
    }, 5000);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

testCodeFrameGenerator();