import { ComplexityCalculator } from './src/core/calculator';
import { createLightweightFactory } from './src/core/calculator-factory';

async function testRecursion() {
  const factory = createLightweightFactory();
  const calculator = new ComplexityCalculator({ 
    enableDetails: true 
  }, factory);

  const code = `
    function recursiveFunction(n) {
      if (n <= 1) {
        return 1;
      }
      return recursiveFunction(n - 1) + recursiveFunction(n - 2);
    }
  `;

  try {
    const functions = await calculator.calculateCode(code, 'test.ts');
    console.log('Functions found:', functions.length);
    
    if (functions.length > 0) {
      const func = functions[0];
      console.log('Function name:', func.name);
      console.log('Complexity:', func.complexity);
      if (func.details) {
        console.log('Details:');
        func.details.forEach((detail, i) => {
          console.log(`  ${i+1}. ${detail.ruleId || 'unknown'}: ${detail.description || detail.reason || 'no description'}`);
        });
      }
    }
  } finally {
    await calculator.dispose();
  }
}

testRecursion().catch(console.error);