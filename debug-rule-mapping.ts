#!/usr/bin/env bun

/**
 * Direct test of the getRuleIdForNodeType bug
 */

import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

// Test the rule mapping directly
function testRuleMappingBug() {
  console.log('=== Direct Rule Mapping Bug Test ===\n');

  // Create a mock BinaryExpression that's NOT a logical operator
  const mockArithmeticBinaryExpression = {
    type: 'BinaryExpression',
    operator: '+',  // Arithmetic, NOT logical
    left: { type: 'Identifier', value: 'a' },
    right: { type: 'Identifier', value: 'b' },
    span: { start: 10, end: 20, ctxt: 0 }
  };

  const mockLogicalBinaryExpression = {
    type: 'BinaryExpression', 
    operator: '&&',  // This IS logical
    left: { type: 'Identifier', value: 'a' },
    right: { type: 'Identifier', value: 'b' },
    span: { start: 10, end: 20, ctxt: 0 }
  };

  console.log('Testing rule mapping on different BinaryExpression types:\n');

  // Simulate the getRuleIdForNodeType logic
  const ruleMap: Record<string, string> = {
    'BinaryExpression': 'logical-operator',  // The problematic mapping
    'LogicalExpression': 'logical-operator',
  };

  console.log('Current mapping for arithmetic + operator:');
  console.log(`  Node type: ${mockArithmeticBinaryExpression.type}`);
  console.log(`  Operator: ${mockArithmeticBinaryExpression.operator}`);
  console.log(`  Maps to rule: ${ruleMap[mockArithmeticBinaryExpression.type]}`);
  console.log(`  ❌ WRONG: Arithmetic + should NOT be logical-operator!\n`);

  console.log('Current mapping for logical && operator:');
  console.log(`  Node type: ${mockLogicalBinaryExpression.type}`);
  console.log(`  Operator: ${mockLogicalBinaryExpression.operator}`);
  console.log(`  Maps to rule: ${ruleMap[mockLogicalBinaryExpression.type]}`);
  console.log(`  ✅ CORRECT: Logical && should be logical-operator\n`);

  // Test the existing isLogicalOperator method
  const visitor = new ComplexityVisitor('', new DetailCollector());
  
  // Access the private method for testing (hack for testing)
  const isLogicalOperator = (visitor as any).isLogicalOperator.bind(visitor);
  
  console.log('Testing isLogicalOperator method:');
  console.log(`  + operator: ${isLogicalOperator(mockArithmeticBinaryExpression)} (should be false)`);
  console.log(`  && operator: ${isLogicalOperator(mockLogicalBinaryExpression)} (should be true)`);

  // Test the shouldSkipNode method
  const shouldSkipNode = (visitor as any).shouldSkipNode.bind(visitor);
  
  console.log('\nTesting shouldSkipNode method:');
  console.log(`  + operator: ${shouldSkipNode(mockArithmeticBinaryExpression)} (should be true - skip)`);
  console.log(`  && operator: ${shouldSkipNode(mockLogicalBinaryExpression)} (should be false - don't skip)`);

  console.log('\n=== Analysis ===');
  console.log('✅ The isLogicalOperator method correctly identifies logical operators');
  console.log('✅ The shouldSkipNode method correctly skips non-logical binary expressions');
  console.log('❌ BUT: The getRuleIdForNodeType method maps ALL BinaryExpression to logical-operator');
  console.log('🔧 Fix needed: Only map BinaryExpression to logical-operator when it\'s actually logical');
}

testRuleMappingBug();