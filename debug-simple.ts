import { parse } from '@swc/core';

async function debugSimpleCall() {
  const code = `
    class TreeNode {
      traverse() {
        this.traverse();
      }
    }
  `;

  const result = await parse(code, {
    syntax: 'typescript',
    target: 'es2022',
  });

  // 查找调用表达式节点
  function findCallExpression(node: any): any {
    if (node?.type === 'CallExpression') {
      console.log('Found CallExpression:');
      console.log('  callee:', JSON.stringify(node.callee, null, 2));
      return node;
    }
    
    if (node && typeof node === 'object') {
      for (const key in node) {
        if (key !== 'parent' && key !== 'span') {
          const child = node[key];
          if (Array.isArray(child)) {
            for (const item of child) {
              const found = findCallExpression(item);
              if (found) return found;
            }
          } else {
            const found = findCallExpression(child);
            if (found) return found;
          }
        }
      }
    }
    
    return null;
  }

  const callNode = findCallExpression(result);
  console.log('\nIdentifier value/name check:');
  const property = callNode?.callee?.property;
  console.log('property.value:', property?.value);
  console.log('property.name:', property?.name);
}

debugSimpleCall().catch(console.error);