import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

async function debugDetailedComplexity() {
  const sourceCode = `
    function test() {
      if (condition1) {                   
        for (let i = 0; i < 10; i++) {   
          if (condition2) {               
            try {                         
              riskyOperation();
            } catch (error) {             
              handleError();
            }
          }
        }
      }
    }
  `;
  
  console.log('=== 详细复杂度分析 ===');
  console.log('源代码:', sourceCode.trim());

  const parser = new ASTParser();
  const ast = await parser.parseCode(sourceCode, 'debug.ts');
  
  // 使用DetailCollector来追踪详细步骤
  const detailCollector = new DetailCollector();
  detailCollector.startFunction('test', 2, 9);
  
  const visitor = new ComplexityVisitor(sourceCode, detailCollector);
  
  console.log('\n=== 开始分析 ===');
  visitor.visit(ast);
  
  const functionDetails = detailCollector.endFunction();
  
  console.log('\n=== 分析结果 ===');
  console.log('总复杂度:', visitor.getTotalComplexity());
  console.log('步骤数量:', functionDetails.details.length);
  
  console.log('\n=== 详细步骤 ===');
  functionDetails.details.forEach((step, index) => {
    console.log(`${index + 1}. ${step.reason} (+${step.complexity}) [嵌套:${step.nestingLevel}]`);
  });
  
  const totalFromSteps = functionDetails.details.reduce((sum, step) => sum + step.complexity, 0);
  console.log('\n=== 汇总 ===');
  console.log('步骤总计:', totalFromSteps);
  console.log('访问者总计:', visitor.getTotalComplexity());
  console.log('期望值: 10');
  console.log('差异:', visitor.getTotalComplexity() - 10);
}

debugDetailedComplexity().catch(console.error);