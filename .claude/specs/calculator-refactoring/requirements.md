# Calculator Refactoring - Requirements

## Overview
重构 src/core/calculator.ts 的架构，将其从一个承担多重职责的复杂类重构为职责分离的架构模式。这是一个关键的架构改进，目标是将当前2486行的庞大类分解为单一职责的组件协作模式。

## Context Analysis
### 现有代码库架构分析
基于对现有代码库的深入分析，发现：

**现有 calculator.ts 的问题：**
- 文件长度：2486行，承担过多职责
- 职责混杂：包含 AST 遍历、规则判断、状态管理、性能监控、错误处理等
- 复杂度过高：包含大量复杂的逻辑分支和条件判断
- IoC架构：已实现依赖注入和工厂模式，但未充分利用

**可重用的现有组件：**
- ✅ ComplexityVisitor (src/core/complexity-visitor.ts): 已实现基于访问者模式的AST遍历
- ✅ RuleRegistry (src/core/rule-registry.ts): 已实现规则注册和管理机制
- ✅ CalculatorFactory (src/core/calculator-factory.ts): 已实现IoC工厂模式
- ✅ 现有规则类 (src/rules/): 已有部分规则类实现如logical-operator-rule.ts等
- ✅ BaseVisitor (src/core/base-visitor.ts): 访问者基类
- ✅ 测试框架 (src/__test__/core/calculator.test.ts): 现有测试用例作为安全网

**技术约束来自引导文档：**
- 必须保持Node.js兼容性，不使用Bun特定API
- 遵循模块化设计和单一职责原则
- 使用TypeScript严格模式和现有架构模式
- 保持对象池和性能优化特性

## Requirements

### Requirement 1: 职责单一化重构
**User Story:** 作为开发者，我希望将calculator.ts从2486行的庞大类重构为职责单一的协调器，使其仅负责流程编排，从而提高代码可维护性和可测试性。

#### Acceptance Criteria
1. WHEN calculator.ts 被重构后 THEN 它应该只保留流程协调逻辑（约200-300行）
2. WHEN calculator.ts 执行分析时 THEN 它只做三件事：创建ComplexityVisitor、启动分析、返回结果
3. IF calculator.ts 中存在具体的复杂度规则逻辑 THEN 必须迁移到src/rules/目录下的独立Rule类
4. WHEN 重构完成后 THEN calculator.ts不应包含任何visitNode、calculateNestingPenalty等方法
5. IF 保留IoC依赖注入特性 THEN 必须通过CalculatorFactory管理依赖

### Requirement 2: ComplexityVisitor架构集成
**User Story:** 作为开发者，我希望充分利用现有的ComplexityVisitor和BaseVisitor架构，使AST遍历逻辑与业务逻辑完全分离。

#### Acceptance Criteria
1. WHEN calculator.ts调用ComplexityVisitor时 THEN 应该使用现有的visit()方法
2. WHEN ComplexityVisitor遍历节点时 THEN 它应该委托RuleRegistry进行复杂度评估
3. IF 需要详细信息收集 THEN 应该通过DetailCollector与ComplexityVisitor集成
4. WHEN 使用访问者模式时 THEN 应该保持现有的错误恢复和性能监控机制

### Requirement 3: 规则引擎重构
**User Story:** 作为开发者，我希望将现有的复杂规则逻辑拆分为独立的Rule类，利用现有的RuleRegistry架构实现规则可插拔管理。

#### Acceptance Criteria
1. WHEN 创建规则类时 THEN 应该继承现有的base-rule.ts或实现统一接口
2. WHEN 迁移规则逻辑时 THEN 优先重构以下规则（按复杂度从高到低）：
   - LogicalOperatorRule（逻辑运算符混用检测）
   - IfStatement/ForStatement/WhileStatement规则
   - ConditionalExpression和CatchClause规则
   - RecursiveCall检测规则
3. IF RuleRegistry收到评估请求 THEN 应该使用现有的register()和evaluate()机制
4. WHEN 规则类实现时 THEN 应该遵循现有的规则命名约定（kebab-case）

### Requirement 4: 现有测试兼容性
**User Story:** 作为开发者，我希望利用现有的完整测试套件作为重构安全网，确保重构不破坏任何现有功能。

#### Acceptance Criteria
1. WHEN 开始重构前 THEN 必须确保src/__test__/core/calculator.test.ts全部测试通过
2. WHEN 每次迁移一个规则后 THEN 必须运行完整测试套件验证功能完整性
3. IF 发现测试失败 THEN 必须立即修复后才能继续下一个规则
4. WHEN 重构完成后 THEN 所有现有测试（包括嵌套复杂度、逻辑运算符等）必须100%通过
5. IF 新增的Rule类 THEN 应该添加对应的单元测试

### Requirement 5: 性能和IoC架构保持
**User Story:** 作为开发者，我希望在重构过程中保持现有的高性能特性和IoC架构优势，不影响工具的分析性能。

#### Acceptance Criteria  
1. WHEN 重构后 THEN 必须保持现有的缓存机制、对象池和异步引擎特性
2. WHEN 使用CalculatorFactory时 THEN 应该保持现有的依赖注入模式
3. IF 保留静态API THEN analyze()、analyzeFile()、quickAnalyze()等方法必须继续工作
4. WHEN 执行大型项目分析时 THEN 性能不应显著下降（保持<30秒的目标）
5. IF 使用详细模式 THEN DetailCollector集成必须正常工作

### Requirement 6: 代码架构清理
**User Story:** 作为开发者，我希望清理重构后的代码，移除冗余逻辑，使最终架构清晰简洁。

#### Acceptance Criteria  
1. WHEN 规则迁移完成后 THEN 必须删除calculator.ts中所有相关的旧逻辑
2. IF 发现不再使用的私有方法 THEN 应该完全移除（如visitNode、calculateNestingPenalty等）
3. WHEN 最终清理完成后 THEN calculator.ts应该只保留必要的public API和依赖注入逻辑
4. IF 保留错误处理 THEN 应该简化为委托模式，不包含具体的复杂度计算逻辑
5. WHEN 代码审查时 THEN 重构后的文件结构应该符合引导文档的架构原则

## Technical Constraints
- **Node.js兼容性**: 重构后的代码必须完全兼容Node.js 18+
- **向后兼容**: 不能修改public API接口，保持现有用户代码兼容
- **IoC架构**: 必须利用现有的CalculatorFactory和依赖注入机制
- **性能要求**: 重构不能显著影响分析性能（<5%性能下降可接受）
- **TypeScript严格模式**: 所有重构代码必须通过严格类型检查

## Dependencies & Integration Points
- **核心依赖**: ComplexityVisitor、RuleRegistry、CalculatorFactory
- **规则系统**: 现有rules/目录下的规则类和base-rule.ts
- **测试安全网**: calculator.test.ts及相关测试文件
- **工具类**: DetailCollector、PositionConverter、type-guards等
- **配置系统**: CalculationOptions和相关配置管理

## Success Criteria & Metrics
- ✅ calculator.ts文件长度减少至300行以内（从2486行）
- ✅ 所有现有测试100%通过，无回归问题
- ✅ 新的Rule类完全承担复杂度计算职责
- ✅ ComplexityVisitor与RuleRegistry正确协作
- ✅ 保持现有的高性能特性和缓存机制
- ✅ 静态API（analyze、analyzeFile等）继续正常工作
- ✅ IoC架构和依赖注入机制得到充分利用
- ✅ 代码结构符合模块化设计和单一职责原则