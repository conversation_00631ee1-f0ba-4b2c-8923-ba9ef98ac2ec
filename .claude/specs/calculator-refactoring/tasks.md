# Calculator Refactoring - Tasks

## Task Overview

This task breakdown refactors the massive 2486-line `src/core/calculator.ts` into a responsibility-separated coordinator architecture. Using incremental migration strategy, each task has clear verification criteria to ensure the refactoring process is safe and reliable, while fully leveraging existing IoC architecture, visitor pattern, and rule engine.

## Steering Document Compliance

### Project Structure (structure.md)
- ✅ Rule classes placed in `src/rules/` directory, following module responsibility division
- ✅ Use kebab-case naming conventions and unified export strategy
- ✅ Test files correspondingly placed in `src/__test__/rules/` directory

### Technical Standards (tech.md)
- ✅ Maintain Node.js compatibility, leverage existing TypeScript strict mode
- ✅ Follow modular design and dependency injection principles
- ✅ Maintain async processing and performance optimization features

## Tasks

- [x] 1. Establish refactoring safety net and basic verification
  - Run existing test suite to ensure all pass as baseline
  - Create dedicated refactoring verification test cases
  - Verify existing IoC architecture components work properly
  - _Leverage: src/__test__/core/calculator.test.ts, src/core/calculator-factory.ts_
  - _Requirements: 4.1, 4.2_

- [x] 2. Analyze and backup existing complexity calculation logic
  - Extract all rule judgment logic and algorithms from calculator.ts
  - Document existing complexity calculation rules and nesting penalty mechanisms
  - Identify core methods to migrate and their dependencies
  - _Leverage: src/core/calculator.ts (lines 1100-2486)_
  - _Requirements: 1.3, 3.1_

- [ ] 3. Create basic rule class framework
  - [x] 3.1 Verify and extend BaseRule base class functionality
    - Confirm abstract methods and caching mechanisms in BaseRule.ts
    - Add standardized methods for nesting level handling
    - Implement rule priority and dependency management mechanisms
    - _Leverage: src/rules/base-rule.ts, src/engine/types.ts_
    - _Requirements: 3.1, 3.4_

  - [x] 3.2 Extend RuleRegistry to support new rules
    - Register new rule IDs and descriptions in default-rules.ts
    - Ensure rule naming follows kebab-case conventions
    - Implement dynamic loading and management mechanisms for rules
    - _Leverage: src/core/rule-registry.ts, src/core/default-rules.ts_
    - _Requirements: 3.3, 3.4_

- [ ] 4. Implement core complexity rule classes (in priority order)
  - [x] 4.1 Create IfStatementRule (highest priority)
    - Migrate if statement complexity calculation logic from calculator.ts
    - Implement nesting level increase and base complexity calculation
    - Add handling for else-if chains and nested if statements
    - Create corresponding unit tests
    - _Leverage: src/rules/base-rule.ts, calculator.ts (visitIfStatement logic)_
    - _Requirements: 1.3, 3.1, 3.2_

  - [x] 4.2 Create ForStatementRule (high priority)
    - Migrate for, for-in, for-of loop complexity logic
    - Implement complexity penalty mechanism for loop nesting
    - Handle unified evaluation interface for different loop types
    - Create loop complexity test cases
    - _Leverage: src/rules/base-rule.ts, calculator.ts (loop handling logic)_
    - _Requirements: 1.3, 3.1, 3.2_

  - [x] 4.3 Create WhileStatementRule (high priority)
    - Migrate while and do-while loop complexity logic
    - Implement complexity calculation strategy consistent with for loops
    - Handle condition complexity evaluation for while loops
    - Add while loop specific test scenarios
    - _Leverage: src/rules/base-rule.ts, calculator.ts (while loop logic)_
    - _Requirements: 1.3, 3.1, 3.2_

  - [x] 4.4 Verify LogicalOperatorRule integration (existing rule)
    - Confirm existing LogicalOperatorRule is compatible with new architecture
    - Verify mixing detection and default value assignment exemption functionality
    - Test logical operator rule collaboration with other rules
    - _Leverage: src/rules/logical-operator-rule.ts_
    - _Requirements: 3.1, 3.3_

  - [x] 4.5 Create ConditionalExpressionRule (medium priority)
    - Migrate ternary operator (?:) complexity calculation logic
    - Implement complexity penalty for nested ternary operators
    - Handle nesting level management for conditional expressions
    - Create conditional expression test cases
    - _Leverage: src/rules/base-rule.ts, calculator.ts (conditional logic)_
    - _Requirements: 1.3, 3.1, 3.2_

  - [x] 4.6 Create CatchClauseRule (medium priority)
    - Migrate try-catch-finally complexity calculation
    - Implement complexity evaluation for exception handling blocks
    - Handle complexity calculation for nested exception handling
    - Add exception handling complexity tests
    - _Leverage: src/rules/base-rule.ts, calculator.ts (catch handling)_
    - _Requirements: 1.3, 3.1, 3.2_

  - [x] 4.7 Create RecursiveCallRule (low priority)
    - Migrate recursive function call detection and complexity calculation
    - Implement identification of direct and indirect recursion
    - Handle this.method() form recursive calls
    - Create recursive call test scenarios
    - _Leverage: src/rules/base-rule.ts, calculator.ts (recursive detection)_
    - _Requirements: 1.3, 3.1, 3.2_

- [ ] 5. Enhance ComplexityVisitor and rule engine integration
  - Modify ComplexityVisitor's visit methods to delegate to RuleRegistry
  - Ensure visitor properly handles nesting level changes and complexity accumulation
  - Implement seamless integration with DetailCollector
  - Maintain existing error recovery and performance monitoring mechanisms
  - _Leverage: src/core/complexity-visitor.ts, src/core/rule-registry.ts_
  - _Requirements: 2.1, 2.2, 2.4_

- [ ] 6. Refactor ComplexityCalculator as simplified coordinator
  - [ ] 6.1 Simplify main calculation methods
    - Simplify calculateFunctionComplexity method to pure delegation pattern
    - Remove all specific complexity calculation logic
    - Maintain IoC dependency injection and factory pattern integration
    - _Leverage: src/core/calculator-factory.ts, src/core/complexity-visitor.ts_
    - _Requirements: 1.1, 1.2, 1.5_

  - [ ] 6.2 Clean up no longer needed private methods
    - Delete visitNode, calculateNestingPenalty and other old methods
    - Remove complex rule judgment logic and state management code
    - Retain necessary error handling and performance monitoring interfaces
    - _Requirements: 1.4, 6.2_

  - [ ] 6.3 Simplify error handling and logging
    - Delegate complex error handling logic to components
    - Simplify logging to delegation pattern
    - Maintain debug mode and performance monitoring functionality
    - _Requirements: 6.4_

- [ ] 7. Maintain high-performance features and static API compatibility
  - [ ] 7.1 Verify IoC architecture and dependency injection work properly
    - Test CalculatorFactory component creation and management
    - Confirm caching mechanisms and object pool functionality work normally
    - Verify async engine and concurrent processing are unaffected
    - _Requirements: 5.1, 5.2_

  - [ ] 7.2 Ensure static API methods continue working
    - Test analyze(), analyzeFile(), quickAnalyze() methods
    - Verify batch file analysis functionality
    - Confirm detail mode and DetailCollector integration
    - _Requirements: 5.3, 5.5_

- [ ] 8. Comprehensive test verification and performance regression testing
  - [ ] 8.1 Run complete test suite verification
    - Ensure all existing tests 100% pass
    - Verify nested complexity, mixed logical operators and other advanced functionality
    - Test boundary conditions and error handling scenarios
    - _Requirements: 4.3, 4.4_

  - [ ] 8.2 Execute performance regression testing
    - Verify large project analysis still completes within 30 seconds
    - Check memory usage doesn't exceed baseline 100MB
    - Confirm cache hit rate and concurrent performance metrics
    - _Requirements: 5.4_

  - [ ] 8.3 Create unit tests for new rule classes
    - Add full coverage tests for each newly created rule class
    - Test rule canHandle and evaluate methods
    - Verify caching mechanisms and performance optimization functionality
    - _Requirements: 4.5_

- [ ] 9. Final architecture verification and code cleanup
  - [ ] 9.1 Code architecture compliance check
    - Verify file length target: calculator.ts < 300 lines
    - Confirm responsibility separation: coordinator only does process orchestration
    - Check modular design and single responsibility principle compliance
    - _Requirements: 1.1, 6.3, 6.5_

  - [ ] 9.2 Final cleanup and optimization
    - Clean up all temporary code and debug information
    - Optimize import statements and dependencies
    - Ensure TypeScript strict mode checks pass
    - Verify all ESLint and formatting rules comply
    - _Requirements: 6.1_

  - [ ] 9.3 Create refactoring completion verification report
    - Compare complexity and performance metrics before and after refactoring
    - Verify all success criteria and metrics are achieved
    - Generate refactoring summary and best practices documentation
    - _Requirements: All success criteria verification_

## Implementation Priority

**Phase 1 (Infrastructure):** Tasks 1-3
**Phase 2 (Core Rule Migration):** Tasks 4.1-4.4
**Phase 3 (Complete Rule Set):** Tasks 4.5-4.7
**Phase 4 (Architecture Integration):** Tasks 5-6
**Phase 5 (Verification Optimization):** Tasks 7-8
**Phase 6 (Final Cleanup):** Task 9

After each phase completion, must run complete test suite to ensure no regression, ensuring safety and reliability of the refactoring process.