# Calculator Refactoring - Design Document

## Overview

This design document describes the architectural refactoring of the massive 2486-line `src/core/calculator.ts` into a responsibility-separated coordinator architecture. The refactoring will fully leverage existing visitor pattern, IoC architecture, and rule engine to achieve a highly modular and testable complexity analysis system.

**Core Principles:**
- Maintain existing IoC architecture and dependency injection patterns
- Fully utilize ComplexityVisitor visitor pattern
- Extend existing RuleRegistry rule engine
- Maintain all public API backward compatibility
- Ensure high-performance features (cache, object pool, etc.) remain unaffected

## Steering Document Alignment

### Technical Standards (tech.md)
- ✅ **Node.js Compatibility**: Refactored code fully compatible with Node.js 18+, no Bun-specific APIs
- ✅ **Modular Design**: Follow single responsibility and dependency injection principles
- ✅ **Performance Optimization**: Maintain async processing, caching strategy, and concurrent processing features
- ✅ **Error Handling**: Layered error handling and graceful degradation mechanisms
- ✅ **TypeScript Strict Mode**: All code passes strict type checking

### Project Structure (structure.md)
- ✅ **Module Boundaries**: Strictly organize code according to existing directory structure
- ✅ **Core Module**: Place refactored rule classes in `src/rules/` directory
- ✅ **File Naming**: Follow kebab-case and functional description naming conventions
- ✅ **Export Strategy**: Unified export through index.ts, maintain API encapsulation

## Code Reuse Analysis

### Existing Architecture Components to Leverage
```typescript
// Existing reusable components analysis
ComplexityVisitor      // ✅ Complete visitor pattern AST traverser
RuleRegistry          // ✅ Rule registration and management mechanism
CalculatorFactory     // ✅ IoC factory and dependency injection
BaseRule             // ✅ Rule base class providing common functionality
DetailCollector      // ✅ Detailed information collection mechanism
AsyncRuleEngine      // ✅ Async rule execution engine
PositionConverter    // ✅ Position conversion utilities
type-guards          // ✅ Type safety guard functions
```

### New Components to Create
```typescript
// New rule classes (based on existing BaseRule)
IfStatementRule       // if statement complexity rule
ForStatementRule      // for loop complexity rule
WhileStatementRule    // while loop complexity rule
ConditionalRule       // conditional expression rule
CatchClauseRule       // catch clause rule
```

## Architecture

### Current Architecture Problems
```mermaid
graph TD
    A[ComplexityCalculator 2486 lines] --> B[AST traversal logic]
    A --> C[Rule judgment logic]
    A --> D[Performance monitoring]
    A --> E[Error handling]
    A --> F[State management]
    A --> G[Cache management]
    A --> H[Detail collection]
    
    classDef problem fill:#ffcccc
    class A problem
```

### Target Refactored Architecture
```mermaid
graph TD
    subgraph "Refactored Coordinator Pattern"
        calc[ComplexityCalculator<br/>~300 lines coordinator]
        factory[CalculatorFactory<br/>IoC dependency injection]
    end
    
    subgraph "Visitor Pattern Layer"
        visitor[ComplexityVisitor<br/>AST traverser]
        base[BaseVisitor<br/>base visitor]
    end
    
    subgraph "Rule Engine Layer"
        registry[RuleRegistry<br/>rule registration manager]
        engine[AsyncRuleEngine<br/>async execution engine]
    end
    
    subgraph "Independent Rule Classes"
        if_rule[IfStatementRule]
        for_rule[ForStatementRule]
        while_rule[WhileStatementRule]
        logical_rule[LogicalOperatorRule<br/>existing]
        conditional_rule[ConditionalRule]
        catch_rule[CatchClauseRule]
        recursive_rule[RecursionRule]
    end
    
    subgraph "Support Layer"
        detail[DetailCollector<br/>detail info collection]
        cache[CacheManager<br/>smart caching]
        monitor[PerformanceMonitor<br/>performance monitoring]
    end
    
    calc --> factory
    factory --> visitor
    factory --> detail
    factory --> cache
    factory --> monitor
    
    visitor --> registry
    registry --> engine
    
    engine --> if_rule
    engine --> for_rule
    engine --> while_rule
    engine --> logical_rule
    engine --> conditional_rule
    engine --> catch_rule
    engine --> recursive_rule
    
    classDef refactored fill:#ccffcc
    classDef existing fill:#ccddff
    classDef new fill:#ffffcc
    
    class calc,factory refactored  
    class visitor,base,registry,engine,logical_rule,detail,cache,monitor existing
    class if_rule,for_rule,while_rule,conditional_rule,catch_rule,recursive_rule new
```

### Data Flow Refactoring
```mermaid
sequenceDiagram
    participant Client
    participant Calculator as ComplexityCalculator<br/>(coordinator)
    participant Factory as CalculatorFactory<br/>(IoC container)
    participant Visitor as ComplexityVisitor<br/>(visitor)
    participant Registry as RuleRegistry<br/>(rule engine)
    participant Rules as Rule Classes<br/>(rule implementation)
    
    Client->>Calculator: calculateCode(code)
    Calculator->>Factory: get dependency components
    Factory-->>Calculator: return configured components
    Calculator->>Calculator: parse AST
    Calculator->>Visitor: new ComplexityVisitor(sourceCode, detailCollector)
    Calculator->>Visitor: visitor.visit(functionNode)
    
    loop each AST node
        Visitor->>Registry: delegate complexity evaluation
        Registry->>Rules: find applicable rules
        Rules-->>Registry: return complexity result
        Registry-->>Visitor: aggregate results
    end
    
    Visitor-->>Calculator: return total complexity
    Calculator-->>Client: return analysis results
```

## Components and Interfaces

### 1. ComplexityCalculator (Refactored Coordinator)
- **Target**: Reduce from 2486 lines to ~300 lines  
- **Responsibility**: Pure process coordination, no business logic
- **Interface**: Maintain all public API backward compatibility

```typescript
export class ComplexityCalculator {
  // Maintain existing IoC architecture
  private factory: ComponentFactory;
  private options: CalculationOptions;
  
  // Simplified core method
  public async calculateCode(code: string, filePath: string): Promise<FunctionResult[]> {
    // 1. Parse AST (maintain existing implementation)
    const ast = await this.parser.parseCode(code, filePath);
    
    // 2. Get function list (maintain existing implementation)  
    const functions = FunctionFinderVisitor.find(ast);
    
    // 3. Create ComplexityVisitor for each function and calculate
    const results: FunctionResult[] = [];
    for (const func of functions) {
      const result = this.calculateFunctionComplexity(func, code);
      results.push(result);
    }
    
    return results;
  }
  
  // Simplified function complexity calculation
  private calculateFunctionComplexity(functionNode: Node, sourceCode: string): FunctionResult {
    // Create ComplexityVisitor instance
    const visitor = new ComplexityVisitor(sourceCode, this.detailCollector, this.options);
    
    // Delegate to visitor pattern
    visitor.visit(functionNode);
    
    // Return result
    return {
      name: this.parser.getFunctionName(functionNode),
      complexity: visitor.getTotalComplexity(),
      line: this.parser.getLocation(functionNode).line,
      column: this.parser.getLocation(functionNode).column,
      filePath: this.currentFilePath
    };
  }
}
```

### 2. Rule Class Refactoring Architecture

Create new rule classes based on existing `BaseRule`:

```typescript
// Basic conditional statement rule
export class IfStatementRule extends BaseRule {
  readonly id = 'if-statement';
  readonly name = 'If Statement Complexity';
  readonly priority = 400;

  canHandle(node: Node): boolean {
    return node.type === 'IfStatement';
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      // Implement if statement logic migrated from calculator.ts
      const complexity = 1 + context.nestingLevel; // Base complexity + nesting penalty
      
      return this.createComplexityResult(
        node,
        complexity,
        `If statement increases cognitive complexity by ${complexity}`,
        true, // increases nesting level
        this.generateSuggestions('if statement', complexity)
      );
    });
  }
}

// Loop statement rule
export class ForStatementRule extends BaseRule {
  readonly id = 'for-statement';
  readonly name = 'For Loop Complexity';
  readonly priority = 450;

  canHandle(node: Node): boolean {
    return ['ForStatement', 'ForInStatement', 'ForOfStatement'].includes(node.type);
  }

  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const complexity = 1 + context.nestingLevel;
      
      return this.createComplexityResult(
        node,
        complexity,
        `${node.type} increases cognitive complexity by ${complexity}`,
        true, // increases nesting level
        this.generateSuggestions('loop', complexity)
      );
    });
  }
}
```

### 3. ComplexityVisitor Enhanced Integration

Fully utilize existing ComplexityVisitor architecture:

```typescript
// ComplexityVisitor already implemented, ensure proper RuleRegistry integration
export class ComplexityVisitor extends BaseVisitor {
  // Existing implementation remains unchanged, ensure delegation to RuleRegistry
  protected visitIfStatement(node: IfStatement): void {
    // Delegate to rule engine
    const result = await this.ruleRegistry.evaluate(node, this.context);
    this.addComplexity(result.complexity);
    
    if (result.shouldIncreaseNesting) {
      this.increaseNesting();
      super.visitIfStatement(node);
      this.decreaseNesting();
    } else {
      super.visitIfStatement(node);
    }
  }
  
  // Other visit methods implement similarly...
}
```

### 4. RuleRegistry Extension

Leverage existing RuleRegistry architecture to register new rules:

```typescript
// Register new rules in default-rules.ts
export function initializeRules(quiet?: boolean): void {
  // Keep existing rules unchanged
  RuleRegistry.register('logical-operators', 'Logical operator complexity');
  
  // Register new rules
  RuleRegistry.register('if-statement', 'If statement conditional complexity');
  RuleRegistry.register('for-statement', 'For loop complexity');
  RuleRegistry.register('while-statement', 'While loop complexity');
  RuleRegistry.register('conditional-expression', 'Ternary operator complexity');
  RuleRegistry.register('catch-clause', 'Catch block error handling complexity');
  RuleRegistry.register('recursive-call', 'Recursive function call complexity');
  
  if (!quiet) {
    console.log(`Registered ${RuleRegistry.getAllRules().length} complexity rules`);
  }
}
```

## Data Models

### Maintain Existing Data Models
```typescript
// Keep existing core interfaces unchanged
interface CalculationOptions {
  enableDebugLog?: boolean;
  enableDetails?: boolean;
  quiet?: boolean;
  enableMixedLogicOperatorPenalty?: boolean;
  // ... other existing options
}

interface FunctionResult {
  name: string;
  complexity: number;
  line: number;
  column: number;
  filePath: string;
  details?: DetailStep[]; // maintain detail mode compatibility
  ignoreExemptions?: IgnoreExemption[]; // maintain exemption mechanism
}

// Rule system related (already exists in engine/types.ts)
interface RuleResult {
  ruleId: string;
  complexity: number;
  isExempted: boolean;
  shouldIncreaseNesting: boolean;
  reason: string;
  suggestions: Suggestion[];
  metadata: Record<string, any>;
  executionTime: number;
  cacheHit: boolean;
}
```

## Error Handling Strategy

### Layered Error Handling Refactoring
```typescript
// 1. Coordinator layer: simplified error handling, delegate to components
export class ComplexityCalculator {
  public async calculateCode(code: string, filePath: string): Promise<FunctionResult[]> {
    try {
      // Delegate to specific components, no complex error handling logic
      return await this.delegateToVisitor(code, filePath);
    } catch (error) {
      this.logError('Calculation failed', error, { filePath });
      return []; // graceful degradation
    }
  }
}

// 2. Rule layer: unified error handling in BaseRule
export abstract class BaseRule {
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    try {
      return await this.doEvaluate(node, context);
    } catch (error) {
      this.error(`Rule ${this.id} evaluation failed`, error);
      return this.createNonExemptionResult(node, `Error in rule evaluation: ${error.message}`);
    }
  }
}

// 3. Visitor layer: maintain existing error recovery mechanism
export class ComplexityVisitor extends BaseVisitor {
  protected visitNode(node: Node): void {
    try {
      super.visitNode(node);
    } catch (error) {
      // Single node failure doesn't affect overall analysis
      this.logError('Node visit failed', error, { nodeType: node.type });
    }
  }
}
```

## Migration Strategy

### Phased Rule Migration

```mermaid
graph TD
    A[Phase 1: Preparation] --> B[Phase 2: Rule Migration]
    B --> C[Phase 3: Cleanup Phase]
    
    subgraph "Phase 1: Infrastructure"
        A1[Ensure tests pass]
        A2[Create rule base classes]
        A3[Setup RuleRegistry]
    end
    
    subgraph "Phase 2: Migrate rules one by one"
        B1[IfStatementRule]
        B2[ForStatementRule]
        B3[WhileStatementRule]
        B4[ConditionalRule]
        B5[CatchClauseRule]
        B6[RecursiveRule]
    end
    
    subgraph "Phase 3: Architecture Cleanup"
        C1[Delete old logic]
        C2[Simplify calculator.ts]
        C3[Final test verification]
    end
    
    A --> A1
    A1 --> A2
    A2 --> A3
    
    B --> B1
    B1 -->|tests pass| B2
    B2 -->|tests pass| B3
    B3 -->|tests pass| B4
    B4 -->|tests pass| B5
    B5 -->|tests pass| B6
    
    C --> C1
    C1 --> C2
    C2 --> C3
```

### Specific Migration Steps

1. **Establish Safety Net**: Ensure all existing tests pass
2. **Create Rule Framework**: Create new rule class skeletons based on BaseRule
3. **Migrate Rules One by One**: Migrate rule logic from simple to complex
4. **Incremental Verification**: Run complete test suite after migrating each rule
5. **Architecture Cleanup**: Delete old logic in calculator.ts
6. **Final Verification**: Ensure all functionality and performance metrics meet standards

## Testing Strategy

### Leverage Existing Test Infrastructure
```typescript
// 1. Existing tests as safety net
describe('Calculator Refactoring', () => {
  // Keep all existing tests unchanged, ensure refactoring doesn't break functionality
  test('basic complexity calculation', async () => {
    const calculator = new ComplexityCalculator();
    const results = await calculator.calculateCode(simpleCode, 'test.ts');
    expect(results[0]?.complexity).toBe(0);
  });
  
  test('nested complexity calculation', async () => {
    const calculator = new ComplexityCalculator();
    const results = await calculator.calculateCode(nestedCode, 'test.ts');
    expect(results[0]?.complexity).toBe(6); // ensure calculation results unchanged
  });
});

// 2. Unit tests for new rule classes
describe('IfStatementRule', () => {
  let rule: IfStatementRule;
  let context: AnalysisContext;
  
  beforeEach(() => {
    rule = new IfStatementRule();
    context = createMockContext();
  });
  
  test('should handle if statements', () => {
    const ifNode = createIfNode();
    expect(rule.canHandle(ifNode)).toBe(true);
  });
  
  test('should calculate correct complexity', async () => {
    const ifNode = createIfNode();
    const result = await rule.evaluate(ifNode, context);
    expect(result.complexity).toBe(1 + context.nestingLevel);
  });
});

// 3. Integration tests
describe('Refactored Architecture Integration', () => {
  test('ComplexityVisitor delegates to RuleRegistry', async () => {
    const visitor = new ComplexityVisitor(sourceCode, detailCollector, options);
    const spy = jest.spyOn(RuleRegistry, 'evaluate');
    
    visitor.visit(astNode);
    
    expect(spy).toHaveBeenCalled();
  });
});
```

### Performance Regression Testing
```typescript
// Ensure refactoring doesn't affect performance
describe('Performance Regression Tests', () => {
  test('large project analysis should complete within 30 seconds', async () => {
    const startTime = performance.now();
    const results = await calculator.analyzeFiles(largeProjectFiles);
    const duration = performance.now() - startTime;
    
    expect(duration).toBeLessThan(30000); // 30 second threshold
    expect(results.size).toBeGreaterThan(0);
  });
  
  test('memory usage should remain stable', async () => {
    const initialMemory = process.memoryUsage().heapUsed;
    await calculator.analyzeFiles(mediumProjectFiles);
    const finalMemory = process.memoryUsage().heapUsed;
    
    const memoryIncrease = (finalMemory - initialMemory) / 1024 / 1024; // MB
    expect(memoryIncrease).toBeLessThan(100); // 100MB threshold
  });
});
```

## Implementation Priorities

### High Priority Rules (Core Complexity Contributors)
1. **IfStatementRule** - Conditional statements, most common complexity source
2. **ForStatementRule** - Loop statements, high complexity contribution
3. **WhileStatementRule** - while loops, similar to for loops
4. **LogicalOperatorRule** - Already exists, need to verify integration

### Medium Priority Rules
5. **ConditionalRule** - Ternary operators
6. **CatchClauseRule** - Exception handling

### Low Priority Rules
7. **RecursiveRule** - Recursive calls, relatively rare but important

### Cleanup Phase
8. Delete old implementation logic in calculator.ts
9. Simplify error handling and state management
10. Final architecture verification and documentation updates

---

This design fully leverages existing IoC architecture, visitor pattern, and rule engine to achieve minimal architectural changes while reaching the goal of responsibility separation. The refactored system will be more modular, testable, and maintainable.