# Context Line Count Feature Tasks

## Task Overview

实现 `--context-lines` 参数功能，按照三个阶段进行：CLI集成、参数传递、格式化器适配。遵循现有架构模式，确保向后兼容性和充分的测试覆盖。

## Steering Document Compliance

### Structure (structure.md)
- 遵循CLI模块职责分工，在 `src/cli/` 中处理参数定义
- 格式化模块在 `src/formatters/` 中接收配置参数
- 核心类型定义保持在 `src/core/types.ts`
- 测试按模块组织在对应的 `src/__test__/` 目录

### Tech (tech.md)  
- 使用Node.js兼容API，避免Bun特定功能
- 遵循异步优先和依赖注入模式
- 实现分层错误处理和用户友好提示
- 保持向后兼容性和性能优化

## Tasks

- [ ] **Phase 1: CLI Parameter Integration**

- [ ] 1.1 Add CLI parameter definition
  - 在 `src/cli/index.ts` 中添加 `--context-lines` 参数
  - 实现参数解析和基础验证逻辑
  - 添加参数描述和默认值 (2)
  - 处理非法输入和边界情况
  - _Files: src/cli/index.ts_
  - _Requirements: FR-1_

- [ ] 1.2 Enhance parameter validation  
  - 修改 `src/utils/concurrent-validation-service.ts` 中的验证逻辑
  - 添加参数范围检查 (0-20)
  - 完善与 `--show-context` 的关联验证
  - 添加性能警告提示 (>20行)
  - _Files: src/utils/concurrent-validation-service.ts_
  - _Requirements: FR-3_

- [ ] 1.3 Write CLI parameter tests
  - 创建 `src/__test__/cli/context-lines-parameter.test.ts`
  - 测试参数解析正确性
  - 测试验证规则和错误处理
  - 测试边界值和异常情况
  - _Files: src/__test__/cli/context-lines-parameter.test.ts_
  - _Requirements: Testing Strategy_

- [ ] **Phase 2: Parameter Propagation**

- [ ] 2.1 Implement parameter passing mechanism
  - 修改 `src/cli/commands.ts` 中的命令处理逻辑
  - 创建配置对象传递 `contextLines` 参数
  - 确保参数正确传递到格式化器
  - 处理参数默认值和可选性
  - _Files: src/cli/commands.ts_
  - _Requirements: FR-2_

- [ ] 2.2 Update CLI options interface
  - 在相关类型定义中添加 `contextLines?: number`
  - 更新命令处理器的类型签名
  - 确保类型安全的参数传递
  - _Files: src/cli/commands.ts, type definitions_
  - _Requirements: FR-2_

- [ ] 2.3 Write parameter propagation tests
  - 创建 `src/__test__/cli/parameter-propagation.test.ts`
  - 测试参数从CLI到格式化器的传递链
  - 测试默认值处理和类型转换
  - 测试多种参数组合场景
  - _Files: src/__test__/cli/parameter-propagation.test.ts_
  - _Requirements: Integration Testing_

- [ ] **Phase 3: Formatter Updates**

- [ ] 3.1 Update Text formatter
  - 修改 `src/formatters/text.ts:253-256` 硬编码部分
  - 实现从配置对象读取 `linesAbove/linesBelow`
  - 保持默认值向后兼容 (2行)
  - 确保类型安全和错误处理
  - _Files: src/formatters/text.ts_
  - _Requirements: FR-4_

- [ ] 3.2 Update JSON formatter
  - 修改 `src/formatters/json.ts:406-407, 417-418` 硬编码部分
  - 实现从配置对象读取上下文行数
  - 保持JSON输出格式一致性
  - 处理配置缺失的情况
  - _Files: src/formatters/json.ts_
  - _Requirements: FR-4_

- [ ] 3.3 Write formatter unit tests
  - 创建 `src/__test__/formatters/context-lines.test.ts`
  - 测试Text和JSON格式化器的配置接收
  - 测试不同上下文行数的输出结果
  - 测试向后兼容性
  - _Files: src/__test__/formatters/context-lines.test.ts_
  - _Requirements: Unit Testing_

- [ ] **Phase 4: Integration & Testing**

- [ ] 4.1 Write end-to-end integration tests
  - 创建 `src/__test__/e2e/context-lines-e2e.test.ts`
  - 测试完整的CLI到输出的流程
  - 测试各种参数组合场景
  - 测试错误处理和用户体验
  - _Files: src/__test__/e2e/context-lines-e2e.test.ts_
  - _Requirements: End-to-End Testing_

- [ ] 4.2 Performance and memory testing
  - 创建 `src/__test__/performance/context-lines-performance.test.ts`
  - 测试不同上下文行数的性能影响
  - 测试大文件处理的内存使用
  - 建立性能基准和回归测试
  - _Files: src/__test__/performance/context-lines-performance.test.ts_
  - _Requirements: Performance Considerations_

- [ ] 4.3 Backward compatibility validation
  - 创建 `src/__test__/integration/backward-compatibility.test.ts`
  - 测试不使用新参数时的行为不变
  - 测试现有脚本和配置的兼容性
  - 验证默认值和输出格式一致性
  - _Files: src/__test__/integration/backward-compatibility.test.ts_
  - _Requirements: NFR-1_

- [ ] **Phase 5: Documentation & Polish**

- [ ] 5.1 Update documentation consistency
  - 统一代码和文档中的默认值 (2行)
  - 更新 `docs/detail-context-usage-guide.md`
  - 添加使用示例和最佳实践
  - 更新CLI帮助文档
  - _Files: docs/detail-context-usage-guide.md, CLI help_
  - _Requirements: Documentation Updates_

- [ ] 5.2 Code quality and refactoring
  - 代码review和重构优化
  - 确保错误处理的一致性
  - 添加必要的代码注释
  - 运行linting和类型检查
  - _Files: All modified files_
  - _Requirements: Code Quality_

- [ ] 5.3 Final validation and testing
  - 运行完整测试套件验证
  - 执行性能基准测试
  - 验证所有需求完成情况
  - 准备发布和部署文档
  - _Files: All test files_
  - _Requirements: All acceptance criteria_

## Completion Criteria

### Functional Requirements ✅
- `--context-lines N` 参数正常工作 (N ∈ [0,20])
- 与 `--show-context`/`--show-all-context` 正确组合
- 不使用参数时保持默认行为 (2行)
- 错误场景有清晰友好的提示

### Quality Requirements ✅
- 单元测试覆盖率 ≥ 90%
- 集成测试验证端到端功能
- 性能测试确认无显著退化
- 向后兼容性测试通过

## Risk Mitigation

### 默认值不一致风险
- **Task 5.1** 专门处理文档和代码的默认值统一
- 在所有相关文件中一致使用2行作为默认值

### 多格式化器同步风险  
- **Tasks 3.1-3.2** 并行处理Text和JSON格式化器
- **Task 3.3** 统一测试两种格式化器的行为一致性

### 向后兼容性风险
- **Task 4.3** 专门验证向后兼容性
- 每个阶段都保持默认值和现有行为不变