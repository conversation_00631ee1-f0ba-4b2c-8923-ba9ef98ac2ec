# Context Line Count Feature Design

## Overview

本设计实现 `--context-lines` 参数功能，允许用户自定义代码上下文显示的行数。功能将集成到现有的CLI参数系统和格式化器架构中，保持向后兼容性的同时提供更大的灵活性。

## Steering Document Alignment

### Technical Standards (tech.md)
- **Node.js兼容性**: 使用标准Node.js API，避免Bun特定功能
- **模块化设计**: 遵循单一职责原则，通过依赖注入传递参数
- **异步优先**: 保持现有异步处理模式不变
- **错误处理**: 分层错误处理，用户友好的错误信息

### Project Structure (structure.md)
- **CLI模块** (`src/cli/`): 添加参数定义和验证
- **格式化模块** (`src/formatters/`): 修改Text和JSON格式化器
- **核心类型** (`src/core/types.ts`): 扩展现有接口
- **测试覆盖**: 遵循测试目录结构，确保充分覆盖

## Architecture

```mermaid
graph TD
    A[CLI Parser] -->|解析--context-lines| B[Validation Service]
    B -->|验证参数| C[Command Handler]
    C -->|传递配置| D[Format Options]
    D -->|配置格式化器| E[Text Formatter]
    D -->|配置格式化器| F[JSON Formatter]
    E -->|生成上下文| G[Code Frame Generator]
    F -->|生成上下文| G
    G -->|使用linesAbove/Below| H[Output]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style G fill:#e8f5e8
```

## Components and Interfaces

### CLI Parameter Extension
- **Purpose:** 在CLI中添加 `--context-lines` 参数定义
- **Location:** `src/cli/index.ts`
- **Interface:** Commander.js option definition
- **Dependencies:** 现有CLI框架

```typescript
.option('--context-lines <number>', '上下文显示的行数 (默认: 2)', 
  (value) => parseInt(value, 10), 2)
```

### Validation Service Enhancement
- **Purpose:** 增强现有的参数验证逻辑
- **Location:** `src/utils/concurrent-validation-service.ts`
- **Interface:** 扩展现有验证函数
- **Dependencies:** 现有验证框架

### Format Options Propagation
- **Purpose:** 创建参数传递机制，将CLI选项传递到格式化器
- **Location:** `src/cli/commands.ts`
- **Interface:** 配置对象传递
- **Dependencies:** CLI handlers, Formatters

### Text Formatter Update
- **Purpose:** 修改文本格式化器支持动态上下文行数
- **Location:** `src/formatters/text.ts:253-256`
- **Interface:** 接收 `CodeFrameOptions` 配置
- **Dependencies:** Code Frame Generator

### JSON Formatter Update
- **Purpose:** 修改JSON格式化器支持动态上下文行数
- **Location:** `src/formatters/json.ts:406-407, 417-418`
- **Interface:** 接收 `CodeFrameOptions` 配置
- **Dependencies:** Code Frame Generator

## Data Models

### Extended CLI Options
```typescript
interface CLIOptions {
  // 现有选项...
  contextLines?: number;        // 新增：上下文行数
  showContext?: boolean;        // 现有
  showAllContext?: boolean;     // 现有
  details?: boolean;            // 现有
}
```

### Enhanced CodeFrameOptions
```typescript
// src/core/types.ts - 现有接口，无需修改
export interface CodeFrameOptions {
  highlightCode?: boolean;
  linesAbove?: number;      // 将从CLI参数动态设置
  linesBelined?: number;     // 将从CLI参数动态设置
  forceColor?: boolean;
}
```

### Format Configuration
```typescript
interface FormatConfig {
  contextOptions: CodeFrameOptions;
  // 其他格式化选项...
}
```

## Error Handling

### Error Scenarios

1. **Invalid Context Lines Value**
   - **Scenario:** 用户提供非数字或负数值
   - **Handling:** CLI参数解析时验证，提供清晰错误信息
   - **User Impact:** 显示使用帮助和错误原因

2. **Context Lines Without Context Display**
   - **Scenario:** 用户指定 `--context-lines` 但未启用 `--show-context`
   - **Handling:** 在验证服务中检查，发出警告但不阻止执行
   - **User Impact:** 显示警告信息，说明参数被忽略

3. **Excessive Context Lines**
   - **Scenario:** 用户指定过大的上下文行数（>20）
   - **Handling:** 发出警告但允许执行，提醒可能的性能影响
   - **User Impact:** 警告信息，但继续执行

## Implementation Details

### Phase 1: CLI Parameter Integration

```typescript
// src/cli/index.ts 中添加
.option('--context-lines <number>', '上下文显示的行数 (默认: 2)', 
  (value) => {
    const num = parseInt(value, 10);
    if (isNaN(num) || num < 0) {
      throw new Error('--context-lines 必须为non-negative integer');
    }
    if (num > 20) {
      console.warn('警告: 过大的上下文行数可能影响性能');
    }
    return num;
  }, 2)
```

### Phase 2: Parameter Propagation

```typescript
// src/cli/commands.ts 中修改
async function handleAnalysis(options: CLIOptions) {
  const formatConfig = {
    contextOptions: {
      linesAbove: options.contextLines ?? 2,
      linesBelow: options.contextLines ?? 2,
      highlightCode: true,
      forceColor: !options.noColors
    }
  };
  
  // 传递给格式化器...
}
```

### Phase 3: Formatter Updates

```typescript
// src/formatters/text.ts 中修改
private generateContextFrame(/* ... */, options: CodeFrameOptions) {
  const frameOptions: CodeFrameOptions = {
    highlightCode: options.highlightCode ?? true,
    linesAbove: options.linesAbove ?? 2,  // 从参数获取
    linesBelow: options.linesBelow ?? 2,   // 从参数获取
    forceColor: options.forceColor ?? false,
  };
  // ...
}
```

## Testing Strategy

### Unit Testing
- **CLI Parameter Parsing**: 测试参数解析和验证逻辑
- **Validation Service**: 测试增强的验证规则
- **Formatter Updates**: 测试格式化器接收和使用新配置

### Integration Testing
- **CLI to Formatter Flow**: 测试端到端参数传递链
- **Multiple Format Support**: 测试Text和JSON格式都正确支持新参数
- **Error Handling Integration**: 测试各种错误场景的处理

### End-to-End Testing
- **User Workflows**: 测试常见使用场景
  - `--details --show-context --context-lines 5`
  - `--context-lines 0` (最小上下文)
  - `--context-lines 10` (扩展上下文)
- **Error Scenarios**: 测试用户错误使用的处理
- **Backward Compatibility**: 确保不使用新参数时行为不变

## Performance Considerations

### Memory Impact
- 更多上下文行数会增加内存使用
- Code Frame Generator已有内存管理机制
- 建议对超大文件和高上下文行数组合进行性能测试

### Processing Speed
- 上下文行数主要影响格式化阶段，不影响核心分析
- 现有的缓存机制仍然有效
- 预期性能影响minimal

## Migration Strategy

### Backward Compatibility
- 默认值保持2行，确保现有脚本不受影响
- 新参数为可选，现有用户工作流无需修改
- 现有的 `CodeFrameOptions` 接口保持不变

### Documentation Updates
- 更新CLI帮助文档
- 统一代码和文档中的默认值表述
- 添加使用示例和最佳实践建议

## Security Considerations

- 输入验证防止极大数值导致的性能问题
- 无安全敏感数据处理
- 遵循现有的安全实践

## Future Enhancements

- 支持配置文件中设置默认上下文行数
- 支持对不同复杂度级别设置不同的上下文行数
- 支持 `linesAbove` 和 `linesBelow` 分别配置