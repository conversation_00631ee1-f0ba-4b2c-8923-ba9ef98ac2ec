# Context Line Count Feature Requirements

## 功能概述

添加 `--context-lines` 命令行参数，允许用户控制在使用 `--details --show-context` 时显示的代码上下文行数。当前默认为上下各2行，用户希望能够自定义这个数值。

## 用户需求分析

### 核心需求
- 用户希望通过传参控制代码上下文显示的额外行数
- 现在默认是上下各2行，需要支持用户自定义
- 参数只在启用上下文显示时有效

### 使用场景
1. **快速查看模式**: 用户希望看到更少的上下文（1行或0行）以减少输出
2. **详细分析模式**: 用户希望看到更多上下文（5-10行）以便更好地理解代码逻辑
3. **代码审查场景**: 不同项目可能需要不同的上下文行数标准

## 现有系统分析

### 当前实现状态
根据代码库分析，发现以下情况：

1. **已有参数验证**: 在 `src/utils/concurrent-validation-service.ts:356` 中已存在对 `--context-lines` 参数的验证逻辑
2. **已有文档**: 在 `docs/detail-context-usage-guide.md:240` 中已经记录了 `--context-lines` 参数，默认值为3
3. **缺失CLI定义**: 在 `src/cli/index.ts` 中未找到 `--context-lines` 参数的定义
4. **现有默认值**: 
   - 代码中使用的默认值为2（`src/utils/code-frame-generator.ts:90-91`）
   - 文档中记录的默认值为3

### 核心接口分析

#### CodeFrameOptions 接口
```typescript
// src/core/types.ts:149
export interface CodeFrameOptions {
  linesAbove?: number;  // 上方显示行数
  linesBelow?: number;  // 下方显示行数
}
```

#### 当前使用位置
1. **Text Formatter** (`src/formatters/text.ts:253-256`): 硬编码为2行
2. **JSON Formatter** (`src/formatters/json.ts:406-407, 417-418`): 硬编码为2行  
3. **Code Frame Generator** (`src/utils/code-frame-generator.ts:90-91`): 默认值为2行

## 技术要求

### 功能要求

#### FR-1: CLI参数定义
- **需求**: 在 `src/cli/index.ts` 中添加 `--context-lines` 参数定义
- **参数格式**: `--context-lines <number>`
- **默认值**: 2（保持向后兼容）
- **验证**: 必须为非负整数，建议最大值为20

#### FR-2: 参数传递机制
- **需求**: 将CLI参数传递到代码框架生成系统
- **范围**: 影响Text和JSON格式化器
- **接口**: 通过现有的 `CodeFrameOptions` 接口传递

#### FR-3: 参数验证增强
- **需求**: 完善现有的参数验证逻辑
- **位置**: `src/utils/concurrent-validation-service.ts`
- **验证规则**:
  - 只能在使用 `--show-context` 或 `--show-all-context` 时使用
  - 必须为非负整数
  - 建议范围: 0-20行

#### FR-4: 格式化器适配
- **需求**: 修改格式化器使用动态上下文行数
- **影响范围**:
  - `src/formatters/text.ts`
  - `src/formatters/json.ts`
- **变更**: 从硬编码改为从配置读取

### 非功能要求

#### NFR-1: 向后兼容性
- 不使用 `--context-lines` 参数时，行为保持不变（默认2行）
- 现有配置文件和脚本不受影响

#### NFR-2: 性能要求
- 不同上下文行数设置不应显著影响分析性能
- 大文件处理时，过多上下文行数应有合理的内存管理

#### NFR-3: 用户体验
- 参数名称清晰易懂 (`--context-lines`)
- 错误提示信息友好
- 与现有参数体系保持一致

## 实现优先级

### P1 (高优先级)
1. CLI参数定义和基础验证
2. Text格式化器适配
3. 参数传递机制

### P2 (中优先级) 
1. JSON格式化器适配
2. 参数验证增强
3. 错误处理优化

### P3 (低优先级)
1. 文档更新和统一
2. 配置文件支持
3. 测试用例完善

## 约束条件

### 技术约束
- 必须保持Node.js兼容性
- 不能破坏现有的 `CodeFrameOptions` 接口
- 需要考虑内存使用优化

### 业务约束
- 必须保持向后兼容性
- 不能影响现有用户的工作流
- 应该与现有的上下文显示参数体系保持一致

## 成功标准

### 功能验收标准
1. ✅ `--context-lines N` 参数正常工作，其中N为0-20的整数
2. ✅ 与 `--show-context` 和 `--show-all-context` 正确组合使用
3. ✅ 不使用该参数时默认行为不变
4. ✅ 错误情况下有清晰的提示信息

### 质量验收标准
1. ✅ 单元测试覆盖率 ≥ 90%
2. ✅ 集成测试验证端到端功能
3. ✅ 性能测试确认无显著性能退化
4. ✅ 向后兼容性测试通过

## 风险评估

### 技术风险
- **中等风险**: 文档和代码中默认值不一致，需要统一
- **低风险**: 多个格式化器需要同步修改，可能遗漏

### 业务风险  
- **低风险**: 功能相对独立，不太可能影响核心分析功能

## 结论

这是一个相对简单但很实用的功能增强，主要涉及CLI参数处理和格式化器的适配。由于已有部分基础设施（参数验证逻辑、文档），实现难度较低，但需要注意统一默认值和保持向后兼容性。