# 认知复杂度CLI工具 - 需求文档

## 简介

基于您提供的技术方案白皮书，本需求文档定义了一个高性能的认知复杂度分析CLI工具，用于提升前端团队的代码质量和开发效能。该工具采用SWC作为解析引擎，严格遵循SonarSource官方白皮书的认知复杂度计算标准。

## 产品愿景对齐

该工具旨在解决前端团队在代码质量管理中的核心痛点：
- 提供客观的代码审查数据支持
- 建立自动化的质量门禁机制
- 实现团队代码复杂度趋势追踪
- 支持遗留项目的平滑质量治理

## 需求

### 需求1：核心认知复杂度计算引擎

**用户故事：** 作为一名开发者，我希望工具能准确计算TypeScript/JavaScript代码的认知复杂度，以便我了解代码的可读性和维护成本。

#### 验收标准
1. WHEN 系统解析包含if语句的代码 THEN 系统应为每个if语句计算基础分1分加上嵌套层级分数
2. WHEN 系统解析包含for/while循环的代码 THEN 系统应为每个循环结构计算基础分1分加上嵌套层级分数
3. WHEN 系统解析包含switch语句的代码 THEN 系统应为switch语句计算基础分1分加上嵌套层级分数
4. WHEN 系统解析包含逻辑运算符(&&,||)的代码 THEN 系统应为每个逻辑运算符增加1分复杂度
5. WHEN 系统解析包含三元运算符的代码 THEN 系统应为三元运算符计算基础分1分加上嵌套层级分数
6. WHEN 系统解析包含递归调用的代码 THEN 系统应为递归调用增加1分复杂度
7. WHEN 系统解析包含catch子句的代码 THEN 系统应为每个catch子句计算基础分1分加上嵌套层级分数
8. WHEN 系统解析嵌套控制结构 THEN 系统应根据嵌套深度为内层结构增加相应的嵌套惩罚分数
9. WHEN 系统解析可选链操作符(?.) THEN 系统不应增加任何复杂度分数
10. WHEN 系统解析用于默认值赋值的||或??操作符 THEN 系统不应增加复杂度分数

### 需求2：命令行界面

**用户故事：** 作为一名开发者，我希望通过命令行工具分析代码复杂度，以便在开发过程中快速获得反馈。

#### 验收标准
1. WHEN 用户运行`cognitive-complexity [paths...]`命令 THEN 系统应分析指定路径下的TypeScript/JavaScript文件
2. WHEN 用户使用`--fail-on <threshold>`选项 THEN 系统应在有函数超过阈值时以非零状态码退出
3. WHEN 用户使用`--details`选项 THEN 系统应显示每个函数的详细复杂度信息
4. WHEN 用户使用`--format json`选项 THEN 系统应输出JSON格式的分析结果
5. WHEN 用户使用`--min <threshold>`选项 THEN 系统应只显示复杂度高于指定阈值的结果
6. WHEN 用户使用`--sort complexity`选项 THEN 系统应按复杂度从高到低排序显示结果
7. WHEN 用户运行`--help`命令 THEN 系统应显示完整的使用说明和选项列表
8. WHEN 用户运行`--version`命令 THEN 系统应显示工具的版本信息

### 需求3：配置文件支持

**用户故事：** 作为一名技术负责人，我希望通过配置文件统一管理团队的代码复杂度规则，以便标准化代码质量要求。

#### 验收标准
1. WHEN 系统启动时 THEN 系统应自动查找并加载cognitive.config.json配置文件
2. WHEN 用户使用`--config <path>`选项 THEN 系统应加载指定路径的配置文件
3. WHEN 配置文件包含failOnComplexity设置 THEN 系统应使用该值作为质量门禁阈值
4. WHEN 配置文件包含exclude模式 THEN 系统应排除匹配模式的文件
5. WHEN 配置文件包含severityMapping设置 THEN 系统应根据阈值显示相应的严重性级别
6. WHEN 配置文件包含report设置 THEN 系统应将结果输出到指定的json或html文件
7. IF 配置文件不存在 THEN 系统应使用默认配置继续执行

### 需求4：质量基线管理

**用户故事：** 作为一名技术负责人，我希望为遗留代码建立质量基线，以便在不影响现有代码的情况下对新代码实施质量控制。

#### 验收标准
1. WHEN 用户运行`--create-baseline`命令 THEN 系统应扫描项目并将所有超标函数记录到cognitive-baseline.json文件
2. WHEN 系统在CI环境中运行且存在基线文件 THEN 系统应只对新出现或恶化的超标函数报告问题
3. WHEN 用户运行`--update-baseline`命令 THEN 系统应更新基线文件，移除已修复的函数记录
4. WHEN 基线文件中的函数复杂度降低 THEN 系统应允许该改进通过质量检查
5. WHEN 基线文件中的函数复杂度增加 THEN 系统应报告该恶化并触发质量门禁

### 需求5：代码内豁免机制

**用户故事：** 作为一名开发者，我希望能够对特定的复杂代码片段进行豁免，以便在无法避免复杂性的场景下仍能通过质量检查。

#### 验收标准
1. WHEN 代码中包含`// cognitive-complexity-ignore-next-line`注释 THEN 系统应忽略下一行代码的复杂度计算
2. WHEN 代码中包含豁免注释 THEN 系统应在报告中标记该豁免的使用
3. WHEN 豁免注释格式不正确 THEN 系统应忽略该注释并正常计算复杂度

### 需求6：输出格式和报告

**用户故事：** 作为一名开发者和技术负责人，我希望获得多种格式的分析报告，以便在不同场景下使用这些数据。

#### 验收标准
1. WHEN 系统以text格式输出 THEN 应显示文件路径、总复杂度和函数级别的详细信息
2. WHEN 系统以json格式输出 THEN 应包含summary统计信息和详细的results数组
3. WHEN 用户使用`--output-dir`选项 THEN 系统应将报告文件保存到指定目录
4. WHEN 生成HTML报告 THEN 应包含可视化的复杂度展示和交互功能
5. WHEN 质量门禁触发 THEN 系统应输出明确的错误信息，包括文件路径、行号、函数名和具体复杂度值

## 非功能性需求

### 性能要求
- 系统应能在1秒内分析包含1000个函数的中型项目
- 系统应支持通过worker_threads并行处理多个文件
- 系统应支持增量扫描，仅分析变更的文件

### 安全要求
- 系统不应执行或评估用户代码，仅进行静态分析
- 系统应安全处理文件路径，防止路径遍历攻击

### 可靠性要求
- 系统应能处理语法错误的TypeScript/JavaScript文件而不崩溃
- 系统应在遇到不支持的语法结构时给出明确提示

### 可用性要求
- 终端输出应具有良好的可读性，支持颜色编码
- 错误信息应提供具体的修复建议
- 工具应提供详细的使用文档和示例