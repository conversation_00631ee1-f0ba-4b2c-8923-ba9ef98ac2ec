# Visitor Pattern 架构重构 - 设计文档

## 概览

本设计文档描述了如何将现有的认知复杂度分析器重构为基于访问者模式的架构。重构的目标是通过分离职责、引入标准设计模式和实现结构化的位置修正来提升代码的可维护性、可扩展性和可持续发展性。

## 指导文档对齐

### 技术标准 (tech.md)
- **模块化设计**: 遵循单一职责原则，每个模块专注特定功能域
- **依赖注入**: 通过构造函数注入依赖，便于测试和扩展
- **Node.js 兼容性**: 继续使用 @swc/core 标准库，确保生产环境兼容性
- **异步优先**: 保持现有的异步处理模式
- **错误处理**: 实现分层错误处理和优雅降级

### 项目结构 (structure.md)
- **核心模块位置**: 新的 Visitor 类将放置在 `src/core/` 目录
- **命名约定**: 类名使用 PascalCase（如 `BaseVisitor`, `ComplexityVisitor`）
- **文件组织**: 每个 Visitor 独立文件，通过 index.ts 统一导出
- **测试结构**: 相应测试放置在 `src/__test__/core/` 目录

## 架构

本重构采用"分层修正架构"，将位置修正逻辑分为两个层次：

```mermaid
graph TD
    A[CLI Commands] --> B[ComplexityCalculator]
    B --> C[ASTParser - parseFile/parseCode]
    B --> D[FunctionFinderVisitor.find]
    B --> E[ComplexityVisitor - per function]
    
    F[BaseVisitor] --> G[parentStack 管理]
    F --> H[getParent 方法]
    
    D --> F
    E --> F
    
    I[结构性修正层] --> J[基于 AST 父节点的 span 回退]
    E --> I
    
    K[表现层修正层] --> L[PositionConverter - 纯位置转换]
    I --> K
    
    style I fill:#e1f5fe
    style K fill:#f3e5f5
```

## 组件和接口

### BaseVisitor
- **目的**: 提供访问者模式的基础实现，管理父节点栈
- **接口**:
  ```typescript
  export abstract class BaseVisitor extends Visitor {
    protected parentStack: Node[]
    protected getParent(): Node | undefined
    public visit<T extends Node>(node: T): T
  }
  ```
- **依赖**: @swc/core/Visitor

### FunctionFinderVisitor
- **目的**: 专门负责在 AST 中查找所有函数类型节点
- **接口**:
  ```typescript
  export class FunctionFinderVisitor extends BaseVisitor {
    public static find(ast: Module): Node[]
    public visitFunctionDeclaration(node: FunctionDeclaration): FunctionDeclaration
    public visitMethodDefinition(node: MethodDefinition): MethodDefinition
    public visitVariableDeclarator(node: VariableDeclarator): VariableDeclarator
  }
  ```
- **依赖**: BaseVisitor

### ComplexityVisitor
- **目的**: 为单个函数计算认知复杂度，实现结构化 span 修正
- **接口**:
  ```typescript
  export class ComplexityVisitor extends BaseVisitor {
    constructor(sourceCode: string, detailCollector?: DetailCollector)
    public getTotalComplexity(): number
    public visitIfStatement(node: IfStatement): IfStatement
    public visitWhileStatement(node: WhileStatement): WhileStatement
    // ... 其他 visit 方法
    private validateSpan(node: Node): number // 返回有效的 span 位置
  }
  ```
- **依赖**: BaseVisitor, DetailCollector, PositionConverter

### 简化后的 ASTParser
- **目的**: 专注于代码解析和基本信息提取
- **接口**:
  ```typescript
  export class ASTParser {
    public async parseCode(code: string, filePath: string): Promise<Module>
    public async parseFile(filePath: string): Promise<Module>
    public getFunctionName(node: Node): string
    public findIgnoreExemptions(sourceCode: string): IgnoreExemption[]
    // 移除: findFunctions, getLineFromSpan, findNearestValidCodeLine 等方法
  }
  ```
- **依赖**: @swc/core, fs/promises

### 简化后的 PositionConverter
- **目的**: 纯粹的位置转换，不包含修正逻辑
- **接口**:
  ```typescript
  export class PositionConverter {
    public static spanToPosition(sourceCode: string, spanStart: number): Position
    private static getCachedLineMap(sourceCode: string): number[]
    private static buildLineMap(sourceCode: string): number[]
    // 移除: findLastValidCodeLine, validateAndCorrectLineIndex 等方法
  }
  ```
- **依赖**: 无外部依赖

### 更新后的 ComplexityCalculator
- **目的**: 协调整个分析流程，使用新的 Visitor 架构
- **接口**:
  ```typescript
  export class ComplexityCalculator {
    public async calculate(ast: Module): Promise<FunctionResult[]>
    private calculateFunctionComplexity(func: Node, sourceCode: string): FunctionResult
    // 移除: visitNodeWithNesting, visitChildrenWithDepth 等递归方法
  }
  ```
- **依赖**: ASTParser, FunctionFinderVisitor, ComplexityVisitor

## 数据模型

### Span 修正策略
```typescript
interface SpanValidationResult {
  isValid: boolean;
  correctedSpan: number;
  correctionMethod: 'original' | 'parent' | 'fallback';
}
```

### 访问者上下文
```typescript
interface VisitorContext {
  sourceCode: string;
  currentFunction: Node;
  totalComplexity: number;
  nestingLevel: number;
}
```

## 错误处理

### 错误场景
1. **无效 Span 处理**
   - **处理策略**: 优先使用父节点 span，最后回退到默认位置
   - **用户影响**: 位置信息更准确，减少错误报告

2. **AST 遍历异常**
   - **处理策略**: 在 BaseVisitor 中捕获和记录异常，继续处理其他节点
   - **用户影响**: 部分节点失败不影响整体分析

3. **Visitor 实例化失败**
   - **处理策略**: 回退到安全的默认计算方式
   - **用户影响**: 提供降级的分析结果

## 实现步骤

### 第一阶段：基础设施
1. 创建 `BaseVisitor` 类 (`src/core/base-visitor.ts`)
2. 创建 `FunctionFinderVisitor` 类 (`src/core/function-finder-visitor.ts`)
3. 更新单元测试验证基础功能

### 第二阶段：复杂度计算重构
1. 创建 `ComplexityVisitor` 类 (`src/core/complexity-visitor.ts`)
2. 实现结构化 span 修正逻辑
3. 重构 `ComplexityCalculator` 使用新的 Visitor

### 第三阶段：简化现有组件
1. 简化 `ASTParser` 职责
2. 简化 `PositionConverter` 职责
3. 更新 CLI 入口点

### 第四阶段：测试和验证
1. 确保所有现有测试通过
2. 添加新的 Visitor 专项测试
3. 性能基准测试对比

## 测试策略

### 单元测试
- **BaseVisitor**: 测试父节点栈管理和遍历逻辑
- **FunctionFinderVisitor**: 测试各种函数类型的识别
- **ComplexityVisitor**: 测试复杂度计算和 span 修正
- **简化后的组件**: 验证职责简化后的功能正确性

### 集成测试
- **端到端分析流程**: 从文件输入到结果输出的完整流程
- **错误恢复**: 测试各种异常情况下的行为
- **性能对比**: 与重构前的性能对比

### 回归测试
- **现有测试套件**: 确保所有现有功能测试通过
- **输出一致性**: 验证分析结果与重构前一致
- **API 兼容性**: 确保公共 API 接口保持不变

## 迁移计划

### 向后兼容性保证
1. 保持 `ComplexityCalculator` 的公共 API 不变
2. 保持 CLI 命令和参数不变
3. 保持输出格式和内容不变

### 分阶段迁移
1. **并行实现**: 新旧架构并行存在，通过特性标志切换
2. **逐步测试**: 在测试环境中验证新架构
3. **平滑切换**: 经过充分验证后完全切换到新架构

### 性能监控
- 在重构过程中持续监控性能指标
- 确保新架构不会显著影响分析速度
- 利用访问者模式的优势进一步优化性能