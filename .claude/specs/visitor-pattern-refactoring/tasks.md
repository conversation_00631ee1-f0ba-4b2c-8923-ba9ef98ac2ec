# Visitor Pattern 架构重构 - 实现计划

## 任务概览

本实现计划将现有的认知复杂度分析器重构为基于访问者模式的架构。采用渐进式重构策略，确保在整个过程中保持系统稳定性和向后兼容性。重构分为4个主要阶段，每个阶段都有明确的交付物和验收标准。

## 指导文档合规性

### 项目结构合规 (structure.md)
- 所有新文件都放置在适当的模块目录中 (`src/core/`, `src/__test__/core/`)
- 遵循文件命名约定 (kebab-case, .ts 扩展名)
- 通过 index.ts 进行统一模块导出
- 保持模块间的单向依赖关系

### 技术标准合规 (tech.md)
- 继续使用 @swc/core 作为解析器基础，保持 Node.js 兼容性
- 遵循单一职责原则和依赖注入模式
- 实现分层错误处理和优雅降级
- 保持异步优先的处理模式

## 任务分解

### 第一阶段：基础设施建设

- [x] 1.1 创建 BaseVisitor 基础类
  - 在 `src/core/base-visitor.ts` 中创建继承自 @swc/core/Visitor 的基类
  - 实现 parentStack 属性和相关的栈管理逻辑
  - 重写 visit<T extends Node>(node: T): T 方法，添加父节点栈维护
  - 提供 protected getParent(): Node | undefined 辅助方法
  - 添加完整的 TypeScript 类型定义
  - _需求参考: 2.1, 2.2_

- [x] 1.2 创建 BaseVisitor 单元测试
  - 在 `src/__test__/core/base-visitor.test.ts` 中创建测试套件
  - 测试父节点栈的正确维护（推入和弹出）
  - 测试 getParent() 方法在不同嵌套层级下的正确性
  - 测试访问者遍历逻辑的完整性
  - 测试边界条件（空AST、单节点AST等）
  - _需求参考: 7.1_

- [x] 1.3 创建 FunctionFinderVisitor 类
  - 在 `src/core/function-finder-visitor.ts` 中创建继承 BaseVisitor 的类
  - 实现 visitFunctionDeclaration 方法处理函数声明
  - 实现 visitMethodDefinition 方法处理类方法
  - 实现 visitVariableDeclarator 方法处理函数表达式和箭头函数
  - 提供静态方法 find(ast: Module): Node[] 作为入口点
  - 处理各种 TypeScript/JavaScript 函数类型
  - _需求参考: 5.1, 5.2_

- [x] 1.4 创建 FunctionFinderVisitor 单元测试
  - 在 `src/__test__/core/function-finder-visitor.test.ts` 中创建测试套件
  - 测试普通函数声明的识别
  - 测试类方法的识别（静态方法、实例方法、构造函数）
  - 测试函数表达式和箭头函数的识别
  - 测试嵌套函数的正确识别
  - 测试边界情况和错误输入的处理
  - _需求参考: 5.2, 7.1_

### 第二阶段：复杂度计算重构

- [x] 2.1 创建 ComplexityVisitor 骨架
  - 在 `src/core/complexity-visitor.ts` 中创建继承 BaseVisitor 的类
  - 添加构造函数接受 sourceCode 和可选的 detailCollector
  - 实现基本属性：totalComplexity, nestingLevel
  - 添加 getTotalComplexity() 公共方法
  - 实现 validateSpan(node: Node): number 私有方法
  - _需求参考: 3.1, 4.1_

- [x] 2.2 实现核心复杂度计算方法
  - 实现 visitIfStatement 方法，处理条件语句复杂度
  - 实现 visitWhileStatement, visitForStatement 等循环语句方法
  - 实现 visitSwitchStatement 方法，处理分支语句
  - 实现 visitTryStatement 方法，处理异常处理
  - 实现 visitConditionalExpression 方法，处理三元运算符
  - 在每个方法中集成 span 验证和修正逻辑
  - _需求参考: 3.2, 4.2_

- [x] 2.3 实现结构化 Span 修正逻辑
  - 在 validateSpan 方法中实现 span 有效性检查
  - 实现基于父节点的回退策略（使用 getParent()）
  - 添加 span 修正的详细日志记录
  - 实现降级机制确保总有有效位置返回
  - 与 DetailCollector 集成记录修正信息
  - _需求参考: 4.1, 4.2_

- [x] 2.4 创建 ComplexityVisitor 综合测试
  - 在 `src/__test__/core/complexity-visitor.test.ts` 中创建测试套件
  - 测试各种语句类型的复杂度计算正确性
  - 测试嵌套层级的正确维护
  - 测试 span 修正逻辑的各种场景
  - 测试与 DetailCollector 的集成
  - 测试边界条件和错误恢复
  - _需求参考: 3.3, 4.3, 7.1_

- [x] 2.5 重构 ComplexityCalculator 使用新架构 ✅ 已完成
  - 修改 `src/core/calculator.ts` 中的 calculate 方法
  - 使用 FunctionFinderVisitor.find() 替代 parser.findFunctions()
  - 为每个函数创建独立的 ComplexityVisitor 实例
  - 移除旧的递归遍历方法 (visitNodeWithNesting, visitChildrenWithDepth)
  - 保持公共 API 接口不变，确保向后兼容
  - 完成定时器泄漏修复，确保进程能正常退出
  - _需求参考: 3.1, 3.2_

### 第三阶段：组件职责简化

- [x] 3.1 简化 ASTParser 职责
  - 修改 `src/core/parser.ts`，移除所有遍历相关方法
  - 删除 findFunctions, findFunctionsIterative, findFunctionsInNode 方法
  - 删除位置修正方法 getLineFromSpan, findNearestValidCodeLine
  - 简化 getLocation 方法，仅调用 PositionConverter
  - 保留核心解析功能：parseCode, parseFile, getFunctionName, findIgnoreExemptions
  - _需求参考: 1.1, 1.2_

- [x] 3.2 简化 PositionConverter 职责
  - 修改 `src/utils/position-converter.ts`，移除复杂修正逻辑
  - 删除 findLastValidCodeLine, validateAndCorrectLineIndex 方法
  - 专注于纯粹的位置转换功能
  - 优化性能，移除 sourceCode.split('\n') 调用
  - 改用基于偏移量的高效行内容获取方式
  - _需求参考: 6.1, 6.2_

- [x] 3.3 更新相关模块的依赖调用
  - 检查并更新所有调用简化后方法的模块
  - 修改 `src/cli/commands.ts` 中的分析流程
  - 更新 `src/engine/` 目录下相关的异步引擎代码
  - 确保所有调用点都使用新的架构
  - _需求参考: 1.3, 3.3_

- [x] 3.4 更新简化组件的单元测试
  - 创建 `src/__test__/core/parser.test.ts`，完整测试简化后的 ASTParser 功能
  - 验证 `src/__test__/utils/position-converter.test.ts`，确认专注于纯位置转换测试
  - 确保简化后的功能测试完整覆盖（43个 ASTParser 测试，16个 PositionConverter 测试）
  - 添加 `src/__test__/integration/simplified-components-integration.test.ts` 测试新架构集成点
  - _需求参考: 7.1_

### 第四阶段：测试验证与优化

- [x] 4.1 运行完整的回归测试套件
  - 执行所有现有测试确保无功能回归
  - 运行 `bun test` 确保测试通过率 100%
  - 修复因重构导致的测试失败
  - 确保 CLI 命令输出格式保持一致
  - _需求参考: 7.1, 7.2_

- [ ] 4.2 性能基准测试和对比
  - 在 `src/__test__/performance/visitor-pattern-benchmark.test.ts` 中创建基准测试
  - 对比重构前后的分析速度
  - 测试大型文件和复杂代码的处理性能
  - 确保新架构不会显著降低性能
  - 记录和分析性能指标
  - _需求参考: 7.3_

- [ ] 4.3 集成测试和端到端验证
  - 在 `src/__test__/integration/visitor-pattern-integration.test.ts` 中创建集成测试
  - 测试完整的分析流程：文件输入 → AST解析 → 函数查找 → 复杂度计算 → 结果输出
  - 测试各种文件类型和代码模式
  - 验证错误处理和异常恢复机制
  - 确保与现有插件系统和配置系统的兼容性
  - _需求参考: 7.1, 7.2_

- [ ] 4.4 文档更新和代码清理
  - 更新内联代码注释反映新的架构
  - 清理未使用的导入和死代码
  - 确保所有新类和方法有适当的 TSDoc 注释
  - 更新相关的开发者文档
  - 运行 `pnpm typecheck` 确保类型安全
  - _需求参考: 7.3_

- [ ] 4.5 最终验收测试
  - 在真实项目上测试新的分析器
  - 验证所有验收标准的达成情况
  - 确认没有手动递归 AST 遍历残留
  - 确认所有 span 修正都基于父节点信息
  - 进行最终的代码审查和质量检查
  - _需求参考: 全部需求_

## 验收标准检查清单

### 功能性验收标准
- [ ] 代码库中不再有手动的递归 AST 遍历
- [ ] ASTParser 只负责解析，不包含遍历或位置修正逻辑
- [ ] PositionConverter 只负责精确位置转换，不包含复杂修正逻辑
- [ ] 所有复杂度计算逻辑都封装在 ComplexityVisitor 中
- [ ] 无效 span 的回退基于 BaseVisitor 的父节点信息

### 质量验收标准
- [ ] 所有现有测试（*.test.ts）全部通过
- [ ] 新增测试覆盖率达到 95% 以上
- [ ] 代码遵循单一职责原则
- [ ] TypeScript 类型定义保持一致
- [ ] 性能指标不低于重构前的 90%

### 兼容性验收标准
- [ ] CLI 命令和参数保持完全兼容
- [ ] 分析结果输出格式保持一致
- [ ] 公共 API 接口保持向后兼容
- [ ] 配置文件格式保持不变
- [ ] 插件系统继续正常工作

## 风险缓解措施

### 技术风险
- **回归风险**: 每个阶段都运行完整测试套件
- **性能风险**: 持续监控性能指标，必要时进行优化
- **兼容性风险**: 保持 API 接口不变，渐进式迁移

### 实施风险
- **时间风险**: 任务颗粒度适中，可灵活调整优先级
- **复杂度风险**: 分阶段实施，每阶段有明确交付物
- **质量风险**: 严格的测试覆盖率要求和代码审查流程

## 成功指标

- ✅ 100% 现有测试通过率
- ✅ 95%+ 新代码测试覆盖率  
- ✅ 90%+ 性能保持率
- ✅ 0 破坏性 API 变更
- ✅ 单一职责原则 100% 合规
- ✅ 访问者模式标准实现