# Visitor Pattern 架构重构 - 需求文档

## 概述

重构认知复杂度分析器架构，引入访问者模式以提升代码的可维护性、可扩展性和可持续发展性。

## 需求

### 需求 1: 分离 AST 解析和遍历职责
**用户故事:** 作为开发者，我希望 AST 解析逻辑与遍历逻辑分离，这样可以独立维护和测试每个模块。

#### 验收标准
1. WHEN 调用 ASTParser 时 THEN 系统应该只执行代码解析，不包含遍历逻辑
2. IF AST 解析成功 THEN PositionConverter 应该仅负责位置转换，不包含复杂修正逻辑
3. WHEN 需要遍历 AST 时 THEN 系统应该使用专门的 Visitor 类

### 需求 2: 实现基于访问者模式的 AST 遍历
**用户故事:** 作为系统架构师，我希望所有 AST 遍历都通过标准访问者模式实现，以便统一管理和扩展。

#### 验收标准
1. WHEN 创建 BaseVisitor 时 THEN 系统应该继承自 @swc/core/Visitor
2. IF 节点被访问 THEN BaseVisitor 应该自动维护父节点栈
3. WHEN 子类需要获取父节点 THEN BaseVisitor 应该提供 getParent() 方法

### 需求 3: 重构复杂度计算逻辑
**用户故事:** 作为维护者，我希望复杂度计算逻辑模块化，这样可以更容易添加新的复杂度规则。

#### 验收标准
1. WHEN 计算函数复杂度时 THEN 系统应该使用专门的 ComplexityVisitor
2. IF 需要处理特定节点类型 THEN ComplexityVisitor 应该有对应的 visit 方法
3. WHEN 访问节点时 THEN 系统应该维护嵌套层级和总复杂度

### 需求 4: 实现结构化的 Span 修正策略
**用户故事:** 作为分析器用户，我希望位置信息准确可靠，特别是在处理复杂代码结构时。

#### 验收标准
1. WHEN span 无效时 THEN 系统应该优先使用父节点的 span 信息进行回退
2. IF 父节点信息可用 THEN 系统应该基于 AST 结构而非文本猜测进行修正
3. WHEN 获得有效 span 后 THEN 系统才应该继续复杂度计算

### 需求 5: 创建专门的函数查找器
**用户故事:** 作为开发者，我希望有专门的模块负责查找函数，这样功能职责更清晰。

#### 验收标准
1. WHEN 需要查找函数时 THEN 系统应该使用 FunctionFinderVisitor
2. IF AST 包含函数声明 THEN FunctionFinderVisitor 应该识别所有函数类型节点
3. WHEN 调用静态方法时 THEN FunctionFinderVisitor.find() 应该返回所有函数节点

### 需求 6: 简化位置转换器职责
**用户故事:** 作为性能优化者，我希望 PositionConverter 只负责核心的位置转换，提升整体性能。

#### 验收标准
1. WHEN 转换位置时 THEN PositionConverter 应该只将字节偏移量转换为行列号
2. IF 需要行内容时 THEN 系统应该使用基于偏移量的高效方法
3. WHEN 处理大文件时 THEN 系统不应该调用 sourceCode.split('\n')

### 需求 7: 确保向后兼容性
**用户故事:** 作为用户，我希望重构后的系统保持原有功能，不影响现有工作流。

#### 验收标准
1. WHEN 重构完成时 THEN 所有现有测试应该继续通过
2. IF 使用 CLI 命令时 THEN 输出格式和行为应该保持一致
3. WHEN 分析代码时 THEN 复杂度计算结果应该与重构前一致

## 技术约束

- 必须基于现有的 @swc/core 解析器
- 保持与 Node.js 18+ 的兼容性
- 遵循现有的单一职责原则
- 不能破坏现有的 API 接口

## 架构模式

本重构采用"分层修正架构"：
- **结构性修正层**: 在 Visitor 内部基于 AST 结构信息进行精确修正
- **表现层修正层**: 在 PositionConverter 中仅进行显示优化

## 成功标准

1. ✅ 代码库不再有手动递归 AST 遍历
2. ✅ ASTParser 职责单一化（仅解析）
3. ✅ PositionConverter 职责单一化（仅位置转换）
4. ✅ ComplexityVisitor 封装所有复杂度计算逻辑
5. ✅ 基于父节点信息的结构化 span 回退
6. ✅ 所有现有测试通过
7. ✅ 代码遵循单一职责原则和一致的类型定义