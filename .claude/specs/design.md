# 认知复杂度CLI工具 - 技术设计文档

## 概述

本文档定义了认知复杂度CLI工具的技术架构和实现方案，基于已确认的需求文档，采用模块化设计确保代码的可维护性和可扩展性。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                     CLI Interface Layer                     │
├─────────────────────────────────────────────────────────────┤
│  Configuration    │  Command Parser   │   Output Formatter  │
│     Manager       │                   │                     │
├─────────────────────────────────────────────────────────────┤
│                   Core Analysis Engine                      │
├─────────────────────────────────────────────────────────────┤
│  AST Parser       │  Complexity       │  Rule Engine       │
│   (SWC)          │   Calculator      │                     │
├─────────────────────────────────────────────────────────────┤
│              File System & Baseline Manager                 │
├─────────────────────────────────────────────────────────────┤
│                    External Dependencies                    │
│        SWC Parser    │    Glob    │ Commander │ Cosmiconfig │
└─────────────────────────────────────────────────────────────┘
```

### 模块设计

#### 1. 核心分析引擎 (Core Analysis Engine)

**位置：** `src/core/`

**职责：** 
- AST解析和遍历
- 认知复杂度计算
- 规则引擎管理

**主要组件：**

```typescript
// src/core/types.ts
export interface CalculationOptions {
  enableMixedLogicOperatorPenalty?: boolean;
  recursionChainThreshold?: number;
}

export interface FunctionResult {
  name: string;
  complexity: number;
  line: number;
  column: number;
  filePath: string;
  severity?: 'Critical' | 'Warning' | 'Info';
}

export interface FileResult {
  filePath: string;
  complexity: number;
  functions: FunctionResult[];
  averageComplexity: number;
}

export interface AnalysisResult {
  summary: {
    totalComplexity: number;
    averageComplexity: number;
    filesAnalyzed: number;
    functionsAnalyzed: number;
    highComplexityFunctions: number;
  };
  results: FileResult[];
  baseline?: BaselineData;
}
```

```typescript
// src/core/calculator.ts
export class ComplexityCalculator {
  private options: CalculationOptions;
  private nestingLevel: number = 0;
  
  constructor(options: CalculationOptions = {}) {
    this.options = options;
  }
  
  public calculate(ast: Module): FunctionResult[] {
    // 主要计算逻辑
  }
  
  private visitNode(node: Node): number {
    // AST节点访问逻辑
  }
  
  private calculateNestingPenalty(baseScore: number): number {
    return baseScore + this.nestingLevel;
  }
  
  private detectLogicalOperatorMixing(node: Node): boolean {
    // 检测逻辑运算符混用
  }
}
```

#### 2. CLI接口层 (CLI Interface Layer)

**位置：** `src/cli/`

**职责：**
- 命令行参数解析
- 用户交互处理
- 输出格式化

```typescript
// src/cli/commands.ts
export interface CLIOptions {
  paths: string[];
  failOn?: number;
  createBaseline?: boolean;
  updateBaseline?: boolean;
  config?: string;
  outputDir?: string;
  details?: boolean;
  format?: 'text' | 'json';
  min?: number;
  sort?: 'name' | 'complexity';
  ui?: boolean;
}

export class CommandProcessor {
  public async execute(options: CLIOptions): Promise<void> {
    // 命令执行逻辑
  }
  
  private async analyzeFiles(paths: string[]): Promise<AnalysisResult> {
    // 文件分析逻辑
  }
  
  private applyQualityGate(result: AnalysisResult, threshold: number): boolean {
    // 质量门禁逻辑
  }
}
```

#### 3. 配置管理系统 (Configuration Manager)

**位置：** `src/config/`

**职责：**
- 使用cosmiconfig自动发现和加载配置文件
- 支持多种配置文件格式(JSON, JS, TS, RC文件)
- 配置文件验证和合并
- 默认配置管理

**cosmiconfig优势：**
- 自动按优先级搜索配置文件
- 支持package.json中的配置
- 内置缓存机制
- 支持同步和异步加载
- TypeScript原生支持

```typescript
// src/config/types.ts
export interface CognitiveConfig {
  failOnComplexity: number;
  exclude: string[];
  report: {
    json?: string;
    html?: string;
  };
  severityMapping: SeverityLevel[];
  rules?: {
    enableMixedLogicOperatorPenalty?: boolean;
    recursionChainThreshold?: number;
  };
}

export interface SeverityLevel {
  level: 'Critical' | 'Warning' | 'Info';
  threshold: number;
}
```

```typescript
// src/config/manager.ts
import { cosmiconfig } from 'cosmiconfig';
import { CognitiveConfig } from './types';

export class ConfigManager {
  private static DEFAULT_CONFIG: CognitiveConfig = {
    failOnComplexity: 15,
    exclude: [
      "**/*.test.ts",
      "**/*.spec.ts", 
      "**/node_modules/**",
      "dist/**"
    ],
    report: {},
    severityMapping: [
      { level: 'Critical', threshold: 30 },
      { level: 'Warning', threshold: 12 }
    ]
  };
  
  private static explorer = cosmiconfig('cognitive-complexity', {
    searchPlaces: [
      'package.json',
      'cognitive-complexity.config.js',
      'cognitive-complexity.config.ts',
      'cognitive-complexity.config.json',
      '.cognitive-complexityrc',
      '.cognitive-complexityrc.json',
      '.cognitive-complexityrc.js',
      '.cognitive-complexityrc.ts',
    ],
    loaders: {
      '.ts': async (filepath: string) => {
        // 使用标准的动态导入加载TypeScript配置文件
        const config = await import(filepath);
        return config.default || config;
      }
    }
  });
  
  public static async loadConfig(configPath?: string): Promise<CognitiveConfig> {
    try {
      let result;
      
      if (configPath) {
        // 加载指定的配置文件
        result = await this.explorer.load(configPath);
      } else {
        // 自动搜索配置文件
        result = await this.explorer.search();
      }
      
      if (!result) {
        console.log('No config file found, using default configuration');
        return this.DEFAULT_CONFIG;
      }
      
      const mergedConfig = this.mergeWithDefaults(result.config);
      console.log(`Loaded config from: ${result.filepath}`);
      
      return this.validateConfig(mergedConfig);
    } catch (error) {
      console.warn(`Failed to load config: ${error.message}`);
      console.log('Using default configuration');
      return this.DEFAULT_CONFIG;
    }
  }
  
  private static mergeWithDefaults(userConfig: Partial<CognitiveConfig>): CognitiveConfig {
    return {
      ...this.DEFAULT_CONFIG,
      ...userConfig,
      severityMapping: userConfig.severityMapping || this.DEFAULT_CONFIG.severityMapping,
      exclude: [
        ...this.DEFAULT_CONFIG.exclude,
        ...(userConfig.exclude || [])
      ]
    };
  }
  
  public static validateConfig(config: any): CognitiveConfig {
    const errors: string[] = [];
    
    if (typeof config.failOnComplexity !== 'number' || config.failOnComplexity < 0) {
      errors.push('failOnComplexity must be a non-negative number');
    }
    
    if (!Array.isArray(config.exclude)) {
      errors.push('exclude must be an array of strings');
    }
    
    if (!Array.isArray(config.severityMapping)) {
      errors.push('severityMapping must be an array');
    } else {
      config.severityMapping.forEach((mapping: any, index: number) => {
        if (!mapping.level || !['Critical', 'Warning', 'Info'].includes(mapping.level)) {
          errors.push(`severityMapping[${index}].level must be 'Critical', 'Warning', or 'Info'`);
        }
        if (typeof mapping.threshold !== 'number' || mapping.threshold < 0) {
          errors.push(`severityMapping[${index}].threshold must be a non-negative number`);
        }
      });
    }
    
    if (errors.length > 0) {
      throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
    }
    
    return config as CognitiveConfig;
  }
  
  /**
   * 清除cosmiconfig缓存，主要用于测试
   */
  public static clearCache(): void {
    this.explorer.clearCaches();
  }
}
```

**支持的配置文件格式：**

```json
// cognitive-complexity.config.json
{
  "failOnComplexity": 15,
  "exclude": ["**/*.test.ts", "dist/**"],
  "report": {
    "json": "reports/complexity.json",
    "html": "reports/complexity.html"
  },
  "severityMapping": [
    { "level": "Critical", "threshold": 30 },
    { "level": "Warning", "threshold": 12 }
  ]
}
```

```javascript
// cognitive-complexity.config.js
module.exports = {
  failOnComplexity: 15,
  exclude: ["**/*.test.ts", "dist/**"],
  report: {
    json: "reports/complexity.json"
  },
  severityMapping: [
    { level: "Critical", threshold: 30 },
    { level: "Warning", threshold: 12 }
  ]
};
```

```typescript
// cognitive-complexity.config.ts
import type { CognitiveConfig } from '@imd/cognitive-complexity';

const config: CognitiveConfig = {
  failOnComplexity: 15,
  exclude: ["**/*.test.ts", "dist/**"],
  report: {
    json: "reports/complexity.json"
  },
  severityMapping: [
    { level: "Critical", threshold: 30 },
    { level: "Warning", threshold: 12 }
  ]
};

export default config;
```

```json
// package.json中的配置
{
  "name": "my-project",
  "cognitive-complexity": {
    "failOnComplexity": 20,
    "exclude": ["**/*.test.ts"]
  }
}
```

#### 4. 基线管理器 (Baseline Manager)

**位置：** `src/baseline/`

**职责：**
- 基线文件创建和更新
- 基线比较逻辑
- 增量分析支持

```typescript
// src/baseline/types.ts
export interface BaselineEntry {
  filePath: string;
  functionName: string;
  line: number;
  complexity: number;
  lastUpdated: string;
}

export interface BaselineData {
  version: string;
  createdAt: string;
  entries: BaselineEntry[];
}
```

```typescript
// src/baseline/manager.ts
export class BaselineManager {
  private baselinePath: string;
  
  constructor(baselinePath: string = 'cognitive-baseline.json') {
    this.baselinePath = baselinePath;
  }
  
  public async createBaseline(analysisResult: AnalysisResult): Promise<void> {
    // 创建基线逻辑
  }
  
  public async loadBaseline(): Promise<BaselineData | null> {
    // 加载基线逻辑
  }
  
  public async updateBaseline(analysisResult: AnalysisResult): Promise<void> {
    // 更新基线逻辑
  }
  
  public compareWithBaseline(current: AnalysisResult, baseline: BaselineData): AnalysisResult {
    // 基线比较逻辑
  }
}
```

#### 5. 输出格式化器 (Output Formatter)

**位置：** `src/formatters/`

**职责：**
- 多种格式输出支持
- 报告生成
- 可视化支持

```typescript
// src/formatters/base.ts
export abstract class BaseFormatter {
  protected config: CognitiveConfig;
  
  constructor(config: CognitiveConfig) {
    this.config = config;
  }
  
  public abstract format(result: AnalysisResult): string;
  public abstract writeToFile?(result: AnalysisResult, outputPath: string): Promise<void>;
}
```

```typescript
// src/formatters/text.ts
export class TextFormatter extends BaseFormatter {
  public format(result: AnalysisResult): string {
    // 文本格式化逻辑
  }
  
  private formatSummary(summary: AnalysisResult['summary']): string {
    // 汇总信息格式化
  }
  
  private formatFileResult(fileResult: FileResult): string {
    // 文件结果格式化
  }
}
```

```typescript
// src/formatters/json.ts
export class JsonFormatter extends BaseFormatter {
  public format(result: AnalysisResult): string {
    return JSON.stringify(result, null, 2);
  }
  
  public async writeToFile(result: AnalysisResult, outputPath: string): Promise<void> {
    // JSON文件写入逻辑
  }
}
```

## 数据流设计

### 主要处理流程

```
1. CLI启动
   ↓
2. 解析命令行参数
   ↓
3. 加载配置文件
   ↓
4. 发现和过滤文件
   ↓
5. 并行解析AST
   ↓
6. 计算认知复杂度
   ↓
7. 应用基线比较
   ↓
8. 格式化输出
   ↓
9. 质量门禁检查
   ↓
10. 退出(成功/失败)
```

### 错误处理策略

```typescript
// src/core/errors.ts
export class CognitiveComplexityError extends Error {
  constructor(message: string, public readonly code: string) {
    super(message);
    this.name = 'CognitiveComplexityError';
  }
}

export class ParseError extends CognitiveComplexityError {
  constructor(filePath: string, originalError: Error) {
    super(`Failed to parse file: ${filePath}. ${originalError.message}`, 'PARSE_ERROR');
  }
}

export class ConfigError extends CognitiveComplexityError {
  constructor(message: string) {
    super(`Configuration error: ${message}`, 'CONFIG_ERROR');
  }
}
```

## API设计

### 核心库API

```typescript
// src/index.ts (Library Entry Point)
export { ComplexityCalculator } from './core/calculator';
export { ConfigManager } from './config/manager';
export { BaselineManager } from './baseline/manager';
export * from './core/types';

// 主要函数导出
export async function analyzeFile(
  filePath: string, 
  options?: CalculationOptions
): Promise<FileResult> {
  // 单文件分析
}

export async function analyzeProject(
  paths: string[], 
  config?: CognitiveConfig
): Promise<AnalysisResult> {
  // 项目分析
}
```

### CLI入口点

```typescript
// src/cli/index.ts
#!/usr/bin/env node

import { Command } from 'commander';
import { CommandProcessor } from './commands';

const program = new Command();

program
  .name('cognitive-complexity')
  .description('Analyze cognitive complexity of TypeScript/JavaScript code')
  .version('1.0.0')
  .argument('[paths...]', 'Files or directories to analyze', ['.'])
  .option('--fail-on <threshold>', 'Fail when complexity exceeds threshold', '15')
  .option('--create-baseline', 'Create baseline file for current project')
  .option('--update-baseline', 'Update baseline file')
  .option('-c, --config <path>', 'Path to config file')
  .option('-o, --output-dir <path>', 'Output directory for reports')
  .option('-d, --details', 'Show detailed complexity report')
  .option('-f, --format <format>', 'Output format', 'text')
  .option('-m, --min <threshold>', 'Show only results above threshold')
  .option('-s, --sort <key>', 'Sort results by', 'complexity')
  .option('--ui', 'Start web UI for visualization')
  .action(async (paths, options) => {
    try {
      const processor = new CommandProcessor();
      await processor.execute({ paths, ...options });
    } catch (error) {
      console.error('Error:', error.message);
      process.exit(1);
    }
  });

program.parse();
```

## 文件系统组织

```
src/
├── index.ts                    # 库主入口
├── cli/
│   ├── index.ts               # CLI入口点
│   ├── commands.ts            # 命令处理器
│   └── help.ts                # 帮助信息
├── core/
│   ├── types.ts               # 核心类型定义
│   ├── calculator.ts          # 复杂度计算器
│   ├── parser.ts              # AST解析器
│   ├── rules/                 # 规则引擎
│   │   ├── base.ts           # 基础规则
│   │   ├── structural.ts     # 结构性规则
│   │   ├── logical.ts        # 逻辑运算符规则
│   │   └── advanced.ts       # 高级规则
│   └── errors.ts              # 错误定义
├── config/
│   ├── types.ts               # 配置类型
│   ├── manager.ts             # 配置管理器
│   └── validator.ts           # 配置验证器
├── baseline/
│   ├── types.ts               # 基线类型
│   ├── manager.ts             # 基线管理器
│   └── comparator.ts          # 基线比较器
├── formatters/
│   ├── base.ts                # 基础格式化器
│   ├── text.ts                # 文本格式化器
│   ├── json.ts                # JSON格式化器
│   └── html.ts                # HTML报告生成器
├── utils/
│   ├── file-finder.ts         # 文件发现工具
│   ├── glob-matcher.ts        # Glob匹配工具
│   └── logger.ts              # 日志工具
├── web/                       # Web UI (可选)
│   ├── server.ts              # Web服务器
│   ├── public/                # 静态资源
│   └── templates/             # 模板文件
└── __test__/                  # 测试文件
    ├── core/
    │   ├── calculator.test.ts
    │   ├── parser.test.ts
    │   └── rules/
    │       ├── structural.test.ts
    │       ├── logical.test.ts
    │       └── advanced.test.ts
    ├── config/
    │   ├── manager.test.ts
    │   └── validator.test.ts
    ├── baseline/
    │   ├── manager.test.ts
    │   └── comparator.test.ts
    ├── formatters/
    │   ├── text.test.ts
    │   ├── json.test.ts
    │   └── html.test.ts
    ├── integration/
    │   ├── cli.test.ts
    │   └── e2e.test.ts
    ├── snapshots/
    │   └── complexity.test.ts
    ├── fixtures/                # 测试用例数据
    │   ├── cases/               # 复杂度计算测试用例
    │   │   ├── basic.ts
    │   │   ├── nested.ts
    │   │   ├── logical.ts
    │   │   └── edge-cases.ts
    │   ├── configs/             # 配置文件测试用例
    │   └── baselines/           # 基线文件测试用例
    └── helpers/                 # 测试辅助工具
        ├── test-utils.ts
        ├── mock-data.ts
        └── analyzer-helpers.ts
```

## 性能优化策略

### 1. 并行处理
```typescript
// src/utils/parallel-processor.ts
export class ParallelProcessor {
  private workerPool: Worker[];
  
  public async processFiles(filePaths: string[]): Promise<FileResult[]> {
    const chunks = this.chunkArray(filePaths, this.workerPool.length);
    const promises = chunks.map((chunk, index) => 
      this.processChunk(chunk, this.workerPool[index])
    );
    
    const results = await Promise.all(promises);
    return results.flat();
  }
}
```

### 2. 增量分析
```typescript
// src/utils/incremental-analyzer.ts
export class IncrementalAnalyzer {
  private cacheManager: CacheManager;
  
  public async analyzeWithCache(
    filePaths: string[], 
    lastAnalysisTime?: Date
  ): Promise<AnalysisResult> {
    const changedFiles = await this.getChangedFiles(filePaths, lastAnalysisTime);
    const cachedResults = await this.cacheManager.getCachedResults(filePaths);
    
    // 只分析变更的文件
    const newResults = await this.analyzeFiles(changedFiles);
    
    return this.mergeResults(cachedResults, newResults);
  }
}
```

### 3. 内存优化
```typescript
// src/core/streaming-parser.ts
import { createReadStream } from 'fs';
import { SWCStreamParser } from '@swc/core';

export class StreamingParser {
  public async parseFileStream(filePath: string): Promise<FileResult> {
    // 使用Node.js流式解析，避免大文件内存占用
    const stream = createReadStream(filePath);
    const parser = new SWCStreamParser();
    
    return new Promise((resolve, reject) => {
      stream.pipe(parser)
        .on('function', (functionNode) => {
          // 逐个处理函数，而不是加载整个AST
        })
        .on('end', () => resolve(result))
        .on('error', reject);
    });
  }
}
```

## 测试策略

### 1. 单元测试
```typescript
// src/__test__/core/calculator.test.ts
describe('ComplexityCalculator', () => {
  describe('基础计分规则', () => {
    it('should calculate if statement complexity correctly', () => {
      const code = `
        function test() {
          if (condition) {
            return true;
          }
        }
      `;
      
      const result = analyzeCode(code);
      expect(result.functions[0].complexity).toBe(1);
    });
    
    it('should calculate nested complexity correctly', () => {
      const code = `
        function test() {
          for (let i = 0; i < 10; i++) {    // +1
            if (condition) {                // +1 + 1(nesting) = +2
              while (otherCondition) {      // +1 + 2(nesting) = +3
                break;
              }
            }
          }
        }
      `;
      
      const result = analyzeCode(code);
      expect(result.functions[0].complexity).toBe(6); // 1 + 2 + 3
    });
  });
});
```

### 2. 集成测试
```typescript
// src/__test__/integration/cli.test.ts
describe('CLI Integration', () => {
  it('should analyze project and generate report', async () => {
    const result = await exec('cognitive-complexity src/__test__/fixtures --format json');
    const analysis = JSON.parse(result.stdout);
    
    expect(analysis.summary.filesAnalyzed).toBeGreaterThan(0);
    expect(analysis.results).toHaveLength(analysis.summary.filesAnalyzed);
  });
});
```

### 3. 快照测试
```typescript
// src/__test__/snapshots/complexity.test.ts
describe('Complexity Calculation Snapshots', () => {
  const testCases = loadTestCases('src/__test__/fixtures/cases');
  
  testCases.forEach(testCase => {
    it(`should calculate complexity for ${testCase.name}`, () => {
      const result = analyzeCode(testCase.code);
      expect(result).toMatchSnapshot();
    });
  });
});
```

## 部署和分发

### 运行时兼容性

本工具采用双运行时策略：
- **开发环境**：使用Bun作为开发运行时，享受快速启动和构建
- **生产代码**：严格使用Node.js兼容的API，避免任何Bun特定功能
- **用户环境**：构建产物完全兼容Node.js，支持npm/npx标准工作流

**Node.js兼容性原则：**
- 使用标准Node.js API（如`fs`、`path`等）
- 避免Bun特定API（如`Bun.file`、`Bun.serve`等）
- 使用成熟的第三方库而非Bun内置功能
- 确保构建产物在Node.js 18+环境中正常运行

**用户使用方式：**
```bash
# 全局安装
npm install -g @imd/cognitive-complexity
cognitive-complexity src/

# 使用npx运行
npx @imd/cognitive-complexity src/

# 项目内安装
npm install --save-dev @imd/cognitive-complexity
npx cognitive-complexity src/
```

**开发者工作流：**
```bash
# 开发环境使用Bun
bun install
bun run dev
bun test
bun run build
```

### 1. 包构建配置
```json
// package.json
{
  "name": "@imd/cognitive-complexity",
  "version": "1.0.0",
  "description": "Analyze cognitive complexity of TypeScript/JavaScript code",
  "main": "dist/index.js",
  "module": "dist/index.js", 
  "types": "src/index.ts",
  "bin": {
    "cognitive-complexity": "dist/cli/index.js"
  },
  "engines": {
    "node": ">=18.0.0"
  },
  "files": [
    "dist/",
    "src/",
    "README.md",
    "LICENSE"
  ],
  "scripts": {
    "dev": "bun --watch src/cli/index.ts",
    "build": "./build.sh",
    "build:lib": "bun build src/index.ts --outdir dist --target node --format esm --external @swc/core --external commander --external cosmiconfig --external glob",
    "build:cli": "bun build src/cli/index.ts --outdir dist/cli --target node --format esm --external @swc/core --external commander --external cosmiconfig --external glob", 
    "test": "bun test src/__test__/**/*.test.ts",
    "start": "bun run src/cli/index.ts",
    "clean": "rm -rf dist/"
  },
  "keywords": [
    "cognitive-complexity",
    "code-quality",
    "static-analysis",
    "typescript",
    "javascript",
    "cli"
  ],
  "dependencies": {
    "@swc/core": "^1.3.0",
    "commander": "^11.0.0",
    "cosmiconfig": "^8.0.0",
    "glob": "^10.0.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "bun-types": "latest"
  },
  "peerDependencies": {
    "typescript": ">=4.5.0"
  }
}
```

### 2. 本地构建脚本

```bash
#!/bin/bash
# build.sh - 本地构建脚本

echo "🏗️  开始构建..."

# 清理旧的构建产物
rm -rf dist/

# 构建库文件
echo "📦 构建库文件..."
bun build src/index.ts \
  --outdir dist \
  --target node \
  --format esm \
  --external @swc/core \
  --external commander \
  --external cosmiconfig \
  --external glob

# 构建CLI文件
echo "🔧 构建CLI文件..."
bun build src/cli/index.ts \
  --outdir dist/cli \
  --target node \
  --format esm \
  --external @swc/core \
  --external commander \
  --external cosmiconfig \
  --external glob

# 为CLI文件添加shebang和可执行权限
echo "⚡ 处理CLI可执行文件..."
sed -i '1i#!/usr/bin/env node' dist/cli/index.js
chmod +x dist/cli/index.js

echo "✅ 构建完成！"
echo "📁 输出目录: dist/"
echo "🚀 测试CLI: ./dist/cli/index.js --help"
```

### 3. Bun构建配置

Bun内置的构建器无需额外配置文件，所有配置通过命令行参数完成：

```bash
# 构建库文件
bun build src/index.ts \
  --outdir dist \
  --target node \
  --format esm \
  --external @swc/core \
  --external commander \
  --external cosmiconfig \
  --external glob

# 构建CLI文件
bun build src/cli/index.ts \
  --outdir dist/cli \
  --target node \
  --format esm \
  --external @swc/core \
  --external commander \
  --external cosmiconfig \
  --external glob
```

**Bun构建优势：**
- 零配置，无需额外配置文件
- 快速构建，比传统构建工具快10-100倍
- 内置TypeScript支持
- 树摇优化和代码分割
- 原生ESM输出

**CLI文件处理：**

构建脚本会自动处理CLI文件的shebang和权限：
```bash
# 为CLI文件添加shebang和可执行权限
sed -i '1i#!/usr/bin/env node' dist/cli/index.js
chmod +x dist/cli/index.js
```

**发布准备：**
```bash
# 本地发布流程
bun run build    # 使用Bun构建（开发环境）
bun test         # 运行测试
npm publish      # 发布到npm（用户使用npm安装）
```

**重要提醒：**
- 开发时使用`bun run dev`享受快速体验
- 构建时使用Bun但输出Node.js兼容代码
- 用户安装后使用标准npm/npx命令
- 所有生产代码严格避免Bun特定API

这个设计文档为认知复杂度CLI工具提供了完整的技术架构，确保了高性能、可维护性和可扩展性。设计是否符合您的期望？是否需要调整任何特定的架构决策？