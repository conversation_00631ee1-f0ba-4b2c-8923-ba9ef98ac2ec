# Design Document: Output Complexity Filter

## Overview

此功能为 cognitive-complexity CLI 工具添加文件级复杂度过滤能力，允许用户通过 `--min-file-complexity` 参数控制输出中显示的文件。这个新参数将在格式化阶段应用过滤逻辑，默认值为 1（自动隐藏复杂度为 0 的文件），支持所有输出格式（text、json、html），并提供过滤统计信息。

这个功能是对现有函数级过滤参数 `--min` 的补充，提供文件级的过滤控制，让用户能够专注于需要关注的复杂文件。

## Steering Document Alignment

### Technical Standards (tech.md)

该设计严格遵循技术栈引导文档中的原则：

1. **Node.js兼容性**：所有实现将使用Node.js标准API，不依赖Bun特定功能
2. **模块化设计**：遵循单一职责原则，将过滤逻辑封装在独立的组件中
3. **性能要求**：过滤逻辑在格式化阶段执行，O(1)复杂度，不影响分析性能
4. **错误处理**：实现分层错误处理，提供用户友好的错误信息
5. **TypeScript严格模式**：启用所有严格类型检查，确保类型安全

### Project Structure (structure.md)

设计遵循项目结构规范：

1. **CLI模块**：在 `src/cli/index.ts` 中添加新参数定义
2. **配置模块**：在 `src/config/types.ts` 中扩展 CLIOptions 接口
3. **格式化模块**：在各个formatter中实现过滤逻辑
4. **工具模块**：在 `src/utils/` 中创建专用的过滤工具类

## Code Reuse Analysis

该设计充分利用现有代码架构和组件：

### 可重用的现有组件

1. **Commander.js 参数处理模式**：
   - 参考现有的 `--min-complexity-increment` 参数实现
   - 使用相同的参数验证和类型转换逻辑

2. **配置管理系统**：
   - 扩展现有的 `CLIOptions` 接口
   - 重用配置合并和验证机制

3. **格式化器基础架构**：
   - 利用 `BaseFormatter` 类的继承架构
   - 重用现有的严重性等级判断逻辑（`getSeverityLevel`）

4. **智能过滤器架构**：
   - 参考 `src/formatters/smart-filter.ts` 的设计模式
   - 重用过滤统计信息收集机制

5. **错误处理和用户体验**：
   - 重用 `CLIUIHelper` 的用户反馈机制
   - 利用现有的静默模式处理逻辑

### 需要扩展的组件

1. **CLIOptions 接口**：添加 `minFileComplexity?: number` 属性
2. **各个Formatter类**：添加文件级过滤逻辑
3. **参数验证系统**：扩展验证规则以支持新参数

## Architecture

该功能采用分层架构，在格式化阶段实现过滤逻辑：

```mermaid
graph TD
    A[CLI Parameter] --> B[CommandProcessor]
    B --> C[ComplexityCalculator]
    C --> D[AnalysisResult]
    D --> E[FileComplexityFilter]
    E --> F1[TextFormatter]
    E --> F2[JsonFormatter]
    E --> F3[HtmlFormatter]
    F1 --> G1[Filtered Text Output]
    F2 --> G2[Filtered JSON Output]
    F3 --> G3[Filtered HTML Output]
    
    E --> H[FilterStatistics]
    H --> I[Summary Display]

    style E fill:#e1f5fe
    style H fill:#f3e5f5
```

### Data Flow

1. **参数解析阶段**：CLI 解析 `--min-file-complexity` 参数
2. **分析阶段**：ComplexityCalculator 分析所有文件（不受过滤影响）
3. **过滤阶段**：FileComplexityFilter 根据阈值过滤文件结果
4. **格式化阶段**：各个Formatter基于过滤后的结果生成输出
5. **统计显示**：显示过滤统计信息（非静默模式）

## Components and Interfaces

### Component 1: FileComplexityFilter
- **Purpose:** 实现文件级复杂度过滤逻辑和统计收集
- **Interfaces:** 
  ```typescript
  class FileComplexityFilter {
    filterResults(results: FileResult[], threshold: number): FilteredResults;
    getStatistics(): FilterStatistics;
  }
  ```
- **Dependencies:** 依赖 `FileResult` 和 `AnalysisResult` 类型
- **Reuses:** 参考 `SmartContextFilter` 的设计模式和统计收集机制

### Component 2: Enhanced CLIOptions
- **Purpose:** 扩展CLI选项以支持文件复杂度过滤
- **Interfaces:** 
  ```typescript
  interface CLIOptions {
    // ... existing options
    minFileComplexity?: number;
  }
  ```
- **Dependencies:** 无新依赖
- **Reuses:** 扩展现有的 `CLIOptions` 接口定义

### Component 3: Enhanced Formatters
- **Purpose:** 在各个格式化器中集成文件过滤功能
- **Interfaces:** 
  ```typescript
  abstract class BaseFormatter {
    protected applyFileComplexityFilter(result: AnalysisResult, options?: CLIOptions): AnalysisResult;
  }
  ```
- **Dependencies:** 依赖 `FileComplexityFilter` 组件
- **Reuses:** 基于现有的 `BaseFormatter` 架构和严重性等级系统

### Component 4: Parameter Validation Extension
- **Purpose:** 扩展参数验证以支持新的文件复杂度参数
- **Interfaces:** 
  ```typescript
  function validateMinFileComplexity(value: string): number;
  ```
- **Dependencies:** 依赖现有的验证服务框架
- **Reuses:** 利用现有的并发验证服务 `ConcurrentValidationService`

## Data Models

### FilteredResults Interface
```typescript
interface FilteredResults {
  filteredFiles: FileResult[];
  statistics: FilterStatistics;
}
```

### FilterStatistics Interface
```typescript
interface FilterStatistics {
  totalFiles: number;
  displayedFiles: number;
  hiddenFiles: number;
  threshold: number;
  hasFiltering: boolean;
}
```

### Enhanced AnalysisResult
```typescript
interface AnalysisResult {
  // ... existing properties
  filterStatistics?: FilterStatistics;
}
```

## Error Handling

遵循项目的分层错误处理策略：

### 参数验证错误
```typescript
class InvalidParameterError extends Error {
  constructor(parameter: string, value: string, expectedType: string) {
    super(`参数 ${parameter} 的值 "${value}" 无效，期望 ${expectedType}`);
  }
}
```

### 过滤操作错误
- **优雅降级**：过滤失败时显示所有文件，记录警告信息
- **详细日志**：提供过滤过程的调试信息
- **用户友好**：错误信息提供具体的修复建议

## Testing Strategy

基于现有的测试架构和工具：

### Unit Tests
- **过滤器逻辑测试**：测试各种阈值下的过滤行为
- **参数验证测试**：验证参数解析和错误处理
- **统计计算测试**：确保过滤统计信息的准确性

### Integration Tests
- **格式化器集成测试**：验证各个格式化器的过滤功能
- **CLI集成测试**：端到端测试参数传递和输出过滤
- **兼容性测试**：确保与现有参数的兼容性

### Test Utilities Reuse
- **利用现有的测试夹具**：重用 `src/__test__/fixtures/` 中的测试数据
- **使用测试工具类**：利用 `cli-testing-utils.ts` 等辅助工具
- **集成现有的断言工具**：使用项目中已建立的测试断言模式

### Edge Case Testing
- **边界值测试**：阈值为0、负数、超大数的处理
- **空结果集测试**：没有文件或所有文件被过滤的情况
- **格式化一致性测试**：确保各种格式的过滤行为完全一致

这个设计充分利用了现有的代码架构和组件，遵循项目的技术标准和结构规范，实现了一个高效、可维护且用户友好的文件复杂度过滤功能。