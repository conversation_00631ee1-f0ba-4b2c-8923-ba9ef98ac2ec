# Implementation Plan: Output Complexity Filter

## Task Overview

此实施计划将为 cognitive-complexity CLI 工具添加文件级复杂度过滤功能。通过添加 `--min-file-complexity` 参数，用户可以控制输出中显示哪些文件，默认值为 1（自动隐藏复杂度为 0 的文件）。该功能将在格式化阶段应用过滤逻辑，支持所有输出格式，并提供过滤统计信息。

## Steering Document Compliance

### Structure.md 遵循

- **CLI 模块**：在 `src/cli/index.ts` 中添加新参数定义，遵循现有参数模式
- **配置模块**：在 `src/config/types.ts` 中扩展 CLIOptions 接口
- **格式化模块**：在各个 formatter 中实现过滤逻辑，继承 BaseFormatter 架构
- **工具模块**：在 `src/utils/` 中创建专用的过滤工具类

### Tech.md 遵循

- 使用 Node.js 标准 API，确保完全兼容性
- 遵循模块化设计原则，单一职责
- 实现分层错误处理机制
- 启用 TypeScript 严格模式进行类型检查

## Tasks

- [x] 1. 扩展 CLI 参数和配置类型定义

  - 在 `src/cli/index.ts` 中添加 `--min-file-complexity` 参数
  - 在 `src/config/types.ts` 中扩展 CLIOptions 接口添加 `minFileComplexity?: number` 属性
  - 实现参数验证逻辑，确保值为非负数
  - 设置默认值为 1，自动隐藏复杂度为 0 的文件
  - _Leverage: 现有的 --min-complexity-increment 参数实现模式, Commander.js 参数处理机制_
  - _Requirements: 1.1, 1.2, 1.3, 4.4_

- [x] 2. 创建文件复杂度过滤器核心组件

  - 在 `src/utils/` 中创建 `file-complexity-filter.ts`
  - 实现 `FileComplexityFilter` 类，包含过滤逻辑和统计收集
  - 定义 `FilteredResults` 和 `FilterStatistics` 接口
  - 实现过滤算法，基于文件总复杂度阈值进行过滤
  - _Leverage: SmartContextFilter 的设计模式和统计收集机制_
  - _Requirements: 1.4, 1.5, 3.1, 3.2_

- [x] 3. 增强基础格式化器架构

  - 修改 `src/formatters/base.ts` 中的 BaseFormatter 抽象类
  - 添加 `applyFileComplexityFilter` 保护方法
  - 集成 FileComplexityFilter 组件进行文件级过滤
  - 确保过滤后汇总统计信息保持准确性（基于所有分析文件）
  - _Leverage: 现有的 BaseFormatter 架构和严重性等级系统_
  - _Requirements: 1.5, 2.4_

- [x] 4. 实现文本格式化器的文件过滤

  - 修改 `src/formatters/text.ts` 中的 TextFormatter 类
  - 在格式化过程中应用文件复杂度过滤
  - 实现过滤统计信息的显示逻辑（非静默模式）
  - 确保过滤后的文件完全从输出中隐藏
  - _Leverage: 现有的文本格式化逻辑和条件渲染机制_
  - _Requirements: 2.1, 3.1, 3.3_

- [x] 5. 实现 JSON 格式化器的文件过滤

  - 修改 `src/formatters/json.ts` 中的 JsonFormatter 类
  - 在 JSON 输出的 results 数组中排除低复杂度文件
  - 在汇总信息中包含过滤统计数据
  - 确保 JSON 结构的完整性和一致性
  - _Leverage: 现有的 JSON 序列化逻辑_
  - _Requirements: 2.2, 3.3_

- [x] 6. 实现 HTML 格式化器的文件过滤

  - 修改 `src/formatters/html.ts` 中的 HtmlFormatter 类
  - 在 HTML 报告中应用文件复杂度过滤
  - 确保 HTML 模板正确处理过滤后的结果集
  - 添加过滤统计信息到 HTML 报告中
  - _Leverage: 现有的 HTML 模板和渲染机制_
  - _Requirements: 2.3, 3.1_

- [x] 7. 扩展参数验证系统

  - 在适当的验证模块中添加 `validateMinFileComplexity` 函数
  - 实现参数值验证，防止无效输入（负数、非数字等）
  - 集成到现有的参数处理流程中
  - 提供清晰的错误信息和修复建议
  - _Leverage: 现有的并发验证服务 ConcurrentValidationService_
  - _Requirements: 4.4_

- [x] 8. 确保与现有参数的兼容性

  - 测试 `--min-file-complexity` 与 `--min` 参数的协同工作
  - 验证与 `--details` 参数的兼容性
  - 确保与 `--baseline` 相关参数不冲突
  - 验证各种参数组合下的正确行为
  - _Leverage: 现有的参数处理和配置合并逻辑_
  - _Requirements: 4.1, 4.2, 4.3_

- [x] 9. 实现错误处理和优雅降级

  - 添加过滤操作的错误处理机制
  - 实现优雅降级：过滤失败时显示所有文件并记录警告
  - 确保分层错误处理策略的一致性
  - 提供用户友好的错误信息
  - _Leverage: 现有的错误处理机制和 CLIUIHelper_
  - _Requirements: 4.4, Non-functional: Reliability_

- [x] 10. 编写单元测试

  - 为 FileComplexityFilter 类编写全面的单元测试
  - 测试各种阈值下的过滤行为和边界条件
  - 验证统计信息计算的准确性
  - 测试参数验证逻辑的正确性
  - _Leverage: 现有的测试夹具和测试工具类 cli-testing-utils.ts_
  - _Requirements: All requirements through comprehensive testing_

- [x] 11. 编写集成测试

  - 实现格式化器集成测试，验证各种格式的过滤功能
  - 编写 CLI 端到端测试，验证参数传递和输出过滤
  - 测试各种输出格式的过滤行为一致性
  - 验证与现有功能的兼容性
  - _Leverage: 现有的集成测试模式和断言工具_
  - _Requirements: 2.4, 4.1, 4.2, 4.3_

- [x] 12. 添加帮助文档和使用示例
  - 在 CLI 帮助信息中添加 `--min-file-complexity` 参数说明
  - 添加使用示例展示不同阈值的效果
  - 更新相关文档说明新功能的使用方法
  - 确保参数描述清晰易懂
  - _Leverage: 现有的帮助文档结构和示例格式_
  - _Requirements: Non-functional: Usability_
