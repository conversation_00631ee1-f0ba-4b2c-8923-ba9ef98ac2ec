# Output Complexity Filter - Requirements

## Introduction

此功能为 cognitive-complexity CLI 工具添加文件级复杂度过滤能力，允许用户通过参数控制是否显示低复杂度或零复杂度的文件。当前版本显示所有扫描的文件（包括复杂度为 0 的文件），导致输出冗长，影响用户体验。此功能将提供 `--min-file-complexity` 参数，默认值为 1，自动过滤掉复杂度为 0 的文件。

## Alignment with Product Vision

此功能符合产品引导文档中的以下目标：
- **易用性**：简洁的 CLI 界面，通过减少冗余输出提升用户体验
- **灵活性**：提供可配置的过滤选项，满足不同场景需求
- **高效性**：减少不必要的输出，提升分析结果的可读性和实用性

## Codebase Analysis Summary

### 现有相关实现
- **CLI 参数处理**：`src/cli/index.ts` 使用 Commander.js 管理参数，已有类似的 `--min-complexity-increment` 参数
- **输出格式化**：`src/formatters/text.ts` 中的 `formatFileResult` 方法负责文件结果格式化
- **智能过滤器**：`src/formatters/smart-filter.ts` 已实现步骤级过滤，可参考其架构
- **配置管理**：`src/config/types.ts` 定义 CLIOptions 接口

### 可复用组件
- Commander.js 参数处理模式
- 现有格式化器的条件渲染逻辑
- 配置类型定义和验证机制

## Requirements

### Requirement 1

**User Story:** 作为开发者，我希望能够配置显示文件的最小复杂度阈值，这样我就可以专注于需要关注的复杂文件，避免被大量零复杂度文件干扰。

#### Acceptance Criteria

1. WHEN 用户运行 CLI 工具不提供 `--min-file-complexity` 参数 THEN 系统 SHALL 使用默认值 1，自动隐藏复杂度为 0 的文件
2. WHEN 用户提供 `--min-file-complexity 0` 参数 THEN 系统 SHALL 显示所有文件，包括复杂度为 0 的文件
3. WHEN 用户提供 `--min-file-complexity N` 参数（N > 0）THEN 系统 SHALL 只显示复杂度 >= N 的文件
4. WHEN 文件复杂度低于指定阈值 THEN 系统 SHALL 从输出中完全隐藏该文件（包括文件头信息）
5. WHEN 应用文件复杂度过滤后 THEN 系统 SHALL 保持汇总统计信息的准确性（仍基于所有分析的文件）

### Requirement 2

**User Story:** 作为开发者，我希望过滤功能支持所有输出格式（text、json、html），这样无论使用哪种格式都能获得一致的过滤体验。

#### Acceptance Criteria

1. WHEN 用户使用 `--format text` 参数 THEN 系统 SHALL 在文本输出中应用文件复杂度过滤
2. WHEN 用户使用 `--format json` 参数 THEN 系统 SHALL 在 JSON 输出的 results 数组中排除低复杂度文件
3. WHEN 用户使用 `--format html` 参数 THEN 系统 SHALL 在 HTML 报告中应用文件复杂度过滤
4. WHEN 应用过滤后 THEN 系统 SHALL 确保各种格式的过滤行为完全一致

### Requirement 3

**User Story:** 作为开发者，我希望在使用过滤功能时能够了解过滤统计信息，这样我就知道有多少文件被隐藏了。

#### Acceptance Criteria

1. WHEN 应用文件复杂度过滤 AND 有文件被隐藏 THEN 系统 SHALL 在汇总信息中显示过滤统计
2. WHEN 在非静默模式下运行 THEN 系统 SHALL 显示 "显示 X/Y 个文件（已隐藏 Z 个低复杂度文件）" 等信息
3. WHEN 在静默模式或 JSON 格式下运行 THEN 系统 SHALL 不显示额外的过滤统计信息
4. WHEN 没有文件被过滤时 THEN 系统 SHALL 不显示过滤统计信息

### Requirement 4

**User Story:** 作为开发者，我希望新参数与现有参数兼容，这样不会影响我当前的工作流程。

#### Acceptance Criteria

1. WHEN 用户同时使用 `--min-file-complexity` 和 `--min` 参数 THEN 系统 SHALL 正确应用两种过滤（文件级 + 函数级）
2. WHEN 用户使用 `--details` 参数 THEN `--min-file-complexity` 过滤 SHALL 仍然生效
3. WHEN 用户使用 `--baseline` 相关参数 THEN 文件过滤 SHALL 不影响基线文件的生成和比较
4. WHEN 参数值无效（如负数或非数字）THEN 系统 SHALL 显示清晰的错误信息

## Non-Functional Requirements

### Performance
- 文件过滤逻辑必须在格式化阶段执行，不影响文件分析性能
- 过滤决策的计算复杂度应为 O(1) 每个文件
- 对于大型项目（>1000文件），过滤不应增加超过 10ms 的处理时间

### Security
- 参数值验证必须防止注入攻击
- 错误信息不得泄露敏感的文件系统信息

### Reliability
- 参数解析失败时必须提供清晰的错误信息
- 过滤逻辑失败不应导致整个分析过程中断
- 必须保持所有输出格式的一致性

### Usability
- 参数名称应直观易懂（`--min-file-complexity`）
- 默认行为应符合大多数用户的期望（隐藏零复杂度文件）
- 帮助文档应包含清晰的使用示例
- 错误信息应提供具体的修复建议