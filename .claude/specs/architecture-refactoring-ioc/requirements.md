# 架构重构：IoC容器与资源生命周期管理 - 需求规范

## 概述

本规范定义了对 cognitive-complexity 分析器进行深度架构重构的需求，主要解决资源生命周期管理问题，实现控制反转(IoC)模式，并建立健壮、可维护的架构。

## 问题陈述

### 当前架构问题

**核心问题1：资源泄漏导致进程无法退出**
- `AsyncRuleEngineImpl` 构造函数中无条件创建带有 `setInterval` 的重量级监控组件
- `ParallelExecutionPool` 和 `AdvancedPerformanceMonitor` 一旦创建就持续运行
- Node.js 进程因活跃定时器无法自然退出，必须强制终止

**核心问题2：全局单例导致状态污染**
- `RuleInitializationManager` 使用单例模式管理全局状态
- 测试间状态污染，影响测试隔离性
- 紧密耦合，难以进行单元测试和依赖替换

**核心问题3：缺乏控制反转**
- 组件内部直接 `new` 复杂依赖
- 依赖关系硬编码在组件内部
- 无法在运行时动态配置或替换依赖

## 业务需求

### 需求1：资源生命周期自动管理
**用户故事：** 作为库的使用者，我希望分析完成后进程能够自然退出，无需手动终止进程。

#### 验收标准
1. WHEN 调用 `ComplexityCalculator.analyze()` 完成分析 THEN 进程能在5秒内自然退出
2. WHEN 不启用监控功能时 THEN 不应创建任何带定时器的组件
3. WHEN 启用监控功能时 THEN 分析完成后应自动清理所有定时器资源
4. WHEN 发生异常时 THEN 资源清理逻辑仍能正常执行

### 需求2：配置驱动的组件创建
**用户故事：** 作为开发者，我希望通过配置选项控制引擎的行为，而不是硬编码的组件创建。

#### 验收标准
1. WHEN 传入 `{ enableMonitoring: false }` THEN 不应创建任何监控组件
2. WHEN 传入 `{ enableMonitoring: true }` THEN 才创建性能监控组件
3. WHEN 传入插件配置 THEN 能够动态加载指定插件
4. WHEN 配置发生变更 THEN 能够重新创建对应的组件实例

### 需求3：依赖注入与组件解耦
**用户故事：** 作为系统维护者，我希望组件间依赖关系清晰明确，便于测试和扩展。

#### 验收标准
1. WHEN 创建组件时 THEN 所有依赖应通过构造函数参数注入
2. WHEN 进行单元测试时 THEN 能够轻松替换任何依赖组件
3. WHEN 组件需要依赖时 THEN 不应在内部直接创建，而应从外部注入
4. WHEN 系统运行时 THEN 依赖关系应形成清晰的有向无环图

### 需求4：工厂模式统一创建入口
**用户故事：** 作为API使用者，我希望有一个简单统一的入口来创建和使用复杂度分析器。

#### 验收标准
1. WHEN 需要分析代码时 THEN 能通过工厂方法一次性创建完整的分析器
2. WHEN 工厂创建实例时 THEN 应根据配置自动装配所有依赖
3. WHEN 分析完成时 THEN 工厂应负责清理所创建的所有资源
4. WHEN 创建失败时 THEN 工厂应清理已创建的部分资源，避免泄漏

### 需求5：优雅的API设计
**用户故事：** 作为最终用户，我希望有一个简单的静态方法来进行代码分析，无需关心复杂的生命周期管理。

#### 验收标准
1. WHEN 调用静态分析方法时 THEN 应自动处理所有资源的创建和清理
2. WHEN 分析过程中发生异常时 THEN 资源仍应被正确清理
3. WHEN 需要自定义配置时 THEN 能通过参数传入配置选项
4. WHEN 多次调用分析方法时 THEN 每次调用应完全独立，无状态污染

## 技术需求

### 需求6：废除全局单例模式
**技术要求：** 移除 `RuleInitializationManager` 单例，用工厂模式和依赖注入替代。

#### 验收标准
1. WHEN 重构完成时 THEN `src/core/rule-initialization.ts` 文件应被删除
2. WHEN 系统运行时 THEN 不应存在任何全局单例状态
3. WHEN 进行测试时 THEN 每个测试用例应有独立的组件实例
4. WHEN 并发使用时 THEN 不同分析任务应使用独立的组件实例

### 需求7：Null Object模式实现
**技术要求：** 为可选组件提供无副作用的空对象实现。

#### 验收标准
1. WHEN 禁用监控时 THEN 应使用 `LightweightExecutionPool` 替代 `ParallelExecutionPool`
2. WHEN 禁用监控时 THEN 应使用 `NullPerformanceMonitor` 替代 `AdvancedPerformanceMonitor`
3. WHEN 空对象被调用时 THEN 所有方法应为空操作，不产生任何副作用
4. WHEN 空对象被创建时 THEN 不应启动任何定时器或后台任务

### 需求8：引擎构造函数重构
**技术要求：** `AsyncRuleEngineImpl` 构造函数应完全由配置驱动。

#### 验收标准
1. WHEN 构造引擎时 THEN 构造函数应只接受一个 `CalculatorOptions` 参数
2. WHEN 处理配置时 THEN 应根据选项决定创建哪些组件
3. WHEN 配置变更时 THEN 应能创建不同的组件组合
4. WHEN 组件创建失败时 THEN 应提供清晰的错误信息

## 性能需求

### 需求9：性能不低于现有实现
**性能要求：** 重构后的性能应保持或优于现有实现。

#### 验收标准
1. WHEN 分析大型文件时 THEN 处理时间不应超过重构前的110%
2. WHEN 内存使用时 THEN 峰值内存不应超过重构前的120%
3. WHEN 并发分析时 THEN 吞吐量应保持在重构前的90%以上
4. WHEN 启动时间时 THEN 冷启动时间应保持在重构前的100%以内

### 需求10：资源使用优化
**性能要求：** 按需创建组件，避免不必要的资源消耗。

#### 验收标准
1. WHEN 不需要监控时 THEN CPU使用率应降低至少20%
2. WHEN 不加载插件时 THEN 内存使用应减少至少15%
3. WHEN 简单分析时 THEN 组件创建开销应降低至少30%
4. WHEN 批量分析时 THEN 应能复用已创建的组件实例

## 兼容性需求

### 需求11：向后兼容的公共API
**兼容性要求：** 现有的公共API接口应保持完全兼容。

#### 验收标准
1. WHEN 使用现有API时 THEN 所有方法签名应保持不变
2. WHEN 使用现有配置时 THEN 所有配置项应继续有效
3. WHEN 使用现有插件时 THEN 插件系统应正常工作
4. WHEN 使用CLI时 THEN 所有命令和参数应保持兼容

### 需求12：测试兼容性
**兼容性要求：** 现有测试应在最小修改下继续通过。

#### 验收标准
1. WHEN 运行现有测试时 THEN 至少95%的测试应无修改通过
2. WHEN 测试失败时 THEN 应仅因为内部实现变更，非功能性问题
3. WHEN 新增测试时 THEN 应覆盖新的架构组件和交互
4. WHEN 测试隔离时 THEN 测试间不应有状态污染

## 质量需求

### 需求13：代码质量提升
**质量要求：** 重构后的代码应具有更好的可维护性和可扩展性。

#### 验收标准
1. WHEN 审查代码时 THEN 循环复杂度应降低至少20%
2. WHEN 分析依赖时 THEN 模块间耦合度应降低至少30%
3. WHEN 添加新功能时 THEN 修改的文件数量应减少至少25%
4. WHEN 进行单元测试时 THEN 测试覆盖率应提升至95%以上

### 需求14：错误处理增强
**质量要求：** 提供更健壮的错误处理和恢复机制。

#### 验收标准
1. WHEN 组件创建失败时 THEN 应有清晰的错误信息和堆栈跟踪
2. WHEN 资源清理失败时 THEN 应记录警告但不阻塞主流程
3. WHEN 依赖注入失败时 THEN 应提供具体的失败原因
4. WHEN 配置错误时 THEN 应在早期阶段检测并报告错误

## 成功指标

### 功能指标
- ✅ 进程退出时间：从分析完成到进程退出 < 5秒
- ✅ 资源泄漏：0个未清理的定时器或资源句柄
- ✅ API兼容性：100%现有API保持兼容
- ✅ 测试通过率：≥95%现有测试通过

### 性能指标
- ✅ 分析性能：≥90%现有性能水平
- ✅ 内存使用：≤120%现有内存峰值
- ✅ 启动时间：≤100%现有冷启动时间
- ✅ 并发吞吐：≥90%现有并发性能

### 质量指标
- ✅ 代码复杂度：降低≥20%循环复杂度
- ✅ 模块耦合：减少≥30%模块间依赖
- ✅ 测试覆盖：≥95%代码覆盖率
- ✅ 文档完整性：100%公开API有文档

## 风险评估

### 高风险
- **兼容性破坏**：重构可能影响现有用户的代码
- **性能回退**：新架构可能引入性能开销
- **功能缺失**：重构过程中可能遗漏某些功能

### 中风险
- **测试不充分**：新架构的边界情况可能未覆盖
- **文档更新**：内部文档可能与新架构不一致
- **学习成本**：开发者需要适应新的代码结构

### 低风险
- **配置迁移**：现有配置格式基本保持不变
- **插件兼容**：插件接口设计保持稳定
- **工具链影响**：构建和部署流程基本不变

## 验收测试场景

### 场景1：基本功能验证
```typescript
// 测试脚本1：基本分析功能
const result = await ComplexityCalculator.analyze("const a = 1;");
console.log("分析完成:", result);
// 进程应在5秒内自然退出
```

### 场景2：监控功能验证
```typescript
// 测试脚本2：启用监控的分析
const result = await ComplexityCalculator.analyze("const a = 1;", { 
  enableMonitoring: true,
  monitorConfig: { collectMetrics: true }
});
console.log("监控分析完成:", result);
// 进程应在5秒内自然退出，无定时器泄漏
```

### 场景3：配置验证
```typescript
// 测试脚本3：自定义配置
const result = await ComplexityCalculator.analyze("const a = 1;", {
  enableMonitoring: false,
  plugins: [{ path: './custom-plugin.js' }],
  enabledRuleIds: ['core-complexity', 'logical-operator']
});
console.log("自定义配置分析完成:", result);
```

### 场景4：错误处理验证
```typescript
// 测试脚本4：错误处理
try {
  const result = await ComplexityCalculator.analyze("invalid syntax {{{", {
    enableMonitoring: true
  });
} catch (error) {
  console.log("错误正确捕获:", error.message);
}
// 即使发生错误，资源仍应被正确清理
```