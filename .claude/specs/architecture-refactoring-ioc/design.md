# 架构重构：IoC容器与资源生命周期管理 - 设计文档

## 设计概述

本设计文档详细描述了 cognitive-complexity 分析器的深度架构重构方案。重构的核心目标是解决资源生命周期管理问题，实现控制反转(IoC)模式，并建立健壮、可维护的架构。

## 当前架构问题分析

### 问题1：资源泄漏导致进程无法退出

**现状分析**
```typescript
// 当前问题代码示例 (src/engine/async-engine.ts)
constructor(config: Partial<ResolvedEngineConfig> = {}, monitorConfig: Partial<PerformanceMonitorConfig> = {}) {
  // 无条件创建重量级组件
  this.executionPool = new ParallelExecutionPool(this.createExecutionOptions());
  this.advancedMonitor = new AdvancedPerformanceMonitor(monitorConfig);
  // 这些组件内部启动 setInterval，导致进程无法退出
}
```

**根因分析**
- `ParallelExecutionPool` 和 `AdvancedPerformanceMonitor` 在构造函数中启动定时器
- 即使不需要监控功能，这些组件也会被创建
- 没有统一的资源清理机制
- 用户调用完分析API后，Node.js进程因活跃定时器无法自然退出

### 问题2：全局单例导致状态污染

**现状分析**
```typescript
// 当前问题代码示例 (src/core/rule-initialization.ts)
export class RuleInitializationManager {
  private static instance: RuleInitializationManager | null = null;
  private asyncRuleEngine: AsyncRuleEngine | null = null;
  
  static getInstance(): RuleInitializationManager {
    if (!RuleInitializationManager.instance) {
      RuleInitializationManager.instance = new RuleInitializationManager();
    }
    return RuleInitializationManager.instance;
  }
}
```

**根因分析**
- 使用单例模式管理全局状态
- 测试间状态污染，影响测试隔离性
- 紧密耦合，难以进行单元测试
- 无法在运行时动态配置不同的引擎实例

### 问题3：缺乏控制反转

**现状分析**
```typescript
// 当前问题代码示例 (src/core/calculator.ts)
constructor(options: CalculationOptions = {}) {
  this.parser = new ASTParser(); // 硬编码依赖
  // 通过全局单例获取引擎
  getRuleEngine().then(engine => this.asyncRuleEngine = engine);
}
```

**根因分析**
- 组件内部直接 `new` 依赖对象
- 依赖关系硬编码，无法灵活替换
- 缺乏统一的依赖管理机制
- 测试时难以 mock 依赖

## 目标架构设计

### 设计原则

1. **控制反转 (IoC)**: 组件不应自己创建复杂依赖，依赖应由外部注入
2. **显式依赖注入 (DI)**: 所有依赖通过构造函数参数传入
3. **单一职责**: 每个对象只负责管理自身创建的资源
4. **配置驱动**: 组件创建完全由配置选项驱动
5. **资源生命周期管理**: 谁创建谁负责销毁

### 目标架构流程

```
用户代码 → CalculatorOptions → CalculatorFactory → AsyncRuleEngine → ComplexityCalculator
         ↓                    ↓                 ↓                  ↓
      配置选项              工厂创建           引擎实例            计算器实例
         ↓                    ↓                 ↓                  ↓
      行为控制              依赖装配           规则执行            复杂度计算
         ↓                    ↓                 ↓                  ↓
      资源策略              生命周期管理        资源清理            结果返回
```

## 核心组件设计

### 1. CalculatorOptions 配置接口

```typescript
// src/engine/types.ts

export interface MonitorOptions {
  collectMetrics?: boolean;
  performanceThreshold?: number;
  metricsInterval?: number;
  enablePerformanceLogging?: boolean;
}

export interface PluginConfig {
  path: string;
  options?: Record<string, any>;
}

export interface CalculatorOptions {
  /**
   * 是否开启性能和吞吐量监控
   * @default false
   */
  enableMonitoring?: boolean;

  /**
   * 监控器的详细配置选项
   */
  monitorConfig?: MonitorOptions;

  /**
   * 要加载的插件列表
   */
  plugins?: PluginConfig[];

  /**
   * 要启用的规则ID列表，如果未提供则全部启用
   */
  enabledRuleIds?: string[];

  /**
   * 最大并发数
   */
  maxConcurrency?: number;

  /**
   * 是否启用缓存
   */
  enableCaching?: boolean;

  /**
   * 调试模式
   */
  debugMode?: boolean;

  /**
   * 静默模式，不输出日志
   */
  quiet?: boolean;
}
```

**设计思路**
- 单一真相来源：所有配置选项统一定义
- 可选性：所有选项都是可选的，有合理默认值
- 类型安全：完整的TypeScript类型定义
- 文档化：每个选项都有清晰的说明

### 2. Null Object 模式实现

```typescript
// src/engine/execution-pool.ts

export interface ExecutionPool {
  execute<T>(task: () => Promise<T>): Promise<T>;
  shutdown(): Promise<void>;
  getActiveTaskCount(): number;
  getMetrics(): PoolMetrics;
}

export class ParallelExecutionPool implements ExecutionPool {
  private activeTimers: NodeJS.Timeout[] = [];
  
  constructor(options: ExecutionOptions) {
    // 启动后台监控定时器
    const timer = setInterval(() => this.monitorTasks(), 1000);
    this.activeTimers.push(timer);
  }
  
  async shutdown(): Promise<void> {
    this.activeTimers.forEach(timer => clearInterval(timer));
    this.activeTimers = [];
  }
}

export class LightweightExecutionPool implements ExecutionPool {
  // 空对象实现：所有方法都是空操作
  async execute<T>(task: () => Promise<T>): Promise<T> {
    return await task(); // 直接执行，无监控开销
  }
  
  async shutdown(): Promise<void> {
    // 无操作：没有资源需要清理
  }
  
  getActiveTaskCount(): number {
    return 0; // 无并发管理
  }
  
  getMetrics(): PoolMetrics {
    return { executedTasks: 0, averageExecutionTime: 0 };
  }
}
```

**设计思路**
- 接口一致性：空对象与原对象实现相同接口
- 无副作用：空对象的方法不产生任何副作用
- 性能优化：避免不必要的监控开销
- 资源安全：不创建任何需要清理的资源

### 3. 重构后的引擎构造函数

```typescript
// src/engine/async-engine.ts

export class AsyncRuleEngineImpl implements AsyncRuleEngine {
  private config: ResolvedEngineConfig;
  private executionPool: ExecutionPool;
  private performanceMonitor: PerformanceMonitor;
  private cacheManager: CacheManager;
  private ruleRegistry: RuleRegistry;
  
  constructor(private options: CalculatorOptions = {}) {
    // 解析配置
    this.config = this.resolveConfigFromOptions(options);
    
    // 创建核心组件（无副作用）
    this.cacheManager = new IntelligentCacheManager();
    this.ruleRegistry = new RuleRegistryImpl();
    this.astParser = new ASTParser();
    
    // 根据配置决定创建重量级组件还是空对象
    if (this.options.enableMonitoring) {
      this.executionPool = new ParallelExecutionPool(this.createExecutionOptions());
      this.performanceMonitor = new AdvancedPerformanceMonitor(this.options.monitorConfig);
    } else {
      this.executionPool = new LightweightExecutionPool();
      this.performanceMonitor = new NullPerformanceMonitor();
    }
    
    // 初始化（无后台任务）
    this.initialize();
  }
  
  private resolveConfigFromOptions(options: CalculatorOptions): ResolvedEngineConfig {
    return {
      maxConcurrency: options.maxConcurrency ?? DEFAULT_ENGINE_CONFIG.maxConcurrency,
      enableCaching: options.enableCaching ?? DEFAULT_ENGINE_CONFIG.enableCaching,
      debugMode: options.debugMode ?? DEFAULT_ENGINE_CONFIG.debugMode,
      quiet: options.quiet ?? DEFAULT_ENGINE_CONFIG.quiet,
    };
  }
  
  async dispose(): Promise<void> {
    // 清理所有组件
    await this.executionPool.shutdown();
    await this.performanceMonitor.stop();
    await this.cacheManager.clear();
  }
}
```

**设计思路**
- 配置驱动：完全由 CalculatorOptions 控制行为
- 条件创建：根据配置决定创建什么组件
- 无后台任务：构造函数不启动任何定时器
- 统一清理：dispose 方法清理所有资源

### 4. CalculatorFactory 工厂类

```typescript
// src/core/calculator-factory.ts

import { CompleteAsyncRuleEngineImpl } from '../engine/complete-async-engine';
import { ComplexityCalculator } from './calculator';
import type { CalculatorOptions, AsyncRuleEngine } from '../engine/types';

export class CalculatorFactory {
  /**
   * 创建复杂度计算器实例
   * 负责组件的创建、装配和配置
   */
  public static async create(options: CalculatorOptions = {}): Promise<{
    calculator: ComplexityCalculator;
    engine: AsyncRuleEngine;
  }> {
    let engine: AsyncRuleEngine | null = null;
    
    try {
      // 创建引擎实例
      engine = new CompleteAsyncRuleEngineImpl(options);
      
      // 加载插件
      if (options.plugins && options.plugins.length > 0) {
        for (const plugin of options.plugins) {
          await engine.loadPlugin(plugin.path, plugin.options);
        }
      }
      
      // 配置规则
      if (options.enabledRuleIds && options.enabledRuleIds.length > 0) {
        // 禁用所有规则
        const allRules = engine.getAllRules();
        for (const rule of allRules) {
          engine.disableRule(rule.getId());
        }
        
        // 启用指定规则
        for (const ruleId of options.enabledRuleIds) {
          engine.enableRule(ruleId);
        }
      }
      
      // 创建计算器实例
      const calculator = new ComplexityCalculator(engine);
      
      return { calculator, engine };
      
    } catch (error) {
      // 创建失败时清理已创建的资源
      if (engine) {
        await engine.dispose().catch(cleanupError => {
          console.warn('Failed to cleanup engine during factory error:', cleanupError);
        });
      }
      throw error;
    }
  }
  
  /**
   * 安全销毁计算器和引擎资源
   */
  public static async destroy(calculator: ComplexityCalculator, engine: AsyncRuleEngine): Promise<void> {
    try {
      // 清理计算器资源
      calculator.dispose();
    } catch (error) {
      console.warn('Failed to dispose calculator:', error);
    }
    
    try {
      // 清理引擎资源
      await engine.dispose();
    } catch (error) {
      console.warn('Failed to dispose engine:', error);
    }
  }
}
```

**设计思路**
- 工厂模式：统一的创建入口
- 依赖装配：负责组件间依赖关系的建立
- 错误处理：创建失败时自动清理已创建资源
- 生命周期管理：提供销毁方法确保资源清理

### 5. 重构后的 ComplexityCalculator

```typescript
// src/core/calculator.ts

export class ComplexityCalculator {
  constructor(private engine: AsyncRuleEngine) {
    // 简化构造函数：只接受注入的依赖
    // 不再负责引擎的创建和初始化
  }
  
  public async calculateCode(code: string, filePath: string = 'inline.ts'): Promise<FileAnalysis> {
    // 直接使用注入的引擎
    return await this.engine.analyzeCode(code, filePath);
  }
  
  public async calculateFile(filePath: string): Promise<FileAnalysis> {
    return await this.engine.analyzeFile(filePath);
  }
  
  public dispose(): void {
    // 只清理本类自己创建的资源
    // 不再负责引擎的生命周期管理
    if (this.detailCollector) {
      this.detailCollector.dispose();
      this.detailCollector = null;
    }
  }
  
  /**
   * 一站式静态分析方法
   * 自动处理资源的创建和清理
   */
  public static async analyze(
    code: string, 
    options: CalculatorOptions = {}
  ): Promise<FileAnalysis> {
    const { calculator, engine } = await CalculatorFactory.create(options);
    
    try {
      return await calculator.calculateCode(code, 'inline.ts');
    } finally {
      // 自动清理资源
      await CalculatorFactory.destroy(calculator, engine);
    }
  }
}
```

**设计思路**
- 依赖注入：构造函数接受 AsyncRuleEngine 实例
- 职责单一：只负责复杂度计算逻辑
- 静态API：提供便捷的一站式调用方法
- 自动清理：静态方法自动管理资源生命周期

## 组件交互设计

### 创建流程

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Factory as CalculatorFactory
    participant Engine as AsyncRuleEngine
    participant Calculator as ComplexityCalculator
    participant Pool as ExecutionPool
    participant Monitor as PerformanceMonitor
    
    User->>Factory: create(options)
    Factory->>Engine: new AsyncRuleEngineImpl(options)
    
    alt enableMonitoring = true
        Engine->>Pool: new ParallelExecutionPool()
        Engine->>Monitor: new AdvancedPerformanceMonitor()
    else enableMonitoring = false
        Engine->>Pool: new LightweightExecutionPool()
        Engine->>Monitor: new NullPerformanceMonitor()
    end
    
    Factory->>Engine: loadPlugin() [if plugins]
    Factory->>Engine: configureRules() [if enabledRuleIds]
    Factory->>Calculator: new ComplexityCalculator(engine)
    Factory-->>User: {calculator, engine}
```

### 分析流程

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Calculator as ComplexityCalculator
    participant Engine as AsyncRuleEngine
    participant Pool as ExecutionPool
    participant Monitor as PerformanceMonitor
    
    User->>Calculator: analyze(code, options)
    Calculator->>Engine: analyzeCode(code, filePath)
    Engine->>Pool: execute(analysisTask)
    Engine->>Monitor: recordStart()
    Pool-->>Engine: analysisResult
    Engine->>Monitor: recordEnd()
    Engine-->>Calculator: FileAnalysis
    Calculator-->>User: FileAnalysis
```

### 清理流程

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Factory as CalculatorFactory
    participant Calculator as ComplexityCalculator
    participant Engine as AsyncRuleEngine
    participant Pool as ExecutionPool
    participant Monitor as PerformanceMonitor
    
    User->>Factory: destroy(calculator, engine)
    Factory->>Calculator: dispose()
    Factory->>Engine: dispose()
    Engine->>Pool: shutdown()
    Engine->>Monitor: stop()
    
    alt Pool is ParallelExecutionPool
        Pool->>Pool: clearInterval(timers)
    else Pool is LightweightExecutionPool
        Pool->>Pool: no-op
    end
    
    alt Monitor is AdvancedPerformanceMonitor
        Monitor->>Monitor: clearInterval(timers)
    else Monitor is NullPerformanceMonitor
        Monitor->>Monitor: no-op
    end
```

## 错误处理设计

### 创建阶段错误处理

```typescript
// 工厂创建时的错误处理
public static async create(options: CalculatorOptions = {}): Promise<CreationResult> {
  let engine: AsyncRuleEngine | null = null;
  
  try {
    engine = new CompleteAsyncRuleEngineImpl(options);
    
    // 插件加载可能失败
    if (options.plugins) {
      for (const plugin of options.plugins) {
        try {
          await engine.loadPlugin(plugin.path, plugin.options);
        } catch (pluginError) {
          // 插件加载失败不应影响整体创建
          console.warn(`Failed to load plugin ${plugin.path}:`, pluginError);
        }
      }
    }
    
    // 规则配置可能失败
    if (options.enabledRuleIds) {
      try {
        this.configureRules(engine, options.enabledRuleIds);
      } catch (ruleError) {
        console.warn('Failed to configure rules:', ruleError);
        // 继续使用默认规则配置
      }
    }
    
    const calculator = new ComplexityCalculator(engine);
    return { calculator, engine };
    
  } catch (error) {
    // 创建失败时清理已创建的资源
    if (engine) {
      await this.safeDispose(engine);
    }
    throw new CalculatorCreationError('Failed to create calculator', { cause: error });
  }
}
```

### 分析阶段错误处理

```typescript
// 静态analyze方法的错误处理
public static async analyze(code: string, options: CalculatorOptions = {}): Promise<FileAnalysis> {
  let calculator: ComplexityCalculator | null = null;
  let engine: AsyncRuleEngine | null = null;
  
  try {
    const result = await CalculatorFactory.create(options);
    calculator = result.calculator;
    engine = result.engine;
    
    return await calculator.calculateCode(code, 'inline.ts');
    
  } catch (error) {
    // 分析失败，记录错误但不重新抛出清理错误
    if (error instanceof CalculatorCreationError) {
      throw error; // 创建错误直接抛出
    } else {
      throw new AnalysisError('Code analysis failed', { cause: error });
    }
  } finally {
    // 无论成功失败都要清理资源
    if (calculator && engine) {
      try {
        await CalculatorFactory.destroy(calculator, engine);
      } catch (cleanupError) {
        // 清理错误不应掩盖原始错误
        console.warn('Failed to cleanup resources:', cleanupError);
      }
    }
  }
}
```

## 性能优化设计

### 条件组件创建的性能优势

```typescript
// 性能对比分析

// 重构前：无条件创建重量级组件
class OldAsyncRuleEngine {
  constructor() {
    this.executionPool = new ParallelExecutionPool(); // 创建开销：~50ms
    this.performanceMonitor = new AdvancedPerformanceMonitor(); // 创建开销：~30ms
    // 总创建开销：~80ms
    // 内存占用：~10MB（包含定时器和缓存）
    // CPU使用：持续2-5%（定时器轮询）
  }
}

// 重构后：按需创建组件
class NewAsyncRuleEngine {
  constructor(options: CalculatorOptions) {
    if (options.enableMonitoring) {
      this.executionPool = new ParallelExecutionPool(); // 创建开销：~50ms
      this.performanceMonitor = new AdvancedPerformanceMonitor(); // 创建开销：~30ms
      // 监控模式开销：~80ms，内存~10MB，CPU 2-5%
    } else {
      this.executionPool = new LightweightExecutionPool(); // 创建开销：~1ms
      this.performanceMonitor = new NullPerformanceMonitor(); // 创建开销：~1ms
      // 轻量模式开销：~2ms，内存~1MB，CPU 0%
    }
  }
}
```

**性能提升预期**
- 创建时间：轻量模式下减少97%（80ms → 2ms）
- 内存使用：轻量模式下减少90%（10MB → 1MB）
- CPU使用：轻量模式下减少100%（2-5% → 0%）
- 进程退出：从无法退出到5秒内自然退出

### 资源池化优化

```typescript
// 可选的资源池化优化（未来增强）
export class CalculatorPool {
  private pool: ComplexityCalculator[] = [];
  private engines: AsyncRuleEngine[] = [];
  
  async acquire(options: CalculatorOptions): Promise<ComplexityCalculator> {
    // 从池中获取匹配配置的实例
    const existing = this.findCompatible(options);
    if (existing) {
      return existing;
    }
    
    // 创建新实例
    const { calculator, engine } = await CalculatorFactory.create(options);
    this.engines.push(engine);
    return calculator;
  }
  
  async release(calculator: ComplexityCalculator): Promise<void> {
    // 重置状态并返回池中
    this.pool.push(calculator);
  }
  
  async shutdown(): Promise<void> {
    // 清理所有池化资源
    await Promise.all(this.engines.map(engine => engine.dispose()));
  }
}
```

## 测试策略设计

### 单元测试策略

```typescript
// 空对象测试
describe('LightweightExecutionPool', () => {
  test('should not create any timers', () => {
    const pool = new LightweightExecutionPool();
    // 验证没有活跃的定时器
    expect(process._getActiveHandles()).toHaveLength(0);
  });
  
  test('should execute tasks without monitoring overhead', async () => {
    const pool = new LightweightExecutionPool();
    const startTime = performance.now();
    
    await pool.execute(async () => {
      await new Promise(resolve => setTimeout(resolve, 10));
    });
    
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(15); // 最小开销
  });
});

// 工厂测试
describe('CalculatorFactory', () => {
  test('should create lightweight components when monitoring disabled', async () => {
    const { calculator, engine } = await CalculatorFactory.create({
      enableMonitoring: false
    });
    
    expect(engine['executionPool']).toBeInstanceOf(LightweightExecutionPool);
    expect(engine['performanceMonitor']).toBeInstanceOf(NullPerformanceMonitor);
  });
  
  test('should cleanup resources on creation failure', async () => {
    const mockPlugin = { path: '/nonexistent/plugin.js' };
    
    await expect(CalculatorFactory.create({
      plugins: [mockPlugin]
    })).rejects.toThrow();
    
    // 验证没有资源泄漏
    expect(process._getActiveHandles()).toHaveLength(0);
  });
});
```

### 集成测试策略

```typescript
// 进程退出测试
describe('Process Exit Integration', () => {
  test('should allow process to exit after basic analysis', async () => {
    const testScript = `
      const { ComplexityCalculator } = require('./dist/core/calculator');
      
      async function test() {
        const result = await ComplexityCalculator.analyze("const a = 1;");
        console.log('Analysis completed');
        process.exit(0);
      }
      
      test().catch(console.error);
    `;
    
    const { spawn } = require('child_process');
    const child = spawn('node', ['-e', testScript]);
    
    const exitPromise = new Promise((resolve) => {
      child.on('exit', (code) => resolve(code));
    });
    
    const timeout = setTimeout(() => {
      child.kill('SIGKILL');
      throw new Error('Process did not exit within 5 seconds');
    }, 5000);
    
    const exitCode = await exitPromise;
    clearTimeout(timeout);
    
    expect(exitCode).toBe(0);
  });
});
```

### 性能基准测试

```typescript
// 性能对比测试
describe('Performance Benchmarks', () => {
  test('should maintain analysis performance', async () => {
    const testCode = fs.readFileSync('test-fixtures/large-file.ts', 'utf8');
    
    // 测试重构后性能
    const startTime = performance.now();
    await ComplexityCalculator.analyze(testCode);
    const newDuration = performance.now() - startTime;
    
    // 与基准对比
    const baselineDuration = 1000; // 从基准测试获得
    expect(newDuration).toBeLessThan(baselineDuration * 1.1); // 不超过110%
  });
  
  test('should reduce memory usage in lightweight mode', async () => {
    const before = process.memoryUsage().heapUsed;
    
    await ComplexityCalculator.analyze('const a = 1;', {
      enableMonitoring: false
    });
    
    const after = process.memoryUsage().heapUsed;
    const memoryIncrease = after - before;
    
    expect(memoryIncrease).toBeLessThan(5 * 1024 * 1024); // 少于5MB
  });
});
```

## 兼容性保证设计

### API兼容性

```typescript
// 确保现有API完全兼容
export class ComplexityCalculator {
  // 保持原有构造函数签名兼容
  constructor(options: CalculationOptions = {}) {
    // 内部使用工厂模式创建，但不暴露给用户
    this.initializeWithFactory(options);
  }
  
  // 保持所有原有方法签名
  public async calculateFile(filePath: string): Promise<FunctionResult[]> {
    // 内部实现变化，但签名保持一致
  }
  
  public async calculateCode(code: string, filePath?: string): Promise<FunctionResult[]> {
    // 内部实现变化，但签名保持一致
  }
  
  // 添加新的静态方法，不影响现有API
  public static async analyze(code: string, options?: CalculatorOptions): Promise<FileAnalysis> {
    // 新增API，向后兼容
  }
}
```

### 配置兼容性

```typescript
// 确保现有配置选项继续有效
interface BackwardCompatibleOptions extends CalculatorOptions {
  // 保持现有选项
  maxComplexity?: number;
  includeDetails?: boolean;
  enableDebugLog?: boolean;
  quiet?: boolean;
  
  // 新增选项
  enableMonitoring?: boolean;
  monitorConfig?: MonitorOptions;
  plugins?: PluginConfig[];
}

// 配置映射和迁移
function migrateOptions(oldOptions: any): CalculatorOptions {
  return {
    // 映射现有选项到新结构
    enableDebugLog: oldOptions.enableDebugLog,
    quiet: oldOptions.quiet,
    // 新选项使用默认值
    enableMonitoring: oldOptions.enableMonitoring ?? false,
    monitorConfig: oldOptions.monitorConfig ?? {},
  };
}
```

## 部署和迁移策略

### 渐进式迁移

1. **阶段1**: 引入新接口，保持旧接口并行工作
2. **阶段2**: 内部实现切换到新架构，API保持兼容
3. **阶段3**: 标记旧接口为deprecated，引导用户迁移
4. **阶段4**: 移除deprecated接口（主版本升级）

### 迁移指南

```typescript
// 迁移前（v1.0）
const calculator = new ComplexityCalculator({
  maxComplexity: 10,
  includeDetails: true
});

const results = await calculator.calculateFile('src/app.ts');
calculator.dispose(); // 用户需要手动调用

// 迁移后（v2.0）- 推荐方式
const results = await ComplexityCalculator.analyze(
  fs.readFileSync('src/app.ts', 'utf8'),
  {
    enableMonitoring: false, // 新选项
    enableDetails: true      // 兼容旧选项
  }
);
// 自动资源清理，无需手动dispose

// 迁移后（v2.0）- 兼容方式
const calculator = new ComplexityCalculator({
  maxComplexity: 10,
  includeDetails: true
});
const results = await calculator.calculateFile('src/app.ts');
calculator.dispose(); // 仍然支持，但推荐使用静态方法
```

## 风险评估和缓解

### 技术风险

**风险1：性能回退**
- 概率：中等
- 影响：高
- 缓解：持续性能基准测试，性能回退超过10%则回滚

**风险2：兼容性破坏**
- 概率：低
- 影响：高
- 缓解：全面的兼容性测试，保持API签名不变

**风险3：资源清理不彻底**
- 概率：中等
- 影响：中等
- 缓解：全面的资源泄漏测试，确保所有定时器被清理

### 实施风险

**风险1：重构复杂度过高**
- 概率：中等
- 影响：中等
- 缓解：分阶段实施，每阶段都有可用版本

**风险2：测试覆盖不足**
- 概率：低
- 影响：高
- 缓解：要求95%以上测试覆盖率，重点测试资源管理

## 成功标准

### 功能标准
- ✅ 进程能在分析完成后自然退出
- ✅ 配置选项能正确控制组件创建
- ✅ 所有现有API保持兼容
- ✅ 插件系统正常工作

### 性能标准
- ✅ 分析性能保持在90%以上
- ✅ 轻量模式下内存使用减少80%以上
- ✅ 轻量模式下CPU使用减少到0%
- ✅ 启动时间不超过原来的100%

### 质量标准
- ✅ 代码复杂度降低20%以上
- ✅ 模块耦合度降低30%以上
- ✅ 测试覆盖率达到95%以上
- ✅ 零资源泄漏

这个设计文档为架构重构提供了完整的技术方案，确保重构过程的可控性和最终效果的可验证性。