# Parameter Validation Enhancement Requirements

## 功能概述

增强CLI参数验证系统，提供更严格的参数联动检查。基于现有问题案例（如 `--context-lines` 需要 `--show-context` 或 `--show-all-context`），系统性地分析和验证所有相关的参数依赖关系，确保用户获得清晰的错误提示而不是静默失败或无效参数。

## 现有系统分析

### 当前验证架构
基于代码分析，发现系统已有完善的验证架构：

1. **验证框架** (`src/utils/concurrent-validation-service.ts`)
   - `ValidationRule` 接口支持优先级和并发验证
   - 已有4个验证规则：details-context依赖、智能过滤、路径验证、输出格式验证
   - 支持错误和警告两种反馈级别

2. **现有联动验证**
   - ✅ `--show-context` → 需要 `--details`
   - ✅ `--show-all-context` → 需要 `--details`  
   - ✅ `--context-lines` → 需要 `--show-context` 或 `--show-all-context`
   - ✅ `--max-context-items` 和 `--min-complexity-increment` 范围验证

3. **待完善的依赖关系**
   通过CLI参数分析，发现以下潜在的参数联动需要验证：

## Requirements

### Requirement 1: 调试参数联动验证
**User Story:** 作为开发者，我希望在使用调试相关参数时得到正确的依赖提示，以便有效地进行问题排查

#### 当前发现的调试参数依赖
- `--debug-level` → 应该需要 `--debug`
- `--debug-output` → 应该需要 `--debug`
- `--visual-report` → 应该需要 `--debug`
- `--break-on-rule` → 应该需要 `--enable-breakpoints`
- `--break-on-complexity` → 应该需要 `--enable-breakpoints`
- `--step-by-step` → 应该需要 `--enable-breakpoints`

#### Acceptance Criteria
1. WHEN user specifies `--debug-level` without `--debug` THEN system SHALL display warning "--debug-level 参数仅在使用 --debug 时有效"
2. WHEN user specifies `--debug-output` without `--debug` THEN system SHALL display warning "--debug-output 参数仅在使用 --debug 时有效"
3. WHEN user specifies `--visual-report` without `--debug` THEN system SHALL display warning "--visual-report 参数仅在使用 --debug 时有效"
4. WHEN user specifies `--break-on-rule` without `--enable-breakpoints` THEN system SHALL display warning "--break-on-rule 参数仅在使用 --enable-breakpoints 时有效"
5. WHEN user specifies `--break-on-complexity` without `--enable-breakpoints` THEN system SHALL display warning "--break-on-complexity 参数仅在使用 --enable-breakpoints 时有效"
6. WHEN user specifies `--step-by-step` without `--enable-breakpoints` THEN system SHALL display warning "--step-by-step 参数仅在使用 --enable-breakpoints 时有效"

### Requirement 2: UI参数联动验证
**User Story:** 作为用户，我希望在使用UI相关参数时得到正确的依赖提示，避免参数组合无效

#### 当前发现的UI参数依赖
- `--open` → 应该需要 `--ui`

#### Acceptance Criteria
1. WHEN user specifies `--open` without `--ui` THEN system SHALL display warning "--open 参数仅在使用 --ui 时有效"

### Requirement 3: 输出目录参数联动验证
**User Story:** 作为用户，我希望在指定输出相关参数时得到合理的组合建议，确保输出功能正常工作

#### 潜在的输出参数依赖
基于用户体验考虑，某些组合可能需要提示：
- `--output-dir` 与 `--format` 的合理性检查
- 文件输出格式与内容详细程度的建议

#### Acceptance Criteria
1. WHEN user specifies `--output-dir` with `format` other than `json` or `html` THEN system SHOULD provide info "建议使用 --format json 或 --format html 以获得更好的文件输出效果"

### Requirement 4: 智能过滤参数联动验证  
**User Story:** 作为用户，我希望智能过滤参数只在有意义的上下文中生效，避免参数被忽略

#### 当前智能过滤参数分析
- `--max-context-items` → 仅在显示上下文时有效（需要 `--show-context` 或 `--show-all-context`）
- `--min-complexity-increment` → 仅在显示上下文时有效

#### Acceptance Criteria  
1. WHEN user specifies `--max-context-items` without context display flags THEN system SHALL display warning "--max-context-items 参数仅在使用 --show-context 或 --show-all-context 时有效"
2. WHEN user specifies `--min-complexity-increment` without context display flags THEN system SHALL display warning "--min-complexity-increment 参数仅在使用 --show-context 或 --show-all-context 时有效"

### Requirement 5: 验证规则统一管理
**User Story:** 作为系统维护者，我希望所有参数联动验证规则集中管理，便于维护和扩展

#### Acceptance Criteria
1. WHEN new parameter dependencies are identified THEN they SHALL be added to the validation rule system
2. IF validation rule conflicts occur THEN system SHALL use priority-based resolution
3. WHEN validation rules execute THEN they SHALL provide consistent error message formatting
4. IF parameter validation fails THEN system SHALL continue execution but display clear warnings

### Requirement 6: 验证反馈优化
**User Story:** 作为用户，我希望参数验证的反馈信息清晰易懂，包含解决建议

#### Acceptance Criteria
1. WHEN parameter dependency validation fails THEN error message SHALL include the missing required parameter
2. IF validation warning occurs THEN message SHALL explain why the parameter is ineffective
3. WHEN multiple validation issues exist THEN system SHALL display them in logical priority order
4. IF user wants to suppress warnings THEN system SHALL provide a way to acknowledge parameter combinations

## 技术要求

### 功能要求

#### FR-1: 扩展验证规则系统
- **需求**: 在现有 `concurrent-validation-service.ts` 中添加新的验证规则
- **范围**: 调试参数、UI参数、智能过滤参数的依赖验证
- **实现**: 创建 `debugParameterValidationRule`、`uiParameterValidationRule`、`smartFilterDependencyRule`

#### FR-2: 保持验证架构一致性
- **需求**: 新验证规则遵循现有的 `ValidationRule` 接口
- **范围**: 保持错误/警告分离、优先级管理、并发执行兼容
- **约束**: 不破坏现有验证逻辑

#### FR-3: 错误信息标准化
- **需求**: 统一所有参数依赖错误信息的格式和语调
- **格式**: "[参数] 参数仅在使用 [依赖参数] 时有效"
- **语言**: 保持中文提示的一致性

### 非功能要求

#### NFR-1: 性能要求
- 验证规则执行不应显著增加CLI启动时间
- 并发验证机制保持高效

#### NFR-2: 可维护性
- 新增验证规则应该易于理解和修改
- 验证逻辑与业务逻辑分离

#### NFR-3: 用户体验
- 验证失败时仍允许工具继续执行（警告而非错误）
- 错误信息提供足够的上下文帮助用户理解和修复

## 实现优先级

### P1 (高优先级)
1. 调试参数联动验证 (最常用的功能)
2. UI参数联动验证 (明确的依赖关系)
3. 智能过滤参数联动验证 (与现有context-lines逻辑一致)

### P2 (中优先级)
1. 输出目录参数建议
2. 验证反馈优化
3. 错误信息标准化

### P3 (低优先级)
1. 验证规则管理工具
2. 参数组合分析报告
3. 高级警告配置选项

## 成功标准

### 功能验收标准
1. ✅ 所有发现的参数依赖关系都有适当的验证
2. ✅ 验证失败时显示清晰的警告信息
3. ✅ 验证系统性能影响小于50ms
4. ✅ 现有验证逻辑保持不变

### 质量验收标准
1. ✅ 新验证规则单元测试覆盖率 ≥ 95%
2. ✅ 集成测试验证所有参数组合场景
3. ✅ 向后兼容性测试通过
4. ✅ 错误信息一致性验证

## 风险评估

### 技术风险
- **低风险**: 基于现有成熟的验证架构，实现风险较低
- **中等风险**: 需要全面分析CLI参数依赖，可能遗漏某些组合

### 用户体验风险
- **低风险**: 警告信息可能增多，但有助于用户理解参数用法
- **低风险**: 不影响核心功能，仅改善用户反馈

## 结论

这是一个有价值的用户体验改进，通过系统性地完善参数验证，可以显著提升CLI工具的易用性。基于现有的良好验证架构，实现难度相对较低，但需要仔细分析所有参数的依赖关系以确保完整性。