# Parameter Validation Enhancement Design

## 设计概述

基于requirements分析，我们需要在现有的并发验证架构上扩展新的验证规则，以增强CLI参数的依赖关系检查。设计目标是保持架构一致性的同时，系统性地解决参数联动验证问题。

## 架构设计

### 现有验证架构分析

通过代码分析，当前系统已有完善的验证框架：

#### 核心组件
```typescript
// src/utils/concurrent-validation-service.ts
interface ValidationRule {
  name: string;
  priority: number;
  validate(options: CLIOptions): Promise<ValidationResult>;
}

interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}
```

#### 现有验证规则
1. `detailsContextDependencyRule` (优先级: 1) - 上下文依赖验证
2. `smartFilterValidationRule` (优先级: 2) - 智能过滤验证  
3. `pathValidationRule` (优先级: 3) - 路径验证
4. `outputFormatValidationRule` (优先级: 4) - 输出格式验证

### 新增验证规则设计

基于需求分析，我们需要添加以下验证规则：

#### 1. Debug Parameter Validation Rule
**名称**: `debugParameterValidationRule`
**优先级**: 5 (在现有规则之后)
**职责**: 验证调试相关参数的依赖关系

```typescript
const debugParameterValidationRule: ValidationRule = {
  name: 'debug-parameter-validation',
  priority: 5,
  validate: async (options: CLIOptions) => {
    const warnings: string[] = [];
    
    // --debug-level 需要 --debug
    if (options.debugLevel && !options.debug) {
      warnings.push('--debug-level 参数仅在使用 --debug 时有效');
    }
    
    // --debug-output 需要 --debug
    if (options.debugOutput && !options.debug) {
      warnings.push('--debug-output 参数仅在使用 --debug 时有效');
    }
    
    // --visual-report 需要 --debug
    if (options.visualReport && !options.debug) {
      warnings.push('--visual-report 参数仅在使用 --debug 时有效');
    }
    
    return {
      valid: true, // 警告不影响程序继续执行
      errors: [],
      warnings
    };
  }
};
```

#### 2. Breakpoint Parameter Validation Rule
**名称**: `breakpointParameterValidationRule`
**优先级**: 6
**职责**: 验证断点调试参数的依赖关系

```typescript
const breakpointParameterValidationRule: ValidationRule = {
  name: 'breakpoint-parameter-validation',
  priority: 6,
  validate: async (options: CLIOptions) => {
    const warnings: string[] = [];
    
    // --break-on-rule 需要 --enable-breakpoints
    if (options.breakOnRule && !options.enableBreakpoints) {
      warnings.push('--break-on-rule 参数仅在使用 --enable-breakpoints 时有效');
    }
    
    // --break-on-complexity 需要 --enable-breakpoints
    if (options.breakOnComplexity && !options.enableBreakpoints) {
      warnings.push('--break-on-complexity 参数仅在使用 --enable-breakpoints 时有效');
    }
    
    // --step-by-step 需要 --enable-breakpoints
    if (options.stepByStep && !options.enableBreakpoints) {
      warnings.push('--step-by-step 参数仅在使用 --enable-breakpoints 时有效');
    }
    
    return {
      valid: true,
      errors: [],
      warnings
    };
  }
};
```

#### 3. UI Parameter Validation Rule
**名称**: `uiParameterValidationRule`
**优先级**: 7
**职责**: 验证UI相关参数的依赖关系

```typescript
const uiParameterValidationRule: ValidationRule = {
  name: 'ui-parameter-validation',
  priority: 7,
  validate: async (options: CLIOptions) => {
    const warnings: string[] = [];
    
    // --open 需要 --ui
    if (options.open && !options.ui) {
      warnings.push('--open 参数仅在使用 --ui 时有效');
    }
    
    return {
      valid: true,
      errors: [],
      warnings
    };
  }
};
```

#### 4. Smart Filter Dependency Rule
**名称**: `smartFilterDependencyRule`
**优先级**: 8
**职责**: 验证智能过滤参数的依赖关系

```typescript
const smartFilterDependencyRule: ValidationRule = {
  name: 'smart-filter-dependency',
  priority: 8,
  validate: async (options: CLIOptions) => {
    const warnings: string[] = [];
    const hasContextDisplay = options.showContext || options.showAllContext;
    
    // --max-context-items 需要上下文显示
    if (options.maxContextItems !== undefined && !hasContextDisplay) {
      warnings.push('--max-context-items 参数仅在使用 --show-context 或 --show-all-context 时有效');
    }
    
    // --min-complexity-increment 需要上下文显示
    if (options.minComplexityIncrement !== undefined && !hasContextDisplay) {
      warnings.push('--min-complexity-increment 参数仅在使用 --show-context 或 --show-all-context 时有效');
    }
    
    return {
      valid: true,
      errors: [],
      warnings
    };
  }
};
```

#### 5. Output Enhancement Suggestion Rule
**名称**: `outputEnhancementSuggestionRule`
**优先级**: 9
**职责**: 为输出参数提供优化建议

```typescript
const outputEnhancementSuggestionRule: ValidationRule = {
  name: 'output-enhancement-suggestion',
  priority: 9,
  validate: async (options: CLIOptions) => {
    const warnings: string[] = [];
    
    // 建议使用更好的输出格式
    if (options.outputDir && options.format === 'text') {
      warnings.push('建议使用 --format json 或 --format html 以获得更好的文件输出效果');
    }
    
    return {
      valid: true,
      errors: [],
      warnings
    };
  }
};
```

## CLI选项类型扩展

需要确保`CLIOptions`接口包含所有需要验证的参数：

```typescript
// src/config/types.ts 需要确保包含：
interface CLIOptions {
  // 调试参数
  debug?: boolean;
  debugLevel?: string;
  debugOutput?: string;
  visualReport?: boolean;
  
  // 断点参数
  enableBreakpoints?: boolean;
  breakOnRule?: string[];
  breakOnComplexity?: number;
  stepByStep?: boolean;
  
  // UI参数
  ui?: boolean;
  open?: boolean;
  
  // 智能过滤参数
  showContext?: boolean;
  showAllContext?: boolean;
  maxContextItems?: number;
  minComplexityIncrement?: number;
  
  // 输出参数
  outputDir?: string;
  format?: string;
  
  // ... 其他现有参数
}
```

## 验证服务集成

### 规则注册

在`concurrent-validation-service.ts`中注册新规则：

```typescript
export function getDefaultValidationService(): ConcurrentValidationService {
  const service = new ConcurrentValidationService();
  
  // 现有规则
  service.addRule(detailsContextDependencyRule);
  service.addRule(smartFilterValidationRule);
  service.addRule(pathValidationRule);
  service.addRule(outputFormatValidationRule);
  
  // 新增规则
  service.addRule(debugParameterValidationRule);
  service.addRule(breakpointParameterValidationRule);
  service.addRule(uiParameterValidationRule);
  service.addRule(smartFilterDependencyRule);
  service.addRule(outputEnhancementSuggestionRule);
  
  return service;
}
```

### 执行流程

验证流程保持不变，继续使用现有的并发执行机制：

1. CLI参数解析完成后调用验证服务
2. 所有规则按优先级并发执行
3. 收集所有验证结果
4. 统一显示警告和错误信息
5. 如有错误则中断执行，警告则继续

## 错误信息标准化

### 信息格式规范

所有参数依赖警告使用统一格式：
```
[参数名] 参数仅在使用 [依赖参数] 时有效
```

### 信息显示优化

在`src/cli/commands.ts`的验证结果处理中，按优先级顺序显示警告：

```typescript
// 显示警告信息
if (summary.warnings.length > 0 && !options.quiet) {
  summary.warnings.forEach(warning => {
    this.ui.warning(warning);
  });
}
```

## 性能考虑

### 执行效率

- 新增验证规则为简单的条件判断，执行时间预计<1ms
- 并发执行机制确保总体验证时间不超过50ms
- 不增加额外的文件I/O或网络请求

### 内存使用

- 验证规则只在验证期间加载，验证完成后可被垃圾回收
- 不持久化验证状态，内存占用minimal

## 测试策略

### 单元测试

为每个新增验证规则创建独立的测试文件：

```typescript
// src/__test__/utils/debug-parameter-validation.test.ts
// src/__test__/utils/breakpoint-parameter-validation.test.ts
// src/__test__/utils/ui-parameter-validation.test.ts
// src/__test__/utils/smart-filter-dependency.test.ts
// src/__test__/utils/output-enhancement-suggestion.test.ts
```

### 集成测试

扩展现有的CLI集成测试，验证参数组合场景：

```typescript
// src/__test__/integration/parameter-validation.test.ts
```

### 测试用例设计

每个验证规则需要覆盖：
- 正常情况（依赖参数存在）
- 异常情况（依赖参数缺失）
- 边界情况（参数为undefined/null/空值）
- 组合情况（多个参数同时存在/缺失）

## 向后兼容性

### 兼容性保证

- 新增验证规则产生的是警告，不是错误
- 不改变任何现有的CLI行为
- 不修改现有的参数解析逻辑
- 不影响现有的配置文件加载

### 迁移路径

- 用户无需修改现有的脚本或配置
- 现有的参数组合继续正常工作
- 新的警告信息帮助用户优化参数使用

## 实现计划

### 第一阶段：核心验证规则
1. 实现debugParameterValidationRule
2. 实现breakpointParameterValidationRule
3. 实现uiParameterValidationRule
4. 更新CLIOptions类型定义
5. 集成到验证服务中

### 第二阶段：增强功能
1. 实现smartFilterDependencyRule
2. 实现outputEnhancementSuggestionRule
3. 优化错误信息显示
4. 完善测试覆盖

### 第三阶段：质量保证
1. 性能测试和优化
2. 向后兼容性验证
3. 用户体验测试
4. 文档更新

## 质量指标

### 功能指标
- 新增验证规则覆盖率：100%
- 参数依赖关系识别准确率：≥95%
- 错误信息一致性：100%

### 性能指标
- 验证执行时间：<50ms
- 内存增加：<1MB
- 启动时间影响：<10ms

### 用户体验指标
- 错误信息可理解性：用户测试通过率≥90%
- 向后兼容性：现有脚本100%正常运行
- 警告信息有用性：用户反馈满意度≥85%

## 结论

这个设计充分利用了现有的并发验证架构，通过系统性地添加新验证规则来增强参数依赖检查。设计保持了架构一致性，确保了向后兼容性，并提供了清晰的实现路径。通过分阶段实施，可以逐步完善参数验证功能，显著提升CLI工具的用户体验。