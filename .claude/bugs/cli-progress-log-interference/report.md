# Bug Report

## Bug Summary
CLI 进度条输出被规则注册日志打乱，导致原地更新进度条被破坏，影响用户体验

## Bug Details

### Expected Behavior
进度条应该在同一位置平滑更新，显示分析进度，不应有其他日志信息干扰

### Actual Behavior  
在进度条显示过程中，规则注册的日志信息（如 "Successfully registered 22 complexity rules", "Rule 'logical-operators' registered successfully" 等）被打印出来，打乱了进度条的原地更新，导致进度条显示混乱

### Steps to Reproduce
1. 运行 cognitive-complexity CLI 工具
2. 观察分析进度条的显示
3. 注意到规则注册日志与进度条混在一起显示

### Environment
- **Version**: 当前开发版本
- **Platform**: CLI 工具
- **Configuration**: 默认配置

## Impact Assessment

### Severity
- [ ] Critical - System unusable
- [ ] High - Major functionality broken
- [x] Medium - Feature impaired but workaround exists
- [ ] Low - Minor issue or cosmetic

### Affected Users
所有使用 CLI 工具的用户

### Affected Features
- 进度条显示功能
- 用户体验（UI/UX）
- 日志输出的清晰度

## Additional Context

### Error Messages
```
Successfully registered 22 complexity rules
Rule 'logical-operators' registered successfully
Rule 'logical-operators' registered in engine
分析进度 |░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░| 0% | 0/4553 | ETA: 0s | 准备中...Rule 'logical-recursion' registered successfully
Rule 'logical-recursion' registered in engine
All class-based rules registered successfully
分析进度 |███
```

### Screenshots/Media
无

### Related Issues
可能与 cli-progress 库的使用方式和日志输出时机有关

## Initial Analysis

### Suspected Root Cause
1. 规则注册过程中的日志输出与进度条输出在时间上冲突
2. 可能缺乏对进度条状态的管理，在进度条活跃时没有抑制其他日志输出
3. 日志级别控制不当，调试信息在用户界面中显示

### Affected Components
- CLI 进度条相关代码
- 规则注册系统的日志输出
- 可能涉及 src/cli/ 下的相关文件
- src/core/rule-registry.ts 或类似的规则管理文件