# Bug Report

## Bug Summary
LogicalOperatorRule类在代码库中已定义但未被实际使用，存在潜在的代码未集成问题

## Bug Details

### Expected Behavior
LogicalOperatorRule应该在复杂度计算系统中被正确注册和使用，处理逻辑运算符的复杂度分析

### Actual Behavior  
LogicalOperatorRule类已实现完整功能，但只在测试文件中被引用，未在生产代码中被实际使用。系统使用传统的函数式方法（getLogicalOperatorRuleId）而非规则类

### Steps to Reproduce
1. 检查src/rules/logical-operator-rule.ts文件
2. 搜索LogicalOperatorRule的使用情况
3. 发现只在测试文件src/__test__/engine/registry.test.ts中被引用
4. 观察到生产代码使用getLogicalOperatorRuleId函数而非LogicalOperatorRule类

### Environment
- **Version**: 当前开发版本
- **Platform**: TypeScript/Node.js项目
- **Configuration**: 认知复杂度分析工具

## Impact Assessment

### Severity
- [ ] Critical - System unusable
- [ ] High - Major functionality broken
- [x] Medium - Feature impaired but workaround exists
- [ ] Low - Minor issue or cosmetic

### Affected Users
开发者和维护者，影响代码一致性和架构完整性

### Affected Features
逻辑运算符复杂度分析功能（目前通过备用实现工作）

## Additional Context

### Code Analysis
```typescript
// 当前生产代码使用：
// src/core/calculator.ts:510 和 530 行
ruleId = getLogicalOperatorRuleId((node as any).operator);

// 而不是使用定义好的规则类：
// src/rules/logical-operator-rule.ts:14
export class LogicalOperatorRule extends BaseRule {
```

### Current Usage Pattern
系统当前使用传统的函数式规则ID获取方式，而LogicalOperatorRule提供了更现代的基于类的规则实现，包含详细的复杂度分析逻辑。

## Initial Analysis

### Suspected Root Cause
可能原因：
1. 新的规则类系统尚未完全集成到主要的复杂度计算流程中
2. 存在新旧两套规则系统并行的情况
3. LogicalOperatorRule是为新架构设计但迁移未完成

### Affected Components
- `src/rules/logical-operator-rule.ts` - 未使用的规则类
- `src/core/calculator.ts` - 使用旧的函数式方法
- `src/core/default-rules.ts` - 包含getLogicalOperatorRuleId函数
- `src/core/rule-registry.ts` - 可能需要注册新规则
- `src/__test__/engine/registry.test.ts` - 测试引用但生产环境未使用