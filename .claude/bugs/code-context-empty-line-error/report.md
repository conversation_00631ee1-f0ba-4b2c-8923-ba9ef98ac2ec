# Bug Report

## Bug Summary
代码上下文显示异常：大量出现指向空行的复杂度分析结果，显示错误的代码框架

## Bug Details

### Expected Behavior  
代码上下文应该显示实际包含逻辑的代码行，而不是空行或闭合花括号行。对于函数或类型定义的复杂度分析，应该指向实际的逻辑代码位置。

### Actual Behavior
在复杂度分析输出中，大批量出现指向文件末尾空行的代码上下文，例如：
- `formMeta.tsx` 第66行：指向空行
- `OptionContent/index.tsx` 第41行：指向空行  
- 这些行都显示复杂度为1.00，但指向的是文件结尾的空行而非实际逻辑

### Steps to Reproduce
1. 运行复杂度分析工具
2. 分析包含导出对象的TypeScript/JSX文件
3. 观察输出的代码上下文部分
4. 发现大量指向空行的复杂度分析结果

### Environment
- **Version**: cognitive-complexity CLI工具
- **Platform**: Node.js + TypeScript/JSX项目
- **Configuration**: 默认配置，分析React/TypeScript代码库

## Impact Assessment

### Severity
- [x] Medium - Feature impaired but workaround exists

### Affected Users
所有使用该工具分析TypeScript/JSX代码的开发者

### Affected Features
- 代码上下文显示功能
- 复杂度分析的可读性和准确性
- 开发者体验和工具可信度

## Additional Context

### Error Messages
无直接错误信息，但输出显示：
```
📊 错误恢复: 2次尝试, 策略: generate-frame
```

### Screenshots/Media
用户提供的示例输出显示多个文件都出现相同问题，代码上下文指向空行

### Related Issues
可能与以下组件相关：
- 代码框架生成器 (`CodeFrameGenerator`)
- 文本格式化器 (`TextFormatter`) 
- 位置转换器 (`PositionConverter`)

## Initial Analysis

### Suspected Root Cause
1. **位置计算错误**: SWC AST span到行列位置的转换可能有偏差，导致指向错误的行号
2. **代码框架生成失败**: `CodeFrameGenerator.generateFallbackFrame()` 可能在某些场景下被触发
3. **复杂度规则定位问题**: 复杂度规则可能错误地将复杂度归因到错误的代码位置

### Affected Components
- `src/utils/code-frame-generator.ts:246` - `generateFallbackFrame` 方法
- `src/formatters/text.ts` - 代码上下文格式化逻辑
- `src/utils/position-converter.ts` - span到位置的转换
- `src/core/calculator.ts` - 复杂度计算和位置定位
- 相关的复杂度规则实现

### Technical Investigation Required
1. 验证SWC解析结果与实际代码行的对应关系
2. 检查位置转换的准确性
3. 分析为什么触发了错误恢复机制
4. 确认复杂度规则的位置计算逻辑

### Potential Fix Directions
1. **改进位置计算**: 确保AST节点位置正确映射到源代码行
2. **增强错误恢复**: 当无法生成有效代码框架时，提供更有意义的fallback
3. **规则位置优化**: 调整复杂度规则，确保指向实际包含逻辑的代码行
4. **调试信息优化**: 提供更详细的调试信息帮助定位问题