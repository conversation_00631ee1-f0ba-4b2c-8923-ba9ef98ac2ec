# Bug Report

## Bug Summary
Import语句被错误识别为逻辑运算符并增加复杂度+1，导致复杂度计算结果不准确。

## Bug Details

### Expected Behavior
Import语句不应该被算入函数复杂度，因为它们是模块导入声明，不包含任何逻辑分支或控制流。

### Actual Behavior  
Import语句（特别是多行import）被logical-operator规则错误识别并增加复杂度+1，如下例所示：

```
- L4: +1 (累计: 1) - ❓ 逻辑运算符 [logical-operator] (嵌套层级: 0)

  ┌─ 代码上下文 ─────
  │   2 |
  │   3 | /* eslint-disable @typescript-eslint/no-explicit-any */
  │ > 4 | import {
  │     | ^
  │   5 |   Ellipsis, enqueueSnackbar, useFireExport,
  │   6 | } from '@imile/components'
```

### Steps to Reproduce
1. 创建一个包含多行import语句的TypeScript/JavaScript文件
2. 运行complexity分析工具
3. 观察import语句被标记为逻辑运算符并增加复杂度
4. 检查复杂度计算结果偏高

### Environment
- **Version**: cognitive-complexity CLI当前版本
- **Platform**: Node.js 18+
- **Configuration**: 默认配置，使用logical-operator规则

## Impact Assessment

### Severity
- [x] Medium - Feature impaired but workaround exists

### Affected Users
所有使用该工具分析包含多行import语句的JavaScript/TypeScript代码的用户。

### Affected Features
- 复杂度计算准确性
- 代码质量评估结果
- 可能影响CI/CD流程中的质量门禁

## Additional Context

### Error Messages
无直接错误消息，但输出显示import语句被错误分类：
```
- L4: +1 (累计: 1) - ❓ 逻辑运算符 [logical-operator] (嵌套层级: 0)
```

### Related Issues
可能与其他AST节点类型的误识别相关，需要检查logical-operator规则的实现逻辑。

## Initial Analysis

### Suspected Root Cause
logical-operator规则在处理AST节点时，可能将import语句的某些部分（如解构语法`{}`）误识别为逻辑运算符或相关语法结构。

### Affected Components
- `src/rules/logical-operator-rule.ts` - 逻辑运算符规则实现
- AST解析相关的代码
- 复杂度计算核心逻辑