# 技术栈引导文档

## 核心技术栈

### 运行时环境策略

**双运行时策略：**

- **开发环境**: 使用 Bun 作为开发运行时，享受快速启动、内置 TypeScript 支持和高性能构建
- **生产代码**: 严格使用 Node.js 兼容的 API 和库，确保用户可以在标准 Node.js 环境中运行
- **用户环境**: 构建产物完全兼容 Node.js 18+，支持 npm/npx 标准工作流

**Node.js 兼容性要求:**

- 所有源码必须使用 Node.js 标准 API
- 禁用任何 Bun 特定功能（如`Bun.file`、`Bun.serve`等）
- 使用成熟的第三方库替代 Bun 内置功能
- 保持与 Node.js 生态系统的完全兼容

### 核心依赖

#### 解析和分析

- **@swc/core**: 高性能 TypeScript/JavaScript 解析器，用于生成 AST
- **glob/fast-glob**: 文件模式匹配，支持批量文件处理
- **cosmiconfig**: 统一的配置文件管理，支持多种配置格式

#### 性能优化

- **缓存系统**: 智能缓存机制，避免重复分析
- **对象池**: 内存优化的对象池模式，减少 GC 压力
- **并发引擎**: 异步执行引擎，支持多文件并发分析
- **流式处理**: 大文件流式处理，避免内存溢出

#### CLI 框架

- **commander**: 命令行界面框架，提供参数解析和命令管理
- **chalk**: 终端颜色输出
- **ora**: 进度指示器
- **cli-progress**: 进度条显示

#### Web UI

- **hono**: 轻量级 Web 框架，提供可视化界面
- **@hono/node-server**: Node.js 适配器
- **open**: 自动打开浏览器

#### 文件系统和 I/O

- **fs**: Node.js 标准文件系统模块（避免使用`Bun.file`）
- **path**: 跨平台路径处理
- **stream**: 流式处理大文件

### 开发工具链

#### 类型系统

- **TypeScript 5.0+**: 使用最新的 TypeScript 特性
- **@types/node**: Node.js 类型定义
- **@types/bun**: Bun 特定类型定义

#### 构建系统

- **Bun Build**: 仅用于开发环境的快速构建，输出 Node.js 兼容代码
- **SWC Transform**: 代码转换和优化（Node.js 兼容）
- **TypeScript Compiler**: 类型检查和声明文件生成

## 架构原则

### 核心架构重构

**重大变更**: 完成从复杂 IoC 架构到轻量级访问者模式的重构

- **移除 ComplexityCalculator 类**: 彻底删除 2484 行的庞大计算器类
- **函数式 API**: 使用简单的`analyzeFile`函数替代复杂的类实例
- **访问者模式**: 基于`ComplexityVisitor`的轻量级 AST 遍历器
- **无兼容性包袱**: 一次性移除所有旧代码，采用现代化架构

### 新架构设计原则

- **简单性优先**: 避免过度工程化，使用最简单有效的解决方案
- **函数式接口**: 公共 API 采用纯函数设计，易于测试和使用
- **访问者模式**: 使用标准的访问者模式进行 AST 遍历和分析
- **单一职责**: 每个模块专注特定功能域
- **接口分离**: 清晰的公共 API 定义
- **开闭原则**: 对扩展开放，对修改封闭

### 性能要求

- **异步优先**: 所有 IO 操作使用异步模式
- **内存管理**: 大文件流式处理，对象池优化内存使用
- **缓存策略**: 智能缓存减少重复计算，支持增量分析
- **并发处理**: 支持多文件并行分析，充分利用多核性能
- **性能监控**: 内置性能基准测试和监控系统

### 错误处理

- **分层错误**: 业务错误、系统错误、用户错误分离
- **优雅降级**: 部分失败不影响整体分析
- **详细日志**: 提供充分的调试信息和可视化
- **用户友好**: 错误信息清晰易懂
- **错误恢复**: 自动重试和错误恢复机制

### 插件系统

- **沙箱隔离**: 插件在安全沙箱中运行
- **依赖管理**: 自动解析和管理插件依赖
- **版本控制**: 支持插件版本管理和兼容性检查
- **热重载**: 开发环境支持插件热重载

## 技术约束

### 版本要求

- **TypeScript**: >= 4.5.0 (peer dependency)
- **Node.js**: >= 18.0.0 (生产环境运行要求)
- **Bun**: >= 1.0.0 (仅开发环境使用)

### 兼容性

- **跨平台**: 支持 Windows、macOS、Linux
- **编码标准**: UTF-8 文件编码
- **路径处理**: 使用跨平台路径工具

### 安全要求

- **输入验证**: 严格验证所有用户输入
- **文件访问**: 安全的文件系统操作
- **依赖安全**: 定期更新依赖，避免安全漏洞

## 技术决策

### 重构架构的原因 (2024)

1. **复杂性管理**: 旧的 ComplexityCalculator 类过于庞大(2484 行)，职责过多
2. **维护性**: IoC 依赖注入增加了不必要的复杂度，影响代码理解和维护
3. **性能优化**: 简化的架构减少了初始化开销和内存使用
4. **测试友好**: 纯函数 API 更易于单元测试和集成测试
5. **用户体验**: 更简单的公共 API 降低了使用门槛

### 访问者模式的优势

1. **职责分离**: AST 遍历与复杂度计算逻辑清晰分离
2. **可扩展性**: 易于添加新的复杂度规则和计算逻辑
3. **标准模式**: 使用经典设计模式，代码更易理解
4. **性能优化**: 单次遍历完成所有计算，效率更高

### 双运行时策略的原因

1. **开发体验**: Bun 提供 3-4 倍的启动速度和原生 TypeScript 支持
2. **用户兼容**: 保持与 Node.js 生态的完全兼容，确保广泛适用性
3. **最佳实践**: 充分利用两个运行时的优势，避免各自局限性

### 选择 SWC 解析器的原因（保持 Node.js 兼容）

1. **性能**: 比 TypeScript Compiler 快 20-70 倍
2. **准确性**: 完整支持 ECMAScript 和 TypeScript 语法
3. **维护性**: 活跃的社区维护
4. **Node.js 兼容**: 作为标准 npm 包，在任何环境都能正常工作

### 模块化架构的好处

1. **可测试性**: 每个模块可独立测试
2. **可维护性**: 职责清晰，修改影响范围可控
3. **可扩展性**: 易于添加新功能和支持新语言
4. **复用性**: 核心算法可在不同场景复用

## 开发规范

### 代码风格

- **TypeScript 严格模式**: 启用所有严格类型检查
- **ESLint**: 使用推荐的代码风格规则
- **Prettier**: 统一代码格式化
- **命名约定**: 使用 camelCase，常量使用 UPPER_CASE

### Node.js 兼容性规范

- **禁用 Bun 特定 API**: 不得使用`Bun.*`命名空间下的任何 API
- **使用标准库**: 优先使用 Node.js 标准库（fs、path、stream 等）
- **第三方依赖**: 确保所有依赖在 Node.js 环境中可用
- **构建验证**: 构建产物必须在 Node.js 18+环境测试通过

### 测试要求

- **单元测试**: 核心算法 100% 覆盖率
- **集成测试**: CLI 命令和文件处理流程
- **兼容性测试**: 在 Node.js 和 Bun 环境下都要通过
- **性能测试**: 大型项目分析性能基准
- **快照测试**: 输出格式稳定性验证
