# 项目结构引导文档

## 目录结构概览

```
src/
├── core/           # 核心算法和类型定义
├── cli/            # 命令行界面
├── config/         # 配置管理
├── baseline/       # 基线管理
├── formatters/     # 输出格式化
├── cache/          # 缓存系统
├── engine/         # 执行引擎和性能优化
├── plugins/        # 插件系统
├── rules/          # 复杂度规则和规则集
├── ui/             # Web用户界面
├── utils/          # 通用工具函数
└── __test__/       # 测试文件
```

## 模块职责分工

### 核心模块 (`src/core/`)
**职责**: 认知复杂度计算的核心算法实现

**重大架构变更 (2024)**:
- ❌ **已移除**: `calculator.ts` - 删除了2484行的庞大ComplexityCalculator类
- ✅ **新增**: 基于访问者模式的轻量级架构
- ✅ **简化**: 使用函数式API替代复杂的类实例

**当前核心文件**:
- `types.ts`: 核心类型定义和接口
- `complexity-visitor.ts`: **重构后的核心** - 纯AST访问者，负责遍历和状态管理
- `parser.ts`: AST解析器封装
- `errors.ts`: 自定义错误类型
- `detail-collector.ts`: 详细复杂度信息收集
- `rule-registry.ts`: 规则注册和管理
- `default-rules.ts`: 默认复杂度规则
- `rule-initialization.ts`: 规则系统初始化
- `object-pool.ts`: 对象池内存优化

**主要导出** (`src/index.ts`):
- `analyzeFile(filePath, options)`: **新的核心API** - 替代ComplexityCalculator
- `ComplexityVisitor`: 访问者模式的核心实现
- 各种工具函数和类型定义

**命名约定**:
- 类名使用 PascalCase: `ComplexityVisitor`
- 接口使用 PascalCase: `CalculationOptions`
- 函数名使用 camelCase: `analyzeFile`
- 类型别名使用 PascalCase: `FunctionResult`

### CLI模块 (`src/cli/`)
**职责**: 命令行界面和用户交互

- `index.ts`: CLI入口点和命令定义
- `commands.ts`: 命令处理器实现
- `debug-commands.ts`: 调试相关命令
- `ui-helper.ts`: UI辅助工具

**设计原则**:
- 使用 Commander.js 统一管理命令
- 错误处理友好，提供清晰的错误信息
- 支持配置文件和命令行参数

### 配置模块 (`src/config/`)
**职责**: 配置文件管理和验证

- `types.ts`: 配置相关类型定义
- `manager.ts`: 配置加载和管理器
- `modern-manager.ts`: 现代化配置管理
- `factory.ts`: 配置工厂模式
- `schema.ts`: 配置架构验证
- `validator.ts`: 配置验证器
- `project-detector.ts`: 项目类型检测

**配置优先级**:
1. 命令行参数
2. 配置文件 (cosmiconfig)
3. 默认值

### 基线模块 (`src/baseline/`)
**职责**: 复杂度基线的创建、更新和比较

- `types.ts`: 基线相关类型定义
- `manager.ts`: 基线管理器实现

### 格式化模块 (`src/formatters/`)
**职责**: 分析结果的格式化输出

- `base.ts`: 格式化器基类
- `text.ts`: 文本格式输出
- `json.ts`: JSON格式输出
- `html.ts`: HTML格式输出

**扩展模式**: 继承 `BaseFormatter` 实现新的输出格式

### 缓存模块 (`src/cache/`)
**职责**: 智能缓存系统，提升分析性能

- `types.ts`: 缓存相关类型定义
- `manager.ts`: 缓存管理器
- `monitor.ts`: 缓存监控
- `index.ts`: 缓存模块导出

### 执行引擎 (`src/engine/`)
**职责**: 高性能异步分析引擎和优化系统

- `types.ts`: 引擎相关类型定义
- `async-engine.ts`: 异步执行引擎
- `execution-pool.ts`: 执行池管理
- `performance-monitor.ts`: 性能监控
- `performance-benchmark.ts`: 性能基准测试
- `debug-system.ts`: 调试系统
- `debug-visualizer.ts`: 调试可视化
- `streaming-processor.ts`: 流式处理器
- `object-pool.ts`: 对象池优化
- `registry.ts`: 引擎注册表
- `iterative-algorithms.ts`: 迭代算法优化
- `jsx-integration.ts`: JSX集成支持
- `demo.ts` & `demo-registry.ts`: 演示和示例

### 插件系统 (`src/plugins/`)
**职责**: 完整的插件架构，支持自定义扩展

- `types.ts`: 插件相关类型定义
- `manager.ts`: 插件管理器
- `sandbox.ts`: 插件沙箱隔离
- `dependency-resolver.ts`: 依赖解析器
- `version-manager.ts`: 版本管理
- `validator.ts`: 插件验证
- `communication.ts`: 插件通信
- `hot-reload.ts`: 热重载支持
- `dev-tools.ts`: 开发工具
- `error-recovery.ts`: 错误恢复

### 规则系统 (`src/rules/`)
**职责**: 复杂度规则定义和管理

- `base-rule.ts`: 规则基类
- `core-complexity-rule.ts`: 核心复杂度规则
- `logical-operator-rule.ts`: 逻辑运算符规则
- `jsx-hook-complexity.ts`: JSX Hook复杂度规则
- `jsx-event-handler-rule.ts`: JSX事件处理器规则
- `jsx-structural-exemption.ts`: JSX结构豁免规则
- `smart-conditional-rendering.ts`: 智能条件渲染规则
- `rule-sets.ts`: 规则集管理
- `jsx/`: JSX专用规则目录
- `base/`: 基础规则目录

### Web UI (`src/ui/`)
**职责**: Web可视化界面

- `server.ts`: Web服务器实现

## 文件组织原则

### 模块边界
- **高内聚**: 相关功能放在同一模块内
- **低耦合**: 模块间通过明确的接口通信
- **单向依赖**: 避免循环依赖

### 文件命名
- **功能描述**: 文件名清晰描述其功能
- **kebab-case**: 使用短横线分隔 (如需要)
- **扩展名**: TypeScript 文件使用 `.ts`

### 导出策略
- **index.ts**: 模块的统一导出入口
- **公共API**: 只导出外部需要使用的接口
- **内部实现**: 保持模块内部实现的私有性

## 代码组织模式

### 类设计原则
```typescript
export class ComplexityCalculator {
  private options: CalculationOptions;  // 私有配置
  private parser: ASTParser;           // 依赖注入
  
  constructor(options: CalculationOptions = {}) {
    // 构造函数参数验证
  }
  
  public async calculateFile(filePath: string): Promise<FunctionResult[]> {
    // 公共API方法
  }
  
  private calculate(ast: Module): FunctionResult[] {
    // 私有实现方法
  }
}
```

### 类型定义模式
```typescript
// 基础类型
export interface CalculationOptions {
  readonly maxComplexity?: number;
  readonly includeDetails?: boolean;
}

// 结果类型
export interface FunctionResult {
  readonly name: string;
  readonly complexity: number;
  readonly startLine: number;
  readonly endLine: number;
}

// 错误类型
export class ComplexityError extends Error {
  constructor(message: string, public readonly filePath?: string) {
    super(message);
    this.name = 'ComplexityError';
  }
}
```

### 异步处理模式
- **Promise优先**: 所有异步操作使用 Promise
- **错误传播**: 使用 try-catch 和 Promise.reject
- **并发控制**: 使用 Promise.all 进行并发处理

## 测试结构

### 测试目录结构
```
src/__test__/
├── core/           # 核心模块测试
├── cli/            # CLI测试
├── config/         # 配置测试
├── baseline/       # 基线测试
├── formatters/     # 格式化测试
├── cache/          # 缓存系统测试
├── engine/         # 执行引擎测试
├── plugins/        # 插件系统测试
├── rules/          # 规则系统测试
├── ui/             # Web UI测试
├── integration/    # 集成测试
├── e2e/           # 端到端测试
├── performance/    # 性能测试
├── stress/         # 压力测试
├── react-ecosystem/ # React生态测试
├── fixtures/       # 测试夹具
├── helpers/        # 测试辅助工具
└── snapshots/      # 快照测试
```

### 测试命名约定
- 测试文件: `*.test.ts`
- 测试描述: 使用清晰的英文描述
- 测试数据: 放在 `fixtures/` 目录

### 测试覆盖率要求
- **核心算法**: 100% 行覆盖率
- **CLI功能**: 90% 分支覆盖率
- **插件系统**: 85% 覆盖率
- **缓存系统**: 90% 覆盖率
- **执行引擎**: 95% 覆盖率
- **规则系统**: 100% 覆盖率（包括JSX规则）
- **边界条件**: 充分测试错误情况和边界值

## 构建和部署

### 构建输出
- **库模式**: `dist/` 目录，包含类型声明
- **CLI模式**: `dist/cli/` 目录，可执行文件
- **类型声明**: 自动生成 `.d.ts` 文件

### 路径别名
```typescript
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

使用示例:
```typescript
import { ComplexityCalculator } from '@/core/calculator';
import type { CalculationOptions } from '@/core/types';
```

## 版本控制

### Git工作流
- **主分支**: `main` - 稳定版本
- **功能分支**: `feature/*` - 新功能开发
- **修复分支**: `fix/*` - 错误修复

### 提交规范
使用 Conventional Commits 格式:
- `feat: 添加新功能`
- `fix: 修复错误`
- `docs: 更新文档`
- `test: 添加测试`
- `refactor: 重构代码`