{"name": "cognitive-complexity", "version": "1.0.0", "description": "Analyze cognitive complexity of TypeScript/JavaScript code", "main": "src/index.ts", "module": "src/index.ts", "type": "module", "private": true, "bin": {"complexity": "./src/cli/index.ts"}, "scripts": {"dev": "bun run src/cli/index.ts", "dev:watch": "bun --watch src/cli/index.ts", "build": "bun build src/index.ts --outdir dist --target node --format esm --external @swc/core --external commander --external cosmiconfig --external fast-glob --external hono --external @hono/node-server --external open", "build:cli": "bun build src/cli/index.ts --outdir dist/cli --target node --format esm --external @swc/core --external commander --external cosmiconfig --external fast-glob --external hono --external @hono/node-server --external open", "build:watch": "bun build src/cli/index.ts --outdir dist/cli --target node --format esm --external @swc/core --external commander --external cosmiconfig --external fast-glob --external hono --external @hono/node-server --external open --watch", "start": "bun run src/cli/index.ts", "test": "bun run build:cli && vitest run", "test:watch": "bun run build:cli && vitest", "test:coverage": "bun run build:cli && vitest run --coverage", "clean": "rm -rf dist/", "typecheck": "tsc --noEmit", "typecheck:watch": "tsc --noEmit --watch"}, "keywords": ["cognitive-complexity", "code-quality", "static-analysis", "typescript", "javascript", "cli"], "devDependencies": {"@babel/code-frame": "^7.27.1", "@types/babel__code-frame": "^7.0.6", "@types/bun": "latest", "@types/cli-progress": "^3.11.6", "@types/node": "^20.0.0", "@types/semver": "^7.7.0", "@vitest/coverage-v8": "^3.2.4", "cli-testing-library": "^3.0.1", "typescript": "^5.0.0", "vitest": "^3.2.4"}, "peerDependencies": {"typescript": ">=4.5.0"}, "dependencies": {"@hono/node-server": "^1.17.1", "@swc/core": "^1.13.2", "chalk": "^5.4.1", "cli-progress": "^3.12.0", "commander": "^14.0.0", "cosmiconfig": "^9.0.0", "fast-glob": "^3.3.3", "hono": "^4.8.9", "open": "^8.4.0", "ora": "^8.2.0", "semver": "^7.7.2"}}