#!/usr/bin/env bun

/**
 * 自动资源清理版本 - 更优雅的解决方案
 * 在进程退出时自动清理所有资源，无需手动调用
 */

import { analyzeFile } from './src/index';
import { TextFormatter } from './src/formatters/text';
import { writeFileSync, unlinkSync } from 'fs';

// 设置自动资源清理
setupAutoCleanup();

async function debugWithAutoCleanup() {
  console.log('🔍 调试输出格式问题（自动资源清理版）...\n');

  const testCode = `
export function simpleFunction() {
  return 'hello';
}

export function functionWithIf(x: number) {
  if (x > 0) {
    return x * 2;
  }
  return 0;
}

export function complexFunction(a: number, b: number) {
  if (a > 0) {
    if (b > 0) {
      return a + b;
    } else if (b < 0) {
      return a - b;
    }
  }
  return 0;
}

export function verySimpleFunction() {
  console.log('simple');
}
`.trim();

  const testFilePath = `/tmp/test-complexity-${Date.now()}.ts`;
  writeFileSync(testFilePath, testCode);

  try {
    console.log('🧠 开始分析...');
    const fileResult = await analyzeFile(testFilePath, { enableDetails: true });

    console.log('📊 分析结果:');
    console.log(`- 总复杂度: ${fileResult.complexity}`);
    console.log(`- 平均复杂度: ${fileResult.averageComplexity}`);
    console.log(`- 函数数量: ${fileResult.functions.length}`);

    // 格式化测试
    const mockAnalysisResult = {
      summary: {
        filesAnalyzed: 1,
        functionsAnalyzed: fileResult.functions.length,
        totalComplexity: fileResult.complexity,
        averageComplexity: fileResult.averageComplexity,
        highComplexityFunctions: fileResult.functions.filter(f => f.complexity > 15).length
      },
      results: [fileResult]
    };

    const formatter = new TextFormatter();
    const detailOutput = await formatter.format(mockAnalysisResult, true);
    console.log('\n📋 详细输出:\n', detailOutput);

    // 清理临时文件
    unlinkSync(testFilePath);
    console.log('✅ 分析完成');

  } catch (error) {
    console.error('❌ 分析失败:', error);
    // 临时文件清理
    try { unlinkSync(testFilePath); } catch {}
  }
}

/**
 * 设置自动资源清理
 * 在进程退出时自动清理所有可能的资源泄漏
 */
function setupAutoCleanup() {
  let cleanupExecuted = false;
  
  const performCleanup = () => {
    if (cleanupExecuted) return;
    cleanupExecuted = true;
    
    console.log('\n🧹 自动清理资源...');
    
    try {
      // 动态导入避免在模块加载时就创建单例
      const { getCodeFrameGenerator } = require('./src/utils/code-frame-generator');
      const { getGlobalFileCache } = require('./src/utils/file-cache');
      
      // 清理 CodeFrameGenerator
      try {
        const codeFrameGen = getCodeFrameGenerator();
        codeFrameGen.destroy();
        console.log('✅ CodeFrameGenerator 已清理');
      } catch (e) {
        console.log('ℹ️ CodeFrameGenerator 未初始化，跳过');
      }
      
      // 清理 FileContentCache
      try {
        const fileCache = getGlobalFileCache();
        fileCache.destroy();
        console.log('✅ FileContentCache 已清理');
      } catch (e) {
        console.log('ℹ️ FileContentCache 未初始化，跳过');
      }
      
    } catch (error) {
      console.warn('⚠️ 自动清理过程中出现错误:', error.message);
    }
  };

  // 注册多种退出事件的清理函数
  process.on('exit', performCleanup);
  process.on('SIGINT', () => {
    performCleanup();
    process.exit(0);
  });
  process.on('SIGTERM', () => {
    performCleanup();
    process.exit(0);
  });
  process.on('uncaughtException', (error) => {
    console.error('💥 未捕获异常:', error);
    performCleanup();
    process.exit(1);
  });
  process.on('unhandledRejection', (reason) => {
    console.error('💥 未处理的Promise拒绝:', reason);
    performCleanup();
    process.exit(1);
  });
}

// 运行脚本
debugWithAutoCleanup()
  .then(() => {
    console.log('✨ 脚本执行完成');
  })
  .catch((error) => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });