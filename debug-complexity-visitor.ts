import { ComplexityVisitor } from './src/core/complexity-visitor';
import { ASTParser } from './src/core/parser';

async function debugComplexityVisitor() {
  console.log('=== 调试 ComplexityVisitor 直接使用 ===');
  
  const code = `
function withIf() {
  if (condition) {
    return true;
  }
  return false;
}
  `;
  
  try {
    const parser = new ASTParser();
    const ast = await parser.parseCode(code, 'test.ts');
    
    console.log('AST 解析成功');
    
    const visitor = new ComplexityVisitor(code);
    visitor.visit(ast);
    
    console.log('直接 visit() 调用 - 总复杂度:', visitor.getTotalComplexity());
    console.log('获取到的结果数量:', visitor.getResults().length);
    
    // 测试 visitFunction 方法
    console.log('\n--- 测试 visitFunction 方法 ---');
    
    // 查找函数节点
    function findFunctionNodes(node: any): any[] {
      const functions = [];
      
      if (node.type === 'FunctionDeclaration') {
        functions.push(node);
      }
      
      if (node.body && Array.isArray(node.body)) {
        for (const child of node.body) {
          functions.push(...findFunctionNodes(child));
        }
      }
      
      if (node.body && !Array.isArray(node.body)) {
        functions.push(...findFunctionNodes(node.body));
      }
      
      return functions;
    }
    
    const functionNodes = findFunctionNodes(ast);
    console.log('找到函数节点数量:', functionNodes.length);
    
    if (functionNodes.length > 0) {
      const visitor2 = new ComplexityVisitor(code);
      visitor2.visitFunction(functionNodes[0]);
      
      const results = visitor2.getResults();
      console.log('visitFunction 结果数量:', results.length);
      if (results.length > 0) {
        console.log('函数名:', results[0].name);
        console.log('复杂度:', results[0].complexity);
      }
    }
    
  } catch (error) {
    console.error('调试失败:', error);
  }
}

debugComplexityVisitor();