#!/usr/bin/env bun

/**
 * 简化版调试脚本，测试是否是ComplexityVisitor本身的问题
 */

import { ComplexityVisitor } from './src/core/complexity-visitor';
import { ASTParser } from './src/core/parser';
import { DetailCollector } from './src/core/detail-collector';

async function testSimpleVisitor() {
  console.log('🧪 测试ComplexityVisitor本身是否导致超时...\n');
  
  const testCode = `
export function simpleFunction() {
  return 'hello';
}
`.trim();

  try {
    // 解析AST
    console.log('📝 解析AST...');
    const parser = new ASTParser();
    const ast = await parser.parseCode(testCode, 'test.ts');
    
    // 创建visitor
    console.log('🔧 创建ComplexityVisitor...');
    const visitor = new ComplexityVisitor(testCode);
    
    // 查找函数节点
    console.log('🔍 查找函数节点...');
    const functions: any[] = [];
    
    function traverse(node: any) {
      if (!node || typeof node !== 'object') return;
      
      // 检查是否是函数节点
      if (node.type === 'FunctionDeclaration' || 
          node.type === 'ArrowFunctionExpression' || 
          node.type === 'FunctionExpression') {
        functions.push(node);
      }
      
      // 遍历子节点
      for (const key in node) {
        if (key === 'span' || key === 'type') continue;
        
        const value = node[key];
        if (Array.isArray(value)) {
          value.forEach(traverse);
        } else if (value && typeof value === 'object') {
          traverse(value);
        }
      }
    }
    
    traverse(ast);
    console.log(`📊 找到 ${functions.length} 个函数`);
    
    // 分析函数
    console.log('⚡ 开始分析函数...');
    for (const func of functions) {
      visitor.visitFunction(func);
    }
    
    // 获取结果
    console.log('✅ 获取分析结果...');
    const results = visitor.getResults();
    
    console.log('📈 分析完成:');
    results.forEach((result, index) => {
      console.log(`  ${index + 1}. ${result.name}: 复杂度 ${result.complexity}`);
    });
    
    console.log('\n🎯 测试成功完成，等待进程退出...');
    
    // 显式设置一个短超时来检查是否会正常退出
    setTimeout(() => {
      console.log('💡 进程应该在这之后立即退出');
      process.exit(0);
    }, 1000);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

testSimpleVisitor();