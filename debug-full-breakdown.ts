import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';

async function debugFullBreakdown() {
  console.log('=== 完整四层嵌套逐步构建 ===');
  
  // 步骤1: 只有最外层 if
  console.log('\n--- 步骤1: if only ---');
  let sourceCode = `
    function test() {
      if (condition1) {
        console.log('step1');
      }
    }
  `;
  
  let parser = new ASTParser();
  let ast = await parser.parseCode(sourceCode, 'debug.ts');
  let visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('复杂度:', visitor.getTotalComplexity(), '(期望: 1)');

  // 步骤2: if + for
  console.log('\n--- 步骤2: if + for ---');
  sourceCode = `
    function test() {
      if (condition1) {
        for (let i = 0; i < 10; i++) {
          console.log('step2');
        }
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('复杂度:', visitor.getTotalComplexity(), '(期望: 3, if=1+0, for=1+1)');

  // 步骤3: if + for + if
  console.log('\n--- 步骤3: if + for + if ---');
  sourceCode = `
    function test() {
      if (condition1) {
        for (let i = 0; i < 10; i++) {
          if (condition2) {
            console.log('step3');
          }
        }
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('复杂度:', visitor.getTotalComplexity(), '(期望: 6, if=1+0, for=1+1, if=1+2)');

  // 步骤4: 完整的四层结构
  console.log('\n--- 步骤4: 完整四层 ---');
  sourceCode = `
    function test() {
      if (condition1) {
        for (let i = 0; i < 10; i++) {
          if (condition2) {
            try {
              riskyOperation();
            } catch (error) {
              handleError();
            }
          }
        }
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('复杂度:', visitor.getTotalComplexity(), '(期望: 10, if=1+0, for=1+1, if=1+2, catch=1+3)');
  console.log('实际与期望差异:', visitor.getTotalComplexity() - 10);
}

debugFullBreakdown().catch(console.error);