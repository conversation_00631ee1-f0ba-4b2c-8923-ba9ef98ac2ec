import { ComplexityCalculator } from './src/core/calculator';

async function quickDiagnostic() {
  console.log('🔍 快速诊断：查找活跃句柄...\n');
  
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  const calculator = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: false
  });
  
  try {
    console.log('1. 计算前的活跃句柄:');
    const handlesBefore = (process as any)._getActiveHandles();
    console.log('   数量:', handlesBefore.length);
    console.log('   类型:', handlesBefore.map((h: any) => h.constructor.name));
    
    const results = await calculator.calculateCode(code, 'test.js');
    console.log('\n2. 计算完成，复杂度:', results[0]?.complexity);
    
    console.log('\n3. 计算后的活跃句柄:');
    const handlesAfter = (process as any)._getActiveHandles();
    console.log('   数量:', handlesAfter.length);
    console.log('   类型:', handlesAfter.map((h: any) => h.constructor.name));
    
    console.log('\n4. 开始清理资源...');
    await calculator.dispose();
    
    console.log('\n5. 清理后的活跃句柄:');
    const handlesAfterDispose = (process as any)._getActiveHandles();
    console.log('   数量:', handlesAfterDispose.length);
    console.log('   类型:', handlesAfterDispose.map((h: any) => h.constructor.name));
    
    if (handlesAfterDispose.length === 0) {
      console.log('\n✅ 所有句柄已清理，进程应该能自然退出');
    } else {
      console.log('\n❌ 仍有句柄未清理，这些句柄阻止了进程退出');
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

quickDiagnostic().catch(console.error);