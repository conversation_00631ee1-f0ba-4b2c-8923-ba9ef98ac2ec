#!/usr/bin/env bun

/**
 * 性能测试工具集成演示脚本（独立版本）
 * 
 * 此脚本演示了扩展后的 PerformanceTestUtils 如何与 CLI 测试框架集成，
 * 提供全面的性能监控和基准测试能力。
 */

import { CLITestingUtils } from './src/__test__/helpers/cli-testing-utils';
import type { ConcurrencyTestConfig, PerformanceMetrics, PerformanceTestResult } from './src/__test__/helpers/test-utils';

// 简化版的性能测试工具（不依赖vitest）
class StandalonePerformanceUtils {
  private static timings: Map<string, number> = new Map();
  
  static async measureCLIPerformance(
    testFn: () => Promise<import('./src/__test__/helpers/cli-testing-utils').CLITestResult>,
    scenario: string = 'CLI Test'
  ): Promise<{ result: import('./src/__test__/helpers/cli-testing-utils').CLITestResult; metrics: PerformanceMetrics }> {
    const startTime = performance.now();
    const startMemory = process.memoryUsage();
    
    const result = await testFn();
    
    const endTime = performance.now();
    const endMemory = process.memoryUsage();
    const executionTime = endTime - startTime;
    
    // 计算内存使用情况
    const memoryUsed = endMemory.heapUsed - startMemory.heapUsed;
    const memoryTotal = endMemory.heapTotal;
    const memoryPercentage = (endMemory.heapUsed / memoryTotal) * 100;
    
    const metrics: PerformanceMetrics = {
      executionTime,
      memoryUsage: {
        used: memoryUsed,
        total: memoryTotal,
        percentage: memoryPercentage
      },
      processInfo: {
        pid: (result as any).getProcessId?.(),
        uptime: (result as any).getRuntime?.() || executionTime
      },
      testDetails: {
        scenario
      }
    };
    
    return { result, metrics };
  }
  
  static async runConcurrencyTest(
    testFactory: () => Promise<import('./src/__test__/helpers/cli-testing-utils').CLITestResult>,
    config: ConcurrencyTestConfig,
    scenario: string = 'Concurrency Test'
  ): Promise<{
    results: PerformanceTestResult[];
    summary: {
      totalExecutionTime: number;
      averageExecutionTime: number;
      peakMemoryUsage: number;
      successRate: number;
      concurrentProcesses: number;
    };
  }> {
    const startTime = performance.now();
    const promises: Promise<{ result: import('./src/__test__/helpers/cli-testing-utils').CLITestResult; metrics: PerformanceMetrics }>[] = [];
    
    // 启动并发测试
    for (let i = 0; i < config.concurrentCount; i++) {
      const promise = this.measureCLIPerformance(
        testFactory,
        `${scenario} - Instance ${i + 1}`
      );
      promises.push(promise);
    }
    
    // 等待所有测试完成
    const concurrentResults = await Promise.allSettled(promises);
    const totalExecutionTime = performance.now() - startTime;
    
    // 处理结果
    const results: PerformanceTestResult[] = [];
    const successfulResults: PerformanceMetrics[] = [];
    let peakMemoryUsage = 0;
    
    for (let i = 0; i < concurrentResults.length; i++) {
      const settledResult = concurrentResults[i];
      
      if (settledResult!.status === 'fulfilled') {
        const { result, metrics } = settledResult.value;
        successfulResults.push(metrics);
        
        // 更新峰值内存使用
        peakMemoryUsage = Math.max(peakMemoryUsage, metrics.memoryUsage.used);
        
        results.push({
          benchmark: `${scenario} - Instance ${i + 1}`,
          passed: true,
          metrics,
          violations: [],
          suggestions: []
        });
        
        // 清理测试结果
        try {
          await result.kill();
        } catch (error) {
          console.warn(`Warning cleaning up concurrent test ${i + 1}:`, error);
        }
      } else {
        // 处理失败的测试
        results.push({
          benchmark: `${scenario} - Instance ${i + 1} (Failed)`,
          passed: false,
          metrics: {
            executionTime: 0,
            memoryUsage: { used: 0, total: 0, percentage: 0 },
            processInfo: { uptime: 0 },
            testDetails: { scenario: `${scenario} - Failed` }
          },
          violations: [`Test failed: ${settledResult.reason}`],
          suggestions: ['Check test configuration and system resources']
        });
      }
    }
    
    const successRate = config.concurrentCount === 0 ? 0 : (successfulResults.length / config.concurrentCount) * 100;
    const averageExecutionTime = successfulResults.length > 0 
      ? successfulResults.reduce((sum, m) => sum + m.executionTime, 0) / successfulResults.length
      : 0;
    
    return {
      results,
      summary: {
        totalExecutionTime,
        averageExecutionTime,
        peakMemoryUsage,
        successRate,
        concurrentProcesses: config.concurrentCount
      }
    };
  }
  
  static getCurrentPerformanceStats(): {
    memory: NodeJS.MemoryUsage;
    uptime: number;
    activeTimers: number;
    loadAverage?: number[];
  } {
    return {
      memory: process.memoryUsage(),
      uptime: process.uptime() * 1000,
      activeTimers: this.timings.size,
      loadAverage: process.platform !== 'win32' ? require('os').loadavg() : undefined
    };
  }
  
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

console.log('🚀 CLI 测试框架性能工具集成演示\n');

async function main() {
  try {
    // 1. 基本性能测试演示
    console.log('📊 1. 基本性能测试演示');
    console.log('================================');
    
    const { result, metrics } = await StandalonePerformanceUtils.measureCLIPerformance(
      () => CLITestingUtils.renderCLI('echo', ['Hello Performance Test']),
      'Basic Echo Command'
    );
    
    console.log(`✅ 测试场景: ${metrics.testDetails.scenario}`);
    console.log(`⏱️  执行时间: ${metrics.executionTime.toFixed(2)}ms`);
    console.log(`💾 内存使用: ${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB (${metrics.memoryUsage.percentage.toFixed(1)}%)`);
    console.log(`🔄 进程运行时间: ${metrics.processInfo.uptime.toFixed(2)}ms`);
    if (metrics.processInfo.pid) {
      console.log(`🆔 进程ID: ${metrics.processInfo.pid}`);
    }
    
    await CLITestingUtils.cleanup(result);
    console.log();

    // 2. 并发性能测试演示
    console.log('🔄 2. 并发性能测试演示');
    console.log('================================');
    
    const concurrencyConfig: ConcurrencyTestConfig = {
      concurrentCount: 3,
      testDuration: 5000,
      memoryLimit: 100 * 1024 * 1024, // 100MB
      cpuLimit: 80 // 80%
    };
    
    const concurrencyResult = await StandalonePerformanceUtils.runConcurrencyTest(
      () => CLITestingUtils.renderCLI('echo', ['Concurrent Test']),
      concurrencyConfig,
      'Echo 并发测试'
    );
    
    console.log(`🔢 并发进程数: ${concurrencyResult.summary.concurrentProcesses}`);
    console.log(`✅ 成功率: ${concurrencyResult.summary.successRate.toFixed(1)}%`);
    console.log(`⏱️  总执行时间: ${concurrencyResult.summary.totalExecutionTime.toFixed(2)}ms`);
    console.log(`📊 平均执行时间: ${concurrencyResult.summary.averageExecutionTime.toFixed(2)}ms`);
    console.log(`💾 峰值内存使用: ${(concurrencyResult.summary.peakMemoryUsage / 1024 / 1024).toFixed(2)}MB`);
    
    // 显示单个测试结果
    console.log('\n并发测试详细结果:');
    concurrencyResult.results.forEach((result, index) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      console.log(`  ${index + 1}. ${result.benchmark} - ${status}`);
      console.log(`     执行时间: ${result.metrics.executionTime.toFixed(2)}ms`);
      console.log(`     内存使用: ${(result.metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB`);
    });
    console.log();

    // 3. 当前性能统计信息演示
    console.log('📊 3. 当前性能统计信息');
    console.log('================================');
    
    const stats = StandalonePerformanceUtils.getCurrentPerformanceStats();
    console.log(`💾 当前内存使用: ${(stats.memory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    console.log(`💾 当前内存总量: ${(stats.memory.heapTotal / 1024 / 1024).toFixed(2)}MB`);
    console.log(`⏱️  系统运行时间: ${(stats.uptime / 1000).toFixed(1)}秒`);
    console.log(`🔄 活动计时器数: ${stats.activeTimers}`);
    
    if (stats.loadAverage && stats.loadAverage.length > 0) {
      console.log(`📈 系统负载: [${stats.loadAverage.map(la => la.toFixed(2)).join(', ')}]`);
    }
    console.log();

    // 4. 进程管理能力演示
    console.log('🔧 4. 进程管理能力演示');
    console.log('================================');
    
    console.log(`📊 当前活动进程数: ${CLITestingUtils.getActiveProcessCount()}`);
    
    // 创建几个测试进程
    const testProcesses = [];
    for (let i = 0; i < 2; i++) {
      const process = await CLITestingUtils.renderCLI('sleep', ['2']);
      testProcesses.push(process);
    }
    
    console.log(`📊 创建后的活动进程数: ${CLITestingUtils.getActiveProcessCount()}`);
    
    const processInfo = CLITestingUtils.getActiveProcessesInfo();
    console.log('📋 活动进程详情:');
    processInfo.forEach((info, index) => {
      console.log(`  ${index + 1}. PID: ${info.processId}, 命令: ${info.command}, 运行时间: ${info.runtime.toFixed(2)}ms`);
    });
    
    // 清理进程
    await CLITestingUtils.cleanupAll();
    console.log(`📊 清理后的活动进程数: ${CLITestingUtils.getActiveProcessCount()}`);
    console.log();

    // 5. 错误处理演示
    console.log('⚠️  5. 错误处理演示');
    console.log('================================');
    
    try {
      const { result: failResult } = await StandalonePerformanceUtils.measureCLIPerformance(
        () => CLITestingUtils.renderCLI('nonexistent-command', ['test']),
        'Error Handling Test'
      );
      console.log('❌ 应该抛出错误，但没有');
    } catch (error) {
      console.log(`✅ 正确捕获错误: ${error instanceof Error ? error.message.substring(0, 50) + '...' : String(error)}`);
    }
    console.log();

    console.log('🎉 性能测试工具集成演示完成！');
    console.log('\n📋 功能总结:');
    console.log('• ✅ CLI 测试性能监控');
    console.log('• ✅ 并发性能测试');
    console.log('• ✅ 进程管理和清理');
    console.log('• ✅ 详细性能指标收集');
    console.log('• ✅ 系统资源监控');
    console.log('• ✅ 错误处理和异常管理');
    console.log('• ✅ 与 CLI 测试框架无缝集成');
    
  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
    process.exit(1);
  } finally {
    // 清理所有资源
    await CLITestingUtils.cleanupAll();
  }
}

if (import.meta.main) {
  main().catch(console.error);
}