#!/usr/bin/env bun

/**
 * 对比静态API和实例API的try-catch复杂度计算结果
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';

async function compareApis() {
  console.log('=== 对比静态API与实例API ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  console.log('测试代码:', testCode);
  console.log();
  
  // 测试1: 静态API
  console.log('--- 静态API (轻量级) ---');
  try {
    const staticResults = await ComplexityCalculator.analyze(testCode);
    console.log('结果:', staticResults.map(r => ({name: r.name, complexity: r.complexity})));
  } catch (error) {
    console.error('静态API错误:', error);
  }
  
  // 测试2: 实例API (轻量级工厂)
  console.log('\n--- 实例API (轻量级工厂) ---');
  try {
    const { createLightweightFactory } = await import('./src/core/calculator-factory');
    const lightFactory = createLightweightFactory();
    const lightCalculator = new ComplexityCalculator({}, lightFactory);
    
    const lightResults = await lightCalculator.calculateCode(testCode, 'test.ts');
    console.log('结果:', lightResults.map(r => ({name: r.name, complexity: r.complexity})));
    
    await lightCalculator.dispose();
  } catch (error) {
    console.error('轻量级实例API错误:', error);
  }
  
  // 测试3: 实例API (完整功能工厂)
  console.log('\n--- 实例API (完整功能工厂) ---');
  try {
    const fullFactory = new CalculatorFactory({
      enableMonitoring: true,
      enableCaching: true,
      quiet: true,
    });
    const fullCalculator = new ComplexityCalculator({}, fullFactory);
    
    const fullResults = await fullCalculator.calculateCode(testCode, 'test.ts');
    console.log('结果:', fullResults.map(r => ({name: r.name, complexity: r.complexity})));
    
    await fullCalculator.dispose();
  } catch (error) {
    console.error('完整功能实例API错误:', error);
  }
}

compareApis().catch(console.error);