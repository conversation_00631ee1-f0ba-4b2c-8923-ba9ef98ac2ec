#!/usr/bin/env bun

/**
 * Debug script to reproduce import-logical-operator-false-positive bug
 */

import { analyzeFile } from './src/index';

async function debugImportBug() {
  console.log('=== Debug: Import Statement Logical Operator False Positive ===\n');

  // Create test code with import statements
  const testCode = `
// This should NOT be detected as logical operator
import {
  Ellipsis, enqueueSnackbar, useFireExport,
} from '@imile/components'

// This SHOULD be detected as logical operator  
function testFunction() {
  if (a && b) {
    return true;
  }
  return false;
}
`;

  try {
    console.log('Test code:');
    console.log(testCode);
    console.log('\n--- Analysis Results ---');

    const results = await analyzeFile(testCode, {
      includeDetails: true,
      filePath: 'test.ts'
    });

    console.log('Analysis results:', JSON.stringify(results, null, 2));

    // Check if import statement is incorrectly flagged  
    const functions = results.functions || [];
    const hasImportError = functions.some(func => 
      func.details?.some(detail => 
        detail.line <= 4 && detail.ruleId === 'logical-operator'
      )
    );

    if (hasImportError) {
      console.error('\n❌ BUG CONFIRMED: Import statement detected as logical operator!');
    } else {
      console.log('\n✅ No import logical operator false positive detected');
    }

  } catch (error) {
    console.error('Error during analysis:', error);
  }
}

// Run the debug
debugImportBug();