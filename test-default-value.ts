import { ComplexityCalculator } from './src/core/calculator';

async function testDefaultValueAssignment() {
  const tests = [
    {
      name: '默认值赋值应该被豁免',
      code: 'function test() { const value = a || "default"; }',
      expected: 0  // 应该被豁免，因为是默认值赋值
    },
    {
      name: '条件语句中的逻辑运算符不应被豁免',
      code: 'function test() { if (a || b) { console.log("test"); } }',
      expected: 2  // if(1) + ||(1)
    }
  ];
  
  const calculator = new ComplexityCalculator();
  
  for (const test of tests) {
    console.log(`\n测试: ${test.name}`);
    console.log(`代码: ${test.code}`);
    console.log(`期望复杂度: ${test.expected}`);
    
    const results = await calculator.calculateCode(test.code, 'test.js');
    const actual = results[0]?.complexity || 0;
    
    console.log(`实际复杂度: ${actual}`);
    console.log(`结果: ${actual === test.expected ? '✅ 通过' : '❌ 失败'}`);
  }
}

testDefaultValueAssignment().catch(console.error);