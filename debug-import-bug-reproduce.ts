/**
 * 复现bug报告中的具体场景
 */

import { analyzeFile } from './src/index';
import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';

// 根据bug报告创建的测试用例
const testContent = `
/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Ellipsis, enqueueSnackbar, useFireExport,
} from '@imile/components'

function someFunction() {
  // 一些简单的逻辑
  const result = true && false;
  return result;
}
`;

async function reproduceImportBug() {
  console.log('🔍 复现 import 语句误判 bug');
  console.log('━'.repeat(50));

  try {
    // 创建临时测试文件
    const fs = require('fs');
    const path = require('path');
    const tempDir = path.join(process.cwd(), 'temp-debug-2');
    const tempFilePath = path.join(tempDir, 'test-import-bug.ts');
    
    // 创建临时目录
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // 写入测试文件
    fs.writeFileSync(tempFilePath, testContent);
    
    console.log('1. 使用 analyzeFile 分析...');
    const results = await analyzeFile(tempFilePath, {
      includeDetails: true,
      maxComplexity: 10
    });
    
    console.log('结果:', JSON.stringify(results, null, 2));
    
    console.log('\n2. 直接使用 ComplexityVisitor 分析...');
    const parser = new ASTParser();
    const ast = await parser.parseCode(testContent, tempFilePath);
    
    const visitor = new ComplexityVisitor({
      includeDetails: true,
      rules: {
        logical: {
          enableMixedLogicOperatorPenalty: true
        }
      }
    });
    
    // 遍历AST
    visitor.visit(ast);
    const visitorResults = visitor.getResults();
    
    console.log('直接访问者结果:');
    visitorResults.forEach((result, index) => {
      console.log(`函数 ${index + 1}: ${result.name} (复杂度: ${result.complexity})`);
      if (result.details) {
        result.details.forEach(detail => {
          console.log(`  L${detail.line}: +${detail.complexity} - ${detail.reason} [${detail.rule}]`);
          if (detail.line === 4) {
            console.log(`    ⚠️  这是第4行！可能就是import语句被误判的位置`);
          }
        });
      }
    });
    
    // 清理临时文件
    fs.unlinkSync(tempFilePath);
    fs.rmSync(tempDir, { recursive: true, force: true });
    
  } catch (error) {
    console.error('❌ 复现过程出错:', error);
    
    // 确保清理临时文件
    try {
      const fs = require('fs');
      const path = require('path');
      const tempDir = path.join(process.cwd(), 'temp-debug-2');
      if (fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true });
      }
    } catch (cleanupError) {
      console.error('清理临时文件失败:', cleanupError);
    }
  }
}

// 运行复现测试
reproduceImportBug();