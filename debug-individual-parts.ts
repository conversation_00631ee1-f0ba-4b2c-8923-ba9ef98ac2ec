import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';

async function debugIndividualParts() {
  // 测试1: 只有 for 循环
  console.log('=== 测试1: 单独的 for 循环 ===');
  let sourceCode = `
    function test() {
      for (let i = 0; i < 10; i++) {
        console.log(i);
      }
    }
  `;
  
  let parser = new ASTParser();
  let ast = await parser.parseCode(sourceCode, 'debug.ts');
  let visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('for 循环复杂度:', visitor.getTotalComplexity(), '(期望: 1)');

  // 测试2: 只有 try-catch
  console.log('\n=== 测试2: 单独的 try-catch ===');
  sourceCode = `
    function test() {
      try {
        riskyOperation();
      } catch (error) {
        handleError();
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('try-catch 复杂度:', visitor.getTotalComplexity(), '(期望: 1, catch子句)');

  // 测试3: for + try-catch 嵌套
  console.log('\n=== 测试3: for + try-catch 嵌套 ===');
  sourceCode = `
    function test() {
      for (let i = 0; i < 10; i++) {
        try {
          riskyOperation();
        } catch (error) {
          handleError();
        }
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('for + try-catch 复杂度:', visitor.getTotalComplexity(), '(期望: 3, for=1+0, catch=1+1)');

  // 测试4: 添加外层 if
  console.log('\n=== 测试4: if + for + try-catch ===');
  sourceCode = `
    function test() {
      if (condition) {
        for (let i = 0; i < 10; i++) {
          try {
            riskyOperation();
          } catch (error) {
            handleError();
          }
        }
      }
    }
  `;
  
  parser = new ASTParser();
  ast = await parser.parseCode(sourceCode, 'debug.ts');
  visitor = new ComplexityVisitor(sourceCode);
  visitor.visit(ast);
  console.log('if + for + try-catch 复杂度:', visitor.getTotalComplexity(), '(期望: 6, if=1+0, for=1+1, catch=1+2)');
}

debugIndividualParts().catch(console.error);