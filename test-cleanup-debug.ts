import { ComplexityCalculator } from './src/core/calculator';
import { RuleInitializationManager } from './src/core/rule-initialization';

async function testResourceCleanup() {
  console.log('测试资源清理解决方案...');
  
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  const calculator = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: true
  });
  
  const results = await calculator.calculateCode(code, 'test.js');
  console.log('总复杂度:', results[0]?.complexity);
  
  console.log('开始清理资源...');
  
  // 主动清理规则引擎资源
  const manager = RuleInitializationManager.getInstance();
  await manager.reset();
  
  console.log('资源清理完成，脚本应该自然退出...');
  
  // 添加超时检测，如果5秒后还没退出说明清理不够
  setTimeout(() => {
    console.error('❌ 脚本5秒后仍未退出，资源清理不彻底！');
    process.exit(1);
  }, 5000);
}

testResourceCleanup().catch((error) => {
  console.error('错误:', error);
  process.exit(1);
});