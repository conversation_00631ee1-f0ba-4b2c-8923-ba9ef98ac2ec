#!/usr/bin/env bun

/**
 * 性能测试工具集成演示脚本
 * 
 * 此脚本演示了扩展后的 PerformanceTestUtils 如何与 CLI 测试框架集成，
 * 提供全面的性能监控和基准测试能力。
 */

import { PerformanceTestUtils, TestUtils, MockDataGenerator } from './src/__test__/helpers/test-utils';
import { CLITestingUtils } from './src/__test__/helpers/cli-testing-utils';
import type { ConcurrencyTestConfig } from './src/__test__/helpers/test-utils';

console.log('🚀 CLI 测试框架性能工具集成演示\n');

async function main() {
  try {
    // 1. 基本性能测试演示
    console.log('📊 1. 基本性能测试演示');
    console.log('================================');
    
    const { result, metrics } = await PerformanceTestUtils.measureCLIPerformance(
      () => CLITestingUtils.renderCLI('echo', ['Hello Performance Test']),
      'Basic Echo Command'
    );
    
    console.log(`✅ 测试场景: ${metrics.testDetails.scenario}`);
    console.log(`⏱️  执行时间: ${metrics.executionTime.toFixed(2)}ms`);
    console.log(`💾 内存使用: ${(metrics.memoryUsage.used / 1024 / 1024).toFixed(2)}MB (${metrics.memoryUsage.percentage.toFixed(1)}%)`);
    console.log(`🔄 进程运行时间: ${metrics.processInfo.uptime.toFixed(2)}ms`);
    if (metrics.processInfo.pid) {
      console.log(`🆔 进程ID: ${metrics.processInfo.pid}`);
    }
    
    await CLITestingUtils.cleanup(result);
    console.log();

    // 2. 性能基准测试演示
    console.log('🎯 2. 性能基准测试演示');
    console.log('================================');
    
    const benchmarkResult = await PerformanceTestUtils.runCLIBenchmark(
      () => CLITestingUtils.renderCLI('echo', ['Benchmark Test']),
      'cli-basic',
      'Echo 基准测试'
    );
    
    console.log(`📋 基准名称: ${benchmarkResult.benchmark}`);
    console.log(`${benchmarkResult.passed ? '✅' : '❌'} 测试结果: ${benchmarkResult.passed ? 'PASS' : 'FAIL'}`);
    console.log(`⏱️  执行时间: ${benchmarkResult.metrics.executionTime.toFixed(2)}ms`);
    
    if (benchmarkResult.violations.length > 0) {
      console.log('⚠️  性能违规:');
      benchmarkResult.violations.forEach(v => console.log(`   • ${v}`));
    }
    
    if (benchmarkResult.suggestions.length > 0) {
      console.log('💡 优化建议:');
      benchmarkResult.suggestions.forEach(s => console.log(`   • ${s}`));
    }
    console.log();

    // 3. 并发性能测试演示
    console.log('🔄 3. 并发性能测试演示');
    console.log('================================');
    
    const concurrencyConfig: ConcurrencyTestConfig = {
      concurrentCount: 3,
      testDuration: 5000,
      memoryLimit: 100 * 1024 * 1024, // 100MB
      cpuLimit: 80 // 80%
    };
    
    const concurrencyResult = await PerformanceTestUtils.runConcurrencyTest(
      () => CLITestingUtils.renderCLI('echo', ['Concurrent Test']),
      concurrencyConfig,
      'Echo 并发测试'
    );
    
    console.log(`🔢 并发进程数: ${concurrencyResult.summary.concurrentProcesses}`);
    console.log(`✅ 成功率: ${concurrencyResult.summary.successRate.toFixed(1)}%`);
    console.log(`⏱️  总执行时间: ${concurrencyResult.summary.totalExecutionTime.toFixed(2)}ms`);
    console.log(`📊 平均执行时间: ${concurrencyResult.summary.averageExecutionTime.toFixed(2)}ms`);
    console.log(`💾 峰值内存使用: ${(concurrencyResult.summary.peakMemoryUsage / 1024 / 1024).toFixed(2)}MB`);
    console.log();

    // 4. 性能报告生成演示
    console.log('📄 4. 性能报告生成演示');
    console.log('================================');
    
    const reportResults = [benchmarkResult, ...concurrencyResult.results.slice(0, 2)];
    const report = PerformanceTestUtils.generatePerformanceReport(
      reportResults,
      'CLI 性能测试综合报告'
    );
    
    console.log(report);

    // 5. 系统资源监控演示
    console.log('📈 5. 系统资源监控演示');
    console.log('================================');
    
    console.log('开始 3 秒的系统资源监控...');
    const monitoringPromise = PerformanceTestUtils.monitorSystemResources(3000, 1000);
    
    // 在监控期间运行一些操作
    await TestUtils.wait(500);
    const testResult1 = await CLITestingUtils.renderCLI('echo', ['监控测试 1']);
    await TestUtils.wait(1000);
    const testResult2 = await CLITestingUtils.renderCLI('echo', ['监控测试 2']);
    await TestUtils.wait(500);
    
    await CLITestingUtils.cleanup(testResult1);
    await CLITestingUtils.cleanup(testResult2);
    
    const monitoringResult = await monitoringPromise;
    
    console.log(`📊 监控样本数: ${monitoringResult.samples.length}`);
    console.log(`📈 峰值内存: ${(monitoringResult.peak.memory / 1024 / 1024).toFixed(2)}MB`);
    console.log(`📊 平均堆使用: ${(monitoringResult.average.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    console.log(`📊 平均堆总量: ${(monitoringResult.average.heapTotal / 1024 / 1024).toFixed(2)}MB`);
    console.log();

    // 6. 当前性能统计信息演示
    console.log('📊 6. 当前性能统计信息');
    console.log('================================');
    
    const stats = PerformanceTestUtils.getCurrentPerformanceStats();
    console.log(`💾 当前内存使用: ${(stats.memory.heapUsed / 1024 / 1024).toFixed(2)}MB`);
    console.log(`💾 当前内存总量: ${(stats.memory.heapTotal / 1024 / 1024).toFixed(2)}MB`);
    console.log(`⏱️  系统运行时间: ${(stats.uptime / 1000).toFixed(1)}秒`);
    console.log(`🔄 活动计时器数: ${stats.activeTimers}`);
    
    if (stats.loadAverage && stats.loadAverage.length > 0) {
      console.log(`📈 系统负载: [${stats.loadAverage.map(la => la.toFixed(2)).join(', ')}]`);
    }
    console.log();

    // 7. 自定义性能基准演示
    console.log('⚙️  7. 自定义性能基准演示');
    console.log('================================');
    
    // 添加自定义基准
    PerformanceTestUtils.addBenchmark('demo-custom', {
      name: '演示自定义基准',
      maxExecutionTime: 2000,
      maxMemoryUsage: 50 * 1024 * 1024, // 50MB
      maxCpuUsage: 70,
      description: '用于演示的自定义性能基准'
    });
    
    const customBenchmark = PerformanceTestUtils.getBenchmark('demo-custom');
    console.log(`📋 自定义基准名称: ${customBenchmark?.name}`);
    console.log(`⏱️  最大执行时间: ${customBenchmark?.maxExecutionTime}ms`);
    console.log(`💾 最大内存使用: ${customBenchmark ? (customBenchmark.maxMemoryUsage / 1024 / 1024).toFixed(0) : 0}MB`);
    console.log(`📝 描述: ${customBenchmark?.description}`);
    console.log();

    // 8. 与现有工具集成演示
    console.log('🔗 8. 与现有测试工具集成演示');
    console.log('================================');
    
    // 使用 TestUtils.withTempDir 和性能测试结合
    await TestUtils.withTempDir(async (tempDir) => {
      console.log(`📁 临时目录: ${tempDir}`);
      
      const config = CLITestingUtils.getDefaultConfig();
      config.cwd = tempDir;
      
      const { result: pwdResult, metrics: pwdMetrics } = await PerformanceTestUtils.measureCLIPerformance(
        () => CLITestingUtils.renderCLI('pwd', [], config),
        '临时目录 PWD 测试'
      );
      
      console.log(`✅ PWD 测试完成，执行时间: ${pwdMetrics.executionTime.toFixed(2)}ms`);
      console.log(`📍 工作目录: ${pwdResult.stdout.trim()}`);
      
      await CLITestingUtils.cleanup(pwdResult);
    });
    
    console.log();

    console.log('🎉 性能测试工具集成演示完成！');
    console.log('\n📋 功能总结:');
    console.log('• ✅ CLI 测试性能监控');
    console.log('• ✅ 基准测试和验证');
    console.log('• ✅ 并发性能测试');
    console.log('• ✅ 详细性能报告生成');
    console.log('• ✅ 系统资源监控');
    console.log('• ✅ 自定义性能基准管理');
    console.log('• ✅ 与现有测试工具无缝集成');
    
  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
    process.exit(1);
  } finally {
    // 清理所有资源
    await CLITestingUtils.cleanupAll();
    PerformanceTestUtils.cleanup();
  }
}

if (import.meta.main) {
  main().catch(console.error);
}