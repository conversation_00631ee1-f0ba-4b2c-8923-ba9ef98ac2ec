import { ComplexityCalculator } from './src/core/calculator';

async function testOriginalBehavior() {
  console.log('测试原始行为（不强制退出）...');
  
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  const calculator = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: true
  });
  
  try {
    const results = await calculator.calculateCode(code, 'test.js');
    console.log('总复杂度:', results[0]?.complexity);
    console.log('计算完成，脚本会自然退出吗？');
  } finally {
    // 确保清理资源
    await calculator.dispose();
    console.log('✅ 资源已清理');
  }
  
  // 不添加 process.exit(0)，看看会发生什么
}

testOriginalBehavior().catch(console.error);