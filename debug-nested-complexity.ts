import { ComplexityCalculator } from './src/core/calculator';
import { createFullFeaturedFactory } from './src/core/calculator-factory';
import { initializeRules } from './src/core/default-rules';

async function testNestedComplexity() {
  // Initialize rules
  initializeRules(true);
  
  const factory = createFullFeaturedFactory();
  const calculator = new ComplexityCalculator({ includeDetails: true }, factory);
  
  const code = `
    function nested(x: number, y: number) {
      if (x > 0) {        // +1
        if (y > 0) {      // +2 (nested)
          return x + y;
        }
      }
      return 0;
    }
  `;
  
  try {
    const results = await calculator.calculateCode(code, 'test.ts');
    console.log('结果数量:', results.length);
    if (results.length > 0) {
      const result = results[0];
      console.log('函数名:', result.name);
      console.log('复杂度:', result.complexity);
      console.log('期望复杂度: 3 (1+2)');
      console.log('测试状态:', result.complexity === 3 ? '✅ 通过' : '❌ 失败');
      
      // 如果有详细信息，显示它
      if (result.details && result.details.length > 0) {
        console.log('\n详细步骤:');
        result.details.forEach((detail, index) => {
          console.log(`  ${index + 1}. ${detail.description}: +${detail.increment} (行${detail.line})`);
        });
        
        const totalFromDetails = result.details.reduce((sum, detail) => sum + detail.increment, 0);
        console.log(`\n详细步骤总计: ${totalFromDetails}`);
      }
    }
  } catch (error) {
    console.error('计算出错:', error);
  } finally {
    await calculator.dispose();
  }
}

testNestedComplexity().catch(console.error);