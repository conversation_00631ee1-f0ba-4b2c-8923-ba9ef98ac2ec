#!/usr/bin/env bun

/**
 * 调试AsyncRuleEngine的节点提取过程
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';

// 修改AsyncRuleEngine以添加调试信息
async function debugAsyncRuleEngine() {
  console.log('=== 调试AsyncRuleEngine节点提取 ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  // 创建一个临时的调试类
  class DebugCalculator extends ComplexityCalculator {
    async debugAnalyzeFunction(code: string) {
      // 解析代码
      const ast = await this.parser.parseCode(code, 'debug.ts');
      
      // 找到函数节点
      const functions = this.findFunctions(ast);
      console.log('找到的函数数量:', functions.length);
      
      if (functions.length > 0) {
        const functionNode = functions[0];
        console.log('函数节点类型:', functionNode.type);
        
        // 获取AsyncRuleEngine
        const asyncEngine = await this.ensureAsyncRuleEngine();
        if (asyncEngine) {
          console.log('AsyncRuleEngine已初始化');
          
          // 手动提取节点来查看
          const extractedNodes = this.extractNodes(functionNode);
          console.log('提取的节点数量:', extractedNodes.length);
          console.log('提取的节点类型:', extractedNodes.map(n => n.type));
          
          // 分析每个节点
          for (const node of extractedNodes) {
            console.log(`\\n--- 分析节点: ${node.type} ---`);
            const context = {
              nestingLevel: 0,
              functionName: 'withTryCatch',
              sourceCode: code,
              filePath: 'debug.ts',
              nodeIndex: 0
            };
            
            try {
              const analysis = await asyncEngine.analyzeNode(node, context);
              console.log('节点复杂度:', analysis.complexity);
              console.log('应用的规则:', analysis.appliedRules?.map(r => r.ruleId) || []);
            } catch (error) {
              console.log('分析错误:', error);
            }
          }
        } else {
          console.log('AsyncRuleEngine未初始化');
        }
      }
    }
    
    // 暴露私有方法
    private extractNodes(functionNode: any): any[] {
      const nodes: any[] = [];
      this.collectNodes(functionNode, nodes);
      return nodes;
    }
    
    private collectNodes(node: any, nodes: any[]): void {
      if (!node || typeof node !== 'object') {
        return;
      }
      
      // 检查是否为可分析节点
      const analyzableTypes = [
        'IfStatement',
        'WhileStatement', 
        'ForStatement',
        'DoWhileStatement',
        'SwitchStatement',
        'ConditionalExpression',
        'LogicalExpression',
        'CatchClause',
        'TryStatement', // 添加TryStatement
        'JSXElement',
        'JSXFragment',
      ];
      
      if (analyzableTypes.includes(node.type)) {
        nodes.push(node);
      }
      
      // 递归遍历子节点
      for (const key in node) {
        const value = node[key];
        
        if (Array.isArray(value)) {
          value.forEach(child => {
            if (child && typeof child === 'object' && child.type) {
              this.collectNodes(child, nodes);
            }
          });
        } else if (value && typeof value === 'object' && value.type) {
          this.collectNodes(value, nodes);
        }
      }
    }
  }
  
  const factory = new CalculatorFactory({
    debugMode: true,
    quiet: true
  });
  
  const debugCalculator = new DebugCalculator({}, factory);
  
  try {
    await debugCalculator.debugAnalyzeFunction(testCode);
  } catch (error) {
    console.error('调试错误:', error);
  } finally {
    await debugCalculator.dispose();
  }
}

debugAsyncRuleEngine().catch(console.error);