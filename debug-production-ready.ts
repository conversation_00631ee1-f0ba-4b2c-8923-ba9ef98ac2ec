#!/usr/bin/env bun

/**
 * 生产就绪的解决方案
 * 结合了手动清理的可靠性和架构改进的优雅性
 */

import { analyzeFile } from './src/index';
import { TextFormatter } from './src/formatters/text';
import { writeFileSync, unlinkSync } from 'fs';

// 全局资源管理器
class ResourceManager {
  private resources: (() => void)[] = [];
  private isCleanedUp = false;

  addCleanupCallback(callback: () => void) {
    this.resources.push(callback);
  }

  cleanup() {
    if (this.isCleanedUp) return;
    this.isCleanedUp = true;

    console.log('\n🧹 清理所有资源...');
    for (const cleanup of this.resources) {
      try {
        cleanup();
      } catch (error) {
        console.warn('⚠️ 清理资源时出现错误:', error.message);
      }
    }
  }
}

const resourceManager = new ResourceManager();

async function debugWithProductionReadySolution() {
  console.log('🔍 调试输出格式问题（生产就绪版）...\n');

  const testCode = `
export function simpleFunction() {
  return 'hello';
}

export function functionWithIf(x: number) {
  if (x > 0) {
    return x * 2;
  }
  return 0;
}

export function complexFunction(a: number, b: number) {
  if (a > 0) {
    if (b > 0) {
      return a + b;
    } else if (b < 0) {
      return a - b;
    }
  }
  return 0;
}

export function verySimpleFunction() {
  console.log('simple');
}
`.trim();

  const testFilePath = `/tmp/test-complexity-${Date.now()}.ts`;
  writeFileSync(testFilePath, testCode);

  // 注册临时文件清理
  resourceManager.addCleanupCallback(() => {
    try {
      unlinkSync(testFilePath);
      console.log('✅ 临时文件已清理');
    } catch {}
  });

  try {
    console.log('🧠 开始分析...');
    const fileResult = await analyzeFile(testFilePath, { enableDetails: true });

    console.log('📊 分析结果:');
    console.log(`- 总复杂度: ${fileResult.complexity}`);
    console.log(`- 平均复杂度: ${fileResult.averageComplexity}`);
    console.log(`- 函数数量: ${fileResult.functions.length}`);

    // 测试格式化器
    const mockAnalysisResult = {
      summary: {
        filesAnalyzed: 1,
        functionsAnalyzed: fileResult.functions.length,
        totalComplexity: fileResult.complexity,
        averageComplexity: fileResult.averageComplexity,
        highComplexityFunctions: fileResult.functions.filter(f => f.complexity > 15).length
      },
      results: [fileResult]
    };

    const formatter = new TextFormatter();
    const detailOutput = await formatter.format(mockAnalysisResult, true);
    console.log('\n📋 详细输出:\n', detailOutput);

    console.log('✅ 分析完成');

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    // 延迟清理所有资源，确保所有异步操作完成
    setTimeout(() => {
      try {
        // 清理全局单例资源
        const { getCodeFrameGenerator } = require('./src/utils/code-frame-generator');
        const { getGlobalFileCache } = require('./src/utils/file-cache');
        
        resourceManager.addCleanupCallback(() => {
          try {
            const codeFrameGen = getCodeFrameGenerator();
            codeFrameGen.destroy();
            console.log('✅ CodeFrameGenerator 已清理');
          } catch {}
        });
        
        resourceManager.addCleanupCallback(() => {
          try {
            const fileCache = getGlobalFileCache();
            fileCache.destroy();
            console.log('✅ FileContentCache 已清理');
          } catch {}
        });

        resourceManager.cleanup();
        console.log('🎉 所有资源已清理，进程将正常退出');
      } catch (error) {
        console.warn('⚠️ 最终清理时出现错误:', error.message);
      }
    }, 100);
  }
}

// 注册进程退出事件的清理
process.on('exit', () => resourceManager.cleanup());
process.on('SIGINT', () => {
  resourceManager.cleanup();
  process.exit(0);
});
process.on('SIGTERM', () => {
  resourceManager.cleanup();
  process.exit(0);
});

// 运行脚本
debugWithProductionReadySolution()
  .then(() => {
    console.log('✨ 脚本正常结束');
  })
  .catch((error) => {
    console.error('💥 脚本失败:', error);
    resourceManager.cleanup();
    process.exit(1);
  });