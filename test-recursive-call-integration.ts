/**
 * RecursiveCallRule简单集成测试
 * 验证RecursiveCallRule是否已正确集成到系统中
 */
import { ComplexityCalculator } from './src/core/calculator';

async function testRecursiveCallIntegration() {
  try {
    console.log('🔄 测试RecursiveCallRule集成...');

    // 测试1: 简单递归函数
    const code1 = `
      function factorial(n) {
        if (n <= 1) return 1;
        return n * factorial(n - 1);
      }
    `;

    const result1 = await ComplexityCalculator.analyze(code1);
    console.log('测试1 - 简单递归函数:');
    console.log(`  - 总复杂度: ${result1.totalComplexity}`);
    console.log(`  - 期望: 2 (if语句+递归调用)`);
    console.log(`  - 实际: ${result1.functions[0]?.complexity}`);
    console.log(`  - ✅ ${result1.totalComplexity === 2 ? '通过' : '失败'}`);

    // 测试2: 方法递归调用
    const code2 = `
      class TreeNode {
        traverse() {
          if (this.hasChildren()) {
            this.traverse();
          }
        }
      }
    `;

    const result2 = await ComplexityCalculator.analyze(code2);
    console.log('\n测试2 - 方法递归调用:');
    console.log(`  - 总复杂度: ${result2.totalComplexity}`);
    console.log(`  - 期望: 2 (if语句+方法递归调用)`);
    console.log(`  - 实际: ${result2.functions[0]?.complexity}`);
    console.log(`  - ✅ ${result2.totalComplexity === 2 ? '通过' : '失败'}`);

    // 测试3: 非递归函数（确保不会误判）
    const code3 = `
      function nonRecursive() {
        someOtherFunction();
        return true;
      }
    `;

    const result3 = await ComplexityCalculator.analyze(code3);
    console.log('\n测试3 - 非递归函数:');
    console.log(`  - 总复杂度: ${result3.totalComplexity}`);
    console.log(`  - 期望: 0 (无复杂度增长)`);
    console.log(`  - 实际: ${result3.functions[0]?.complexity}`);
    console.log(`  - ✅ ${result3.totalComplexity === 0 ? '通过' : '失败'}`);

    // 测试4: 复杂递归场景
    const code4 = `
      function fibonacci(n) {
        if (n <= 1) return n;
        return fibonacci(n-1) + fibonacci(n-2);
      }
    `;

    const result4 = await ComplexityCalculator.analyze(code4);
    console.log('\n测试4 - 复杂递归场景:');
    console.log(`  - 总复杂度: ${result4.totalComplexity}`);
    console.log(`  - 期望: 3 (if语句+2个递归调用)`);
    console.log(`  - 实际: ${result4.functions[0]?.complexity}`);
    console.log(`  - ✅ ${result4.totalComplexity === 3 ? '通过' : '失败'}`);

    console.log('\n🎉 RecursiveCallRule集成测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testRecursiveCallIntegration().catch(console.error);