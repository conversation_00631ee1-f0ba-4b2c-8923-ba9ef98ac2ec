#!/usr/bin/env bun

/**
 * 调试 try-catch 语句的复杂度计算
 */

import { ComplexityCalculator } from './src/core/calculator';

async function debugTryCatch() {
  console.log('=== 调试 try-catch 语句 ===\n');
  
  const testCases = [
    {
      name: "只有 try",
      code: `
        function justTry() {
          try {
            riskyOperation();
          } finally {
            cleanup();
          }
        }
      `
    },
    {
      name: "try-catch",
      code: `
        function tryCatch() {
          try {
            riskyOperation();
          } catch (error) {
            handleError(error);
          }
        }
      `
    },
    {
      name: "try-catch-finally",  
      code: `
        function tryCatchFinally() {
          try {
            riskyOperation();
          } catch (error) {
            handleError(error);
          } finally {
            cleanup();
          }
        }
      `
    },
    {
      name: "多个 catch",
      code: `
        function multipleCatch() {
          try {
            riskyOperation();
          } catch (TypeError: error) {
            handleTypeError(error);
          } catch (Error: error) {
            handleGenericError(error);
          }
        }
      `
    }
  ];

  for (const testCase of testCases) {
    console.log(`--- ${testCase.name} ---`);
    console.log('Code:');
    console.log(testCase.code);
    
    try {
      const results = await ComplexityCalculator.analyze(testCase.code);
      results.forEach(result => {
        console.log(`Function: ${result.name}, Complexity: ${result.complexity}`);
      });
    } catch (error) {
      console.error('Error:', error);
    }
    console.log();
  }
}

debugTryCatch().catch(console.error);