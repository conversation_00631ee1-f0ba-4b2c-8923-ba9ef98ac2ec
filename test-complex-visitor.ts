import { ComplexityCalculator } from './src/core/calculator';

async function testComplexCode() {
  const code = `
function complexFunction(a, b) {
  if (a > 0) {
    for (let i = 0; i < 10; i++) {
      if (b && i % 2 === 0) {
        console.log('even');
      } else if (b || i > 5) {
        console.log('odd or large');
      }
    }
  }
  
  switch (a) {
    case 1:
      return 'one';
    case 2:
      return 'two';
    default:
      return 'other';
  }
}

const arrowFunc = (x) => {
  if (x) {
    return x * 2;
  }
  return 0;
};

class MyClass {
  myMethod() {
    try {
      if (true) {
        throw new Error('test');
      }
    } catch (e) {
      console.log(e);
    }
  }
}
`;
  
  console.log('测试复杂代码的分析...');
  const calculator = new ComplexityCalculator();
  const results = await calculator.calculateCode(code, 'complex.js');
  
  console.log('分析结果:');
  results.forEach(result => {
    console.log(`函数: ${result.name}, 复杂度: ${result.complexity}, 位置: ${result.line}:${result.column}`);
  });
}

testComplexCode().catch(console.error);