#!/usr/bin/env bun

/**
 * 测试规则注册过程中的异常
 */

import { RuleInitializationManager } from './src/core/rule-initialization';

async function testRuleRegistration() {
  console.log('=== 测试规则注册过程 ===\n');
  
  const manager = new RuleInitializationManager(false); // 不静默，显示日志
  
  try {
    console.log('开始初始化AsyncRuleEngine...');
    await manager.initializeAsyncRuleEngine({
      maxRuleConcurrency: 10,
      enableRuleCaching: true,
      ruleDebugMode: false
    });
    
    console.log('\\n获取已注册的规则...');
    const engine = manager.getAsyncRuleEngine();
    if (engine) {
      const allRules = (engine as any).ruleRegistry.getAllRules();
      console.log(`总共注册了 ${allRules.size} 个规则:`);
      allRules.forEach((rule: any) => {
        console.log(`  - ${rule.id} (priority: ${rule.priority})`);
      });
    } else {
      console.log('AsyncRuleEngine未初始化');
    }
    
  } catch (error) {
    console.error('规则注册过程中出现错误:', error);
  }
}

testRuleRegistration().catch(console.error);