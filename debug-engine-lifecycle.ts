#!/usr/bin/env bun

/**
 * 深度调试AsyncRuleEngine实例的生命周期
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';

async function deepDebugAsyncEngine() {
  console.log('=== 深度调试AsyncRuleEngine生命周期 ===\n');
  
  const testCode = `
    function withTryCatch() {
      try {
        riskyOperation();
      } catch (error) {
        handleError(error);
      }
    }
  `;
  
  // 劫持AsyncRuleEngine的构造函数以跟踪实例创建
  const asyncEngineModule = await import('./src/engine/async-engine');
  const originalConstructor = asyncEngineModule.AsyncRuleEngineImpl;
  
  let instanceCount = 0;
  const instances: any[] = [];
  
  // 劫持构造函数
  (asyncEngineModule as any).AsyncRuleEngineImpl = function(...args: any[]) {
    instanceCount++;
    console.log(`[LIFECYCLE] AsyncRuleEngine实例 #${instanceCount} 创建`);
    
    const instance = new originalConstructor(...args);
    instances.push(instance);
    
    // 劫持registerRule方法
    const originalRegisterRule = instance.registerRule.bind(instance);
    instance.registerRule = function(rule: any, quiet: boolean = false) {
      console.log(`[LIFECYCLE] 实例 #${instanceCount} 注册规则: ${rule.id}`);
      return originalRegisterRule(rule, quiet);
    };
    
    // 劫持analyzeNode方法
    const originalAnalyzeNode = instance.analyzeNode.bind(instance);
    instance.analyzeNode = function(node: any, context: any) {
      const allRules = this.ruleRegistry.getAllRules();
      console.log(`[LIFECYCLE] 实例 #${instanceCount} 分析节点 ${node.type}，当前规则数: ${allRules.size || allRules.length}`);
      
      // 检查是否为CatchClause
      if (node.type === 'CatchClause') {
        console.log(`[LIFECYCLE] 实例 #${instanceCount} 处理CatchClause，详细规则:`);
        if (allRules.size) {
          allRules.forEach((rule: any, id: any) => {
            console.log(`  - ${id}: canHandle=${rule.canHandle(node)}`);
          });
        } else if (allRules.length) {
          allRules.forEach((rule: any, index: any) => {
            console.log(`  - ${rule.id}: canHandle=${rule.canHandle(node)}`);
          });
        }
      }
      
      return originalAnalyzeNode(node, context);
    };
    
    return instance;
  };
  
  // 测试
  const factory = new CalculatorFactory({
    debugMode: false,
    quiet: true
  });
  
  const calculator = new ComplexityCalculator({}, factory);
  
  try {
    console.log('\\n开始分析...');
    const results = await calculator.calculateCode(testCode, 'debug.ts');
    console.log('\\n=== 最终结果 ===');
    console.log(results.map(r => ({name: r.name, complexity: r.complexity})));
    
    console.log(`\\n=== 总结 ===`);
    console.log(`总共创建了 ${instanceCount} 个AsyncRuleEngine实例`);
    
  } catch (error) {
    console.error('分析错误:', error);
  } finally {
    await calculator.dispose();
  }
}

deepDebugAsyncEngine().catch(console.error);