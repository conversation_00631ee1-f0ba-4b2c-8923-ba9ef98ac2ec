
> cognitive-complexity@1.0.0 dev /Users/<USER>/Documents/code/personal/cognitive-complexity
> bun run src/cli/index.ts src/cli/index.ts --format json

{
  "summary": {
    "totalComplexity": 0,
    "averageComplexity": 0,
    "filesAnalyzed": 1,
    "functionsAnalyzed": 0,
    "highComplexityFunctions": 0
  },
  "results": [
    {
      "filePath": "src/cli/index.ts",
      "complexity": 0,
      "functions": [],
      "averageComplexity": 0
    }
  ],
  "qualityGateInfo": {
    "passed": true,
    "threshold": 15,
    "failingFunctions": 0
  },
  "metadata": {
    "schemaVersion": "2.1.0",
    "generatedAt": "2025-07-29T06:02:57.982Z",
    "format": "cognitive-complexity-json",
    "detailsEnabled": false,
    "contextEnabled": false,
    "contextAllEnabled": false,
    "errorRecoveryEnabled": true,
    "qualityGate": {
      "passed": true,
      "threshold": 15,
      "failingFunctions": 0
    }
  }
}
