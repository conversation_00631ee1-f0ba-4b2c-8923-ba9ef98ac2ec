# 在线文档和社区支持指南

## 概述

本指南描述了如何建立和维护 Cognitive Complexity Analyzer 的在线文档站点和社区支持体系。

## 文档站点架构

### 技术栈选择

**推荐方案 1: VitePress**
```bash
npm install -D vitepress
```

优势：
- 基于Vue 3，现代化的开发体验
- 内置Markdown扩展和组件支持
- 出色的SEO和性能
- 支持多语言
- 与Vue生态系统完美集成

**推荐方案 2: Docusaurus**
```bash
npx create-docusaurus@latest docs classic --typescript
```

优势：
- Facebook开源，社区活跃
- 强大的博客功能
- 内置搜索和国际化
- 丰富的插件生态
- 适合大型文档项目

### 文档结构设计

```
docs-site/
├── guide/                    # 用户指南
│   ├── getting-started.md    # 快速开始
│   ├── installation.md       # 安装指南
│   ├── basic-usage.md        # 基础用法
│   ├── configuration.md      # 配置说明
│   └── cli-reference.md      # CLI参考
├── api/                      # API文档
│   ├── core-api.md          # 核心API
│   ├── rule-engine.md       # 规则引擎
│   ├── performance.md       # 性能监控
│   └── types.md             # 类型定义
├── plugins/                  # 插件开发
│   ├── development-guide.md  # 开发指南
│   ├── plugin-api.md        # 插件API
│   ├── examples/            # 示例代码
│   └── templates/           # 插件模板
├── react/                   # React专项
│   ├── best-practices.md    # 最佳实践
│   ├── jsx-analysis.md      # JSX分析
│   ├── hooks-complexity.md  # Hooks复杂度
│   └── examples/            # 示例项目
├── advanced/                # 高级主题
│   ├── custom-rules.md      # 自定义规则
│   ├── performance-tuning.md # 性能调优
│   ├── ci-cd-integration.md # CI/CD集成
│   └── troubleshooting.md   # 故障排除
├── community/               # 社区
│   ├── contributing.md      # 贡献指南
│   ├── roadmap.md           # 发展路线图
│   ├── changelog.md         # 更新日志
│   └── faq.md              # 常见问题
└── blog/                   # 博客
    ├── 2024-01-release-v2.md
    ├── performance-tips.md
    └── community-highlights.md
```

## VitePress 配置示例

### 基础配置

```typescript
// .vitepress/config.ts
import { defineConfig } from 'vitepress'

export default defineConfig({
  title: 'Cognitive Complexity Analyzer',
  description: '高性能的认知复杂度分析工具',
  
  lang: 'zh-CN',
  base: '/cognitive-complexity/',
  
  head: [
    ['link', { rel: 'icon', href: '/favicon.ico' }],
    ['meta', { name: 'theme-color', content: '#646cff' }],
    ['meta', { name: 'og:type', content: 'website' }],
    ['meta', { name: 'og:locale', content: 'zh_CN' }],
    ['meta', { name: 'og:site_name', content: 'Cognitive Complexity Analyzer' }]
  ],

  themeConfig: {
    logo: '/logo.svg',
    
    nav: [
      { text: '指南', link: '/guide/getting-started' },
      { text: 'API', link: '/api/core-api' },
      { text: '插件', link: '/plugins/development-guide' },
      { text: 'React', link: '/react/best-practices' },
      { 
        text: '版本', 
        items: [
          { text: 'v2.0', link: '/v2/' },
          { text: 'v1.x', link: '/v1/' }
        ]
      }
    ],

    sidebar: {
      '/guide/': [
        {
          text: '入门',
          items: [
            { text: '快速开始', link: '/guide/getting-started' },
            { text: '安装指南', link: '/guide/installation' },
            { text: '基础用法', link: '/guide/basic-usage' }
          ]
        },
        {
          text: '配置',
          items: [
            { text: '配置文件', link: '/guide/configuration' },
            { text: 'CLI参考', link: '/guide/cli-reference' },
            { text: '基线管理', link: '/guide/baseline' }
          ]
        }
      ],
      
      '/api/': [
        {
          text: '核心API',
          items: [
            { text: '概述', link: '/api/core-api' },
            { text: '规则引擎', link: '/api/rule-engine' },
            { text: '性能监控', link: '/api/performance' },
            { text: '类型定义', link: '/api/types' }
          ]
        }
      ],
      
      '/plugins/': [
        {
          text: '插件开发',
          items: [
            { text: '开发指南', link: '/plugins/development-guide' },
            { text: 'API参考', link: '/plugins/plugin-api' },
            { text: '示例插件', link: '/plugins/examples' },
            { text: '插件模板', link: '/plugins/templates' }
          ]
        }
      ]
    },

    socialLinks: [
      { icon: 'github', link: 'https://github.com/your-org/cognitive-complexity' },
      { icon: 'discord', link: 'https://discord.gg/cognitive-complexity' }
    ],

    footer: {
      message: 'MIT Licensed',
      copyright: 'Copyright © 2024 Cognitive Complexity Team'
    },

    search: {
      provider: 'algolia',
      options: {
        appId: 'YOUR_APP_ID',
        apiKey: 'YOUR_API_KEY',
        indexName: 'cognitive-complexity'
      }
    },

    editLink: {
      pattern: 'https://github.com/your-org/cognitive-complexity/edit/main/docs/:path',
      text: '在GitHub上编辑此页'
    },

    lastUpdated: {
      text: '最后更新',
      formatOptions: {
        dateStyle: 'medium',
        timeStyle: 'short'
      }
    }
  },

  markdown: {
    theme: 'github-dark',
    lineNumbers: true,
    
    config: (md) => {
      // 添加自定义Markdown插件
      md.use(require('markdown-it-task-lists'))
      md.use(require('markdown-it-footnote'))
    }
  },

  vite: {
    define: {
      __VUE_OPTIONS_API__: false
    }
  }
})
```

### 自定义组件

```vue
<!-- .vitepress/components/ApiReference.vue -->
<template>
  <div class="api-reference">
    <h3>{{ method.name }}</h3>
    <div class="signature">
      <code>{{ method.signature }}</code>
    </div>
    <div class="description">
      {{ method.description }}
    </div>
    <div class="parameters" v-if="method.parameters">
      <h4>参数</h4>
      <ul>
        <li v-for="param in method.parameters" :key="param.name">
          <code>{{ param.name }}</code>
          <span class="type">{{ param.type }}</span>
          <span class="desc">{{ param.description }}</span>
        </li>
      </ul>
    </div>
    <div class="example" v-if="method.example">
      <h4>示例</h4>
      <pre><code>{{ method.example }}</code></pre>
    </div>
  </div>
</template>

<script setup>
defineProps({
  method: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.api-reference {
  border: 1px solid var(--vp-c-border);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.signature {
  background: var(--vp-c-bg-alt);
  padding: 0.5rem;
  border-radius: 4px;
  margin: 0.5rem 0;
}

.type {
  color: var(--vp-c-brand);
  font-family: var(--vp-font-family-mono);
}
</style>
```

```vue
<!-- .vitepress/components/ComplexityMeter.vue -->
<template>
  <div class="complexity-meter">
    <div class="meter-bar">
      <div 
        class="meter-fill" 
        :style="{ width: `${percentage}%`, backgroundColor: color }"
      ></div>
    </div>
    <div class="meter-info">
      <span class="complexity-value">{{ complexity }}</span>
      <span class="complexity-label">{{ label }}</span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  complexity: {
    type: Number,
    required: true
  },
  max: {
    type: Number,
    default: 20
  }
})

const percentage = computed(() => Math.min((props.complexity / props.max) * 100, 100))

const color = computed(() => {
  if (props.complexity <= 5) return '#28a745'
  if (props.complexity <= 10) return '#ffc107'
  if (props.complexity <= 15) return '#fd7e14'
  return '#dc3545'
})

const label = computed(() => {
  if (props.complexity <= 5) return '简单'
  if (props.complexity <= 10) return '中等'
  if (props.complexity <= 15) return '复杂'
  return '非常复杂'
})
</script>
```

## 社区支持体系

### 1. GitHub 社区

**Issues 模板**

```markdown
<!-- .github/ISSUE_TEMPLATE/bug-report.md -->
---
name: Bug 报告
about: 报告一个bug帮助我们改进
title: '[BUG] '
labels: bug
assignees: ''
---

## Bug 描述
简洁清晰地描述bug是什么。

## 重现步骤
重现此行为的步骤：
1. 运行命令 '...'
2. 分析文件 '...'
3. 查看错误 '...'

## 预期行为
简洁清晰地描述你期望发生什么。

## 实际行为
简洁清晰地描述实际发生了什么。

## 环境信息
- 操作系统: [e.g. Windows 11, macOS 13, Ubuntu 22.04]
- Node.js版本: [e.g. 18.17.0]
- 工具版本: [e.g. 2.0.1]

## 附加信息
在这里添加任何关于问题的其他上下文、截图或文件。
```

```markdown
<!-- .github/ISSUE_TEMPLATE/feature-request.md -->
---
name: 功能请求
about: 建议这个项目的新功能
title: '[FEATURE] '
labels: enhancement
assignees: ''
---

## 功能描述
简洁清晰地描述你希望实现的功能。

## 问题背景
你遇到的问题是什么？这个功能如何解决问题？

## 解决方案
你认为的理想解决方案是什么？

## 替代方案
你考虑过的任何替代解决方案或功能。

## 附加信息
在这里添加任何关于功能请求的其他上下文或截图。
```

**讨论区设置**

```yaml
# .github/discussion-template.yml
body:
  - type: dropdown
    id: category
    attributes:
      label: 讨论类别
      options:
        - 一般讨论
        - 功能建议
        - 使用帮助
        - 插件开发
        - 性能优化
        - React最佳实践
    validations:
      required: true
      
  - type: textarea
    id: description
    attributes:
      label: 描述
      description: 详细描述你的问题或想法
    validations:
      required: true
```

### 2. Discord 社区服务器

**频道结构**

```
认知复杂度分析工具 Discord服务器
├── 📋 信息
│   ├── #公告
│   ├── #规则
│   └── #常见问题
├── 💬 讨论
│   ├── #一般讨论
│   ├── #功能建议
│   └── #展示项目
├── 🛠️ 开发
│   ├── #插件开发
│   ├── #贡献讨论
│   └── #技术问题
├── 📚 学习
│   ├── #最佳实践
│   ├── #React专区
│   └── #性能优化
└── 🎯 专题
    ├── #每周挑战
    ├── #代码审查
    └── #工具集成
```

**Discord Bot 配置**

```javascript
// discord-bot/index.js
const { Client, GatewayIntentBits } = require('discord.js');
const client = new Client({ 
  intents: [
    GatewayIntentBits.Guilds, 
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent
  ] 
});

// 复杂度分析命令
client.on('messageCreate', async (message) => {
  if (message.content.startsWith('!analyze')) {
    const code = message.content.slice(8).trim();
    if (code.startsWith('```') && code.endsWith('```')) {
      const codeBlock = code.slice(3, -3);
      const analysis = await analyzeCode(codeBlock);
      
      const embed = {
        title: '复杂度分析结果',
        color: getComplexityColor(analysis.complexity),
        fields: [
          {
            name: '复杂度',
            value: analysis.complexity.toString(),
            inline: true
          },
          {
            name: '等级',
            value: getComplexityLevel(analysis.complexity),
            inline: true
          },
          {
            name: '建议',
            value: analysis.suggestions.join('\n') || '代码结构良好',
            inline: false
          }
        ]
      };
      
      message.reply({ embeds: [embed] });
    }
  }
});

function getComplexityColor(complexity) {
  if (complexity <= 5) return 0x28a745; // 绿色
  if (complexity <= 10) return 0xffc107; // 黄色
  if (complexity <= 15) return 0xfd7e14; // 橙色
  return 0xdc3545; // 红色
}
```

### 3. 在线示例和演练场

**CodePen/CodeSandbox 集成**

```html
<!-- docs/examples/online-playground.md -->
# 在线演练场

## 基础示例

<iframe 
  src="https://codesandbox.io/embed/cognitive-complexity-basic-example" 
  style="width:100%; height:500px; border:0; border-radius: 4px; overflow:hidden;"
  title="Cognitive Complexity Basic Example"
  allow="accelerometer; ambient-light-sensor; camera; encrypted-media; geolocation; gyroscope; hid; microphone; midi; payment; usb; vr; xr-spatial-tracking"
  sandbox="allow-forms allow-modals allow-popups allow-presentation allow-same-origin allow-scripts"
></iframe>

## React Hooks 示例

<iframe 
  src="https://codesandbox.io/embed/cognitive-complexity-react-hooks" 
  style="width:100%; height:500px; border:0; border-radius: 4px; overflow:hidden;"
  title="Cognitive Complexity React Hooks"
></iframe>
```

### 4. 社区活动

**每周挑战赛**

```markdown
# 每周复杂度挑战 #1

## 挑战描述
重构以下高复杂度函数，将复杂度降低到10以下，同时保持功能不变。

\`\`\`typescript
function processUserData(users: User[], filters: FilterOptions): ProcessedUser[] {
  const result: ProcessedUser[] = [];
  
  for (const user of users) {
    if (filters.includeInactive || user.isActive) {
      if (filters.ageRange) {
        if (user.age >= filters.ageRange.min && user.age <= filters.ageRange.max) {
          if (filters.departments && filters.departments.length > 0) {
            if (filters.departments.includes(user.department)) {
              const processedUser = processUser(user);
              if (processedUser) {
                result.push(processedUser);
              }
            }
          } else {
            const processedUser = processUser(user);
            if (processedUser) {
              result.push(processedUser);
            }
          }
        }
      } else {
        // ... 更多嵌套逻辑
      }
    }
  }
  
  return result;
}
\`\`\`

## 参与方式
1. Fork [示例仓库](https://github.com/community/weekly-challenge-1)
2. 提交你的重构方案
3. 运行复杂度分析验证结果
4. 创建Pull Request

## 奖励
- 最佳重构方案：GitHub Pro订阅一年
- 创意奖：工具官方周边
- 参与奖：Discord特殊徽章

## 截止时间
2024年1月31日 23:59 UTC
```

### 5. 文档贡献系统

**文档贡献指南**

```markdown
# 文档贡献指南

## 贡献方式

### 1. 修正错误
发现文档中的错误？点击页面底部的"在GitHub上编辑此页"链接直接修改。

### 2. 添加示例
我们欢迎更多实用示例：
- React组件复杂度分析案例
- 插件开发教程
- CI/CD集成实践
- 性能优化经验

### 3. 翻译文档
帮助我们将文档翻译成更多语言：
- 英文 (English)
- 日文 (日本語)
- 韩文 (한국어)

## 文档规范

### 代码示例
- 使用TypeScript编写示例
- 提供完整的导入语句
- 包含实际运行结果
- 添加必要的注释

### 截图和图表
- 使用统一的主题和样式
- 图片大小不超过500KB
- 包含替代文本(alt text)
- 使用描述性文件名

### 写作风格
- 使用简洁明了的语言
- 避免过于技术化的术语
- 提供实际使用场景
- 包含故障排除提示
```

## 自动化部署

### GitHub Actions 配置

```yaml
# .github/workflows/docs.yml
name: Deploy Documentation

on:
  push:
    branches: [main]
    paths: ['docs/**']
  pull_request:
    branches: [main]
    paths: ['docs/**']

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: npm
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build documentation
        run: npm run docs:build
        
      - name: Deploy to GitHub Pages
        if: github.ref == 'refs/heads/main'
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: docs/.vitepress/dist
          
      - name: Deploy to Vercel
        if: github.ref == 'refs/heads/main'
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          working-directory: docs/.vitepress/dist
```

### 多语言支持

```typescript
// .vitepress/config/index.ts
import { defineConfig } from 'vitepress'
import { zhConfig } from './zh'
import { enConfig } from './en'

export default defineConfig({
  locales: {
    root: {
      label: '简体中文',
      lang: 'zh-CN',
      ...zhConfig
    },
    en: {
      label: 'English',
      lang: 'en-US',
      ...enConfig
    }
  }
})
```

通过建立完善的在线文档和社区支持体系，可以大大提升工具的可用性和社区参与度。