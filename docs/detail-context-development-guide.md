# 详细上下文功能开发规范

## 概述

本文档规定了详细上下文功能相关代码的开发规范、架构原则和实现标准。所有参与此功能开发的开发者都应遵循这些规范，以确保代码质量、一致性和可维护性。

## 架构设计原则

### 1. 模块化设计

#### 核心模块职责分离
- **DetailCollector**: 负责收集复杂度详细信息
- **CodeFrameGenerator**: 负责生成代码上下文框架
- **PositionConverter**: 负责 SWC span 到行列位置的转换
- **SmartContextFilter**: 负责智能过滤上下文信息
- **FileContentCache**: 负责文件内容缓存

#### 模块依赖规则
```
CLI层 → 格式化层 → 核心逻辑层 → 工具层
```

- CLI层只能依赖格式化层
- 格式化层只能依赖核心逻辑层
- 核心逻辑层只能依赖工具层
- 严禁反向依赖和循环依赖

### 2. 接口设计原则

#### 统一的类型定义
所有详细上下文相关的类型都应在 `src/core/types.ts` 中定义：

```typescript
// 扩展现有的 DetailStep 接口
interface DetailStep {
  // 基础信息
  line: number;
  column: number;
  increment: number;
  cumulative: number;
  ruleId: string;
  description: string;
  nestingLevel: number;
  
  // 上下文相关
  context?: string;
  span?: { start: number; end: number };
  shouldShowContext?: boolean;
  contextRank?: number;
}
```

#### 配置接口标准化
```typescript
interface DetailFormattingOptions {
  showContext: boolean;
  contextOptions: CodeFrameOptions;
  filterOptions: FilterOptions;
}

interface CodeFrameOptions {
  highlightCode?: boolean;
  linesAbove?: number;
  linesBelow?: number; 
  forceColor?: boolean;
}

interface FilterOptions {
  minComplexityIncrement: number;
  maxContextItems: number;
  forceShowAll: boolean;
}
```

### 3. 错误处理策略

#### 分层错误处理
```typescript
// 工具层错误 - 具体的技术错误
class CodeFrameGenerationError extends Error {
  constructor(message: string, public filePath: string, public line: number) {
    super(message);
    this.name = 'CodeFrameGenerationError';
  }
}

// 业务层错误 - 用户操作相关错误
class ContextDisplayError extends Error {
  constructor(message: string, public cause?: Error) {
    super(message);
    this.name = 'ContextDisplayError';
    this.cause = cause;
  }
}
```

#### 错误恢复机制
```typescript
// 优雅降级示例
async function generateCodeFrameWithFallback(
  filePath: string,
  line: number,
  column: number
): Promise<CodeFrameResult> {
  try {
    return await this.codeFrameGenerator.generateFrame(filePath, line, column);
  } catch (error) {
    console.warn(`代码框架生成失败: ${error.message}`);
    return this.generateFallbackFrame(filePath, line);
  }
}
```

## 代码规范

### 1. 命名约定

#### 类命名
- 使用 PascalCase
- 名称应该清晰表达职责
- 避免缩写和简称

```typescript
// 正确
class CodeFrameGenerator {}
class PositionConverter {}
class SmartContextFilter {}

// 错误  
class CFGen {}
class PosConv {}
class Filter {}
```

#### 方法命名
- 使用 camelCase
- 使用动词开头表达动作
- 布尔返回值使用 is/has/should 前缀

```typescript
// 正确
generateFrame(filePath: string): Promise<string>
shouldShowContext(step: DetailStep): boolean
hasValidSpan(span: Span): boolean

// 错误
frame(filePath: string): Promise<string>
contextShow(step: DetailStep): boolean
spanValid(span: Span): boolean
```

#### 接口和类型命名
- 使用 PascalCase
- 接口通常不使用 I 前缀
- 类型别名使用描述性名称

```typescript
// 正确
interface CodeFrameOptions {}
type FilterResult = {
  filtered: DetailStep[];
  summary: string;
};

// 错误
interface ICodeFrameOptions {}
type Result = any;
```

### 2. 函数设计原则

#### 单一职责原则
每个函数只做一件事：

```typescript
// 正确 - 职责单一
async function readFileContent(filePath: string): Promise<string> {
  return fs.readFile(filePath, 'utf8');
}

function convertSpanToPosition(sourceCode: string, span: Span): Position {
  const lines = sourceCode.split('\n');
  // 转换逻辑
}

// 错误 - 职责混合
async function readFileAndConvertPosition(filePath: string, span: Span): Promise<Position> {
  const content = await fs.readFile(filePath, 'utf8');
  const lines = content.split('\n');
  // 转换逻辑
}
```

#### 参数设计
- 超过 3 个参数使用配置对象
- 使用 TypeScript 严格类型
- 提供合理的默认值

```typescript
// 正确
interface GenerateFrameOptions {
  highlightCode?: boolean;
  linesAbove?: number;
  linesBelow?: number;
  forceColor?: boolean;
}

async function generateFrame(
  filePath: string,
  position: Position,
  options: GenerateFrameOptions = {}
): Promise<string>

// 错误
async function generateFrame(
  filePath: string,
  line: number,
  column: number,
  highlightCode: boolean,
  linesAbove: number,
  linesBelow: number,
  forceColor: boolean
): Promise<string>
```

#### 异步处理
- 统一使用 async/await
- 正确处理 Promise 并发
- 避免不必要的 async 包装

```typescript
// 正确 - 合理的异步使用
async function generateMultipleFrames(
  requests: FrameRequest[]
): Promise<CodeFrameResult[]> {
  return Promise.all(
    requests.map(request => this.generateFrame(request))
  );
}

// 错误 - 不必要的异步包装
async function simpleCalculation(a: number, b: number): Promise<number> {
  return a + b; // 同步操作不需要异步
}
```

### 3. 性能优化规范

#### 缓存策略
```typescript
class FileContentCache {
  private cache = new Map<string, string>();
  private maxSize = 100 * 1024 * 1024; // 100MB
  private currentSize = 0;

  async getFileContent(filePath: string): Promise<string> {
    if (this.cache.has(filePath)) {
      return this.cache.get(filePath)!;
    }

    const content = await fs.readFile(filePath, 'utf8');
    this.addToCache(filePath, content);
    return content;
  }

  private addToCache(key: string, content: string): void {
    const size = Buffer.byteLength(content, 'utf8');
    
    // 检查缓存大小限制
    if (this.currentSize + size > this.maxSize) {
      this.evictOldEntries();
    }

    this.cache.set(key, content);
    this.currentSize += size;
  }
}
```

#### 内存管理
```typescript
// 正确 - 及时释放资源
class CodeFrameGenerator {
  private fileContentCache: FileContentCache;

  constructor() {
    this.fileContentCache = new FileContentCache();
  }

  async generateFrames(requests: FrameRequest[]): Promise<CodeFrameResult[]> {
    try {
      const results = await Promise.all(
        requests.map(request => this.generateFrame(request))
      );
      return results;
    } finally {
      // 清理临时资源（如果有）
      this.fileContentCache.clearTemporary();
    }
  }
}
```

### 4. 测试规范

#### 单元测试结构
```typescript
describe('CodeFrameGenerator', () => {
  let generator: CodeFrameGenerator;
  let mockFileSystem: MockFileSystem;

  beforeEach(() => {
    mockFileSystem = new MockFileSystem();
    generator = new CodeFrameGenerator(mockFileSystem);
  });

  afterEach(() => {
    mockFileSystem.cleanup();
  });

  describe('generateFrame', () => {
    test('应该为有效位置生成代码框架', async () => {
      // Arrange
      const filePath = '/test/file.ts';
      const content = 'export function test() {\n  return true;\n}';
      mockFileSystem.addFile(filePath, content);
      
      // Act
      const result = await generator.generateFrame(filePath, { line: 1, column: 0 });
      
      // Assert
      expect(result).toContain('export function test()');
      expect(result).toContain('1 |');
    });

    test('应该处理无效文件路径', async () => {
      // Arrange
      const invalidPath = '/nonexistent/file.ts';
      
      // Act & Assert
      await expect(
        generator.generateFrame(invalidPath, { line: 1, column: 0 })
      ).rejects.toThrow('File not found');
    });
  });
});
```

#### 集成测试规范
```typescript
describe('Detail Context Integration', () => {
  test('应该端到端显示详细上下文', async () => {
    await TestUtils.withTempDir(async (tempDir) => {
      // 创建测试文件
      const testFile = path.join(tempDir, 'test.ts');
      await fs.writeFile(testFile, complexTestCode);

      // 执行CLI命令
      const result = await CLITestingUtils.runCommand([
        '--details',
        '--show-context',
        testFile
      ]);

      // 验证结果
      expect(result.exitCode).toBe(0);
      expect(result.stdout).toContain('Details:');
      expect(result.stdout).toContain('Context:');
      expect(result.stdout).toMatch(/\d+\s*\|/); // 行号格式
    });
  });
});
```

## 代码审查标准

### 1. 审查检查清单

#### 功能性审查
- [ ] 是否正确实现了需求规范
- [ ] 是否处理了所有边界情况
- [ ] 是否有适当的错误处理
- [ ] 是否有性能考虑

#### 代码质量审查
- [ ] 代码是否遵循命名约定
- [ ] 函数是否职责单一
- [ ] 是否有适当的注释
- [ ] 是否遵循 TypeScript 最佳实践

#### 测试审查
- [ ] 是否有充分的单元测试
- [ ] 是否有集成测试覆盖
- [ ] 测试是否覆盖边界情况
- [ ] 测试是否可读和可维护

### 2. 性能审查标准

#### 内存使用
```typescript
// 需要审查的内存使用模式
class BadExample {
  private allFiles: Map<string, string> = new Map(); // 潜在内存泄漏

  async processFiles(files: string[]): Promise<void> {
    for (const file of files) {
      const content = await fs.readFile(file, 'utf8');
      this.allFiles.set(file, content); // 无限制增长
      // 处理逻辑
    }
  }
}

// 推荐的内存管理模式
class GoodExample {
  private cache = new LRUCache<string, string>({ max: 100 });

  async processFiles(files: string[]): Promise<void> {
    for (const file of files) {
      const content = await this.getFileContent(file);
      // 处理逻辑
      // cache 会自动管理内存
    }
  }
}
```

#### 并发控制
```typescript
// 需要审查并发处理
async function processFilesWithConcurrencyControl(
  files: string[],
  maxConcurrency: number = 10
): Promise<void> {
  const semaphore = new Semaphore(maxConcurrency);
  
  await Promise.all(
    files.map(async (file) => {
      await semaphore.acquire();
      try {
        await processFile(file);
      } finally {
        semaphore.release();
      }
    })
  );
}
```

## 文档标准

### 1. 代码注释规范

#### JSDoc 注释
```typescript
/**
 * 生成指定位置的代码框架
 * 
 * @param filePath - 文件路径
 * @param position - 代码位置（行列）
 * @param options - 生成选项
 * @returns Promise 解析为格式化的代码框架字符串
 * 
 * @throws {CodeFrameGenerationError} 当文件不存在或位置无效时
 * 
 * @example
 * ```typescript
 * const frame = await generator.generateFrame(
 *   'src/example.ts',
 *   { line: 10, column: 5 },
 *   { highlightCode: true, linesAbove: 2 }
 * );
 * console.log(frame);
 * ```
 */
async generateFrame(
  filePath: string,
  position: Position,
  options: CodeFrameOptions = {}
): Promise<string>
```

#### 内联注释
```typescript
function complexAlgorithm(input: any[]): any[] {
  // 第一阶段：预处理输入数据
  const preprocessed = input.filter(item => item != null);
  
  // 第二阶段：应用业务规则
  // 这里的复杂度计算考虑嵌套级别
  const processed = preprocessed.map(item => {
    if (item.type === 'complex') {
      // +1 for if statement
      return this.processComplexItem(item);
    } else {
      // +1 for else branch
      return this.processSimpleItem(item);
    }
  });
  
  return processed;
}
```

### 2. API 文档规范

每个公共模块都应该有完整的 API 文档：

```markdown
## CodeFrameGenerator

### 概述
负责基于文件位置生成带语法高亮的代码上下文框架。

### 构造函数
```typescript
constructor(fileSystem?: FileSystem)
```

### 方法

#### generateFrame
生成单个位置的代码框架。

**签名**:
```typescript
generateFrame(filePath: string, position: Position, options?: CodeFrameOptions): Promise<string>
```

**参数**:
- `filePath`: 目标文件的绝对路径
- `position`: 目标代码位置
- `options`: 可选的生成配置

**返回值**:
返回 Promise，解析为格式化的代码框架字符串。

**异常**:
- `CodeFrameGenerationError`: 文件不存在或位置无效
- `FileSystemError`: 文件系统访问错误
```

## 部署和发布规范

### 1. 版本控制

#### 语义化版本控制
- **主版本号**: API 不兼容变更
- **次版本号**: 新增功能，向后兼容
- **修订版本号**: 错误修复，向后兼容

#### 变更日志规范
```markdown
## [1.2.0] - 2024-01-15

### Added
- 新增代码上下文显示功能
- 新增智能过滤算法
- 新增 HTML 输出格式支持

### Changed
- 改进了 CLI 参数解析逻辑
- 优化了大文件处理性能

### Fixed
- 修复了 Windows 路径分隔符问题
- 修复了 Unicode 字符显示问题

### Deprecated
- `--legacy-format` 参数将在下个主版本移除
```

### 2. 构建和测试

#### 预发布检查
```bash
# 运行完整测试套件
npm run test

# 执行类型检查
npm run type-check

# 执行代码质量检查
npm run lint

# 执行性能基准测试
npm run benchmark

# 验证构建产物
npm run build && npm run test:dist
```

#### 持续集成配置
```yaml
name: CI/CD Pipeline

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20]
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm test
      
      - name: Run type checking
        run: npm run type-check
      
      - name: Run linting
        run: npm run lint
      
      - name: Test build
        run: npm run build
```

## 故障排除和维护

### 1. 调试指南

#### 启用调试模式
```bash
# 环境变量配置
export DEBUG=complexity:detail-context
export COMPLEXITY_LOG_LEVEL=debug

# 运行时启用详细日志
complexity --details --show-context --debug src/file.ts
```

#### 常见问题诊断
```typescript
// 添加诊断日志
class CodeFrameGenerator {
  async generateFrame(filePath: string, position: Position): Promise<string> {
    console.debug(`生成代码框架: ${filePath}:${position.line}:${position.column}`);
    
    try {
      const content = await this.fileCache.getContent(filePath);
      console.debug(`文件内容长度: ${content.length}`);
      
      const frame = await this.doGenerateFrame(content, position);
      console.debug(`生成的框架长度: ${frame.length}`);
      
      return frame;
    } catch (error) {
      console.error(`代码框架生成失败: ${error.message}`, {
        filePath,
        position,
        stack: error.stack
      });
      throw error;
    }
  }
}
```

### 2. 性能监控

#### 内置性能指标
```typescript
class PerformanceMonitor {
  private metrics = new Map<string, number>();

  startTiming(name: string): void {
    this.metrics.set(name, Date.now());
  }

  endTiming(name: string): number {
    const start = this.metrics.get(name);
    if (!start) throw new Error(`未找到计时器: ${name}`);
    
    const duration = Date.now() - start;
    console.debug(`${name} 耗时: ${duration}ms`);
    return duration;
  }
}

// 使用示例
const monitor = new PerformanceMonitor();

monitor.startTiming('generateFrame');
const frame = await generator.generateFrame(filePath, position);
monitor.endTiming('generateFrame');
```

## 总结

本开发规范涵盖了详细上下文功能开发的所有重要方面。遵循这些规范将确保：

1. **代码质量**: 统一的编码标准和最佳实践
2. **可维护性**: 清晰的架构和模块化设计
3. **性能**: 优化的算法和资源管理
4. **可测试性**: 全面的测试覆盖和规范
5. **可扩展性**: 灵活的设计支持未来扩展

所有开发者在贡献代码时都应参考此规范，并在代码审查过程中严格执行这些标准。