# 插件开发完整指南

## 目录
- [插件系统概述](#插件系统概述)
- [快速开始](#快速开始)
- [插件架构深入](#插件架构深入)
- [规则开发详解](#规则开发详解)
- [API参考](#api参考)
- [开发示例](#开发示例)
- [测试和调试](#测试和调试)
- [最佳实践](#最佳实践)
- [发布和分发](#发布和分发)
- [插件模板](#插件模板)

## 插件系统概述

Cognitive Complexity Analyzer 的插件系统基于现代化异步规则引擎设计，允许开发者：

### 核心特性

- **动态加载**: 运行时加载和卸载插件，无需重启应用
- **热重载**: 开发期间自动重载插件，提升开发效率
- **错误隔离**: 插件错误不影响核心功能，提供优雅降级
- **类型安全**: 完整的TypeScript类型支持
- **生命周期管理**: 完整的插件生命周期钩子
- **性能监控**: 内置性能监控和优化建议

### 插件能力

```typescript
// 插件可以做什么
interface PluginCapabilities {
  // 规则扩展
  customRules: Rule[];           // 添加自定义分析规则
  ruleOverrides: RuleOverride[]; // 覆盖现有规则行为
  
  // 分析扩展
  preAnalysis: AnalysisHook[];   // 分析前处理
  postAnalysis: AnalysisHook[];  // 分析后处理
  
  // 输出扩展
  formatters: Formatter[];       // 自定义输出格式
  reporters: Reporter[];         // 自定义报告生成
  
  // 集成扩展
  integrations: Integration[];   // 第三方工具集成
  webhooks: WebhookHandler[];    // 事件通知
}
```

## 快速开始

### 1. 创建插件项目

```bash
# 使用官方模板创建插件
npx create-complexity-plugin my-awesome-plugin

# 或手动创建
mkdir my-complexity-plugin
cd my-complexity-plugin
npm init -y

# 安装依赖
npm install --save-dev typescript @types/node
npm install @imd/cognitive-complexity
```

### 2. 基础插件结构

```typescript
// src/index.ts - 插件入口文件
import { Plugin, Rule, AnalysisContext, RuleResult, Node } from '@imd/cognitive-complexity';

// 自定义规则示例
class ConsoleLogRule implements Rule {
  readonly id = 'no-console-log';
  readonly name = 'Console.log Detection';
  readonly priority = 50;
  
  canHandle(node: Node): boolean {
    return (
      node.type === 'CallExpression' &&
      node.callee?.type === 'MemberExpression' &&
      node.callee.object?.name === 'console' &&
      node.callee.property?.name === 'log'
    );
  }
  
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return {
      ruleId: this.id,
      complexity: 1, // console.log 增加复杂度
      isExempted: false,
      shouldIncreaseNesting: false,
      reason: 'Console.log statement detected',
      suggestions: [
        'Consider using a proper logging library',
        'Remove debug console.log statements'
      ],
      metadata: { 
        nodeType: 'CallExpression',
        severity: 'warning',
        category: 'code-quality'
      },
      executionTime: performance.now(),
      cacheHit: false
    };
  }
  
  getDependencies(): string[] {
    return []; // 无依赖
  }
}

// 主插件导出
export const myPlugin: Plugin = {
  id: 'my-awesome-plugin',
  name: 'My Awesome Complexity Plugin',
  version: '1.0.0',
  description: 'A plugin that detects console.log statements',
  author: 'Your Name <<EMAIL>>',
  
  // 生命周期钩子
  async initialize(context) {
    console.log('Plugin initialized:', this.name);
    
    // 注册自定义规则
    context.registerRule(new ConsoleLogRule());
    
    // 注册格式化器
    context.registerFormatter({
      id: 'custom-text',
      name: 'Custom Text Format',
      format: async (result) => {
        return `Custom Report: ${result.summary.totalComplexity} total complexity`;
      }
    });
  },
  
  async analyze(node, context) {
    // 可选：全局分析钩子
    if (node.type === 'FunctionDeclaration') {
      return {
        complexity: 0, // 不增加复杂度
        metadata: { analyzed: true }
      };
    }
    
    return { complexity: 0 };
  },
  
  async finalize(results) {
    // 可选：结果后处理
    console.log(`Analyzed ${results.summary.functionsAnalyzed} functions`);
    return results;
  },
  
  async cleanup() {
    // 清理资源
    console.log('Plugin cleanup completed');
  }
};

export default myPlugin;
```

### 3. 插件配置文件

```json
// package.json
{
  "name": "my-complexity-plugin",
  "version": "1.0.0",
  "description": "Custom complexity analysis plugin",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "keywords": ["complexity", "plugin", "analysis"],
  
  "scripts": {
    "build": "tsc",
    "dev": "tsc -w",
    "test": "vitest",
    "prepublishOnly": "npm run build"
  },
  
  "peerDependencies": {
    "@imd/cognitive-complexity": "^1.0.0"
  },
  
  "devDependencies": {
    "typescript": "^5.0.0",
    "@types/node": "^18.0.0"
  },
  
  "files": [
    "dist/**/*",
    "README.md"
  ],
  
  "cognitive-complexity-plugin": {
    "entry": "dist/index.js",
    "configSchema": "./schema.json"
  }
}
```

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./src",
    "declaration": true,
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist", "**/*.test.ts"]
}
```

### 4. 使用插件

```javascript
// cognitive-complexity.config.js
module.exports = {
  plugins: [
    {
      path: './my-complexity-plugin',
      config: {
        enableConsoleLogDetection: true,
        severity: 'warning'
      }
    }
  ]
};
```

## 插件架构深入

### 插件生命周期

```typescript
interface PluginLifecycle {
  // 1. 初始化阶段
  initialize?(context: PluginContext): Promise<void>;
  
  // 2. 配置验证阶段
  validateConfig?(config: any): ValidationResult;
  
  // 3. 分析阶段钩子
  beforeAnalysis?(files: string[]): Promise<void>;
  analyze?(node: Node, context: AnalysisContext): Promise<PluginResult>;
  afterAnalysis?(results: AnalysisResult): Promise<AnalysisResult>;
  
  // 4. 报告生成阶段
  beforeReport?(results: AnalysisResult): Promise<void>;
  afterReport?(report: string): Promise<string>;
  
  // 5. 清理阶段
  cleanup?(): Promise<void>;
}

// 完整的生命周期实现
export class AdvancedPlugin implements Plugin, PluginLifecycle {
  readonly id = 'advanced-plugin';
  readonly name = 'Advanced Analysis Plugin';
  readonly version = '1.0.0';
  
  private config: PluginConfig = {};
  private metrics: PluginMetrics = new PluginMetrics();
  
  async initialize(context: PluginContext): Promise<void> {
    this.config = context.config;
    
    // 验证配置
    const validation = this.validateConfig(this.config);
    if (!validation.valid) {
      throw new Error(`Invalid plugin config: ${validation.errors.join(', ')}`);
    }
    
    // 注册多个规则
    const rules = [
      new CustomComplexityRule(this.config),
      new PerformanceRule(this.config),
      new SecurityRule(this.config)
    ];
    
    rules.forEach(rule => context.registerRule(rule));
    
    // 注册事件监听器
    context.on('analysis:start', this.onAnalysisStart.bind(this));
    context.on('analysis:complete', this.onAnalysisComplete.bind(this));
    
    this.metrics.recordInitialization();
  }
  
  validateConfig(config: any): ValidationResult {
    const errors: string[] = [];
    
    if (config.threshold && typeof config.threshold !== 'number') {
      errors.push('threshold must be a number');
    }
    
    if (config.rules && !Array.isArray(config.rules)) {
      errors.push('rules must be an array');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  async beforeAnalysis(files: string[]): Promise<void> {
    console.log(`Starting analysis of ${files.length} files`);
    this.metrics.recordAnalysisStart(files.length);
  }
  
  async analyze(node: Node, context: AnalysisContext): Promise<PluginResult> {
    // 全局分析逻辑
    const startTime = performance.now();
    
    try {
      const result = await this.performAnalysis(node, context);
      this.metrics.recordSuccess(performance.now() - startTime);
      return result;
    } catch (error) {
      this.metrics.recordError(error);
      throw error;
    }
  }
  
  async afterAnalysis(results: AnalysisResult): Promise<AnalysisResult> {
    // 结果后处理
    const enhancedResults = {
      ...results,
      pluginMetrics: this.metrics.getReport(),
      recommendations: this.generateRecommendations(results)
    };
    
    return enhancedResults;
  }
  
  async cleanup(): Promise<void> {
    this.metrics.recordCleanup();
    console.log('Plugin cleanup completed');
  }
  
  private async performAnalysis(node: Node, context: AnalysisContext): Promise<PluginResult> {
    // 自定义分析逻辑
    return { complexity: 0 };
  }
  
  private generateRecommendations(results: AnalysisResult): string[] {
    const recommendations: string[] = [];
    
    if (results.summary.averageComplexity > 10) {
      recommendations.push('Consider refactoring high-complexity functions');
    }
    
    return recommendations;
  }
  
  private onAnalysisStart(): void {
    console.log('Analysis started');
  }
  
  private onAnalysisComplete(): void {
    console.log('Analysis completed');
  }
}
```

### 插件上下文 API

```typescript
interface PluginContext {
  // 核心引擎访问
  readonly engine: AsyncRuleEngine;
  readonly config: PluginConfig;
  readonly version: string;
  
  // 规则注册
  registerRule(rule: Rule): void;
  unregisterRule(ruleId: string): void;
  getRules(): Rule[];
  
  // 格式化器注册
  registerFormatter(formatter: Formatter): void;
  unregisterFormatter(formatterId: string): void;
  
  // 事件系统
  on(event: string, handler: EventHandler): void;
  off(event: string, handler: EventHandler): void;
  emit(event: string, data: any): void;
  
  // 缓存访问
  getCache(): CacheManager;
  setCacheData(key: string, data: any, ttl?: number): void;
  getCacheData(key: string): any;
  
  // 日志记录
  log(level: LogLevel, message: string, meta?: any): void;
  
  // 文件系统访问
  readFile(path: string): Promise<string>;
  writeFile(path: string, content: string): Promise<void>;
  
  // 配置访问
  getGlobalConfig(): CognitiveConfig;
  getPluginConfig(pluginId: string): any;
}

// 使用上下文的高级示例
class ContextAwarePlugin implements Plugin {
  private context!: PluginContext;
  
  async initialize(context: PluginContext): Promise<void> {
    this.context = context;
    
    // 监听文件变化事件
    context.on('file:changed', this.onFileChanged.bind(this));
    
    // 访问全局配置
    const globalConfig = context.getGlobalConfig();
    const pluginConfig = context.getPluginConfig(this.id);
    
    // 缓存配置数据
    context.setCacheData('plugin-config', pluginConfig, 3600000); // 1小时TTL
    
    // 记录日志
    context.log('info', 'Plugin initialized', { 
      plugin: this.id,
      config: pluginConfig 
    });
  }
  
  private async onFileChanged(filePath: string): Promise<void> {
    // 响应文件变化
    this.context.log('debug', `File changed: ${filePath}`);
    
    // 清除相关缓存
    this.context.getCache().invalidatePattern(`file:${filePath}:*`);
  }
}
```

## 规则开发详解

### 规则基类和工具

```typescript
// 高性能规则基类
abstract class AdvancedRule implements Rule {
  abstract readonly id: string;
  abstract readonly name: string;
  abstract readonly priority: number;
  
  protected config: RuleConfig;
  protected cache: Map<string, RuleResult> = new Map();
  
  constructor(config: RuleConfig = {}) {
    this.config = config;
  }
  
  // 抽象方法
  abstract canHandle(node: Node): boolean;
  abstract async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult>;
  
  // 缓存支持
  protected async evaluateWithCache(
    node: Node, 
    context: AnalysisContext,
    evaluator: () => Promise<RuleResult>
  ): Promise<RuleResult> {
    const cacheKey = this.generateCacheKey(node, context);
    
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!;
      return { ...cached, cacheHit: true };
    }
    
    const result = await evaluator();
    this.cache.set(cacheKey, result);
    
    // LRU缓存限制
    if (this.cache.size > 1000) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    return result;
  }
  
  protected generateCacheKey(node: Node, context: AnalysisContext): string {
    return `${this.id}:${node.type}:${this.getNodeHash(node)}:${context.filePath}`;
  }
  
  protected getNodeHash(node: Node): string {
    // 简化的节点哈希
    return JSON.stringify({
      type: node.type,
      start: node.start,
      end: node.end
    });
  }
  
  // 工具方法
  protected createResult(
    complexity: number,
    reason: string,
    suggestions: string[] = [],
    metadata: any = {}
  ): RuleResult {
    return {
      ruleId: this.id,
      complexity,
      isExempted: complexity === 0,
      shouldIncreaseNesting: false,
      reason,
      suggestions,
      metadata: { ...metadata, rule: this.name },
      executionTime: performance.now(),
      cacheHit: false
    };
  }
  
  protected isInTestFile(context: AnalysisContext): boolean {
    return /\.(test|spec)\.(ts|tsx|js|jsx)$/.test(context.filePath);
  }
  
  protected getComplexityLevel(complexity: number): 'low' | 'medium' | 'high' | 'critical' {
    if (complexity <= 5) return 'low';
    if (complexity <= 10) return 'medium';
    if (complexity <= 20) return 'high';
    return 'critical';
  }
  
  getDependencies(): string[] {
    return [];
  }
}

// 具体规则实现示例
class AsyncAwaitRule extends AdvancedRule {
  readonly id = 'async-await-complexity';
  readonly name = 'Async/Await Complexity Analysis';
  readonly priority = 75;
  
  canHandle(node: Node): boolean {
    return (
      node.type === 'FunctionDeclaration' ||
      node.type === 'FunctionExpression' ||
      node.type === 'ArrowFunctionExpression'
    ) && (node as any).async === true;
  }
  
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const asyncComplexity = await this.calculateAsyncComplexity(node, context);
      
      const level = this.getComplexityLevel(asyncComplexity);
      const suggestions = this.generateAsyncSuggestions(asyncComplexity, level);
      
      return this.createResult(
        asyncComplexity,
        `Async function with ${asyncComplexity} complexity points`,
        suggestions,
        { 
          level,
          asyncPatterns: this.detectAsyncPatterns(node),
          hasErrorHandling: this.hasErrorHandling(node)
        }
      );
    });
  }
  
  private async calculateAsyncComplexity(node: Node, context: AnalysisContext): Promise<number> {
    let complexity = 0;
    
    // 遍历函数体查找async相关模式
    const visitor = new AsyncComplexityVisitor();
    visitor.visit(node);
    
    // 基础async函数复杂度
    complexity += 1;
    
    // await表达式复杂度
    complexity += visitor.awaitCount * 0.5;
    
    // Promise.all/race复杂度
    complexity += visitor.promiseCombinatorsCount * 2;
    
    // try-catch with async
    complexity += visitor.errorHandlingCount * 1;
    
    // 嵌套async函数
    complexity += visitor.nestedAsyncCount * 2;
    
    return Math.round(complexity);
  }
  
  private generateAsyncSuggestions(complexity: number, level: string): string[] {
    const suggestions: string[] = [];
    
    if (level === 'high' || level === 'critical') {
      suggestions.push('Consider breaking down this async function into smaller parts');
      suggestions.push('Use Promise.all() for parallel operations where possible');
    }
    
    if (complexity > 10) {
      suggestions.push('Add comprehensive error handling with try-catch blocks');
      suggestions.push('Consider using async/await consistently instead of mixing with .then()');
    }
    
    return suggestions;
  }
  
  private detectAsyncPatterns(node: Node): string[] {
    // 检测async模式
    return ['basic-async']; // 简化实现
  }
  
  private hasErrorHandling(node: Node): boolean {
    // 检查是否有错误处理
    return false; // 简化实现
  }
}

// AST访问器工具类
class AsyncComplexityVisitor {
  awaitCount = 0;
  promiseCombinatorsCount = 0;
  errorHandlingCount = 0;
  nestedAsyncCount = 0;
  
  visit(node: Node): void {
    // 递归访问AST节点
    this.visitNode(node);
  }
  
  private visitNode(node: Node): void {
    switch (node.type) {
      case 'AwaitExpression':
        this.awaitCount++;
        break;
      case 'CallExpression':
        this.visitCallExpression(node as any);
        break;
      case 'TryStatement':
        this.errorHandlingCount++;
        break;
      case 'FunctionDeclaration':
      case 'FunctionExpression':  
      case 'ArrowFunctionExpression':
        if ((node as any).async) {
          this.nestedAsyncCount++;
        }
        break;
    }
    
    // 递归访问子节点
    this.visitChildren(node);
  }
  
  private visitCallExpression(node: any): void {
    if (
      node.callee?.type === 'MemberExpression' &&
      node.callee.object?.name === 'Promise' &&
      ['all', 'race', 'allSettled'].includes(node.callee.property?.name)
    ) {
      this.promiseCombinatorsCount++;
    }
  }
  
  private visitChildren(node: Node): void {
    // 简化的子节点访问
    for (const key in node) {
      const child = (node as any)[key];
      if (child && typeof child === 'object') {
        if (Array.isArray(child)) {
          child.forEach(item => {
            if (item && typeof item === 'object' && item.type) {
              this.visitNode(item);
            }
          });
        } else if (child.type) {
          this.visitNode(child);
        }
      }
    }
  }
}
```

### 特定语言规则示例

```typescript
// React专用规则
class ReactHookComplexityRule extends AdvancedRule {
  readonly id = 'react-hook-complexity';
  readonly name = 'React Hook Complexity Analysis';
  readonly priority = 85;
  
  private readonly HOOK_PATTERNS = [
    'useState', 'useEffect', 'useContext', 'useReducer',
    'useCallback', 'useMemo', 'useRef', 'useImperativeHandle'
  ];
  
  canHandle(node: Node): boolean {
    return node.type === 'CallExpression' && this.isHookCall(node);
  }
  
  private isHookCall(node: Node): boolean {
    const callNode = node as any;
    return (
      callNode.callee?.type === 'Identifier' &&
      this.HOOK_PATTERNS.includes(callNode.callee.name)
    );
  }
  
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return this.evaluateWithCache(node, context, async () => {
      const hookName = (node as any).callee.name;
      const complexity = await this.calculateHookComplexity(node, hookName, context);
      
      return this.createResult(
        complexity,
        `React ${hookName} hook with complexity ${complexity}`,
        this.generateHookSuggestions(hookName, complexity),
        { 
          hookType: hookName,
          isCustomHook: this.isCustomHook(context),
          hasCleanup: this.hasCleanupFunction(node, hookName)
        }
      );
    });
  }
  
  private async calculateHookComplexity(
    node: Node, 
    hookName: string, 
    context: AnalysisContext
  ): Promise<number> {
    let complexity = 0;
    
    switch (hookName) {
      case 'useState':
        complexity = 1;
        break;
      case 'useEffect':
        complexity = await this.calculateEffectComplexity(node);
        break;
      case 'useCallback':
      case 'useMemo':
        complexity = await this.calculateMemoComplexity(node);
        break;
      case 'useReducer':
        complexity = 3; // 较复杂的状态管理
        break;
      default:
        complexity = 1;
    }
    
    return complexity;
  }
  
  private async calculateEffectComplexity(node: Node): Promise<number> {
    const effectNode = node as any;
    let complexity = 2; // 基础useEffect复杂度
    
    // 检查依赖数组
    if (effectNode.arguments[1]) {
      const deps = effectNode.arguments[1];
      if (deps.type === 'ArrayExpression') {
        complexity += Math.min(deps.elements.length * 0.5, 3); // 依赖越多越复杂
      }
    } else {
      complexity += 1; // 无依赖数组，每次渲染都执行
    }
    
    // 检查cleanup函数
    const effectCallback = effectNode.arguments[0];
    if (this.hasReturnStatement(effectCallback)) {
      complexity += 1; // 有cleanup逻辑
    }
    
    return Math.round(complexity);
  }
  
  private async calculateMemoComplexity(node: Node): Promise<number> {
    const memoNode = node as any;
    let complexity = 1; // 基础memo复杂度
    
    // 检查依赖数组长度
    if (memoNode.arguments[1]?.type === 'ArrayExpression') {
      const depsLength = memoNode.arguments[1].elements.length;
      complexity += Math.min(depsLength * 0.3, 2);
    }
    
    return Math.round(complexity);
  }
  
  private generateHookSuggestions(hookName: string, complexity: number): string[] {
    const suggestions: string[] = [];
    
    if (hookName === 'useEffect' && complexity > 4) {
      suggestions.push('Consider splitting this useEffect into multiple effects');
      suggestions.push('Minimize the dependency array to improve performance');
    }
    
    if (hookName === 'useCallback' && complexity > 3) {
      suggestions.push('Ensure dependencies are stable to avoid unnecessary re-renders');
    }
    
    if (complexity > 5) {
      suggestions.push('Consider extracting this logic into a custom hook');
    }
    
    return suggestions;
  }
  
  private isCustomHook(context: AnalysisContext): boolean {
    return context.currentFunction?.name?.startsWith('use') || false;
  }
  
  private hasCleanupFunction(node: Node, hookName: string): boolean {
    if (hookName !== 'useEffect') return false;
    
    const effectCallback = (node as any).arguments[0];
    return this.hasReturnStatement(effectCallback);
  }
  
  private hasReturnStatement(node: Node): boolean {
    // 简化的return语句检测
    return JSON.stringify(node).includes('"type":"ReturnStatement"');
  }
}
```

## API参考

### 核心接口

```typescript
// 插件主接口
interface Plugin {
  // 基本信息
  readonly id: string;
  readonly name: string;
  readonly version: string;
  readonly description?: string;
  readonly author?: string;
  readonly homepage?: string;
  readonly repository?: string;
  
  // 生命周期钩子
  initialize?(context: PluginContext): Promise<void>;
  analyze?(node: Node, context: AnalysisContext): Promise<PluginResult>;
  finalize?(results: AnalysisResult): Promise<AnalysisResult>;
  cleanup?(): Promise<void>;
}

// 规则接口
interface Rule {
  readonly id: string;
  readonly name: string;
  readonly priority: number; // 数值越高优先级越高
  
  canHandle(node: Node): boolean;
  evaluate(node: Node, context: AnalysisContext): Promise<RuleResult>;
  getDependencies(): string[];
}

// 格式化器接口
interface Formatter {
  readonly id: string;
  readonly name: string;
  readonly extension: string;
  
  format(results: AnalysisResult, config?: FormatterConfig): Promise<string>;
  getConfigSchema?(): JSONSchema;
}

// 集成接口
interface Integration {
  readonly id: string;
  readonly name: string;
  readonly platform: string; // 'github', 'gitlab', 'jenkins', etc.
  
  setup(config: IntegrationConfig): Promise<void>;
  notify(results: AnalysisResult): Promise<void>;
  teardown?(): Promise<void>;
}
```

### 工具函数

```typescript
// 插件开发工具函数
export class PluginUtils {
  // AST工具
  static isFunction(node: Node): boolean {
    return ['FunctionDeclaration', 'FunctionExpression', 'ArrowFunctionExpression']
      .includes(node.type);
  }
  
  static isLoop(node: Node): boolean {
    return ['ForStatement', 'WhileStatement', 'DoWhileStatement', 'ForInStatement', 'ForOfStatement']
      .includes(node.type);
  }
  
  static isConditional(node: Node): boolean {
    return ['IfStatement', 'ConditionalExpression', 'SwitchStatement']
      .includes(node.type);
  }
  
  // 复杂度计算工具
  static calculateNestingLevel(node: Node): number {
    let level = 0;
    let current = node.parent;
    
    while (current) {
      if (this.isFunction(current) || this.isLoop(current) || this.isConditional(current)) {
        level++;
      }
      current = current.parent;
    }
    
    return level;
  }
  
  // 文件检测工具
  static isTestFile(filePath: string): boolean {
    return /\.(test|spec)\.(ts|tsx|js|jsx)$/.test(filePath);
  }
  
  static isTypeDefinitionFile(filePath: string): boolean {
    return filePath.endsWith('.d.ts');
  }
  
  static getFileExtension(filePath: string): string {
    return filePath.split('.').pop()?.toLowerCase() || '';
  }
  
  // 配置工具
  static mergeConfigs<T>(base: T, override: Partial<T>): T {
    return { ...base, ...override };
  }
  
  static validateConfig(config: any, schema: JSONSchema): ValidationResult {
    // JSON Schema验证实现
    return { valid: true, errors: [] };
  }
  
  // 性能监控工具
  static measurePerformance<T>(
    name: string, 
    fn: () => Promise<T>
  ): Promise<{ result: T; duration: number }> {
    return new Promise(async (resolve) => {
      const start = performance.now();
      const result = await fn();
      const duration = performance.now() - start;
      
      resolve({ result, duration });
    });
  }
}

// 缓存工具
export class PluginCache {
  private cache = new Map<string, { data: any; expiry: number }>();
  
  set(key: string, data: any, ttlMs = 300000): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + ttlMs
    });
  }
  
  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  has(key: string): boolean {
    return this.get(key) !== null;
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  size(): number {
    return this.cache.size;
  }
}
```

## 测试和调试

### 插件测试框架

```typescript
// test/plugin.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { createTestContext, parseCode } from '@imd/cognitive-complexity/testing';
import { myPlugin } from '../src/index';

describe('MyPlugin', () => {
  let context: TestPluginContext;
  
  beforeEach(async () => {
    context = await createTestContext();
    await myPlugin.initialize!(context);
  });
  
  describe('ConsoleLogRule', () => {
    it('should detect console.log statements', async () => {
      const code = `
        function example() {
          console.log('debug message');
          return 42;
        }
      `;
      
      const ast = parseCode(code);
      const results = await context.analyzeCode(code);
      
      expect(results.functions).toHaveLength(1);
      expect(results.functions[0].complexity).toBe(2); // 1 (function) + 1 (console.log)
      
      const consoleLogRules = results.functions[0].appliedRules
        .filter(rule => rule.ruleId === 'no-console-log');
      
      expect(consoleLogRules).toHaveLength(1);
      expect(consoleLogRules[0].suggestions).toContain('Consider using a proper logging library');
    });
    
    it('should not detect other console methods', async () => {
      const code = `
        function example() {
          console.error('error message');
          console.warn('warning message');
          return 42;
        }
      `;
      
      const results = await context.analyzeCode(code);
      
      const consoleLogRules = results.functions[0].appliedRules
        .filter(rule => rule.ruleId === 'no-console-log');
      
      expect(consoleLogRules).toHaveLength(0);
    });
  });
  
  describe('Plugin lifecycle', () => {
    it('should initialize correctly', async () => {
      expect(context.getRegisteredRules().some(rule => rule.id === 'no-console-log')).toBe(true);
    });
    
    it('should handle cleanup', async () => {
      await myPlugin.cleanup!();
      // 验证清理是否正确
    });
  });
  
  describe('Performance', () => {
    it('should process large files efficiently', async () => {
      const largeCode = generateLargeCodeSample(1000); // 1000行代码
      
      const startTime = performance.now();
      await context.analyzeCode(largeCode);
      const duration = performance.now() - startTime;
      
      expect(duration).toBeLessThan(5000); // 5秒内完成
    });
  });
});

// 测试工具函数
function generateLargeCodeSample(lines: number): string {
  const functions = [];
  
  for (let i = 0; i < lines / 10; i++) {
    functions.push(`
      function func${i}() {
        console.log('function ${i}');
        if (Math.random() > 0.5) {
          console.log('random condition');
          return ${i};
        }
        return 0;
      }
    `);
  }
  
  return functions.join('\n');
}

// 集成测试
describe('Plugin Integration', () => {
  it('should work with real projects', async () => {
    const projectPath = './test-fixtures/sample-project';
    const results = await context.analyzeProject(projectPath);
    
    expect(results.summary.filesAnalyzed).toBeGreaterThan(0);
    expect(results.summary.functionsAnalyzed).toBeGreaterThan(0);
  });
  
  it('should handle TypeScript files', async () => {
    const tsCode = `
      interface User {
        name: string;
        age: number;
      }
      
      function processUser(user: User): string {
        console.log('processing user:', user.name);
        if (user.age > 18) {
          return 'adult';
        }
        return 'minor';
      }
    `;
    
    const results = await context.analyzeCode(tsCode, 'test.ts');
    expect(results.functions[0].complexity).toBe(3); // function + console.log + if
  });
});
```

### 调试工具

```typescript
// debug/debug-plugin.ts
export class PluginDebugger {
  private logs: DebugLog[] = [];
  private performance: PerformanceEntry[] = [];
  
  enableDebugMode(plugin: Plugin): Plugin {
    return new Proxy(plugin, {
      get(target, prop) {
        const value = target[prop as keyof Plugin];
        
        if (typeof value === 'function') {
          return new Proxy(value, {
            apply: (fn, thisArg, args) => {
              const startTime = performance.now();
              
              this.log('debug', `Calling ${String(prop)}`, { args });
              
              try {
                const result = fn.apply(thisArg, args);
                
                if (result instanceof Promise) {
                  return result
                    .then(res => {
                      this.recordPerformance(String(prop), performance.now() - startTime);
                      this.log('debug', `${String(prop)} completed`, { result: res });
                      return res;
                    })
                    .catch(err => {
                      this.log('error', `${String(prop)} failed`, { error: err });
                      throw err;
                    });
                }
                
                this.recordPerformance(String(prop), performance.now() - startTime);
                this.log('debug', `${String(prop)} completed`, { result });
                return result;
              } catch (error) {
                this.log('error', `${String(prop)} failed`, { error });
                throw error;
              }
            }
          });
        }
        
        return value;
      }
    });
  }
  
  private log(level: string, message: string, meta: any = {}): void {
    this.logs.push({
      timestamp: new Date(),
      level,
      message,
      meta
    });
    
    console.log(`[${level.toUpperCase()}] ${message}`, meta);
  }
  
  private recordPerformance(operation: string, duration: number): void {
    this.performance.push({
      operation,
      duration,
      timestamp: new Date()
    });
  }
  
  getDebugReport(): DebugReport {
    return {
      logs: this.logs,
      performance: this.performance,
      summary: {
        totalOperations: this.performance.length,
        averageDuration: this.performance.reduce((sum, p) => sum + p.duration, 0) / this.performance.length,
        slowestOperation: this.performance.reduce((max, p) => p.duration > max.duration ? p : max, this.performance[0])
      }
    };
  }
}

// 使用调试器
const debugger = new PluginDebugger();
const debugPlugin = debugger.enableDebugMode(myPlugin);

// 运行分析
await runAnalysisWithPlugin(debugPlugin);

// 获取调试报告
const report = debugger.getDebugReport();
console.log('Debug Report:', report);
```

## 最佳实践

### 1. 性能优化

```typescript
// 高性能规则实践
class HighPerformanceRule extends AdvancedRule {
  // 1. 使用快速节点类型检查
  canHandle(node: Node): boolean {
    // 避免复杂的检查逻辑
    return node.type === 'FunctionDeclaration';
  }
  
  // 2. 实现智能缓存
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    // 使用基类的缓存机制
    return this.evaluateWithCache(node, context, async () => {
      return await this.performEvaluation(node, context);
    });
  }
  
  // 3. 批量处理
  async evaluateBatch(nodes: Node[], context: AnalysisContext): Promise<RuleResult[]> {
    // 并行处理多个节点
    return Promise.all(
      nodes.map(node => this.evaluate(node, context))
    );
  }
  
  // 4. 内存管理
  private performEvaluation(node: Node, context: AnalysisContext): Promise<RuleResult> {
    // 及时释放大对象
    const analysis = this.analyzeNode(node);
    const result = this.createResult(analysis.complexity, analysis.reason);
    
    // 清理临时数据
    analysis.dispose?.();
    
    return Promise.resolve(result);
  }
}
```

### 2. 错误处理

```typescript
class RobustRule extends AdvancedRule {
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    try {
      return await this.safeEvaluate(node, context);
    } catch (error) {
      // 记录错误但不中断分析
      context.log('error', `Rule ${this.id} failed`, { 
        error: error.message,
        nodeType: node.type,
        filePath: context.filePath
      });
      
      // 返回默认结果
      return this.createResult(0, 'Rule evaluation failed', [], {
        error: true,
        errorMessage: error.message
      });
    }
  }
  
  private async safeEvaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    // 验证输入
    if (!node || !context) {
      throw new Error('Invalid input parameters');
    }
    
    // 超时保护
    return Promise.race([
      this.performAnalysis(node, context),
      this.createTimeoutPromise(5000) // 5秒超时
    ]);
  }
  
  private createTimeoutPromise(ms: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`Rule evaluation timeout (${ms}ms)`)), ms);
    });
  }
}
```

### 3. 配置管理

```typescript
// 配置架构
interface PluginConfig {
  enabled: boolean;
  severity: 'error' | 'warning' | 'info';
  rules: RuleConfig[];
  performance: {
    cacheEnabled: boolean;
    timeoutMs: number;
    maxCacheSize: number;
  };
  debug: {
    enabled: boolean;
    verbose: boolean;
    logPerformance: boolean;
  };
}

class ConfigurablePlugin implements Plugin {
  private config: PluginConfig;
  
  constructor(config: Partial<PluginConfig> = {}) {
    this.config = this.mergeWithDefaults(config);
  }
  
  private mergeWithDefaults(config: Partial<PluginConfig>): PluginConfig {
    return {
      enabled: true,
      severity: 'warning',
      rules: [],
      performance: {
        cacheEnabled: true,
        timeoutMs: 5000,
        maxCacheSize: 1000
      },
      debug: {
        enabled: false,
        verbose: false,
        logPerformance: false
      },
      ...config
    };
  }
  
  async initialize(context: PluginContext): Promise<void> {
    // 验证配置
    this.validateConfig();
    
    // 根据配置启用功能
    if (this.config.debug.enabled) {
      this.enableDebugMode(context);
    }
    
    if (this.config.performance.cacheEnabled) {
      this.enableCaching(context);
    }
  }
  
  private validateConfig(): void {
    if (this.config.performance.timeoutMs < 1000) {
      throw new Error('Timeout must be at least 1000ms');
    }
    
    if (this.config.performance.maxCacheSize < 10) {
      throw new Error('Cache size must be at least 10');
    }
  }
}
```

## 发布和分发

### 1. 插件包结构

```
my-complexity-plugin/
├── src/
│   ├── index.ts              # 插件入口
│   ├── rules/               # 规则实现
│   │   ├── console-log.ts
│   │   └── async-await.ts
│   ├── formatters/          # 格式化器
│   │   └── custom-json.ts
│   └── utils/               # 工具函数
│       └── ast-utils.ts
├── test/                    # 测试文件
│   ├── plugin.test.ts
│   └── fixtures/
├── docs/                    # 文档
│   ├── README.md
│   └── API.md
├── dist/                    # 构建输出
├── package.json
├── tsconfig.json
├── schema.json              # 配置架构
└── .npmignore
```

### 2. 发布配置

```json
// package.json
{
  "name": "@yourorg/complexity-plugin-name",
  "version": "1.0.0",
  "description": "Custom complexity analysis plugin",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  
  "keywords": [
    "complexity",
    "analysis", 
    "plugin",
    "typescript",
    "javascript",
    "cognitive-complexity"
  ],
  
  "scripts": {
    "build": "tsc",
    "test": "vitest",
    "prepare": "npm run build",
    "prepublishOnly": "npm test && npm run build"
  },
  
  "peerDependencies": {
    "@imd/cognitive-complexity": "^1.0.0"
  },
  
  "files": [
    "dist/**/*",
    "schema.json",
    "README.md"
  ],
  
  "cognitive-complexity-plugin": {
    "entry": "dist/index.js",
    "configSchema": "./schema.json",
    "supportedVersions": "^1.0.0"
  }
}
```

```json
// schema.json - 配置架构
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "properties": {
    "enabled": {
      "type": "boolean",
      "default": true,
      "description": "Enable this plugin"
    },
    "severity": {
      "type": "string",
      "enum": ["error", "warning", "info"],
      "default": "warning",
      "description": "Default severity level"
    },
    "rules": {
      "type": "object",
      "properties": {
        "consoleLog": {
          "type": "object",
          "properties": {
            "enabled": {
              "type": "boolean",
              "default": true
            },
            "allowInTests": {
              "type": "boolean", 
              "default": true
            }
          }
        }
      }
    }
  }
}
```

### 3. 发布流程

```bash
# 1. 构建和测试
npm run build
npm test

# 2. 版本管理
npm version patch  # 或 minor, major

# 3. 发布到npm
npm publish

# 4. 创建GitHub release
git tag v1.0.0
git push origin --tags
```

## 插件模板

### 基础插件模板

```typescript
// template/basic-plugin.ts
import { Plugin, Rule, AnalysisContext, RuleResult, Node } from '@imd/cognitive-complexity';

// 插件配置接口
interface MyPluginConfig {
  enabled: boolean;
  customThreshold: number;
  excludePatterns: string[];
}

// 自定义规则
class MyCustomRule implements Rule {
  readonly id = 'my-custom-rule';
  readonly name = 'My Custom Analysis Rule';
  readonly priority = 50;
  
  private config: MyPluginConfig;
  
  constructor(config: MyPluginConfig) {
    this.config = config;
  }
  
  canHandle(node: Node): boolean {
    // 判断是否处理此节点
    return node.type === 'FunctionDeclaration';
  }
  
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    // 实现分析逻辑
    const complexity = this.calculateComplexity(node, context);
    
    return {
      ruleId: this.id,
      complexity,
      isExempted: complexity === 0,
      shouldIncreaseNesting: false,
      reason: `Custom rule applied with complexity ${complexity}`,
      suggestions: this.generateSuggestions(complexity),
      metadata: { customRule: true },
      executionTime: performance.now(),
      cacheHit: false
    };
  }
  
  private calculateComplexity(node: Node, context: AnalysisContext): number {
    // 自定义复杂度计算逻辑
    return 1;
  }
  
  private generateSuggestions(complexity: number): string[] {
    if (complexity > this.config.customThreshold) {
      return ['Consider refactoring this function'];
    }
    return [];
  }
  
  getDependencies(): string[] {
    return [];
  }
}

// 主插件导出
export const myPlugin: Plugin = {
  id: 'my-custom-plugin',
  name: 'My Custom Plugin',
  version: '1.0.0',
  description: 'A template for creating custom complexity plugins',
  
  async initialize(context) {
    const config: MyPluginConfig = {
      enabled: true,
      customThreshold: 10,
      excludePatterns: [],
      ...context.config
    };
    
    if (config.enabled) {
      context.registerRule(new MyCustomRule(config));
    }
    
    context.log('info', 'My Custom Plugin initialized');
  }
};

export default myPlugin;
```

### 高级插件模板

```typescript
// template/advanced-plugin.ts
import { 
  Plugin, 
  Rule, 
  AnalysisContext, 
  RuleResult, 
  Node,
  Formatter,
  Integration
} from '@imd/cognitive-complexity';

// 完整的插件实现模板
export class AdvancedPluginTemplate implements Plugin {
  readonly id = 'advanced-plugin-template';
  readonly name = 'Advanced Plugin Template';
  readonly version = '1.0.0';
  readonly description = 'A comprehensive plugin template with all features';
  
  private config: any = {};
  private rules: Rule[] = [];
  private formatters: Formatter[] = [];
  private integrations: Integration[] = [];
  
  async initialize(context) {
    this.config = context.config;
    
    // 初始化规则
    this.initializeRules(context);
    
    // 初始化格式化器
    this.initializeFormatters(context);
    
    // 初始化集成
    this.initializeIntegrations(context);
    
    // 设置事件监听
    this.setupEventListeners(context);
  }
  
  private initializeRules(context): void {
    // 注册多个规则
    this.rules = [
      new CustomComplexityRule(this.config),
      new PerformanceRule(this.config),
      new SecurityRule(this.config)
    ];
    
    this.rules.forEach(rule => context.registerRule(rule));
  }
  
  private initializeFormatters(context): void {
    // 注册自定义格式化器
    this.formatters = [
      new CustomJSONFormatter(),
      new XMLFormatter(),
      new MarkdownFormatter()
    ];
    
    this.formatters.forEach(formatter => context.registerFormatter(formatter));
  }
  
  private initializeIntegrations(context): void {
    // 注册第三方集成
    this.integrations = [
      new SlackIntegration(this.config.slack),
      new JiraIntegration(this.config.jira),
      new GitHubIntegration(this.config.github)
    ];
    
    this.integrations.forEach(integration => {
      if (integration.isConfigured()) {
        integration.setup();
      }
    });
  }
  
  private setupEventListeners(context): void {
    context.on('analysis:start', this.onAnalysisStart.bind(this));
    context.on('analysis:complete', this.onAnalysisComplete.bind(this));
    context.on('file:processed', this.onFileProcessed.bind(this));
  }
  
  async analyze(node: Node, context: AnalysisContext) {
    // 全局分析钩子
    return { complexity: 0 };
  }
  
  async finalize(results) {
    // 结果后处理
    const enhancedResults = {
      ...results,
      pluginData: {
        pluginId: this.id,
        version: this.version,
        timestamp: new Date().toISOString()
      }
    };
    
    // 发送到集成服务
    await this.notifyIntegrations(enhancedResults);
    
    return enhancedResults;
  }
  
  async cleanup() {
    // 清理资源
    await Promise.all(
      this.integrations.map(integration => integration.teardown?.())
    );
  }
  
  private async onAnalysisStart(): Promise<void> {
    console.log('Analysis started');
  }
  
  private async onAnalysisComplete(results): Promise<void> {
    console.log(`Analysis completed: ${results.summary.totalComplexity} total complexity`);
  }
  
  private async onFileProcessed(filePath: string): Promise<void> {
    console.log(`Processed file: ${filePath}`);
  }
  
  private async notifyIntegrations(results): Promise<void> {
    await Promise.all(
      this.integrations
        .filter(integration => integration.isConfigured())
        .map(integration => integration.notify(results))
    );
  }
}

export default new AdvancedPluginTemplate();
```

通过这个完整的插件开发指南，开发者可以创建功能强大、性能优秀的自定义复杂度分析插件，扩展工具的分析能力。
  
  // 插件初始化
  async initialize(context) {
    console.log('Plugin initialized');
  },
  
  // 分析钩子
  async analyze(node, context) {
    // 自定义分析逻辑
    return {
      complexity: 0,
      suggestions: []
    };
  },
  
  // 清理钩子
  async finalize(results) {
    return results;
  }
};

export default myPlugin;
```

### 3. 配置文件

```json
// package.json
{
  "name": "my-complexity-plugin",
  "version": "1.0.0",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "keywords": ["cognitive-complexity", "plugin", "code-analysis"],
  "files": ["dist/"],
  "scripts": {
    "build": "tsc",
    "dev": "tsc --watch",
    "test": "jest"
  },
  "peerDependencies": {
    "@imd/cognitive-complexity": "^1.0.0"
  }
}
```

```json
// tsconfig.json
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "declaration": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "dist"]
}
```

## 插件架构

### 插件接口定义

```typescript
// 核心插件接口
export interface Plugin {
  // 基础信息
  id: string;
  name: string;
  version: string;
  description?: string;
  author?: string;
  homepage?: string;
  
  // 依赖信息
  dependencies?: string[];
  peerDependencies?: Record<string, string>;
  
  // 生命周期钩子
  initialize?(context: PluginContext): Promise<void>;
  analyze?(node: Node, context: AnalysisContext): Promise<RuleResult>;
  finalize?(results: AnalysisResult): Promise<AnalysisResult>;
  dispose?(): Promise<void>;
  
  // 配置
  configSchema?: ConfigSchema;
  validateConfig?(config: any): ValidationResult;
}

// 插件上下文
export interface PluginContext {
  // 引擎实例
  engine: AsyncRuleEngine;
  
  // 配置信息
  config: ResolvedConfig;
  pluginConfig: any;
  
  // 工具函数
  logger: Logger;
  cache: CacheManager;
  
  // 扩展点
  registerRule(rule: Rule): void;
  registerFormatter(formatter: Formatter): void;
  registerCommand(command: Command): void;
}
```

### 分析上下文

```typescript
export interface AnalysisContext {
  // 文件信息
  filePath: string;
  fileContent: string;
  ast: Module;
  
  // 当前节点信息
  currentNode: Node;
  parentNode?: Node;
  ancestors: Node[];
  
  // 分析状态
  functionContext?: FunctionNode;
  nestingLevel: number;
  complexity: number;
  
  // 配置和缓存
  config: ResolvedConfig;
  cache: CacheManager;
  
  // 工具函数
  traverse(visitor: NodeVisitor): void;
  getNodeSource(node: Node): string;
  reportIssue(issue: Issue): void;
}
```

## API参考

### 规则开发API

```typescript
// 基础规则接口
export interface Rule {
  id: string;
  name: string;
  priority: number;
  
  // 规则逻辑
  canHandle(node: Node): boolean;
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult>;
  
  // 依赖管理
  getDependencies(): string[];
}

// 规则结果
export interface RuleResult {
  ruleId: string;
  complexity: number;
  isExempted: boolean;
  shouldIncreaseNesting: boolean;
  
  reason: string;
  suggestions: Suggestion[];
  metadata: RuleMetadata;
  
  executionTime: number;
  cacheHit: boolean;
}

// 建议接口
export interface Suggestion {
  type: 'refactor' | 'optimize' | 'warning' | 'info';
  message: string;
  fix?: CodeFix;
}

// 代码修复
export interface CodeFix {
  range: SourceRange;
  replacement: string;
  description: string;
}
```

### 格式化器API

```typescript
// 格式化器接口
export interface Formatter {
  id: string;
  name: string;
  extensions: string[];
  
  format(result: AnalysisResult, options: FormatOptions): string;
  writeToFile?(result: AnalysisResult, outputPath: string): Promise<void>;
}

// 格式化选项
export interface FormatOptions {
  showDetails: boolean;
  includeMetrics: boolean;
  template?: string;
  theme?: string;
}
```

### 命令行扩展API

```typescript
// 命令接口
export interface Command {
  name: string;
  description: string;
  options: CommandOption[];
  
  execute(args: string[], options: any): Promise<void>;
}

// 命令选项
export interface CommandOption {
  name: string;
  description: string;
  type: 'string' | 'number' | 'boolean';
  required?: boolean;
  default?: any;
}
```

## 开发示例

### 示例1: 简单规则插件

```typescript
// src/simple-rule-plugin.ts
import { Plugin, Rule, Node, AnalysisContext, RuleResult } from '@imd/cognitive-complexity';

// 自定义规则：检测过长的函数名
class LongFunctionNameRule implements Rule {
  readonly id = 'long-function-name';
  readonly name = 'Long Function Name Detection';
  readonly priority = 100;
  
  canHandle(node: Node): boolean {
    return node.type === 'FunctionDeclaration' || 
           node.type === 'ArrowFunctionExpression' ||
           node.type === 'FunctionExpression';
  }
  
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    const functionName = this.getFunctionName(node);
    const nameLength = functionName.length;
    
    // 函数名超过30个字符认为过长
    if (nameLength > 30) {
      return {
        ruleId: this.id,
        complexity: 1, // 添加1点复杂度惩罚
        isExempted: false,
        shouldIncreaseNesting: false,
        reason: `Function name "${functionName}" is too long (${nameLength} characters)`,
        suggestions: [{
          type: 'refactor',
          message: 'Consider shortening the function name to improve readability',
          fix: {
            range: this.getFunctionNameRange(node),
            replacement: this.suggestShorterName(functionName),
            description: 'Shorten function name'
          }
        }],
        metadata: { 
          originalName: functionName,
          nameLength,
          suggestedName: this.suggestShorterName(functionName)
        },
        executionTime: performance.now(),
        cacheHit: false
      };
    }
    
    return {
      ruleId: this.id,
      complexity: 0,
      isExempted: true,
      shouldIncreaseNesting: false,
      reason: `Function name "${functionName}" is acceptable`,
      suggestions: [],
      metadata: { originalName: functionName, nameLength },
      executionTime: performance.now(),
      cacheHit: false
    };
  }
  
  getDependencies(): string[] {
    return [];
  }
  
  private getFunctionName(node: Node): string {
    // 实现获取函数名的逻辑
    if (node.type === 'FunctionDeclaration' && node.id) {
      return node.id.name;
    }
    return 'anonymous';
  }
  
  private getFunctionNameRange(node: Node): SourceRange {
    // 实现获取函数名位置的逻辑
    return { start: 0, end: 0 };
  }
  
  private suggestShorterName(originalName: string): string {
    // 简单的名称缩短建议
    return originalName
      .replace(/([A-Z])/g, (match, p1, offset) => offset > 0 ? match : match.toLowerCase())
      .substring(0, 20);
  }
}

// 插件定义
export const longFunctionNamePlugin: Plugin = {
  id: 'long-function-name-plugin',
  name: 'Long Function Name Plugin',
  version: '1.0.0',
  description: 'Detects and penalizes overly long function names',
  
  async initialize(context) {
    console.log('Long Function Name Plugin initialized');
    
    // 注册自定义规则
    context.registerRule(new LongFunctionNameRule());
  },
  
  configSchema: {
    type: 'object',
    properties: {
      maxNameLength: {
        type: 'number',
        default: 30,
        description: 'Maximum allowed function name length'
      },
      complexityPenalty: {
        type: 'number',
        default: 1,
        description: 'Complexity penalty for long names'
      }
    }
  },
  
  validateConfig(config) {
    if (config.maxNameLength < 1) {
      return {
        valid: false,
        errors: ['maxNameLength must be greater than 0']
      };
    }
    return { valid: true, errors: [] };
  }
};

export default longFunctionNamePlugin;
```

### 示例2: 格式化器插件

```typescript
// src/csv-formatter-plugin.ts
import { Plugin, Formatter, AnalysisResult, FormatOptions } from '@imd/cognitive-complexity';
import { writeFile } from 'fs/promises';

// CSV格式化器
class CsvFormatter implements Formatter {
  readonly id = 'csv';
  readonly name = 'CSV Formatter';
  readonly extensions = ['.csv'];
  
  format(result: AnalysisResult, options: FormatOptions): string {
    const lines: string[] = [];
    
    // CSV头部
    lines.push('File,Function,Complexity,Line,EndLine');
    
    // 数据行
    for (const fileResult of result.results) {
      for (const func of fileResult.functions) {
        lines.push([
          this.escapeCsv(fileResult.filePath),
          this.escapeCsv(func.name),
          func.complexity.toString(),
          func.line.toString(),
          func.endLine?.toString() || func.line.toString()
        ].join(','));
      }
    }
    
    return lines.join('\n');
  }
  
  async writeToFile(result: AnalysisResult, outputPath: string): Promise<void> {
    const content = this.format(result, { showDetails: true, includeMetrics: true });
    await writeFile(outputPath, content, 'utf-8');
  }
  
  private escapeCsv(value: string): string {
    if (value.includes(',') || value.includes('"') || value.includes('\n')) {
      return `"${value.replace(/"/g, '""')}"`;
    }
    return value;
  }
}

// 插件定义
export const csvFormatterPlugin: Plugin = {
  id: 'csv-formatter-plugin',
  name: 'CSV Formatter Plugin',
  version: '1.0.0',
  description: 'Adds CSV output format support',
  
  async initialize(context) {
    console.log('CSV Formatter Plugin initialized');
    
    // 注册CSV格式化器
    context.registerFormatter(new CsvFormatter());
  }
};

export default csvFormatterPlugin;
```

### 示例3: 复合功能插件

```typescript
// src/advanced-react-plugin.ts
import { Plugin, Rule, Node, AnalysisContext, RuleResult, Command } from '@imd/cognitive-complexity';

// React Hooks复杂度规则
class ReactHooksComplexityRule implements Rule {
  readonly id = 'react-hooks-complexity';
  readonly name = 'React Hooks Complexity';
  readonly priority = 500;
  
  canHandle(node: Node): boolean {
    return node.type === 'CallExpression' && this.isHookCall(node);
  }
  
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    const hookName = this.getHookName(node);
    let complexity = 0;
    
    switch (hookName) {
      case 'useEffect':
        complexity = await this.analyzeUseEffect(node, context);
        break;
      case 'useMemo':
      case 'useCallback':
        complexity = await this.analyzeMemoHook(node, context);
        break;
      case 'useReducer':
        complexity = await this.analyzeUseReducer(node, context);
        break;
      default:
        complexity = 1; // 基础Hook复杂度
    }
    
    return {
      ruleId: this.id,
      complexity,
      isExempted: false,
      shouldIncreaseNesting: false,
      reason: `React Hook ${hookName} adds ${complexity} complexity`,
      suggestions: this.generateHookSuggestions(hookName, complexity),
      metadata: { hookName, hookComplexity: complexity },
      executionTime: performance.now(),
      cacheHit: false
    };
  }
  
  getDependencies(): string[] {
    return [];
  }
  
  private isHookCall(node: Node): boolean {
    // 检查是否是Hook调用（以use开头）
    return node.callee?.name?.startsWith('use') || false;
  }
  
  private getHookName(node: Node): string {
    return node.callee?.name || 'unknown';
  }
  
  private async analyzeUseEffect(node: Node, context: AnalysisContext): Promise<number> {
    let complexity = 1; // 基础复杂度
    
    const [callback, dependencies] = node.arguments;
    
    if (callback) {
      // 分析回调函数复杂度
      const callbackComplexity = await this.analyzeCallback(callback, context);
      complexity += callbackComplexity;
    }
    
    if (dependencies) {
      // 分析依赖数组
      const depComplexity = this.analyzeDependencies(dependencies);
      complexity += depComplexity;
    }
    
    return complexity;
  }
  
  private async analyzeMemoHook(node: Node, context: AnalysisContext): Promise<number> {
    // useMemo和useCallback的复杂度分析
    let complexity = 1;
    
    const [callback, dependencies] = node.arguments;
    
    if (callback) {
      const callbackComplexity = await this.analyzeCallback(callback, context);
      complexity += Math.min(callbackComplexity, 3); // 限制最大复杂度
    }
    
    return complexity;
  }
  
  private async analyzeUseReducer(node: Node, context: AnalysisContext): Promise<number> {
    // useReducer通常用于复杂状态管理
    return 3;
  }
  
  private async analyzeCallback(callback: Node, context: AnalysisContext): Promise<number> {
    // 分析回调函数的复杂度
    // 这里需要递归分析函数体
    return 1; // 简化实现
  }
  
  private analyzeDependencies(dependencies: Node): number {
    if (dependencies.type === 'ArrayExpression') {
      // 依赖数组越长，复杂度越高
      return Math.min(dependencies.elements.length * 0.5, 2);
    }
    return 0;
  }
  
  private generateHookSuggestions(hookName: string, complexity: number): any[] {
    const suggestions = [];
    
    if (complexity > 5) {
      suggestions.push({
        type: 'refactor',
        message: `Consider breaking down the ${hookName} into smaller pieces`
      });
    }
    
    if (hookName === 'useEffect' && complexity > 3) {
      suggestions.push({
        type: 'optimize',
        message: 'Consider splitting useEffect into multiple effects'
      });
    }
    
    return suggestions;
  }
}

// React组件分析命令
class ReactAnalysisCommand implements Command {
  readonly name = 'react-analysis';
  readonly description = 'Specialized analysis for React components';
  readonly options = [
    {
      name: 'components-only',
      description: 'Only analyze React components',
      type: 'boolean' as const,
      default: false
    },
    {
      name: 'hook-threshold',
      description: 'Complexity threshold for hooks',
      type: 'number' as const,
      default: 5
    }
  ];
  
  async execute(args: string[], options: any): Promise<void> {
    console.log('Running React-specific analysis...');
    console.log('Options:', options);
    
    // 实现React专门的分析逻辑
    // 这里可以调用主分析引擎，但使用React特定的配置
  }
}

// 插件定义
export const advancedReactPlugin: Plugin = {
  id: 'advanced-react-plugin',
  name: 'Advanced React Analysis Plugin',
  version: '1.0.0',
  description: 'Advanced React component and hooks analysis',
  author: 'React Team',
  
  dependencies: ['react-jsx-plugin'], // 依赖其他插件
  
  async initialize(context) {
    console.log('Advanced React Plugin initialized');
    
    // 注册React Hooks规则
    context.registerRule(new ReactHooksComplexityRule());
    
    // 注册React分析命令
    context.registerCommand(new ReactAnalysisCommand());
    
    // 设置React特定的配置
    if (context.pluginConfig.enableHooksAnalysis !== false) {
      console.log('React Hooks analysis enabled');
    }
  },
  
  configSchema: {
    type: 'object',
    properties: {
      enableHooksAnalysis: {
        type: 'boolean',
        default: true,
        description: 'Enable React Hooks complexity analysis'
      },
      maxHookComplexity: {
        type: 'number',
        default: 8,
        description: 'Maximum allowed hook complexity'
      },
      componentPatterns: {
        type: 'array',
        items: { type: 'string' },
        default: ['**/*.tsx', '**/*.jsx'],
        description: 'File patterns to identify React components'
      }
    }
  },
  
  validateConfig(config) {
    const errors = [];
    
    if (config.maxHookComplexity < 1) {
      errors.push('maxHookComplexity must be at least 1');
    }
    
    if (!Array.isArray(config.componentPatterns)) {
      errors.push('componentPatterns must be an array');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
};

export default advancedReactPlugin;
```

## 测试和调试

### 单元测试

```typescript
// tests/simple-rule-plugin.test.ts
import { longFunctionNamePlugin } from '../src/simple-rule-plugin';
import { createMockContext, createMockNode } from '@imd/cognitive-complexity/testing';

describe('Long Function Name Plugin', () => {
  let plugin: Plugin;
  let mockContext: PluginContext;
  
  beforeEach(() => {
    plugin = longFunctionNamePlugin;
    mockContext = createMockContext();
  });
  
  test('should initialize correctly', async () => {
    await plugin.initialize!(mockContext);
    
    expect(mockContext.registerRule).toHaveBeenCalledWith(
      expect.objectContaining({
        id: 'long-function-name'
      })
    );
  });
  
  test('should validate config correctly', () => {
    const validConfig = { maxNameLength: 25 };
    const invalidConfig = { maxNameLength: 0 };
    
    expect(plugin.validateConfig!(validConfig)).toEqual({
      valid: true,
      errors: []
    });
    
    expect(plugin.validateConfig!(invalidConfig)).toEqual({
      valid: false,
      errors: ['maxNameLength must be greater than 0']
    });
  });
});

// 规则测试
describe('LongFunctionNameRule', () => {
  let rule: LongFunctionNameRule;
  let mockContext: AnalysisContext;
  
  beforeEach(() => {
    rule = new LongFunctionNameRule();
    mockContext = createMockAnalysisContext();
  });
  
  test('should detect long function names', async () => {
    const longNameNode = createMockNode({
      type: 'FunctionDeclaration',
      id: { name: 'thisIsAVeryLongFunctionNameThatExceedsTheLimit' }
    });
    
    const result = await rule.evaluate(longNameNode, mockContext);
    
    expect(result.complexity).toBe(1);
    expect(result.isExempted).toBe(false);
    expect(result.suggestions).toHaveLength(1);
  });
  
  test('should accept normal function names', async () => {
    const normalNameNode = createMockNode({
      type: 'FunctionDeclaration',
      id: { name: 'normalFunctionName' }
    });
    
    const result = await rule.evaluate(normalNameNode, mockContext);
    
    expect(result.complexity).toBe(0);
    expect(result.isExempted).toBe(true);
    expect(result.suggestions).toHaveLength(0);
  });
});
```

### 集成测试

```typescript
// tests/integration.test.ts
import { ComplexityCalculator } from '@imd/cognitive-complexity';
import { longFunctionNamePlugin } from '../src/simple-rule-plugin';

describe('Plugin Integration', () => {
  test('should work with ComplexityCalculator', async () => {
    const calculator = new ComplexityCalculator({
      plugins: [longFunctionNamePlugin]
    });
    
    const code = `
      function thisIsAVeryLongFunctionNameThatShouldTriggerTheRule() {
        return 'test';
      }
      
      function normalName() {
        return 'test';
      }
    `;
    
    const results = await calculator.calculateCode(code);
    
    // 长函数名应该有额外的复杂度惩罚
    const longFunction = results.find(r => r.name.includes('VeryLong'));
    const normalFunction = results.find(r => r.name === 'normalName');
    
    expect(longFunction?.complexity).toBeGreaterThan(normalFunction?.complexity);
  });
});
```

### 调试工具

```typescript
// src/debug-utils.ts
export class PluginDebugger {
  private logs: Array<{ timestamp: number; level: string; message: string; data?: any }> = [];
  
  log(level: 'info' | 'warn' | 'error', message: string, data?: any) {
    this.logs.push({
      timestamp: Date.now(),
      level,
      message,
      data
    });
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`[${level.toUpperCase()}] ${message}`, data || '');
    }
  }
  
  getLogs() {
    return [...this.logs];
  }
  
  clearLogs() {
    this.logs = [];
  }
  
  exportLogs() {
    return JSON.stringify(this.logs, null, 2);
  }
}

// 在插件中使用
export const debugger = new PluginDebugger();

// 在规则中记录调试信息
async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
  debugger.log('info', 'Evaluating node', { 
    nodeType: node.type, 
    ruleName: this.name 
  });
  
  // ... 规则逻辑
  
  debugger.log('info', 'Rule evaluation complete', { 
    complexity: result.complexity 
  });
  
  return result;
}
```

## 发布和分发

### 1. 构建和打包

```json
// package.json 发布配置
{
  "name": "my-complexity-plugin",
  "version": "1.0.0",
  "description": "Custom complexity analysis plugin",
  "main": "dist/index.js",
  "types": "dist/index.d.ts",
  "files": [
    "dist/",
    "README.md",
    "LICENSE"
  ],
  "scripts": {
    "build": "tsc",
    "prepublishOnly": "npm run build",
    "test": "jest",
    "lint": "eslint src/**/*.ts"
  },
  "keywords": [
    "cognitive-complexity",
    "plugin",
    "code-analysis",
    "typescript"
  ],
  "peerDependencies": {
    "@imd/cognitive-complexity": "^1.0.0"
  },
  "devDependencies": {
    "@types/node": "^18.0.0",
    "typescript": "^5.0.0",
    "jest": "^29.0.0",
    "@types/jest": "^29.0.0"
  }
}
```

### 2. 插件注册表

```json
// cognitive-complexity-plugins.json (插件注册表)
{
  "plugins": [
    {
      "id": "my-complexity-plugin",
      "name": "My Complexity Plugin",
      "description": "Custom complexity analysis rules",
      "version": "1.0.0",
      "author": "Your Name",
      "homepage": "https://github.com/yourname/my-complexity-plugin",
      "repository": "https://github.com/yourname/my-complexity-plugin",
      "npmPackage": "my-complexity-plugin",
      "tags": ["rules", "custom", "analysis"],
      "compatibility": {
        "minEngineVersion": "1.0.0",
        "maxEngineVersion": "2.0.0"
      }
    }
  ]
}
```

### 3. 使用示例

```bash
# 安装插件
npm install my-complexity-plugin

# 在配置文件中启用
# cognitive-complexity.config.js
module.exports = {
  plugins: [
    'my-complexity-plugin',
    {
      name: 'advanced-react-plugin',
      config: {
        maxHookComplexity: 6
      }
    }
  ]
};

# 或者通过CLI参数
cognitive-complexity --plugin my-complexity-plugin src/
```

### 4. 文档模板

```markdown
# My Complexity Plugin

A custom plugin for Cognitive Complexity Analyzer.

## Installation

\`\`\`bash
npm install my-complexity-plugin
\`\`\`

## Usage

Add to your cognitive-complexity.config.js:

\`\`\`javascript
module.exports = {
  plugins: ['my-complexity-plugin']
};
\`\`\`

## Configuration

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| maxNameLength | number | 30 | Maximum function name length |
| complexityPenalty | number | 1 | Complexity penalty for violations |

## Rules

### long-function-name

Detects function names that exceed the configured length limit.

## License

MIT
```

## 最佳实践

### 1. 插件设计原则

- **单一职责**: 每个插件专注特定功能域
- **向后兼容**: 保持API的向后兼容性
- **性能考虑**: 避免阻塞主分析流程
- **错误处理**: 优雅处理异常情况

### 2. 代码质量

```typescript
// ✅ 良好的插件代码示例
export class WellDesignedRule implements Rule {
  private readonly config: RuleConfig;
  private readonly cache = new Map<string, RuleResult>();
  
  constructor(config: RuleConfig) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }
  
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    // 缓存检查
    const cacheKey = this.generateCacheKey(node);
    if (this.cache.has(cacheKey)) {
      return { ...this.cache.get(cacheKey)!, cacheHit: true };
    }
    
    // 性能监控
    const startTime = performance.now();
    
    try {
      const result = await this.performAnalysis(node, context);
      
      // 缓存结果
      this.cache.set(cacheKey, result);
      
      return {
        ...result,
        executionTime: performance.now() - startTime,
        cacheHit: false
      };
    } catch (error) {
      // 错误处理
      context.reportIssue({
        severity: 'error',
        message: `Rule ${this.id} failed: ${error.message}`,
        location: node.loc
      });
      
      // 返回默认结果
      return this.createDefaultResult();
    }
  }
}
```

### 3. 测试覆盖

- 单元测试覆盖率 > 90%
- 集成测试覆盖主要场景
- 性能测试确保不影响分析速度
- 兼容性测试跨版本验证

### 4. 文档完整性

- README.md 包含安装和使用说明
- API文档详细说明所有接口
- 示例代码展示典型用法
- 更新日志记录版本变更

通过遵循这些指南，你可以开发出高质量、可维护的复杂度分析插件。