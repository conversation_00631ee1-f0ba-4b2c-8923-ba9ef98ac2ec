# React 最佳实践分析示例

## 目录
- [React组件复杂度分析](#react组件复杂度分析)
- [JSX智能分析规则](#jsx智能分析规则)
- [Hooks复杂度分析](#hooks复杂度分析)
- [条件渲染分析](#条件渲染分析)
- [事件处理分析](#事件处理分析)
- [组件优化建议](#组件优化建议)
- [性能最佳实践](#性能最佳实践)
- [实际项目示例](#实际项目示例)

## React组件复杂度分析

### 简单组件 (复杂度: 1-3)

```typescript
// ✅ 优秀示例 - 复杂度: 1
function UserProfile({ user }: { user: User }) {
  return (
    <div className="user-profile">
      <img src={user.avatar} alt={user.name} />
      <h2>{user.name}</h2>
      <p>{user.email}</p>
      <p>{user.department}</p>
    </div>
  );
}

/* 
分析结果:
┌─────────────────────────────────────────────────────────────────┐
│ 智能分析器识别为纯展示组件                                        │
├─────────────────────────────────────────────────────────────────┤
│ • JSX结构节点被完全豁免                                          │
│ • 属性传递不计入复杂度                                           │
│ • 无控制流和业务逻辑                                             │
│ • 最终复杂度: 1 (函数基础复杂度)                                  │
└─────────────────────────────────────────────────────────────────┘
*/
```

```typescript
// ✅ 带简单逻辑的组件 - 复杂度: 2
function StatusBadge({ status }: { status: string }) {
  const getBadgeColor = () => {  // +1
    return status === 'active' ? 'green' : 'gray';
  };

  return (
    <span 
      className={`badge badge-${getBadgeColor()}`}
      style={{ backgroundColor: getBadgeColor() }}
    >
      {status.toUpperCase()}
    </span>
  );
}

/* 
分析结果:
┌─────────────────────────────────────────────────────────────────┐
│ 包含简单业务逻辑的展示组件                                        │
├─────────────────────────────────────────────────────────────────┤
│ • getBadgeColor函数: +1                                          │
│ • JSX属性中的函数调用被豁免                                       │
│ • 条件表达式在纯展示上下文中被优化处理                            │
│ • 最终复杂度: 2                                                  │
└─────────────────────────────────────────────────────────────────┘
*/
```

### 中等复杂度组件 (复杂度: 4-8)

```typescript
// ✅ 合理的交互组件 - 复杂度: 6
function TodoItem({ todo, onToggle, onDelete }: TodoItemProps) {
  const [isEditing, setIsEditing] = useState(false);

  const handleEdit = () => {  // +1
    setIsEditing(!isEditing);
  };

  const handleSave = (newText: string) => {  // +1
    if (newText.trim()) {  // +1 (条件判断)
      onSave(todo.id, newText);
      setIsEditing(false);
    }
  };

  return (
    <li className={`todo-item ${todo.completed ? 'completed' : ''}`}>
      {isEditing ? (  // +1 (条件渲染)
        <EditForm 
          initialValue={todo.text} 
          onSave={handleSave}
          onCancel={() => setIsEditing(false)} 
        />
      ) : (
        <div className="todo-content">
          <input
            type="checkbox"
            checked={todo.completed}
            onChange={() => onToggle(todo.id)}  // 事件处理器被豁免
          />
          <span 
            onClick={handleEdit}
            className="todo-text"
          >
            {todo.text}
          </span>
          <button 
            onClick={() => onDelete(todo.id)}  // 简单事件处理器被豁免
            className="delete-btn"
          >
            删除
          </button>
        </div>
      )}
    </li>
  );
}

/* 
分析结果:
┌─────────────────────────────────────────────────────────────────┐
│ 标准交互组件，复杂度控制良好                                      │
├─────────────────────────────────────────────────────────────────┤
│ • handleEdit 函数: +1                                           │
│ • handleSave 函数: +1                                           │
│ • handleSave 内条件判断: +1                                      │
│ • 主要条件渲染: +1                                               │
│ • 内联事件处理器被智能豁免                                        │
│ • JSX结构被豁免                                                  │
│ • 最终复杂度: 6                                                  │
└─────────────────────────────────────────────────────────────────┘
*/
```

```typescript
// ⚠️ 需要关注的组件 - 复杂度: 9
function SearchableList({ items, onSelect }: SearchableListProps) {
  const [query, setQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'date'>('name');
  const [loading, setLoading] = useState(false);

  const filteredItems = useMemo(() => {  // +1 (复杂计算逻辑)
    let result = items;
    
    if (query) {  // +1
      result = result.filter(item => 
        item.name.toLowerCase().includes(query.toLowerCase()) ||
        item.description.toLowerCase().includes(query.toLowerCase())
      );
    }
    
    if (sortBy === 'name') {  // +1
      result = result.sort((a, b) => a.name.localeCompare(b.name));
    } else if (sortBy === 'date') {  // +1
      result = result.sort((a, b) => 
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    }
    
    return result;
  }, [items, query, sortBy]);

  const handleItemClick = async (item: Item) => {  // +1
    setLoading(true);
    try {
      await onSelect(item);  // +1 (异步调用)
    } catch (error) {  // +1 (错误处理)
      console.error('Selection failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="searchable-list">
      <div className="search-controls">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="搜索..."
        />
        <select 
          value={sortBy} 
          onChange={(e) => setSortBy(e.target.value as 'name' | 'date')}
        >
          <option value="name">按名称排序</option>
          <option value="date">按日期排序</option>
        </select>
      </div>
      
      {loading && <div className="loading">加载中...</div>}
      
      <ul className="item-list">
        {filteredItems.map(item => (
          <li key={item.id} onClick={() => handleItemClick(item)}>
            <h3>{item.name}</h3>
            <p>{item.description}</p>
          </li>
        ))}
      </ul>
    </div>
  );
}

/* 
分析结果:
┌─────────────────────────────────────────────────────────────────┐
│ 功能丰富的搜索列表组件，建议考虑重构                              │
├─────────────────────────────────────────────────────────────────┤
│ • filteredItems useMemo: +1                                     │
│ • query 条件判断: +1                                             │
│ • sortBy name 条件: +1                                           │
│ • sortBy date 条件: +1                                           │
│ • handleItemClick 函数: +1                                       │
│ • 异步操作: +1                                                   │
│ • try-catch 错误处理: +1                                         │
│ • 最终复杂度: 9                                                  │
│                                                                 │
│ 🔧 重构建议:                                                     │
│ • 提取自定义 hook: useSearchableList                             │
│ • 分离排序逻辑到独立函数                                          │
│ • 考虑使用 React Query 处理异步状态                              │
└─────────────────────────────────────────────────────────────────┘
*/
```

### 复杂组件重构示例 (复杂度优化: 9 → 4)

```typescript
// ✅ 重构后的版本 - 复杂度: 4
// 1. 提取自定义Hook
function useSearchableList(items: Item[], onSelect: (item: Item) => Promise<void>) {
  const [query, setQuery] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'date'>('name');
  const [loading, setLoading] = useState(false);

  const filteredItems = useMemo(() => {
    return searchAndSort(items, query, sortBy);  // 提取到独立函数
  }, [items, query, sortBy]);

  const handleItemClick = async (item: Item) => {
    setLoading(true);
    try {
      await onSelect(item);
    } catch (error) {
      console.error('Selection failed:', error);
    } finally {
      setLoading(false);
    }
  };

  return { query, setQuery, sortBy, setSortBy, loading, filteredItems, handleItemClick };
}

// 2. 主组件变得简洁
function SearchableList({ items, onSelect }: SearchableListProps) {
  const { 
    query, setQuery, sortBy, setSortBy, 
    loading, filteredItems, handleItemClick 
  } = useSearchableList(items, onSelect);

  return (
    <div className="searchable-list">
      <SearchControls 
        query={query} 
        onQueryChange={setQuery}
        sortBy={sortBy}
        onSortChange={setSortBy}
      />
      
      {loading && <LoadingIndicator />}  // +1 (条件渲染)
      
      <ItemList 
        items={filteredItems} 
        onItemClick={handleItemClick} 
      />
    </div>
  );
}

/* 
重构效果:
┌─────────────────────────────────────────────────────────────────┐
│ 通过组件分解和逻辑提取，复杂度大幅降低                            │
├─────────────────────────────────────────────────────────────────┤
│ • 主组件只负责组合子组件                                          │
│ • 业务逻辑封装在自定义Hook中                                      │
│ • 只有条件渲染产生复杂度: +1                                      │
│ • 最终复杂度: 4 (降低了56%)                                      │
│                                                                 │
│ ✨ 重构优势:                                                     │
│ • 更好的可测试性                                                 │
│ • 逻辑复用性提升                                                 │
│ • 组件职责更清晰                                                 │
│ • 维护成本降低                                                   │
└─────────────────────────────────────────────────────────────────┘
*/
```

## JSX智能分析规则

### 结构豁免 (Structural Exemption)

```typescript
// ✅ 这些JSX模式被完全豁免
function ProductCard({ product }: { product: Product }) {
  return (
    <div className="product-card">                    {/* ✅ 豁免 */}
      <div className="product-image">                 {/* ✅ 豁免 */}
        <img 
          src={product.imageUrl} 
          alt={product.name}                          // ✅ 豁免
          loading="lazy"                              // ✅ 豁免
        />
      </div>
      <div className="product-info">                 {/* ✅ 豁免 */}
        <h3 className="product-name">                {/* ✅ 豁免 */}
          {product.name}                              {/* ✅ 豁免 */}
        </h3>
        <p className="product-price">                {/* ✅ 豁免 */}
          ${product.price.toFixed(2)}                {/* ✅ 豁免 */}
        </p>
        <div className="product-tags">               {/* ✅ 豁免 */}
          {product.tags.map(tag => (                 /* ✅ 豁免 */
            <span key={tag} className="tag">         /* ✅ 豁免 */
              {tag}                                   /* ✅ 豁免 */
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

/* 复杂度: 1 - 纯展示组件，所有JSX结构被豁免 */
```

### 智能条件渲染分析

```typescript
// ✅ 简单显示逻辑 - 被豁免
function UserStatus({ user }: { user: User }) {
  return (
    <div>
      {user.isOnline && <span className="online-indicator" />}  {/* ✅ 豁免 */}
      {user.avatar ? (                                          /* ✅ 豁免 */
        <img src={user.avatar} alt="Avatar" />
      ) : (
        <div className="default-avatar" />
      )}
      <span>{user.name}</span>
    </div>
  );
}

// ⚠️ 复杂业务逻辑 - 计入复杂度
function OrderStatus({ order }: { order: Order }) {
  const getStatusMessage = () => {  // +1
    if (order.status === 'pending') {  // +1
      return order.paymentMethod === 'credit_card' ? 
        '等待支付确认' : '等待银行转账';
    } else if (order.status === 'processing') {  // +1
      return order.priority === 'high' ? 
        '优先处理中' : '正在处理';
    } else if (order.status === 'shipped') {  // +1
      return '已发货，预计3-5天到达';
    }
    return '状态未知';
  };

  return (
    <div className="order-status">
      <h3>订单状态</h3>
      {order.status === 'cancelled' ? (  // +1 (业务逻辑条件)
        <div className="cancelled-message">
          <p>订单已取消</p>
          {order.refundStatus && (  // +1 (嵌套条件)
            <p>退款状态：{order.refundStatus}</p>
          )}
        </div>
      ) : (
        <p className="status-message">
          {getStatusMessage()}
        </p>
      )}
    </div>
  );
}

/* 复杂度: 7 - 包含复杂业务逻辑 */
```

### 事件处理器智能分析

```typescript
function InteractiveComponent({ data, onAction }: Props) {
  // ✅ 简单事件处理器 - 被豁免
  return (
    <div>
      <button onClick={() => onAction('save')}>        {/* ✅ 豁免 */}
        保存
      </button>
      
      <button onClick={() => setVisible(!visible)}>    {/* ✅ 豁免 */}
        切换显示
      </button>
      
      <input 
        onChange={(e) => setValue(e.target.value)}     // ✅ 豁免
        placeholder="输入内容"
      />
    </div>
  );
}

// ⚠️ 复杂事件处理器 - 计入复杂度
function ComplexFormComponent({ onSubmit }: Props) {
  const handleSubmit = async (e: FormEvent) => {  // +1
    e.preventDefault();
    
    if (!validateForm()) {  // +1
      setErrors(getValidationErrors());
      return;
    }
    
    setLoading(true);
    try {
      await onSubmit(formData);  // +1 (异步处理)
      showSuccessMessage();
    } catch (error) {  // +1 (错误处理)
      if (error.code === 'NETWORK_ERROR') {  // +1 (错误分类)
        showNetworkError();
      } else {
        showGenericError();
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单内容 */}
    </form>
  );
}

/* 复杂度: 6 - 包含复杂异步处理和错误处理逻辑 */
```

## Hooks复杂度分析

### useEffect 复杂度分析

```typescript
// ✅ 简单Effect - 复杂度: +1
function SimpleComponent({ userId }: { userId: string }) {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {  // +1
    fetchUser(userId).then(setUser);
  }, [userId]);

  return <div>{user?.name}</div>;
}

// ⚠️ 复杂Effect - 复杂度: +4
function ComplexDataComponent({ filters }: { filters: FilterConfig }) {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {  // +1
    let cancelled = false;
    
    const loadData = async () => {
      setLoading(true);
      
      try {
        const result = await fetchData(filters);
        
        if (!cancelled) {  // +1 (取消检查)
          if (result.length === 0) {  // +1 (空结果处理)
            showEmptyState();
          } else {
            setData(result);
          }
        }
      } catch (error) {  // +1 (错误处理)
        if (!cancelled) {
          handleError(error);
        }
      } finally {
        setLoading(false);
      }
    };

    loadData();

    return () => {  // cleanup函数不增加复杂度
      cancelled = true;
    };
  }, [filters]);

  return (
    <div>
      {loading && <div>加载中...</div>}
      {data.map(item => <Item key={item.id} data={item} />)}
    </div>
  );
}

/* 
useEffect 复杂度计算:
┌─────────────────────────────────────────────────────────────────┐
│ • Effect 函数本身: +1                                            │
│ • 取消检查条件: +1                                               │
│ • 空结果条件判断: +1                                             │
│ • 错误处理: +1                                                  │
│ • cleanup 函数不计入复杂度                                       │
│ • 总复杂度: +4                                                   │
└─────────────────────────────────────────────────────────────────┘
*/
```

### 自定义Hook复杂度

```typescript
// ✅ 优化的自定义Hook
function useApiData<T>(url: string, options?: RequestOptions) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {  // +1
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(url, options);
      
      if (!response.ok) {  // +1
        throw new Error(`HTTP ${response.status}`);
      }
      
      const result = await response.json();
      setData(result);
    } catch (err) {  // +1
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [url, options]);

  useEffect(() => {  // +1
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {  // +1 (手动刷新)
    return fetchData();
  }, [fetchData]);

  return { data, loading, error, refetch };
}

/* 
自定义Hook复杂度: 5
- 适中的复杂度，功能完整
- 提供了良好的错误处理和重试机制
- 可以被多个组件复用
*/
```

## 条件渲染分析

### 条件渲染复杂度等级

```typescript
// 🟢 简单条件渲染 - 复杂度: +0 (被豁免)
function SimpleConditional({ isVisible, content }: Props) {
  return (
    <div>
      {isVisible && <div>{content}</div>}              {/* ✅ 简单显示逻辑 */}
      {content ? <p>{content}</p> : <p>无内容</p>}     {/* ✅ null检查 */}
    </div>
  );
}

// 🟡 中等条件渲染 - 复杂度: +2
function ModerateConditional({ user, permissions }: Props) {
  return (
    <div>
      {user.role === 'admin' ? (  // +1 (业务逻辑条件)
        <AdminPanel />
      ) : user.role === 'moderator' ? (  // +1 (链式条件)
        <ModeratorPanel />
      ) : (
        <UserPanel />
      )}
    </div>
  );
}

// 🔴 复杂条件渲染 - 复杂度: +5
function ComplexConditional({ user, project, settings }: Props) {
  return (
    <div>
      {user.isPremium ? (  // +1
        project.status === 'active' ? (  // +1 (嵌套条件)
          settings.showAdvanced ? (  // +1 (三层嵌套)
            <PremiumAdvancedView />
          ) : (
            <PremiumBasicView />
          )
        ) : (
          <PremiumInactiveView />
        )
      ) : (
        user.trialDaysLeft > 0 ? (  // +1 (并列复杂条件)
          <TrialView daysLeft={user.trialDaysLeft} />
        ) : (
          <FreeView />
        )
      )}
      
      {user.hasNotifications && (  // +1 (额外条件)
        <NotificationBanner />
      )}
    </div>
  );
}

/* 
条件渲染优化建议:
┌─────────────────────────────────────────────────────────────────┐
│ 🔧 针对复杂条件渲染的重构策略:                                     │
│                                                                 │
│ 1. 提取组件选择逻辑:                                             │
│    const ViewComponent = selectViewComponent(user, project, settings); │
│    return <ViewComponent />;                                    │
│                                                                 │
│ 2. 使用策略模式:                                                 │
│    const viewStrategies = { premium: PremiumView, trial: TrialView }; │
│                                                                 │
│ 3. 配置驱动渲染:                                                 │
│    const config = getViewConfig(user, project);                 │
│    return <ConfigurableView config={config} />;                 │
└─────────────────────────────────────────────────────────────────┘
*/
```

### 条件渲染最佳实践

```typescript
// ✅ 推荐方案：提取选择逻辑
function OptimizedConditionalView({ user, project, settings }: Props) {
  const ViewComponent = useMemo(() => {  // +1
    return selectAppropriateView(user, project, settings);
  }, [user.isPremium, user.trialDaysLeft, project.status, settings.showAdvanced]);

  return (
    <div>
      <ViewComponent user={user} project={project} />
      <NotificationBanner show={user.hasNotifications} />
    </div>
  );
}

// 选择逻辑独立封装
function selectAppropriateView(user: User, project: Project, settings: Settings) {
  if (user.isPremium) {
    return project.status === 'active' 
      ? (settings.showAdvanced ? PremiumAdvancedView : PremiumBasicView)
      : PremiumInactiveView;
  }
  
  return user.trialDaysLeft > 0 ? TrialView : FreeView;
}

/* 优化效果: 复杂度从 5 降低到 1 */
```

## 事件处理分析

### 事件处理器复杂度分类

```typescript
// ✅ 简单事件处理器 - 被豁免
function SimpleEventHandlers({ onSave, onCancel }: Props) {
  const [value, setValue] = useState('');

  return (
    <div>
      {/* 直接调用props函数 - 豁免 */}
      <button onClick={onSave}>保存</button>
      <button onClick={onCancel}>取消</button>
      
      {/* 简单状态更新 - 豁免 */}
      <input 
        value={value}
        onChange={(e) => setValue(e.target.value)}
      />
      
      {/* 简单参数传递 - 豁免 */}
      <button onClick={() => onSave(value)}>
        保存内容
      </button>
    </div>
  );
}

// ⚠️ 复杂事件处理器 - 计入复杂度
function ComplexFormHandler({ onSubmit }: Props) {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});

  const handleFieldChange = (field: string, value: any) => {  // +1
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 实时验证
    if (errors[field]) {  // +1
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handleSubmit = async (e: FormEvent) => {  // +1
    e.preventDefault();
    
    const validationErrors = validateForm(formData);
    
    if (Object.keys(validationErrors).length > 0) {  // +1
      setErrors(validationErrors);
      showErrorToast('请修正表单错误');
      return;
    }

    setLoading(true);
    
    try {
      await onSubmit(formData);
      showSuccessToast('提交成功');
      resetForm();
    } catch (error) {  // +1
      if (error.response?.status === 422) {  // +1 (错误分类)
        setErrors(error.response.data.errors);
      } else if (error.response?.status >= 500) {  // +1
        showErrorToast('服务器错误，请稍后重试');
      } else {
        showErrorToast('提交失败');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* 表单字段 */}
    </form>
  );
}

/* 复杂度: 8 - 包含完整的表单处理逻辑 */
```

### 事件处理优化策略

```typescript
// ✅ 优化方案：使用自定义Hook
function useFormHandler<T>(initialData: T, onSubmit: (data: T) => Promise<void>) {
  const [formData, setFormData] = useState(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const updateField = useCallback((field: keyof T, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as string]) {
      setErrors(prev => ({ ...prev, [field as string]: '' }));
    }
  }, [errors]);

  const handleSubmit = useCallback(async (e: FormEvent) => {
    e.preventDefault();
    // 处理提交逻辑...
  }, [formData, onSubmit]);

  return { formData, errors, loading, updateField, handleSubmit };
}

// 简化后的组件
function OptimizedFormComponent({ onSubmit }: Props) {
  const { formData, errors, loading, updateField, handleSubmit } 
    = useFormHandler(initialFormData, onSubmit);

  return (
    <form onSubmit={handleSubmit}>
      <input 
        value={formData.name}
        onChange={(e) => updateField('name', e.target.value)}
        className={errors.name ? 'error' : ''}
      />
      {errors.name && <span className="error-message">{errors.name}</span>}
      
      <button type="submit" disabled={loading}>
        {loading ? '提交中...' : '提交'}
      </button>
    </form>
  );
}

/* 优化后组件复杂度: 1 - 大幅简化 */
```

## 组件优化建议

### 基于复杂度的重构策略

```typescript
// 🔧 重构策略矩阵
/*
┌─────────────────┬─────────────────┬─────────────────────────────────┐
│   复杂度范围    │     重构优先级   │           推荐策略                │
├─────────────────┼─────────────────┼─────────────────────────────────┤
│    1-5         │     无需重构     │ 保持现状，优秀的组件设计         │
│    6-10        │     低优先级     │ 考虑提取自定义Hook或分解组件      │
│    11-15       │     中优先级     │ 建议重构，提取业务逻辑           │
│    16-20       │     高优先级     │ 必须重构，拆分为多个组件         │
│    20+         │     紧急重构     │ 立即重构，架构重新设计           │
└─────────────────┴─────────────────┴─────────────────────────────────┘
*/

// 示例：复杂组件的系统化重构
// 🔴 原始复杂组件 (复杂度: 18)
function MonolithicUserDashboard({ userId }: { userId: string }) {
  // 大量状态和逻辑...
  // 复杂度分析：多个useEffect、复杂条件渲染、异步处理等
}

// ✅ 重构后的组件架构 (总复杂度: 18 → 6)
function UserDashboard({ userId }: { userId: string }) {
  return (
    <div className="user-dashboard">
      <UserProfile userId={userId} />        {/* 复杂度: 2 */}
      <UserStats userId={userId} />          {/* 复杂度: 2 */}
      <UserActivity userId={userId} />       {/* 复杂度: 2 */}
    </div>
  );
}

/* 
重构收益分析:
┌─────────────────────────────────────────────────────────────────┐
│ 📊 重构前后对比:                                                 │
│                                                                 │
│ • 单个组件复杂度: 18 → 6 (降低67%)                              │
│ • 可测试性: 困难 → 容易                                          │
│ • 复用性: 无 → 高                                               │
│ • 维护成本: 高 → 低                                             │
│ • 团队协作: 困难 → 容易                                          │
│                                                                 │
│ 🎯 重构关键原则:                                                 │
│ • 单一职责：每个组件只负责一个功能域                             │
│ • 业务逻辑分离：使用自定义Hook                                   │
│ • 状态管理优化：最小化状态数量                                   │
│ • 条件渲染简化：避免深层嵌套                                     │
└─────────────────────────────────────────────────────────────────┘
*/
```

## 性能最佳实践

### React.memo 和 useMemo 的智能使用

```typescript
// ✅ 合理使用 React.memo
const ExpensiveComponent = React.memo(({ data, config }: Props) => {
  // 复杂计算逻辑...
  return <div>{/* 渲染内容 */}</div>;
}, (prevProps, nextProps) => {
  // 自定义比较逻辑，避免不必要的重渲染
  return (
    prevProps.data.id === nextProps.data.id &&
    prevProps.config.theme === nextProps.config.theme
  );
});

// ✅ useMemo 优化复杂计算
function OptimizedDataGrid({ data, filters, sorting }: Props) {
  const processedData = useMemo(() => {  // +1 (但这是有益的复杂度)
    let result = data;
    
    // 应用过滤器
    if (filters.length > 0) {
      result = applyFilters(result, filters);
    }
    
    // 应用排序
    if (sorting.field) {
      result = applySorting(result, sorting);
    }
    
    return result;
  }, [data, filters, sorting]);

  return (
    <div className="data-grid">
      {processedData.map(item => (
        <DataGridRow key={item.id} data={item} />
      ))}
    </div>
  );
}

/* 
性能优化复杂度权衡:
┌─────────────────────────────────────────────────────────────────┐
│ 🚀 虽然 useMemo 增加了 +1 复杂度，但带来的性能收益显著:           │
│                                                                 │
│ • 避免每次渲染重新计算                                           │
│ • 减少子组件不必要的重渲染                                       │
│ • 提升大数据集的处理性能                                         │
│                                                                 │
│ 💡 使用原则:                                                     │
│ • 计算成本 > 依赖比较成本时使用                                  │
│ • 结果会被子组件使用时考虑                                       │
│ • 避免过度优化简单操作                                           │
└─────────────────────────────────────────────────────────────────┘
*/
```

## 实际项目示例

### 电商项目复杂度分析

```typescript
// 📦 产品列表页面复杂度分析
function ProductListPage() {
  // 使用多个自定义Hook分离关注点
  const { products, loading, error, refetch } = useProductList();      // 复杂度: 4
  const { filters, updateFilter, clearFilters } = useProductFilters(); // 复杂度: 3
  const { cart, addToCart } = useShoppingCart();                       // 复杂度: 2

  if (error) {  // +1
    return <ErrorBoundary error={error} onRetry={refetch} />;
  }

  return (
    <div className="product-list-page">
      <ProductFilters 
        filters={filters} 
        onFilterChange={updateFilter}
        onClearFilters={clearFilters}
      />
      
      {loading ? (  // +1
        <ProductListSkeleton />
      ) : (
        <ProductGrid 
          products={products} 
          onAddToCart={addToCart}
        />
      )}
      
      <CartSummary cart={cart} />
    </div>
  );
}

/* 
项目级复杂度管理:
┌─────────────────────────────────────────────────────────────────┐
│ 📈 复杂度分布分析:                                               │
│                                                                 │
│ • ProductListPage: 2 (主要是条件渲染)                           │
│ • useProductList: 4 (API调用、状态管理)                         │
│ • useProductFilters: 3 (过滤逻辑)                               │
│ • useShoppingCart: 2 (购物车操作)                               │
│                                                                 │
│ 总体评估: 优秀 ✅                                                │
│ • 复杂度分散合理                                                 │
│ • 主组件保持简洁                                                 │
│ • 业务逻辑封装良好                                               │
│ • 易于测试和维护                                                 │
└─────────────────────────────────────────────────────────────────┘
*/
```

### 管理后台复杂度优化案例

```typescript
// 🏢 管理后台表格组件优化
// 重构前：复杂度 15+
// 重构后：通过组件化分解

function AdminDataTable<T>({ 
  data, 
  columns, 
  onEdit, 
  onDelete, 
  permissions 
}: AdminDataTableProps<T>) {
  const {
    sortedData,
    sortConfig,
    handleSort
  } = useTableSorting(data);                    // 复杂度: 3

  const {
    paginatedData,
    currentPage,
    totalPages,
    handlePageChange
  } = useTablePagination(sortedData, 20);       // 复杂度: 2

  const {
    selectedRows,
    handleSelectRow,
    handleSelectAll
  } = useTableSelection<T>();                   // 复杂度: 3

  return (
    <div className="admin-data-table">
      <TableToolbar 
        selectedCount={selectedRows.length}
        onBulkDelete={handleBulkDelete}
        permissions={permissions}
      />
      
      <Table>
        <TableHeader 
          columns={columns}
          sortConfig={sortConfig}
          onSort={handleSort}
          onSelectAll={handleSelectAll}
        />
        <TableBody 
          data={paginatedData}
          columns={columns}
          selectedRows={selectedRows}
          onSelectRow={handleSelectRow}
          onEdit={onEdit}
          onDelete={onDelete}
          permissions={permissions}
        />
      </Table>
      
      <TablePagination 
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={handlePageChange}
      />
    </div>
  );
}

/* 
企业级组件复杂度管理策略:
┌─────────────────────────────────────────────────────────────────┐
│ 🏗️ 架构设计原则:                                                 │
│                                                                 │
│ 1. 按功能域拆分Hook:                                             │
│    • useTableSorting: 排序逻辑                                   │
│    • useTablePagination: 分页逻辑                                │
│    • useTableSelection: 选择逻辑                                 │
│                                                                 │
│ 2. 组件职责分离:                                                 │
│    • TableToolbar: 工具栏和批量操作                              │
│    • TableHeader: 表头和排序控制                                 │
│    • TableBody: 数据展示和行级操作                               │
│    • TablePagination: 分页控制                                  │
│                                                                 │
│ 3. 复杂度控制效果:                                               │
│    • 主组件复杂度: 1 (仅组合子组件)                             │
│    • 平均Hook复杂度: 2.7                                        │
│    • 总体架构清晰，易于维护                                       │
└─────────────────────────────────────────────────────────────────┘
*/
```

通过这些最佳实践和实际示例，开发者可以更好地理解如何在React项目中控制认知复杂度，构建更加清晰、可维护的组件架构。
            type="checkbox"
            checked={todo.completed}
            onChange={() => onToggle(todo.id)}  // +1 (事件处理)
          />
          <span>{todo.text}</span>
          <button onClick={handleEdit}>编辑</button>
          <button onClick={() => onDelete(todo.id)}>删除</button>  // +1 (事件处理)
        </div>
      )}
    </li>
  );
}

// 分析结果:
// - 基础复杂度: 1
// - 条件渲染: +1
// - 事件处理函数: +2
// - 状态逻辑: +1
// - 总复杂度: 5
```

### 高复杂度组件需要重构 (复杂度: 15+)

```typescript
// ❌ 需要重构示例 - 复杂度: 18
function UserDashboard({ userId }: { userId: string }) {
  const [user, setUser] = useState<User | null>(null);
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'published' | 'draft'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {  // +1 (Hook复杂度)
    const fetchData = async () => {
      try {  // +1 (异常处理)
        setLoading(true);
        const userData = await fetchUser(userId);
        const postsData = await fetchPosts(userId);
        
        if (userData) {  // +1 (条件判断)
          setUser(userData);
        }
        
        if (postsData) {  // +1 (条件判断)
          setPosts(postsData);
        }
      } catch (err) {  // +1 (异常捕获)
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {  // +1 (条件判断)
      fetchData();
    }
  }, [userId]);

  const filteredPosts = useMemo(() => {  // +1 (Hook复杂度)
    let result = posts;
    
    if (filter !== 'all') {  // +1 (条件判断)
      result = result.filter(post => post.status === filter);
    }
    
    if (searchTerm) {  // +1 (条件判断)
      result = result.filter(post => 
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||  // +1 (逻辑操作)
        post.content.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    return result;
  }, [posts, filter, searchTerm]);

  const handleFilterChange = (newFilter: 'all' | 'published' | 'draft') => {
    setFilter(newFilter);
  };

  const handleDeletePost = async (postId: string) => {  // +1 (异步处理)
    try {  // +1 (异常处理)
      await deletePost(postId);
      setPosts(prev => prev.filter(p => p.id !== postId));
    } catch (err) {  // +1 (异常捕获)
      setError('Failed to delete post');
    }
  };

  if (loading) {  // +1 (条件渲染)
    return <LoadingSpinner />;
  }

  if (error) {  // +1 (条件渲染)
    return <ErrorMessage message={error} />;
  }

  if (!user) {  // +1 (条件渲染)
    return <div>User not found</div>;
  }

  return (
    <div className="user-dashboard">
      <UserHeader user={user} />
      
      <div className="filters">
        <input
          type="text"
          placeholder="搜索帖子..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
        <select 
          value={filter} 
          onChange={(e) => handleFilterChange(e.target.value as any)}
        >
          <option value="all">全部</option>
          <option value="published">已发布</option>
          <option value="draft">草稿</option>
        </select>
      </div>

      <div className="posts">
        {filteredPosts.length > 0 ? (  // +1 (条件渲染)
          filteredPosts.map(post => (
            <PostCard
              key={post.id}
              post={post}
              onDelete={() => handleDeletePost(post.id)}
            />
          ))
        ) : (
          <div>No posts found</div>
        )}
      </div>
    </div>
  );
}

// 分析结果:
// - 基础复杂度: 1
// - 条件判断: +8
// - 异常处理: +4
// - Hook复杂度: +2
// - 逻辑操作: +1
// - 条件渲染: +4
// - 总复杂度: 20 (需要重构!)
```

### 重构后的版本

```typescript
// ✅ 重构后示例 - 复杂度分布优化
function UserDashboard({ userId }: { userId: string }) {
  const { user, loading, error } = useUserData(userId);  // 提取自定义Hook
  const { 
    posts, 
    filteredPosts, 
    searchTerm, 
    filter, 
    setSearchTerm,
    setFilter,
    deletePost: handleDeletePost 
  } = useUserPosts(userId);  // 提取自定义Hook

  if (loading) return <LoadingSpinner />;  // 复杂度: 1
  if (error) return <ErrorMessage message={error} />;
  if (!user) return <div>User not found</div>;

  return (
    <div className="user-dashboard">
      <UserHeader user={user} />
      <PostFilters 
        searchTerm={searchTerm}
        filter={filter}
        onSearchChange={setSearchTerm}
        onFilterChange={setFilter}
      />
      <PostList 
        posts={filteredPosts}
        onDeletePost={handleDeletePost}
      />
    </div>
  );
}

// 提取的自定义Hook - 复杂度: 8
function useUserData(userId: string) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const userData = await fetchUser(userId);
        setUser(userData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]);

  return { user, loading, error };
}

// 提取的帖子管理Hook - 复杂度: 12
function useUserPosts(userId: string) {
  // 实现帖子相关逻辑...
}
```

## JSX模式识别

### 智能豁免模式

```typescript
// ✅ 这些JSX模式会被智能豁免，不增加复杂度
function ProductCard({ product }: { product: Product }) {
  return (
    <div className="product-card">           {/* 豁免: 结构节点 */}
      <img 
        src={product.image} 
        alt={product.name}                    {/* 豁免: 属性表达式 */}
      />
      <h3>{product.name}</h3>                {/* 豁免: 简单表达式 */}
      <p>{product.description}</p>
      <span className="price">
        ${product.price.toFixed(2)}          {/* 豁免: 简单方法调用 */}
      </span>
      {product.discount && (                 {/* 豁免: 简单条件渲染 */}
        <span className="discount">Sale!</span>
      )}
    </div>
  );
}
// 总复杂度: 1 (仅基础函数复杂度)
```

### 需要计分的复杂JSX模式

```typescript
// ⚠️ 这些模式会增加复杂度
function OrderSummary({ order }: { order: Order }) {
  return (
    <div className="order-summary">
      {order.items.map(item => (           // +1 (循环渲染)
        <div key={item.id}>
          {item.type === 'physical' ? (   // +1 (复杂条件渲染)
            <PhysicalItemView item={item} />
          ) : item.type === 'digital' ? ( // +1 (嵌套条件)
            <DigitalItemView item={item} />
          ) : (
            <UnknownItemView item={item} />
          ))}
        </div>
      ))}
      
      <div className="total">
        {order.discountCode && 
         order.discountCode.isValid &&    // +1 (复合条件判断)
         order.total > 100 ? (
          <DiscountDisplay discount={order.discount} />
        ) : null}
      </div>
    </div>
  );
}
// 总复杂度: 5 (1 + 4个复杂度点)
```

## Hooks复杂度分析

### useEffect Hook分析

```typescript
// ✅ 简单useEffect - 复杂度: +2
function SimpleTimer() {
  const [count, setCount] = useState(0);

  useEffect(() => {                      // +1 (Hook基础复杂度)
    const timer = setInterval(() => {
      setCount(c => c + 1);
    }, 1000);

    return () => clearInterval(timer);   // +1 (清理函数)
  }, []);

  return <div>Count: {count}</div>;
}

// ⚠️ 复杂useEffect - 复杂度: +8
function DataSyncEffect({ userId, settings }: Props) {
  useEffect(() => {                      // +1 (Hook基础复杂度)
    const controller = new AbortController();
    
    const syncData = async () => {
      try {                              // +1 (异常处理)
        if (!userId) {                   // +1 (条件判断)
          return;
        }

        const response = await fetch(`/api/users/${userId}`, {
          signal: controller.signal
        });

        if (!response.ok) {              // +1 (条件判断)
          throw new Error('Fetch failed');
        }

        const data = await response.json();
        
        if (settings.autoUpdate) {       // +1 (条件判断)
          updateUserData(data);
        }
      } catch (error) {                  // +1 (异常捕获)
        if (error.name !== 'AbortError') { // +1 (条件判断)
          handleError(error);
        }
      }
    };

    syncData();
    
    return () => {                       // +1 (清理函数)
      controller.abort();
    };
  }, [userId, settings.autoUpdate]);

  return null;
}
```

### 自定义Hook分析

```typescript
// ✅ 优秀的自定义Hook设计 - 复杂度: 6
function useApi<T>(url: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {  // +1 (Hook复杂度)
    try {                                      // +1 (异常处理)
      setLoading(true);
      setError(null);
      
      const response = await fetch(url);
      
      if (!response.ok) {                      // +1 (条件判断)
        throw new Error(`HTTP ${response.status}`);
      }
      
      const result = await response.json();
      setData(result);
    } catch (err) {                           // +1 (异常捕获)
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [url]);

  useEffect(() => {                           // +1 (Hook复杂度)
    if (url) {                                // +1 (条件判断)
      fetchData();
    }
  }, [fetchData]);

  return { data, loading, error, refetch: fetchData };
}
```

## 条件渲染分析

### 简单条件渲染 (被豁免)

```typescript
// ✅ 这些模式通常被豁免
function UserAvatar({ user }: { user: User }) {
  return (
    <div>
      {user.avatar && <img src={user.avatar} alt={user.name} />}    {/* 豁免: 简单存在性检查 */}
      {user.isOnline ? '🟢' : '🔴'}                                {/* 豁免: 简单状态显示 */}
      {user.role === 'admin' && <AdminBadge />}                    {/* 豁免: 简单角色检查 */}
    </div>
  );
}
```

### 复杂条件渲染 (需要计分)

```typescript
// ⚠️ 这些模式会增加复杂度
function NotificationPanel({ notifications, user }: Props) {
  return (
    <div>
      {notifications.length > 0 ? (                    // +1 (复杂条件)
        <div>
          {notifications
            .filter(n => n.priority === 'high')        // +1 (复杂过滤逻辑)
            .map(notification => (
              <div key={notification.id}>
                {notification.type === 'system' ? (    // +1 (嵌套条件)
                  <SystemNotification notification={notification} />
                ) : notification.type === 'user' ? (   // +1 (多重条件)
                  <UserNotification notification={notification} />
                ) : notification.urgent && 
                   user.preferences.showUrgent ? (     // +1 (复合条件)
                  <UrgentNotification notification={notification} />
                ) : (
                  <DefaultNotification notification={notification} />
                )}
              </div>
            ))}
        </div>
      ) : user.isFirstTime ? (                         // +1 (嵌套条件)
        <WelcomeMessage />
      ) : (
        <EmptyState message="No notifications" />
      )}
    </div>
  );
}
// 总复杂度: 7 (1 + 6个复杂度点)
```

## 事件处理分析

### 简单事件处理

```typescript
// ✅ 简单事件处理 - 复杂度: 3
function ToggleButton({ initialState, onToggle }: Props) {
  const [isOn, setIsOn] = useState(initialState);

  const handleClick = () => {                        // +1 (事件处理函数)
    const newState = !isOn;
    setIsOn(newState);
    onToggle?.(newState);                           // +1 (可选调用)
  };

  return (
    <button onClick={handleClick}>
      {isOn ? 'ON' : 'OFF'}
    </button>
  );
}
```

### 复杂事件处理

```typescript
// ⚠️ 复杂事件处理 - 复杂度: 12
function SearchInput({ onSearch, debounceMs = 300 }: Props) {
  const [value, setValue] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const handleInputChange = async (e: ChangeEvent<HTMLInputElement>) => {  // +1 (事件处理)
    const newValue = e.target.value;
    setValue(newValue);

    // 清除之前的定时器
    if (timeoutRef.current) {                       // +1 (条件判断)
      clearTimeout(timeoutRef.current);
    }

    if (newValue.length < 2) {                      // +1 (条件判断)
      setSuggestions([]);
      return;
    }

    setIsLoading(true);

    timeoutRef.current = setTimeout(async () => {   // +1 (异步处理)
      try {                                         // +1 (异常处理)
        const response = await fetch(`/api/suggestions?q=${newValue}`);
        
        if (response.ok) {                          // +1 (条件判断)
          const data = await response.json();
          setSuggestions(data.suggestions || []);
        } else {                                    // +1 (else分支)
          setSuggestions([]);
        }
      } catch (error) {                             // +1 (异常捕获)
        console.error('Suggestion fetch failed:', error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, debounceMs);
  };

  const handleSuggestionClick = (suggestion: string) => {  // +1 (事件处理)
    setValue(suggestion);
    setSuggestions([]);
    onSearch(suggestion);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {  // +1 (事件处理)
    if (e.key === 'Enter') {                        // +1 (条件判断)
      onSearch(value);
      setSuggestions([]);
    } else if (e.key === 'Escape') {                // +1 (条件判断)
      setSuggestions([]);
    }
  };

  return (
    <div className="search-input">
      <input
        type="text"
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder="搜索..."
      />
      
      {isLoading && <div className="loading">Loading...</div>}
      
      {suggestions.length > 0 && (
        <ul className="suggestions">
          {suggestions.map((suggestion, index) => (
            <li
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              {suggestion}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
}
```

## 最佳实践建议

### 1. 组件设计原则

```typescript
// ✅ 单一职责原则
function UserProfile({ user }: { user: User }) {
  // 只负责显示用户信息
}

function UserActions({ user, onEdit, onDelete }: UserActionsProps) {
  // 只负责用户操作
}

// ✅ 组合优于继承
function UserCard({ user }: { user: User }) {
  return (
    <div className="user-card">
      <UserProfile user={user} />
      <UserActions 
        user={user} 
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    </div>
  );
}
```

### 2. Hook使用最佳实践

```typescript
// ✅ 自定义Hook提取复杂逻辑
function useForm<T>(initialValues: T, validation: ValidationRules<T>) {
  // 复杂的表单逻辑封装在自定义Hook中
  // 降低组件复杂度
}

// ✅ Hook组合模式
function UserDashboard() {
  const user = useCurrentUser();
  const posts = useUserPosts(user?.id);
  const { showModal, hideModal } = useModal();
  
  // 组件逻辑保持简洁
}
```

### 3. 条件渲染优化

```typescript
// ✅ 提取条件渲染逻辑
function renderUserStatus(user: User) {
  if (user.isOnline) return <OnlineIndicator />;
  if (user.lastSeen) return <LastSeenIndicator lastSeen={user.lastSeen} />;
  return <OfflineIndicator />;
}

function UserCard({ user }: { user: User }) {
  return (
    <div>
      <UserAvatar user={user} />
      <UserName name={user.name} />
      {renderUserStatus(user)}
    </div>
  );
}
```

### 4. 复杂度阈值建议

| 组件类型 | 建议阈值 | 说明 |
|----------|----------|------|
| 展示组件 | 1-3 | 纯UI展示，无业务逻辑 |
| 容器组件 | 4-8 | 包含状态管理和事件处理 |
| 页面组件 | 8-12 | 协调多个子组件 |
| 复杂业务组件 | 12-15 | 包含复杂业务逻辑，需要谨慎设计 |
| 重构阈值 | 15+ | 必须重构或拆分 |

### 5. 重构策略

```typescript
// ❌ 复杂度过高的组件
function ComplexForm() {
  // 20+ 复杂度的大型表单组件
}

// ✅ 重构后的组件结构
function OrderForm() {  // 复杂度: 5
  return (
    <form>
      <PersonalInfoSection />   {/* 复杂度: 4 */}
      <ShippingSection />       {/* 复杂度: 6 */}
      <PaymentSection />        {/* 复杂度: 8 */}
      <OrderSummary />          {/* 复杂度: 3 */}
    </form>
  );
}
```

### 6. 测试和监控

```typescript
// ✅ 为复杂组件编写测试
describe('UserDashboard', () => {
  it('should handle loading state', () => {
    // 测试加载状态
  });
  
  it('should handle error state', () => {
    // 测试错误状态
  });
  
  it('should filter posts correctly', () => {
    // 测试复杂的过滤逻辑
  });
});

// ✅ 使用复杂度监控
// cognitive-complexity.config.js
module.exports = {
  failOnComplexity: 15,
  rules: {
    'jsx-hook-complexity': {
      maxHookComplexity: 8,
      maxEffectComplexity: 5
    }
  }
};
```

通过遵循这些最佳实践，可以有效控制React组件的复杂度，提高代码的可维护性和可读性。