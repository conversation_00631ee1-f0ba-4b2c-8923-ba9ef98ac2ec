# CLI 测试 API 文档

## 概述

本文档详细介绍了新的 CLI 测试框架的所有 API 接口、类型定义和使用方法。该框架提供了强大的 CLI 测试能力，包括进程管理、输出验证、交互式测试和性能监控。

## 核心类型定义

### CLITestResult 接口

CLI 测试结果的核心接口，提供了丰富的查询和交互能力。

```typescript
interface CLITestResult {
  readonly stdout: string;           // 标准输出内容
  readonly stderr: string;           // 错误输出内容
  readonly exitCode: number | null;  // 退出码（null 表示进程仍在运行）
  readonly isRunning: boolean;       // 进程是否正在运行
  
  // 查询方法
  findByText(text: string | RegExp): Promise<boolean>;
  queryByText(text: string | RegExp): Promise<string | null>;
  getByText(text: string | RegExp): Promise<string>;
  
  // 等待方法
  waitForOutput(text: string | RegExp, timeout?: number): Promise<void>;
  waitForExit(timeout?: number): Promise<number>;
  
  // 用户交互
  userEvent: {
    type(text: string): Promise<void>;
    keyboard(keys: string): Promise<void>;
  };
  
  // 进程控制
  clear(): Promise<void>;
  kill(signal?: NodeJS.Signals): Promise<void>;
}
```

### CLITestConfig 接口

CLI 测试配置选项。

```typescript
interface CLITestConfig {
  readonly timeout: number;              // 测试超时时间（毫秒）
  readonly maxBuffer: number;            // 输出缓冲区大小
  readonly env: Record<string, string>;  // 环境变量
  readonly cwd: string;                  // 工作目录
  readonly cleanup: boolean;             // 是否自动清理
}
```

### TestScenario 接口

测试场景配置。

```typescript
interface TestScenario {
  readonly name: string;              // 测试场景名称
  readonly command: string;           // CLI命令
  readonly args: string[];            // 命令参数
  readonly expectedOutput: string[];  // 期望输出
  readonly expectedExitCode: number;  // 期望退出码
  readonly interactive?: boolean;     // 是否交互式
  readonly timeout?: number;          // 自定义超时
}
```

## CLITestingUtils 类

### 基础方法

#### renderCLI
执行 CLI 命令并返回测试实例。

```typescript
static async renderCLI(
  command: string,
  args: string[] = [],
  config: Partial<CLITestConfig> = {}
): Promise<CLITestResult>
```

**示例：**
```typescript
const result = await CLITestingUtils.renderCLI('complexity', ['--version']);
console.log(result.stdout); // 获取输出内容
```

#### waitForOutput
等待特定文本出现在输出中。

```typescript
static async waitForOutput(
  instance: CLITestResult,
  text: string | RegExp,
  timeout: number = 5000
): Promise<void>
```

**示例：**
```typescript
await CLITestingUtils.waitForOutput(result, 'Analysis completed', 10000);
```

#### findByText
查询输出内容中是否包含指定文本。

```typescript
static async findByText(
  instance: CLITestResult,
  text: string | RegExp
): Promise<boolean>
```

**示例：**
```typescript
const found = await CLITestingUtils.findByText(result, /Functions: \d+/);
expect(found).toBe(true);
```

#### queryByText
查询输出内容，返回匹配的文本或 null。

```typescript
static async queryByText(
  instance: CLITestResult,
  text: string | RegExp
): Promise<string | null>
```

**示例：**
```typescript
const match = await CLITestingUtils.queryByText(result, /Version: (\d+\.\d+\.\d+)/);
if (match) {
  console.log('Found version:', match);
}
```

### 用户交互方法

#### sendInput
模拟用户文本输入。

```typescript
static async sendInput(
  instance: CLITestResult,
  input: string
): Promise<void>
```

**示例：**
```typescript
await CLITestingUtils.sendInput(result, 'my-project-name\n');
```

#### sendKeys
模拟键盘按键。

```typescript
static async sendKeys(
  instance: CLITestResult,
  keys: string
): Promise<void>
```

**示例：**
```typescript
await CLITestingUtils.sendKeys(result, '\u0003'); // 发送 Ctrl+C
```

### 进程管理方法

#### cleanup
清理单个 CLI 测试实例。

```typescript
static async cleanup(instance: CLITestResult): Promise<void>
```

**示例：**
```typescript
try {
  // 测试逻辑
  const result = await CLITestingUtils.renderCLI('command', ['args']);
  // ...
} finally {
  await CLITestingUtils.cleanup(result);
}
```

#### cleanupAll
清理所有活动的 CLI 进程。

```typescript
static async cleanupAll(): Promise<void>
```

**示例：**
```typescript
afterAll(async () => {
  await CLITestingUtils.cleanupAll();
});
```

#### forceCleanupAll
强制清理所有进程（不等待优雅终止）。

```typescript
static async forceCleanupAll(): Promise<void>
```

### 监控和调试方法

#### getActiveProcessCount
获取当前活动进程数量。

```typescript
static getActiveProcessCount(): number
```

#### getActiveProcessesInfo
获取活动进程的详细信息。

```typescript
static getActiveProcessesInfo(): Array<{
  processId?: number;
  command: string;
  runtime: number;
  isKilled: boolean;
}>
```

#### validateExecution
验证 CLI 执行结果。

```typescript
static async validateExecution(
  instance: CLITestResult,
  expectedExitCode: number = 0,
  expectedOutput?: string[],
  timeout: number = 5000
): Promise<TestExecutionResult>
```

### 配置方法

#### getDefaultConfig
获取默认配置。

```typescript
static getDefaultConfig(): CLITestConfig
```

#### setDefaultConfig
设置全局默认配置。

```typescript
static setDefaultConfig(config: Partial<CLITestConfig>): void
```

**示例：**
```typescript
CLITestingUtils.setDefaultConfig({
  timeout: 15000,
  maxBuffer: 2 * 1024 * 1024 // 2MB
});
```

### 性能监控方法

#### getPerformanceMetrics
获取性能监控指标。

```typescript
static getPerformanceMetrics(): Record<string, any>
```

#### getPerformanceSuggestions
获取性能优化建议。

```typescript
static getPerformanceSuggestions(): string[]
```

#### optimizeTestSuite
优化测试套件性能。

```typescript
static optimizeTestSuite(): {
  recommendations: string[];
  optimizedConfig: Partial<CLITestConfig>;
}
```

### 批量执行方法

#### runTestBatch
批量执行测试用例。

```typescript
static async runTestBatch<T>(
  testCases: Array<() => Promise<T>>,
  concurrency: number = 3
): Promise<Array<{ result?: T; error?: Error; duration: number }>>
```

**示例：**
```typescript
const testCases = [
  () => CLITestingUtils.renderCLI('command', ['arg1']),
  () => CLITestingUtils.renderCLI('command', ['arg2']),
  () => CLITestingUtils.renderCLI('command', ['arg3'])
];

const results = await CLITestingUtils.runTestBatch(testCases, 2);
```

## TestUtils 扩展方法

### CLI 测试封装方法

#### executeCLITest
执行 CLI 测试的便捷方法。

```typescript
static async executeCLITest(
  command: string,
  args: string[] = []
): Promise<CLITestResult>
```

#### expectCLIOutput
验证 CLI 输出包含期望的文本。

```typescript
static async expectCLIOutput(
  result: CLITestResult,
  expectedTexts: string[],
  options?: OutputValidationOptions
): Promise<void>
```

#### expectCLISuccess
验证 CLI 成功执行。

```typescript
static async expectCLISuccess(result: CLITestResult): Promise<void>
```

#### expectCLIFailure
验证 CLI 执行失败。

```typescript
static async expectCLIFailure(
  result: CLITestResult,
  expectedError?: string,
  options?: OutputValidationOptions
): Promise<void>
```

#### runCLIScenario
运行完整的 CLI 测试场景。

```typescript
static async runCLIScenario(
  scenario: TestScenario
): Promise<TestExecutionResult>
```

### 高级验证方法

#### expectCLIOutputMatches
使用正则表达式验证输出。

```typescript
static async expectCLIOutputMatches(
  result: CLITestResult,
  pattern: RegExp,
  options?: OutputValidationOptions
): Promise<void>
```

#### expectCLIJSONOutput
验证 JSON 格式输出。

```typescript
static async expectCLIJSONOutput(
  result: CLITestResult,
  schema: object,
  options?: JSONValidationOptions
): Promise<void>
```

#### expectCLITableOutput
验证表格格式输出。

```typescript
static async expectCLITableOutput(
  result: CLITestResult,
  expectedColumns: string[],
  options?: TableValidationOptions
): Promise<void>
```

#### expectCLIPerformance
验证性能指标。

```typescript
static async expectCLIPerformance(
  result: CLITestResult,
  maxTime: number,
  options?: OutputValidationOptions
): Promise<void>
```

#### validateCLIComprehensive
综合验证 CLI 结果。

```typescript
static async validateCLIComprehensive(
  result: CLITestResult,
  validations: {
    contains?: string[];
    matches?: RegExp[];
    json?: object;
    table?: string[];
    performance?: number;
    fuzzy?: { target: string; threshold?: number }[];
  },
  options?: OutputValidationOptions
): Promise<void>
```

## OutputValidator 类

### 基础验证方法

#### findInOutput
在输出中查找匹配的文本。

```typescript
static async findInOutput(
  output: string,
  pattern: string | RegExp,
  options: OutputValidationOptions = {}
): Promise<string[]>
```

#### assertOutputContains
断言输出包含期望的文本。

```typescript
static async assertOutputContains(
  output: string,
  expected: string[],
  options: OutputValidationOptions = {}
): Promise<ValidationResult>
```

#### assertOutputMatches
断言输出匹配正则表达式。

```typescript
static async assertOutputMatches(
  output: string,
  pattern: RegExp,
  options: OutputValidationOptions = {}
): Promise<ValidationResult>
```

### 结构化验证方法

#### validateJSONOutput
验证 JSON 格式输出。

```typescript
static async validateJSONOutput(
  output: string,
  schema: object,
  options: JSONValidationOptions = {}
): Promise<ValidationResult>
```

#### validateTableOutput
验证表格格式输出。

```typescript
static async validateTableOutput(
  output: string,
  expectedColumns: string[],
  options: TableValidationOptions = {}
): Promise<ValidationResult>
```

### 性能验证方法

#### extractMetrics
从输出中提取性能指标。

```typescript
static async extractMetrics(output: string): Promise<PerformanceMetrics>
```

#### validatePerformanceOutput
验证性能输出。

```typescript
static async validatePerformanceOutput(
  output: string,
  maxTime: number,
  options: OutputValidationOptions = {}
): Promise<ValidationResult>
```

#### validateAdvancedPerformance
高级性能指标验证。

```typescript
static async validateAdvancedPerformance(
  output: string,
  options: AdvancedPerformanceOptions = {}
): Promise<ValidationResult>
```

### 多行验证方法

#### validateMultilineStructure
验证多行输出结构。

```typescript
static async validateMultilineStructure(
  output: string,
  expectedStructure: {
    sections?: Array<{
      name: string;
      patterns: (string | RegExp)[];
      required?: boolean;
      order?: number;
    }>;
    linePatterns?: (string | RegExp)[];
    minLines?: number;
    maxLines?: number;
  },
  options: MultilineValidationOptions = {}
): Promise<ValidationResult>
```

### 比较和分析方法

#### compareOutputs
比较两个输出的差异。

```typescript
static async compareOutputs(
  expected: string,
  actual: string,
  options: OutputValidationOptions = {}
): Promise<DiffResult>
```

#### fuzzyMatch
模糊匹配文本。

```typescript
static async fuzzyMatch(
  output: string,
  target: string,
  threshold: number = 0.8,
  options: OutputValidationOptions = {}
): Promise<ValidationResult>
```

#### advancedPatternMatch
高级模式匹配。

```typescript
static async advancedPatternMatch(
  output: string,
  queries: Array<{
    name: string;
    pattern: string | RegExp;
    type: 'exact' | 'fuzzy' | 'regex' | 'contains';
    options?: {
      minMatches?: number;
      maxMatches?: number;
      fuzzyThreshold?: number;
      caseSensitive?: boolean;
    };
  }>,
  options: OutputValidationOptions = {}
): Promise<{
  results: Array<{
    name: string;
    matches: string[];
    passed: boolean;
    message: string;
  }>;
  overallPassed: boolean;
}>
```

## InteractiveTestHelper 类

### 核心交互方法

#### simulateUserFlow
模拟完整的用户交互流程。

```typescript
static async simulateUserFlow(
  instance: CLITestResult,
  flow: InteractiveStep[],
  config: Partial<InteractiveTestConfig> = {}
): Promise<InteractiveTestResult>
```

#### waitForPrompt
等待特定提示信息出现。

```typescript
static async waitForPrompt(
  instance: CLITestResult,
  prompt: string | RegExp,
  timeout: number = 5000
): Promise<void>
```

#### sendKeySequence
发送键盘按键序列。

```typescript
static async sendKeySequence(
  instance: CLITestResult,
  keys: string | string[]
): Promise<void>
```

### 便捷交互方法

#### confirmDialog
模拟确认对话框交互。

```typescript
static async confirmDialog(
  instance: CLITestResult,
  prompt: string | RegExp,
  confirm: boolean,
  timeout: number = 5000
): Promise<void>
```

#### selectFromMenu
模拟选择菜单交互。

```typescript
static async selectFromMenu(
  instance: CLITestResult,
  menuPrompt: string | RegExp,
  choice: string | number,
  useArrowKeys: boolean = false,
  timeout: number = 5000
): Promise<void>
```

#### inputText
模拟文本输入对话框。

```typescript
static async inputText(
  instance: CLITestResult,
  prompt: string | RegExp,
  input: string,
  timeout: number = 5000
): Promise<void>
```

#### inputPassword
模拟密码输入。

```typescript
static async inputPassword(
  instance: CLITestResult,
  prompt: string | RegExp,
  password: string,
  timeout: number = 5000
): Promise<void>
```

#### inputMultilineText
模拟多行文本输入。

```typescript
static async inputMultilineText(
  instance: CLITestResult,
  prompt: string | RegExp,
  lines: string[],
  endSequence: string = KeyMap.CTRL_D,
  timeout: number = 5000
): Promise<void>
```

#### interruptProgram
中断正在运行的程序。

```typescript
static async interruptProgram(
  instance: CLITestResult,
  waitForExit: boolean = true,
  timeout: number = 3000
): Promise<void>
```

### 步骤构建器

#### createSteps
提供常用交互步骤的快速构建方法。

```typescript
static createSteps = {
  confirm(prompt: string | RegExp, answer: boolean, description?: string): InteractiveStep;
  input(prompt: string | RegExp, text: string, description?: string): InteractiveStep;
  select(prompt: string | RegExp, choice: string | number, description?: string): InteractiveStep;
  wait(prompt: string | RegExp, delay?: number, description?: string): InteractiveStep;
  key(prompt: string | RegExp, key: string, description?: string): InteractiveStep;
}
```

### 调试和验证方法

#### debugOutput
显示当前输出内容（调试用）。

```typescript
static debugOutput(instance: CLITestResult, label: string = 'Current Output'): void
```

#### validateResult
验证交互式测试结果。

```typescript
static validateResult(
  result: InteractiveTestResult,
  expectedSteps?: number
): void
```

## 错误类型

### CLITestError
CLI 测试基础错误类。

```typescript
class CLITestError extends Error {
  constructor(
    message: string,
    public readonly command: string,
    public readonly output?: string,
    public readonly exitCode?: number
  )
}
```

### OutputValidationError
输出验证错误。

```typescript
class OutputValidationError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly expected: string,
    public readonly actual: string
  )
}
```

### InteractiveTestError
交互式测试错误。

```typescript
class InteractiveTestError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly step: string
  )
}
```

### TestTimeoutError
测试超时错误。

```typescript
class TestTimeoutError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly timeoutMs: number
  )
}
```

### ProcessCleanupError
进程清理错误。

```typescript
class ProcessCleanupError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly processId?: number
  )
}
```

### BufferOverflowError
缓冲区溢出错误。

```typescript
class BufferOverflowError extends CLITestError {
  constructor(
    message: string,
    command: string,
    public readonly bufferSize: number,
    public readonly maxBuffer: number
  )
}
```

## 配置选项类型

### OutputValidationOptions
输出验证选项。

```typescript
interface OutputValidationOptions {
  readonly caseSensitive?: boolean;  // 是否区分大小写
  readonly multiline?: boolean;      // 是否多行模式
  readonly fuzzyThreshold?: number;  // 模糊匹配阈值 (0-1)
  readonly debug?: boolean;          // 是否启用调试
}
```

### JSONValidationOptions
JSON 验证选项。

```typescript
interface JSONValidationOptions extends OutputValidationOptions {
  readonly allowPartial?: boolean;  // 是否允许部分匹配
  readonly strictMode?: boolean;    // 是否严格模式
}
```

### TableValidationOptions
表格验证选项。

```typescript
interface TableValidationOptions extends OutputValidationOptions {
  readonly delimiter?: string;       // 分隔符
  readonly hasHeader?: boolean;      // 是否有表头
  readonly ignoreEmptyRows?: boolean; // 是否忽略空行
}
```

### InteractiveTestConfig
交互式测试配置。

```typescript
interface InteractiveTestConfig {
  readonly defaultTimeout: number;    // 默认超时时间
  readonly defaultDelay: number;      // 默认延迟时间
  readonly autoEnter: boolean;        // 是否自动按回车
  readonly debug: boolean;            // 是否启用调试
  readonly stabilizeDelay: number;    // 稳定延迟时间
}
```

## 常量定义

### KeyMap
键盘按键映射常量。

```typescript
const KeyMap = {
  // 控制键
  ENTER: '\n',
  TAB: '\t',
  ESCAPE: '\u001b',
  BACKSPACE: '\u0008',
  DELETE: '\u007f',
  
  // 方向键
  ARROW_UP: '\u001b[A',
  ARROW_DOWN: '\u001b[B',
  ARROW_RIGHT: '\u001b[C',
  ARROW_LEFT: '\u001b[D',
  
  // 功能键
  HOME: '\u001b[H',
  END: '\u001b[F',
  PAGE_UP: '\u001b[5~',
  PAGE_DOWN: '\u001b[6~',
  
  // Ctrl 组合键
  CTRL_C: '\u0003',
  CTRL_D: '\u0004',
  CTRL_Z: '\u001a',
  CTRL_L: '\u000c',
  
  // 常用字符
  SPACE: ' ',
  Y: 'y',
  N: 'n',
  YES: 'yes',
  NO: 'no'
} as const;
```

## 使用示例

### 基础 CLI 测试

```typescript
import { CLITestingUtils, TestUtils } from '../helpers';

test('basic CLI test', async () => {
  const result = await CLITestingUtils.renderCLI('complexity', ['--version']);
  
  try {
    await TestUtils.expectCLISuccess(result);
    expect(await result.findByText(/\d+\.\d+\.\d+/)).toBe(true);
  } finally {
    await CLITestingUtils.cleanup(result);
  }
});
```

### 交互式测试

```typescript
import { InteractiveTestHelper, InteractiveSteps } from '../helpers';

test('interactive configuration', async () => {
  const result = await CLITestingUtils.renderCLI('complexity', ['init']);
  
  try {
    const interactiveResult = await InteractiveTestHelper.simulateUserFlow(result, [
      InteractiveSteps.input(/Project name:/, 'my-project'),
      InteractiveSteps.confirm(/Save configuration\?/, true)
    ]);
    
    expect(interactiveResult.success).toBe(true);
  } finally {
    await CLITestingUtils.cleanup(result);
  }
});
```

### 输出验证测试

```typescript
import { OutputValidator } from '../helpers';

test('advanced output validation', async () => {
  const result = await CLITestingUtils.renderCLI('complexity', ['scan', 'file.ts']);
  
  try {
    const validation = await OutputValidator.validateMultilineStructure(
      result.stdout,
      {
        sections: [
          {
            name: 'Header',
            patterns: [/=== Analysis Results ===/],
            required: true,
            order: 1
          }
        ],
        minLines: 5
      }
    );
    
    expect(validation.passed).toBe(true);
  } finally {
    await CLITestingUtils.cleanup(result);
  }
});
```

这个 API 文档提供了新 CLI 测试框架的完整参考，涵盖了所有可用的类、方法、接口和配置选项。