# CLI 测试迁移指南

## 概述

本指南详细说明如何从旧的 `TestUtils.executeCommand` 方法迁移到新的 `cli-testing-library` 测试框架。新框架提供了更强大的功能、更好的错误处理和更灵活的测试选项。

## 迁移对比

### 旧的测试方式

```typescript
// 旧的方式 - 使用 TestUtils.executeCommand
import { TestUtils } from '../helpers/test-utils';

test('should analyze TypeScript file', async () => {
  const result = await TestUtils.executeCommand('cognitive-complexity', ['scan', 'test.ts']);
  
  expect(result.exitCode).toBe(0);
  expect(result.stdout).toContain('Analysis completed');
});
```

### 新的测试方式

```typescript
// 新的方式 - 使用 CLITestingUtils
import { CLITestingUtils } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';

test('should analyze TypeScript file', async () => {
  const result = await CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'test.ts']);
  
  try {
    await TestUtils.expectCLISuccess(result);
    expect(await result.findByText('Analysis completed')).toBe(true);
  } finally {
    await CLITestingUtils.cleanup(result);
  }
});
```

## 逐步迁移过程

### 步骤 1: 更新导入语句

**旧的导入：**
```typescript
import { TestUtils } from '../helpers/test-utils';
```

**新的导入：**
```typescript
import { CLITestingUtils, CLITestResult } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';
import { OutputValidator } from '../helpers/output-validator';
```

### 步骤 2: 替换命令执行方法

**旧的执行方式：**
```typescript
const result = await TestUtils.executeCommand('command', ['args']);
```

**新的执行方式：**
```typescript
const result = await CLITestingUtils.renderCLI('command', ['args']);
// 或使用 TestUtils 的封装方法
const result = await TestUtils.executeCLITest('command', ['args']);
```

### 步骤 3: 更新输出验证逻辑

**旧的验证方式：**
```typescript
expect(result.stdout).toContain('expected text');
expect(result.stderr).toBe('');
expect(result.exitCode).toBe(0);
```

**新的验证方式：**
```typescript
// 方式 1: 使用 TestUtils 封装方法
await TestUtils.expectCLISuccess(result);
await TestUtils.expectCLIOutput(result, ['expected text']);

// 方式 2: 使用 CLITestResult 的查询方法
expect(await result.findByText('expected text')).toBe(true);
const exitCode = await result.waitForExit();
expect(exitCode).toBe(0);

// 方式 3: 使用 OutputValidator 的高级验证
const validation = await OutputValidator.assertOutputContains(
  result.stdout,
  ['expected text'],
  { caseSensitive: false }
);
expect(validation.passed).toBe(true);
```

### 步骤 4: 添加资源清理

**新的测试需要显式清理：**
```typescript
describe('CLI Tests', () => {
  let cliResult: CLITestResult | undefined;
  
  afterEach(async () => {
    if (cliResult) {
      await CLITestingUtils.cleanup(cliResult);
      cliResult = undefined;
    }
  });
  
  test('example test', async () => {
    cliResult = await CLITestingUtils.renderCLI('command', ['args']);
    // 测试逻辑...
  });
});
```

**或使用 try-finally 模式：**
```typescript
test('should handle command execution', async () => {
  const result = await CLITestingUtils.renderCLI('command', ['args']);
  
  try {
    // 测试逻辑
    await TestUtils.expectCLISuccess(result);
  } finally {
    await CLITestingUtils.cleanup(result);
  }
});
```

### 步骤 5: 更新错误处理

**旧的错误处理：**
```typescript
const result = await TestUtils.executeCommand('invalid-command');
expect(result.exitCode).not.toBe(0);
expect(result.stderr).toContain('error message');
```

**新的错误处理：**
```typescript
const result = await CLITestingUtils.renderCLI('invalid-command');

try {
  await TestUtils.expectCLIFailure(result, 'error message');
} finally {
  await CLITestingUtils.cleanup(result);
}
```

## 完整迁移示例

### 迁移前的测试文件

```typescript
// 旧版本 - cli.test.ts
import { TestUtils } from '../helpers/test-utils';

describe('CLI Integration Tests', () => {
  test('should display version', async () => {
    const result = await TestUtils.executeCommand('cognitive-complexity', ['--version']);
    expect(result.exitCode).toBe(0);
    expect(result.stdout).toMatch(/\d+\.\d+\.\d+/);
  });

  test('should analyze single file', async () => {
    const result = await TestUtils.executeCommand('cognitive-complexity', ['scan', 'test.ts']);
    expect(result.exitCode).toBe(0);
    expect(result.stdout).toContain('Analysis completed');
    expect(result.stdout).toContain('Functions analyzed:');
  });

  test('should handle invalid files', async () => {
    const result = await TestUtils.executeCommand('cognitive-complexity', ['scan', 'nonexistent.ts']);
    expect(result.exitCode).not.toBe(0);
    expect(result.stderr).toContain('File not found');
  });

  test('should generate JSON output', async () => {
    const result = await TestUtils.executeCommand('cognitive-complexity', ['--format', 'json']);
    expect(result.exitCode).toBe(0);
    
    const json = JSON.parse(result.stdout);
    expect(json).toHaveProperty('summary');
    expect(json.summary).toHaveProperty('totalComplexity');
  });
});
```

### 迁移后的测试文件

```typescript
// 新版本 - cli.test.ts
import { CLITestingUtils, CLITestResult } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';
import { OutputValidator } from '../helpers/output-validator';

describe('CLI Integration Tests', () => {
  let cliResult: CLITestResult | undefined;
  
  afterEach(async () => {
    if (cliResult) {
      await CLITestingUtils.cleanup(cliResult);
      cliResult = undefined;
    }
  });
  
  afterAll(async () => {
    // 确保所有进程都被清理
    await CLITestingUtils.cleanupAll();
  });

  test('should display version', async () => {
    cliResult = await CLITestingUtils.renderCLI('cognitive-complexity', ['--version']);
    
    await TestUtils.expectCLISuccess(cliResult);
    expect(await cliResult.findByText(/\d+\.\d+\.\d+/)).toBe(true);
  });

  test('should analyze single file', async () => {
    cliResult = await TestUtils.executeCLITest('cognitive-complexity', ['scan', 'test.ts']);
    
    await TestUtils.expectCLISuccess(cliResult);
    await TestUtils.expectCLIOutput(cliResult, [
      'Analysis completed',
      'Functions analyzed:'
    ]);
  });

  test('should handle invalid files', async () => {
    cliResult = await CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'nonexistent.ts']);
    
    await TestUtils.expectCLIFailure(cliResult, 'File not found');
  });

  test('should generate JSON output', async () => {
    cliResult = await CLITestingUtils.renderCLI('cognitive-complexity', ['--format', 'json']);
    
    await TestUtils.expectCLIJSONOutput(cliResult, {
      summary: {
        totalComplexity: expect.any(Number)
      }
    });
  });
});
```

## 高级迁移功能

### 交互式测试迁移

如果原来有一些手动测试或模拟用户输入的测试，现在可以使用交互式测试框架：

```typescript
import { InteractiveTestHelper, InteractiveSteps } from '../helpers/interactive-test-helper';

test('should handle interactive configuration', async () => {
  const result = await CLITestingUtils.renderCLI('cognitive-complexity', ['init']);
  
  try {
    const interactiveResult = await InteractiveTestHelper.simulateUserFlow(result, [
      InteractiveSteps.input(/Project name:/, 'my-project'),
      InteractiveSteps.select(/Output format:/, '2'),
      InteractiveSteps.confirm(/Save configuration\?/, true)
    ]);
    
    expect(interactiveResult.success).toBe(true);
    expect(await result.findByText('Configuration saved')).toBe(true);
  } finally {
    await CLITestingUtils.cleanup(result);
  }
});
```

### 性能测试迁移

对于需要验证性能的测试，可以使用新的性能测试工具：

```typescript
import { PerformanceTestUtils } from '../helpers/test-utils';

test('should meet performance requirements', async () => {
  const performanceResult = await PerformanceTestUtils.runCLIBenchmark(
    () => CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'large-project/']),
    'cli-basic'
  );
  
  expect(performanceResult.passed).toBe(true);
  if (!performanceResult.passed) {
    console.log('Performance violations:', performanceResult.violations);
    console.log('Suggestions:', performanceResult.suggestions);
  }
});
```

### 并发测试迁移

如果需要测试并发执行情况：

```typescript
test('should handle concurrent execution', async () => {
  const concurrencyResult = await PerformanceTestUtils.runConcurrencyTest(
    () => CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'test.ts']),
    { concurrentCount: 3, testDuration: 5000 }
  );
  
  expect(concurrencyResult.summary.successRate).toBeGreaterThan(90);
});
```

## 迁移检查清单

使用以下检查清单确保迁移完整：

### 基础迁移
- [ ] 更新导入语句，添加新的测试工具类
- [ ] 将 `TestUtils.executeCommand` 替换为 `CLITestingUtils.renderCLI`
- [ ] 更新输出验证逻辑，使用新的查询和验证方法
- [ ] 添加适当的资源清理（afterEach/afterAll 或 try-finally）
- [ ] 更新错误处理，使用 `TestUtils.expectCLIFailure`

### 输出验证升级
- [ ] 将字符串包含检查替换为 `findByText` 或 `OutputValidator.assertOutputContains`
- [ ] 将正则表达式匹配替换为 `OutputValidator.assertOutputMatches`
- [ ] 对于 JSON 输出，使用 `TestUtils.expectCLIJSONOutput`
- [ ] 对于表格输出，使用 `TestUtils.expectCLITableOutput`

### 高级功能添加
- [ ] 考虑添加交互式测试场景（如果有用户输入）
- [ ] 添加性能测试（对于耗时较长的命令）
- [ ] 考虑并发测试（如果需要验证并发安全性）
- [ ] 使用调试工具进行测试开发和问题排查

### 测试质量改进
- [ ] 确保所有测试都有适当的描述和分组
- [ ] 添加边界条件和错误场景测试
- [ ] 使用模拟数据生成器创建测试数据
- [ ] 添加超时处理和异常情况测试

## 常见迁移问题和解决方案

### 问题 1: 测试运行后进程未清理

**症状：** 测试完成后仍有子进程在运行

**解决方案：**
```typescript
// 确保在每个测试后清理
afterEach(async () => {
  if (cliResult) {
    await CLITestingUtils.cleanup(cliResult);
  }
});

// 确保在测试套件结束时强制清理
afterAll(async () => {
  await CLITestingUtils.cleanupAll();
});
```

### 问题 2: 异步操作超时

**症状：** 测试因为等待输出超时而失败

**解决方案：**
```typescript
// 增加超时时间
const result = await CLITestingUtils.renderCLI('command', ['args'], {
  timeout: 15000  // 15秒
});

// 或在等待输出时增加超时
await result.waitForOutput('expected text', 10000);
```

### 问题 3: 输出验证不稳定

**症状：** 同样的测试有时通过，有时失败

**解决方案：**
```typescript
// 使用更宽松的验证条件
const validation = await OutputValidator.assertOutputContains(
  result.stdout,
  ['expected text'],
  { 
    caseSensitive: false,
    fuzzyThreshold: 0.8  // 允许模糊匹配
  }
);

// 或添加稳定性延迟
await result.waitForOutput('expected text');
await new Promise(resolve => setTimeout(resolve, 200)); // 等待输出稳定
```

### 问题 4: JSON 输出解析失败

**症状：** JSON 输出验证抛出解析错误

**解决方案：**
```typescript
// 先验证是否包含 JSON 内容
expect(await result.findByText(/\{.*\}/)).toBe(true);

// 使用更宽松的 JSON 验证
await TestUtils.expectCLIJSONOutput(result, {
  summary: expect.objectContaining({
    totalComplexity: expect.any(Number)
  })
}, { allowPartial: true });
```

## 批量迁移脚本

可以使用以下脚本辅助批量迁移：

```bash
#!/bin/bash
# migrate-cli-tests.sh

# 查找所有使用旧方法的测试文件
find src/__test__ -name "*.test.ts" -exec grep -l "TestUtils.executeCommand" {} \;

# 替换基本的方法调用
find src/__test__ -name "*.test.ts" -exec sed -i.bak 's/TestUtils.executeCommand/CLITestingUtils.renderCLI/g' {} \;

# 添加清理代码提醒
echo "请手动添加以下代码到迁移后的测试文件："
echo "1. 导入新的测试工具类"
echo "2. 添加 afterEach 和 afterAll 清理逻辑"
echo "3. 更新输出验证逻辑"
echo "4. 添加 ResourceCleanup 处理"
```

## 验证迁移成功

迁移完成后，运行以下检查：

```bash
# 运行所有测试确保没有破坏性变更
npm test

# 检查是否有进程泄漏
ps aux | grep cognitive-complexity

# 运行性能基准确保性能没有回退
npm run test:performance
```

## 迁移后的维护

### 定期清理
```typescript
// 在测试套件中添加定期清理
beforeAll(() => {
  // 重置性能监控
  CLITestingUtils.resetPerformanceMonitor();
});

afterAll(async () => {
  // 获取性能报告
  const metrics = CLITestingUtils.getPerformanceMetrics();
  const suggestions = CLITestingUtils.getPerformanceSuggestions();
  
  if (suggestions.length > 0) {
    console.log('Performance suggestions:', suggestions);
  }
});
```

### 性能优化
```typescript
// 定期运行性能优化
test('performance optimization check', () => {
  const optimization = CLITestingUtils.optimizeTestSuite();
  
  if (optimization.recommendations.length > 0) {
    console.log('Optimization recommendations:', optimization.recommendations);
  }
});
```

这个迁移指南提供了从旧测试框架到新框架的完整迁移路径，包括所有必要的代码变更和最佳实践。