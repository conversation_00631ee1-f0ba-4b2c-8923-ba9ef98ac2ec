# Cognitive Complexity Analyzer - API 文档

## 目录

- [基础用法](#基础用法)
- [核心API](#核心api)
- [规则引擎API](#规则引擎api)
- [配置系统](#配置系统)
- [输出格式](#输出格式)
- [插件开发](#插件开发)
- [性能监控](#性能监控)
- [类型定义](#类型定义)
- [高级用法](#高级用法)

## 基础用法

### CLI 使用

```bash
# 基本分析
cognitive-complexity src/

# 详细分析
cognitive-complexity --details src/

# 设置复杂度阈值
cognitive-complexity --fail-on 10 src/

# 输出JSON格式
cognitive-complexity --format json src/

# 启动Web UI
cognitive-complexity --ui src/
```

### 程序化API

```typescript
import { ComplexityCalculator } from '@imd/cognitive-complexity';

// 创建计算器实例
const calculator = new ComplexityCalculator({
  maxComplexity: 15,
  includeDetails: true
});

// 分析单个文件
const result = await calculator.calculateFile('path/to/file.ts');
console.log(result);

// 分析多个文件
const files = ['src/index.ts', 'src/utils.ts'];
const results = await Promise.all(
  files.map(file => calculator.calculateFile(file))
);
```

## 核心API

## 规则引擎API

### AsyncRuleEngine

现代化的异步规则引擎，支持并行执行、智能缓存和动态扩展。

#### 构造函数

```typescript
new AsyncRuleEngine(config: EngineConfig)
```

**参数:**
- `config` - 引擎配置对象

**示例:**
```typescript
import { AsyncRuleEngine } from '@imd/cognitive-complexity';

const engine = new AsyncRuleEngine({
  maxConcurrency: 4,
  enableCache: true,
  cacheSize: 1000,
  timeout: 30000
});
```

#### 方法

##### analyzeNode(node: Node, context: AnalysisContext): Promise<NodeAnalysis>

异步分析单个AST节点。

**参数:**
- `node` - 要分析的AST节点
- `context` - 分析上下文

**返回值:**
- `Promise<NodeAnalysis>` - 节点分析结果

**示例:**
```typescript
const context = engine.createContext(sourceCode, config);
const nodeAnalysis = await engine.analyzeNode(functionNode, context);

console.log(`Node complexity: ${nodeAnalysis.complexity}`);
console.log(`Applied rules: ${nodeAnalysis.appliedRules.length}`);
```

##### analyzeFunction(func: FunctionNode): Promise<FunctionAnalysis>

分析函数节点的复杂度。

**参数:**
- `func` - 函数AST节点

**返回值:**
- `Promise<FunctionAnalysis>` - 函数分析结果

**示例:**
```typescript
const functionAnalysis = await engine.analyzeFunction(functionNode);

console.log(`Function: ${functionAnalysis.name}`);
console.log(`Complexity: ${functionAnalysis.complexity}`);
console.log(`Suggestions: ${functionAnalysis.suggestions.length}`);
```

##### analyzeFile(ast: Module): Promise<FileAnalysis>

分析整个文件的复杂度。

**参数:**
- `ast` - 文件的AST根节点

**返回值:**
- `Promise<FileAnalysis>` - 文件分析结果

**示例:**
```typescript
const ast = await parser.parseFile('src/example.ts');
const fileAnalysis = await engine.analyzeFile(ast);

console.log(`Total complexity: ${fileAnalysis.totalComplexity}`);
console.log(`Average complexity: ${fileAnalysis.averageComplexity}`);
console.log(`Functions analyzed: ${fileAnalysis.functions.length}`);
```

##### registerRule(rule: Rule): void

动态注册自定义规则。

**参数:**
- `rule` - 规则实现对象

**示例:**
```typescript
class CustomRule implements Rule {
  readonly id = 'custom-rule';
  readonly name = 'Custom Complexity Rule';
  readonly priority = 100;
  
  canHandle(node: Node): boolean {
    return node.type === 'CallExpression';
  }
  
  async evaluate(node: Node, context: AnalysisContext): Promise<RuleResult> {
    return {
      ruleId: this.id,
      complexity: 1,
      isExempted: false,
      shouldIncreaseNesting: false,
      reason: 'Custom rule applied',
      suggestions: [],
      metadata: {},
      executionTime: performance.now(),
      cacheHit: false
    };
  }
  
  getDependencies(): string[] {
    return [];
  }
}

engine.registerRule(new CustomRule());
```

##### getMetrics(): EngineMetrics

获取引擎性能指标。

**返回值:**
- `EngineMetrics` - 包含性能统计的对象

**示例:**
```typescript
const metrics = engine.getMetrics();

console.log(`Total analyses: ${metrics.totalAnalyses}`);
console.log(`Cache hit rate: ${metrics.cacheHitRate * 100}%`);
console.log(`Average execution time: ${metrics.averageExecutionTime}ms`);
console.log(`Rules registered: ${metrics.rulesRegistered}`);
```

### RuleRegistry

规则注册表，管理所有分析规则。

#### 方法

##### registerRule(rule: Rule): void

注册新规则到系统中。

**参数:**
- `rule` - 要注册的规则对象

##### getRulesForNode(node: Node): Rule[]

获取适用于指定节点的所有规则。

**参数:**
- `node` - AST节点

**返回值:**
- `Rule[]` - 适用规则列表

##### resolveDependencies(ruleId: string): Rule[]

解析规则的依赖关系。

**参数:**
- `ruleId` - 规则ID

**返回值:**
- `Rule[]` - 依赖的规则列表

##### loadPlugin(pluginPath: string): Promise<void>

动态加载插件规则。

**参数:**
- `pluginPath` - 插件文件路径

**示例:**
```typescript
import { RuleRegistry } from '@imd/cognitive-complexity';

const registry = new RuleRegistry();

// 注册规则
registry.registerRule(new MyCustomRule());

// 获取适用规则
const applicableRules = registry.getRulesForNode(node);

// 加载插件
await registry.loadPlugin('./plugins/my-plugin.js');
```

### CacheManager

智能缓存管理器，提供多层缓存策略。

#### 方法

##### getCachedNodeResult(nodeHash: string): Promise<NodeAnalysis | null>

获取节点级缓存结果。

**参数:**
- `nodeHash` - 节点哈希值

**返回值:**
- `Promise<NodeAnalysis | null>` - 缓存的节点分析结果

##### setCachedNodeResult(nodeHash: string, result: NodeAnalysis): void

设置节点级缓存结果。

**参数:**
- `nodeHash` - 节点哈希值
- `result` - 分析结果

##### getCachedTypeInfo(nodeType: string): TypeInfo | null

获取类型级缓存信息。

**参数:**
- `nodeType` - 节点类型

**返回值:**
- `TypeInfo | null` - 缓存的类型信息

##### getHitRate(): number

获取缓存命中率。

**返回值:**
- `number` - 命中率（0-1之间）

**示例:**
```typescript
import { CacheManager } from '@imd/cognitive-complexity';

const cache = new CacheManager({
  maxSize: 1000,
  ttl: 300000 // 5分钟
});

// 检查缓存
const cached = await cache.getCachedNodeResult(nodeHash);
if (cached) {
  console.log('Cache hit!');
  return cached;
}

// 设置缓存
cache.setCachedNodeResult(nodeHash, analysisResult);

// 监控缓存性能
console.log(`Cache hit rate: ${cache.getHitRate() * 100}%`);
```


主要的复杂度计算器类。

#### 构造函数

```typescript
new ComplexityCalculator(options?: CalculationOptions)
```

**参数:**
- `options` - 可选的计算选项

#### 方法

##### calculateFile(filePath: string): Promise<FunctionResult[]>

分析单个文件的复杂度。

**参数:**
- `filePath` - 要分析的文件路径

**返回值:**
- `Promise<FunctionResult[]>` - 函数分析结果数组

**示例:**
```typescript
const calculator = new ComplexityCalculator();
const functions = await calculator.calculateFile('src/index.ts');

functions.forEach(fn => {
  console.log(`${fn.name}: ${fn.complexity}`);
});
```

##### calculateCode(code: string, filePath?: string): Promise<FunctionResult[]>

分析代码字符串的复杂度。

**参数:**
- `code` - 要分析的代码字符串
- `filePath` - 可选的文件路径（用于错误报告）

**返回值:**
- `Promise<FunctionResult[]>` - 函数分析结果数组

**示例:**
```typescript
const code = `
function example(x: number): number {
  if (x > 0) {
    return x * 2;
  } else {
    return x * -1;
  }
}
`;

const result = await calculator.calculateCode(code, 'example.ts');
console.log(result[0].complexity); // 输出: 2
```

### ConfigManager

配置管理器，用于加载和管理配置。

#### 静态方法

##### loadConfig(configPath?: string, searchFrom?: string): Promise<CognitiveConfig>

加载配置文件。

**参数:**
- `configPath` - 可选的配置文件路径
- `searchFrom` - 可选的搜索起始目录

**返回值:**
- `Promise<CognitiveConfig>` - 解析后的配置对象

**示例:**
```typescript
import { ConfigManager } from '@imd/cognitive-complexity';

// 自动搜索配置文件
const config = await ConfigManager.loadConfig();

// 指定配置文件
const config2 = await ConfigManager.loadConfig('./my-config.json');
```

### BaselineManager

基线管理器，用于创建和比较复杂度基线。

#### 方法

##### createBaseline(result: AnalysisResult, threshold?: number): Promise<void>

创建新的复杂度基线。

**参数:**
- `result` - 分析结果
- `threshold` - 可选的复杂度阈值

**示例:**
```typescript
import { BaselineManager } from '@imd/cognitive-complexity';

const baselineManager = new BaselineManager();
await baselineManager.createBaseline(analysisResult, 15);
```

##### compareWithBaseline(result: AnalysisResult, baseline: BaselineData): AnalysisResult

将当前结果与基线进行比较。

**参数:**
- `result` - 当前分析结果
- `baseline` - 基线数据

**返回值:**
- `AnalysisResult` - 包含比较信息的分析结果

## 配置系统

### 配置文件格式

支持多种配置文件格式：

**package.json:**
```json
{
  "cognitive-complexity": {
    "failOnComplexity": 15,
    "exclude": ["**/*.test.ts", "**/node_modules/**"],
    "report": {
      "html": "reports/complexity.html",
      "json": "reports/complexity.json"
    }
  }
}
```

**cognitive-complexity.json:**
```json
{
  "failOnComplexity": 15,
  "exclude": ["**/*.test.ts"],
  "rules": {
    "enableMixedLogicOperatorPenalty": true,
    "recursionChainThreshold": 10
  },
  "ui": {
    "port": 3000,
    "host": "localhost",
    "openBrowser": true
  }
}
```

**cognitive-complexity.config.js:**
```javascript
module.exports = {
  failOnComplexity: 15,
  exclude: [
    '**/*.test.ts',
    '**/node_modules/**'
  ],
  rules: {
    enableMixedLogicOperatorPenalty: true
  }
};
```

### 配置选项

#### 基础配置

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `failOnComplexity` | `number` | `15` | 复杂度阈值，超过则视为失败 |
| `exclude` | `string[]` | `[]` | 排除文件模式列表 |
| `includeOverrides` | `string[]` | `[]` | 包含覆盖模式列表 |
| `disableSmartExclusion` | `boolean` | `false` | 禁用智能排除 |

#### 规则配置

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `rules.enableMixedLogicOperatorPenalty` | `boolean` | `false` | 启用混合逻辑操作符惩罚 |
| `rules.recursionChainThreshold` | `number` | `10` | 递归链阈值 |

#### 报告配置

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `report.html` | `string` | - | HTML报告输出路径 |
| `report.json` | `string` | - | JSON报告输出路径 |

#### UI配置

| 选项 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `ui.port` | `number` | `3000` | Web UI端口 |
| `ui.host` | `string` | `localhost` | Web UI主机 |
| `ui.openBrowser` | `boolean` | `true` | 自动打开浏览器 |
| `ui.autoShutdown` | `boolean` | `false` | 自动关闭服务器 |

## 输出格式

### 文本格式 (默认)

```
🧠 认知复杂度分析结果
=======================

📋 分析汇总:
📁 文件数量: 5
🔍 函数数量: 23
📊 平均复杂度: 4.2
📈 总复杂度: 97
⚠️  高复杂度函数: 2

📋 详细分析结果:
──────────────────────────────────────────────────

📄 src/utils.ts (总复杂度: 15)
  └─ 🔴 complexFunction:45-67 (复杂度: 12)
  └─ 🟢 simpleFunction:69-73 (复杂度: 3)
```

### JSON格式

```json
{
  "summary": {
    "totalComplexity": 97,
    "averageComplexity": 4.217391304347826,
    "filesAnalyzed": 5,
    "functionsAnalyzed": 23,
    "highComplexityFunctions": 2
  },
  "results": [
    {
      "filePath": "src/utils.ts",
      "complexity": 15,
      "averageComplexity": 7.5,
      "functions": [
        {
          "name": "complexFunction",
          "complexity": 12,
          "line": 45,
          "endLine": 67,
          "filePath": "src/utils.ts"
        }
      ]
    }
  ]
}
```

### HTML格式

生成交互式HTML报告，包含：
- 复杂度统计图表
- 函数复杂度分布
- 代码热力图
- 详细的函数列表

## 插件开发

### 插件接口

```typescript
export interface Plugin {
  id: string;
  name: string;
  version: string;
  
  // 生命周期钩子
  initialize?(context: PluginContext): Promise<void>;
  analyze?(node: Node, context: AnalysisContext): Promise<PluginResult>;
  finalize?(results: AnalysisResult): Promise<AnalysisResult>;
}
```

### 创建插件

```typescript
// my-plugin.ts
export const myPlugin: Plugin = {
  id: 'my-plugin',
  name: 'My Custom Plugin',
  version: '1.0.0',
  
  async initialize(context) {
    console.log('Plugin initialized');
  },
  
  async analyze(node, context) {
    // 自定义分析逻辑
    if (node.type === 'FunctionDeclaration') {
      return {
        complexity: 1,
        suggestions: ['Consider refactoring this function']
      };
    }
    
    return { complexity: 0 };
  }
};
```

### 注册插件

```typescript
import { PluginManager } from '@imd/cognitive-complexity';
import { myPlugin } from './my-plugin';

const pluginManager = new PluginManager();
pluginManager.register(myPlugin);
```

## 类型定义

### 核心类型

```typescript
// 计算选项
interface CalculationOptions {
  maxComplexity?: number;
  includeDetails?: boolean;
  enableMixedLogicOperatorPenalty?: boolean;
  recursionChainThreshold?: number;
}

// 函数结果
interface FunctionResult {
  name: string;
  complexity: number;
  line: number;
  endLine: number;
  filePath: string;
}

// 文件结果
interface FileResult {
  filePath: string;
  complexity: number;
  averageComplexity: number;
  functions: FunctionResult[];
}

// 分析结果
interface AnalysisResult {
  summary: {
    totalComplexity: number;
    averageComplexity: number;
    filesAnalyzed: number;
    functionsAnalyzed: number;
    highComplexityFunctions: number;
  };
  results: FileResult[];
  baseline?: BaselineData;
}

// 配置类型
interface CognitiveConfig {
  failOnComplexity?: number;
  exclude?: string[];
  includeOverrides?: string[];
  disableSmartExclusion?: boolean;
  rules?: {
    enableMixedLogicOperatorPenalty?: boolean;
    recursionChainThreshold?: number;
  };
  report?: {
    html?: string;
    json?: string;
  };
  ui?: {
    port?: number;
    host?: string;
    openBrowser?: boolean;
    autoShutdown?: boolean;
  };
}
```

### CLI类型

```typescript
// CLI选项
interface CLIOptions {
  paths: string[];
  failOn?: number;
  createBaseline?: boolean;
  updateBaseline?: boolean;
  config?: string;
  outputDir?: string;
  details?: boolean;
  format?: 'text' | 'json' | 'html';
  min?: number;
  sort?: 'complexity' | 'name';
  exclude?: string[];
  excludePattern?: string[];
  includeDeps?: boolean;
  excludeDefaults?: boolean;
  showExcluded?: boolean;
  ui?: boolean;
  quiet?: boolean;
  noColors?: boolean;
}
```

## 错误处理

### 常见错误类型

```typescript
// 复杂度计算错误
class ComplexityError extends Error {
  constructor(message: string, public filePath?: string) {
    super(message);
    this.name = 'ComplexityError';
  }
}

// 配置错误
class ConfigError extends Error {
  constructor(message: string, public configPath?: string) {
    super(message);
    this.name = 'ConfigError';
  }
}

// 解析错误
class ParseError extends Error {
  constructor(message: string, public filePath: string, public line?: number) {
    super(message);
    this.name = 'ParseError';
  }
}
```

### 错误处理示例

```typescript
try {
  const result = await calculator.calculateFile('invalid-file.ts');
} catch (error) {
  if (error instanceof ComplexityError) {
    console.error(`Complexity analysis failed: ${error.message}`);
    if (error.filePath) {
      console.error(`File: ${error.filePath}`);
    }
  } else if (error instanceof ParseError) {
    console.error(`Parse error in ${error.filePath}: ${error.message}`);
    if (error.line) {
      console.error(`Line: ${error.line}`);
    }
  } else {
    console.error('Unknown error:', error);
  }
}
```

## 最佳实践

### 1. 配置文件组织

建议在项目根目录创建 `cognitive-complexity.config.js`：

```javascript
module.exports = {
  // 基础配置
  failOnComplexity: 15,
  
  // 排除测试文件和构建产物
  exclude: [
    '**/*.test.ts',
    '**/*.spec.ts',
    '**/dist/**',
    '**/build/**',
    '**/node_modules/**'
  ],
  
  // 启用高级规则
  rules: {
    enableMixedLogicOperatorPenalty: true,
    recursionChainThreshold: 8
  },
  
  // 生成报告
  report: {
    html: 'reports/complexity.html',
    json: 'reports/complexity.json'
  }
};
```

### 2. CI/CD 集成

```yaml
# .github/workflows/complexity.yml
name: Code Complexity Check

on: [push, pull_request]

jobs:
  complexity:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run complexity analysis
        run: npx @imd/cognitive-complexity --fail-on 15 src/
      
      - name: Generate reports
        run: npx @imd/cognitive-complexity --format html --output-dir reports src/
      
      - name: Upload reports
        uses: actions/upload-artifact@v3
        with:
          name: complexity-reports
          path: reports/
```

### 3. 基线管理工作流

```bash
# 初始创建基线
cognitive-complexity --create-baseline src/

# 定期更新基线（如发布前）
cognitive-complexity --update-baseline src/

# CI中检查复杂度变化
cognitive-complexity --fail-on 15 src/
```

### 4. 团队开发规范

```typescript
// 建议的复杂度阈值
const COMPLEXITY_THRESHOLDS = {
  LOW: 5,      // 简单函数
  MEDIUM: 10,  // 中等复杂度
  HIGH: 15,    // 需要重构
  CRITICAL: 25 // 必须重构
};

// 代码审查检查点
// - 新函数复杂度 < 10
// - 修改函数复杂度增加 < 3
// - 总体项目复杂度不增加
```

通过遵循这些最佳实践，可以有效控制React组件的复杂度，提高代码的可维护性和可读性。

## 性能监控

### PerformanceMonitor

提供全面的性能监控和分析功能。

#### 方法

##### startTimer(operation: string): Timer

启动性能计时器。

**参数:**
- `operation` - 操作名称

**返回值:**
- `Timer` - 计时器对象

**示例:**
```typescript
import { PerformanceMonitor } from '@imd/cognitive-complexity';

const monitor = new PerformanceMonitor();

const timer = monitor.startTimer('file-analysis');
// 执行分析操作
await analyzeFile('src/complex.ts');
timer.end();
```

##### recordCacheHit(cacheType: string, hit: boolean): void

记录缓存命中情况。

**参数:**
- `cacheType` - 缓存类型
- `hit` - 是否命中

##### getPerformanceReport(): PerformanceReport

获取性能报告。

**返回值:**
- `PerformanceReport` - 详细的性能统计报告

**示例:**
```typescript
const report = monitor.getPerformanceReport();

console.log('Performance Report:');
console.log(`- Total operations: ${report.totalOperations}`);
console.log(`- Average execution time: ${report.averageTime}ms`);
console.log(`- Cache hit rate: ${report.cacheHitRate * 100}%`);
console.log(`- Memory usage: ${report.memoryUsage.rss / 1024 / 1024}MB`);

// 获取优化建议
report.recommendations.forEach(rec => {
  console.log(`⚡ ${rec.message}`);
});
```

### 性能优化示例

```typescript
// 批量分析优化
async function analyzeBatch(files: string[]): Promise<AnalysisResult[]> {
  const engine = new AsyncRuleEngine({
    maxConcurrency: Math.min(files.length, 4), // 限制并发数
    enableCache: true,
    cacheSize: 2000
  });

  // 使用Promise.all进行并行处理
  const results = await Promise.all(
    files.map(async (file) => {
      const timer = monitor.startTimer(`analyze-${file}`);
      try {
        const result = await engine.analyzeFile(await parseFile(file));
        timer.end();
        return result;
      } catch (error) {
        timer.end();
        throw error;
      }
    })
  );

  return results;
}

// 内存使用监控
function monitorMemoryUsage(): void {
  const usage = process.memoryUsage();
  
  if (usage.heapUsed > 512 * 1024 * 1024) { // 512MB
    console.warn('⚠️ High memory usage detected:', {
      heapUsed: `${usage.heapUsed / 1024 / 1024}MB`,
      heapTotal: `${usage.heapTotal / 1024 / 1024}MB`,
      external: `${usage.external / 1024 / 1024}MB`
    });
    
    // 触发垃圾回收
    if (global.gc) {
      global.gc();
    }
  }
}
```

## 高级用法

### 自定义分析流程

```typescript
// 创建自定义分析器
class CustomAnalyzer {
  private engine: AsyncRuleEngine;
  private cache: CacheManager;
  private monitor: PerformanceMonitor;

  constructor(config: CustomAnalyzerConfig) {
    this.engine = new AsyncRuleEngine({
      maxConcurrency: config.maxConcurrency || 4,
      enableCache: true,
      cacheSize: config.cacheSize || 1000
    });
    
    this.cache = new CacheManager(config.cacheConfig);
    this.monitor = new PerformanceMonitor();
    
    this.setupCustomRules(config.customRules);
  }

  async analyzeProject(projectPath: string): Promise<ProjectAnalysis> {
    const timer = this.monitor.startTimer('project-analysis');
    
    try {
      // 1. 发现所有文件
      const files = await this.discoverFiles(projectPath);
      
      // 2. 并行分析
      const fileResults = await this.analyzeFiles(files);
      
      // 3. 聚合结果
      const projectResult = this.aggregateResults(fileResults);
      
      // 4. 生成建议
      const suggestions = this.generateSuggestions(projectResult);
      
      timer.end();
      
      return {
        ...projectResult,
        suggestions,
        performanceMetrics: this.monitor.getPerformanceReport()
      };
    } catch (error) {
      timer.end();
      throw error;
    }
  }

  private setupCustomRules(rules: CustomRule[]): void {
    rules.forEach(rule => {
      this.engine.registerRule(rule);
    });
  }

  private async analyzeFiles(files: string[]): Promise<FileAnalysis[]> {
    // 分批处理，避免内存溢出
    const batchSize = 10;
    const results: FileAnalysis[] = [];
    
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(file => this.analyzeFile(file))
      );
      results.push(...batchResults);
      
      // 监控内存使用
      this.monitorMemoryUsage();
    }
    
    return results;
  }
}
```

### 插件生态系统集成

```typescript
// 插件管理器
class PluginManager {
  private plugins: Map<string, Plugin> = new Map();
  private engine: AsyncRuleEngine;

  constructor(engine: AsyncRuleEngine) {
    this.engine = engine;
  }

  async loadPlugin(pluginConfig: PluginConfig): Promise<void> {
    const plugin = await this.resolvePlugin(pluginConfig);
    
    // 验证插件
    if (!this.validatePlugin(plugin)) {
      throw new Error(`Invalid plugin: ${pluginConfig.name}`);
    }

    // 初始化插件
    if (plugin.initialize) {
      const context: PluginContext = {
        engine: this.engine,
        config: pluginConfig.config || {},
        registerRule: (rule: Rule) => this.engine.registerRule(rule),
        registerFormatter: (formatter: Formatter) => this.registerFormatter(formatter),
        logger: this.createLogger(plugin.id)
      };
      
      await plugin.initialize(context);
    }

    this.plugins.set(plugin.id, plugin);
  }

  async loadPluginsFromConfig(config: PluginManagerConfig): Promise<void> {
    const loadPromises = config.plugins.map(pluginConfig => 
      this.loadPlugin(pluginConfig)
    );
    
    await Promise.allSettled(loadPromises);
  }

  private async resolvePlugin(config: PluginConfig): Promise<Plugin> {
    if (config.path) {
      // 从文件路径加载
      return await import(config.path);
    } else if (config.package) {
      // 从npm包加载
      return await import(config.package);
    } else {
      throw new Error('Plugin must specify either path or package');
    }
  }
}
```

### 增量分析系统

```typescript
// 增量分析管理器
class IncrementalAnalyzer {
  private baseline: Map<string, FileSnapshot> = new Map();
  private engine: AsyncRuleEngine;

  constructor(engine: AsyncRuleEngine) {
    this.engine = engine;
  }

  async analyzeIncremental(
    files: string[], 
    baselineData?: BaselineData
  ): Promise<IncrementalAnalysisResult> {
    if (baselineData) {
      this.loadBaseline(baselineData);
    }

    const changedFiles = await this.detectChangedFiles(files);
    const newFiles = await this.detectNewFiles(files);
    const deletedFiles = this.detectDeletedFiles(files);

    // 只分析变更的文件
    const analysisResults = await Promise.all([
      ...changedFiles.map(file => this.analyzeFile(file)),
      ...newFiles.map(file => this.analyzeFile(file))
    ]);

    // 更新基线
    this.updateBaseline(analysisResults);

    return {
      changedFiles: changedFiles.length,
      newFiles: newFiles.length,
      deletedFiles: deletedFiles.length,
      totalComplexityChange: this.calculateComplexityChange(),
      results: analysisResults,
      recommendations: this.generateIncrementalRecommendations()
    };
  }

  private async detectChangedFiles(files: string[]): Promise<string[]> {
    const changed: string[] = [];
    
    for (const file of files) {
      const currentSnapshot = await this.createFileSnapshot(file);
      const baselineSnapshot = this.baseline.get(file);
      
      if (!baselineSnapshot || 
          currentSnapshot.hash !== baselineSnapshot.hash) {
        changed.push(file);
      }
    }
    
    return changed;
  }

  private async createFileSnapshot(filePath: string): Promise<FileSnapshot> {
    const content = await fs.readFile(filePath, 'utf-8');
    const stats = await fs.stat(filePath);
    
    return {
      filePath,
      hash: this.computeHash(content),
      size: stats.size,
      mtime: stats.mtime,
      complexity: 0 // 将在分析后更新
    };
  }
}
```

### 分布式分析

```typescript
// 分布式分析协调器
class DistributedAnalyzer {
  private workers: Worker[] = [];
  private workQueue: AnalysisTask[] = [];

  constructor(workerCount: number = os.cpus().length) {
    this.initializeWorkers(workerCount);
  }

  async analyzeProject(projectPath: string): Promise<ProjectAnalysis> {
    const files = await this.discoverFiles(projectPath);
    
    // 将文件分配给工作线程
    const tasks = files.map(file => ({
      id: generateId(),
      type: 'analyze-file' as const,
      filePath: file
    }));

    // 分发任务
    const results = await this.distributeWork(tasks);
    
    // 聚合结果
    return this.aggregateDistributedResults(results);
  }

  private async distributeWork(tasks: AnalysisTask[]): Promise<AnalysisResult[]> {
    return new Promise((resolve, reject) => {
      const results: AnalysisResult[] = [];
      let completedTasks = 0;

      const onTaskComplete = (result: AnalysisResult) => {
        results.push(result);
        completedTasks++;
        
        if (completedTasks === tasks.length) {
          resolve(results);
        }
      };

      const onTaskError = (error: Error) => {
        reject(error);
      };

      // 分发任务到工作线程
      tasks.forEach((task, index) => {
        const worker = this.workers[index % this.workers.length];
        worker.postMessage({ task });
        
        worker.once('message', ({ result, error }) => {
          if (error) {
            onTaskError(error);
          } else {
            onTaskComplete(result);
          }
        });
      });
    });
  }
}