# CLI 测试编写指南和最佳实践

## 概述

本指南提供了使用新的 CLI 测试框架进行测试编写的完整指导。我们已成功从自定义 `TestUtils.executeCommand` 迁移到功能更强大的 `CLITestingUtils`，提供了更好的测试体验和功能覆盖度。

## 快速开始

### 基础 CLI 测试

```typescript
import { CLITestingUtils } from '../helpers/cli-testing-utils';

describe('Basic CLI Tests', () => {
  test('should analyze TypeScript file', async () => {
    // 执行 CLI 命令
    const result = await CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'test.ts']);
    
    // 验证执行成功
    const exitCode = await result.waitForExit();
    expect(exitCode).toBe(0);
    
    // 验证输出内容
    expect(await result.findByText('Analysis completed')).toBe(true);
    
    // 清理资源
    await CLITestingUtils.cleanup(result);
  });
});
```

### 使用 TestUtils 增强方法

```typescript
import { TestUtils } from '../helpers/test-utils';

describe('Enhanced CLI Tests', () => {
  test('should handle JSON output validation', async () => {
    const result = await TestUtils.executeCLITest('cognitive-complexity', ['--format', 'json']);
    
    // 验证成功执行
    await TestUtils.expectCLISuccess(result);
    
    // 验证 JSON 输出格式
    await TestUtils.expectCLIJSONOutput(result, {
      summary: {
        totalComplexity: expect.any(Number),
        filesAnalyzed: expect.any(Number)
      }
    });
    
    await CLITestingUtils.cleanup(result);
  });
});
```

## 核心测试模式

### 1. 输出验证模式

#### 基础文本验证

```typescript
import { OutputValidator } from '../helpers/output-validator';

// 验证输出包含特定文本
const validation = await OutputValidator.assertOutputContains(
  result.stdout,
  ['Analysis completed', 'Functions processed: 15'],
  { caseSensitive: false }
);
expect(validation.passed).toBe(true);
```

#### 正则表达式验证

```typescript
// 验证输出匹配正则表达式
const validation = await OutputValidator.assertOutputMatches(
  result.stdout,
  /Analysis took (\d+\.\d+) seconds/,
  { debug: true }
);
expect(validation.passed).toBe(true);
```

#### 结构化输出验证

```typescript
// JSON 格式验证
await TestUtils.expectCLIJSONOutput(result, {
  summary: {
    totalComplexity: expect.any(Number),
    averageComplexity: expect.any(Number)
  },
  results: expect.any(Array)
});

// 表格格式验证
await TestUtils.expectCLITableOutput(result, 
  ['File', 'Functions', 'Avg Complexity', 'Max Complexity']
);
```

### 2. 交互式测试模式

```typescript
import { InteractiveTestHelper, InteractiveSteps } from '../helpers/interactive-test-helper';

test('should handle interactive configuration', async () => {
  const result = await CLITestingUtils.renderCLI('cognitive-complexity', ['init']);
  
  // 定义交互流程
  const flow = [
    InteractiveSteps.input(/Enter project name:/, 'my-project'),
    InteractiveSteps.select(/Select output format:/, '1'),
    InteractiveSteps.confirm(/Save configuration\?/, true),
  ];
  
  // 执行交互流程
  const interactiveResult = await InteractiveTestHelper.simulateUserFlow(
    result, 
    flow, 
    { debug: true }
  );
  
  // 验证交互结果
  expect(interactiveResult.success).toBe(true);
  expect(interactiveResult.completedSteps).toBe(3);
  
  await CLITestingUtils.cleanup(result);
});
```

### 3. 性能测试模式

```typescript
import { PerformanceTestUtils } from '../helpers/test-utils';

test('should meet performance benchmarks', async () => {
  // 运行性能基准测试
  const performanceResult = await PerformanceTestUtils.runCLIBenchmark(
    () => CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'large-project/']),
    'cli-complex', // 使用预定义的复杂任务基准
    'Large Project Analysis'
  );
  
  expect(performanceResult.passed).toBe(true);
  expect(performanceResult.violations).toHaveLength(0);
});
```

### 4. 并发测试模式

```typescript
test('should handle concurrent analysis', async () => {
  const concurrencyResult = await PerformanceTestUtils.runConcurrencyTest(
    () => CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'test-file.ts']),
    { concurrentCount: 5, testDuration: 10000 },
    'Concurrent Analysis Test'
  );
  
  expect(concurrencyResult.summary.successRate).toBeGreaterThan(90);
  expect(concurrencyResult.summary.averageExecutionTime).toBeLessThan(5000);
});
```

## 高级功能

### 模糊匹配

```typescript
// 模糊匹配输出内容
const fuzzyResult = await OutputValidator.fuzzyMatch(
  result.stdout,
  'complexity', // 目标文本
  0.8 // 相似度阈值
);
expect(fuzzyResult.passed).toBe(true);
```

### 输出差异对比

```typescript
const expectedOutput = "Analysis completed\nFiles processed: 5";
const actualOutput = result.stdout;

const diffResult = await OutputValidator.compareOutputs(expectedOutput, actualOutput);
if (!diffResult.identical) {
  console.log(OutputDebugHelper.generateDiffReport(diffResult, 'CLI Output Comparison'));
}
```

### 多行结构验证

```typescript
const structureValidation = await OutputValidator.validateMultilineStructure(
  result.stdout,
  {
    sections: [
      {
        name: 'Summary Section',
        patterns: [/=== Analysis Summary ===/, /Total files: \d+/],
        required: true,
        order: 1
      },
      {
        name: 'Results Section', 
        patterns: [/=== Detailed Results ===/, /Function:/],
        required: true,
        order: 2
      }
    ],
    minLines: 5,
    maxLines: 100
  },
  { preserveWhitespace: false, ignoreEmptyLines: true }
);
```

## 最佳实践

### 1. 资源管理

**始终清理测试资源：**

```typescript
describe('CLI Tests', () => {
  let cliResult: CLITestResult;
  
  afterEach(async () => {
    if (cliResult) {
      await CLITestingUtils.cleanup(cliResult);
    }
  });
  
  test('example test', async () => {
    cliResult = await CLITestingUtils.renderCLI('command', ['args']);
    // 测试逻辑...
  });
});
```

**或使用 try-finally 模式：**

```typescript
test('should handle error cases', async () => {
  const result = await CLITestingUtils.renderCLI('cognitive-complexity', ['invalid-command']);
  
  try {
    await TestUtils.expectCLIFailure(result, 'Unknown command');
  } finally {
    await CLITestingUtils.cleanup(result);
  }
});
```

### 2. 错误处理

**处理超时和进程错误：**

```typescript
test('should handle timeout gracefully', async () => {
  const result = await CLITestingUtils.renderCLI('slow-command', [], {
    timeout: 5000,
    maxBuffer: 1024 * 1024
  });
  
  try {
    await result.waitForExit(3000);
  } catch (error) {
    expect(error).toBeInstanceOf(TestTimeoutError);
  } finally {
    await CLITestingUtils.cleanup(result);
  }
});
```

### 3. 调试和诊断

**启用调试输出：**

```typescript
// 在测试中启用调试
const result = await CLITestingUtils.renderCLI('command', ['args'], {
  debug: true
});

// 手动调试输出
InteractiveTestHelper.debugOutput(result, 'Current CLI State');

// 使用输出分析器
const analysis = OutputDebugHelper.analyzeOutput(result.stdout);
console.log('Output Analysis:', analysis);
```

### 4. 测试数据管理

**使用模拟数据生成器：**

```typescript
import { MockDataGenerator } from '../helpers/test-utils';

test('should handle large datasets', async () => {
  // 生成测试数据
  const testScenario = MockDataGenerator.generateCLITestScenario({
    command: 'cognitive-complexity',
    args: ['scan', '--format', 'json']
  });
  
  const result = await TestUtils.runCLIScenario(testScenario);
  expect(result.success).toBe(true);
});
```

### 5. 跨平台兼容性

**处理平台差异：**

```typescript
import { platform } from 'os';

test('should work across platforms', async () => {
  const command = platform() === 'win32' ? 'cognitive-complexity.exe' : 'cognitive-complexity';
  const result = await CLITestingUtils.renderCLI(command, ['--version']);
  
  await TestUtils.expectCLISuccess(result);
  await CLITestingUtils.cleanup(result);
});
```

## 常见问题和解决方案

### 问题 1: 进程未正确清理

**症状：** 测试完成后仍有子进程运行

**解决方案：**
```typescript
// 在测试套件结束时强制清理所有进程
afterAll(async () => {
  await CLITestingUtils.cleanupAll();
});

// 或在每个测试中使用 try-finally
test('example', async () => {
  let result;
  try {
    result = await CLITestingUtils.renderCLI('command', []);
    // 测试逻辑
  } finally {
    if (result) await CLITestingUtils.cleanup(result);
  }
});
```

### 问题 2: 输出验证不稳定

**症状：** 同样的测试有时通过，有时失败

**解决方案：**
```typescript
// 增加等待时间和稳定性检查
await result.waitForOutput(/Expected pattern/, 10000);
await new Promise(resolve => setTimeout(resolve, 200)); // 稳定延迟

// 使用模糊匹配增加容错性
const fuzzyValidation = await OutputValidator.fuzzyMatch(
  output, 
  'expected text', 
  0.8 // 降低匹配阈值
);
```

### 问题 3: 交互式测试超时

**症状：** 交互式测试在等待提示时超时

**解决方案：**
```typescript
const flow = [
  {
    waitFor: /Enter your choice:/,
    input: '1',
    timeout: 15000, // 增加单步超时
    delay: 500      // 增加输入前延迟
  }
];

await InteractiveTestHelper.simulateUserFlow(result, flow, {
  defaultTimeout: 20000,    // 增加默认超时
  stabilizeDelay: 300,      // 增加稳定延迟
  debug: true               // 启用调试输出
});
```

## 迁移检查清单

从旧的 `TestUtils.executeCommand` 迁移到新框架时，请检查以下项目：

- [ ] 将 `TestUtils.executeCommand` 替换为 `CLITestingUtils.renderCLI`
- [ ] 更新输出验证逻辑，使用 `OutputValidator` 方法
- [ ] 添加适当的资源清理（`CLITestingUtils.cleanup`）
- [ ] 更新错误处理，使用新的错误类型
- [ ] 考虑添加交互式测试场景
- [ ] 更新性能测试，使用 `PerformanceTestUtils`
- [ ] 验证并发测试的稳定性
- [ ] 添加调试和诊断工具的使用

## 示例测试套件

```typescript
import { CLITestingUtils, CLITestResult } from '../helpers/cli-testing-utils';
import { TestUtils } from '../helpers/test-utils';
import { OutputValidator } from '../helpers/output-validator';
import { InteractiveTestHelper, InteractiveSteps } from '../helpers/interactive-test-helper';

describe('Cognitive Complexity CLI', () => {
  let cliResult: CLITestResult | undefined;
  
  afterEach(async () => {
    if (cliResult) {
      await CLITestingUtils.cleanup(cliResult);
      cliResult = undefined;
    }
  });
  
  afterAll(async () => {
    await CLITestingUtils.cleanupAll();
  });

  describe('Basic Functionality', () => {
    test('should display version information', async () => {
      cliResult = await CLITestingUtils.renderCLI('cognitive-complexity', ['--version']);
      await TestUtils.expectCLISuccess(cliResult);
      expect(await cliResult.findByText(/\d+\.\d+\.\d+/)).toBe(true);
    });

    test('should analyze single file', async () => {
      cliResult = await TestUtils.executeCLITest('cognitive-complexity', ['scan', 'test.ts']);
      await TestUtils.expectCLISuccess(cliResult);
      await TestUtils.expectCLIOutput(cliResult, ['Analysis completed']);
    });
  });

  describe('Output Formats', () => {
    test('should generate JSON output', async () => {
      cliResult = await CLITestingUtils.renderCLI('cognitive-complexity', ['--format', 'json']);
      await TestUtils.expectCLIJSONOutput(cliResult, {
        summary: expect.objectContaining({
          totalComplexity: expect.any(Number)
        })
      });
    });

    test('should generate table output', async () => {
      cliResult = await CLITestingUtils.renderCLI('cognitive-complexity', ['--format', 'table']);
      await TestUtils.expectCLITableOutput(cliResult, ['File', 'Functions', 'Complexity']);
    });
  });

  describe('Interactive Features', () => {
    test('should handle interactive configuration', async () => {
      cliResult = await CLITestingUtils.renderCLI('cognitive-complexity', ['init']);
      
      const interactiveResult = await InteractiveTestHelper.simulateUserFlow(cliResult, [
        InteractiveSteps.input(/Project name:/, 'test-project'),
        InteractiveSteps.select(/Output format:/, '2'),
        InteractiveSteps.confirm(/Save configuration\?/, true)
      ]);
      
      expect(interactiveResult.success).toBe(true);
    });
  });

  describe('Performance', () => {
    test('should meet performance benchmarks', async () => {
      const performanceResult = await PerformanceTestUtils.runCLIBenchmark(
        () => CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'src/']),
        'cli-basic'
      );
      
      expect(performanceResult.passed).toBe(true);
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid files gracefully', async () => {
      cliResult = await CLITestingUtils.renderCLI('cognitive-complexity', ['scan', 'non-existent.ts']);
      await TestUtils.expectCLIFailure(cliResult, 'File not found');
    });
  });
});
```

这个指南提供了从基础到高级的完整测试编写方法，帮助开发者充分利用新的 CLI 测试框架的强大功能。