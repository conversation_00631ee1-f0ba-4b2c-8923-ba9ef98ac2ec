# 详细上下文功能使用指南

## 概述

认知复杂度分析工具的详细上下文功能为开发者提供了深入的代码分析体验。通过 `--details` 和 `--show-context` 参数，您可以获得比基础分析更详细的信息，包括每个复杂度增量的具体位置、原因和代码上下文。

## 功能特性

### 核心功能
- **详细步骤分析**: 显示每个复杂度增量的具体原因
- **代码上下文显示**: 显示相关代码片段和行号
- **智能过滤**: 自动过滤不重要的细节，突出关键信息
- **多种输出格式**: 支持文本、JSON、HTML格式
- **性能优化**: 大文件分析的缓存和流式处理

### 支持的分析类型
- TypeScript/JavaScript 函数和方法
- 条件语句 (if/else, switch)
- 循环结构 (for, while, do-while)
- 逻辑运算符 (&&, ||)
- 三元运算符
- Try-catch 语句
- JSX 事件处理器
- React Hook 复杂度

## 基本使用

### 启用详细分析

```bash
# 基础详细分析
cognitive-complexity --details src/myfile.ts

# 详细分析 + 代码上下文
cognitive-complexity --details --show-context src/myfile.ts

# 显示所有上下文（跳过智能过滤）
cognitive-complexity --details --show-all-context src/myfile.ts
```

### 输出格式

#### 文本格式（默认）
```bash
cognitive-complexity --details --show-context src/example.ts
```

输出示例：
```
Function: complexFunction (Line: 5-25) - Complexity: 8

Details:
  Line 7:   if (condition1) {           +1 (nesting=0) [if-statement]
  Line 9:     if (condition2) {         +2 (nesting=1) [nested-if]
  Line 11:      return "result1";
  Line 12:    } else if (condition3) {  +1 (nesting=1) [else-if]
  Line 14:      return "result2";
  Line 15:    } else {                  +1 (nesting=1) [else]
  Line 17:      return "result3";
  Line 18:    }
  Line 19:  } else {                    +1 (nesting=0) [else]
  Line 21:    return "default";
  Line 22:  }

Context:
   5 | export function complexFunction(input: any): string {
   6 |   let result = null;
   7 | > if (condition1) {
   8 |     // 复杂的业务逻辑
   9 | >   if (condition2) {
  10 |       return "result1";
```

#### JSON 格式
```bash
cognitive-complexity --details --show-context --output json src/example.ts
```

输出示例：
```json
[
  {
    "name": "complexFunction",
    "complexity": 8,
    "startLine": 5,
    "endLine": 25,
    "details": [
      {
        "line": 7,
        "column": 3,
        "increment": 1,
        "cumulative": 2,
        "ruleId": "if-statement",
        "description": "if statement",
        "nestingLevel": 0,
        "context": "  if (condition1) {",
        "span": { "start": 156, "end": 171 }
      }
    ],
    "filePath": "src/example.ts"
  }
]
```

#### HTML 格式
```bash
cognitive-complexity --details --show-context --output html --output-file report.html src/
```

生成交互式 HTML 报告，包含：
- 语法高亮的代码显示
- 可点击的复杂度热点
- 过滤和搜索功能
- 响应式设计

## 高级用法

### 智能上下文过滤

工具自动过滤低重要性的复杂度增量，只显示关键信息：

```bash
# 默认智能过滤（推荐）
cognitive-complexity --details --show-context src/myfile.ts

# 显示所有详情（包括简单的增量）
cognitive-complexity --details --show-all-context src/myfile.ts

# 自定义过滤阈值
cognitive-complexity --details --show-context --context-lines 5 src/myfile.ts
```

### 配置文件集成

创建 `.complexityrc.json` 配置文件：

```json
{
  "threshold": 10,
  "enableDetails": true,
  "showContext": true,
  "contextLines": 3,
  "smartFiltering": true,
  "outputFormat": "text"
}
```

### 批量分析

```bash
# 分析整个项目
cognitive-complexity --details --show-context "src/**/*.ts"

# 分析多个文件
cognitive-complexity --details --show-context src/utils/*.ts src/components/*.tsx

# 排除测试文件
cognitive-complexity --details --show-context "src/**/*.ts" --exclude "**/*.test.ts"
```

### 与 CI/CD 集成

#### GitHub Actions 示例

```yaml
name: Code Complexity Check
on: [push, pull_request]

jobs:
  complexity:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm install -g cognitive-complexity
      
      - name: Check complexity with details
        run: |
          cognitive-complexity \
            --details \
            --show-context \
            --output json \
            --output-file complexity-report.json \
            "src/**/*.ts"
      
      - name: Upload complexity report
        uses: actions/upload-artifact@v3
        with:
          name: complexity-report
          path: complexity-report.json
```

#### Jenkins Pipeline 示例

```groovy
pipeline {
    agent any
    
    stages {
        stage('Complexity Analysis') {
            steps {
                sh '''
                    npm install -g cognitive-complexity
                    cognitive-complexity \
                        --details \
                        --show-context \
                        --threshold 15 \
                        --output html \
                        --output-file complexity-report.html \
                        "src/**/*.ts"
                '''
                
                publishHTML([
                    allowMissing: false,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: '.',
                    reportFiles: 'complexity-report.html',
                    reportName: 'Complexity Report'
                ])
            }
        }
    }
}
```

## 参数详细说明

### 基础参数

| 参数 | 描述 | 默认值 | 示例 |
|-----|------|--------|------|
| `--details` | 启用详细分析模式 | false | `--details` |
| `--show-context` | 显示代码上下文（需要 --details） | false | `--show-context` |
| `--show-all-context` | 显示所有上下文，跳过智能过滤 | false | `--show-all-context` |
| `--context-lines` | 上下文显示的行数 | 3 | `--context-lines 5` |

### 输出控制

| 参数 | 描述 | 可选值 | 示例 |
|-----|------|--------|------|
| `--output` | 输出格式 | text, json, html | `--output json` |
| `--output-file` | 输出文件路径 | - | `--output-file report.html` |
| `--no-highlight` | 禁用语法高亮 | - | `--no-highlight` |

### 过滤控制

| 参数 | 描述 | 默认值 | 示例 |
|-----|------|--------|------|
| `--threshold` | 复杂度阈值 | 10 | `--threshold 15` |
| `--min-complexity-increment` | 最小显示的复杂度增量 | 1 | `--min-complexity-increment 2` |
| `--max-context-items` | 最大上下文项目数 | 50 | `--max-context-items 100` |

## 实际应用场景

### 1. 代码审查

在代码审查过程中使用详细分析：

```bash
# 分析 PR 中的变更文件
git diff --name-only origin/main...HEAD | grep '\\.ts$' | xargs cognitive-complexity --details --show-context
```

### 2. 重构指导

识别需要重构的复杂函数：

```bash
# 找出复杂度超过阈值的函数详情
cognitive-complexity --details --show-context --threshold 15 "src/**/*.ts" | grep -A 20 "Complexity: [2-9][0-9]"
```

### 3. 新手培训

帮助新开发者理解代码复杂度：

```bash
# 为新手显示详细的复杂度分析
cognitive-complexity --details --show-all-context --context-lines 5 src/tutorial/*.ts
```

### 4. 技术债务评估

评估项目的技术债务状况：

```bash
# 生成完整的项目复杂度报告
cognitive-complexity \
  --details \
  --show-context \
  --output html \
  --output-file debt-assessment.html \
  "src/**/*.{ts,tsx,js,jsx}"
```

## 性能优化建议

### 大型项目处理

对于大型项目，建议使用以下策略：

```bash
# 启用缓存以提高重复分析速度
export COMPLEXITY_CACHE_ENABLED=true

# 限制并发分析的文件数量
cognitive-complexity --details --show-context --max-concurrent 4 "src/**/*.ts"

# 分批处理大量文件
find src -name "*.ts" | xargs -n 10 cognitive-complexity --details --show-context
```

### 内存使用优化

```bash
# 为大文件启用流式处理
export COMPLEXITY_STREAMING=true

# 限制上下文缓存大小
export COMPLEXITY_MAX_CACHE_SIZE=100MB

# 定期清理缓存
cognitive-complexity --clear-cache
```

## 故障排除

### 常见问题

#### 1. `--show-context` 不起作用

**问题**: 使用 `--show-context` 但没有显示代码上下文

**解决方案**: 确保同时使用 `--details` 参数

```bash
# 错误用法
cognitive-complexity --show-context src/file.ts

# 正确用法
cognitive-complexity --details --show-context src/file.ts
```

#### 2. 输出过于详细

**问题**: 详细输出包含太多低价值信息

**解决方案**: 使用智能过滤或提高阈值

```bash
# 使用默认智能过滤
cognitive-complexity --details --show-context src/file.ts

# 或者提高复杂度阈值
cognitive-complexity --details --show-context --threshold 5 src/file.ts
```

#### 3. 代码框架生成失败

**问题**: 某些文件的代码上下文无法显示

**解决方案**: 检查文件编码和权限

```bash
# 检查文件编码
file -I src/problematic-file.ts

# 确保文件可读
ls -la src/problematic-file.ts

# 使用降级模式
cognitive-complexity --details --show-context --fallback-context src/problematic-file.ts
```

#### 4. 性能问题

**问题**: 大文件分析速度慢

**解决方案**: 启用缓存和优化设置

```bash
# 启用所有性能优化
export COMPLEXITY_CACHE_ENABLED=true
export COMPLEXITY_STREAMING=true
export COMPLEXITY_PARALLEL_PROCESSING=true

cognitive-complexity --details --show-context "src/**/*.ts"
```

### 调试模式

启用调试模式获取更多信息：

```bash
# 启用详细日志
export DEBUG=complexity:*
cognitive-complexity --details --show-context src/file.ts

# 启用性能监控
export COMPLEXITY_PROFILE=true
cognitive-complexity --details --show-context src/file.ts
```

## 最佳实践

### 1. 团队协作

- 在团队中统一使用配置文件
- 将复杂度报告集成到 PR 流程
- 定期审查高复杂度函数

### 2. 持续改进

- 设置合适的复杂度阈值
- 监控项目复杂度趋势
- 将重构纳入开发计划

### 3. 工具集成

- 与 IDE 扩展集成
- 配置预提交钩子
- 集成到构建流程

## 扩展功能

### 自定义规则

可以通过插件系统添加自定义复杂度规则：

```javascript
// custom-rule.js
module.exports = {
  name: 'custom-complexity-rule',
  visitor: {
    // 自定义访问器逻辑
  }
};
```

### API 使用

也可以通过编程方式使用详细分析功能：

```typescript
import { ComplexityCalculator } from 'cognitive-complexity';

const calculator = new ComplexityCalculator({
  enableDetails: true,
  showContext: true
});

const result = await calculator.analyzeFile('src/example.ts');
console.log(result.details);
```

## 总结

详细上下文功能为认知复杂度分析提供了强大的深入分析能力。通过合理使用这些功能，可以：

- 更准确地识别代码中的复杂性问题
- 为重构提供具体的指导
- 提高代码审查的效率
- 帮助团队维护代码质量

记住根据项目规模和团队需求调整参数设置，以获得最佳的分析体验。