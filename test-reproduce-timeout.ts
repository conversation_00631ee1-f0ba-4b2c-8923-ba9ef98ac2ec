#!/usr/bin/env bun

/**
 * 最终验证：重现原始问题的最小化版本
 */

import { analyzeFile } from './src/index';
import { TextFormatter } from './src/formatters/text';
import { writeFileSync, unlinkSync } from 'fs';

async function reproduceOriginalIssue() {
  console.log('🧪 重现原始超时问题...\n');
  
  const testCode = `
export function simpleFunction() {
  return 'hello';
}

export function functionWithIf(x: number) {
  if (x > 0) {
    return x * 2;
  }
  return 0;
}
`.trim();

  const testFilePath = `/tmp/test-complexity-${Date.now()}.ts`;
  writeFileSync(testFilePath, testCode);

  try {
    console.log('📊 活跃handles（开始）:', process._getActiveHandles().length);
    
    // 分析文件（启用详细信息）
    console.log('🧠 分析文件（enableDetails: true）...');
    const fileResult = await analyzeFile(testFilePath, { enableDetails: true });
    
    console.log('📊 活跃handles（分析后）:', process._getActiveHandles().length);
    
    // 构造AnalysisResult
    const mockAnalysisResult = {
      summary: {
        filesAnalyzed: 1,
        functionsAnalyzed: fileResult.functions.length,
        totalComplexity: fileResult.complexity,
        averageComplexity: fileResult.averageComplexity,
        highComplexityFunctions: fileResult.functions.filter(f => f.complexity > 15).length
      },
      results: [fileResult]
    };

    const formatter = new TextFormatter();

    console.log('🎨 格式化（showDetails: true）...');
    const detailOutput = await formatter.format(mockAnalysisResult, true);
    
    console.log('📊 活跃handles（格式化后）:', process._getActiveHandles().length);
    console.log('🔄 活跃requests（格式化后）:', process._getActiveRequests().length);
    
    console.log('\n🎯 如果这里超时，说明问题已重现');
    console.log('📝 输出长度:', detailOutput.length, '字符');
    
    // 等待一段时间观察
    setTimeout(() => {
      console.log('\n📊 5秒后活跃handles:', process._getActiveHandles().length);
      console.log('🔄 5秒后活跃requests:', process._getActiveRequests().length);
      
      // 如果有活跃handles，尝试找出是什么
      const handles = process._getActiveHandles();
      if (handles.length > 0) {
        console.log('⚠️ 检测到活跃handles:', handles.map(h => h.constructor.name));
      }
      
      console.log('✅ 强制退出以避免无限等待');
      process.exit(0);
    }, 5000);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  } finally {
    try {
      unlinkSync(testFilePath);
    } catch (e) {
      // ignore cleanup errors
    }
  }
}

reproduceOriginalIssue();