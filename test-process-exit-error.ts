#!/usr/bin/env bun
/**
 * 异常情况下进程退出验证脚本
 * 验证在分析错误或异常情况下，进程能在5秒内自然退出
 */

import { ComplexityCalculator } from './src/core/calculator';
import { CalculatorFactory } from './src/core/calculator-factory';
import type { CalculatorOptions } from './src/engine/types';

async function testErrorProcessExit() {
  console.log('🧪 测试异常情况下进程退出');
  
  const startTime = Date.now();
  let calculator: ComplexityCalculator | null = null;
  
  try {
    // 创建调试配置
    const debugOptions: CalculatorOptions = {
      enableMonitoring: true,
      debugMode: true,
      quiet: false,
      ruleEngineConfig: {
        maxRuleConcurrency: 2,
        enableRuleCaching: true,
        ruleDebugMode: true,
      }
    };
    
    console.log('📋 创建调试配置的工厂和计算器...');
    const factory = new CalculatorFactory(debugOptions);
    calculator = new ComplexityCalculator({
      enableDebugLog: true,
      quiet: false
    }, factory);
    
    // 测试场景1: 无效语法
    console.log('🔍 测试场景1: 无效语法处理');
    try {
      const invalidCode = `
        function broken( {
          invalid syntax here {{{}}}
          return undefined;
        }
      `;
      
      const results1 = await calculator.calculateCode(invalidCode, 'invalid-syntax.ts');
      console.log(`📊 无效语法结果: ${results1.length} 个函数 (应该为0)`);
    } catch (syntaxError) {
      console.log('⚠️ 语法错误被正确捕获:', syntaxError.message);
    }
    
    // 测试场景2: 空文件
    console.log('🔍 测试场景2: 空文件处理');
    const emptyResults = await calculator.calculateCode('', 'empty.ts');
    console.log(`📊 空文件结果: ${emptyResults.length} 个函数 (应该为0)`);
    
    // 测试场景3: 只有注释
    console.log('🔍 测试场景3: 仅注释文件处理');
    const commentOnlyCode = `
      // 这是单行注释
      /* 这是
         多行注释 */
      /** 
       * JSDoc 注释
       */
    `;
    const commentResults = await calculator.calculateCode(commentOnlyCode, 'comments-only.ts');
    console.log(`📊 仅注释结果: ${commentResults.length} 个函数 (应该为0)`);
    
    // 测试场景4: 复杂但有效的代码（确保正常情况下也能工作）
    console.log('🔍 测试场景4: 复杂有效代码处理');
    const complexValidCode = `
      function errorProne(data) {
        try {
          if (data && data.length > 0) {
            for (const item of data) {
              if (item.valid) {
                while (item.processing) {
                  if (item.ready && item.status === 'ok') {
                    return item.result;
                  } else if (item.error) {
                    throw new Error('Processing failed');
                  }
                }
              }
            }
          }
        } catch (error) {
          console.error('Error in processing:', error);
          return null;
        }
        return [];
      }
    `;
    
    const validResults = await calculator.calculateCode(complexValidCode, 'complex-valid.ts');
    console.log(`📊 复杂有效代码结果: ${validResults.length} 个函数，复杂度: ${validResults[0]?.complexity || 0}`);
    
    // 测试场景5: 不存在的文件分析
    console.log('🔍 测试场景5: 不存在文件处理');
    try {
      await calculator.calculateFile('/non/existent/file.ts');
      console.log('⚠️ 不存在的文件应该抛出错误');
    } catch (fileError) {
      console.log('✅ 文件不存在错误被正确处理:', fileError.message);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`⏱️ 错误处理测试耗时: ${duration}ms`);
    console.log('🎉 异常处理测试完成，正在清理资源...');
    
    // 显式清理资源
    await calculator.dispose();
    calculator = null;
    
    console.log('✨ 资源清理完成，进程应该自动退出');
    
  } catch (error) {
    console.error('❌ 异常处理测试失败:', error);
    
    // 确保资源清理
    if (calculator) {
      try {
        await calculator.dispose();
      } catch (disposeError) {
        console.error('⚠️ 资源清理失败:', disposeError);
      }
    }
    
    // 对于测试脚本，这不应该算作失败
    console.log('⚠️ 测试过程中遇到预期错误，这是正常的');
  }
}

// 添加进程退出检测
const processStartTime = Date.now();

process.on('beforeExit', () => {
  const exitTime = Date.now() - processStartTime;
  console.log(`🚪 进程即将退出，总运行时间: ${exitTime}ms`);
  
  if (exitTime > 5000) {
    console.warn('⚠️ 进程运行时间超过5秒，可能存在资源泄漏');
  } else {
    console.log('✅ 进程在合理时间内退出');
  }
});

// 运行测试
testErrorProcessExit().catch(error => {
  console.error('💥 异常测试执行失败:', error);
  process.exit(1);
});