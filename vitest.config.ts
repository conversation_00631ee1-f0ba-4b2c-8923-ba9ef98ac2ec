import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    clearMocks: true,
    // 使用 Node.js 环境，符合双运行时策略
    environment: 'node',

    // 测试超时配置 - CLI集成测试需要更长时间
    testTimeout: 10000, // 10秒超时

    // 测试文件匹配模式
    include: ['src/__test__/**/*.test.ts'],
    exclude: ['node_modules', 'dist'],

    // 覆盖率配置
    coverage: {
      provider: 'v8',
      include: ['src/**/*.ts'],
      exclude: ['src/__test__/**', 'src/**/*.test.ts', 'src/**/*.d.ts', 'dist/**'],
      reporter: ['text', 'html', 'json', 'lcov'],
      // 覆盖率阈值配置 - 核心算法模块要求100%覆盖率
      thresholds: {
        // 全局阈值
        functions: 85,
        lines: 80,
        branches: 75,
        statements: 80,
        // 针对核心模块的严格要求
        'src/core/**': {
          functions: 100,
          lines: 100,
          branches: 95,
          statements: 100,
        },
        // 针对计算器模块的最严格要求
        'src/core/calculator.ts': {
          functions: 100,
          lines: 100,
          branches: 100,
          statements: 100,
        },
      },
      // 覆盖率报告输出目录
      reportsDirectory: './coverage',
    },

    // 全局设置
    globals: true,

    // 并发测试
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
      },
    },
  },

  resolve: {
    alias: {
      // 与 tsconfig.json 保持一致的路径别名
      '@': resolve(__dirname, './src'),
    },
  },
});
