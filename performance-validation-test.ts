import { getDefaultValidationService } from './src/utils/concurrent-validation-service';
import type { CLIOptions } from './src/config/types';

// 性能基准测试
async function runPerformanceBenchmark() {
  console.log('=== Parameter Validation Performance Benchmark ===\n');

  const service = getDefaultValidationService();
  
  // 测试场景：复杂参数组合
  const complexOptions: CLIOptions = {
    paths: ['.', './src', './test'],
    debug: true,
    debugLevel: 'trace',
    debugOutput: './debug.log',
    visualReport: true,
    enableBreakpoints: true,
    breakOnRule: ['complexity-rule', 'jsx-rule'],
    breakOnComplexity: 15,
    stepByStep: true,
    ui: true,
    open: true,
    details: true,
    showContext: true,
    showAllContext: false,
    maxContextItems: 10,
    minComplexityIncrement: 2,
    outputDir: './reports',
    format: 'html'
  };

  // 热身运行
  for (let i = 0; i < 5; i++) {
    await service.validateConcurrently(complexOptions);
  }

  // 性能测试
  const iterations = 100;
  const times: number[] = [];
  
  console.log(`执行 ${iterations} 次验证测试...\n`);
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    const result = await service.validateConcurrently(complexOptions);
    const duration = performance.now() - start;
    
    times.push(duration);
    
    if (i % 20 === 0) {
      console.log(`进度: ${i + 1}/${iterations} - 当前验证时间: ${duration.toFixed(2)}ms`);
    }
  }

  // 统计分析
  const avgTime = times.reduce((a, b) => a + b) / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);
  const p50 = times.sort((a, b) => a - b)[Math.floor(times.length * 0.5)];
  const p95 = times.sort((a, b) => a - b)[Math.floor(times.length * 0.95)];
  const p99 = times.sort((a, b) => a - b)[Math.floor(times.length * 0.99)];

  console.log('\n=== 性能测试结果 ===');
  console.log(`平均时间: ${avgTime.toFixed(2)}ms`);
  console.log(`最小时间: ${minTime.toFixed(2)}ms`);
  console.log(`最大时间: ${maxTime.toFixed(2)}ms`);
  console.log(`P50 (中位数): ${p50.toFixed(2)}ms`);
  console.log(`P95: ${p95.toFixed(2)}ms`);
  console.log(`P99: ${p99.toFixed(2)}ms`);
  
  // 性能要求验证
  const requirementMet = avgTime < 50 && p95 < 50;
  console.log(`\n=== 性能要求验证 ===`);
  console.log(`要求: 平均时间 < 50ms, P95 < 50ms`);
  console.log(`结果: ${requirementMet ? '✅ 通过' : '❌ 未通过'}`);
  
  if (!requirementMet) {
    console.log(`⚠️  性能优化建议:`);
    if (avgTime >= 50) console.log(`   - 平均时间 ${avgTime.toFixed(2)}ms 超过 50ms 要求`);
    if (p95 >= 50) console.log(`   - P95 时间 ${p95.toFixed(2)}ms 超过 50ms 要求`);
  }

  // 内存使用测试
  console.log(`\n=== 内存使用测试 ===`);
  const memStart = process.memoryUsage();
  
  // 连续执行验证
  for (let i = 0; i < 1000; i++) {
    await service.validateConcurrently(complexOptions);
  }
  
  const memEnd = process.memoryUsage();
  const memIncrease = (memEnd.heapUsed - memStart.heapUsed) / 1024 / 1024;
  
  console.log(`内存增长: ${memIncrease.toFixed(2)}MB`);
  console.log(`内存泄漏检查: ${memIncrease < 1 ? '✅ 通过' : '⚠️  需要关注'}`);

  return { avgTime, p95, memIncrease, requirementMet };
}

// 并发压力测试
async function runConcurrencyStressTest() {
  console.log(`\n=== 并发压力测试 ===`);
  
  const service = getDefaultValidationService();
  const options: CLIOptions = {
    paths: ['.'],
    debug: true,
    debugLevel: 'info',
    ui: true,
    open: true,
    details: true, // 添加必要的details参数
    showContext: true,
    maxContextItems: 5
  };

  const concurrentCount = 20;
  const start = performance.now();
  
  const promises = Array.from({ length: concurrentCount }, async (_, i) => {
    const testStart = performance.now();
    try {
      const result = await service.validateConcurrently(options);
      const testDuration = performance.now() - testStart;
      return { 
        index: i, 
        duration: testDuration, 
        valid: result.valid, 
        errors: result.errors,
        warnings: result.warnings
      };
    } catch (error) {
      const testDuration = performance.now() - testStart;
      return { 
        index: i, 
        duration: testDuration, 
        valid: false, 
        errors: [`执行错误: ${error}`],
        warnings: []
      };
    }
  });

  const results = await Promise.all(promises);
  const totalTime = performance.now() - start;
  
  const avgConcurrentTime = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
  const allValid = results.every(r => r.valid);
  const validResults = results.filter(r => r.valid).length;
  
  console.log(`并发数量: ${concurrentCount}`);
  console.log(`总耗时: ${totalTime.toFixed(2)}ms`);
  console.log(`平均单次耗时: ${avgConcurrentTime.toFixed(2)}ms`);
  console.log(`成功数量: ${validResults}/${concurrentCount}`);
  if (!allValid) {
    const failedResults = results.filter(r => !r.valid);
    console.log(`失败详情:`);
    failedResults.slice(0, 3).forEach(r => {
      console.log(`  索引${r.index}: errors=${r.errors.join(', ')}, warnings=${r.warnings.join(', ')}`);
    });
    if (failedResults.length > 3) {
      console.log(`  ...和其他${failedResults.length - 3}个失败案例`);
    }
  }
  console.log(`并发处理性能: ${allValid ? '✅ 通过' : '❌ 失败'}`);
  
  return { concurrentCount, totalTime, avgConcurrentTime, allValid };
}

// 主函数
async function main() {
  try {
    const perfResult = await runPerformanceBenchmark();
    const concurrencyResult = await runConcurrencyStressTest();
    
    console.log(`\n=== 综合评估 ===`);
    const overallPass = perfResult.requirementMet && 
                       concurrencyResult.allValid && 
                       perfResult.memIncrease < 1;
    
    console.log(`性能要求: ${perfResult.requirementMet ? '✅' : '❌'}`);
    console.log(`并发稳定性: ${concurrencyResult.allValid ? '✅' : '❌'}`);
    console.log(`内存效率: ${perfResult.memIncrease < 1 ? '✅' : '❌'}`);
    console.log(`\n总体评估: ${overallPass ? '✅ 优秀' : '⚠️  需要优化'}`);
    
    process.exit(overallPass ? 0 : 1);
    
  } catch (error) {
    console.error('性能测试失败:', error);
    process.exit(1);
  }
}

main();