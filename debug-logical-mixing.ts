import { ComplexityCalculator } from './src/core/calculator';

async function debugLogicalOperators() {
  const code = 'function test() { if (a && b || c) { console.log("mixed"); } }';
  
  console.log('测试代码:', code);
  
  // 测试启用混用检测
  const calculatorEnabled = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: true,
    enableDetails: true
  });
  const resultsEnabled = await calculatorEnabled.calculateCode(code, 'test.js');
  
  console.log('\n启用混用检测:');
  console.log('总复杂度:', resultsEnabled[0]?.complexity);
  if (resultsEnabled[0]?.details) {
    console.log('详细步骤:');
    resultsEnabled[0].details.forEach((detail, index) => {
      console.log(`  ${index + 1}. ${detail.description}: +${detail.increment} (规则: ${detail.ruleId})`);
    });
  }
  
  // 测试禁用混用检测
  const calculatorDisabled = new ComplexityCalculator({ 
    enableMixedLogicOperatorPenalty: false,
    enableDetails: true
  });
  const resultsDisabled = await calculatorDisabled.calculateCode(code, 'test.js');
  
  console.log('\n禁用混用检测:');
  console.log('总复杂度:', resultsDisabled[0]?.complexity);
  if (resultsDisabled[0]?.details) {
    console.log('详细步骤:');
    resultsDisabled[0].details.forEach((detail, index) => {
      console.log(`  ${index + 1}. ${detail.description}: +${detail.increment} (规则: ${detail.ruleId})`);
    });
  }
  
  // 正确清理资源
  await calculatorEnabled.dispose();
  await calculatorDisabled.dispose();
  console.log('\n✅ 资源清理完成');
}

debugLogicalOperators().catch(console.error);