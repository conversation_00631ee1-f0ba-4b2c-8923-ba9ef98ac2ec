import { ASTParser } from './src/core/parser';
import { ComplexityVisitor } from './src/core/complexity-visitor';
import { DetailCollector } from './src/core/detail-collector';

async function debugDetailCollectorIntegration() {
  const sourceCode = `
    function test() {
      if (condition) {
        console.log('test');
      }
    }
  `;
  
  console.log('=== DetailCollector 集成测试 ===');
  console.log('源代码:', sourceCode.trim());

  const parser = new ASTParser();
  const ast = await parser.parseCode(sourceCode, 'debug.ts');
  
  const detailCollector = new DetailCollector();
  console.log('DetailCollector 已创建');
  
  detailCollector.startFunction('test', 2, 9);
  console.log('开始函数追踪');
  
  const visitor = new ComplexityVisitor(sourceCode, detailCollector);
  console.log('ComplexityVisitor 已创建，传入了 detailCollector');
  
  visitor.visit(ast);
  console.log('AST 访问完成');
  
  const functionDetails = detailCollector.endFunction();
  console.log('结束函数追踪');
  
  console.log('\n=== 结果 ===');
  console.log('总复杂度:', visitor.getTotalComplexity());
  console.log('步骤数量:', functionDetails.details.length);
  console.log('函数复杂度:', functionDetails.complexity);
  
  console.log('\n=== 详细步骤 ===');
  if (functionDetails.details.length > 0) {
    functionDetails.details.forEach((step, index) => {
      console.log(`${index + 1}. ${step.description} (+${step.increment}) [${step.line}:${step.column}]`);
    });
  } else {
    console.log('没有记录到步骤');
  }
  
  // 检查 detailCollector 是否正确传递
  console.log('\n=== 调试信息 ===');
  console.log('visitor 中是否有 detailCollector:', visitor.detailCollector !== undefined);
}

debugDetailCollectorIntegration().catch(console.error);