import { spawn } from "child_process";
import { writeFileSync, mkdirSync, existsSync, mkdtempSync, rmSync } from "fs";
import { join } from "path";
import { tmpdir } from "os";

async function withTempDir<T>(fn: (dir: string) => Promise<T> | T): Promise<T> {
  const dir = mkdtempSync(join(tmpdir(), "cognitive-"));
  try {
    return await fn(dir);
  } finally {
    rmSync(dir, { recursive: true, force: true });
  }
}

async function testRegularFunction() {
  console.log("测试常规函数...");
  
  await withTempDir(async (testDir) => {
    // 创建常规函数 - 不是class方法
    const regularCode = `
export function regularFunction() {
  if (true) {
    return 1;
  }
  return 0;
}
    `;

    const filePath = join(testDir, "regular.ts");
    writeFileSync(filePath, regularCode, 'utf8');
    
    // 运行CLI命令 - 不使用配置文件
    const result = await runCLI([
      filePath,
      "--format", "json"
    ]);
    
    console.log("=== 常规函数结果 ===");
    console.log("Exit Code:", result.exitCode);
    
    const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const output = JSON.parse(jsonMatch[0]);
      const func = output.results[0]?.functions[0];
      console.log(`常规函数 ${func?.name}: 复杂度 ${func?.complexity}`);
    }
  });
}

async function testClassMethod() {
  console.log("\n测试class方法...");
  
  await withTempDir(async (testDir) => {
    // 创建class方法
    const classCode = `
export class TestClass {
  public testMethod() {
    if (true) {
      return 1;
    }
    return 0;
  }
}
    `;

    const filePath = join(testDir, "class.ts");
    writeFileSync(filePath, classCode, 'utf8');
    
    // 运行CLI命令 - 不使用配置文件
    const result = await runCLI([
      filePath,
      "--format", "json"
    ]);
    
    console.log("=== Class方法结果 ===");
    console.log("Exit Code:", result.exitCode);
    
    const jsonMatch = result.stdout.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const output = JSON.parse(jsonMatch[0]);
      const func = output.results[0]?.functions[0];
      console.log(`Class方法 ${func?.name}: 复杂度 ${func?.complexity}`);
    }
  });
}

const runCLI = async (args: string[], timeoutMs = 20000): Promise<{ stdout: string; stderr: string; exitCode: number }> => {
  return new Promise((resolve, reject) => {
    const proc = spawn("node", ["dist/cli/index.js", ...args], {
      cwd: process.cwd(),
      stdio: ["pipe", "pipe", "pipe"]
    });
    
    let stdout = "";
    let stderr = "";
    let resolved = false;
    
    const timeout = setTimeout(() => {
      if (!resolved) {
        resolved = true;
        proc.kill('SIGKILL');
        reject(new Error(`CLI process timed out after ${timeoutMs}ms`));
      }
    }, timeoutMs);
    
    proc.stdout?.on("data", (data) => {
      stdout += data.toString();
    });
    
    proc.stderr?.on("data", (data) => {
      stderr += data.toString();
    });
    
    proc.on("close", (code) => {
      if (!resolved) {
        resolved = true;
        clearTimeout(timeout);
        resolve({ stdout, stderr, exitCode: code || 0 });
      }
    });
    
    proc.on("error", (err) => {
      if (!resolved) {
        resolved = true;
        clearTimeout(timeout);
        reject(err);
      }
    });
  });
};

async function runTests() {
  await testRegularFunction();
  await testClassMethod();
}

runTests().catch(console.error);