/**
 * IoC 架构验证测试
 * 测试 CalculatorFactory 和 async engine 的功能
 */

import { CalculatorFactory, createLightweightFactory } from './src/core/calculator-factory';
import { ComplexityCalculator } from './src/core/calculator';
import type { CalculatorOptions } from './src/engine/types';

async function testIoCArchitecture() {
  console.log('🏗️ 测试 IoC 架构...');

  try {
    // 测试 1: 轻量级工厂
    console.log('\n📦 测试 1: 轻量级工厂');
    const lightweightFactory = createLightweightFactory();
    const lightweightCalculator = new ComplexityCalculator({}, lightweightFactory);
    
    const lightweightResult = await lightweightCalculator.calculateCode(`
      function test() {
        if (condition) {
          return true;
        }
        return false;
      }
    `, 'test-lightweight.ts');
    
    console.log(`✅ 轻量级工厂测试成功 - 复杂度: ${lightweightResult[0]?.complexity}`);
    await lightweightCalculator.dispose();

    // 测试 2: 标准工厂配置
    console.log('\n🛠️ 测试 2: 标准工厂配置');
    const standardOptions: CalculatorOptions = {
      enableMonitoring: true,
      enableCaching: true,
      maxConcurrency: 4,
      debugMode: false,
      quiet: true,
      enableDetails: false
    };
    
    const standardFactory = new CalculatorFactory(standardOptions);
    const standardCalculator = new ComplexityCalculator({}, standardFactory);
    
    const standardResult = await standardCalculator.calculateCode(`
      function complex() {
        for (let i = 0; i < 10; i++) {
          if (i % 2 === 0) {
            console.log(i);
          }
        }
      }
    `, 'test-standard.ts');
    
    console.log(`✅ 标准工厂测试成功 - 复杂度: ${standardResult[0]?.complexity}`);
    
    // 测试工厂功能摘要
    const features = standardCalculator.getFactoryFeatures();
    console.log('🔧 工厂功能:', features);
    
    await standardCalculator.dispose();

    // 测试 3: AsyncRuleEngine 集成
    console.log('\n⚡ 测试 3: AsyncRuleEngine 集成');
    const asyncOptions: CalculatorOptions = {
      enableMonitoring: true,
      enableCaching: true,
      ruleEngineConfig: {
        maxRuleConcurrency: 8,
        enableRuleCaching: true,
        ruleDebugMode: false
      }
    };
    
    const asyncFactory = new CalculatorFactory(asyncOptions);
    const asyncCalculator = new ComplexityCalculator({}, asyncFactory);
    
    const asyncResult = await asyncCalculator.calculateCode(`
      function recursive(n) {
        if (n <= 1) {
          return 1;
        }
        return recursive(n - 1) + recursive(n - 2);
      }
    `, 'test-async.ts');
    
    console.log(`✅ AsyncRuleEngine 集成测试成功 - 复杂度: ${asyncResult[0]?.complexity}`);
    await asyncCalculator.dispose();

    // 测试 4: 配置更新功能
    console.log('\n🔄 测试 4: 配置更新功能');
    const updateableCalculator = new ComplexityCalculator({}, standardFactory);
    
    // 更新配置
    updateableCalculator.updateConfiguration({
      enableDebugLog: true,
      enableDetails: true
    });
    
    const updateResult = await updateableCalculator.calculateCode(`
      function updated() {
        while (condition) {
          if (check) {
            break;
          }
        }
      }
    `, 'test-update.ts');
    
    console.log(`✅ 配置更新测试成功 - 复杂度: ${updateResult[0]?.complexity}`);
    console.log(`📊 详细模式: ${updateableCalculator.isDetailsMode()}`);
    
    await updateableCalculator.dispose();

    console.log('\n🎉 IoC 架构验证完成！所有测试通过。');

  } catch (error) {
    console.error('❌ IoC 架构测试失败:', error);
  }
}

testIoCArchitecture().catch(console.error);