import { ComplexityCalculator } from './src/core/calculator';

async function testLogicalOperators() {
  const code = `function test() {
    if (a && b) {
      console.log('both true');
    }
    if (c || d) {
      console.log('at least one true');
    }
  }`;
  
  console.log('测试逻辑运算符...');
  console.log('代码:', code);
  
  const calculator = new ComplexityCalculator();
  const results = await calculator.calculateCode(code, 'test.js');
  
  console.log('分析结果:');
  results.forEach(result => {
    console.log(`函数: ${result.name}, 复杂度: ${result.complexity}, 位置: ${result.line}:${result.column}`);
  });
  
  // 期望复杂度: if(1) + &&(1) + if(1) + ||(1) = 4
  console.log('期望复杂度: 4 (两个if语句 + 两个逻辑运算符)');
}

testLogicalOperators().catch(console.error);