import { ComplexityCalculator, createLightweightFactory, CalculatorFactory } from './src/index.js';

async function finalVerification() {
  console.log('🚀 Calculator Refactoring 最终验证\n');

  const testCode = `
function complexFunction(x, y) {
  if (x > 0) {
    for (let i = 0; i < y; i++) {
      if (i % 2 === 0) {
        try {
          if (Math.random() > 0.5) {
            return complexFunction(x - 1, y);
          }
        } catch (e) {
          console.error(e);
        }
      }
    }
  }
  return x && y ? x : y;
}
`;

  try {
    // 测试 1: 静态 API
    console.log('✅ 测试 1: 静态 API');
    const staticResult = await ComplexityCalculator.analyze(testCode);
    console.log(`   复杂度: ${staticResult[0]?.complexity || 'N/A'}`);
    console.log(`   函数数: ${staticResult.length}`);

    // 测试 2: 轻量级工厂
    console.log('\n✅ 测试 2: 轻量级工厂');
    const lightFactory = createLightweightFactory();
    const lightCalculator = new ComplexityCalculator({}, lightFactory);
    
    const lightResult = await lightCalculator.calculateCode(testCode, 'test.ts');
    console.log(`   复杂度: ${lightResult[0]?.complexity || 'N/A'}`);
    await lightCalculator.dispose();

    // 测试 3: 完整功能工厂
    console.log('\n✅ 测试 3: 完整功能工厂');
    const fullFactory = new CalculatorFactory({
      enableMonitoring: true,
      enableCaching: true,
      ruleEngineConfig: { maxRuleConcurrency: 8 }
    });
    const fullCalculator = new ComplexityCalculator({}, fullFactory);
    
    const fullResult = await fullCalculator.calculateCode(testCode, 'test.ts');
    console.log(`   复杂度: ${fullResult[0]?.complexity || 'N/A'}`);
    console.log(`   详细信息: ${fullResult[0]?.details?.length || 0} 条`);
    await fullCalculator.dispose();

    // 测试 4: 快速分析
    console.log('\n✅ 测试 4: 快速分析');
    const quickResult = await ComplexityCalculator.quickAnalyze(testCode);
    console.log(`   总复杂度: ${quickResult.totalComplexity}`);
    console.log(`   平均复杂度: ${quickResult.averageComplexity.toFixed(2)}`);

    console.log('\n🎉 所有测试通过! Calculator Refactoring 重构成功完成!');

  } catch (error) {
    console.error('❌ 验证失败:', error);
  }
}

finalVerification();